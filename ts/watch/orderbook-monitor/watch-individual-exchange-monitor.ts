import * as ccxt from 'ccxt';
import { BaseOrderbookMonitor } from './base-orderbook-monitor';
import { logger } from '../utils/pinno-logger';
import { MonitoredRequest } from '../core/monitored-request';
import { ExchangeHelper } from '../utils/exchange-helper';
import { ExchangeClosedByUser } from 'ccxt';

export class WatchIndividualExchangeMonitor extends BaseOrderbookMonitor {
  runningLoops = new Map<string, boolean>();

  constructor(exchange: ccxt.Exchange) {
    super(exchange);
  }

  override subscribe(item: MonitoredRequest): boolean {
    const result = super.subscribe(item);
    if (!result) {
      return false;
    }

    if (!this.runningLoops.get(item.symbols)) {
      this.runningLoops.set(item.symbols, true);
      this.watchLoop(item.symbols)
        .catch((error) => logger.error(error, 'WatchIndividualExchangeMonitor symbolLoop error:'))
        .finally(() => this.runningLoops.delete(item.symbols));
    }

    return result;
  }


  public async watchLoop(symbol: string): Promise<void> {
    if(!this.exchange.has['unWatchOrderBook']){
      // stop trying to close this symbol's connection, it case we failed to close it before
      ExchangeHelper.getConnectionsHelper(this.exchange).markAsMonitored(symbol);
    }
    
    while (this.hasSubscribers(symbol)) {
      // watch till request isn't in the list
      const wasUpdated = await this.watchSymbol(symbol);
    }
    await this.unwatchSymbol(symbol);
  }

  private async watchSymbol(symbol: string): Promise<boolean> {
    try {
      const orderbook = await this.exchange.watchOrderBook(symbol);
      this.notifyUpdateSymbol(symbol, orderbook);
      return true;
    } catch (error) {
      if (error instanceof ExchangeClosedByUser) {
        // the underlying connection was closed, we should not watch these symbols anymore
        logger.warn(`WatchIndividualExchangeMonitor (${symbol}) connection closed, but we're still watching it`);
        return false;
      }
      
      this.notifyUpdateSymbol(symbol, error);
      const connectionHelper = ExchangeHelper.getConnectionsHelper(this.exchange);
      const isRelatedToConnectionClose = connectionHelper.wasConnectionClosedForSymbols(symbol);
      logger.error(error, `WatchIndividualExchangeMonitor 'watchSymbol' ${symbol} error (RelatedToConnectionClose: ${isRelatedToConnectionClose}):`);
      const delay = this.errorToDelay(error);
      await new Promise(resolve => setTimeout(resolve, delay));
      return false;
    }
  }

  private async unwatchSymbol(symbol: string): Promise<boolean> {
    try {
      if (this.exchange.has['unWatchOrderBook']) {
        const result = await this.exchange.unWatchOrderBook(symbol);
        if (typeof result === 'boolean' && !result) {
          logger.warn(`WatchIndividualExchangeMonitor. Failed to unwatch '${symbol}'`);
          return false;
        }

        // only mark it as unwatched when it's not monitored.
        // unwatch failing is really an edge case.
        this.removeSymbolWithoutSubscribers(symbol);
      } else {
        // mark is as unwatched, because either way the corresponding 
        // client (ws connection) will be closed now or later.
        // closing client fails in case it is shared between several symbols.
        this.removeSymbolWithoutSubscribers(symbol);

        const connectionHelper = ExchangeHelper.getConnectionsHelper(this.exchange);
        await connectionHelper.markAsNotMonitored(symbol);
        const connectionClosed = !connectionHelper.isPendingConnectionClose(symbol);
        if (!connectionClosed) {
          logger.warn(`WatchIndividualExchangeMonitor. Failed to close connection for '${symbol}'`);
        }
        return connectionClosed;
      }

      return true;
    } catch (error) {
      logger.error(error, 'WatchIndividualExchangeMonitor unwatchSymbol error:');
      const delay = this.errorToDelay(error);
      await new Promise(resolve => setTimeout(resolve, delay));
      return false;
    }
  }
}
