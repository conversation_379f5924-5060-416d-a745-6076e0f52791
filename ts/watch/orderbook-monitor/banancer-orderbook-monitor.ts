import * as ccxt from 'ccxt';
import { logger } from '../utils/pinno-logger';
import { MonitoredRequest } from '../core/monitored-request';
import { BaseOrderbookMonitor } from './base-orderbook-monitor';
import { WatchGroupOrderbookMonitor } from './watch-group-orderbook-monitor';
import { WatchIndividualExchangeMonitor } from './watch-individual-exchange-monitor';
import { FetchIndividualOrderbookMonitor } from './fetch-individual-orderbook-monitor';
import * as os from 'node:os';
import { LINQ } from '../utils/linq';
import { ExchangeHelper } from '../utils/exchange-helper';

export class BalancerOrderbookMonitor extends BaseOrderbookMonitor {
  groupMonitors: WatchGroupOrderbookMonitor[];
  individualMonitor: WatchIndividualExchangeMonitor;
  fetchMonitor: FetchIndividualOrderbookMonitor;

  // how many connection should serve the exchange's static symbols (TOP_TICKERS_NUMBER)
  private static CONNECTIONS_PER_EXCHANGE_STATIC_SYMBOLS = new Map<string, number>([
    ['binance', 6],
    ['gate', 3],
    ['mexc', 3],
    ['bitget', 3],
    ['bybit', 3],
    ['okx', 3],
    ['htx', 2],
    ['coinbase', 2],
    ['kucoin', 2],
    ['kraken', 2]
  ]);

  // used to create lists for TOP_TICKERS_NUMBER (by trading volume) symbols on each exchange 
  private static TOP_TICKERS_NUMBER = 60;

  // how many opened socket connection can each exchange have
  private MAX_CONNECTIONS_PER_EXCHANGE = 40;

  // how many symbols needed to start monitoring existing group
  private MIN_SYMBOLS_TO_START_GROUP_MONITOR = 3;

  // how many symbols needed to create a new group
  private MIN_SYMBOLS_TO_CREATE_NEW_GROUP = 15;

  constructor(exchange: ccxt.Exchange) {
    super(exchange);
  }

  override toString(): string {
    let dynamic = 'individualMonitor (none)';
    if (this.individualMonitor) {
      const symbols = LINQ.toArray(this.individualMonitor.getMonitoredSymbols());
      // dynamic = `individualMonitor (${symbols.length}): ${symbols.join(', ')}`;
      dynamic = `individualMonitor (${symbols.length})`;
    }

    let fetch = 'fetchWatcher (none)';
    if (this.fetchMonitor) {
      const symbols = LINQ.toArray(this.fetchMonitor.getMonitoredSymbols());
      fetch = `fetchWatcher (${symbols.length}): ${symbols.join(', ')}`;
      // fetch = `fetchWatcher (${symbols.length})}`;
    }

    let stat = 'groupMonitors (none)';
    if (this.groupMonitors) {
      const runningMonitors = LINQ.count(this.groupMonitors, (monitor) => monitor.isRunning);
      if (runningMonitors === 0) {
        stat = `0/${this.groupMonitors.length} groupMonitors (none)`;
      } else {
        stat = `${runningMonitors}/${this.groupMonitors.length} groupMonitors`;

        for (const monitor of this.groupMonitors) {
          if (monitor.isRunning) {
            const symbols = LINQ.count(monitor.getMonitoredSymbols());
            stat += ` '${monitor.id}':(${symbols})`;
          }
        }
      }
    }

    return `${os.EOL}${dynamic}${os.EOL}${fetch}${os.EOL}${stat}`;
  }

  public override async initialize(): Promise<void> {
    await super.initialize();

    if (this.exchange.has['watchOrderBookForSymbols']) {
      const topTickers = await ExchangeHelper.getTopTickersByVolume(this.exchange, BalancerOrderbookMonitor.TOP_TICKERS_NUMBER);
      const watchersToCreate = BalancerOrderbookMonitor.CONNECTIONS_PER_EXCHANGE_STATIC_SYMBOLS.get(this.exchange.id) || 2;

      this.groupMonitors = [];
      for (const groupedTickers of ExchangeHelper.groupSymbolsByActivity(topTickers, watchersToCreate)) {
        const symbols = groupedTickers.symbols.map(ticket => ticket.symbol);
        await this.addNewGroupMonitor(symbols);
      }
    }

    //if (this.exchange.has['watchOrderBook'] && this.exchange.has['unWatchOrderBook']) {
    if (this.exchange.has['watchOrderBook']) {
      this.individualMonitor = new WatchIndividualExchangeMonitor(this.exchange);
      this.individualMonitor.notifyUpdateSymbol = this.notifyUpdateSymbol;// to avoid missing updates when rebalancing
      await this.individualMonitor.initialize();
    }

    // if (this.exchange.has['fetchOrderBooks']) {
    //   monitors.push(new FetchGroupOrderbookMonitor(this.exchange));
    // }

    if (this.exchange.has['fetchOrderBook']) {
      this.fetchMonitor = new FetchIndividualOrderbookMonitor(this.exchange);
      this.fetchMonitor.notifyUpdateSymbol = this.notifyUpdateSymbol;// to avoid missing updates when rebalancing
      await this.fetchMonitor.initialize();
    }

    // setInterval(()=>this.closeNonUsedExchanges().catch((error) =>
    //     logger.error(error, 'BalancerOrderbookMonitor closeNonUsedExchanges error:'))
    //   , 30_000);

    this.rebalanceLoop().catch((error) =>
      logger.error(error, 'BalancerOrderbookMonitor rebalanceLoop error:'));
  }


  // sometimes we get ws connection errors for unknown reasons 
  // (most likely, due to the bad connection or high exchange api load).
  // we need this method to clean up the failed connections and thus restore the exchange instance.
  async closeNonUsedExchanges(): Promise<void> {
    if (Object.keys(this.exchange.clients).length === 0) {
      return;
    }

    if (this.groupMonitors
      && LINQ.any(this.groupMonitors, (monitor) => monitor.isRunning)) {
      // do not close connections, if there are active group monitors
      return;
    }


    if (this.individualMonitor
      && LINQ.any(this.individualMonitor.runningLoops.values(), (value) => value)) {
      // do not close connections, if there are any active individual monitoring loops
      return;
    }

    logger.info(`Closing '${this.exchange.id}' exchange due to no active monitoring`);
    await this.exchange.close();
  }

  private async rebalanceLoop(): Promise<void> {
    while (true) {
      await new Promise(resolve => setTimeout(resolve, 60_000));
      if(LINQ.count(this.fetchAndIndividualMonitorSymbols()) < this.MIN_SYMBOLS_TO_START_GROUP_MONITOR){
        continue;
      }
      
      logger.info(`Rebalancing ${this.exchange.id}`);
      try {
        await this.rebalanceUp();
        await this.rebalanceDown();
      } catch (error) {
        logger.error(error, 'BalancerOrderbookMonitor rebalanceLoop error:');
      }
    }
  }

  private async addNewGroupMonitor(symbols: string[]): Promise<WatchGroupOrderbookMonitor> {
    const monitor = new WatchGroupOrderbookMonitor(this.exchange, symbols);
    monitor.notifyUpdateSymbol = this.notifyUpdateSymbol;// to avoid missing updates when rebalancing
    await monitor.initialize();
    this.groupMonitors.push(monitor);
    return monitor;
  }

  public override subscribe(item: MonitoredRequest): boolean {
    super.subscribe(item); // need this to be able to prioritize symbols when rebalancing

    // check if this symbol is already being monitored
    if (this.groupMonitors) {
      const monitoringMonitor = LINQ.first(this.groupMonitors,
        (monitor) => monitor.isMonitored(item.symbols));

      if (monitoringMonitor) {
        return monitoringMonitor.subscribe(item);
      }
    }

    if (this.individualMonitor && this.individualMonitor.isMonitored(item.symbols)) {
      return this.individualMonitor.subscribe(item);
    }

    if (this.fetchMonitor && this.fetchMonitor.isMonitored(item.symbols)) {
      return this.fetchMonitor.subscribe(item);
    }

    // check if we can start monitoring this symbol
    if (this.individualMonitor && Object.keys(this.exchange.clients).length < this.MAX_CONNECTIONS_PER_EXCHANGE) {
      return this.individualMonitor.subscribe(item);
    }

    if (this.fetchMonitor) {
      return this.fetchMonitor.subscribe(item);
    }

    logger.error(`No monitor found for ${item.symbols} on ${this.exchange.id}`);
    return false;
  }

  public override unsubscribe(item: MonitoredRequest): boolean {
    super.unsubscribe(item); // need this to be able to prioritize symbols when rebalancing
    this.removeSymbolWithoutSubscribers(item.symbols);

    if (this.groupMonitors && LINQ.any(this.groupMonitors,
      (monitor) =>
        monitor.isRunning
        && LINQ.contains(monitor.symbols, item.symbols)
        && monitor.unsubscribe(item))) {
      return true;
    }

    if (this.individualMonitor?.unsubscribe(item)) {
      return true;
    }

    if (this.fetchMonitor?.unsubscribe(item)) {
      return true;
    }

    logger.error(`No monitor found for ${item.symbols} on ${this.exchange.id}`);
    return false;
  }

  // overriden, because this.symbolSubscribers doesn't represent the actual monitored symbols.
  // we unsubscribe instantly, while the monitor might still be in the process of monitoring symbol.
  public override isMonitored(symbol: string): boolean {
    if (this.groupMonitors && LINQ.any(this.groupMonitors,
      (monitor) => monitor.isMonitored(symbol))) {
      return true;
    }

    if (this.individualMonitor?.isMonitored(symbol)) {
      return true;
    }

    if (this.fetchMonitor?.isMonitored(symbol)) {
      return true;
    }

    return false;
  }

  public override* getMonitoredSymbols(): IterableIterator<string> {
    if (this.groupMonitors) {
      for (const monitor of this.groupMonitors) {
        for (const symbol of monitor.getMonitoredSymbols()) {
          yield symbol;
        }
      }
    }

    if (this.individualMonitor) {
      for (const symbol of this.individualMonitor.getMonitoredSymbols()) {
        yield symbol;
      }
    }

    if (this.fetchMonitor) {
      for (const symbol of this.fetchMonitor.getMonitoredSymbols()) {
        yield symbol;
      }
    }
  }


  private* fetchAndIndividualMonitorSymbols(): IterableIterator<string> {
    if (this.fetchMonitor) {
      for (const symbol of this.fetchMonitor.getMonitoredSymbols()) {
        yield symbol;
      }
    }

    if (this.individualMonitor) {
      for (const symbol of this.individualMonitor.getMonitoredSymbols()) {
        yield symbol;
      }
    }
  }

  private promoteToGroupWatcher(symbols: string[], monitor: WatchGroupOrderbookMonitor) {
    let subscribers: MonitoredRequest[] = [];

    for (const symbol of symbols) {
      if (this.individualMonitor && this.individualMonitor.hasSubscribers(symbol)) {
        logger.info(`[${symbol}] MovesUP individualMonitor -> groupMonitor_${monitor.id}`);
        subscribers.length = 0; // copy list, because we'll alter subscribers in the loop
        LINQ.toArray(this.individualMonitor.getSubscribers(symbol), subscribers);
        for (const subscriber of subscribers) {
          this.individualMonitor.unsubscribe(subscriber);
          monitor.subscribe(subscriber);
        }
      }

      if (this.fetchMonitor && this.fetchMonitor.hasSubscribers(symbol)) {
        logger.info(`[${symbol}] MovesUP fetchWatcher -> groupMonitor_${monitor.id}`);
        subscribers.length = 0; // copy list, because we'll alter subscribers in the loop
        LINQ.toArray(this.fetchMonitor.getSubscribers(symbol), subscribers);
        for (const subscriber of subscribers) {
          this.fetchMonitor.unsubscribe(subscriber);
          monitor.subscribe(subscriber);
        }
      }
    }

  }

  private async rebalanceUp(): Promise<void> {
    if (!this.groupMonitors) {
      return;
    }

    let symbolsToPromote: string[] = [];
    for (const monitor of this.groupMonitors) {
      const iterator = LINQ.where(this.fetchAndIndividualMonitorSymbols(),
        (symbol) => monitor.symbols.includes(symbol));

      symbolsToPromote.length = 0;
      LINQ.toArray(iterator, symbolsToPromote);

      if (!monitor.isRunning && symbolsToPromote.length <= this.MIN_SYMBOLS_TO_START_GROUP_MONITOR) {
        // not enough symbols to justify monitoring entire group
        continue;
      }

      this.promoteToGroupWatcher(symbolsToPromote, monitor);
    }

    const iterator = LINQ.where(this.fetchAndIndividualMonitorSymbols(),
      (symbol) => LINQ.all(this.groupMonitors,
        (monitor) => !monitor.isMonitored(symbol)));
    const notUpgradedSymbols = LINQ.toArray(iterator);

    // const symbolAvgEmaRequestPeriod =
    //   this.getSymbolsAvgEmaRequestPeriod(notUpgradedSymbols);
    //
    // // order symbols by emaRequestPeriod
    // notUpgradedSymbols.sort((a, b) => {
    //   const aEma = symbolAvgEmaRequestPeriod.get(a);
    //   const bEma = symbolAvgEmaRequestPeriod.get(b);
    //   return aEma - bEma;
    // });

    if (notUpgradedSymbols.length > this.MIN_SYMBOLS_TO_CREATE_NEW_GROUP) {
      const newMonitor = await this.addNewGroupMonitor(notUpgradedSymbols);
      newMonitor.onMonitoringFinish = () => this.removeNotRunningMonitor(newMonitor);
      logger.info(`Created a new dynamic monitor (${newMonitor.id}) for ${notUpgradedSymbols.length} symbols`);
      this.promoteToGroupWatcher(notUpgradedSymbols, newMonitor);
    }
  }

  private removeNotRunningMonitor(monitor: WatchGroupOrderbookMonitor) {
    if (monitor.isRunning) {
      throw new Error(`You cannot remove this monitor (${monitor.id}). It's still running.`);
    }

    logger.info(`Removing not running dynamic monitor (${monitor.id})`);
    const index = this.groupMonitors.indexOf(monitor);
    if (index !== -1) {
      this.groupMonitors.splice(index, 1);
    }
  }

  private getSymbolsAvgEmaRequestPeriod(symbols: string[]): Map<string, number> {
    const result = new Map<string, number>();
    for (const symbol of symbols
      ) {
      let total = 0;
      let count = 0;
      for (const subscriber of this.getSubscribers(symbol)) {
        total += subscriber.emaRequestPeriod;
        count++;
      }
      result.set(symbol, this.getAvgEmaRequestPeriod(symbol));
    }
    return result;
  }

  private getAvgEmaRequestPeriod(symbol: string): number {
    let total = 0;
    let count = 0;
    for (const subscriber of this.getSubscribers(symbol)) {
      total += subscriber.emaRequestPeriod;
      count++;
    }

    return Math.round(total / count);
  }

  private async rebalanceDown(): Promise<void> {
    if (!this.groupMonitors) {
      return;
    }

    let subscribers: MonitoredRequest[] = [];

    for (const monitor of this.groupMonitors) {
      if (!monitor.isRunning) {
        continue;
      }

      if (LINQ.count(monitor.getMonitoredSymbols()) > this.MIN_SYMBOLS_TO_START_GROUP_MONITOR) {
        // there are still enough symbols to justify a multiplexed connection
        continue;
      }

      for (const symbol of monitor.getMonitoredSymbols()) {
        subscribers.length = 0; // copy list, because we'll alter subscribers later
        LINQ.toArray(monitor.getSubscribers(symbol), subscribers);

        if (this.individualMonitor) {
          let connectionsLeft = this.MAX_CONNECTIONS_PER_EXCHANGE - Object.keys(this.exchange.clients).length;
          if (connectionsLeft > 0 && subscribers.length > 0) {
            logger.info(`[${symbol}] MovesDOWN groupMonitor_${monitor.id} -> individualMonitor (${subscribers.length}/${connectionsLeft})`);
          }

          for (let i = subscribers.length - 1; i >= 0; i--) {
            if (connectionsLeft <= 0) {
              break;
            }

            const subscriber = subscribers[i];
            monitor.unsubscribe(subscriber);
            this.individualMonitor.subscribe(subscriber);
            subscribers.splice(i, 1);
            connectionsLeft--;
          }
        }

        if (this.fetchMonitor && subscribers.length > 0) {
          logger.info(`[${symbol}] MovesDOWN groupMonitor_${monitor.id} -> fetchWatcher (${subscribers.length})`);
          for (let i = subscribers.length - 1; i >= 0; i++) {
            const subscriber = subscribers[i];
            monitor.unsubscribe(subscriber);
            this.fetchMonitor.subscribe(subscriber);
          }
        }
      }
    }
  }
}
