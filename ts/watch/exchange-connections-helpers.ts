import * as ccxt from 'ccxt';
import WsClient from 'ccxt/js/src/base/ws/WsClient';
import { ExchangeClosedByUser } from 'ccxt';
import { logger } from './pinno-logger';

const exchangeClientsHelpers = new Map<ccxt.Exchange, ExchangeConnectionsHelper>();

export function getConnectionsHelper(exchange: ccxt.Exchange): ExchangeConnectionsHelper {
  let result = exchangeClientsHelpers.get(this.exchange);
  if (!result) {
    result = new ExchangeConnectionsHelper(this.exchange);
    exchangeClientsHelpers.set(exchange, result);
  }

  return result;
}

class ExchangeConnectionsHelper {
  private exchange: ccxt.Exchange;
  private symbolsToCloseConnectionsFor = new Set<string>();

  constructor(exchange: ccxt.Exchange) {
    this.exchange = exchange;
  }

  public markAsMonitored(symbols: string | Iterable<string>) {
    if (typeof symbols === 'string') {
      this.symbolsToCloseConnectionsFor.delete(symbols);
      return;
    }

    for (const symbol of symbols) {
      this.symbolsToCloseConnectionsFor.delete(symbol);
    }
  }

  public async markAsNotMonitored(symbols: string | Iterable<string>): Promise<void> {
    if (typeof symbols === 'string') {
      this.symbolsToCloseConnectionsFor.add(symbols);
    } else {
      for (const symbol of symbols) {
        this.symbolsToCloseConnectionsFor.add(symbol);
      }
    }

    await this.tryToClosePendingSymbolsConnections();
  }

  public async tryToClosePendingSymbolsConnections(): Promise<void> {
    const clientsToClose = 
      findClientsExclusivelyServingSomeOfSymbols(this.exchange, this.symbolsToCloseConnectionsFor);
    if (clientsToClose.size === 0) {
      return;
    }
    // for debugging
    // TODO: remove this line
    const clientsToClose1 = findClientsExclusivelyServingSomeOfSymbols(this.exchange, this.symbolsToCloseConnectionsFor);

    for (const [client, symbols] of clientsToClose.entries()) {
      if (symbols.size > 1) {
        const symbolsArray = new Array<string>();
        for (const symbol of symbols) {
          symbolsArray.push(symbol);
        }
        
        logger.warn(`Close multiplexed connection for: [${symbolsArray.join(', ')}]`);
      }

      await closeClient(this.exchange, client);

      for (const symbol of symbols) {
        this.symbolsToCloseConnectionsFor.delete(symbol);
      }
    }
  }

  public isPendingConnectionClose(symbols: string | Iterable<string>): boolean {
    if (typeof symbols === 'string') {
      return this.symbolsToCloseConnectionsFor.has(symbols);
    }
    for (const s of symbols) {
      if (this.symbolsToCloseConnectionsFor.has(s)) {
        return true;
      }
    }
    return false;
  }
}

function findClientsExclusivelyServingSomeOfSymbols(exchange: ccxt.Exchange, symbolsToCheck: Set<string>): Map<WsClient, Set<string>> {
  const result = new Map<WsClient, Set<string>>();

  const connectionsBySymbols = getClientsBySymbols(exchange);
  if (connectionsBySymbols.size === 0) {
    return result;
  }

  for (const [client, subscriptions] of connectionsBySymbols.entries()) {
    const connectionSymbols = new Set<string>();

    for (const subscriptionSymbols of subscriptions) {
      for (const symbol of subscriptionSymbols) {
        connectionSymbols.add(symbol);
      }

      let isSubSet = true; // connectionSymbols is subset of symbolsToCheck
      for (const symbol of connectionSymbols) {
        if (!symbolsToCheck.has(symbol)) {
          isSubSet = false;
          break;
        }
      }

      if (!isSubSet)
        break;

      result.set(client, connectionSymbols);
    }
  }

  return result;
}

function getClientsBySymbols(exchange: ccxt.Exchange): Map<WsClient, Set<Set<string>>> {
  const clientSubscriptions = new Map<WsClient, Set<Set<string>>>();

  if (!exchange?.clients) {
    return clientSubscriptions;
  }

  for (const [url, client] of Object.entries(exchange.clients)) {
    clientSubscriptions.set(client, new Set<Set<string>>());

    for (const [hash, subscription] of Object.entries(client.subscriptions)) {
      if (!isOrderBookSubscription(hash)) {
        continue;
      }
      const subscriptionSymbols = extractSymbolsFromSubscription(exchange, hash, subscription);
      clientSubscriptions.get(client).add(subscriptionSymbols);
    }
  }

  return clientSubscriptions;
}

async function closeClientBySymbol(exchange: ccxt.Exchange, symbol: string): Promise<boolean> {
  return closeClientForSymbols(exchange, [symbol]);
}

async function closeClientForSymbols(exchange: ccxt.Exchange, symbols: string[]): Promise<boolean> {
  if (!exchange?.clients) {
    return false;
  }

  for (const client of Object.values(exchange.clients)) {
    const { orderbookSymbols, otherChannels } = summarizeClient(exchange, client);

    if (orderbookSymbols.size === 0) {
      continue; // do not touch connections without subscriptions, it's likely an unWatch connection
    }

    if (otherChannels.size > 0) {
      continue; // do not touch connections are serving some other channels (like trades)
    }

    if (orderbookSymbols.size !== symbols.length
      || !symbols.every((s) => orderbookSymbols.has(s))) {
      continue; // not 1:1 match
    }

    await closeClient(exchange, client);
    return true; // closed the exact-match connection
  }
  return false; // no exact-match connection found
}

function summarizeClient(exchange: ccxt.Exchange, client: WsClient): {
  orderbookSymbols: Set<string>;
  otherChannels: Set<string>;
} {
  const orderbookSymbols = new Set<string>();
  const otherChannels = new Set<string>();

  for (const [hash, subscription] of Object.entries(client.subscriptions)) {
    if (isOrderBookSubscription(hash)) {
      const subscriptionSymbols =
        extractSymbolsFromSubscription(exchange, hash, subscription);
      for (const symbol of subscriptionSymbols) {
        orderbookSymbols.add(symbol);
      }
    } else {
      otherChannels.add(hash);
    }
  }
  return {
    orderbookSymbols,
    otherChannels
  };
}

function isOrderBookSubscription(messageHash: string): boolean {
  messageHash = messageHash.toLowerCase();
  return messageHash.includes('orderbook')
    || messageHash.includes('books')
    || messageHash.includes('depth')
    || messageHash.includes('l2')
    || messageHash.includes('level2');
}

function extractSymbolsFromSubscription(
  exchange: ccxt.Exchange,
  messageHash: string,
  subscription
): Set<string> {
  const result = new Set<string>();

  // 1) Use explicit subscription hints when available
  if (subscription) {
    if (subscription.symbol && typeof subscription.symbol === 'string') {
      result.add(subscription.symbol);
    }

    if (subscription.symbols && Array.isArray(subscription.symbols)) {
      for (const symbol of subscription.symbols) {
        if (typeof symbol === 'string') {
          result.add(symbol);
        }
      }
    }

    if (subscription.market && typeof subscription.market.symbol === 'string') {
      result.add(subscription.market.symbol);
    }

    if (subscription.marketId) {
      if (Array.isArray(subscription.marketId)) {
        for (const id of subscription.marketId) {
          addSymbolsFromMarketId(exchange, id, result);
        }
      } else {
        addSymbolsFromMarketId(exchange, subscription.marketId, result);
      }
    }

    if (Array.isArray(subscription.marketIds)) {
      for (const id of subscription.marketIds) {
        addSymbolsFromMarketId(exchange, id, result);
      }
    }
  }

  // 2) Fallback: parse messageHash
  if (result.size === 0 && typeof messageHash === 'string') {
    const cleaned = messageHash.replaceAll(/:+/g, ' ');
    const tokens = cleaned.split(/[^\w./@-]+/).filter(Boolean);

    for (const token of tokens) {
      if (exchange.markets && exchange.markets[token]) {
        result.add(token);
        continue;
      }

      if (exchange.markets_by_id && exchange.markets_by_id[token]) {
        const entry = exchange.markets_by_id[token];
        if (Array.isArray(entry)) {
          for (const market of entry) {
            if (market && market.symbol) {
              result.add(market.symbol);
            }
          }
        } else {
          if (entry && entry.symbol) {
            result.add(entry.symbol);
          }
        }
        continue;
      }

      if (token.includes('-') || token.includes('_')) {
        const asUnified = token.replace('_', '/').replace('-', '/').toUpperCase();
        if (exchange.markets && exchange.markets[asUnified]) {
          result.add(asUnified);
        }
      }
    }
  }

  return result;
}

function addSymbolsFromMarketId(
  exchange: ccxt.Exchange,
  id: string,
  out: Set<string>
) {
  if (!id) {
    return;
  }

  const entry = exchange.markets_by_id[id];
  if (!entry) {
    return;
  }

  if (Array.isArray(entry)) {
    for (const m of entry) {
      if (m && m.symbol) {
        out.add(m.symbol);
      }
    }
  } else {
    if (entry.symbol) {
      out.add(entry.symbol);
    }
  }
}

async function closeClient(exchange: ccxt.Exchange, client: WsClient): Promise<void> {
  try {
    client.error = new ExchangeClosedByUser('Connection closed by user');
    await client.close();
  } catch {
    // ignore errors
  }

  delete exchange.clients[client.url];
}



