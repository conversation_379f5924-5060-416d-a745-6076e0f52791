// Fetches and returns the top N markets from an exchange, sorted by 24h trading volume,
// considering only markets quoted in major stablecoins.
import * as ccxt from 'ccxt';
import { getConnectionsHelper } from './exchange-connections-helpers';

export class ExchangeHelper {
  public static getExchangeCapabilities(exchange: ccxt.Exchange): {
    fetchOrderBook: boolean,
    fetchOrderBooks: boolean,
    watchOrderBook: boolean,
    unWatchOrderBook: boolean,
    watchOrderBookForSymbols: boolean,
    unWatchOrderBookForSymbols: boolean
  } {
    return {
      fetchOrderBook: exchange.has['fetchOrderBook'] == true || false,
      fetchOrderBooks: exchange.has['fetchOrderBooks'] == true || false,
      watchOrderBook: exchange.has['watchOrderBook'] == true || false,
      unWatchOrderBook: exchange.has['unWatchOrderBook'] == true || false,
      watchOrderBookForSymbols: exchange.has['watchOrderBookForSymbols'] == true || false,
      unWatchOrderBookForSymbols: exchange.has['unWatchOrderBookForSymbols'] == true || false
    };
  }

  public static getAllSupportedPairs(exchange: ccxt.Exchange): { symbol1: string, symbol2: string }[] {
    const result: { symbol1: string, symbol2: string }[] = [];

    for (const pairName in exchange.markets) {
      const market = exchange.markets[pairName];
      if (market && market.spot && (market.active || market.active === undefined)) {
        const [symbol1, symbol2] = pairName.split('/');
        result.push({ symbol1, symbol2 });
      }
    }

    return result;
  }

  public static findSupportedSymbols(exchange: ccxt.Exchange, symbol1: string, symbol2: string): string {
    let pairName = `${symbol1}/${symbol2}`;
    let market = exchange.markets[pairName];

    // use the alias (the original name) if available.
    // on coinbase when you request BTC/USDC orderbook, you get BTC/USD.
    // so we need this check.
    if (market?.info?.alias) {
      market = exchange.markets_by_id[market.info.alias][0];
      if (market && market.spot && (market.active || market.active === undefined)) {
        pairName = market.symbol;
        return pairName;
      }
    }
    // exmo has undefined state for 'market.active'. but otherwise it works fine.
    else if (market && market.spot && (market.active || market.active === undefined)) {
      return pairName;
    }

    return undefined;
  }


  private static STABLECOINS = new Set(['USDT', 'USDC', 'DAI', 'FDUSD', 'TUSD', 'BUSD', 'USDP', 'GUSD']);

  public static async getTopTickersByVolume(exchange: ccxt.Exchange, topN = 100)
    : Promise<ccxt.Ticker[]> {
    if (!exchange.has['fetchTickers']) {
      throw new ccxt.NotSupported(`${exchange.id} does not support 'fetchTickers'`);
    }

    // Fetch all available tickers
    const tickers: ccxt.Tickers = await exchange.fetchTickers();

    // Convert to array and apply our custom stablecoin filter
    const stablecoinTickers = Object.values(tickers).filter(ticker => {
      // Basic data validation
      if (!ticker || ticker.quoteVolume === undefined || !ticker.symbol) {
        return false;
      }

      // Extract the quote currency from the symbol string 'BASE/QUOTE'
      const parts = ticker.symbol.split('/');
      if (parts.length < 2) {
        return false; // Not a valid pair
      }
      const quoteCurrency = parts[1];

      // Check if the quote currency is in our predefined set
      return ExchangeHelper.STABLECOINS.has(quoteCurrency);
    });

    // Sort the filtered array by quoteVolume in descending order
    stablecoinTickers.sort((a, b) => {
      // Use 0 as a fallback if quoteVolume is missing for any reason
      return (b.quoteVolume || 0) - (a.quoteVolume || 0);
    });

    // Get the top N results
    return stablecoinTickers.slice(0, topN);
  }

  public static groupSymbolsByActivity(tickers: ccxt.Ticker[], numGroups): {
    symbols: ccxt.Ticker[],
    totalVolume: number
  }[] {
    if (!tickers || tickers.length === 0) {
      return [];
    }

    // initialize our bins (groups)
    const groups: { symbols: ccxt.Ticker[], totalVolume: number }[] =
      Array.from({ length: numGroups },
        () => ({
          symbols: [],
          totalVolume: 0
        }));

    // iterate through the sorted markets (most active first)
    for (const ticker of tickers) {
      // find the group with the minimum total volume so far
      let lightestGroup = groups[0];
      for (const group of groups) {
        if (group.totalVolume < lightestGroup.totalVolume) {
          lightestGroup = group;
        }
      }

      // add the current ticker to this "lightest" group
      lightestGroup.symbols.push(ticker);
      lightestGroup.totalVolume += ticker.quoteVolume;
    }

    return groups;
  }

  public static getConnectionsHelper = getConnectionsHelper;
}
