import * as ccxt from 'ccxt';
import { BaseOrderbookMonitor } from './base-orderbook-monitor';
import { logger } from '../utils/pinno-logger';
import { MonitoredRequest } from '../core/monitored-request';
import { LINQ } from '../utils/linq';
import { ExchangeHelper } from '../utils/exchange-helper';

export class WatchGroupOrderbookMonitor extends BaseOrderbookMonitor {
  private static idCounter = 1;

  id: number = WatchGroupOrderbookMonitor.idCounter++;
  symbols: string[];
  isRunning = false;
  onMonitoringFinish: () => void;

  constructor(exchange: ccxt.Exchange, symbols: string[]) {
    super(exchange);
    this.symbols = symbols;
  }

  override subscribe(item: MonitoredRequest): boolean {
    if (!LINQ.contains(this.symbols, item.symbols)) {
      throw new Error(`'${item.symbols}' is not supported by this group (${this.id}).`);
    }

    // add the item to the subscribers list
    const result = super.subscribe(item);

    if (!this.isRunning) {
      this.isRunning = true;
      this.loop(this.symbols)
        .catch((error) => {
          logger.error(error, `WatchGroupOrderbookMonitor (${this.id}) 'loop' error:`);
        })
        .finally(() => {
          this.isRunning = false;
          if(this.onMonitoringFinish != undefined){
            this.onMonitoringFinish();
          }
        });
    }

    return result;
  }

  override unsubscribe(item: MonitoredRequest): boolean {
    if (!LINQ.contains(this.symbols, item.symbols)) {
      throw new Error(`'${item.symbols}' is not supported by this group (${this.id}).`);
    }

    // add the item to the subscribers list
    return super.unsubscribe(item);
  }

  public async loop(symbols: string[]): Promise<void> {
    if(!this.exchange.has['unWatchOrderBookForSymbols']){
      // stop trying to close this symbol's connection, it case we failed to close it before
      ExchangeHelper.getConnectionsHelper(this.exchange).markAsMonitored(symbols);
    }

    while (this.hasSubscribers()) {
      await this.watchSymbols(symbols);
    }

    logger.info(`WatchGroupOrderbookMonitor (${this.id}) has no subscribers, stopping`);
    const unwatchResult = await this.unwatchSymbols(symbols);
  }

  private async watchSymbols(symbols: string[]): Promise<boolean> {
    try {
      const orderbook = await this.exchange.watchOrderBookForSymbols(symbols);
      this.notifyUpdateSymbol(orderbook.symbol, orderbook);
      return true;
    } catch (error) {
      logger.error(error, `WatchGroupOrderbookMonitor (${this.id}) 'watchSymbols' error:`);
      const delay = this.errorToDelay(error);
      await new Promise(resolve => setTimeout(resolve, delay));
      return false;
    }
  }

  private async unwatchSymbols(symbols: string[]): Promise<boolean> {
    try {
      if (this.exchange.has['unWatchOrderBookForSymbols']) {
        const result = await this.exchange.unWatchOrderBookForSymbols(symbols);
        if (typeof result === 'boolean' && !result) {
          logger.warn(`WatchGroupOrderbookMonitor (${this.id}) Failed to unwatch symbols`);
          return false;
        }

        // only mark it as unwatched when it's not monitored.
        // unwatch failing is really an edge case.
        for (const symbol of symbols) {
          this.removeSymbolWithoutSubscribers(symbol);
        }
      } else {
        // mark is as unwatched, because either way the corresponding 
        // client (ws connection) will be closed now or later.
        // closing client fails in case it is shared between several symbols.
        for (const symbol of symbols) {
          this.removeSymbolWithoutSubscribers(symbol);
        }

        const connectionHelper = ExchangeHelper.getConnectionsHelper(this.exchange);
        await connectionHelper.markAsNotMonitored(symbols);
        const connectionClosed = !connectionHelper.isPendingConnectionClose(symbols);
        if (!connectionClosed) {
          logger.warn(`WatchGroupOrderbookMonitor (${this.id}) Failed to close connection`);
        }
        return connectionClosed;
      }

      return true;
    } catch (error) {
      logger.error(error, `WatchGroupOrderbookMonitor (${this.id}) 'unwatchRequests' error:`);
      const delay = this.errorToDelay(error);
      await new Promise(resolve => setTimeout(resolve, delay));
      return false;
    }
  }
}
