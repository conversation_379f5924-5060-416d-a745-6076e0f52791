{"exchange": "digifinex", "skipKeys": [], "methods": {"fetchCurrencies": [{"description": "fetchCurrencies", "method": "fetchCurrencies", "input": [], "httpResponse": {"data": [{"deposit_status": "0", "min_withdraw_fee": "5", "withdraw_fee_currency": "USDT", "chain": "OMNI", "withdraw_fee_rate": "0", "min_withdraw_amount": "10", "currency": "USDT", "withdraw_status": "0", "min_deposit_amount": "10"}, {"deposit_status": "1", "min_withdraw_fee": "5", "withdraw_fee_currency": "USDT", "chain": "ERC20", "withdraw_fee_rate": "0", "min_withdraw_amount": "10", "currency": "USDT", "withdraw_status": "1", "min_deposit_amount": "10"}, {"deposit_status": "1", "min_withdraw_fee": "1", "withdraw_fee_currency": "USDT", "chain": "TRC20", "withdraw_fee_rate": "0", "min_withdraw_amount": "10", "currency": "USDT", "withdraw_status": "1", "min_deposit_amount": "10"}, {"deposit_status": "1", "min_withdraw_fee": "1", "withdraw_fee_currency": "USDT", "chain": "BEP20", "withdraw_fee_rate": "0", "min_withdraw_amount": "10", "currency": "USDT", "withdraw_status": "1", "min_deposit_amount": "10"}, {"deposit_status": "1", "min_withdraw_fee": "2", "withdraw_fee_currency": "USDT", "chain": "Polygon", "withdraw_fee_rate": "0", "min_withdraw_amount": "10", "currency": "USDT", "withdraw_status": "1", "min_deposit_amount": "10"}, {"deposit_status": "1", "min_withdraw_fee": "0.1", "withdraw_fee_currency": "USDT", "chain": "Arbitrum-One", "withdraw_fee_rate": "0", "min_withdraw_amount": "10", "currency": "USDT", "withdraw_status": "1", "min_deposit_amount": "10"}, {"deposit_status": "1", "min_withdraw_fee": "1", "withdraw_fee_currency": "USDT", "chain": "Ton", "withdraw_fee_rate": "0", "min_withdraw_amount": "10", "currency": "USDT", "withdraw_status": "1", "min_deposit_amount": "10"}], "code": "200"}, "parsedResponse": {"USDT": {"info": [{"deposit_status": "0", "min_withdraw_fee": "5", "withdraw_fee_currency": "USDT", "chain": "OMNI", "withdraw_fee_rate": "0", "min_withdraw_amount": "10", "currency": "USDT", "withdraw_status": "0", "min_deposit_amount": "10"}, {"deposit_status": "1", "min_withdraw_fee": "5", "withdraw_fee_currency": "USDT", "chain": "ERC20", "withdraw_fee_rate": "0", "min_withdraw_amount": "10", "currency": "USDT", "withdraw_status": "1", "min_deposit_amount": "10"}, {"deposit_status": "1", "min_withdraw_fee": "1", "withdraw_fee_currency": "USDT", "chain": "TRC20", "withdraw_fee_rate": "0", "min_withdraw_amount": "10", "currency": "USDT", "withdraw_status": "1", "min_deposit_amount": "10"}, {"deposit_status": "1", "min_withdraw_fee": "1", "withdraw_fee_currency": "USDT", "chain": "BEP20", "withdraw_fee_rate": "0", "min_withdraw_amount": "10", "currency": "USDT", "withdraw_status": "1", "min_deposit_amount": "10"}, {"deposit_status": "1", "min_withdraw_fee": "2", "withdraw_fee_currency": "USDT", "chain": "Polygon", "withdraw_fee_rate": "0", "min_withdraw_amount": "10", "currency": "USDT", "withdraw_status": "1", "min_deposit_amount": "10"}, {"deposit_status": "1", "min_withdraw_fee": "0.1", "withdraw_fee_currency": "USDT", "chain": "Arbitrum-One", "withdraw_fee_rate": "0", "min_withdraw_amount": "10", "currency": "USDT", "withdraw_status": "1", "min_deposit_amount": "10"}, {"deposit_status": "1", "min_withdraw_fee": "1", "withdraw_fee_currency": "USDT", "chain": "Ton", "withdraw_fee_rate": "0", "min_withdraw_amount": "10", "currency": "USDT", "withdraw_status": "1", "min_deposit_amount": "10"}], "id": "USDT", "numericId": null, "code": "USDT", "precision": null, "type": null, "name": null, "active": true, "deposit": true, "withdraw": true, "fee": 0.1, "fees": {}, "networks": {"OMNI": {"id": "OMNI", "network": "OMNI", "active": false, "deposit": false, "withdraw": false, "fee": 5, "precision": null, "limits": {"withdraw": {"min": 10, "max": null}, "deposit": {"min": 10, "max": null}}, "info": {"deposit_status": "0", "min_withdraw_fee": "5", "withdraw_fee_currency": "USDT", "chain": "OMNI", "withdraw_fee_rate": "0", "min_withdraw_amount": "10", "currency": "USDT", "withdraw_status": "0", "min_deposit_amount": "10"}}, "ERC20": {"id": "ERC20", "network": "ERC20", "active": true, "deposit": true, "withdraw": true, "fee": 5, "precision": null, "limits": {"withdraw": {"min": 10, "max": null}, "deposit": {"min": 10, "max": null}}, "info": {"deposit_status": "1", "min_withdraw_fee": "5", "withdraw_fee_currency": "USDT", "chain": "ERC20", "withdraw_fee_rate": "0", "min_withdraw_amount": "10", "currency": "USDT", "withdraw_status": "1", "min_deposit_amount": "10"}}, "TRC20": {"id": "TRC20", "network": "TRC20", "active": true, "deposit": true, "withdraw": true, "fee": 1, "precision": null, "limits": {"withdraw": {"min": 10, "max": null}, "deposit": {"min": 10, "max": null}}, "info": {"deposit_status": "1", "min_withdraw_fee": "1", "withdraw_fee_currency": "USDT", "chain": "TRC20", "withdraw_fee_rate": "0", "min_withdraw_amount": "10", "currency": "USDT", "withdraw_status": "1", "min_deposit_amount": "10"}}, "BEP20": {"id": "BEP20", "network": "BEP20", "active": true, "deposit": true, "withdraw": true, "fee": 1, "precision": null, "limits": {"withdraw": {"min": 10, "max": null}, "deposit": {"min": 10, "max": null}}, "info": {"deposit_status": "1", "min_withdraw_fee": "1", "withdraw_fee_currency": "USDT", "chain": "BEP20", "withdraw_fee_rate": "0", "min_withdraw_amount": "10", "currency": "USDT", "withdraw_status": "1", "min_deposit_amount": "10"}}, "POLYGON": {"id": "Polygon", "network": "POLYGON", "active": true, "deposit": true, "withdraw": true, "fee": 2, "precision": null, "limits": {"withdraw": {"min": 10, "max": null}, "deposit": {"min": 10, "max": null}}, "info": {"deposit_status": "1", "min_withdraw_fee": "2", "withdraw_fee_currency": "USDT", "chain": "Polygon", "withdraw_fee_rate": "0", "min_withdraw_amount": "10", "currency": "USDT", "withdraw_status": "1", "min_deposit_amount": "10"}}, "Arbitrum-One": {"id": "Arbitrum-One", "network": "Arbitrum-One", "active": true, "deposit": true, "withdraw": true, "fee": 0.1, "precision": null, "limits": {"withdraw": {"min": 10, "max": null}, "deposit": {"min": 10, "max": null}}, "info": {"deposit_status": "1", "min_withdraw_fee": "0.1", "withdraw_fee_currency": "USDT", "chain": "Arbitrum-One", "withdraw_fee_rate": "0", "min_withdraw_amount": "10", "currency": "USDT", "withdraw_status": "1", "min_deposit_amount": "10"}}, "TON": {"id": "Ton", "network": "TON", "active": true, "deposit": true, "withdraw": true, "fee": 1, "precision": null, "limits": {"withdraw": {"min": 10, "max": null}, "deposit": {"min": 10, "max": null}}, "info": {"deposit_status": "1", "min_withdraw_fee": "1", "withdraw_fee_currency": "USDT", "chain": "Ton", "withdraw_fee_rate": "0", "min_withdraw_amount": "10", "currency": "USDT", "withdraw_status": "1", "min_deposit_amount": "10"}}}, "limits": {"deposit": {"min": 10, "max": null}, "withdraw": {"min": 10, "max": null}}}}}], "fetchTrades": [{"description": "public spot trades", "method": "fetchTrades", "input": ["BTC/USDT", null, 1], "httpResponse": {"data": [{"date": "1710327662", "id": "18230819210", "amount": "0.00025", "type": "sell", "price": "73238.69"}], "date": "1710327662", "code": "0"}, "parsedResponse": [{"id": "18230819210", "info": {"date": "1710327662", "id": "18230819210", "amount": "0.00025", "type": "sell", "price": "73238.69"}, "timestamp": 1710327662000, "datetime": "2024-03-13T11:01:02.000Z", "symbol": "BTC/USDT", "type": "limit", "order": null, "side": "sell", "price": 73238.69, "amount": 0.00025, "cost": 18.3096725, "takerOrMaker": "taker", "fee": {"cost": null, "currency": null}, "fees": []}]}], "fetchTicker": [{"description": "public spot ticker", "method": "fetchTicker", "input": ["BTC/USDT"], "httpResponse": {"ticker": [{"vol": "19018.83725", "change": "1.71", "base_vol": "1362205687.8796", "sell": "73368.21", "last": "73374.31", "symbol": "btc_usdt", "low": "68697.21", "buy": "73368.2", "high": "73628.03"}], "date": "1710328246", "code": "0"}, "parsedResponse": {"symbol": "BTC/USDT", "timestamp": 1710328246000, "datetime": "2024-03-13T11:10:46.000Z", "high": 73628.03, "low": 68697.21, "bid": 73368.2, "bidVolume": null, "ask": 73368.21, "askVolume": null, "vwap": 71624.02569482001, "open": 72140.7039622456, "close": 73374.31, "last": 73374.31, "previousClose": null, "change": 1233.6060377543997, "percentage": 1.71, "average": 72757.5, "baseVolume": 19018.83725, "quoteVolume": 1362205687.8796, "markPrice": null, "indexPrice": null, "info": {"date": 1710328246, "vol": "19018.83725", "change": "1.71", "base_vol": "1362205687.8796", "sell": "73368.21", "last": "73374.31", "symbol": "btc_usdt", "low": "68697.21", "buy": "73368.2", "high": "73628.03"}}}], "fetchOHLCV": [{"description": "public spot ohlcv", "method": "fetchOHLCV", "input": ["BTC/USDT", "1h", null, 1], "httpResponse": {"data": [[**********, 114.77683, 73426.86, 73449.5, 73238.69, 73259.69]], "code": "0"}, "parsedResponse": [[*************, 73259.69, 73449.5, 73238.69, 73426.86, 114.77683]]}], "transfer": [{"description": "transfer 5 USDT from spot to margin account", "method": "transfer", "input": ["USDT", 5, "spot", "margin"], "httpResponse": {"code": "0"}, "parsedResponse": {"info": {"code": "0"}, "id": null, "timestamp": null, "datetime": null, "currency": "USDT", "amount": null, "fromAccount": null, "toAccount": null, "status": "ok"}}, {"description": "transfer 5 USDT from spot to swap account", "method": "transfer", "input": ["USDT", 5, "spot", "swap"], "httpResponse": {"code": "0", "data": {"type": "1", "currency": "USDT", "transfer_amount": "5"}}, "parsedResponse": {"info": {"code": "0", "data": {"type": "1", "currency": "USDT", "transfer_amount": "5"}}, "id": null, "timestamp": null, "datetime": null, "currency": "USDT", "amount": 5, "fromAccount": "spot", "toAccount": "swap", "status": "ok"}}, {"description": "transfer 10 USDT from swap to spot account", "method": "transfer", "input": ["USDT", 10, "swap", "spot"], "httpResponse": {"code": "0", "data": {"type": "2", "currency": "USDT", "transfer_amount": "10"}}, "parsedResponse": {"info": {"code": "0", "data": {"type": "2", "currency": "USDT", "transfer_amount": "10"}}, "id": null, "timestamp": null, "datetime": null, "currency": "USDT", "amount": 10, "fromAccount": "swap", "toAccount": "spot", "status": "ok"}}]}}