{"exchange": "htx", "skipKeys": [], "options": {}, "methods": {"fetchClosedOrders": [{"description": "fetch closed spot orders", "method": "fetchClosedOrders", "disabledPHP": false, "input": ["LTC/USDT", null, 1], "httpResponse": {"status": "ok", "data": [{"id": "***************", "symbol": "ltcusdt", "account-id": "********", "client-order-id": "AA03022abcb87f5568-3133-4bea-8853-50aacb41d452", "amount": "0.2", "market-amount": "0", "ice-amount": "0", "is-ice": false, "price": "0", "created-at": "*************", "type": "sell-market", "field-amount": "0.2", "field-cash-amount": "13.402", "field-fees": "0.026804", "finished-at": "*************", "updated-at": "*************", "source": "spot-api", "state": "filled", "canceled-at": "0"}]}, "parsedResponse": [{"info": {"id": "***************", "symbol": "ltcusdt", "account-id": "********", "client-order-id": "AA03022abcb87f5568-3133-4bea-8853-50aacb41d452", "amount": "0.2", "market-amount": "0", "ice-amount": "0", "is-ice": false, "price": "0", "created-at": "*************", "type": "sell-market", "field-amount": "0.2", "field-cash-amount": "13.402", "field-fees": "0.026804", "finished-at": "*************", "updated-at": "*************", "source": "spot-api", "state": "filled", "canceled-at": "0"}, "id": "***************", "clientOrderId": "AA03022abcb87f5568-3133-4bea-8853-50aacb41d452", "timestamp": *************, "datetime": "2024-01-29T15:03:31.347Z", "lastTradeTimestamp": null, "symbol": "LTC/USDT", "type": "market", "timeInForce": "IOC", "postOnly": null, "side": "sell", "price": 67.01, "stopPrice": null, "triggerPrice": null, "average": 67.01, "cost": 13.402, "amount": 0.2, "filled": 0.2, "remaining": 0, "status": "closed", "reduceOnly": null, "fee": {"cost": "0.026804", "currency": "USDT"}, "trades": [], "fees": [{"cost": 0.026804, "currency": "USDT"}], "lastUpdateTimestamp": null, "takeProfitPrice": null, "stopLossPrice": null}]}], "fetchMyTrades": [{"description": "spot trade", "method": "fetchMyTrades", "input": ["LTC/USDT", null, 1], "httpResponse": {"status": "ok", "data": [{"symbol": "ltcusdt", "fee-currency": "usdt", "source": "spot-api", "trade-id": "101561650282", "match-id": "114584861642", "role": "taker", "price": "67.01", "created-at": "1706540611357", "filled-amount": "0.2", "filled-fees": "0.026804", "filled-points": "0.0", "fee-deduct-currency": "", "fee-deduct-state": "done", "order-id": "***************", "updated-at": "1706540611357", "id": "991994676282308", "type": "sell-market"}]}, "parsedResponse": [{"id": "101561650282", "info": {"symbol": "ltcusdt", "fee-currency": "usdt", "source": "spot-api", "trade-id": "101561650282", "match-id": "114584861642", "role": "taker", "price": "67.01", "created-at": "1706540611357", "filled-amount": "0.2", "filled-fees": "0.026804", "filled-points": "0.0", "fee-deduct-currency": "", "fee-deduct-state": "done", "order-id": "***************", "updated-at": "1706540611357", "id": "991994676282308", "type": "sell-market"}, "order": "***************", "timestamp": 1706540611357, "datetime": "2024-01-29T15:03:31.357Z", "symbol": "LTC/USDT", "type": "market", "side": "sell", "takerOrMaker": "taker", "price": 67.01, "amount": 0.2, "cost": 13.402, "fee": {"cost": 0.026804, "currency": "USDT"}, "fees": [{"cost": 0.026804, "currency": "USDT"}]}]}], "fetchPositions": [{"description": "Linear swap position", "method": "fetchPositions", "input": [["LTC/USDT:USDT"]], "httpResponse": {"status": "ok", "data": [{"symbol": "LTC", "contract_code": "LTC-USDT", "volume": "1.***********0000000", "available": "1.***********0000000", "frozen": "0E-18", "cost_open": "67.35***********00000", "cost_hold": "67.35***********00000", "profit_unreal": "0.092***********0000", "profit_rate": "0.01***************1", "lever_rate": "1", "position_margin": "6.827***********0000", "direction": "buy", "profit": "0.092***********0000", "last_price": "68.27", "margin_asset": "USDT", "margin_mode": "cross", "margin_account": "USDT", "contract_type": "swap", "pair": "LTC-USDT", "business_type": "swap", "trade_partition": "USDT", "position_mode": "single_side", "store_time": null, "liquidation_price": null, "market_closing_slippage": null, "risk_rate": null, "new_risk_rate": null, "withdraw_available": null, "open_adl": "1", "adl_risk_percent": "3", "tp_trigger_price": null, "sl_trigger_price": null, "tp_order_id": null, "sl_order_id": null, "tp_trigger_type": null, "sl_trigger_type": null}], "ts": "*************"}, "parsedResponse": [{"info": {"symbol": "LTC", "contract_code": "LTC-USDT", "volume": "1.***********0000000", "available": "1.***********0000000", "frozen": "0E-18", "cost_open": "67.35***********00000", "cost_hold": "67.35***********00000", "profit_unreal": "0.092***********0000", "profit_rate": "0.01***************1", "lever_rate": "1", "position_margin": "6.827***********0000", "direction": "buy", "profit": "0.092***********0000", "last_price": "68.27", "margin_asset": "USDT", "margin_mode": "cross", "margin_account": "USDT", "contract_type": "swap", "pair": "LTC-USDT", "business_type": "swap", "trade_partition": "USDT", "position_mode": "single_side", "store_time": null, "liquidation_price": null, "market_closing_slippage": null, "risk_rate": null, "new_risk_rate": null, "withdraw_available": null, "open_adl": "1", "adl_risk_percent": "3", "tp_trigger_price": null, "sl_trigger_price": null, "tp_order_id": null, "sl_order_id": null, "tp_trigger_type": null, "sl_trigger_type": null}, "id": null, "symbol": "LTC/USDT:USDT", "contracts": 1, "contractSize": 0.1, "entryPrice": 67.35, "collateral": null, "side": "long", "unrealizedPnl": 0.092, "leverage": 1, "percentage": 1.***************, "marginMode": "cross", "notional": 6.827, "markPrice": null, "lastPrice": null, "liquidationPrice": null, "initialMargin": 6.827, "initialMarginPercentage": 1, "maintenanceMargin": null, "maintenanceMarginPercentage": null, "marginRatio": null, "timestamp": *************, "datetime": "2024-01-31T12:49:54.995Z", "hedged": null, "lastUpdateTimestamp": null, "stopLossPrice": null, "takeProfitPrice": null}]}], "fetchOrder": [{"description": "spot order with clientOrderId", "method": "fetchOrder", "input": ["****************", "LTC/USDT"], "httpResponse": {"status": "ok", "data": {"id": "****************", "symbol": "ltcusdt", "account-id": "********", "client-order-id": "101", "amount": "0.21", "market-amount": "0", "ice-amount": "0", "is-ice": false, "price": "50", "created-at": "*************", "type": "buy-limit", "field-amount": "0", "field-cash-amount": "0", "field-fees": "0", "finished-at": "0", "updated-at": "*************", "source": "spot-api", "state": "submitted", "canceled-at": "0", "canceled-source": null, "canceled-source-desc": null}}, "parsedResponse": {"info": {"id": "****************", "symbol": "ltcusdt", "account-id": "********", "client-order-id": "101", "amount": "0.21", "market-amount": "0", "ice-amount": "0", "is-ice": false, "price": "50", "created-at": "*************", "type": "buy-limit", "field-amount": "0", "field-cash-amount": "0", "field-fees": "0", "finished-at": "0", "updated-at": "*************", "source": "spot-api", "state": "submitted", "canceled-at": "0", "canceled-source": null, "canceled-source-desc": null}, "id": "****************", "clientOrderId": "101", "timestamp": *************, "datetime": "2024-09-30T11:03:37.201Z", "lastTradeTimestamp": null, "symbol": "LTC/USDT", "type": "limit", "timeInForce": null, "postOnly": null, "side": "buy", "price": 50, "stopPrice": null, "triggerPrice": null, "average": null, "cost": 0, "amount": 0.21, "filled": 0, "remaining": 0.21, "status": "open", "reduceOnly": null, "fee": {"cost": "0", "currency": "LTC"}, "trades": [], "fees": [{"cost": 0, "currency": "LTC"}], "lastUpdateTimestamp": null, "takeProfitPrice": null, "stopLossPrice": null}}, {"description": "spot order with clientOrderId", "method": "fetchOrder", "input": ["1290387797828591616", "LTC/USDT:USDT"], "httpResponse": {"status": "ok", "data": [{"business_type": "swap", "contract_type": "swap", "pair": "LTC-USDT", "remark2": null, "symbol": "LTC", "contract_code": "LTC-USDT", "volume": "1", "price": "50", "order_price_type": "limit", "order_type": "1", "direction": "buy", "offset": "both", "lever_rate": "1", "order_id": "1290387797828591616", "client_order_id": "100", "created_at": "*************", "trade_volume": "0", "trade_turnover": "0", "fee": "0", "trade_avg_price": null, "margin_frozen": "0", "profit": "0", "status": "3", "order_source": "api", "canceled_source": null, "order_id_str": "1290387797828591616", "fee_asset": "USDT", "fee_amount": "0", "fee_quote_amount": "0", "liquidation_type": "0", "canceled_at": "0", "margin_asset": "USDT", "margin_account": "USDT", "margin_mode": "cross", "is_tpsl": "0", "real_profit": "0", "trade_partition": "USDT", "reduce_only": "0"}], "ts": "*************"}, "parsedResponse": {"info": {"business_type": "swap", "contract_type": "swap", "pair": "LTC-USDT", "remark2": null, "symbol": "LTC", "contract_code": "LTC-USDT", "volume": "1", "price": "50", "order_price_type": "limit", "order_type": "1", "direction": "buy", "offset": "both", "lever_rate": "1", "order_id": "1290387797828591616", "client_order_id": "100", "created_at": "*************", "trade_volume": "0", "trade_turnover": "0", "fee": "0", "trade_avg_price": null, "margin_frozen": "0", "profit": "0", "status": "3", "order_source": "api", "canceled_source": null, "order_id_str": "1290387797828591616", "fee_asset": "USDT", "fee_amount": "0", "fee_quote_amount": "0", "liquidation_type": "0", "canceled_at": "0", "margin_asset": "USDT", "margin_account": "USDT", "margin_mode": "cross", "is_tpsl": "0", "real_profit": "0", "trade_partition": "USDT", "reduce_only": "0"}, "id": "1290387797828591616", "clientOrderId": "100", "timestamp": *************, "datetime": "2024-09-30T11:00:25.243Z", "lastTradeTimestamp": null, "symbol": "LTC/USDT:USDT", "type": "limit", "timeInForce": null, "postOnly": null, "side": "buy", "price": 50, "stopPrice": null, "triggerPrice": null, "average": null, "cost": 0, "amount": 1, "filled": 0, "remaining": 1, "status": "open", "reduceOnly": false, "fee": {"cost": "0", "currency": "USDT"}, "trades": [], "fees": [{"cost": 0, "currency": "USDT"}], "lastUpdateTimestamp": null, "takeProfitPrice": null, "stopLossPrice": null}}, {"description": "fetch spot order", "disabledPHP": false, "method": "fetchOrder", "input": [***************, "LTC/USDT"], "httpResponse": {"status": "ok", "data": {"id": "***************", "symbol": "ltcusdt", "account-id": "********", "client-order-id": "AA03022abcb87f5568-3133-4bea-8853-50aacb41d452", "amount": "0.2", "market-amount": "0", "ice-amount": "0", "is-ice": false, "price": "0", "created-at": "*************", "type": "sell-market", "field-amount": "0.2", "field-cash-amount": "13.402", "field-fees": "0.026804", "finished-at": "*************", "updated-at": "*************", "source": "spot-api", "state": "filled", "canceled-at": "0"}}, "parsedResponse": {"info": {"id": "***************", "symbol": "ltcusdt", "account-id": "********", "client-order-id": "AA03022abcb87f5568-3133-4bea-8853-50aacb41d452", "amount": "0.2", "market-amount": "0", "ice-amount": "0", "is-ice": false, "price": "0", "created-at": "*************", "type": "sell-market", "field-amount": "0.2", "field-cash-amount": "13.402", "field-fees": "0.026804", "finished-at": "*************", "updated-at": "*************", "source": "spot-api", "state": "filled", "canceled-at": "0"}, "id": "***************", "clientOrderId": "AA03022abcb87f5568-3133-4bea-8853-50aacb41d452", "timestamp": *************, "datetime": "2024-01-29T15:03:31.347Z", "lastTradeTimestamp": null, "symbol": "LTC/USDT", "type": "market", "timeInForce": "IOC", "postOnly": null, "side": "sell", "price": 67.01, "stopPrice": null, "triggerPrice": null, "average": 67.01, "cost": 13.402, "amount": 0.2, "filled": 0.2, "remaining": 0, "status": "closed", "reduceOnly": null, "fee": {"cost": "0.026804", "currency": "USDT"}, "trades": [], "fees": [{"cost": 0.026804, "currency": "USDT"}], "lastUpdateTimestamp": null, "takeProfitPrice": null, "stopLossPrice": null}}, {"description": "spot order with correct cost", "method": "fetchOrder", "input": ["****************", "LTC/USDT"], "httpResponse": {"status": "ok", "data": {"id": "****************", "symbol": "ltcusdt", "account-id": "********", "client-order-id": "101", "amount": "1494.8", "market-amount": "0", "ice-amount": "0", "is-ice": false, "price": "0", "created-at": "*************", "type": "buy-market", "field-amount": "4131.9039", "field-cash-amount": "844.********", "field-fees": "3.********", "finished-at": "*************", "updated-at": "*************", "source": "spot-api", "state": "partial-canceled", "canceled-at": "*************", "canceled-source": "410", "canceled-source-desc": "Market order circuit-breaker"}}, "parsedResponse": {"info": {"id": "****************", "symbol": "ltcusdt", "account-id": "********", "client-order-id": "101", "amount": "1494.8", "market-amount": "0", "ice-amount": "0", "is-ice": false, "price": "0", "created-at": "*************", "type": "buy-market", "field-amount": "4131.9039", "field-cash-amount": "844.********", "field-fees": "3.********", "finished-at": "*************", "updated-at": "*************", "source": "spot-api", "state": "partial-canceled", "canceled-at": "*************", "canceled-source": "410", "canceled-source-desc": "Market order circuit-breaker"}, "id": "****************", "clientOrderId": "101", "timestamp": *************, "datetime": "2024-10-18T14:12:19.570Z", "lastTradeTimestamp": null, "symbol": "LTC/USDT", "type": "market", "timeInForce": "IOC", "postOnly": null, "side": "buy", "price": 0.*****************, "stopPrice": null, "triggerPrice": null, "average": 0.*****************, "cost": 844.********, "amount": null, "filled": 4131.9039, "remaining": 0, "status": "canceled", "reduceOnly": null, "fee": {"cost": "3.********", "currency": "LTC"}, "trades": [], "fees": [{"cost": 3.********, "currency": "LTC"}], "lastUpdateTimestamp": null, "takeProfitPrice": null, "stopLossPrice": null}}], "fetchTrades": [{"description": "public spot trades", "method": "fetchTrades", "input": ["BTC/USDT", null, 1], "httpResponse": {"ch": "market.btcusdt.trade.detail", "status": "ok", "ts": "1710327661021", "data": [{"id": "169303851038", "ts": "1710327659140", "data": [{"id": "1693038510381020960550923522", "ts": "1710327659140", "trade-id": "102882518926", "amount": "0.026931", "price": "73225.45", "direction": "sell"}, {"id": "1693038510381020960542107941", "ts": "1710327659140", "trade-id": "102882518925", "amount": "0.004208", "price": "73227.05", "direction": "sell"}, {"id": "1693038510381020962438611861", "ts": "1710327659140", "trade-id": "102882518924", "amount": "0.002585", "price": "73228.15", "direction": "sell"}, {"id": "1693038510381020960542304627", "ts": "1710327659140", "trade-id": "102882518923", "amount": "0.001429", "price": "73228.51", "direction": "sell"}, {"id": "1693038510381020960533556311", "ts": "1710327659140", "trade-id": "102882518922", "amount": "0.09", "price": "73236.97", "direction": "sell"}, {"id": "1693038510381020960550857096", "ts": "1710327659140", "trade-id": "102882518921", "amount": "0.09", "price": "73236.97", "direction": "sell"}, {"id": "1693038510381020960559375831", "ts": "1710327659140", "trade-id": "102882518920", "amount": "0.027309", "price": "73236.97", "direction": "sell"}, {"id": "1693038510381020960559407613", "ts": "1710327659140", "trade-id": "102882518919", "amount": "0.013653", "price": "73236.97", "direction": "sell"}, {"id": "1693038510381020960542236260", "ts": "1710327659140", "trade-id": "102882518918", "amount": "0.013653", "price": "73236.97", "direction": "sell"}, {"id": "1693038510381020960542268276", "ts": "1710327659140", "trade-id": "102882518917", "amount": "0.013653", "price": "73236.97", "direction": "sell"}, {"id": "1693038510381020960533520740", "ts": "1710327659140", "trade-id": "102882518916", "amount": "0.006723", "price": "73236.97", "direction": "sell"}, {"id": "1693038510381020960542296466", "ts": "1710327659140", "trade-id": "102882518915", "amount": "0.007856", "price": "73236.97", "direction": "sell"}]}]}, "parsedResponse": [{"id": "102882518915", "info": {"id": "1693038510381020960542296466", "ts": "1710327659140", "trade-id": "102882518915", "amount": "0.007856", "price": "73236.97", "direction": "sell"}, "order": null, "timestamp": 1710327659140, "datetime": "2024-03-13T11:00:59.140Z", "symbol": "BTC/USDT", "type": null, "side": "sell", "takerOrMaker": null, "price": 73236.97, "amount": 0.007856, "cost": 575.34963632, "fee": {"cost": null, "currency": null}, "fees": []}]}], "fetchTicker": [{"description": "public spot ticker", "method": "fetchTicker", "input": ["BTC/USDT"], "httpResponse": {"ch": "market.btcusdt.detail.merged", "status": "ok", "ts": "1710328245889", "tick": {"id": "338582949929", "version": "338582949929", "open": "72150.14", "close": "73360.54", "low": "68640.0", "high": "73635.99", "amount": "2314.593946077937", "vol": "1.6575393849546936E8", "count": "104640", "bid": [73360.53, 0.595189], "ask": [73360.54, 0.566063]}}, "parsedResponse": {"symbol": "BTC/USDT", "timestamp": 1710328245889, "datetime": "2024-03-13T11:10:45.889Z", "high": 73635.99, "low": 68640, "bid": 73360.53, "bidVolume": 0.595189, "ask": 73360.54, "askVolume": 0.566063, "vwap": 71612.53436108663, "open": 72150.14, "close": 73360.54, "last": 73360.54, "previousClose": null, "change": 1210.4, "percentage": 1.6776128223729019, "average": 72755.34, "baseVolume": 2314.593946077937, "quoteVolume": 165753938.49546936, "markPrice": null, "indexPrice": null, "info": {"id": "338582949929", "version": "338582949929", "open": "72150.14", "close": "73360.54", "low": "68640.0", "high": "73635.99", "amount": "2314.593946077937", "vol": "1.6575393849546936E8", "count": "104640", "bid": [73360.53, 0.595189], "ask": [73360.54, 0.566063]}}}], "fetchOHLCV": [{"description": "public spot ohlcv", "method": "fetchOHLCV", "input": ["BTC/USDT", "1h", null, 1], "httpResponse": {"ch": "market.btcusdt.kline.60min", "status": "ok", "ts": "*************", "data": [{"id": "**********", "open": "73239.67", "close": "73394.27", "low": "73225.45", "high": "73429.87", "amount": "7.***************", "vol": "577530.********", "count": "497"}]}, "parsedResponse": [[**********000, 73239.67, 73429.87, 73225.45, 73394.27, 7.***************]]}], "fetchOpenOrders": [{"description": "spot orders with clientOrderId", "method": "fetchOpenOrders", "input": ["LTC/USDT", null, null, {"account-id": "myaccount"}], "httpResponse": {"status": "ok", "data": [{"symbol": "ltcusdt", "source": "api", "account-id": "********", "amount": "0.21***********00000", "price": "50.***********0000000", "created-at": "*************", "ice-amount": "0.0", "client-order-id": "104", "filled-amount": "0.0", "filled-cash-amount": "0.0", "filled-fees": "0.0", "id": "****************", "state": "submitted", "type": "buy-limit"}]}, "parsedResponse": [{"info": {"symbol": "ltcusdt", "source": "api", "account-id": "********", "amount": "0.21***********00000", "price": "50.***********0000000", "created-at": "*************", "ice-amount": "0.0", "client-order-id": "104", "filled-amount": "0.0", "filled-cash-amount": "0.0", "filled-fees": "0.0", "id": "****************", "state": "submitted", "type": "buy-limit"}, "id": "****************", "clientOrderId": "104", "timestamp": *************, "datetime": "2024-10-01T10:18:58.596Z", "lastTradeTimestamp": null, "symbol": "LTC/USDT", "type": "limit", "timeInForce": null, "postOnly": null, "side": "buy", "price": 50, "stopPrice": null, "triggerPrice": null, "average": null, "cost": 0, "amount": 0.21, "filled": 0, "remaining": 0.21, "status": "open", "reduceOnly": null, "fee": {"cost": "0.0", "currency": "LTC"}, "trades": [], "fees": [{"cost": 0, "currency": "LTC"}], "lastUpdateTimestamp": null, "takeProfitPrice": null, "stopLossPrice": null}]}], "fetchOpenInterest": [{"description": "linear swap fetch open interest", "method": "fetchOpenInterest", "input": ["BTC/USDT:USDT"], "httpResponse": {"status": "ok", "data": [{"volume": "29162446", "amount": "29162.446***********0000", "symbol": "BTC", "value": "2761301608.1574***********000", "contract_code": "BTC-USDT", "trade_amount": "8142.478", "trade_volume": "8142478", "trade_turnover": "772388837.3592", "business_type": "swap", "pair": "BTC-USDT", "contract_type": "swap", "trade_partition": "USDT"}], "ts": "1735383186448"}, "parsedResponse": {"symbol": "BTC/USDT:USDT", "baseVolume": 29162446, "quoteVolume": 2761301608.1574, "openInterestAmount": 29162446, "openInterestValue": 2761301608.1574, "timestamp": 1735383186448, "datetime": "2024-12-28T10:53:06.448Z", "info": {"volume": "29162446", "amount": "29162.446***********0000", "symbol": "BTC", "value": "2761301608.1574***********000", "contract_code": "BTC-USDT", "trade_amount": "8142.478", "trade_volume": "8142478", "trade_turnover": "772388837.3592", "business_type": "swap", "pair": "BTC-USDT", "contract_type": "swap", "trade_partition": "USDT"}}}, {"description": "inverse swap fetch open interest", "method": "fetchOpenInterest", "input": ["BTC/USD:BTC"], "httpResponse": {"status": "ok", "data": [{"volume": "358158.***********0000000", "amount": "378.400136924647070383", "symbol": "BTC", "contract_code": "BTC-USD", "trade_amount": "116.9397254894748133599845592591834559263", "trade_volume": "110724", "trade_turnover": "11072400.***********0000000"}], "ts": "1735383301024"}, "parsedResponse": {"symbol": "BTC/USD:BTC", "baseVolume": 358158, "quoteVolume": null, "openInterestAmount": 358158, "openInterestValue": null, "timestamp": 1735383301024, "datetime": "2024-12-28T10:55:01.024Z", "info": {"volume": "358158.***********0000000", "amount": "378.400136924647070383", "symbol": "BTC", "contract_code": "BTC-USD", "trade_amount": "116.9397254894748133599845592591834559263", "trade_volume": "110724", "trade_turnover": "11072400.***********0000000"}}}], "fetchOpenInterests": [{"description": "inverse swap fetch open interests", "method": "fetchOpenInterests", "input": [["BTC/USD:BTC"]], "httpResponse": {"status": "ok", "data": [{"volume": "231391.***********0000000", "amount": "1073301.853535447241961519", "symbol": "XRP", "contract_code": "XRP-USD", "trade_amount": "878789.8722867232510360793169418857709672311", "trade_volume": "189372", "trade_turnover": "1893720.***********0000000"}, {"volume": "358165.***********0000000", "amount": "378.679187631564757614", "symbol": "BTC", "contract_code": "BTC-USD", "trade_amount": "117.0178362634861033342502886349856939548", "trade_volume": "110800", "trade_turnover": "11080000.***********0000000"}, {"volume": "42608.***********0000000", "amount": "490444.459536145070634914", "symbol": "ADA", "contract_code": "ADA-USD", "trade_amount": "939951.7861933000483488196333556351784453272", "trade_volume": "82736", "trade_turnover": "827360.***********0000000"}], "ts": "1735382530465"}, "parsedResponse": {"BTC/USD:BTC": {"symbol": "BTC/USD:BTC", "baseVolume": 358165, "quoteVolume": null, "openInterestAmount": 358165, "openInterestValue": null, "timestamp": null, "datetime": null, "info": {"volume": "358165.***********0000000", "amount": "378.679187631564757614", "symbol": "BTC", "contract_code": "BTC-USD", "trade_amount": "117.0178362634861033342502886349856939548", "trade_volume": "110800", "trade_turnover": "11080000.***********0000000"}}}}, {"description": "linear swap fetch open interests", "method": "fetchOpenInterests", "input": [["BTC/USDT:USDT"]], "httpResponse": {"status": "ok", "data": [{"volume": "4941204", "amount": "4941204000000.***********0000000", "symbol": "SATS", "value": "865698.9408***********000", "contract_code": "SATS-USDT", "trade_amount": "1917446000000", "trade_volume": "1917446", "trade_turnover": "338799.9488", "business_type": "swap", "pair": "SATS-USDT", "contract_type": "swap", "trade_partition": "USDT"}, {"volume": "29163579", "amount": "29163.579***********0000", "symbol": "BTC", "value": "2761750102.2894***********000", "contract_code": "BTC-USDT", "trade_amount": "8225.994", "trade_volume": "8225994", "trade_turnover": "780469490.0094", "business_type": "swap", "pair": "BTC-USDT", "contract_type": "swap", "trade_partition": "USDT"}, {"volume": "91348", "amount": "91348.***********0000000", "symbol": "ZRO", "value": "485880.012***********0000", "contract_code": "ZRO-USDT", "trade_amount": "75458", "trade_volume": "75458", "trade_turnover": "416536.154", "business_type": "swap", "pair": "ZRO-USDT", "contract_type": "swap", "trade_partition": "USDT"}], "ts": "1735382711003"}, "parsedResponse": {"BTC/USDT:USDT": {"symbol": "BTC/USDT:USDT", "baseVolume": 29163579, "quoteVolume": 2761750102.2894, "openInterestAmount": 29163579, "openInterestValue": 2761750102.2894, "timestamp": null, "datetime": null, "info": {"volume": "29163579", "amount": "29163.579***********0000", "symbol": "BTC", "value": "2761750102.2894***********000", "contract_code": "BTC-USDT", "trade_amount": "8225.994", "trade_volume": "8225994", "trade_turnover": "780469490.0094", "business_type": "swap", "pair": "BTC-USDT", "contract_type": "swap", "trade_partition": "USDT"}}}}]}}