{"exchange": "defx", "skipKeys": [], "methods": {"fetchStatus": [{"description": "fetchStatus", "method": "fetchStatus", "input": [], "httpResponse": {"success": true, "t": 1730273553694, "msg": "A programmer’s wife tells him, “While you’re at the grocery store, buy some eggs.” He never comes back."}, "parsedResponse": {"status": "ok", "updated": null, "eta": null, "url": null, "info": {"success": true, "t": 1730273553694, "msg": "A programmer’s wife tells him, “While you’re at the grocery store, buy some eggs.” He never comes back."}}}], "fetchTime": [{"description": "fetchTime", "method": "fetchTime", "input": [], "httpResponse": {"success": true, "t": 1730273553694, "msg": "A programmer’s wife tells him, “While you’re at the grocery store, buy some eggs.” He never comes back."}, "parsedResponse": 1730273553694}], "fetchMarkets": [{"description": "fetchMarkets", "method": "fetchMarkets", "disabled": true, "input": [], "httpResponse": {"data": [{"market": "AVAX_USDC", "candleWindows": ["1m", "3m", "5m", "15m", "30m", "1h", "2h", "4h", "12h", "1d", "1w", "1M"], "depthSlabs": ["0.001", "0.01", "0.1", "1"], "filters": [{"filterType": "LOT_SIZE", "minQty": "1.00", "maxQty": "7000.00", "stepSize": "1.00"}, {"filterType": "MARKET_LOT_SIZE", "minQty": "1.00", "maxQty": "3500.00", "stepSize": "1.00"}, {"filterType": "PRICE_FILTER", "minPrice": "0.35000000", "maxPrice": "2403.00000000", "tickSize": "0.001"}, {"filterType": "NOTIONAL", "minNotional": "100.00000000"}, {"filterType": "PERCENT_PRICE_BY_SIDE", "bidMultiplierUp": "1.5", "bidMultiplierDown": "0.5", "askMultiplierUp": "1.5", "askMultiplierDown": "0.5"}, {"filterType": "INDEX_PRICE_FILTER", "multiplierUp": "1.3", "multiplierDown": "0.7"}], "cappedLeverage": "25", "maintenanceMarginTiers": [{"tier": "1", "minMaintenanceMargin": "0", "maxMaintenanceMargin": "2500", "leverage": "25"}, {"tier": "2", "minMaintenanceMargin": "2500", "maxMaintenanceMargin": "12500", "leverage": "20"}, {"tier": "3", "minMaintenanceMargin": "12500", "maxMaintenanceMargin": "25000", "leverage": "15"}, {"tier": "4", "minMaintenanceMargin": "25000", "maxMaintenanceMargin": "50000", "leverage": "10"}, {"tier": "5", "minMaintenanceMargin": "50000", "maxMaintenanceMargin": "75000", "leverage": "8"}, {"tier": "6", "minMaintenanceMargin": "75000", "maxMaintenanceMargin": "125000", "leverage": "7"}, {"tier": "7", "minMaintenanceMargin": "125000", "maxMaintenanceMargin": "187500", "leverage": "5"}, {"tier": "8", "minMaintenanceMargin": "187500", "maxMaintenanceMargin": "250000", "leverage": "3"}, {"tier": "9", "minMaintenanceMargin": "250000", "maxMaintenanceMargin": "375000", "leverage": "2"}, {"tier": "10", "minMaintenanceMargin": "375000", "maxMaintenanceMargin": "500000", "leverage": "1"}], "fees": {"maker": "0.02", "taker": "0.055"}}]}, "parsedResponse": [{"id": "AVAX_USDC", "symbol": "AVAX/USDC:USDC", "base": "AVAX", "quote": "USDC", "settle": "USDC", "baseId": "AVAX", "quoteId": "USDC", "settleId": "USDC", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "active": false, "contract": true, "linear": true, "inverse": null, "taker": 0.055, "maker": 0.02, "contractSize": 1, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1, "price": 0.001}, "limits": {"leverage": {"min": null, "max": 25}, "amount": {"min": 1, "max": 7000}, "price": {"min": 0.35, "max": 2403}, "cost": {"min": 100, "max": null}, "market": {"min": 1, "max": 3500}}, "created": null, "info": {"market": "AVAX_USDC", "candleWindows": ["1m", "3m", "5m", "15m", "30m", "1h", "2h", "4h", "12h", "1d", "1w", "1M"], "depthSlabs": ["0.001", "0.01", "0.1", "1"], "filters": [{"filterType": "LOT_SIZE", "minQty": "1.00", "maxQty": "7000.00", "stepSize": "1.00"}, {"filterType": "MARKET_LOT_SIZE", "minQty": "1.00", "maxQty": "3500.00", "stepSize": "1.00"}, {"filterType": "PRICE_FILTER", "minPrice": "0.35000000", "maxPrice": "2403.00000000", "tickSize": "0.001"}, {"filterType": "NOTIONAL", "minNotional": "100.00000000"}, {"filterType": "PERCENT_PRICE_BY_SIDE", "bidMultiplierUp": "1.5", "bidMultiplierDown": "0.5", "askMultiplierUp": "1.5", "askMultiplierDown": "0.5"}, {"filterType": "INDEX_PRICE_FILTER", "multiplierUp": "1.3", "multiplierDown": "0.7"}], "cappedLeverage": "25", "maintenanceMarginTiers": [{"tier": "1", "minMaintenanceMargin": "0", "maxMaintenanceMargin": "2500", "leverage": "25"}, {"tier": "2", "minMaintenanceMargin": "2500", "maxMaintenanceMargin": "12500", "leverage": "20"}, {"tier": "3", "minMaintenanceMargin": "12500", "maxMaintenanceMargin": "25000", "leverage": "15"}, {"tier": "4", "minMaintenanceMargin": "25000", "maxMaintenanceMargin": "50000", "leverage": "10"}, {"tier": "5", "minMaintenanceMargin": "50000", "maxMaintenanceMargin": "75000", "leverage": "8"}, {"tier": "6", "minMaintenanceMargin": "75000", "maxMaintenanceMargin": "125000", "leverage": "7"}, {"tier": "7", "minMaintenanceMargin": "125000", "maxMaintenanceMargin": "187500", "leverage": "5"}, {"tier": "8", "minMaintenanceMargin": "187500", "maxMaintenanceMargin": "250000", "leverage": "3"}, {"tier": "9", "minMaintenanceMargin": "250000", "maxMaintenanceMargin": "375000", "leverage": "2"}, {"tier": "10", "minMaintenanceMargin": "375000", "maxMaintenanceMargin": "500000", "leverage": "1"}], "fees": {"maker": "0.02", "taker": "0.055"}, "status": null}}]}], "fetchTicker": [{"description": "fetchTicker", "method": "fetchTicker", "input": ["SOL/USDC:USDC"], "httpResponse": {"symbol": "SOL_USDC", "priceChange": "0.00000000", "priceChangePercent": "0.00", "weightedAvgPrice": "0", "lastPrice": "174.50000000", "lastQty": "1.14", "bestBidPrice": "178.73600000", "bestBidQty": "1.68", "bestAskPrice": "178.87800000", "bestAskQty": "1.68", "openPrice": "0.00000000", "highPrice": "0.00000000", "lowPrice": "0.00000000", "volume": "0.00", "quoteVolume": "0.00000000", "openTime": 1730187420000, "closeTime": 1730273880000, "openInterestBase": "52.53", "openInterestQuote": "9187.76804324"}, "parsedResponse": {"symbol": "SOL/USDC:USDC", "timestamp": 1730273880000, "datetime": "2024-10-30T07:38:00.000Z", "high": null, "low": null, "bid": 178.736, "bidVolume": 1.68, "ask": 178.878, "askVolume": 1.68, "vwap": null, "open": null, "close": 174.5, "last": 174.5, "previousClose": null, "change": null, "percentage": null, "average": null, "baseVolume": 0, "quoteVolume": 0, "markPrice": null, "indexPrice": null, "info": {"symbol": "SOL_USDC", "priceChange": "0.00000000", "priceChangePercent": "0.00", "weightedAvgPrice": "0", "lastPrice": "174.50000000", "lastQty": "1.14", "bestBidPrice": "178.73600000", "bestBidQty": "1.68", "bestAskPrice": "178.87800000", "bestAskQty": "1.68", "openPrice": "0.00000000", "highPrice": "0.00000000", "lowPrice": "0.00000000", "volume": "0.00", "quoteVolume": "0.00000000", "openTime": 1730187420000, "closeTime": 1730273880000, "openInterestBase": "52.53", "openInterestQuote": "9187.76804324"}}}], "fetchTickers": [{"description": "fetchTickers", "method": "fetchTickers", "input": [["SOL/USDC:USDC"]], "httpResponse": {"SOL_USDC": {"openPrice": "0.00000000", "highPrice": "0.00000000", "lowPrice": "0.00000000", "lastPrice": "174.50000000", "quoteVolume": "0.00000000", "volume": "0.00", "priceChange": "0.00000000", "priceChangePercent": "0.00", "markPrice": "178.80636255"}}, "parsedResponse": {"SOL/USDC:USDC": {"symbol": "SOL/USDC:USDC", "timestamp": null, "datetime": null, "high": null, "low": null, "bid": null, "bidVolume": null, "ask": null, "askVolume": null, "vwap": null, "open": null, "close": 174.5, "last": 174.5, "previousClose": null, "change": null, "percentage": null, "average": null, "baseVolume": 0, "quoteVolume": 0, "markPrice": 178.80636255, "indexPrice": null, "info": {"openPrice": "0.00000000", "highPrice": "0.00000000", "lowPrice": "0.00000000", "lastPrice": "174.50000000", "quoteVolume": "0.00000000", "volume": "0.00", "priceChange": "0.00000000", "priceChangePercent": "0.00", "markPrice": "178.80636255"}}}}], "fetchOHLCV": [{"description": "fetchOHLCV", "method": "fetchOHLCV", "input": ["SOL/USDC:USDC"], "httpResponse": [{"symbol": "SOL_USDC", "open": "174.50000000", "high": "174.50000000", "low": "174.50000000", "close": "174.50000000", "volume": "0.00", "quoteAssetVolume": "0.00000000", "takerBuyAssetVolume": "0.00", "takerBuyQuoteAssetVolume": "0.00000000", "numberOfTrades": 0, "start": 1730273880000, "end": 1730273940000, "isClosed": true}], "parsedResponse": [[1730273880000, 174.5, 174.5, 174.5, 174.5, 0]]}], "fetchTrades": [{"description": "fetchTrades", "method": "fetchTrades", "input": ["SOL/USDC:USDC"], "httpResponse": [{"symbol": "SOL_USDC", "buyerMaker": false, "price": "174.50000000", "qty": "1.14", "timestamp": 1730132171932}], "parsedResponse": [{"id": null, "timestamp": 1730132171932, "datetime": "2024-10-28T16:16:11.932Z", "symbol": "SOL/USDC:USDC", "side": "buy", "price": 174.5, "amount": 1.14, "cost": 198.93, "order": null, "takerOrMaker": null, "type": null, "fee": {"cost": null, "currency": null}, "info": {"symbol": "SOL_USDC", "buyerMaker": false, "price": "174.50000000", "qty": "1.14", "timestamp": 1730132171932}, "fees": []}]}], "fetchMyTrades": [{"description": "fetchMyTrades", "method": "fetchMyTrades", "input": ["SOL/USDC:USDC"], "httpResponse": {"data": [{"id": "0192f665-c05b-7ba0-a080-8b6c99083489", "orderId": "757730811259651728", "time": "2024-11-04T08:58:36.474Z", "symbol": "SOL_USDC", "side": "SELL", "price": "160.43600000", "qty": "1.00", "fee": "0.08823980", "role": "TAKER", "pnl": "0.00000000"}]}, "parsedResponse": [{"id": "0192f665-c05b-7ba0-a080-8b6c99083489", "timestamp": 1730710716474, "datetime": "2024-11-04T08:58:36.474Z", "symbol": "SOL/USDC:USDC", "side": "sell", "price": 160.436, "amount": 1, "cost": 160.436, "order": "757730811259651728", "takerOrMaker": "taker", "type": null, "fee": {"currency": "USDC", "cost": 0.0882398}, "info": {"id": "0192f665-c05b-7ba0-a080-8b6c99083489", "orderId": "757730811259651728", "time": "2024-11-04T08:58:36.474Z", "symbol": "SOL_USDC", "side": "SELL", "price": "160.43600000", "qty": "1.00", "fee": "0.08823980", "role": "TAKER", "pnl": "0.00000000"}, "fees": [{"currency": "USDC", "cost": 0.0882398}]}]}], "fetchOrderBook": [{"description": "fetchOrderBook", "method": "fetchOrderBook", "input": ["SOL/USDC:USDC"], "httpResponse": {"symbol": "SOL_USDC", "level": "10", "slab": "1", "timestamp": 1730274080699, "lastTradeTimestamp": 1730132171932, "bids": [{"price": "165.00000000", "qty": "33.57"}], "asks": [{"price": "193.00000000", "qty": "33.57"}]}, "parsedResponse": {"symbol": "SOL/USDC:USDC", "bids": [[165, 33.57]], "asks": [[193, 33.57]], "timestamp": 1730274080699, "datetime": "2024-10-30T07:41:20.699Z", "nonce": null}}], "fetchMarkPrice": [{"description": "fetchMarkPrice", "method": "fetchMarkPrice", "input": ["SOL/USDC:USDC"], "httpResponse": {"markPrice": "178.64588318", "indexPrice": "178.70100403", "ltp": "174.50000000", "movingFundingRate": "0.01", "payoutFundingRate": "0.01", "nextFundingPayout": 1730275200000}, "parsedResponse": {"symbol": "SOL/USDC:USDC", "timestamp": null, "datetime": null, "high": null, "low": null, "bid": null, "bidVolume": null, "ask": null, "askVolume": null, "vwap": null, "open": null, "close": null, "last": null, "previousClose": null, "change": null, "percentage": null, "average": null, "baseVolume": null, "quoteVolume": null, "markPrice": 178.64588318, "indexPrice": 178.70100403, "info": {"markPrice": "178.64588318", "indexPrice": "178.70100403", "ltp": "174.50000000", "movingFundingRate": "0.01", "payoutFundingRate": "0.01", "nextFundingPayout": 1730275200000}}}], "fetchFundingRate": [{"description": "fetchFundingRate", "method": "fetchFundingRate", "input": ["SOL/USDC:USDC"], "httpResponse": {"markPrice": "178.64588318", "indexPrice": "178.70100403", "ltp": "174.50000000", "movingFundingRate": "0.01", "payoutFundingRate": "0.01", "nextFundingPayout": 1730275200000}, "parsedResponse": {"info": {"markPrice": "178.64588318", "indexPrice": "178.70100403", "ltp": "174.50000000", "movingFundingRate": "0.01", "payoutFundingRate": "0.01", "nextFundingPayout": 1730275200000}, "symbol": "SOL/USDC:USDC", "markPrice": 178.64588318, "indexPrice": 178.70100403, "interestRate": null, "estimatedSettlePrice": null, "timestamp": null, "datetime": null, "fundingRate": 0.01, "fundingTimestamp": 1730275200000, "fundingDatetime": "2024-10-30T08:00:00.000Z", "nextFundingRate": null, "nextFundingTimestamp": null, "nextFundingDatetime": null, "previousFundingRate": null, "previousFundingTimestamp": null, "previousFundingDatetime": null, "interval": null}}], "fetchBalance": [{"description": "fetchBalance", "method": "fetchBalance", "input": [], "httpResponse": {"assets": [{"asset": "USDC", "balance": "39.98964354"}]}, "parsedResponse": {"info": [{"asset": "USDC", "balance": "39.98964354"}], "USDC": {"free": null, "used": null, "total": 39.98964354}, "free": {"USDC": null}, "used": {"USDC": null}, "total": {"USDC": 39.98964354}}}], "createOrder": [{"description": "createOrder", "method": "createOrder", "input": ["SOL/USDC:USDC", "limit", "sell", 0.5, 200, {"timeInForce": "GTC"}], "httpResponse": {"data": {"orderId": "752101266248173192", "symbol": "SOL_USDC", "side": "SELL", "price": "200.00000000", "origQty": "0.50", "status": "NEW", "cumulativeQuote": "0.00000000", "executedQty": "0.00", "avgPrice": "0.00000000", "timeInForce": "GTC", "type": "LIMIT", "postOnly": false}}, "parsedResponse": {"id": "752101266248173192", "clientOrderId": null, "timestamp": null, "datetime": null, "lastTradeTimestamp": null, "lastUpdateTimestamp": null, "status": "open", "symbol": "SOL/USDC:USDC", "type": "limit", "timeInForce": "gtc", "postOnly": false, "reduceOnly": null, "side": "sell", "price": 200, "stopPrice": null, "triggerPrice": null, "takeProfitPrice": null, "stopLossPrice": null, "average": null, "amount": 0.5, "filled": null, "remaining": null, "cost": null, "trades": [], "fee": {"cost": null, "currency": "USDC"}, "info": {"orderId": "752101266248173192", "symbol": "SOL_USDC", "side": "SELL", "price": "200.00000000", "origQty": "0.50", "status": "NEW", "cumulativeQuote": "0.00000000", "executedQty": "0.00", "avgPrice": "0.00000000", "timeInForce": "GTC", "type": "LIMIT", "postOnly": false}, "fees": [{"cost": null, "currency": "USDC"}]}}], "cancelOrder": [{"description": "cancelOrder", "method": "cancelOrder", "input": ["752101209672255152", "SOL/USDC:USDC"], "httpResponse": {"success": true}, "parsedResponse": {"id": "752101209672255152", "clientOrderId": null, "timestamp": null, "datetime": null, "lastTradeTimestamp": null, "lastUpdateTimestamp": null, "status": null, "symbol": "SOL/USDC:USDC", "type": null, "timeInForce": null, "postOnly": null, "reduceOnly": null, "side": null, "price": null, "stopPrice": null, "triggerPrice": null, "takeProfitPrice": null, "stopLossPrice": null, "average": null, "amount": null, "filled": null, "remaining": null, "cost": null, "trades": [], "fee": {"cost": null, "currency": "USDC"}, "info": {"success": true}, "fees": [{"cost": null, "currency": "USDC"}]}}], "cancelAllOrders": [{"description": "cancelAllOrders", "disabled": true, "method": "cancelAllOrders", "input": ["SOL/USDC:USDC"], "httpResponse": {"data": {"msg": "The operation of cancel all open order is done."}}, "parsedResponse": {"data": {"msg": "The operation of cancel all open order is done."}}}], "fetchPosition": [{"description": "fetchPosition", "method": "fetchPosition", "input": ["SOL/USDC:USDC"], "httpResponse": {"data": [{"positionId": "0192c495-4a68-70ee-9081-9d368bd16dfc", "symbol": "SOL_USDC", "positionSide": "SHORT", "entryPrice": "172.34300000", "quantity": "0.80", "marginAmount": "20.11561173", "marginAsset": "USDC", "pnl": "0.00000000"}]}, "parsedResponse": {"info": {"positionId": "0192c495-4a68-70ee-9081-9d368bd16dfc", "symbol": "SOL_USDC", "positionSide": "SHORT", "entryPrice": "172.34300000", "quantity": "0.80", "marginAmount": "20.11561173", "marginAsset": "USDC", "pnl": "0.00000000"}, "id": "0192c495-4a68-70ee-9081-9d368bd16dfc", "symbol": "SOL/USDC:USDC", "timestamp": null, "datetime": null, "lastUpdateTimestamp": null, "initialMargin": 20.11561173, "initialMarginPercentage": null, "maintenanceMargin": null, "maintenanceMarginPercentage": null, "entryPrice": 172.343, "notional": null, "leverage": null, "unrealizedPnl": null, "realizedPnl": null, "contracts": 0.8, "contractSize": 1, "marginRatio": null, "liquidationPrice": null, "markPrice": null, "lastPrice": null, "collateral": null, "marginMode": null, "side": "short", "percentage": null, "stopLossPrice": null, "takeProfitPrice": null, "hedged": null}}], "fetchPositions": [{"description": "fetchPositions", "method": "fetchPositions", "input": [["SOL/USDC:USDC"]], "httpResponse": {"data": [{"positionId": "0192c495-4a68-70ee-9081-9d368bd16dfc", "symbol": "SOL_USDC", "positionSide": "SHORT", "entryPrice": "172.34300000", "quantity": "0.80", "marginAmount": "20.11561173", "marginAsset": "USDC", "pnl": "0.00000000"}]}, "parsedResponse": [{"info": {"positionId": "0192c495-4a68-70ee-9081-9d368bd16dfc", "symbol": "SOL_USDC", "positionSide": "SHORT", "entryPrice": "172.34300000", "quantity": "0.80", "marginAmount": "20.11561173", "marginAsset": "USDC", "pnl": "0.00000000"}, "id": "0192c495-4a68-70ee-9081-9d368bd16dfc", "symbol": "SOL/USDC:USDC", "timestamp": null, "datetime": null, "lastUpdateTimestamp": null, "initialMargin": 20.11561173, "initialMarginPercentage": null, "maintenanceMargin": null, "maintenanceMarginPercentage": null, "entryPrice": 172.343, "notional": null, "leverage": null, "unrealizedPnl": null, "realizedPnl": null, "contracts": 0.8, "contractSize": 1, "marginRatio": null, "liquidationPrice": null, "markPrice": null, "lastPrice": null, "collateral": null, "marginMode": null, "side": "short", "percentage": null, "stopLossPrice": null, "takeProfitPrice": null, "hedged": null}]}], "fetchOrder": [{"description": "fetchOrder", "method": "fetchOrder", "input": ["754353041298163336", "SOL/USDC:USDC"], "httpResponse": {"success": true, "data": {"orderId": "754353041298163336", "createdAt": "2024-11-01T06:44:37.724Z", "updatedAt": "2024-11-01T06:44:38.252Z", "clientOrderId": "0192e678-02d9-7657-96fb-fbfaa03e604d", "reduceOnly": false, "side": "SELL", "status": "OPEN", "symbol": "SOL_USDC", "timeInForce": "GTC", "type": "LIMIT", "origQty": "0.50", "executedQty": "0.00", "cumulativeQuote": "0.00000000", "avgPrice": "0.00000000", "price": "200.00000000", "totalPnL": "0.00000000", "workingType": null, "postOnly": false, "linkedOrderParentType": null, "isTriggered": false, "slippagePercentage": 0}}, "parsedResponse": {"id": "754353041298163336", "clientOrderId": "0192e678-02d9-7657-96fb-fbfaa03e604d", "timestamp": 1730443477724, "datetime": "2024-11-01T06:44:37.724Z", "lastTradeTimestamp": 1730443478252, "lastUpdateTimestamp": 1730443478252, "status": "open", "symbol": "SOL/USDC:USDC", "type": "limit", "timeInForce": "gtc", "postOnly": false, "reduceOnly": false, "side": "sell", "price": 200, "stopPrice": null, "triggerPrice": null, "takeProfitPrice": null, "stopLossPrice": null, "average": null, "amount": 0.5, "filled": null, "remaining": null, "cost": null, "trades": [], "fee": {"cost": null, "currency": "USDC"}, "info": {"orderId": "754353041298163336", "createdAt": "2024-11-01T06:44:37.724Z", "updatedAt": "2024-11-01T06:44:38.252Z", "clientOrderId": "0192e678-02d9-7657-96fb-fbfaa03e604d", "reduceOnly": false, "side": "SELL", "status": "OPEN", "symbol": "SOL_USDC", "timeInForce": "GTC", "type": "LIMIT", "origQty": "0.50", "executedQty": "0.00", "cumulativeQuote": "0.00000000", "avgPrice": "0.00000000", "price": "200.00000000", "totalPnL": "0.00000000", "workingType": null, "postOnly": false, "linkedOrderParentType": null, "isTriggered": false, "slippagePercentage": 0}, "fees": [{"cost": null, "currency": "USDC"}]}}], "fetchOrders": [{"description": "fetchOrders", "method": "fetchOrders", "input": ["SOL/USDC:USDC", 1729703590000, 1, {"until": *************}], "httpResponse": {"data": [{"orderId": "754353060444636808", "createdAt": "2024-11-01T07:52:53.617Z", "updatedAt": "2024-11-01T07:52:53.673Z", "clientOrderId": "0192e6b6-8270-7898-905b-ebc2b733590e", "reduceOnly": false, "side": "SELL", "status": "OPEN", "symbol": "SOL_USDC", "timeInForce": "GTC", "type": "LIMIT", "origQty": "0.50", "executedQty": "0.00", "cumulativeQuote": "0.00000000", "avgPrice": "0.00000000", "price": "200.00000000", "totalPnL": "0.00000000", "workingType": null, "postOnly": false, "linkedOrderParentType": null, "isTriggered": false, "slippagePercentage": 0}]}, "parsedResponse": [{"id": "754353060444636808", "clientOrderId": "0192e6b6-8270-7898-905b-ebc2b733590e", "timestamp": 1730447573617, "datetime": "2024-11-01T07:52:53.617Z", "lastTradeTimestamp": 1730447573673, "lastUpdateTimestamp": 1730447573673, "status": "open", "symbol": "SOL/USDC:USDC", "type": "limit", "timeInForce": "gtc", "postOnly": false, "reduceOnly": false, "side": "sell", "price": 200, "stopPrice": null, "triggerPrice": null, "takeProfitPrice": null, "stopLossPrice": null, "average": null, "amount": 0.5, "filled": null, "remaining": null, "cost": null, "trades": [], "fee": {"cost": null, "currency": "USDC"}, "info": {"orderId": "754353060444636808", "createdAt": "2024-11-01T07:52:53.617Z", "updatedAt": "2024-11-01T07:52:53.673Z", "clientOrderId": "0192e6b6-8270-7898-905b-ebc2b733590e", "reduceOnly": false, "side": "SELL", "status": "OPEN", "symbol": "SOL_USDC", "timeInForce": "GTC", "type": "LIMIT", "origQty": "0.50", "executedQty": "0.00", "cumulativeQuote": "0.00000000", "avgPrice": "0.00000000", "price": "200.00000000", "totalPnL": "0.00000000", "workingType": null, "postOnly": false, "linkedOrderParentType": null, "isTriggered": false, "slippagePercentage": 0}, "fees": [{"cost": null, "currency": "USDC"}]}]}], "fetchOpenOrders": [{"description": "fetchOpenOrders", "method": "fetchOpenOrders", "input": ["SOL/USDC:USDC", 1729703590000, 1, {"until": *************}], "httpResponse": {"data": [{"orderId": "754353060444636808", "createdAt": "2024-11-01T07:52:53.617Z", "updatedAt": "2024-11-01T07:52:53.673Z", "clientOrderId": "0192e6b6-8270-7898-905b-ebc2b733590e", "reduceOnly": false, "side": "SELL", "status": "OPEN", "symbol": "SOL_USDC", "timeInForce": "GTC", "type": "LIMIT", "origQty": "0.50", "executedQty": "0.00", "cumulativeQuote": "0.00000000", "avgPrice": "0.00000000", "price": "200.00000000", "totalPnL": "0.00000000", "workingType": null, "postOnly": false, "linkedOrderParentType": null, "isTriggered": false, "slippagePercentage": 0}]}, "parsedResponse": [{"id": "754353060444636808", "clientOrderId": "0192e6b6-8270-7898-905b-ebc2b733590e", "timestamp": 1730447573617, "datetime": "2024-11-01T07:52:53.617Z", "lastTradeTimestamp": 1730447573673, "lastUpdateTimestamp": 1730447573673, "status": "open", "symbol": "SOL/USDC:USDC", "type": "limit", "timeInForce": "gtc", "postOnly": false, "reduceOnly": false, "side": "sell", "price": 200, "stopPrice": null, "triggerPrice": null, "takeProfitPrice": null, "stopLossPrice": null, "average": null, "amount": 0.5, "filled": null, "remaining": null, "cost": null, "trades": [], "fee": {"cost": null, "currency": "USDC"}, "info": {"orderId": "754353060444636808", "createdAt": "2024-11-01T07:52:53.617Z", "updatedAt": "2024-11-01T07:52:53.673Z", "clientOrderId": "0192e6b6-8270-7898-905b-ebc2b733590e", "reduceOnly": false, "side": "SELL", "status": "OPEN", "symbol": "SOL_USDC", "timeInForce": "GTC", "type": "LIMIT", "origQty": "0.50", "executedQty": "0.00", "cumulativeQuote": "0.00000000", "avgPrice": "0.00000000", "price": "200.00000000", "totalPnL": "0.00000000", "workingType": null, "postOnly": false, "linkedOrderParentType": null, "isTriggered": false, "slippagePercentage": 0}, "fees": [{"cost": null, "currency": "USDC"}]}]}], "fetchClosedOrders": [{"description": "fetchClosedOrders", "method": "fetchClosedOrders", "input": ["SOL/USDC:USDC", 1729703590000, 1, {"until": *************}], "httpResponse": {"data": [{"orderId": "746472647227344528", "createdAt": "2024-10-25T16:49:31.077Z", "updatedAt": "2024-10-25T16:49:31.378Z", "clientOrderId": "0192c495-49c3-71ee-b3d3-7442a2090807", "reduceOnly": false, "side": "SELL", "status": "FILLED", "symbol": "SOL_USDC", "timeInForce": "GTC", "type": "MARKET", "origQty": "0.80", "executedQty": "0.80", "cumulativeQuote": "137.87440000", "avgPrice": "172.34300000", "totalPnL": "0.00000000", "totalFee": "0.07583092", "workingType": null, "postOnly": false, "linkedOrderParentType": null, "isTriggered": false, "slippagePercentage": 5}]}, "parsedResponse": [{"id": "746472647227344528", "clientOrderId": "0192c495-49c3-71ee-b3d3-7442a2090807", "timestamp": 1729874971077, "datetime": "2024-10-25T16:49:31.077Z", "lastTradeTimestamp": 1729874971378, "lastUpdateTimestamp": 1729874971378, "status": "closed", "symbol": "SOL/USDC:USDC", "type": "market", "timeInForce": "gtc", "postOnly": false, "reduceOnly": false, "side": "sell", "price": 172.343, "stopPrice": null, "triggerPrice": null, "takeProfitPrice": null, "stopLossPrice": null, "average": 172.343, "amount": 0.8, "filled": 0.8, "remaining": 0, "cost": 137.8744, "trades": [], "fee": {"cost": "0.07583092", "currency": "USDC"}, "info": {"orderId": "746472647227344528", "createdAt": "2024-10-25T16:49:31.077Z", "updatedAt": "2024-10-25T16:49:31.378Z", "clientOrderId": "0192c495-49c3-71ee-b3d3-7442a2090807", "reduceOnly": false, "side": "SELL", "status": "FILLED", "symbol": "SOL_USDC", "timeInForce": "GTC", "type": "MARKET", "origQty": "0.80", "executedQty": "0.80", "cumulativeQuote": "137.87440000", "avgPrice": "172.34300000", "totalPnL": "0.00000000", "totalFee": "0.07583092", "workingType": null, "postOnly": false, "linkedOrderParentType": null, "isTriggered": false, "slippagePercentage": 5}, "fees": [{"cost": 0.07583092, "currency": "USDC"}]}]}], "fetchCanceledOrders": [{"description": "fetchCanceledOrders", "method": "fetchCanceledOrders", "input": ["SOL/USDC:USDC", 1729703590000, 1, {"until": *************}], "httpResponse": {"data": [{"orderId": "746472638440801928", "createdAt": "2024-10-25T16:29:51.708Z", "updatedAt": "2024-10-25T17:04:18.079Z", "clientOrderId": "testccxt007", "reduceOnly": false, "side": "SELL", "status": "CANCELED", "symbol": "SOL_USDC", "timeInForce": "GTC", "type": "LIMIT", "origQty": "1.00", "executedQty": "0.00", "cumulativeQuote": "0.00000000", "avgPrice": "0.00000000", "price": "223.00000000", "totalPnL": "0.00000000", "workingType": null, "postOnly": false, "linkedOrderParentType": null, "isTriggered": false, "slippagePercentage": 0}]}, "parsedResponse": [{"id": "746472638440801928", "clientOrderId": "testccxt007", "timestamp": 1729873791708, "datetime": "2024-10-25T16:29:51.708Z", "lastTradeTimestamp": 1729875858079, "lastUpdateTimestamp": 1729875858079, "status": "CANCELED", "symbol": "SOL/USDC:USDC", "type": "limit", "timeInForce": "gtc", "postOnly": false, "reduceOnly": false, "side": "sell", "price": 223, "stopPrice": null, "triggerPrice": null, "takeProfitPrice": null, "stopLossPrice": null, "average": null, "amount": 1, "filled": null, "remaining": null, "cost": null, "trades": [], "fee": {"cost": null, "currency": "USDC"}, "info": {"orderId": "746472638440801928", "createdAt": "2024-10-25T16:29:51.708Z", "updatedAt": "2024-10-25T17:04:18.079Z", "clientOrderId": "testccxt007", "reduceOnly": false, "side": "SELL", "status": "CANCELED", "symbol": "SOL_USDC", "timeInForce": "GTC", "type": "LIMIT", "origQty": "1.00", "executedQty": "0.00", "cumulativeQuote": "0.00000000", "avgPrice": "0.00000000", "price": "223.00000000", "totalPnL": "0.00000000", "workingType": null, "postOnly": false, "linkedOrderParentType": null, "isTriggered": false, "slippagePercentage": 0}, "fees": [{"cost": null, "currency": "USDC"}]}]}], "closePosition": [], "closeAllPositions": [{"description": "closeAllPositions", "method": "closeAllPositions", "input": [], "httpResponse": {"data": [{"positionId": "d6ca1a27-28ad-47ae-b244-0bda5ac37b2b", "success": true}]}, "parsedResponse": [{"info": {"positionId": "d6ca1a27-28ad-47ae-b244-0bda5ac37b2b", "success": true}, "id": "d6ca1a27-28ad-47ae-b244-0bda5ac37b2b", "symbol": null, "timestamp": null, "datetime": null, "lastUpdateTimestamp": null, "initialMargin": null, "initialMarginPercentage": null, "maintenanceMargin": null, "maintenanceMarginPercentage": null, "entryPrice": null, "notional": null, "leverage": null, "unrealizedPnl": null, "realizedPnl": null, "contracts": null, "contractSize": null, "marginRatio": null, "liquidationPrice": null, "markPrice": null, "lastPrice": null, "collateral": null, "marginMode": null, "side": null, "percentage": null, "stopLossPrice": null, "takeProfitPrice": null, "hedged": null}]}], "fetchLedger": [{"description": "fetchLedger", "method": "fetchLedger", "input": [null, *************, null, {"until": *************}], "httpResponse": {"transactions": [{"id": "01JBT7HMPQ214718VH9ZRNDTQW", "timestamp": *************, "type": "FundingFee", "amount": "0.********", "asset": "USDC", "operation": "CREDIT"}]}, "parsedResponse": [{"id": "01JBT7HMPQ214718VH9ZRNDTQW", "timestamp": *************, "datetime": "2024-11-04T00:00:11.990Z", "direction": null, "account": null, "referenceId": null, "referenceAccount": null, "type": "fee", "currency": "USDC", "amount": 0.********, "before": null, "after": null, "status": null, "fee": null, "info": {"id": "01JBT7HMPQ214718VH9ZRNDTQW", "timestamp": *************, "type": "FundingFee", "amount": "0.********", "asset": "USDC", "operation": "CREDIT"}}]}], "withdraw": [{"description": "withdraw", "method": "withdraw", "input": ["USDC", 5, "", null, {"network": "ARB_SEPOLIA", "chainId": "421614"}], "httpResponse": {"transactionId": "0x301e5851e5aefa733abfbc8b30817ca3b61601e0ddf1df8c59656fb888b0bc9c"}, "parsedResponse": {"info": {"transactionId": "0x301e5851e5aefa733abfbc8b30817ca3b61601e0ddf1df8c59656fb888b0bc9c"}, "id": null, "txid": "0x301e5851e5aefa733abfbc8b30817ca3b61601e0ddf1df8c59656fb888b0bc9c", "timestamp": null, "datetime": null, "network": null, "address": null, "addressTo": null, "addressFrom": null, "tag": null, "tagTo": null, "tagFrom": null, "type": null, "amount": null, "currency": "USDC", "status": null, "updated": null, "internal": null, "comment": null, "fee": null}}]}}