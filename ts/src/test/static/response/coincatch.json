{"exchange": "coincatch", "skipKeys": [], "methods": {"fetchCurrencies": [{"description": "fetchCurrencies", "method": "fetchCurrencies", "input": [], "httpResponse": {"code": "00000", "data": [{"chains": [{"browserUrl": "https://blockchair.com/bitcoin/transaction/", "chain": "BITCOIN", "chainId": "10", "coinChainId": "10", "depositConfirm": "1", "extraWithDrawFee": "0", "minDepositAmount": "0.00001", "minWithdrawAmount": "0.001", "needTag": "false", "rechargeable": "true", "shortName": "BTC", "withdrawConfirm": "1", "withdrawFee": "0.00008", "withdrawable": "true"}], "coinDisplayName": "BTC", "coinId": "1", "coinName": "BTC", "transfer": "true"}, {"chains": [{"browserUrl": "https://etherscan.io/tx/", "chain": "ERC20", "chainId": "70", "coinChainId": "70", "depositConfirm": "12", "extraWithDrawFee": "0", "minDepositAmount": "0.9", "minWithdrawAmount": "1", "needTag": "false", "rechargeable": "true", "shortName": "ETH", "withdrawConfirm": "64", "withdrawFee": "3", "withdrawable": "true"}, {"browserUrl": "https://tronscan.org/#/transaction/", "chain": "TRC20", "chainId": "73", "coinChainId": "73", "depositConfirm": "1", "extraWithDrawFee": "0", "minDepositAmount": "1", "minWithdrawAmount": "1", "needTag": "false", "rechargeable": "true", "shortName": "TRX", "withdrawConfirm": "1", "withdrawFee": "1", "withdrawable": "true"}, {"browserUrl": "https://bscscan.com/tx/", "chain": "BEP20", "chainId": "1000", "coinChainId": "1000", "depositConfirm": "15", "extraWithDrawFee": "0", "minDepositAmount": "0.01", "minWithdrawAmount": "0.01", "needTag": "false", "rechargeable": "true", "shortName": "BSC", "withdrawConfirm": "15", "withdrawFee": "0.29", "withdrawable": "true"}, {"browserUrl": "https://arbiscan.io/tx/", "chain": "ArbitrumOne", "chainId": "4720", "coinChainId": "4720", "depositConfirm": "24", "extraWithDrawFee": "0", "minDepositAmount": "0.1", "minWithdrawAmount": "10", "needTag": "false", "rechargeable": "true", "shortName": "ARBITRUMONE", "withdrawConfirm": "24", "withdrawFee": "0.8", "withdrawable": "true"}, {"browserUrl": "https://solscan.io/tx/", "chain": "SOL", "chainId": "3000", "coinChainId": "3000", "depositConfirm": "10", "extraWithDrawFee": "0", "minDepositAmount": "0.0014", "minWithdrawAmount": "1", "needTag": "false", "rechargeable": "true", "shortName": "SOL", "withdrawConfirm": "10", "withdrawFee": "1", "withdrawable": "true"}, {"browserUrl": "https://polygonscan.com/tx/", "chain": "Polygon", "chainId": "4400", "coinChainId": "4400", "depositConfirm": "60", "extraWithDrawFee": "0", "minDepositAmount": "0.01", "minWithdrawAmount": "10", "needTag": "false", "rechargeable": "true", "shortName": "POL", "withdrawConfirm": "60", "withdrawFee": "0.2", "withdrawable": "true"}], "coinDisplayName": "USDT", "coinId": "2", "coinName": "USDT", "transfer": "true"}], "msg": "success", "requestTime": "1747311244441"}, "parsedResponse": {"BTC": {"info": {"chains": [{"browserUrl": "https://blockchair.com/bitcoin/transaction/", "chain": "BITCOIN", "chainId": "10", "coinChainId": "10", "depositConfirm": "1", "extraWithDrawFee": "0", "minDepositAmount": "0.00001", "minWithdrawAmount": "0.001", "needTag": "false", "rechargeable": "true", "shortName": "BTC", "withdrawConfirm": "1", "withdrawFee": "0.00008", "withdrawable": "true"}], "coinDisplayName": "BTC", "coinId": "1", "coinName": "BTC", "transfer": "true"}, "id": "BTC", "numericId": 1, "code": "BTC", "precision": null, "type": null, "name": null, "active": true, "deposit": true, "withdraw": true, "fee": 8e-05, "fees": {}, "networks": {"BITCOIN": {"id": "BITCOIN", "network": "BITCOIN", "limits": {"deposit": {"min": 1e-05, "max": null}, "withdraw": {"min": 0.001, "max": null}}, "active": true, "deposit": true, "withdraw": true, "fee": 8e-05, "precision": null, "info": {"browserUrl": "https://blockchair.com/bitcoin/transaction/", "chain": "BITCOIN", "chainId": "10", "coinChainId": "10", "depositConfirm": "1", "extraWithDrawFee": "0", "minDepositAmount": "0.00001", "minWithdrawAmount": "0.001", "needTag": "false", "rechargeable": "true", "shortName": "BTC", "withdrawConfirm": "1", "withdrawFee": "0.00008", "withdrawable": "true"}}}, "limits": {"deposit": {"min": 1e-05, "max": null}, "withdraw": {"min": 0.001, "max": null}}}, "USDT": {"info": {"chains": [{"browserUrl": "https://etherscan.io/tx/", "chain": "ERC20", "chainId": "70", "coinChainId": "70", "depositConfirm": "12", "extraWithDrawFee": "0", "minDepositAmount": "0.9", "minWithdrawAmount": "1", "needTag": "false", "rechargeable": "true", "shortName": "ETH", "withdrawConfirm": "64", "withdrawFee": "3", "withdrawable": "true"}, {"browserUrl": "https://tronscan.org/#/transaction/", "chain": "TRC20", "chainId": "73", "coinChainId": "73", "depositConfirm": "1", "extraWithDrawFee": "0", "minDepositAmount": "1", "minWithdrawAmount": "1", "needTag": "false", "rechargeable": "true", "shortName": "TRX", "withdrawConfirm": "1", "withdrawFee": "1", "withdrawable": "true"}, {"browserUrl": "https://bscscan.com/tx/", "chain": "BEP20", "chainId": "1000", "coinChainId": "1000", "depositConfirm": "15", "extraWithDrawFee": "0", "minDepositAmount": "0.01", "minWithdrawAmount": "0.01", "needTag": "false", "rechargeable": "true", "shortName": "BSC", "withdrawConfirm": "15", "withdrawFee": "0.29", "withdrawable": "true"}, {"browserUrl": "https://arbiscan.io/tx/", "chain": "ArbitrumOne", "chainId": "4720", "coinChainId": "4720", "depositConfirm": "24", "extraWithDrawFee": "0", "minDepositAmount": "0.1", "minWithdrawAmount": "10", "needTag": "false", "rechargeable": "true", "shortName": "ARBITRUMONE", "withdrawConfirm": "24", "withdrawFee": "0.8", "withdrawable": "true"}, {"browserUrl": "https://solscan.io/tx/", "chain": "SOL", "chainId": "3000", "coinChainId": "3000", "depositConfirm": "10", "extraWithDrawFee": "0", "minDepositAmount": "0.0014", "minWithdrawAmount": "1", "needTag": "false", "rechargeable": "true", "shortName": "SOL", "withdrawConfirm": "10", "withdrawFee": "1", "withdrawable": "true"}, {"browserUrl": "https://polygonscan.com/tx/", "chain": "Polygon", "chainId": "4400", "coinChainId": "4400", "depositConfirm": "60", "extraWithDrawFee": "0", "minDepositAmount": "0.01", "minWithdrawAmount": "10", "needTag": "false", "rechargeable": "true", "shortName": "POL", "withdrawConfirm": "60", "withdrawFee": "0.2", "withdrawable": "true"}], "coinDisplayName": "USDT", "coinId": "2", "coinName": "USDT", "transfer": "true"}, "id": "USDT", "numericId": 2, "code": "USDT", "precision": null, "type": null, "name": null, "active": true, "deposit": true, "withdraw": true, "fee": 0.2, "fees": {}, "networks": {"ERC20": {"id": "ERC20", "network": "ERC20", "limits": {"deposit": {"min": 0.9, "max": null}, "withdraw": {"min": 1, "max": null}}, "active": true, "deposit": true, "withdraw": true, "fee": 3, "precision": null, "info": {"browserUrl": "https://etherscan.io/tx/", "chain": "ERC20", "chainId": "70", "coinChainId": "70", "depositConfirm": "12", "extraWithDrawFee": "0", "minDepositAmount": "0.9", "minWithdrawAmount": "1", "needTag": "false", "rechargeable": "true", "shortName": "ETH", "withdrawConfirm": "64", "withdrawFee": "3", "withdrawable": "true"}}, "TRC20": {"id": "TRC20", "network": "TRC20", "limits": {"deposit": {"min": 1, "max": null}, "withdraw": {"min": 1, "max": null}}, "active": true, "deposit": true, "withdraw": true, "fee": 1, "precision": null, "info": {"browserUrl": "https://tronscan.org/#/transaction/", "chain": "TRC20", "chainId": "73", "coinChainId": "73", "depositConfirm": "1", "extraWithDrawFee": "0", "minDepositAmount": "1", "minWithdrawAmount": "1", "needTag": "false", "rechargeable": "true", "shortName": "TRX", "withdrawConfirm": "1", "withdrawFee": "1", "withdrawable": "true"}}, "BEP20": {"id": "BEP20", "network": "BEP20", "limits": {"deposit": {"min": 0.01, "max": null}, "withdraw": {"min": 0.01, "max": null}}, "active": true, "deposit": true, "withdraw": true, "fee": 0.29, "precision": null, "info": {"browserUrl": "https://bscscan.com/tx/", "chain": "BEP20", "chainId": "1000", "coinChainId": "1000", "depositConfirm": "15", "extraWithDrawFee": "0", "minDepositAmount": "0.01", "minWithdrawAmount": "0.01", "needTag": "false", "rechargeable": "true", "shortName": "BSC", "withdrawConfirm": "15", "withdrawFee": "0.29", "withdrawable": "true"}}, "ArbitrumOne": {"id": "ArbitrumOne", "network": "ArbitrumOne", "limits": {"deposit": {"min": 0.1, "max": null}, "withdraw": {"min": 10, "max": null}}, "active": true, "deposit": true, "withdraw": true, "fee": 0.8, "precision": null, "info": {"browserUrl": "https://arbiscan.io/tx/", "chain": "ArbitrumOne", "chainId": "4720", "coinChainId": "4720", "depositConfirm": "24", "extraWithDrawFee": "0", "minDepositAmount": "0.1", "minWithdrawAmount": "10", "needTag": "false", "rechargeable": "true", "shortName": "ARBITRUMONE", "withdrawConfirm": "24", "withdrawFee": "0.8", "withdrawable": "true"}}, "SOL": {"id": "SOL", "network": "SOL", "limits": {"deposit": {"min": 0.0014, "max": null}, "withdraw": {"min": 1, "max": null}}, "active": true, "deposit": true, "withdraw": true, "fee": 1, "precision": null, "info": {"browserUrl": "https://solscan.io/tx/", "chain": "SOL", "chainId": "3000", "coinChainId": "3000", "depositConfirm": "10", "extraWithDrawFee": "0", "minDepositAmount": "0.0014", "minWithdrawAmount": "1", "needTag": "false", "rechargeable": "true", "shortName": "SOL", "withdrawConfirm": "10", "withdrawFee": "1", "withdrawable": "true"}}, "Polygon": {"id": "Polygon", "network": "Polygon", "limits": {"deposit": {"min": 0.01, "max": null}, "withdraw": {"min": 10, "max": null}}, "active": true, "deposit": true, "withdraw": true, "fee": 0.2, "precision": null, "info": {"browserUrl": "https://polygonscan.com/tx/", "chain": "Polygon", "chainId": "4400", "coinChainId": "4400", "depositConfirm": "60", "extraWithDrawFee": "0", "minDepositAmount": "0.01", "minWithdrawAmount": "10", "needTag": "false", "rechargeable": "true", "shortName": "POL", "withdrawConfirm": "60", "withdrawFee": "0.2", "withdrawable": "true"}}}, "limits": {"deposit": {"min": 0.0014, "max": null}, "withdraw": {"min": 0.01, "max": null}}}}}], "fetchTime": [{"description": "Fetch time", "method": "fetchTime", "input": [], "httpResponse": {"code": "00000", "msg": "success", "requestTime": "1726742203660", "data": "1726742203660"}, "parsedResponse": 1726742203660}], "fetchOrderBook": [{"description": "Fetch order book", "method": "fetchOrderBook", "input": ["ETH/USDT", 1], "httpResponse": {"code": "00000", "msg": "success", "requestTime": "1726780941338", "data": {"asks": [[2467.72, 0.8847]], "bids": [[2467.71, 0.1217]], "ts": "1726780941359", "scale": "0.01", "precision": "scale0", "isMaxPrecision": "NO"}}, "parsedResponse": {"symbol": "ETH/USDT", "bids": [[2467.71, 0.1217]], "asks": [[2467.72, 0.8847]], "timestamp": 1726780941359, "datetime": "2024-09-19T21:22:21.359Z", "nonce": null}}], "fetchOHLCV": [{"description": "Fetch OHLCV for spot", "method": "fetchOHLCV", "input": ["ETH/USDT", "1m", 1726956360000, 1], "httpResponse": {"code": "00000", "msg": "success", "requestTime": "1726957385791", "data": [{"open": "2563.55", "high": "2563.55", "low": "2563.5", "close": "2563.52", "quoteVol": "3337.952208", "baseVol": "1.3021", "usdtVol": "3337.952208", "ts": "1726957380000"}]}, "parsedResponse": [[1726957380000, 2563.55, 2563.55, 2563.5, 2563.52, 1.3021]]}, {"description": "Fetch OHLCV for swap", "method": "fetchOHLCV", "input": ["ETH/USDT:USDT", "1m", 1726956360000, 1], "httpResponse": [["1726957440000", "2563.42", "2564.04", "2563.36", "2564.04", "84.15", "215716.0056"]], "parsedResponse": [[1726957440000, 2563.42, 2564.04, 2563.36, 2564.04, 84.15]]}], "fetchFundingRate": [{"description": "Fetch funding rate", "method": "fetchFundingRate", "input": ["ETH/USDT:USDT"], "httpResponse": {"code": "00000", "msg": "success", "requestTime": "1726957973542", "data": {"symbol": "ETHUSDT_UMCBL", "fundingRate": "0.000029"}}, "parsedResponse": {"info": {"symbol": "ETHUSDT_UMCBL", "fundingRate": "0.000029"}, "symbol": "ETH/USDT:USDT", "markPrice": null, "indexPrice": null, "interestRate": null, "estimatedSettlePrice": null, "timestamp": null, "datetime": null, "fundingRate": 2.9e-05, "fundingTimestamp": null, "fundingDatetime": null, "nextFundingRate": null, "nextFundingTimestamp": null, "nextFundingDatetime": null, "previousFundingRate": null, "previousFundingTimestamp": null, "previousFundingDatetime": null}}], "fetchDepositAddress": [{"description": "Fetch deposit address", "method": "fetchDepositAddress", "input": ["USDT", {"network": "TRC20"}], "httpResponse": {"code": "00000", "msg": "success", "requestTime": "1726958196458", "data": {"coin": "USDT", "address": "TKTUt7qiTaMgnTwZXjE3ZBkPB6LKhLPJyZ", "chain": "TRC20", "tag": null, "url": "https://tronscan.org/#/transaction/"}}, "parsedResponse": {"currency": "USDT", "address": "TKTUt7qiTaMgnTwZXjE3ZBkPB6LKhLPJyZ", "tag": null, "network": "TRC20", "info": {"coin": "USDT", "address": "TKTUt7qiTaMgnTwZXjE3ZBkPB6LKhLPJyZ", "chain": "TRC20", "tag": null, "url": "https://tronscan.org/#/transaction/"}}}], "fetchDeposits": [{"description": "Fetch deposits", "method": "fetchDeposits", "input": ["USDT", *************, 1, {"until": 1726386252000}], "httpResponse": {"code": "00000", "msg": "success", "requestTime": "1726958434803", "data": [{"id": "1213046466852196352", "txId": "824246b030cd84d56400661303547f43a1d9fef66cf968628dd5112f362053ff", "coin": "USDT", "type": "deposit", "amount": "99.20000000", "status": "success", "toAddress": "TKTUt7qiTaMgnTwZXjE3ZBkPB6LKhLPJyZ", "fee": null, "chain": "TRX(TRC20)", "confirm": null, "clientOid": null, "tag": null, "fromAddress": null, "dest": "on_chain", "cTime": "1724938735688", "uTime": "1724938746015"}]}, "parsedResponse": [{"info": {"id": "1213046466852196352", "txId": "824246b030cd84d56400661303547f43a1d9fef66cf968628dd5112f362053ff", "coin": "USDT", "type": "deposit", "amount": "99.20000000", "status": "success", "toAddress": "TKTUt7qiTaMgnTwZXjE3ZBkPB6LKhLPJyZ", "fee": null, "chain": "TRX(TRC20)", "confirm": null, "clientOid": null, "tag": null, "fromAddress": null, "dest": "on_chain", "cTime": "1724938735688", "uTime": "1724938746015"}, "id": "1213046466852196352", "txid": "824246b030cd84d56400661303547f43a1d9fef66cf968628dd5112f362053ff", "timestamp": 1724938735688, "datetime": "2024-08-29T13:38:55.688Z", "network": "TRC20", "address": null, "addressTo": "TKTUt7qiTaMgnTwZXjE3ZBkPB6LKhLPJyZ", "addressFrom": null, "tag": null, "tagTo": null, "tagFrom": null, "type": "deposit", "amount": 99.2, "currency": "USDT", "status": "ok", "updated": null, "internal": null, "comment": null, "fee": null}]}], "fetchMyTrades": [{"description": "Fetch my trades", "method": "fetchMyTrades", "input": ["ETH/USDT", *************, 1], "httpResponse": {"code": "00000", "msg": "success", "requestTime": "*************", "data": [{"accountId": "*************", "symbol": "ETHUSDT_SPBL", "orderId": "1217143186968068096", "fillId": "1217143193356505089", "orderType": "market", "side": "buy", "fillPrice": "2340.55", "fillQuantity": "0.0042", "fillTotalAmount": "9.83031", "feeCcy": "ETH", "fees": "-0.0000042", "takerMakerFlag": "taker", "cTime": "*************"}]}, "parsedResponse": [{"id": "1217143193356505089", "order": "1217143186968068096", "timestamp": *************, "datetime": "2024-09-09T20:57:51.400Z", "symbol": "ETH/USDT", "type": "market", "side": "buy", "takerOrMaker": "taker", "price": 2340.55, "amount": 0.0042, "cost": 9.83031, "fee": {"currency": "ETH", "cost": 4.2e-06}, "info": {"accountId": "*************", "symbol": "ETHUSDT_SPBL", "orderId": "1217143186968068096", "fillId": "1217143193356505089", "orderType": "market", "side": "buy", "fillPrice": "2340.55", "fillQuantity": "0.0042", "fillTotalAmount": "9.83031", "feeCcy": "ETH", "fees": "-0.0000042", "takerMakerFlag": "taker", "cTime": "*************"}, "fees": [{"currency": "ETH", "cost": 4.2e-06}]}]}], "fetchMarginMode": [{"description": "Fetch margin mode", "method": "fetchMarginMode", "input": ["ETH/USDT:USDT"], "httpResponse": {"code": "00000", "msg": "success", "requestTime": "*************", "data": {"marginCoin": "USDT", "locked": "0", "available": "59.********", "crossMaxAvailable": "55.********", "fixedMaxAvailable": "55.********", "maxTransferOut": "54.********", "equity": "60.********", "usdtEquity": "60.**********", "btcEquity": "0.***********", "crossRiskRate": "0.************", "crossMarginLeverage": "5", "fixedLongLeverage": "10", "fixedShortLeverage": "10", "marginMode": "crossed", "holdMode": "double_hold", "unrealizedPL": "0.1964", "bonus": "0", "crossedUnrealizedPL": "0.1964", "isolatedUnrealizedPL": ""}}, "parsedResponse": {"info": {"marginCoin": "USDT", "locked": "0", "available": "59.********", "crossMaxAvailable": "55.********", "fixedMaxAvailable": "55.********", "maxTransferOut": "54.********", "equity": "60.********", "usdtEquity": "60.**********", "btcEquity": "0.***********", "crossRiskRate": "0.************", "crossMarginLeverage": "5", "fixedLongLeverage": "10", "fixedShortLeverage": "10", "marginMode": "crossed", "holdMode": "double_hold", "unrealizedPL": "0.1964", "bonus": "0", "crossedUnrealizedPL": "0.1964", "isolatedUnrealizedPL": ""}, "symbol": "ETH/USDT:USDT", "marginMode": "cross"}}], "setMarginMode": [{"description": "Set margin mode", "method": "setMarginMode", "input": ["cross", "ETH/USDT:USDT"], "httpResponse": {"code": "00000", "msg": "success", "requestTime": "1726958903442", "data": {"symbol": "ETHUSDT_UMCBL", "marginCoin": "USDT", "longLeverage": "5", "shortLeverage": "5", "crossMarginLeverage": null, "marginMode": "crossed"}}, "parsedResponse": {"code": "00000", "msg": "success", "requestTime": "1726958903442", "data": {"symbol": "ETHUSDT_UMCBL", "marginCoin": "USDT", "longLeverage": "5", "shortLeverage": "5", "crossMarginLeverage": null, "marginMode": "crossed"}}}], "fetchPositionMode": [{"description": "Fetch position mode", "method": "fetchPositionMode", "input": ["ETH/USDT:USDT"], "httpResponse": {"code": "00000", "msg": "success", "requestTime": "1726958984026", "data": {"marginCoin": "USDT", "locked": "0", "available": "59.********", "crossMaxAvailable": "55.00803155", "fixedMaxAvailable": "55.00803155", "maxTransferOut": "54.********", "equity": "60.11827155", "usdtEquity": "60.1182715547", "btcEquity": "0.000951879277", "crossRiskRate": "0.001666292417", "crossMarginLeverage": "5", "fixedLongLeverage": "10", "fixedShortLeverage": "10", "marginMode": "crossed", "holdMode": "double_hold", "unrealizedPL": "0.1346", "bonus": "0", "crossedUnrealizedPL": "0.1346", "isolatedUnrealizedPL": ""}}, "parsedResponse": {"info": {"code": "00000", "msg": "success", "requestTime": "1726958984026", "data": {"marginCoin": "USDT", "locked": "0", "available": "59.********", "crossMaxAvailable": "55.00803155", "fixedMaxAvailable": "55.00803155", "maxTransferOut": "54.********", "equity": "60.11827155", "usdtEquity": "60.1182715547", "btcEquity": "0.000951879277", "crossRiskRate": "0.001666292417", "crossMarginLeverage": "5", "fixedLongLeverage": "10", "fixedShortLeverage": "10", "marginMode": "crossed", "holdMode": "double_hold", "unrealizedPL": "0.1346", "bonus": "0", "crossedUnrealizedPL": "0.1346", "isolatedUnrealizedPL": ""}}, "hedged": true}}], "setPositionMode": [{"description": "Set position mode", "method": "setPositionMode", "input": [true, "ETH/USDT:USDT"], "httpResponse": {"code": "00000", "msg": "success", "requestTime": "1726959061849", "data": {"marginCoin": "USDT", "dualSidePosition": true}}, "parsedResponse": {"code": "00000", "msg": "success", "requestTime": "1726959061849", "data": {"marginCoin": "USDT", "dualSidePosition": true}}}], "fetchLeverage": [{"description": "Fetch leverage", "method": "fetchLeverage", "input": ["ETH/USDT:USDT"], "httpResponse": {"code": "00000", "msg": "success", "requestTime": "1726959119499", "data": {"marginCoin": "USDT", "locked": "0", "available": "59.********", "crossMaxAvailable": "55.03953155", "fixedMaxAvailable": "55.03953155", "maxTransferOut": "54.********", "equity": "60.14977155", "usdtEquity": "60.1497715547", "btcEquity": "0.000951788617", "crossRiskRate": "0.001667462194", "crossMarginLeverage": "5", "fixedLongLeverage": "10", "fixedShortLeverage": "10", "marginMode": "crossed", "holdMode": "double_hold", "unrealizedPL": "0.1661", "bonus": "0", "crossedUnrealizedPL": "0.1661", "isolatedUnrealizedPL": ""}}, "parsedResponse": {"info": {"marginCoin": "USDT", "locked": "0", "available": "59.********", "crossMaxAvailable": "55.03953155", "fixedMaxAvailable": "55.03953155", "maxTransferOut": "54.********", "equity": "60.14977155", "usdtEquity": "60.1497715547", "btcEquity": "0.000951788617", "crossRiskRate": "0.001667462194", "crossMarginLeverage": "5", "fixedLongLeverage": "10", "fixedShortLeverage": "10", "marginMode": "crossed", "holdMode": "double_hold", "unrealizedPL": "0.1661", "bonus": "0", "crossedUnrealizedPL": "0.1661", "isolatedUnrealizedPL": ""}, "symbol": "ETH/USDT:USDT", "marginMode": "cross", "longLeverage": 5, "shortLeverage": 5}}], "setLeverage": [{"description": "Set leverage", "method": "setLeverage", "input": [5, "ETH/USDT:USDT"], "httpResponse": {"code": "00000", "msg": "success", "requestTime": "1726959175416", "data": {"symbol": "ETHUSDT_UMCBL", "marginCoin": "USDT", "longLeverage": "5", "shortLeverage": "5", "crossMarginLeverage": "5", "marginMode": "crossed"}}, "parsedResponse": {"info": {"symbol": "ETHUSDT_UMCBL", "marginCoin": "USDT", "longLeverage": "5", "shortLeverage": "5", "crossMarginLeverage": "5", "marginMode": "crossed"}, "symbol": "ETH/USDT:USDT", "marginMode": "cross", "longLeverage": 5, "shortLeverage": 5}}], "fetchPosition": [{"description": "Fetch position", "method": "fetchPosition", "input": ["ETH/USDT:USDT"], "httpResponse": {"code": "00000", "msg": "success", "requestTime": "1728409130017", "data": [{"marginCoin": "USDT", "symbol": "ETHUSDT_UMCBL", "holdSide": "long", "openDelegateCount": "0", "margin": "0", "available": "0", "locked": "0", "total": "0", "leverage": "5", "achievedProfits": null, "averageOpenPrice": null, "marginMode": "crossed", "holdMode": "single_hold", "unrealizedPL": null, "liquidationPrice": null, "keepMarginRate": null, "marketPrice": null, "marginRatio": null, "cTime": null}, {"marginCoin": "USDT", "symbol": "ETHUSDT_UMCBL", "holdSide": "short", "openDelegateCount": "0", "margin": "0", "available": "0", "locked": "0", "total": "0", "leverage": "5", "achievedProfits": null, "averageOpenPrice": null, "marginMode": "crossed", "holdMode": "single_hold", "unrealizedPL": null, "liquidationPrice": null, "keepMarginRate": null, "marketPrice": null, "marginRatio": null, "cTime": null}]}, "parsedResponse": {"symbol": "ETH/USDT:USDT", "id": null, "timestamp": null, "datetime": null, "contracts": 0, "contractSize": 0.01, "side": "long", "notional": 0, "leverage": 5, "unrealizedPnl": null, "realizedPnl": null, "collateral": null, "entryPrice": null, "markPrice": null, "liquidationPrice": null, "marginMode": "cross", "hedged": false, "maintenanceMargin": null, "maintenanceMarginPercentage": null, "initialMargin": 0, "initialMarginPercentage": null, "marginRatio": null, "lastUpdateTimestamp": null, "lastPrice": null, "stopLossPrice": null, "takeProfitPrice": null, "percentage": null, "info": {"marginCoin": "USDT", "symbol": "ETHUSDT_UMCBL", "holdSide": "long", "openDelegateCount": "0", "margin": "0", "available": "0", "locked": "0", "total": "0", "leverage": "5", "achievedProfits": null, "averageOpenPrice": null, "marginMode": "crossed", "holdMode": "single_hold", "unrealizedPL": null, "liquidationPrice": null, "keepMarginRate": null, "marketPrice": null, "marginRatio": null, "cTime": null}}}], "createOrder": [{"description": "Create order", "method": "createOrder", "input": ["BTC/USDT", "market", "sell", 2e-05], "httpResponse": {"code": "00000", "msg": "success", "requestTime": "1729510697635", "data": {"orderId": "1232222665168039937", "clientOrderId": "3d13ec41-ee8b-4b76-90cc-c98add74ecd1"}}, "parsedResponse": {"id": "1232222665168039937", "clientOrderId": "3d13ec41-ee8b-4b76-90cc-c98add74ecd1", "datetime": null, "timestamp": null, "lastTradeTimestamp": null, "lastUpdateTimestamp": null, "status": null, "symbol": "BTC/USDT", "type": null, "timeInForce": null, "side": null, "price": null, "average": null, "amount": null, "filled": null, "remaining": null, "stopPrice": null, "triggerPrice": null, "takeProfitPrice": null, "stopLossPrice": null, "cost": null, "trades": [], "fee": {"currency": null, "cost": null}, "fees": [{"currency": null, "cost": null}], "reduceOnly": null, "postOnly": null, "info": {"orderId": "1232222665168039937", "clientOrderId": "3d13ec41-ee8b-4b76-90cc-c98add74ecd1"}}}], "fetchOpenOrders": [{"description": "Fetch open orders", "method": "fetchOpenOrders", "input": ["LTC/USDT"], "httpResponse": {"code": "00000", "msg": "success", "requestTime": "*************", "data": [{"accountId": "*************", "symbol": "LTCUSDT_SPBL", "orderId": "1232224960702230528", "clientOrderId": "e7436cfb-9fad-4891-b18c-3b177f6f0434", "price": "90.****************", "quantity": "0.****************", "orderType": "limit", "side": "sell", "status": "new", "fillPrice": "0", "fillQuantity": "0.****************", "fillTotalAmount": "0.****************", "enterPointSource": "API", "feeDetail": "", "orderSource": "normal", "cTime": "*************"}]}, "parsedResponse": [{"id": "1232224960702230528", "clientOrderId": "e7436cfb-9fad-4891-b18c-3b177f6f0434", "datetime": "2024-10-21T11:47:24.941Z", "timestamp": *************, "lastTradeTimestamp": null, "lastUpdateTimestamp": null, "status": "open", "symbol": "LTC/USDT", "type": "limit", "timeInForce": null, "side": "sell", "price": 90, "average": null, "amount": 0.2, "filled": 0, "remaining": 0.2, "stopPrice": null, "triggerPrice": null, "takeProfitPrice": null, "stopLossPrice": null, "cost": 0, "trades": [], "fee": {"currency": null, "cost": null}, "fees": [{"currency": null, "cost": null}], "reduceOnly": null, "postOnly": null, "info": {"accountId": "*************", "symbol": "LTCUSDT_SPBL", "orderId": "1232224960702230528", "clientOrderId": "e7436cfb-9fad-4891-b18c-3b177f6f0434", "price": "90.****************", "quantity": "0.****************", "orderType": "limit", "side": "sell", "status": "new", "fillPrice": "0", "fillQuantity": "0.****************", "fillTotalAmount": "0.****************", "enterPointSource": "API", "feeDetail": "", "orderSource": "normal", "cTime": "*************"}}]}], "fetchBalance": [{"description": "Fetch balance", "method": "fetchBalance", "input": [], "httpResponse": {"code": "00000", "msg": "success", "requestTime": "*************", "data": [{"coinId": "1", "coinName": "BTC", "coinDisplayName": "BTC", "available": "0.********", "frozen": "0.********", "lock": "0.********", "uTime": "*************"}, {"coinId": "3", "coinName": "ETH", "coinDisplayName": "ETH", "available": "0.********", "frozen": "0.********", "lock": "0.********", "uTime": "*************"}, {"coinId": "2", "coinName": "USDT", "coinDisplayName": "USDT", "available": "2.57886818", "frozen": "0.********", "lock": "0.********", "uTime": "*************"}, {"coinId": "5", "coinName": "LTC", "coinDisplayName": "LTC", "available": "0.01898080", "frozen": "0.20000000", "lock": "0.********", "uTime": "1729511245000"}]}, "parsedResponse": {"info": [{"coinId": "1", "coinName": "BTC", "coinDisplayName": "BTC", "available": "0.********", "frozen": "0.********", "lock": "0.********", "uTime": "*************"}, {"coinId": "3", "coinName": "ETH", "coinDisplayName": "ETH", "available": "0.********", "frozen": "0.********", "lock": "0.********", "uTime": "*************"}, {"coinId": "2", "coinName": "USDT", "coinDisplayName": "USDT", "available": "2.57886818", "frozen": "0.********", "lock": "0.********", "uTime": "*************"}, {"coinId": "5", "coinName": "LTC", "coinDisplayName": "LTC", "available": "0.01898080", "frozen": "0.20000000", "lock": "0.********", "uTime": "1729511245000"}], "BTC": {"free": 9.87e-06, "used": 0, "total": 9.87e-06}, "ETH": {"free": 9.3e-06, "used": 0, "total": 9.3e-06}, "USDT": {"free": 2.57886818, "used": 0, "total": 2.57886818}, "LTC": {"free": 0.0189808, "used": 0.2, "total": 0.2189808}, "free": {"BTC": 9.87e-06, "ETH": 9.3e-06, "USDT": 2.57886818, "LTC": 0.0189808}, "used": {"BTC": 0, "ETH": 0, "USDT": 0, "LTC": 0.2}, "total": {"BTC": 9.87e-06, "ETH": 9.3e-06, "USDT": 2.57886818, "LTC": 0.2189808}}}]}}