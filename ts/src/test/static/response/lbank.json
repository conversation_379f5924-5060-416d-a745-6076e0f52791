{"exchange": "lbank", "skipKeys": [], "methods": {"fetchCurrencies": [{"description": "fetchCurrencies", "method": "fetchCurrencies", "input": [], "httpResponse": {"msg": "Success", "result": "true", "data": [{"amountScale": "4", "chain": "bep20(bsc)", "assetCode": "usdt", "min": "10", "transferAmtScale": "4", "canWithDraw": true, "fee": "0.0000", "minTransfer": "0.0001", "type": "1"}, {"amountScale": "4", "chain": "trc20", "assetCode": "usdt", "min": "1", "transferAmtScale": "4", "canWithDraw": true, "fee": "1.0000", "minTransfer": "0.0001", "type": "1"}, {"amountScale": "4", "chain": "erc20", "assetCode": "usdt", "min": "1", "transferAmtScale": "4", "canWithDraw": true, "fee": "6.0000", "minTransfer": "0.0001", "type": "1"}, {"amountScale": "4", "chain": "polygon", "assetCode": "usdt", "min": "1", "transferAmtScale": "4", "canWithDraw": true, "fee": "1.0000", "minTransfer": "0.0001", "type": "1"}, {"amountScale": "4", "chain": "omni", "assetCode": "usdt", "min": "0", "transferAmtScale": "4", "canWithDraw": false, "minTransfer": "0", "type": "1"}, {"amountScale": "4", "chain": "arbitrum one", "assetCode": "usdt", "min": "1", "transferAmtScale": "4", "canWithDraw": true, "fee": "1.0000", "minTransfer": "0.0001", "type": "1"}, {"amountScale": "4", "chain": "solana", "assetCode": "usdt", "min": "1", "transferAmtScale": "4", "canWithDraw": true, "fee": "1.0000", "minTransfer": "0.0001", "type": "1"}, {"amountScale": "4", "chain": "ton", "assetCode": "usdt", "min": "1", "transferAmtScale": "4", "canWithDraw": true, "fee": "1.0000", "minTransfer": "0.0001", "type": "1"}, {"amountScale": "4", "chain": "near", "assetCode": "usdt", "min": "1", "transferAmtScale": "4", "canWithDraw": true, "fee": "1.0000", "minTransfer": "0.0001", "type": "1"}, {"amountScale": "4", "chain": "c-chain", "assetCode": "usdt", "min": "1", "transferAmtScale": "4", "canWithDraw": false, "fee": "1.0000", "minTransfer": "0.0001", "type": "1"}], "error_code": "0", "ts": "1750830601761"}, "parsedResponse": {"USDT": {"info": [{"amountScale": "4", "chain": "bep20(bsc)", "assetCode": "usdt", "min": "10", "transferAmtScale": "4", "canWithDraw": true, "fee": "0.0000", "minTransfer": "0.0001", "type": "1"}, {"amountScale": "4", "chain": "trc20", "assetCode": "usdt", "min": "1", "transferAmtScale": "4", "canWithDraw": true, "fee": "1.0000", "minTransfer": "0.0001", "type": "1"}, {"amountScale": "4", "chain": "erc20", "assetCode": "usdt", "min": "1", "transferAmtScale": "4", "canWithDraw": true, "fee": "6.0000", "minTransfer": "0.0001", "type": "1"}, {"amountScale": "4", "chain": "polygon", "assetCode": "usdt", "min": "1", "transferAmtScale": "4", "canWithDraw": true, "fee": "1.0000", "minTransfer": "0.0001", "type": "1"}, {"amountScale": "4", "chain": "omni", "assetCode": "usdt", "min": "0", "transferAmtScale": "4", "canWithDraw": false, "minTransfer": "0", "type": "1"}, {"amountScale": "4", "chain": "arbitrum one", "assetCode": "usdt", "min": "1", "transferAmtScale": "4", "canWithDraw": true, "fee": "1.0000", "minTransfer": "0.0001", "type": "1"}, {"amountScale": "4", "chain": "solana", "assetCode": "usdt", "min": "1", "transferAmtScale": "4", "canWithDraw": true, "fee": "1.0000", "minTransfer": "0.0001", "type": "1"}, {"amountScale": "4", "chain": "ton", "assetCode": "usdt", "min": "1", "transferAmtScale": "4", "canWithDraw": true, "fee": "1.0000", "minTransfer": "0.0001", "type": "1"}, {"amountScale": "4", "chain": "near", "assetCode": "usdt", "min": "1", "transferAmtScale": "4", "canWithDraw": true, "fee": "1.0000", "minTransfer": "0.0001", "type": "1"}, {"amountScale": "4", "chain": "c-chain", "assetCode": "usdt", "min": "1", "transferAmtScale": "4", "canWithDraw": false, "fee": "1.0000", "minTransfer": "0.0001", "type": "1"}], "id": "usdt", "numericId": null, "code": "USDT", "precision": 0.0001, "type": null, "name": null, "active": null, "deposit": null, "withdraw": true, "fee": 0, "fees": {}, "networks": {"BEP20": {"id": "bep20(bsc)", "network": "BEP20", "limits": {"withdraw": {"min": 10, "max": null}, "deposit": {"min": 0.0001, "max": null}}, "active": null, "deposit": null, "withdraw": true, "fee": 0, "precision": 0.0001, "info": {"amountScale": "4", "chain": "bep20(bsc)", "assetCode": "usdt", "min": "10", "transferAmtScale": "4", "canWithDraw": true, "fee": "0.0000", "minTransfer": "0.0001", "type": "1"}}, "TRC20": {"id": "trc20", "network": "TRC20", "limits": {"withdraw": {"min": 1, "max": null}, "deposit": {"min": 0.0001, "max": null}}, "active": null, "deposit": null, "withdraw": true, "fee": 1, "precision": 0.0001, "info": {"amountScale": "4", "chain": "trc20", "assetCode": "usdt", "min": "1", "transferAmtScale": "4", "canWithDraw": true, "fee": "1.0000", "minTransfer": "0.0001", "type": "1"}}, "ERC20": {"id": "erc20", "network": "ERC20", "limits": {"withdraw": {"min": 1, "max": null}, "deposit": {"min": 0.0001, "max": null}}, "active": null, "deposit": null, "withdraw": true, "fee": 6, "precision": 0.0001, "info": {"amountScale": "4", "chain": "erc20", "assetCode": "usdt", "min": "1", "transferAmtScale": "4", "canWithDraw": true, "fee": "6.0000", "minTransfer": "0.0001", "type": "1"}}, "polygon": {"id": "polygon", "network": "polygon", "limits": {"withdraw": {"min": 1, "max": null}, "deposit": {"min": 0.0001, "max": null}}, "active": null, "deposit": null, "withdraw": true, "fee": 1, "precision": 0.0001, "info": {"amountScale": "4", "chain": "polygon", "assetCode": "usdt", "min": "1", "transferAmtScale": "4", "canWithDraw": true, "fee": "1.0000", "minTransfer": "0.0001", "type": "1"}}, "OMNI": {"id": "omni", "network": "OMNI", "limits": {"withdraw": {"min": 0, "max": null}, "deposit": {"min": 0, "max": null}}, "active": null, "deposit": null, "withdraw": false, "fee": null, "precision": 0.0001, "info": {"amountScale": "4", "chain": "omni", "assetCode": "usdt", "min": "0", "transferAmtScale": "4", "canWithDraw": false, "minTransfer": "0", "type": "1"}}, "arbitrum one": {"id": "arbitrum one", "network": "arbitrum one", "limits": {"withdraw": {"min": 1, "max": null}, "deposit": {"min": 0.0001, "max": null}}, "active": null, "deposit": null, "withdraw": true, "fee": 1, "precision": 0.0001, "info": {"amountScale": "4", "chain": "arbitrum one", "assetCode": "usdt", "min": "1", "transferAmtScale": "4", "canWithDraw": true, "fee": "1.0000", "minTransfer": "0.0001", "type": "1"}}, "solana": {"id": "solana", "network": "solana", "limits": {"withdraw": {"min": 1, "max": null}, "deposit": {"min": 0.0001, "max": null}}, "active": null, "deposit": null, "withdraw": true, "fee": 1, "precision": 0.0001, "info": {"amountScale": "4", "chain": "solana", "assetCode": "usdt", "min": "1", "transferAmtScale": "4", "canWithDraw": true, "fee": "1.0000", "minTransfer": "0.0001", "type": "1"}}, "ton": {"id": "ton", "network": "ton", "limits": {"withdraw": {"min": 1, "max": null}, "deposit": {"min": 0.0001, "max": null}}, "active": null, "deposit": null, "withdraw": true, "fee": 1, "precision": 0.0001, "info": {"amountScale": "4", "chain": "ton", "assetCode": "usdt", "min": "1", "transferAmtScale": "4", "canWithDraw": true, "fee": "1.0000", "minTransfer": "0.0001", "type": "1"}}, "near": {"id": "near", "network": "near", "limits": {"withdraw": {"min": 1, "max": null}, "deposit": {"min": 0.0001, "max": null}}, "active": null, "deposit": null, "withdraw": true, "fee": 1, "precision": 0.0001, "info": {"amountScale": "4", "chain": "near", "assetCode": "usdt", "min": "1", "transferAmtScale": "4", "canWithDraw": true, "fee": "1.0000", "minTransfer": "0.0001", "type": "1"}}, "c-chain": {"id": "c-chain", "network": "c-chain", "limits": {"withdraw": {"min": 1, "max": null}, "deposit": {"min": 0.0001, "max": null}}, "active": null, "deposit": null, "withdraw": false, "fee": 1, "precision": 0.0001, "info": {"amountScale": "4", "chain": "c-chain", "assetCode": "usdt", "min": "1", "transferAmtScale": "4", "canWithDraw": false, "fee": "1.0000", "minTransfer": "0.0001", "type": "1"}}}, "limits": {"withdraw": {"min": 0, "max": null}, "deposit": {"min": 0, "max": null}}}}}], "fetchTrades": [{"description": "public spot trades", "method": "fetchTrades", "input": ["BTC/USDT", null, 1], "httpResponse": {"result": "true", "data": [{"date_ms": "1710327656856", "amount": "0.0347", "price": "73238.2", "type": "sell", "tid": "448d76dfada24311899d7f8ee925c7f3"}], "error_code": "0", "ts": "1710327657210"}, "parsedResponse": [{"timestamp": 1710327656856, "datetime": "2024-03-13T11:00:56.856Z", "symbol": "BTC/USDT", "id": "448d76dfada24311899d7f8ee925c7f3", "order": null, "type": "limit", "takerOrMaker": "taker", "side": "sell", "price": 73238.2, "amount": 0.0347, "cost": 2541.36554, "fee": {"cost": null, "currency": null}, "info": {"date_ms": "1710327656856", "amount": "0.0347", "price": "73238.2", "type": "sell", "tid": "448d76dfada24311899d7f8ee925c7f3"}, "fees": []}]}], "fetchTicker": [{"description": "public spot ticker", "method": "fetchTicker", "input": ["BTC/USDT"], "httpResponse": {"result": "true", "data": [{"symbol": "btc_usdt", "ticker": {"high": "73640.51", "vol": "12127.5132", "low": "68677.55", "change": "1.77", "turnover": "869993132.77", "latest": "73380.29"}, "timestamp": "1710328237047"}], "error_code": "0", "ts": "1710328244598"}, "parsedResponse": {"symbol": "BTC/USDT", "timestamp": 1710328237047, "datetime": "2024-03-13T11:10:37.047Z", "high": 73640.51, "low": 68677.55, "bid": null, "bidVolume": null, "ask": null, "askVolume": null, "vwap": 71737.14168952625, "open": 72104.04834430579, "close": 73380.29, "last": 73380.29, "previousClose": null, "change": 1276.2416556942123, "percentage": 1.77, "average": 72742.16, "baseVolume": 12127.5132, "quoteVolume": 869993132.77, "markPrice": null, "indexPrice": null, "info": {"symbol": "btc_usdt", "ticker": {"high": "73640.51", "vol": "12127.5132", "low": "68677.55", "change": "1.77", "turnover": "869993132.77", "latest": "73380.29"}, "timestamp": "1710328237047"}}}], "fetchOHLCV": [{"description": "should return updated candle", "disabled": true, "method": "fetchOHLCV", "input": ["BTC/USDT", "1h", null, 5], "httpResponse": {"msg": "Success", "result": "true", "data": [[1729144800, 67344.21, 67503.97, 67125.2, 67149.99, 240.5828], [1729148400, 67149.99, 67304.47, 67088.99, 67302.74, 297.7536], [1729152000, 67302.74, 67473.19, 67195.47, 67449.8, 234.7698], [1729155600, 67449.8, 67529.62, 67185.9, 67191.03, 235.5836], [1729159200, 67191.03, 67295.34, 66820.52, 67213.82, 258.9329], [1729162800, 67213.82, 67238.34, 67209.83, 67235.47, 5.3682]], "error_code": "0", "ts": "1729162874772"}, "parsedResponse": [[1729148400000, 67149.99, 67304.47, 67088.99, 67302.74, 297.7536], [1729152000000, 67302.74, 67473.19, 67195.47, 67449.8, 234.7698], [1729155600000, 67449.8, 67529.62, 67185.9, 67191.03, 235.5836], [1729159200000, 67191.03, 67295.34, 66820.52, 67213.82, 258.9329], [1729162800000, 67213.82, 67238.34, 67209.83, 67235.47, 5.3682]]}]}}