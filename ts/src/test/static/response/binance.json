{"exchange": "binance", "skipKeys": [], "options": {"leverageBrackets": {"BTC/USDT:USDT": [["0", "0.004"], ["50000", "0.005"], ["250000", "0.01"], ["3000000", "0.025"], ["20000000", "0.05"], ["40000000", "0.1"], ["1********", "0.125"], ["120000000", "0.15"], ["2********", "0.25"], ["3********", "0.5"]], "ETH/USDT:USDT": [["0", "0.005"], ["100000", "0.0065"], ["250000", "0.01"], ["2000000", "0.02"], ["15000000", "0.05"], ["30000000", "0.1"], ["60000000", "0.125"], ["80000000", "0.15"], ["1********", "0.25"], ["150000000", "0.5"]], "LTC/USDT:USDT": [["0", "0.0065"], ["10000", "0.01"], ["50000", "0.02"], ["250000", "0.05"], ["1000000", "0.1"], ["2000000", "0.125"], ["5000000", "0.15"], ["10000000", "0.25"]], "ADA/USDT:USDT": [["0", "0.0065"], ["10000", "0.01"], ["50000", "0.02"], ["250000", "0.05"], ["1000000", "0.1"], ["2000000", "0.125"], ["5000000", "0.15"], ["10000000", "0.25"]], "BTC/USD:BTC": [["0", "0.004"], ["5", "0.005"], ["10", "0.01"], ["20", "0.025"], ["50", "0.05"], ["100", "0.1"], ["200", "0.125"], ["400", "0.15"], ["1000", "0.25"], ["1500", "0.5"]]}}, "methods": {"fetchDepositAddress": [{"description": "with code", "method": "fetchDepositAddress", "input": ["USDT"], "httpResponse": {"coin": "USDT", "address": "0x437ef27bace760782226ba15217735b05a2edc811", "tag": "", "url": "https://etherscan.io/address/0x437ef27bace760782226ba15217735b05a2edc811", "isDefault": "0"}, "parsedResponse": {"info": {"coin": "USDT", "address": "0x437ef27bace760782226ba15217735b05a2edc811", "tag": "", "url": "https://etherscan.io/address/0x437ef27bace760782226ba15217735b05a2edc811", "isDefault": "0"}, "currency": "USDT", "network": "ERC20", "address": "0x437ef27bace760782226ba15217735b05a2edc811", "tag": null}}, {"description": "with network ETH", "method": "fetchDepositAddress", "input": ["USDT", {"network": "ETH"}], "httpResponse": {"coin": "USDT", "address": "0x437ef27bace760782226ba15217735b05a2edc811", "tag": "", "url": "https://etherscan.io/address/0x437ef27bace760782226ba15217735b05a2edc811", "isDefault": "0"}, "parsedResponse": {"info": {"coin": "USDT", "address": "0x437ef27bace760782226ba15217735b05a2edc811", "tag": "", "url": "https://etherscan.io/address/0x437ef27bace760782226ba15217735b05a2edc811", "isDefault": "0"}, "currency": "USDT", "network": "ERC20", "address": "0x437ef27bace760782226ba15217735b05a2edc811", "tag": null}}, {"description": "with networkCode", "method": "fetchDepositAddress", "input": ["USDT", {"network": "ERC20"}], "httpResponse": {"coin": "USDT", "address": "0x437ef27bace760782226ba15217735b05a2edc811", "tag": "", "url": "https://etherscan.io/address/0x437ef27bace760782226ba15217735b05a2edc811", "isDefault": "0"}, "parsedResponse": {"info": {"coin": "USDT", "address": "0x437ef27bace760782226ba15217735b05a2edc811", "tag": "", "url": "https://etherscan.io/address/0x437ef27bace760782226ba15217735b05a2edc811", "isDefault": "0"}, "currency": "USDT", "network": "ERC20", "address": "0x437ef27bace760782226ba15217735b05a2edc811", "tag": null}}, {"description": "with network TRC20", "method": "fetchDepositAddress", "input": ["USDT", {"network": "TRC20"}], "httpResponse": {"coin": "USDT", "address": "TGC4Fq6Mum7kE2eEHkt8RudZY1o8oqkvBu", "tag": "", "url": "https://tronscan.org/#/address/TGC4Fq6Mum7kE2eEHkt8RudZY1o8oqkvBu", "isDefault": "0"}, "parsedResponse": {"info": {"coin": "USDT", "address": "TGC4Fq6Mum7kE2eEHkt8RudZY1o8oqkvBu", "tag": "", "url": "https://tronscan.org/#/address/TGC4Fq6Mum7kE2eEHkt8RudZY1o8oqkvBu", "isDefault": "0"}, "currency": "USDT", "network": "TRC20", "address": "TGC4Fq6Mum7kE2eEHkt8RudZY1o8oqkvBu", "tag": null}}], "createOrder": [{"description": "spot +selfTradePrevention +iceberg", "method": "createOrder", "input": ["SOL/USDT", "limit", "buy", 0.05, 241, {"selfTradePrevention": "expire_maker", "icebergAmount": 0.03}], "httpResponse": {"symbol": "SOLUSDT", "orderId": "9780331417", "orderListId": "-1", "clientOrderId": "x-R4BD3S824d31be3228a348eaa0a303", "transactTime": "1737635557417", "price": "241.********", "origQty": "0.05000000", "executedQty": "0.********", "origQuoteOrderQty": "0.********", "cummulativeQuoteQty": "0.********", "status": "NEW", "timeInForce": "GTC", "type": "LIMIT", "side": "BUY", "workingTime": "1737635557417", "icebergQty": "0.03000000", "fills": [], "selfTradePreventionMode": "EXPIRE_MAKER"}, "parsedResponse": {"info": {"symbol": "SOLUSDT", "orderId": "9780331417", "orderListId": "-1", "clientOrderId": "x-R4BD3S824d31be3228a348eaa0a303", "transactTime": "1737635557417", "price": "241.********", "origQty": "0.05000000", "executedQty": "0.********", "origQuoteOrderQty": "0.********", "cummulativeQuoteQty": "0.********", "status": "NEW", "timeInForce": "GTC", "type": "LIMIT", "side": "BUY", "workingTime": "1737635557417", "icebergQty": "0.03000000", "fills": [], "selfTradePreventionMode": "EXPIRE_MAKER"}, "id": "9780331417", "clientOrderId": "x-R4BD3S824d31be3228a348eaa0a303", "timestamp": 1737635557417, "datetime": "2025-01-23T12:32:37.417Z", "lastTradeTimestamp": null, "lastUpdateTimestamp": 1737635557417, "symbol": "SOL/USDT", "type": "limit", "timeInForce": "GTC", "postOnly": false, "reduceOnly": null, "side": "buy", "price": 241, "triggerPrice": null, "amount": 0.05, "cost": 0, "average": null, "filled": 0, "remaining": 0.05, "status": "open", "fee": null, "trades": [], "fees": [], "stopPrice": null, "takeProfitPrice": null, "stopLossPrice": null}}], "createOrders": [{"description": "create swap orders", "method": "createOrders", "input": [[{"symbol": "BTC/USDT:USDT", "type": "limit", "side": "sell", "amount": 0.2, "price": 110001}]], "httpResponse": [{"orderId": 4087922044, "symbol": "BTCUSDT", "status": "NEW", "clientOrderId": "x-xcKtGhcu04075babaeb34c55af79ba", "price": "110001.00", "avgPrice": "0.00", "origQty": "0.200", "executedQty": "0.000", "cumQty": "0.000", "cumQuote": "0.00000", "timeInForce": "GTC", "type": "LIMIT", "reduceOnly": false, "closePosition": false, "side": "SELL", "positionSide": "BOTH", "stopPrice": "0.00", "workingType": "CONTRACT_PRICE", "priceProtect": false, "origType": "LIMIT", "priceMatch": "NONE", "selfTradePreventionMode": "NONE", "goodTillDate": 0, "updateTime": 1739781831932}], "parsedResponse": [{"info": {"orderId": 4087922044, "symbol": "BTCUSDT", "status": "NEW", "clientOrderId": "x-xcKtGhcu04075babaeb34c55af79ba", "price": "110001.00", "avgPrice": "0.00", "origQty": "0.200", "executedQty": "0.000", "cumQty": "0.000", "cumQuote": "0.00000", "timeInForce": "GTC", "type": "LIMIT", "reduceOnly": false, "closePosition": false, "side": "SELL", "positionSide": "BOTH", "stopPrice": "0.00", "workingType": "CONTRACT_PRICE", "priceProtect": false, "origType": "LIMIT", "priceMatch": "NONE", "selfTradePreventionMode": "NONE", "goodTillDate": 0, "updateTime": 1739781831932}, "id": "4087922044", "clientOrderId": "x-xcKtGhcu04075babaeb34c55af79ba", "timestamp": 1739781831932, "datetime": "2025-02-17T08:43:51.932Z", "lastTradeTimestamp": null, "lastUpdateTimestamp": 1739781831932, "symbol": "BTC/USDT:USDT", "type": "limit", "timeInForce": "GTC", "postOnly": false, "reduceOnly": false, "side": "sell", "price": 110001, "triggerPrice": null, "amount": 0.2, "cost": 0, "average": null, "filled": 0, "remaining": 0.2, "status": "open", "fee": null, "trades": [], "fees": [], "stopPrice": null, "takeProfitPrice": null, "stopLossPrice": null}]}], "fetchOrder": [{"description": "Fetch spot closed order", "method": "fetchOrder", "input": [3397148653, "LTC/USDT"], "httpResponse": {"symbol": "LTCUSDT", "orderId": "3397148653", "orderListId": "-1", "clientOrderId": "x-R4BD3S825806ed40c861260428a48c", "price": "0.********", "origQty": "0.10000000", "executedQty": "0.10000000", "cummulativeQuoteQty": "9.08000000", "status": "FILLED", "timeInForce": "GTC", "type": "MARKET", "side": "SELL", "stopPrice": "0.********", "icebergQty": "0.********", "time": "1679571174472", "updateTime": "1679571174472", "isWorking": true, "workingTime": "1679571174472", "origQuoteOrderQty": "0.********", "selfTradePreventionMode": "NONE"}, "parsedResponse": {"info": {"symbol": "LTCUSDT", "orderId": "3397148653", "orderListId": "-1", "clientOrderId": "x-R4BD3S825806ed40c861260428a48c", "price": "0.********", "origQty": "0.10000000", "executedQty": "0.10000000", "cummulativeQuoteQty": "9.08000000", "status": "FILLED", "timeInForce": "GTC", "type": "MARKET", "side": "SELL", "stopPrice": "0.********", "icebergQty": "0.********", "time": "1679571174472", "updateTime": "1679571174472", "isWorking": true, "workingTime": "1679571174472", "origQuoteOrderQty": "0.********", "selfTradePreventionMode": "NONE"}, "id": "3397148653", "clientOrderId": "x-R4BD3S825806ed40c861260428a48c", "timestamp": 1679571174472, "datetime": "2023-03-23T11:32:54.472Z", "lastTradeTimestamp": 1679571174472, "lastUpdateTimestamp": 1679571174472, "symbol": "LTC/USDT", "type": "market", "timeInForce": "GTC", "postOnly": false, "reduceOnly": null, "side": "sell", "price": 90.8, "triggerPrice": null, "amount": 0.1, "cost": 9.08, "average": 90.8, "filled": 0.1, "remaining": 0, "status": "closed", "fee": null, "trades": [], "fees": [], "stopPrice": null, "takeProfitPrice": null, "stopLossPrice": null}}], "fetchOpenOrders": [{"description": "Fetch spot open orders", "method": "fetchOpenOrders", "input": ["LTC/USDT", null, 1], "httpResponse": [{"symbol": "LTCUSDT", "orderId": "3811057334", "orderListId": "-1", "clientOrderId": "x-R4BD3S82102a551f8c1642bba06728", "price": "50.********", "origQty": "0.20000000", "executedQty": "0.********", "cummulativeQuoteQty": "0.********", "status": "NEW", "timeInForce": "GTC", "type": "LIMIT_MAKER", "side": "BUY", "stopPrice": "0.********", "icebergQty": "0.********", "time": "1699380855570", "updateTime": "1699380855570", "isWorking": true, "workingTime": "1699380855570", "origQuoteOrderQty": "0.********", "selfTradePreventionMode": "EXPIRE_MAKER"}], "parsedResponse": [{"info": {"symbol": "LTCUSDT", "orderId": "3811057334", "orderListId": "-1", "clientOrderId": "x-R4BD3S82102a551f8c1642bba06728", "price": "50.********", "origQty": "0.20000000", "executedQty": "0.********", "cummulativeQuoteQty": "0.********", "status": "NEW", "timeInForce": "GTC", "type": "LIMIT_MAKER", "side": "BUY", "stopPrice": "0.********", "icebergQty": "0.********", "time": "1699380855570", "updateTime": "1699380855570", "isWorking": true, "workingTime": "1699380855570", "origQuoteOrderQty": "0.********", "selfTradePreventionMode": "EXPIRE_MAKER"}, "id": "3811057334", "clientOrderId": "x-R4BD3S82102a551f8c1642bba06728", "timestamp": 1699380855570, "datetime": "2023-11-07T18:14:15.570Z", "lastTradeTimestamp": null, "lastUpdateTimestamp": 1699380855570, "symbol": "LTC/USDT", "type": "limit", "timeInForce": "GTC", "postOnly": true, "reduceOnly": null, "side": "buy", "price": 50, "triggerPrice": null, "amount": 0.2, "cost": 0, "average": null, "filled": 0, "remaining": 0.2, "status": "open", "fee": null, "trades": [], "fees": [], "stopPrice": null, "takeProfitPrice": null, "stopLossPrice": null}]}, {"description": "open portfolio margin orders", "method": "fetchOpenOrders", "input": ["LTC/USDT:USDT", null, 1, {"portfolioMargin": true}], "httpResponse": [{"orderId": "29067571132", "symbol": "LTCUSDT", "status": "NEW", "clientOrderId": "x-xcKtGhcud28547066cdd46a7b96e40", "price": "50", "avgPrice": "0", "origQty": "0.410", "executedQty": "0", "cumQuote": "0", "timeInForce": "GTC", "type": "LIMIT", "reduceOnly": false, "side": "BUY", "positionSide": "BOTH", "origType": "LIMIT", "time": "1707212775174", "updateTime": "1707212775174", "goodTillDate": "0", "selfTradePreventionMode": "NONE"}], "parsedResponse": [{"info": {"orderId": "29067571132", "symbol": "LTCUSDT", "status": "NEW", "clientOrderId": "x-xcKtGhcud28547066cdd46a7b96e40", "price": "50", "avgPrice": "0", "origQty": "0.410", "executedQty": "0", "cumQuote": "0", "timeInForce": "GTC", "type": "LIMIT", "reduceOnly": false, "side": "BUY", "positionSide": "BOTH", "origType": "LIMIT", "time": "1707212775174", "updateTime": "1707212775174", "goodTillDate": "0", "selfTradePreventionMode": "NONE"}, "id": "29067571132", "clientOrderId": "x-xcKtGhcud28547066cdd46a7b96e40", "timestamp": 1707212775174, "datetime": "2024-02-06T09:46:15.174Z", "lastTradeTimestamp": null, "lastUpdateTimestamp": 1707212775174, "symbol": "LTC/USDT:USDT", "type": "limit", "timeInForce": "GTC", "postOnly": false, "reduceOnly": false, "side": "buy", "price": 50, "triggerPrice": null, "amount": 0.41, "cost": 0, "average": null, "filled": 0, "remaining": 0.41, "status": "open", "fee": null, "trades": [], "fees": [], "stopPrice": null, "takeProfitPrice": null, "stopLossPrice": null}]}], "fetchOpenOrder": [{"description": "swap open order", "method": "fetchOpenOrder", "input": [664082074, "LTC/USDT:USDT"], "httpResponse": {"orderId": "664082074", "symbol": "LTCUSDT", "status": "NEW", "clientOrderId": "x-xcKtGhcu68f8383451844295b3e8b9", "price": "50.00", "avgPrice": "0.00000", "origQty": "0.200", "executedQty": "0.000", "cumQuote": "0.00000", "timeInForce": "GTX", "type": "LIMIT", "reduceOnly": false, "closePosition": false, "side": "BUY", "positionSide": "BOTH", "stopPrice": "0.00", "workingType": "CONTRACT_PRICE", "priceProtect": false, "origType": "LIMIT", "priceMatch": "NONE", "selfTradePreventionMode": "NONE", "goodTillDate": "0", "time": "1707907323983", "updateTime": "1707907323991"}, "parsedResponse": {"info": {"orderId": "664082074", "symbol": "LTCUSDT", "status": "NEW", "clientOrderId": "x-xcKtGhcu68f8383451844295b3e8b9", "price": "50.00", "avgPrice": "0.00000", "origQty": "0.200", "executedQty": "0.000", "cumQuote": "0.00000", "timeInForce": "GTX", "type": "LIMIT", "reduceOnly": false, "closePosition": false, "side": "BUY", "positionSide": "BOTH", "stopPrice": "0.00", "workingType": "CONTRACT_PRICE", "priceProtect": false, "origType": "LIMIT", "priceMatch": "NONE", "selfTradePreventionMode": "NONE", "goodTillDate": "0", "time": "1707907323983", "updateTime": "1707907323991"}, "id": "664082074", "clientOrderId": "x-xcKtGhcu68f8383451844295b3e8b9", "timestamp": 1707907323983, "datetime": "2024-02-14T10:42:03.983Z", "lastTradeTimestamp": null, "lastUpdateTimestamp": 1707907323991, "symbol": "LTC/USDT:USDT", "type": "limit", "timeInForce": "PO", "postOnly": true, "reduceOnly": false, "side": "buy", "price": 50, "triggerPrice": null, "amount": 0.2, "cost": 0, "average": null, "filled": 0, "remaining": 0.2, "status": "open", "fee": null, "trades": [], "fees": [], "stopPrice": null, "takeProfitPrice": null, "stopLossPrice": null}}], "fetchPositions": [{"description": "Fetch linear positions without symbols", "method": "fetchPositions", "input": [], "httpResponse": [{"symbol": "BTCUSDT", "positionSide": "BOTH", "positionAmt": "0.009", "entryPrice": "67445.9", "breakEvenPrice": "67479.62295", "markPrice": "67502.68519858", "unRealizedProfit": "0.51106678", "liquidationPrice": "64846.98753790", "isolatedMargin": "0", "notional": "607.52416678", "marginAsset": "USDT", "isolatedWallet": "0", "initialMargin": "7.59405208", "maintMargin": "2.43009666", "positionInitialMargin": "7.59405208", "openOrderInitialMargin": "0", "adl": "2", "bidNotional": "0", "askNotional": "0", "updateTime": "*************"}], "parsedResponse": [{"info": {"symbol": "BTCUSDT", "positionSide": "BOTH", "positionAmt": "0.009", "entryPrice": "67445.9", "breakEvenPrice": "67479.62295", "markPrice": "67502.68519858", "unRealizedProfit": "0.51106678", "liquidationPrice": "64846.98753790", "isolatedMargin": "0", "notional": "607.52416678", "marginAsset": "USDT", "isolatedWallet": "0", "initialMargin": "7.59405208", "maintMargin": "2.43009666", "positionInitialMargin": "7.59405208", "openOrderInitialMargin": "0", "adl": "2", "bidNotional": "0", "askNotional": "0", "updateTime": "*************"}, "id": null, "symbol": "BTC/USDT:USDT", "contracts": 0.009, "contractSize": 1, "unrealizedPnl": 0.51106678, "leverage": null, "liquidationPrice": 64846.9875379, "collateral": 25.72470371, "notional": 607.52416678, "markPrice": 67502.68519858, "entryPrice": 67445.9, "timestamp": *************, "initialMargin": 7.59405208, "initialMarginPercentage": 0.********, "maintenanceMargin": 2.43009666712, "maintenanceMarginPercentage": 0.004, "marginRatio": 0.0945, "datetime": "2024-07-28T10:06:06.529Z", "marginMode": "cross", "marginType": "cross", "side": "long", "hedged": false, "percentage": 6.72, "stopLossPrice": null, "takeProfitPrice": null}]}, {"description": "Fetch linear positions with symbols", "method": "fetchPositions", "input": [["BTC/USDT:USDT"]], "httpResponse": [{"symbol": "BTCUSDT", "positionSide": "BOTH", "positionAmt": "0.009", "entryPrice": "67445.9", "breakEvenPrice": "67479.62295", "markPrice": "67582.20000000", "unRealizedProfit": "1.22670000", "liquidationPrice": "64846.78939917", "isolatedMargin": "0", "notional": "608.23980000", "marginAsset": "USDT", "isolatedWallet": "0", "initialMargin": "7.********", "maintMargin": "2.43295920", "positionInitialMargin": "7.********", "openOrderInitialMargin": "0", "adl": "2", "bidNotional": "0", "askNotional": "0", "updateTime": "*************"}], "parsedResponse": [{"info": {"symbol": "BTCUSDT", "positionSide": "BOTH", "positionAmt": "0.009", "entryPrice": "67445.9", "breakEvenPrice": "67479.62295", "markPrice": "67582.20000000", "unRealizedProfit": "1.22670000", "liquidationPrice": "64846.78939917", "isolatedMargin": "0", "notional": "608.23980000", "marginAsset": "USDT", "isolatedWallet": "0", "initialMargin": "7.********", "maintMargin": "2.43295920", "positionInitialMargin": "7.********", "openOrderInitialMargin": "0", "adl": "2", "bidNotional": "0", "askNotional": "0", "updateTime": "*************"}, "id": null, "symbol": "BTC/USDT:USDT", "contracts": 0.009, "contractSize": 1, "unrealizedPnl": 1.2267, "leverage": null, "liquidationPrice": 64846.78939917, "collateral": 25.********, "notional": 608.2398, "markPrice": 67582.2, "entryPrice": 67445.9, "timestamp": *************, "initialMargin": 7.********, "initialMarginPercentage": 0.********, "maintenanceMargin": 2.4329592, "maintenanceMarginPercentage": 0.004, "marginRatio": 0.0946, "datetime": "2024-07-28T10:06:06.529Z", "marginMode": "cross", "marginType": "cross", "side": "long", "hedged": false, "percentage": 16.13, "stopLossPrice": null, "takeProfitPrice": null}]}, {"description": "Fetch linear positions using account endnpoint", "method": "fetchPositions", "input": [["BTC/USDT:USDT"], {"method": "account"}], "httpResponse": {"totalInitialMargin": "7.********", "totalMaintMargin": "2.********", "totalWalletBalance": "25.********", "totalUnrealizedProfit": "0.********", "totalMarginBalance": "26.********", "totalPositionInitialMargin": "7.********", "totalOpenOrderInitialMargin": "0.********", "totalCrossWalletBalance": "25.********", "totalCrossUnPnl": "0.********", "availableBalance": "18.********", "maxWithdrawAmount": "18.********", "assets": [{"asset": "FDUSD", "walletBalance": "0.********", "unrealizedProfit": "0.********", "marginBalance": "0.********", "maintMargin": "0.********", "initialMargin": "0.********", "positionInitialMargin": "0.********", "openOrderInitialMargin": "0.********", "crossWalletBalance": "0.********", "crossUnPnl": "0.********", "availableBalance": "18.********", "maxWithdrawAmount": "0.********", "updateTime": "0"}, {"asset": "BTC", "walletBalance": "0.********", "unrealizedProfit": "0.********", "marginBalance": "0.********", "maintMargin": "0.********", "initialMargin": "0.********", "positionInitialMargin": "0.********", "openOrderInitialMargin": "0.********", "crossWalletBalance": "0.********", "crossUnPnl": "0.********", "availableBalance": "0.00026317", "maxWithdrawAmount": "0.********", "updateTime": "0"}, {"asset": "BNB", "walletBalance": "0.********", "unrealizedProfit": "0.********", "marginBalance": "0.********", "maintMargin": "0.********", "initialMargin": "0.********", "positionInitialMargin": "0.********", "openOrderInitialMargin": "0.********", "crossWalletBalance": "0.********", "crossUnPnl": "0.********", "availableBalance": "0.03068085", "maxWithdrawAmount": "0.********", "updateTime": "1722095514499"}, {"asset": "ETH", "walletBalance": "0.********", "unrealizedProfit": "0.********", "marginBalance": "0.********", "maintMargin": "0.********", "initialMargin": "0.********", "positionInitialMargin": "0.********", "openOrderInitialMargin": "0.********", "crossWalletBalance": "0.********", "crossUnPnl": "0.********", "availableBalance": "0.00548108", "maxWithdrawAmount": "0.********", "updateTime": "0"}, {"asset": "USDT", "walletBalance": "3.72964781", "unrealizedProfit": "0.54238506", "marginBalance": "4.27203287", "maintMargin": "2.43022194", "initialMargin": "7.59444356", "positionInitialMargin": "7.59444356", "openOrderInitialMargin": "0.********", "crossWalletBalance": "3.72964781", "crossUnPnl": "0.54238506", "availableBalance": "18.65856952", "maxWithdrawAmount": "3.72964781", "updateTime": "*************"}, {"asset": "USDC", "walletBalance": "22.********", "unrealizedProfit": "0.********", "marginBalance": "22.********", "maintMargin": "0.********", "initialMargin": "0.********", "positionInitialMargin": "0.********", "openOrderInitialMargin": "0.********", "crossWalletBalance": "22.********", "crossUnPnl": "0.********", "availableBalance": "18.65830123", "maxWithdrawAmount": "18.65830123", "updateTime": "1721991855073"}], "positions": [{"symbol": "BTCUSDT", "positionSide": "BOTH", "positionAmt": "0.009", "unrealizedProfit": "0.54238506", "isolatedMargin": "0", "notional": "607.55548506", "isolatedWallet": "0", "initialMargin": "7.59444356", "maintMargin": "2.43022194", "updateTime": "*************"}]}, "parsedResponse": [{"info": {"symbol": "BTCUSDT", "positionSide": "BOTH", "positionAmt": "0.009", "unrealizedProfit": "0.54238506", "isolatedMargin": "0", "notional": "607.55548506", "isolatedWallet": "0", "initialMargin": "7.59444356", "maintMargin": "2.43022194", "updateTime": "*************", "crossMargin": "4.27203287", "crossWalletBalance": "3.72964781"}, "id": null, "symbol": "BTC/USDT:USDT", "timestamp": *************, "datetime": "2024-07-28T10:06:06.529Z", "initialMargin": 7.59444356, "initialMarginPercentage": null, "maintenanceMargin": 2.43022194, "maintenanceMarginPercentage": 0.004, "entryPrice": null, "notional": 607.55548506, "leverage": null, "unrealizedPnl": 0.54238506, "contracts": 0.009, "contractSize": 1, "marginRatio": 0.5689, "liquidationPrice": null, "markPrice": null, "collateral": 4.27203287, "marginMode": "cross", "side": "long", "hedged": false, "percentage": 7.14}]}, {"description": "inverse positions", "method": "fetchPositions", "input": [null, {"subType": "inverse"}], "httpResponse": [{"symbol": "BTCUSD_PERP", "positionAmt": "-3", "entryPrice": "68911.00000037", "markPrice": "68234.20000000", "unRealizedProfit": "0.00004318", "liquidationPrice": "72491.16879818", "leverage": "20", "maxQty": "150", "marginType": "cross", "isolatedMargin": "0.********", "isAutoAddMargin": "false", "positionSide": "BOTH", "notionalValue": "-0.00439662", "isolatedWallet": "0", "updateTime": "1722105622903", "breakEvenPrice": "68876.54450033"}, {"symbol": "BCHUSD_241227", "positionAmt": "0", "entryPrice": "0.********", "markPrice": "0.********", "unRealizedProfit": "0.********", "liquidationPrice": "0", "leverage": "20", "maxQty": "500", "marginType": "cross", "isolatedMargin": "0.********", "isAutoAddMargin": "false", "positionSide": "BOTH", "notionalValue": "0", "isolatedWallet": "0", "updateTime": "0", "breakEvenPrice": "0.********"}], "parsedResponse": [{"info": {"symbol": "BTCUSD_PERP", "positionAmt": "-3", "entryPrice": "68911.00000037", "markPrice": "68234.20000000", "unRealizedProfit": "0.00004318", "liquidationPrice": "72491.16879818", "leverage": "20", "maxQty": "150", "marginType": "cross", "isolatedMargin": "0.********", "isAutoAddMargin": "false", "positionSide": "BOTH", "notionalValue": "-0.00439662", "isolatedWallet": "0", "updateTime": "1722105622903", "breakEvenPrice": "68876.54450033"}, "id": null, "symbol": "BTC/USD:BTC", "contracts": 3, "contractSize": 100, "unrealizedPnl": 4.318e-05, "leverage": 20, "liquidationPrice": 72491.16879818, "collateral": 0.00023156, "notional": 0.00439662, "markPrice": 68234.2, "entryPrice": 68911.00000037, "timestamp": 1722105622903, "initialMargin": 0.00021983, "initialMarginPercentage": 0.05, "maintenanceMargin": 1.758648e-05, "maintenanceMarginPercentage": 0.004, "marginRatio": 0.0759, "datetime": "2024-07-27T18:40:22.903Z", "marginMode": "cross", "marginType": "cross", "side": "short", "hedged": false, "percentage": 19.64, "stopLossPrice": null, "takeProfitPrice": null}]}], "fetchMyTrades": [{"description": "Spot private trade", "method": "fetchMyTrades", "input": ["LTC/USDT", null, 1], "httpResponse": [{"symbol": "LTCUSDT", "id": "309520010", "orderId": "3784695242", "orderListId": "-1", "price": "69.63000000", "qty": "0.20000000", "quoteQty": "13.92600000", "commission": "0.01392600", "commissionAsset": "USDT", "time": "1698138992358", "isBuyer": false, "isMaker": false, "isBestMatch": true}], "parsedResponse": [{"info": {"symbol": "LTCUSDT", "id": "309520010", "orderId": "3784695242", "orderListId": "-1", "price": "69.63000000", "qty": "0.20000000", "quoteQty": "13.92600000", "commission": "0.01392600", "commissionAsset": "USDT", "time": "1698138992358", "isBuyer": false, "isMaker": false, "isBestMatch": true}, "timestamp": 1698138992358, "datetime": "2023-10-24T09:16:32.358Z", "symbol": "LTC/USDT", "id": "309520010", "order": "3784695242", "type": null, "side": "sell", "takerOrMaker": "taker", "price": 69.63, "amount": 0.2, "cost": 13.926, "fee": {"cost": 0.013926, "currency": "USDT"}, "fees": [{"cost": 0.013926, "currency": "USDT"}]}]}, {"description": "Swap private trade", "method": "fetchMyTrades", "input": ["BTC/USDT:USDT", null, 1], "httpResponse": [{"symbol": "BTCUSDT", "id": "272536245", "orderId": "3524852987", "side": "BUY", "price": "36567.10", "qty": "0.010", "realizedPnl": "0", "marginAsset": "USDT", "quoteQty": "365.67100", "commission": "0.14626840", "commissionAsset": "USDT", "time": "1699972836419", "positionSide": "BOTH", "maker": false, "buyer": true}], "parsedResponse": [{"info": {"symbol": "BTCUSDT", "id": "272536245", "orderId": "3524852987", "side": "BUY", "price": "36567.10", "qty": "0.010", "realizedPnl": "0", "marginAsset": "USDT", "quoteQty": "365.67100", "commission": "0.14626840", "commissionAsset": "USDT", "time": "1699972836419", "positionSide": "BOTH", "maker": false, "buyer": true}, "timestamp": 1699972836419, "datetime": "2023-11-14T14:40:36.419Z", "symbol": "BTC/USDT:USDT", "id": "272536245", "order": "3524852987", "type": null, "side": "buy", "takerOrMaker": "taker", "price": 36567.1, "amount": 0.01, "cost": 365.671, "fee": {"cost": 0.1462684, "currency": "USDT"}, "fees": [{"cost": 0.1462684, "currency": "USDT"}]}]}, {"description": "spot porfolioMargin private trade", "method": "fetchMyTrades", "input": ["LTC/USDT", null, 1, {"portfolioMargin": true}], "httpResponse": [{"symbol": "LTCUSDT", "id": "320530367", "orderId": "3949667670", "price": "69.39000000", "qty": "0.17300000", "quoteQty": "12.00447000", "commission": "0.01200447", "commissionAsset": "USDT", "time": "1706694267729", "isBuyer": false, "isMaker": false, "isBestMatch": true}], "parsedResponse": [{"info": {"symbol": "LTCUSDT", "id": "320530367", "orderId": "3949667670", "price": "69.39000000", "qty": "0.17300000", "quoteQty": "12.00447000", "commission": "0.01200447", "commissionAsset": "USDT", "time": "1706694267729", "isBuyer": false, "isMaker": false, "isBestMatch": true}, "timestamp": 1706694267729, "datetime": "2024-01-31T09:44:27.729Z", "symbol": "LTC/USDT", "id": "320530367", "order": "3949667670", "type": null, "side": "sell", "takerOrMaker": "taker", "price": 69.39, "amount": 0.173, "cost": 12.00447, "fee": {"cost": 0.01200447, "currency": "USDT"}, "fees": [{"cost": 0.01200447, "currency": "USDT"}]}]}], "fetchBalance": [{"description": "Spot balance", "method": "fetchBalance", "input": [], "httpResponse": {"makerCommission": "10", "takerCommission": "10", "buyerCommission": "0", "sellerCommission": "0", "commissionRates": {"maker": "0.********", "taker": "0.********", "buyer": "0.********", "seller": "0.********"}, "canTrade": true, "canWithdraw": true, "canDeposit": true, "brokered": false, "requireSelfTradePrevention": false, "preventSor": false, "updateTime": "*************", "accountType": "SPOT", "balances": [{"asset": "BTC", "free": "0.********", "locked": "0.********"}, {"asset": "LTC", "free": "0.********", "locked": "0.********"}, {"asset": "ETH", "free": "0.********", "locked": "0.********"}], "permissions": ["SPOT"], "uid": "*********"}, "parsedResponse": {"info": {"makerCommission": "10", "takerCommission": "10", "buyerCommission": "0", "sellerCommission": "0", "commissionRates": {"maker": "0.********", "taker": "0.********", "buyer": "0.********", "seller": "0.********"}, "canTrade": true, "canWithdraw": true, "canDeposit": true, "brokered": false, "requireSelfTradePrevention": false, "preventSor": false, "updateTime": "*************", "accountType": "SPOT", "balances": [{"asset": "BTC", "free": "0.********", "locked": "0.********"}, {"asset": "LTC", "free": "0.********", "locked": "0.********"}, {"asset": "ETH", "free": "0.********", "locked": "0.********"}], "permissions": ["SPOT"], "uid": "*********"}, "BTC": {"free": 0, "used": 0, "total": 0}, "LTC": {"free": 0.919741, "used": 0, "total": 0.919741}, "ETH": {"free": 0.********, "used": 0, "total": 0.********}, "timestamp": *************, "datetime": "2023-12-06T09:53:15.927Z", "free": {"BTC": 0, "LTC": 0.919741, "ETH": 0.********}, "used": {"BTC": 0, "LTC": 0, "ETH": 0}, "total": {"BTC": 0, "LTC": 0.919741, "ETH": 0.********}}}, {"description": "Linear swap balance", "method": "fetchBalance", "input": [{"type": "swap"}], "httpResponse": {"feeTier": "0", "canTrade": true, "canDeposit": true, "canWithdraw": true, "tradeGroupId": "-1", "updateTime": "0", "multiAssetsMargin": false, "totalInitialMargin": "0.********", "totalMaintMargin": "0.********", "totalWalletBalance": "31.02261200", "totalUnrealizedProfit": "0.********", "totalMarginBalance": "31.02261200", "totalPositionInitialMargin": "0.********", "totalOpenOrderInitialMargin": "0.********", "totalCrossWalletBalance": "31.02261200", "totalCrossUnPnl": "0.********", "availableBalance": "31.02261200", "maxWithdrawAmount": "31.02261200", "assets": [{"asset": "USDT", "walletBalance": "31.02261200", "unrealizedProfit": "0.********", "marginBalance": "31.02261200", "maintMargin": "0.********", "initialMargin": "0.********", "positionInitialMargin": "0.********", "openOrderInitialMargin": "0.********", "maxWithdrawAmount": "31.02261200", "crossWalletBalance": "31.02261200", "crossUnPnl": "0.********", "availableBalance": "31.02261200", "marginAvailable": true, "updateTime": "1701270042059"}], "positions": []}, "parsedResponse": {"info": {"feeTier": "0", "canTrade": true, "canDeposit": true, "canWithdraw": true, "tradeGroupId": "-1", "updateTime": "0", "multiAssetsMargin": false, "totalInitialMargin": "0.********", "totalMaintMargin": "0.********", "totalWalletBalance": "31.02261200", "totalUnrealizedProfit": "0.********", "totalMarginBalance": "31.02261200", "totalPositionInitialMargin": "0.********", "totalOpenOrderInitialMargin": "0.********", "totalCrossWalletBalance": "31.02261200", "totalCrossUnPnl": "0.********", "availableBalance": "31.02261200", "maxWithdrawAmount": "31.02261200", "assets": [{"asset": "USDT", "walletBalance": "31.02261200", "unrealizedProfit": "0.********", "marginBalance": "31.02261200", "maintMargin": "0.********", "initialMargin": "0.********", "positionInitialMargin": "0.********", "openOrderInitialMargin": "0.********", "maxWithdrawAmount": "31.02261200", "crossWalletBalance": "31.02261200", "crossUnPnl": "0.********", "availableBalance": "31.02261200", "marginAvailable": true, "updateTime": "1701270042059"}], "positions": []}, "USDT": {"free": 31.022612, "used": 0, "total": 31.022612}, "timestamp": null, "datetime": null, "free": {"USDT": 31.022612}, "used": {"USDT": 0}, "total": {"USDT": 31.022612}}}, {"description": "fetch papi balance", "method": "fetchBalance", "input": [{"type": "papi"}], "httpResponse": [{"asset": "ETH", "totalWalletBalance": "0.00057786", "crossMarginAsset": "0.0", "crossMarginBorrowed": "0.0", "crossMarginFree": "0.0", "crossMarginInterest": "0.0", "crossMarginLocked": "0.0", "umWalletBalance": "0.0", "umUnrealizedPNL": "0.0", "cmWalletBalance": "0.00057786", "cmUnrealizedPNL": "0.0", "updateTime": "1708067615305", "negativeBalance": "0.0"}, {"asset": "USDT", "totalWalletBalance": "136.03451668", "crossMarginAsset": "30.6003261", "crossMarginBorrowed": "0.00011146", "crossMarginFree": "30.6003261", "crossMarginInterest": "0.00007902", "crossMarginLocked": "0.0", "umWalletBalance": "105.43419058", "umUnrealizedPNL": "0.0", "cmWalletBalance": "0.0", "cmUnrealizedPNL": "0.0", "updateTime": "1708064880778", "negativeBalance": "0.0"}, {"asset": "LTC", "totalWalletBalance": "0.000636", "crossMarginAsset": "0.000636", "crossMarginBorrowed": "0.0", "crossMarginFree": "0.000636", "crossMarginInterest": "0.0", "crossMarginLocked": "0.0", "umWalletBalance": "0.0", "umUnrealizedPNL": "0.0", "cmWalletBalance": "0.0", "cmUnrealizedPNL": "0.0", "updateTime": "0", "negativeBalance": "0.0"}, {"asset": "USDC", "totalWalletBalance": "0.0", "crossMarginAsset": "0.0", "crossMarginBorrowed": "0.00014784", "crossMarginFree": "0.0", "crossMarginInterest": "0.00002402", "crossMarginLocked": "0.0", "umWalletBalance": "0.0", "umUnrealizedPNL": "0.0", "cmWalletBalance": "0.0", "cmUnrealizedPNL": "0.0", "updateTime": "0", "negativeBalance": "0.0"}, {"asset": "ADA", "totalWalletBalance": "10.0325", "crossMarginAsset": "10.0325", "crossMarginBorrowed": "0.0", "crossMarginFree": "10.0325", "crossMarginInterest": "0.0", "crossMarginLocked": "0.0", "umWalletBalance": "0.0", "umUnrealizedPNL": "0.0", "cmWalletBalance": "0.0", "cmUnrealizedPNL": "0.0", "updateTime": "0", "negativeBalance": "0.0"}], "parsedResponse": {"info": [{"asset": "ETH", "totalWalletBalance": "0.00057786", "crossMarginAsset": "0.0", "crossMarginBorrowed": "0.0", "crossMarginFree": "0.0", "crossMarginInterest": "0.0", "crossMarginLocked": "0.0", "umWalletBalance": "0.0", "umUnrealizedPNL": "0.0", "cmWalletBalance": "0.00057786", "cmUnrealizedPNL": "0.0", "updateTime": "1708067615305", "negativeBalance": "0.0"}, {"asset": "USDT", "totalWalletBalance": "136.03451668", "crossMarginAsset": "30.6003261", "crossMarginBorrowed": "0.00011146", "crossMarginFree": "30.6003261", "crossMarginInterest": "0.00007902", "crossMarginLocked": "0.0", "umWalletBalance": "105.43419058", "umUnrealizedPNL": "0.0", "cmWalletBalance": "0.0", "cmUnrealizedPNL": "0.0", "updateTime": "1708064880778", "negativeBalance": "0.0"}, {"asset": "LTC", "totalWalletBalance": "0.000636", "crossMarginAsset": "0.000636", "crossMarginBorrowed": "0.0", "crossMarginFree": "0.000636", "crossMarginInterest": "0.0", "crossMarginLocked": "0.0", "umWalletBalance": "0.0", "umUnrealizedPNL": "0.0", "cmWalletBalance": "0.0", "cmUnrealizedPNL": "0.0", "updateTime": "0", "negativeBalance": "0.0"}, {"asset": "USDC", "totalWalletBalance": "0.0", "crossMarginAsset": "0.0", "crossMarginBorrowed": "0.00014784", "crossMarginFree": "0.0", "crossMarginInterest": "0.00002402", "crossMarginLocked": "0.0", "umWalletBalance": "0.0", "umUnrealizedPNL": "0.0", "cmWalletBalance": "0.0", "cmUnrealizedPNL": "0.0", "updateTime": "0", "negativeBalance": "0.0"}, {"asset": "ADA", "totalWalletBalance": "10.0325", "crossMarginAsset": "10.0325", "crossMarginBorrowed": "0.0", "crossMarginFree": "10.0325", "crossMarginInterest": "0.0", "crossMarginLocked": "0.0", "umWalletBalance": "0.0", "umUnrealizedPNL": "0.0", "cmWalletBalance": "0.0", "cmUnrealizedPNL": "0.0", "updateTime": "0", "negativeBalance": "0.0"}], "ETH": {"free": null, "used": null, "total": 0.00057786}, "USDT": {"free": null, "used": null, "total": 136.03451668}, "LTC": {"free": null, "used": null, "total": 0.000636}, "USDC": {"free": null, "used": null, "total": 0}, "ADA": {"free": null, "used": null, "total": 10.0325}, "timestamp": null, "datetime": null, "free": {"ETH": null, "USDT": null, "LTC": null, "USDC": null, "ADA": null}, "used": {"ETH": null, "USDT": null, "LTC": null, "USDC": null, "ADA": null}, "total": {"ETH": 0.00057786, "USDT": 136.03451668, "LTC": 0.000636, "USDC": 0, "ADA": 10.0325}}}, {"description": "fetch linear papi balance", "method": "fetchBalance", "input": [{"type": "papi", "subType": "linear"}], "httpResponse": [{"asset": "ETH", "totalWalletBalance": "0.00057786", "crossMarginAsset": "0.0", "crossMarginBorrowed": "0.0", "crossMarginFree": "0.0", "crossMarginInterest": "0.0", "crossMarginLocked": "0.0", "umWalletBalance": "0.0", "umUnrealizedPNL": "0.0", "cmWalletBalance": "0.00057786", "cmUnrealizedPNL": "0.0", "updateTime": "1708067615305", "negativeBalance": "0.0"}, {"asset": "USDT", "totalWalletBalance": "136.03451668", "crossMarginAsset": "30.6003261", "crossMarginBorrowed": "0.00011146", "crossMarginFree": "30.6003261", "crossMarginInterest": "0.00000197", "crossMarginLocked": "0.0", "umWalletBalance": "105.43419058", "umUnrealizedPNL": "0.0", "cmWalletBalance": "0.0", "cmUnrealizedPNL": "0.0", "updateTime": "1708064880778", "negativeBalance": "0.0"}, {"asset": "LTC", "totalWalletBalance": "0.000636", "crossMarginAsset": "0.000636", "crossMarginBorrowed": "0.0", "crossMarginFree": "0.000636", "crossMarginInterest": "0.0", "crossMarginLocked": "0.0", "umWalletBalance": "0.0", "umUnrealizedPNL": "0.0", "cmWalletBalance": "0.0", "cmUnrealizedPNL": "0.0", "updateTime": "1707550484595", "negativeBalance": "0.0"}, {"asset": "ADA", "totalWalletBalance": "10.0325", "crossMarginAsset": "10.0325", "crossMarginBorrowed": "0.0", "crossMarginFree": "10.0325", "crossMarginInterest": "0.0", "crossMarginLocked": "0.0", "umWalletBalance": "0.0", "umUnrealizedPNL": "0.0", "cmWalletBalance": "0.0", "cmUnrealizedPNL": "0.0", "updateTime": "1707550484595", "negativeBalance": "0.0"}], "parsedResponse": {"info": [{"asset": "ETH", "totalWalletBalance": "0.00057786", "crossMarginAsset": "0.0", "crossMarginBorrowed": "0.0", "crossMarginFree": "0.0", "crossMarginInterest": "0.0", "crossMarginLocked": "0.0", "umWalletBalance": "0.0", "umUnrealizedPNL": "0.0", "cmWalletBalance": "0.00057786", "cmUnrealizedPNL": "0.0", "updateTime": "1708067615305", "negativeBalance": "0.0"}, {"asset": "USDT", "totalWalletBalance": "136.03451668", "crossMarginAsset": "30.6003261", "crossMarginBorrowed": "0.00011146", "crossMarginFree": "30.6003261", "crossMarginInterest": "0.00000197", "crossMarginLocked": "0.0", "umWalletBalance": "105.43419058", "umUnrealizedPNL": "0.0", "cmWalletBalance": "0.0", "cmUnrealizedPNL": "0.0", "updateTime": "1708064880778", "negativeBalance": "0.0"}, {"asset": "LTC", "totalWalletBalance": "0.000636", "crossMarginAsset": "0.000636", "crossMarginBorrowed": "0.0", "crossMarginFree": "0.000636", "crossMarginInterest": "0.0", "crossMarginLocked": "0.0", "umWalletBalance": "0.0", "umUnrealizedPNL": "0.0", "cmWalletBalance": "0.0", "cmUnrealizedPNL": "0.0", "updateTime": "1707550484595", "negativeBalance": "0.0"}, {"asset": "ADA", "totalWalletBalance": "10.0325", "crossMarginAsset": "10.0325", "crossMarginBorrowed": "0.0", "crossMarginFree": "10.0325", "crossMarginInterest": "0.0", "crossMarginLocked": "0.0", "umWalletBalance": "0.0", "umUnrealizedPNL": "0.0", "cmWalletBalance": "0.0", "cmUnrealizedPNL": "0.0", "updateTime": "1707550484595", "negativeBalance": "0.0"}], "ETH": {"free": 0, "used": 0, "total": 0}, "USDT": {"free": 105.43419058, "used": 0, "total": 105.43419058}, "LTC": {"free": 0, "used": 0, "total": 0}, "ADA": {"free": 0, "used": 0, "total": 0}, "timestamp": null, "datetime": null, "free": {"ETH": 0, "USDT": 105.43419058, "LTC": 0, "ADA": 0}, "used": {"ETH": 0, "USDT": 0, "LTC": 0, "ADA": 0}, "total": {"ETH": 0, "USDT": 105.43419058, "LTC": 0, "ADA": 0}}}], "fetchOHLCV": [{"description": "Spot OHLCV with since and limit", "method": "fetchOHLCV", "input": ["BTC/USDT", "1h", 1706628191000, 3], "httpResponse": [[1706630400000, "43519.34000000", "43936.********", "43171.********", "43488.71000000", "20.55495000", 1706633999999, "891428.42858070", 3743, "10.91428000", "473782.95739920", "0"], [1706634000000, "43488.71000000", "43832.08000000", "43084.********", "43374.80000000", "21.80865000", 1706637599999, "946546.04941680", 4365, "11.70274000", "508392.74777040", "0"], [1706637600000, "43374.81000000", "44048.********", "43062.********", "43361.11000000", "23.66547000", 1706641199999, "1027527.77116170", 4063, "15.19718000", "660419.73275280", "0"]], "parsedResponse": [[1706630400000, 43519.34, 43936, 43171, 43488.71, 20.55495], [1706634000000, 43488.71, 43832.08, 43084, 43374.8, 21.80865], [1706637600000, 43374.81, 44048, 43062, 43361.11, 23.66547]]}, {"description": "inverse index price", "method": "fetchOHLCV", "input": ["BTC/USD:BTC", "1d", null, 3, {"price": "index"}], "httpResponse": [[1712534400000, "69362.49174406", "72740.01330078", "69076.81471239", "71625.41841177", "0", 1712620799999, "0", 86400, "0", "0", "0"], [1712620800000, "71628.33407436", "71739.77815692", "68230.05238367", "69135.87773144", "0", 1712707199999, "0", 86400, "0", "0", "0"], [1712707200000, "69136.23648785", "69565.97257021", "68482.85614772", "68921.79749600", "0", 1712793599999, "0", 31378, "0", "0", "0"]], "parsedResponse": [[1712534400000, 69362.49174406, 72740.01330078, 69076.81471239, 71625.41841177, 0], [1712620800000, 71628.33407436, 71739.77815692, 68230.05238367, 69135.87773144, 0], [1712707200000, 69136.23648785, 69565.97257021, 68482.85614772, 68921.797496, 0]]}, {"description": "swap ohlcv", "method": "fetchOHLCV", "input": ["BTC/USDT:USDT", "1m", null, 3], "httpResponse": [[1712738460000, "71481.90", "71481.90", "71481.90", "71481.90", "0.002", 1712738519999, "142.96380", 1, "0.002", "142.96380", "0"], [1712738520000, "71471.00", "71471.00", "70459.40", "70459.40", "0.120", 1712738579999, "8518.17600", 9, "0.061", "4359.72600", "0"], [1712738580000, "71470.70", "71470.90", "70459.40", "70459.40", "1.542", 1712738639999, "108661.55840", 8, "0.013", "929.12130", "0"]], "parsedResponse": [[1712738460000, 71481.9, 71481.9, 71481.9, 71481.9, 0.002], [1712738520000, 71471, 71471, 70459.4, 70459.4, 0.12], [1712738580000, 71470.7, 71470.9, 70459.4, 70459.4, 1.542]]}], "fetchOrderBook": [{"description": "spot orderbook", "method": "fetchOrderBook", "input": ["BTC/USDT", 2], "httpResponse": {"lastUpdateId": "25445101", "bids": [["42574.30000000", "0.01070000"], ["42574.20000000", "0.00646000"]], "asks": [["42574.31000000", "0.00870000"], ["42574.80000000", "0.00893000"]]}, "parsedResponse": {"symbol": "BTC/USDT", "bids": [[42574.3, 0.0107], [42574.2, 0.00646]], "asks": [[42574.31, 0.0087], [42574.8, 0.00893]], "timestamp": null, "datetime": null, "nonce": 25445101}}, {"description": "swap orderbook", "method": "fetchOrderBook", "input": ["BTC/USDT:USDT", 5], "httpResponse": {"lastUpdateId": "35263532345", "E": "1706803917264", "T": "1706803917258", "bids": [["42651.10", "470.672"], ["42650.00", "66.452"], ["42646.80", "126.261"], ["42616.10", "1.394"], ["42609.80", "93.162"]], "asks": [["42878.00", "7.011"], ["42950.90", "0.005"], ["42980.90", "0.061"], ["42981.80", "23.165"], ["42998.10", "0.071"]]}, "parsedResponse": {"symbol": "BTC/USDT:USDT", "bids": [[42651.1, 470.672], [42650, 66.452], [42646.8, 126.261], [42616.1, 1.394], [42609.8, 93.162]], "asks": [[42878, 7.011], [42950.9, 0.005], [42980.9, 0.061], [42981.8, 23.165], [42998.1, 0.071]], "timestamp": 1706803917258, "datetime": "2024-02-01T16:11:57.258Z", "nonce": 35263532345}}], "fetchFundingHistory": [{"description": "fetch portfolio margin funding history", "method": "fetchFundingHistory", "input": ["BTC/USDT:USDT", null, 1, {"portfolioMargin": true}], "httpResponse": [{"symbol": "BTCUSDT", "incomeType": "FUNDING_FEE", "income": "-0.04618913", "asset": "USDT", "time": "1707465600000", "info": "FUNDING_FEE", "tranId": "8082457372019390060", "tradeId": ""}], "parsedResponse": [{"info": {"symbol": "BTCUSDT", "incomeType": "FUNDING_FEE", "income": "-0.04618913", "asset": "USDT", "time": "1707465600000", "info": "FUNDING_FEE", "tranId": "8082457372019390060", "tradeId": ""}, "symbol": "BTC/USDT:USDT", "code": "USDT", "timestamp": 1707465600000, "datetime": "2024-02-09T08:00:00.000Z", "id": "8082457372019390060", "amount": -0.04618913}]}], "fetchBorrowInterest": [{"description": "borrow interest porftolio margin", "method": "fetchBorrowInterest", "input": ["USDT", null, null, 1, {"portfolioMargin": true}], "httpResponse": {"total": "55", "rows": [{"txId": "1656373250307228092", "interestAccuredTime": "1707562800000", "asset": "USDT", "rawAsset": "USDT", "principal": "0.00011146", "interest": "0.000000010", "interestRate": "0.00089489", "type": "PERIODIC"}]}, "parsedResponse": [{"info": {"txId": "1656373250307228092", "interestAccuredTime": "1707562800000", "asset": "USDT", "rawAsset": "USDT", "principal": "0.00011146", "interest": "0.000000010", "interestRate": "0.00089489", "type": "PERIODIC"}, "symbol": null, "currency": "USDT", "interest": 1e-08, "interestRate": 0.00089489, "amountBorrowed": 0.00011146, "marginMode": "cross", "timestamp": 1707562800000, "datetime": "2024-02-10T11:00:00.000Z"}]}], "fetchOrders": [{"description": "fetch portfolio margin orders", "method": "fetchOrders", "input": ["BTC/USDT:USDT", null, 1, {"portfolioMargin": true}], "httpResponse": [{"orderId": "262065925771", "symbol": "BTCUSDT", "status": "FILLED", "clientOrderId": "x-xcKtGhcud2deca88ac9f4ccb9485a9", "price": "0", "avgPrice": "47279.40000", "origQty": "0.010", "executedQty": "0.010", "cumQuote": "472.79400", "timeInForce": "GTC", "type": "MARKET", "reduceOnly": false, "side": "BUY", "origType": "MARKET", "time": "1707550415587", "updateTime": "1707550415587", "positionSide": "LONG", "selfTradePreventionMode": "NONE", "goodTillDate": "0"}], "parsedResponse": [{"info": {"orderId": "262065925771", "symbol": "BTCUSDT", "status": "FILLED", "clientOrderId": "x-xcKtGhcud2deca88ac9f4ccb9485a9", "price": "0", "avgPrice": "47279.40000", "origQty": "0.010", "executedQty": "0.010", "cumQuote": "472.79400", "timeInForce": "GTC", "type": "MARKET", "reduceOnly": false, "side": "BUY", "origType": "MARKET", "time": "1707550415587", "updateTime": "1707550415587", "positionSide": "LONG", "selfTradePreventionMode": "NONE", "goodTillDate": "0"}, "id": "262065925771", "clientOrderId": "x-xcKtGhcud2deca88ac9f4ccb9485a9", "timestamp": 1707550415587, "datetime": "2024-02-10T07:33:35.587Z", "lastTradeTimestamp": 1707550415587, "lastUpdateTimestamp": 1707550415587, "symbol": "BTC/USDT:USDT", "type": "market", "timeInForce": "GTC", "postOnly": false, "reduceOnly": false, "side": "buy", "price": 47279.4, "triggerPrice": null, "amount": 0.01, "cost": 472.794, "average": 47279.4, "filled": 0.01, "remaining": 0, "status": "closed", "fee": null, "trades": [], "fees": [], "stopPrice": null, "takeProfitPrice": null, "stopLossPrice": null}]}, {"description": "fetch spot orders", "method": "fetchOrders", "input": ["LTC/USDT", null, 1], "httpResponse": [{"symbol": "LTCUSDT", "orderId": "3854801792", "orderListId": "-1", "clientOrderId": "x-R4BD3S82d1481ae59ba59326d82c8a", "price": "50.********", "origQty": "0.10000000", "executedQty": "0.********", "cummulativeQuoteQty": "0.********", "status": "CANCELED", "timeInForce": "GTC", "type": "LIMIT", "side": "BUY", "stopPrice": "0.********", "icebergQty": "0.********", "time": "*************", "updateTime": "1706883268013", "isWorking": true, "workingTime": "*************", "origQuoteOrderQty": "0.********", "selfTradePreventionMode": "EXPIRE_MAKER"}], "parsedResponse": [{"info": {"symbol": "LTCUSDT", "orderId": "3854801792", "orderListId": "-1", "clientOrderId": "x-R4BD3S82d1481ae59ba59326d82c8a", "price": "50.********", "origQty": "0.10000000", "executedQty": "0.********", "cummulativeQuoteQty": "0.********", "status": "CANCELED", "timeInForce": "GTC", "type": "LIMIT", "side": "BUY", "stopPrice": "0.********", "icebergQty": "0.********", "time": "*************", "updateTime": "1706883268013", "isWorking": true, "workingTime": "*************", "origQuoteOrderQty": "0.********", "selfTradePreventionMode": "EXPIRE_MAKER"}, "id": "3854801792", "clientOrderId": "x-R4BD3S82d1481ae59ba59326d82c8a", "timestamp": *************, "datetime": "2023-12-06T09:53:15.927Z", "lastTradeTimestamp": null, "lastUpdateTimestamp": 1706883268013, "symbol": "LTC/USDT", "type": "limit", "timeInForce": "GTC", "postOnly": false, "reduceOnly": null, "side": "buy", "price": 50, "triggerPrice": null, "amount": 0.1, "cost": 0, "average": null, "filled": 0, "remaining": 0.1, "status": "canceled", "fee": null, "trades": [], "fees": [], "stopPrice": null, "takeProfitPrice": null, "stopLossPrice": null}]}], "fetchTrades": [{"description": "public spot trades", "method": "fetchTrades", "input": ["BTC/USDT", null, 1], "httpResponse": [{"a": "2913537567", "p": "73238.70000000", "q": "0.00109000", "f": "3479463557", "l": "3479463557", "T": "1710327661939", "m": false, "M": true}], "parsedResponse": [{"info": {"a": "2913537567", "p": "73238.70000000", "q": "0.00109000", "f": "3479463557", "l": "3479463557", "T": "1710327661939", "m": false, "M": true}, "timestamp": 1710327661939, "datetime": "2024-03-13T11:01:01.939Z", "symbol": "BTC/USDT", "id": "2913537567", "order": null, "type": null, "side": "buy", "takerOrMaker": null, "price": 73238.7, "amount": 0.00109, "cost": 79.830183, "fee": {"cost": null, "currency": null}, "fees": []}]}], "fetchTicker": [{"description": "public spot ticker", "method": "fetchTicker", "input": ["BTC/USDT"], "httpResponse": {"symbol": "BTCUSDT", "priceChange": "1188.75000000", "priceChangePercent": "1.647", "weightedAvgPrice": "71624.65401064", "prevClosePrice": "72180.93000000", "lastPrice": "73369.84000000", "lastQty": "0.23925000", "bidPrice": "73369.83000000", "bidQty": "6.27086000", "askPrice": "73369.84000000", "askQty": "0.03321000", "openPrice": "72181.09000000", "highPrice": "73650.25000000", "lowPrice": "68620.82000000", "volume": "72140.34862800", "quoteVolume": "5167027510.68728707", "openTime": "1710241845391", "closeTime": "1710328245391", "firstId": "3476288989", "lastId": "3479476926", "count": "3187938"}, "parsedResponse": {"symbol": "BTC/USDT", "timestamp": 1710328245391, "datetime": "2024-03-13T11:10:45.391Z", "high": 73650.25, "low": 68620.82, "bid": 73369.83, "bidVolume": 6.27086, "ask": 73369.84, "askVolume": 0.03321, "vwap": 71624.65401064, "open": 72181.09, "close": 73369.84, "last": 73369.84, "previousClose": 72180.93, "change": 1188.75, "percentage": 1.647, "average": 72775.46, "baseVolume": 72140.348628, "quoteVolume": 5167027510.687287, "markPrice": null, "indexPrice": null, "info": {"symbol": "BTCUSDT", "priceChange": "1188.75000000", "priceChangePercent": "1.647", "weightedAvgPrice": "71624.65401064", "prevClosePrice": "72180.93000000", "lastPrice": "73369.84000000", "lastQty": "0.23925000", "bidPrice": "73369.83000000", "bidQty": "6.27086000", "askPrice": "73369.84000000", "askQty": "0.03321000", "openPrice": "72181.09000000", "highPrice": "73650.25000000", "lowPrice": "68620.82000000", "volume": "72140.34862800", "quoteVolume": "5167027510.68728707", "openTime": "1710241845391", "closeTime": "1710328245391", "firstId": "3476288989", "lastId": "3479476926", "count": "3187938"}}}], "fetchMarginAdjustmentHistory": [{"description": "fetch margin adjustment history", "method": "fetchMarginAdjustmentHistory", "input": ["XRP/USDT:USDT", null, null, 2], "httpResponse": [{"symbol": "XRPUSDT", "type": "1", "deltaType": "TRADE", "amount": "2.57148240", "asset": "USDT", "time": "1711046271555", "positionSide": "BOTH", "clientTranId": ""}, {"symbol": "XRPUSDT", "type": "1", "deltaType": "USER_ADJUST", "amount": "10.********", "asset": "USDT", "time": "1711046323584", "positionSide": "BOTH", "clientTranId": ""}], "parsedResponse": [{"info": {"symbol": "XRPUSDT", "type": "1", "deltaType": "TRADE", "amount": "2.57148240", "asset": "USDT", "time": "1711046271555", "positionSide": "BOTH", "clientTranId": ""}, "symbol": "XRP/USDT:USDT", "type": "add", "marginMode": "isolated", "amount": 2.5714824, "code": "USDT", "total": null, "status": "ok", "timestamp": 1711046271555, "datetime": "2024-03-21T18:37:51.555Z"}, {"info": {"symbol": "XRPUSDT", "type": "1", "deltaType": "USER_ADJUST", "amount": "10.********", "asset": "USDT", "time": "1711046323584", "positionSide": "BOTH", "clientTranId": ""}, "symbol": "XRP/USDT:USDT", "type": "add", "marginMode": "isolated", "amount": 10, "code": "USDT", "total": null, "status": "ok", "timestamp": 1711046323584, "datetime": "2024-03-21T18:38:43.584Z"}]}], "fetchWithdrawals": [{"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "method": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "input": [], "httpResponse": [{"id": "54c1b7543534543fsdfsdf6", "amount": "9", "transactionFee": "1", "coin": "USDT", "status": "6", "address": "TKRxCKea88FDKDSJFSDKJ43243K", "txId": "FDSJHFKDSHFJ4KR5H4KJH3454JHK", "applyTime": "2024-05-05 15:38:56", "network": "TRX", "transferType": "0", "info": "TJDENs5435LK4J3", "confirmNo": "20", "walletType": "0", "txKey": "", "completeTime": "2024-05-05 15:40:44", "type": "withdrawal"}], "parsedResponse": [{"info": {"id": "54c1b7543534543fsdfsdf6", "amount": "9", "transactionFee": "1", "coin": "USDT", "status": "6", "address": "TKRxCKea88FDKDSJFSDKJ43243K", "txId": "FDSJHFKDSHFJ4KR5H4KJH3454JHK", "applyTime": "2024-05-05 15:38:56", "network": "TRX", "transferType": "0", "info": "TJDENs5435LK4J3", "confirmNo": "20", "walletType": "0", "txKey": "", "completeTime": "2024-05-05 15:40:44", "type": "withdrawal"}, "id": "54c1b7543534543fsdfsdf6", "txid": "FDSJHFKDSHFJ4KR5H4KJH3454JHK", "timestamp": 1714923536000, "datetime": "2024-05-05T15:38:56.000Z", "network": "TRX", "address": "TKRxCKea88FDKDSJFSDKJ43243K", "addressTo": "TKRxCKea88FDKDSJFSDKJ43243K", "addressFrom": null, "tag": null, "tagTo": null, "tagFrom": null, "type": "withdrawal", "amount": 9, "currency": "USDT", "status": "ok", "updated": null, "internal": false, "comment": null, "fee": {"currency": "USDT", "cost": 1}}]}], "fetchDeposits": [{"description": "fetchDeposits", "method": "fetchDeposits", "input": [], "httpResponse": [{"id": "4534543435", "amount": "10", "coin": "USDT", "network": "TRX", "status": "1", "address": "534j5h435j34hk", "addressTag": "", "txId": "435kl435jh4354j3k5", "insertTime": "1714923704000", "transferType": "0", "confirmTimes": "1/1", "unlockConfirm": "0", "walletType": "1", "type": "deposit"}], "parsedResponse": [{"info": {"id": "4534543435", "amount": "10", "coin": "USDT", "network": "TRX", "status": "1", "address": "534j5h435j34hk", "addressTag": "", "txId": "435kl435jh4354j3k5", "insertTime": "1714923704000", "transferType": "0", "confirmTimes": "1/1", "unlockConfirm": "0", "walletType": "1", "type": "deposit"}, "id": "4534543435", "txid": "435kl435jh4354j3k5", "timestamp": 1714923704000, "datetime": "2024-05-05T15:41:44.000Z", "network": "TRX", "address": "534j5h435j34hk", "addressTo": "534j5h435j34hk", "addressFrom": null, "tag": null, "tagTo": null, "tagFrom": null, "type": "deposit", "amount": 10, "currency": "USDT", "status": "ok", "updated": null, "internal": false, "comment": null, "fee": null}]}], "withdraw": [{"description": "withdraw", "method": "withdraw", "input": ["USDT", 10.34332, "TKRxCKea88ZvHeGsXEY33CqAQHPDqcAw4N", null, {"network": "TRC20"}], "httpResponse": {"id": "0b894f44acb74b0687398205de0db2d0"}, "parsedResponse": {"info": {"id": "0b894f44acb74b0687398205de0db2d0"}, "id": "0b894f44acb74b0687398205de0db2d0", "txid": null, "timestamp": null, "datetime": null, "network": null, "address": null, "addressTo": null, "addressFrom": null, "tag": null, "tagTo": null, "tagFrom": null, "type": null, "amount": null, "currency": "USDT", "status": null, "updated": null, "internal": null, "comment": null, "fee": null}}], "fetchConvertQuote": [{"description": "fetchConvertQuote", "method": "fetchConvertQuote", "input": ["USDC", "USDT", 5], "httpResponse": {"ratio": "0.999351", "inverseRatio": "1.00065", "validTimestamp": "1718724795211", "toAmount": "4.99675625", "fromAmount": "5"}, "parsedResponse": {"info": {"ratio": "0.999351", "inverseRatio": "1.00065", "validTimestamp": "1718724795211", "toAmount": "4.99675625", "fromAmount": "5"}, "timestamp": 1718724795211, "datetime": "2024-06-18T15:33:15.211Z", "id": null, "fromCurrency": "USDC", "fromAmount": 5, "toCurrency": "USDT", "toAmount": 4.99675625, "price": null, "fee": null}}], "fetchMarginMode": [{"description": "linear swap fetch margin mode", "method": "fetchMarginMode", "input": ["BTC/USDT:USDT"], "httpResponse": [{"symbol": "BTCUSDT", "marginType": "CROSSED", "isAutoAddMargin": false, "leverage": "20", "maxNotionalValue": "1********"}], "parsedResponse": {"info": {"symbol": "BTCUSDT", "marginType": "CROSSED", "isAutoAddMargin": false, "leverage": "20", "maxNotionalValue": "1********"}, "symbol": "BTC/USDT:USDT", "marginMode": "cross"}}, {"description": "inverse swap fetch margin mode", "method": "fetchMarginMode", "input": ["BTC/USD:BTC"], "httpResponse": {"feeTier": "0", "canTrade": true, "canDeposit": true, "canWithdraw": true, "updateTime": "0", "assets": [{"asset": "BTC", "walletBalance": "0.********", "unrealizedProfit": "0.********", "marginBalance": "0.********", "maintMargin": "0.********", "initialMargin": "0.********", "positionInitialMargin": "0.********", "openOrderInitialMargin": "0.********", "maxWithdrawAmount": "0.********", "crossWalletBalance": "0.********", "crossUnPnl": "0.********", "availableBalance": "0.********", "updateTime": "0"}, {"asset": "XLM", "walletBalance": "0.********", "unrealizedProfit": "0.********", "marginBalance": "0.********", "maintMargin": "0.********", "initialMargin": "0.********", "positionInitialMargin": "0.********", "openOrderInitialMargin": "0.********", "maxWithdrawAmount": "0.********", "crossWalletBalance": "0.********", "crossUnPnl": "0.********", "availableBalance": "0.********", "updateTime": "0"}, {"asset": "CRV", "walletBalance": "0.********", "unrealizedProfit": "0.********", "marginBalance": "0.********", "maintMargin": "0.********", "initialMargin": "0.********", "positionInitialMargin": "0.********", "openOrderInitialMargin": "0.********", "maxWithdrawAmount": "0.********", "crossWalletBalance": "0.********", "crossUnPnl": "0.********", "availableBalance": "0.********", "updateTime": "0"}], "positions": [{"symbol": "XLMUSD_PERP", "initialMargin": "0", "maintMargin": "0", "unrealizedProfit": "0.********", "positionInitialMargin": "0", "openOrderInitialMargin": "0", "leverage": "20", "isolated": false, "positionSide": "BOTH", "entryPrice": "0.********", "maxQty": "200000", "notionalValue": "0", "isolatedWallet": "0", "updateTime": "0", "positionAmt": "0", "breakEvenPrice": "0.********"}, {"symbol": "BTCUSD_PERP", "initialMargin": "0", "maintMargin": "0", "unrealizedProfit": "0.********", "positionInitialMargin": "0", "openOrderInitialMargin": "0", "leverage": "20", "isolated": false, "positionSide": "BOTH", "entryPrice": "0.********", "maxQty": "150", "notionalValue": "0", "isolatedWallet": "0", "updateTime": "0", "positionAmt": "0", "breakEvenPrice": "0.********"}, {"symbol": "LTCUSD_241227", "initialMargin": "0", "maintMargin": "0", "unrealizedProfit": "0.********", "positionInitialMargin": "0", "openOrderInitialMargin": "0", "leverage": "20", "isolated": false, "positionSide": "BOTH", "entryPrice": "0.********", "maxQty": "2500", "notionalValue": "0", "isolatedWallet": "0", "updateTime": "0", "positionAmt": "0", "breakEvenPrice": "0.********"}]}, "parsedResponse": {"info": {"symbol": "BTCUSD_PERP", "initialMargin": "0", "maintMargin": "0", "unrealizedProfit": "0.********", "positionInitialMargin": "0", "openOrderInitialMargin": "0", "leverage": "20", "isolated": false, "positionSide": "BOTH", "entryPrice": "0.********", "maxQty": "150", "notionalValue": "0", "isolatedWallet": "0", "updateTime": "0", "positionAmt": "0", "breakEvenPrice": "0.********"}, "symbol": "BTC/USD:BTC", "marginMode": "cross"}}], "editOrders": [{"description": "edit swap orders", "method": "editOrders", "input": [[{"id": "4087922044", "symbol": "BTC/USDT:USDT", "type": "limit", "side": "buy", "amount": 0.1, "price": 666666}]], "httpResponse": [{"orderId": 4087922044, "symbol": "BTCUSDT", "status": "NEW", "clientOrderId": "x-xcKtGhcu04075babaeb34c55af79ba", "price": "666666.00", "avgPrice": "0.00", "origQty": "0.100", "executedQty": "0.000", "cumQty": "0.000", "cumQuote": "0.00000", "timeInForce": "GTC", "type": "LIMIT", "reduceOnly": false, "closePosition": false, "side": "SELL", "positionSide": "BOTH", "stopPrice": "0.00", "workingType": "CONTRACT_PRICE", "priceProtect": false, "origType": "LIMIT", "priceMatch": "NONE", "selfTradePreventionMode": "NONE", "goodTillDate": 0, "updateTime": 1739782577805}], "parsedResponse": [{"info": {"orderId": 4087922044, "symbol": "BTCUSDT", "status": "NEW", "clientOrderId": "x-xcKtGhcu04075babaeb34c55af79ba", "price": "666666.00", "avgPrice": "0.00", "origQty": "0.100", "executedQty": "0.000", "cumQty": "0.000", "cumQuote": "0.00000", "timeInForce": "GTC", "type": "LIMIT", "reduceOnly": false, "closePosition": false, "side": "SELL", "positionSide": "BOTH", "stopPrice": "0.00", "workingType": "CONTRACT_PRICE", "priceProtect": false, "origType": "LIMIT", "priceMatch": "NONE", "selfTradePreventionMode": "NONE", "goodTillDate": 0, "updateTime": 1739782577805}, "id": "4087922044", "clientOrderId": "x-xcKtGhcu04075babaeb34c55af79ba", "timestamp": 1739782577805, "datetime": "2025-02-17T08:56:17.805Z", "lastTradeTimestamp": null, "lastUpdateTimestamp": 1739782577805, "symbol": "BTC/USDT:USDT", "type": "limit", "timeInForce": "GTC", "postOnly": false, "reduceOnly": false, "side": "sell", "price": 666666, "triggerPrice": null, "amount": 0.1, "cost": 0, "average": null, "filled": 0, "remaining": 0.1, "status": "open", "fee": null, "trades": [], "fees": [], "stopPrice": null, "takeProfitPrice": null, "stopLossPrice": null}]}], "fetchAllGreeks": [{"description": "fetchAllGreeks for a single symbol", "method": "fetchAllGreeks", "input": [["ETH/USDT:USDT-231229-800-C"]], "httpResponse": [{"symbol": "ETH-231229-800-C", "markPrice": "1789.2", "bidIV": "-0.00000001", "askIV": "-0.00000001", "markIV": "0.708575", "delta": "-0.91110168", "theta": "-1.0575559", "gamma": "0.0001436", "vega": "2.00337645", "highPriceLimit": "1982.4", "lowPriceLimit": "1596", "riskFreeInterest": "0.065"}], "parsedResponse": {"ETH/USDT:USDT-231229-800-C": {"symbol": "ETH/USDT:USDT-231229-800-C", "timestamp": null, "datetime": null, "delta": -0.91110168, "gamma": 0.0001436, "theta": -1.0575559, "vega": 2.00337645, "rho": null, "bidSize": null, "askSize": null, "bidImpliedVolatility": -1e-08, "askImpliedVolatility": -1e-08, "markImpliedVolatility": 0.708575, "bidPrice": null, "askPrice": null, "markPrice": 1789.2, "lastPrice": null, "underlyingPrice": null, "info": {"symbol": "ETH-231229-800-C", "markPrice": "1789.2", "bidIV": "-0.00000001", "askIV": "-0.00000001", "markIV": "0.708575", "delta": "-0.91110168", "theta": "-1.0575559", "gamma": "0.0001436", "vega": "2.00337645", "highPriceLimit": "1982.4", "lowPriceLimit": "1596", "riskFreeInterest": "0.065"}}}}]}}