{"exchange": "probit", "skipKeys": [], "methods": {"fetchCurrencies": [{"description": "fetchCurrencies", "method": "fetchCurrencies", "input": [], "httpResponse": {"data": [{"id": "USDT", "display_name": {"ko-kr": "테더", "en-us": "<PERSON><PERSON>"}, "show_in_ui": true, "platform": [{"id": "SOL", "priority": "1", "deposit": false, "withdrawal": false, "currency_id": "USDT", "precision": "6", "min_confirmation_count": "5", "require_destination_tag": false, "allow_withdrawal_destination_tag": false, "display_name": {"name": {"en-us": "Solana"}}, "min_deposit_amount": "0", "min_withdrawal_amount": "0", "withdrawal_fee": [{"amount": "3", "priority": "1", "currency_id": "USDT"}], "deposit_fee": {}, "suspended_reason": "", "deposit_suspended": true, "withdrawal_suspended": true, "platform_currency_display_name": {"name": {"en-us": "Solana"}}}, {"id": "BSC", "priority": "1", "deposit": true, "withdrawal": true, "currency_id": "USDT", "precision": "18", "min_confirmation_count": "60", "require_destination_tag": false, "allow_withdrawal_destination_tag": false, "display_name": {"name": {"en-us": "BNB Smart Chain (BEP-20)"}}, "min_deposit_amount": "0", "min_withdrawal_amount": "5", "withdrawal_fee": [{"amount": "1", "priority": "1", "currency_id": "USDT"}], "deposit_fee": {}, "suspended_reason": "", "deposit_suspended": false, "withdrawal_suspended": false, "platform_currency_display_name": {"name": {"en-us": "BNB Smart Chain (BEP-20)"}}}, {"id": "EOS", "priority": "1", "deposit": false, "withdrawal": false, "currency_id": "USDT", "precision": "18", "min_confirmation_count": "1", "require_destination_tag": true, "allow_withdrawal_destination_tag": true, "display_name": {"name": {"en-us": "EOS"}, "destinationTag": {"en-us": "Tag/Memo"}}, "min_deposit_amount": "0", "min_withdrawal_amount": "0", "withdrawal_fee": [{"amount": "0.171267", "priority": "1", "currency_id": "USDT"}], "deposit_fee": {}, "suspended_reason": "token_swap", "deposit_suspended": true, "withdrawal_suspended": true, "platform_currency_display_name": {"name": {"en-us": "EOS"}}}, {"id": "TRON", "priority": "2", "deposit": true, "withdrawal": true, "currency_id": "USDT", "precision": "6", "min_confirmation_count": "1", "require_destination_tag": false, "allow_withdrawal_destination_tag": false, "display_name": {"name": {"en-us": "TRON (TRC-20)"}}, "min_deposit_amount": "0", "min_withdrawal_amount": "0", "withdrawal_fee": [{"amount": "1", "priority": "1", "currency_id": "USDT"}], "deposit_fee": {}, "suspended_reason": "", "deposit_suspended": false, "withdrawal_suspended": false, "platform_currency_display_name": {"name": {"en-us": "TRON (TRC-20)"}}}, {"id": "ETH", "priority": "3", "deposit": true, "withdrawal": true, "currency_id": "USDT", "precision": "6", "min_confirmation_count": "15", "require_destination_tag": false, "allow_withdrawal_destination_tag": false, "display_name": {"name": {"en-us": "Ethereum (ERC-20)"}}, "min_deposit_amount": "0", "min_withdrawal_amount": "5", "withdrawal_fee": [{"amount": "4", "priority": "1", "currency_id": "USDT"}], "deposit_fee": {}, "suspended_reason": "", "deposit_suspended": false, "withdrawal_suspended": false, "platform_currency_display_name": {"name": {"en-us": "Ethereum (ERC-20)"}}}, {"id": "ARB", "priority": "4", "deposit": true, "withdrawal": true, "currency_id": "USDT", "precision": "6", "min_confirmation_count": "60", "require_destination_tag": false, "allow_withdrawal_destination_tag": false, "display_name": {"name": {"en-us": "Arbitrum One"}}, "min_deposit_amount": "0", "min_withdrawal_amount": "5", "withdrawal_fee": [{"currency_id": "USDT", "amount": "1", "priority": "1"}], "deposit_fee": {}, "suspended_reason": "", "deposit_suspended": false, "withdrawal_suspended": false, "platform_currency_display_name": {"name": {"en-us": "Arbitrum One"}}}, {"id": "OMNI", "priority": "4", "deposit": false, "withdrawal": false, "currency_id": "USDT", "precision": "6", "min_confirmation_count": "3", "require_destination_tag": false, "allow_withdrawal_destination_tag": false, "display_name": {"name": {"en-us": "OMNI", "ko-kr": "OMNI"}}, "min_deposit_amount": "0", "min_withdrawal_amount": "5", "withdrawal_fee": [{"amount": "24", "priority": "1", "currency_id": "USDT"}], "deposit_fee": {}, "suspended_reason": "paused", "deposit_suspended": true, "withdrawal_suspended": true, "platform_currency_display_name": {"name": {"en-us": "OMNI", "ko-kr": "OMNI"}}}, {"id": "MATIC", "priority": "4", "deposit": true, "withdrawal": true, "currency_id": "USDT", "precision": "6", "min_confirmation_count": "18", "require_destination_tag": false, "allow_withdrawal_destination_tag": false, "display_name": {"name": {"en-us": "Polygon"}}, "min_deposit_amount": "0", "min_withdrawal_amount": "5", "withdrawal_fee": [{"currency_id": "USDT", "amount": "1", "priority": "1"}], "deposit_fee": {}, "suspended_reason": "", "deposit_suspended": false, "withdrawal_suspended": false, "platform_currency_display_name": {"name": {"en-us": "Polygon"}}}, {"id": "TON", "priority": "5", "deposit": true, "withdrawal": true, "currency_id": "USDT", "precision": "6", "min_confirmation_count": "30", "require_destination_tag": true, "allow_withdrawal_destination_tag": true, "display_name": {"name": {"en-us": "<PERSON><PERSON>in"}, "destinationTag": {"en-us": "Tag/Memo"}}, "min_deposit_amount": "0", "min_withdrawal_amount": "5", "withdrawal_fee": [{"currency_id": "USDT", "amount": "3", "priority": "1"}], "deposit_fee": {}, "suspended_reason": "", "deposit_suspended": false, "withdrawal_suspended": false, "platform_currency_display_name": {"name": {"en-us": "<PERSON><PERSON>in"}, "destinationTag": {"en-us": "Tag/Memo"}}}, {"id": "BNC", "priority": "6", "deposit": false, "withdrawal": false, "currency_id": "USDT", "precision": "18", "min_confirmation_count": "1", "require_destination_tag": true, "allow_withdrawal_destination_tag": true, "display_name": {"name": {"en-us": "BNB Beacon Chain (BEP2)"}, "destinationTag": {"en-us": "Tag/Memo"}}, "min_deposit_amount": "0", "min_withdrawal_amount": "0", "withdrawal_fee": [{"amount": "0.910653", "priority": "1", "currency_id": "USDT"}, {"amount": "0.0011", "priority": "2", "currency_id": "BNB"}], "deposit_fee": {}, "suspended_reason": "not_supported", "deposit_suspended": true, "withdrawal_suspended": true, "platform_currency_display_name": {"name": {"en-us": "BNB Beacon Chain (BEP2)"}}}, {"id": "AVAX", "priority": "6", "deposit": true, "withdrawal": true, "currency_id": "USDT", "precision": "6", "min_confirmation_count": "30", "require_destination_tag": false, "allow_withdrawal_destination_tag": false, "display_name": {"name": {"en-us": "AVAX C-Chain"}}, "min_deposit_amount": "0", "min_withdrawal_amount": "5", "withdrawal_fee": [{"currency_id": "USDT", "amount": "1", "priority": "1"}], "deposit_fee": {}, "suspended_reason": "", "deposit_suspended": false, "withdrawal_suspended": false, "platform_currency_display_name": {"name": {"en-us": "AVAX C-Chain"}}}], "stakeable": false, "unstakeable": false, "auto_stake": false, "auto_stake_amount": "0", "shutdown": false, "internal_transfer": {"precision": "6", "min_withdrawal_amount": "0", "suspended": false, "suspended_reason": null}}]}, "parsedResponse": {"USDT": {"info": {"id": "USDT", "display_name": {"ko-kr": "테더", "en-us": "<PERSON><PERSON>"}, "show_in_ui": true, "platform": [{"id": "SOL", "priority": "1", "deposit": false, "withdrawal": false, "currency_id": "USDT", "precision": "6", "min_confirmation_count": "5", "require_destination_tag": false, "allow_withdrawal_destination_tag": false, "display_name": {"name": {"en-us": "Solana"}}, "min_deposit_amount": "0", "min_withdrawal_amount": "0", "withdrawal_fee": [{"amount": "3", "priority": "1", "currency_id": "USDT"}], "deposit_fee": {}, "suspended_reason": "", "deposit_suspended": true, "withdrawal_suspended": true, "platform_currency_display_name": {"name": {"en-us": "Solana"}}}, {"id": "BSC", "priority": "1", "deposit": true, "withdrawal": true, "currency_id": "USDT", "precision": "18", "min_confirmation_count": "60", "require_destination_tag": false, "allow_withdrawal_destination_tag": false, "display_name": {"name": {"en-us": "BNB Smart Chain (BEP-20)"}}, "min_deposit_amount": "0", "min_withdrawal_amount": "5", "withdrawal_fee": [{"amount": "1", "priority": "1", "currency_id": "USDT"}], "deposit_fee": {}, "suspended_reason": "", "deposit_suspended": false, "withdrawal_suspended": false, "platform_currency_display_name": {"name": {"en-us": "BNB Smart Chain (BEP-20)"}}}, {"id": "EOS", "priority": "1", "deposit": false, "withdrawal": false, "currency_id": "USDT", "precision": "18", "min_confirmation_count": "1", "require_destination_tag": true, "allow_withdrawal_destination_tag": true, "display_name": {"name": {"en-us": "EOS"}, "destinationTag": {"en-us": "Tag/Memo"}}, "min_deposit_amount": "0", "min_withdrawal_amount": "0", "withdrawal_fee": [{"amount": "0.171267", "priority": "1", "currency_id": "USDT"}], "deposit_fee": {}, "suspended_reason": "token_swap", "deposit_suspended": true, "withdrawal_suspended": true, "platform_currency_display_name": {"name": {"en-us": "EOS"}}}, {"id": "TRON", "priority": "2", "deposit": true, "withdrawal": true, "currency_id": "USDT", "precision": "6", "min_confirmation_count": "1", "require_destination_tag": false, "allow_withdrawal_destination_tag": false, "display_name": {"name": {"en-us": "TRON (TRC-20)"}}, "min_deposit_amount": "0", "min_withdrawal_amount": "0", "withdrawal_fee": [{"amount": "1", "priority": "1", "currency_id": "USDT"}], "deposit_fee": {}, "suspended_reason": "", "deposit_suspended": false, "withdrawal_suspended": false, "platform_currency_display_name": {"name": {"en-us": "TRON (TRC-20)"}}}, {"id": "ETH", "priority": "3", "deposit": true, "withdrawal": true, "currency_id": "USDT", "precision": "6", "min_confirmation_count": "15", "require_destination_tag": false, "allow_withdrawal_destination_tag": false, "display_name": {"name": {"en-us": "Ethereum (ERC-20)"}}, "min_deposit_amount": "0", "min_withdrawal_amount": "5", "withdrawal_fee": [{"amount": "4", "priority": "1", "currency_id": "USDT"}], "deposit_fee": {}, "suspended_reason": "", "deposit_suspended": false, "withdrawal_suspended": false, "platform_currency_display_name": {"name": {"en-us": "Ethereum (ERC-20)"}}}, {"id": "ARB", "priority": "4", "deposit": true, "withdrawal": true, "currency_id": "USDT", "precision": "6", "min_confirmation_count": "60", "require_destination_tag": false, "allow_withdrawal_destination_tag": false, "display_name": {"name": {"en-us": "Arbitrum One"}}, "min_deposit_amount": "0", "min_withdrawal_amount": "5", "withdrawal_fee": [{"currency_id": "USDT", "amount": "1", "priority": "1"}], "deposit_fee": {}, "suspended_reason": "", "deposit_suspended": false, "withdrawal_suspended": false, "platform_currency_display_name": {"name": {"en-us": "Arbitrum One"}}}, {"id": "OMNI", "priority": "4", "deposit": false, "withdrawal": false, "currency_id": "USDT", "precision": "6", "min_confirmation_count": "3", "require_destination_tag": false, "allow_withdrawal_destination_tag": false, "display_name": {"name": {"en-us": "OMNI", "ko-kr": "OMNI"}}, "min_deposit_amount": "0", "min_withdrawal_amount": "5", "withdrawal_fee": [{"amount": "24", "priority": "1", "currency_id": "USDT"}], "deposit_fee": {}, "suspended_reason": "paused", "deposit_suspended": true, "withdrawal_suspended": true, "platform_currency_display_name": {"name": {"en-us": "OMNI", "ko-kr": "OMNI"}}}, {"id": "MATIC", "priority": "4", "deposit": true, "withdrawal": true, "currency_id": "USDT", "precision": "6", "min_confirmation_count": "18", "require_destination_tag": false, "allow_withdrawal_destination_tag": false, "display_name": {"name": {"en-us": "Polygon"}}, "min_deposit_amount": "0", "min_withdrawal_amount": "5", "withdrawal_fee": [{"currency_id": "USDT", "amount": "1", "priority": "1"}], "deposit_fee": {}, "suspended_reason": "", "deposit_suspended": false, "withdrawal_suspended": false, "platform_currency_display_name": {"name": {"en-us": "Polygon"}}}, {"id": "TON", "priority": "5", "deposit": true, "withdrawal": true, "currency_id": "USDT", "precision": "6", "min_confirmation_count": "30", "require_destination_tag": true, "allow_withdrawal_destination_tag": true, "display_name": {"name": {"en-us": "<PERSON><PERSON>in"}, "destinationTag": {"en-us": "Tag/Memo"}}, "min_deposit_amount": "0", "min_withdrawal_amount": "5", "withdrawal_fee": [{"currency_id": "USDT", "amount": "3", "priority": "1"}], "deposit_fee": {}, "suspended_reason": "", "deposit_suspended": false, "withdrawal_suspended": false, "platform_currency_display_name": {"name": {"en-us": "<PERSON><PERSON>in"}, "destinationTag": {"en-us": "Tag/Memo"}}}, {"id": "BNC", "priority": "6", "deposit": false, "withdrawal": false, "currency_id": "USDT", "precision": "18", "min_confirmation_count": "1", "require_destination_tag": true, "allow_withdrawal_destination_tag": true, "display_name": {"name": {"en-us": "BNB Beacon Chain (BEP2)"}, "destinationTag": {"en-us": "Tag/Memo"}}, "min_deposit_amount": "0", "min_withdrawal_amount": "0", "withdrawal_fee": [{"amount": "0.910653", "priority": "1", "currency_id": "USDT"}, {"amount": "0.0011", "priority": "2", "currency_id": "BNB"}], "deposit_fee": {}, "suspended_reason": "not_supported", "deposit_suspended": true, "withdrawal_suspended": true, "platform_currency_display_name": {"name": {"en-us": "BNB Beacon Chain (BEP2)"}}}, {"id": "AVAX", "priority": "6", "deposit": true, "withdrawal": true, "currency_id": "USDT", "precision": "6", "min_confirmation_count": "30", "require_destination_tag": false, "allow_withdrawal_destination_tag": false, "display_name": {"name": {"en-us": "AVAX C-Chain"}}, "min_deposit_amount": "0", "min_withdrawal_amount": "5", "withdrawal_fee": [{"currency_id": "USDT", "amount": "1", "priority": "1"}], "deposit_fee": {}, "suspended_reason": "", "deposit_suspended": false, "withdrawal_suspended": false, "platform_currency_display_name": {"name": {"en-us": "AVAX C-Chain"}}}], "stakeable": false, "unstakeable": false, "auto_stake": false, "auto_stake_amount": "0", "shutdown": false, "internal_transfer": {"precision": "6", "min_withdrawal_amount": "0", "suspended": false, "suspended_reason": null}}, "id": "USDT", "numericId": null, "code": "USDT", "precision": 1e-06, "type": "crypto", "name": "<PERSON><PERSON>", "active": true, "deposit": true, "withdraw": true, "fee": 0.171267, "fees": {}, "networks": {"SOL": {"id": "SOL", "network": "SOL", "active": false, "deposit": false, "withdraw": false, "fee": 3, "precision": 1e-06, "limits": {"withdraw": {"min": 0, "max": null}, "deposit": {"min": 0, "max": null}}, "info": {"id": "SOL", "priority": "1", "deposit": false, "withdrawal": false, "currency_id": "USDT", "precision": "6", "min_confirmation_count": "5", "require_destination_tag": false, "allow_withdrawal_destination_tag": false, "display_name": {"name": {"en-us": "Solana"}}, "min_deposit_amount": "0", "min_withdrawal_amount": "0", "withdrawal_fee": [{"amount": "3", "priority": "1", "currency_id": "USDT"}], "deposit_fee": {}, "suspended_reason": "", "deposit_suspended": true, "withdrawal_suspended": true, "platform_currency_display_name": {"name": {"en-us": "Solana"}}}}, "BEP20": {"id": "BSC", "network": "BEP20", "active": true, "deposit": true, "withdraw": true, "fee": 1, "precision": 1e-18, "limits": {"withdraw": {"min": 5, "max": null}, "deposit": {"min": 0, "max": null}}, "info": {"id": "BSC", "priority": "1", "deposit": true, "withdrawal": true, "currency_id": "USDT", "precision": "18", "min_confirmation_count": "60", "require_destination_tag": false, "allow_withdrawal_destination_tag": false, "display_name": {"name": {"en-us": "BNB Smart Chain (BEP-20)"}}, "min_deposit_amount": "0", "min_withdrawal_amount": "5", "withdrawal_fee": [{"amount": "1", "priority": "1", "currency_id": "USDT"}], "deposit_fee": {}, "suspended_reason": "", "deposit_suspended": false, "withdrawal_suspended": false, "platform_currency_display_name": {"name": {"en-us": "BNB Smart Chain (BEP-20)"}}}}, "EOS": {"id": "EOS", "network": "EOS", "active": false, "deposit": false, "withdraw": false, "fee": 0.171267, "precision": 1e-18, "limits": {"withdraw": {"min": 0, "max": null}, "deposit": {"min": 0, "max": null}}, "info": {"id": "EOS", "priority": "1", "deposit": false, "withdrawal": false, "currency_id": "USDT", "precision": "18", "min_confirmation_count": "1", "require_destination_tag": true, "allow_withdrawal_destination_tag": true, "display_name": {"name": {"en-us": "EOS"}, "destinationTag": {"en-us": "Tag/Memo"}}, "min_deposit_amount": "0", "min_withdrawal_amount": "0", "withdrawal_fee": [{"amount": "0.171267", "priority": "1", "currency_id": "USDT"}], "deposit_fee": {}, "suspended_reason": "token_swap", "deposit_suspended": true, "withdrawal_suspended": true, "platform_currency_display_name": {"name": {"en-us": "EOS"}}}}, "TRC20": {"id": "TRON", "network": "TRC20", "active": true, "deposit": true, "withdraw": true, "fee": 1, "precision": 1e-06, "limits": {"withdraw": {"min": 0, "max": null}, "deposit": {"min": 0, "max": null}}, "info": {"id": "TRON", "priority": "2", "deposit": true, "withdrawal": true, "currency_id": "USDT", "precision": "6", "min_confirmation_count": "1", "require_destination_tag": false, "allow_withdrawal_destination_tag": false, "display_name": {"name": {"en-us": "TRON (TRC-20)"}}, "min_deposit_amount": "0", "min_withdrawal_amount": "0", "withdrawal_fee": [{"amount": "1", "priority": "1", "currency_id": "USDT"}], "deposit_fee": {}, "suspended_reason": "", "deposit_suspended": false, "withdrawal_suspended": false, "platform_currency_display_name": {"name": {"en-us": "TRON (TRC-20)"}}}}, "ERC20": {"id": "ETH", "network": "ERC20", "active": true, "deposit": true, "withdraw": true, "fee": 4, "precision": 1e-06, "limits": {"withdraw": {"min": 5, "max": null}, "deposit": {"min": 0, "max": null}}, "info": {"id": "ETH", "priority": "3", "deposit": true, "withdrawal": true, "currency_id": "USDT", "precision": "6", "min_confirmation_count": "15", "require_destination_tag": false, "allow_withdrawal_destination_tag": false, "display_name": {"name": {"en-us": "Ethereum (ERC-20)"}}, "min_deposit_amount": "0", "min_withdrawal_amount": "5", "withdrawal_fee": [{"amount": "4", "priority": "1", "currency_id": "USDT"}], "deposit_fee": {}, "suspended_reason": "", "deposit_suspended": false, "withdrawal_suspended": false, "platform_currency_display_name": {"name": {"en-us": "Ethereum (ERC-20)"}}}}, "ARB": {"id": "ARB", "network": "ARB", "active": true, "deposit": true, "withdraw": true, "fee": 1, "precision": 1e-06, "limits": {"withdraw": {"min": 5, "max": null}, "deposit": {"min": 0, "max": null}}, "info": {"id": "ARB", "priority": "4", "deposit": true, "withdrawal": true, "currency_id": "USDT", "precision": "6", "min_confirmation_count": "60", "require_destination_tag": false, "allow_withdrawal_destination_tag": false, "display_name": {"name": {"en-us": "Arbitrum One"}}, "min_deposit_amount": "0", "min_withdrawal_amount": "5", "withdrawal_fee": [{"currency_id": "USDT", "amount": "1", "priority": "1"}], "deposit_fee": {}, "suspended_reason": "", "deposit_suspended": false, "withdrawal_suspended": false, "platform_currency_display_name": {"name": {"en-us": "Arbitrum One"}}}}, "OMNI": {"id": "OMNI", "network": "OMNI", "active": false, "deposit": false, "withdraw": false, "fee": 24, "precision": 1e-06, "limits": {"withdraw": {"min": 5, "max": null}, "deposit": {"min": 0, "max": null}}, "info": {"id": "OMNI", "priority": "4", "deposit": false, "withdrawal": false, "currency_id": "USDT", "precision": "6", "min_confirmation_count": "3", "require_destination_tag": false, "allow_withdrawal_destination_tag": false, "display_name": {"name": {"en-us": "OMNI", "ko-kr": "OMNI"}}, "min_deposit_amount": "0", "min_withdrawal_amount": "5", "withdrawal_fee": [{"amount": "24", "priority": "1", "currency_id": "USDT"}], "deposit_fee": {}, "suspended_reason": "paused", "deposit_suspended": true, "withdrawal_suspended": true, "platform_currency_display_name": {"name": {"en-us": "OMNI", "ko-kr": "OMNI"}}}}, "MATIC": {"id": "MATIC", "network": "MATIC", "active": true, "deposit": true, "withdraw": true, "fee": 1, "precision": 1e-06, "limits": {"withdraw": {"min": 5, "max": null}, "deposit": {"min": 0, "max": null}}, "info": {"id": "MATIC", "priority": "4", "deposit": true, "withdrawal": true, "currency_id": "USDT", "precision": "6", "min_confirmation_count": "18", "require_destination_tag": false, "allow_withdrawal_destination_tag": false, "display_name": {"name": {"en-us": "Polygon"}}, "min_deposit_amount": "0", "min_withdrawal_amount": "5", "withdrawal_fee": [{"currency_id": "USDT", "amount": "1", "priority": "1"}], "deposit_fee": {}, "suspended_reason": "", "deposit_suspended": false, "withdrawal_suspended": false, "platform_currency_display_name": {"name": {"en-us": "Polygon"}}}}, "TON": {"id": "TON", "network": "TON", "active": true, "deposit": true, "withdraw": true, "fee": 3, "precision": 1e-06, "limits": {"withdraw": {"min": 5, "max": null}, "deposit": {"min": 0, "max": null}}, "info": {"id": "TON", "priority": "5", "deposit": true, "withdrawal": true, "currency_id": "USDT", "precision": "6", "min_confirmation_count": "30", "require_destination_tag": true, "allow_withdrawal_destination_tag": true, "display_name": {"name": {"en-us": "<PERSON><PERSON>in"}, "destinationTag": {"en-us": "Tag/Memo"}}, "min_deposit_amount": "0", "min_withdrawal_amount": "5", "withdrawal_fee": [{"currency_id": "USDT", "amount": "3", "priority": "1"}], "deposit_fee": {}, "suspended_reason": "", "deposit_suspended": false, "withdrawal_suspended": false, "platform_currency_display_name": {"name": {"en-us": "<PERSON><PERSON>in"}, "destinationTag": {"en-us": "Tag/Memo"}}}}, "BNC": {"id": "BNC", "network": "BNC", "active": false, "deposit": false, "withdraw": false, "fee": 0.910653, "precision": 1e-18, "limits": {"withdraw": {"min": 0, "max": null}, "deposit": {"min": 0, "max": null}}, "info": {"id": "BNC", "priority": "6", "deposit": false, "withdrawal": false, "currency_id": "USDT", "precision": "18", "min_confirmation_count": "1", "require_destination_tag": true, "allow_withdrawal_destination_tag": true, "display_name": {"name": {"en-us": "BNB Beacon Chain (BEP2)"}, "destinationTag": {"en-us": "Tag/Memo"}}, "min_deposit_amount": "0", "min_withdrawal_amount": "0", "withdrawal_fee": [{"amount": "0.910653", "priority": "1", "currency_id": "USDT"}, {"amount": "0.0011", "priority": "2", "currency_id": "BNB"}], "deposit_fee": {}, "suspended_reason": "not_supported", "deposit_suspended": true, "withdrawal_suspended": true, "platform_currency_display_name": {"name": {"en-us": "BNB Beacon Chain (BEP2)"}}}}, "AVAX": {"id": "AVAX", "network": "AVAX", "active": true, "deposit": true, "withdraw": true, "fee": 1, "precision": 1e-06, "limits": {"withdraw": {"min": 5, "max": null}, "deposit": {"min": 0, "max": null}}, "info": {"id": "AVAX", "priority": "6", "deposit": true, "withdrawal": true, "currency_id": "USDT", "precision": "6", "min_confirmation_count": "30", "require_destination_tag": false, "allow_withdrawal_destination_tag": false, "display_name": {"name": {"en-us": "AVAX C-Chain"}}, "min_deposit_amount": "0", "min_withdrawal_amount": "5", "withdrawal_fee": [{"currency_id": "USDT", "amount": "1", "priority": "1"}], "deposit_fee": {}, "suspended_reason": "", "deposit_suspended": false, "withdrawal_suspended": false, "platform_currency_display_name": {"name": {"en-us": "AVAX C-Chain"}}}}}, "limits": {"amount": {"min": null, "max": null}, "deposit": {"min": 0, "max": null}, "withdraw": {"min": 0, "max": null}}}}}], "fetchTrades": [{"description": "public spot trades", "method": "fetchTrades", "input": ["BTC/USDT", null, 1], "httpResponse": {"data": [{"id": "BTC-USDT:9860155", "price": "73238.7", "quantity": "0.62417", "time": "2024-03-13T11:00:59.680Z", "side": "sell", "tick_direction": "down"}]}, "parsedResponse": [{"id": "BTC-USDT:9860155", "info": {"id": "BTC-USDT:9860155", "price": "73238.7", "quantity": "0.62417", "time": "2024-03-13T11:00:59.680Z", "side": "sell", "tick_direction": "down"}, "timestamp": 1710327659680, "datetime": "2024-03-13T11:00:59.680Z", "symbol": "BTC/USDT", "order": null, "type": null, "side": "sell", "takerOrMaker": null, "price": 73238.7, "amount": 0.62417, "cost": 45713.399379, "fee": {"cost": null, "currency": null}, "fees": []}]}], "fetchTicker": [{"description": "public spot ticker", "method": "fetchTicker", "input": ["BTC/USDT"], "httpResponse": {"data": [{"last": "73369", "low": "69089", "high": "73618", "change": "1219.7", "base_volume": "1042.9499547", "quote_volume": "74982342.163428567", "market_id": "BTC-USDT", "time": "2024-03-13T11:10:26.000Z"}]}, "parsedResponse": {"symbol": "BTC/USDT", "timestamp": 1710328226000, "datetime": "2024-03-13T11:10:26.000Z", "high": 73618, "low": 69089, "bid": null, "bidVolume": null, "ask": null, "askVolume": null, "vwap": 71894.47760702661, "open": 72149.3, "close": 73369, "last": 73369, "previousClose": null, "change": 1219.7, "percentage": 1.6905222919695686, "average": 72759.15, "baseVolume": 1042.9499547, "quoteVolume": 74982342.16342856, "markPrice": null, "indexPrice": null, "info": {"last": "73369", "low": "69089", "high": "73618", "change": "1219.7", "base_volume": "1042.9499547", "quote_volume": "74982342.163428567", "market_id": "BTC-USDT", "time": "2024-03-13T11:10:26.000Z"}}}], "fetchOHLCV": [{"description": "spot ohlcv", "method": "fetchOHLCV", "input": ["BTC/USDT", "1h", null, 1], "httpResponse": {"data": [{"market_id": "BTC-USDT", "open": "73269.5", "close": "73399", "low": "73238.7", "high": "73459.6", "base_volume": "13.480878", "quote_volume": "988687.5240468", "start_time": "2024-03-13T11:00:00.000Z", "end_time": "2024-03-13T12:00:00.000Z"}]}, "parsedResponse": [[1710327600000, 73269.5, 73459.6, 73238.7, 73399, 13.480878]]}]}}