{"exchange": "bingx", "skipKeys": ["tranId", "timestamp", "id"], "options": {}, "methods": {"fetchFundingRate": [{"description": "fetch funding rate linear", "method": "fetchFundingRate", "input": ["BTC/USDT:USDT"], "httpResponse": {"code": "0", "msg": "", "data": {"symbol": "BTC-USDT", "markPrice": "104701.1", "indexPrice": "104748.9", "lastFundingRate": "0.00004900", "nextFundingTime": "*************"}}, "parsedResponse": {"info": {"symbol": "BTC-USDT", "markPrice": "104701.1", "indexPrice": "104748.9", "lastFundingRate": "0.00004900", "nextFundingTime": "*************"}, "symbol": "BTC/USDT:USDT", "markPrice": 104701.1, "indexPrice": 104748.9, "interestRate": null, "estimatedSettlePrice": null, "timestamp": null, "datetime": null, "fundingRate": 4.9e-05, "fundingTimestamp": null, "fundingDatetime": null, "nextFundingRate": null, "nextFundingTimestamp": *************, "nextFundingDatetime": "2025-06-13T16:00:00.000Z", "previousFundingRate": null, "previousFundingTimestamp": null, "previousFundingDatetime": null, "interval": null}}], "fetchTransfers": [{"description": "fetch USDT transfers from spot to swap", "method": "fetchTransfers", "input": ["USDT", null, null, {"fromAccount": "spot", "toAccount": "swap"}], "httpResponse": {"total": "2", "rows": [{"asset": "LTC", "amount": "0.05000000000000000000", "status": "CONFIRMED", "transferId": "1051461075661819338791", "timestamp": "*************", "fromAccount": "spot", "toAccount": "USDTMPerp"}, {"asset": "USDT", "amount": "10.00000000000000000000", "status": "CONFIRMED", "transferId": "1051461077440303243364", "timestamp": "*************", "fromAccount": "spot", "toAccount": "USDTMPerp"}]}, "parsedResponse": [{"info": {"asset": "USDT", "amount": "10.00000000000000000000", "status": "CONFIRMED", "transferId": "1051461077440303243364", "timestamp": "*************", "fromAccount": "spot", "toAccount": "USDTMPerp"}, "id": "1051461077440303243364", "timestamp": *************, "datetime": "2025-07-11T02:55:46.000Z", "currency": "USDT", "amount": 10, "fromAccount": "spot", "toAccount": "linear", "status": "ok"}]}], "transfer": [{"description": "transfer LTC from inverse swap to spot using the subType parameter", "method": "transfer", "input": ["LTC", 0.05, "swap", "spot", {"subType": "inverse"}], "httpResponse": {"code": "0", "timestamp": "*************", "data": {"tranId": "1943502883135819776", "transferId": "1051461075875997081703"}}, "parsedResponse": {"info": {"code": "0", "timestamp": "*************", "data": {"tranId": "1943502883135819776", "transferId": "1051461075875997081703"}}, "id": null, "timestamp": null, "datetime": null, "currency": "LTC", "amount": 0.05, "fromAccount": "swap", "toAccount": "spot", "status": null}}, {"description": "transfer USDT from spot to linear swap", "method": "transfer", "input": ["USDT", 10, "spot", "swap"], "httpResponse": {"code": "0", "timestamp": "*************", "data": {"tranId": "1943504457329414144", "transferId": "1051461077440303243364"}}, "parsedResponse": {"info": {"code": "0", "timestamp": "*************", "data": {"tranId": "1943504457329414144", "transferId": "1051461077440303243364"}}, "id": null, "timestamp": null, "datetime": null, "currency": "USDT", "amount": 10, "fromAccount": "spot", "toAccount": "swap", "status": null}}], "fetchCurrencies": [{"description": "fetchCurrencies", "method": "fetchCurrencies", "disabled": true, "input": [], "httpResponse": {"code": "0", "timestamp": "*************", "data": [{"coin": "BTC", "name": "BTC", "networkList": [{"name": "BTC", "network": "BTC", "isDefault": true, "minConfirm": "2", "withdrawEnable": true, "depositEnable": true, "withdrawFee": "0.000049", "withdrawMax": "97.118145", "withdrawMin": "0.000054", "depositMin": "0.0002", "withdrawPrecision": "8", "depositPrecision": "8", "contractAddress": "", "needTagOrMemo": "false"}, {"name": "BTC", "network": "BEP20", "isDefault": false, "minConfirm": "15", "withdrawEnable": true, "depositEnable": true, "withdrawFee": "0.000001", "withdrawMax": "97.118145", "withdrawMin": "0.000049", "depositMin": "0.0002", "withdrawPrecision": "8", "depositPrecision": "18", "contractAddress": "******************************************", "needTagOrMemo": "false"}]}, {"coin": "USDT", "name": "USDT", "networkList": [{"name": "USDT", "network": "ERC20", "isDefault": false, "minConfirm": "12", "withdrawEnable": true, "depositEnable": true, "withdrawFee": "7", "withdrawMax": "10000000", "withdrawMin": "5.5", "depositMin": "1", "withdrawPrecision": "6", "depositPrecision": "6", "contractAddress": "******************************************", "needTagOrMemo": "false"}, {"name": "USDT", "network": "TRC20", "isDefault": false, "minConfirm": "2", "withdrawEnable": true, "depositEnable": true, "withdrawFee": "1", "withdrawMax": "10000000", "withdrawMin": "8", "depositMin": "1", "withdrawPrecision": "6", "depositPrecision": "6", "contractAddress": "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t", "needTagOrMemo": "false"}, {"name": "USDT", "network": "BEP20", "isDefault": false, "minConfirm": "15", "withdrawEnable": true, "depositEnable": true, "withdrawFee": "0", "withdrawMax": "10000000", "withdrawMin": "3", "depositMin": "0.8", "withdrawPrecision": "8", "depositPrecision": "18", "contractAddress": "0x55d398326f99059ff775485246999027b3197955", "needTagOrMemo": "false"}, {"name": "USDT", "network": "SOL", "isDefault": false, "minConfirm": "50", "withdrawEnable": true, "depositEnable": true, "withdrawFee": "1", "withdrawMax": "10000000", "withdrawMin": "1.00000000000000000001", "depositMin": "0.9", "withdrawPrecision": "6", "depositPrecision": "6", "contractAddress": "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB", "needTagOrMemo": "false"}, {"name": "USDT", "network": "POLYGON", "isDefault": false, "minConfirm": "64", "withdrawEnable": true, "depositEnable": true, "withdrawFee": "0.8", "withdrawMax": "10000000", "withdrawMin": "10", "depositMin": "0.9", "withdrawPrecision": "6", "depositPrecision": "6", "contractAddress": "0xc2132d05d31c914a87c6611c10748aeb04b58e8f", "needTagOrMemo": "false"}, {"name": "USDT", "network": "ARBITRUM", "isDefault": false, "minConfirm": "30", "withdrawEnable": true, "depositEnable": true, "withdrawFee": "0.5", "withdrawMax": "10000000", "withdrawMin": "0.5", "depositMin": "5", "withdrawPrecision": "6", "depositPrecision": "6", "contractAddress": "0xfd086bc7cd5c481dcc9c85ebe478a1c0b69fcbb9", "needTagOrMemo": "false"}, {"name": "USDT", "network": "OPTIMISM", "isDefault": false, "minConfirm": "50", "withdrawEnable": false, "depositEnable": true, "withdrawFee": "1", "withdrawMax": "10000000", "withdrawMin": "1.1", "depositMin": "5", "withdrawPrecision": "6", "depositPrecision": "6", "contractAddress": "******************************************", "needTagOrMemo": "false"}, {"name": "USDT", "network": "TON", "isDefault": false, "minConfirm": "25", "withdrawEnable": true, "depositEnable": true, "withdrawFee": "0.15", "withdrawMax": "10000000", "withdrawMin": "5", "depositMin": "5", "withdrawPrecision": "6", "depositPrecision": "6", "contractAddress": "EQCxE6mUtQJKFnGfaROTKOt1lZbDiiX1kCixRv7Nw2Id_sDs", "needTagOrMemo": "false"}]}]}, "parsedResponse": {"BTC": {"info": {"coin": "BTC", "name": "BTC", "networkList": [{"name": "BTC", "network": "BTC", "isDefault": true, "minConfirm": "2", "withdrawEnable": true, "depositEnable": true, "withdrawFee": "0.000049", "withdrawMax": "97.118145", "withdrawMin": "0.000054", "depositMin": "0.0002", "withdrawPrecision": "8", "depositPrecision": "8", "contractAddress": "", "needTagOrMemo": "false"}, {"name": "BTC", "network": "BEP20", "isDefault": false, "minConfirm": "15", "withdrawEnable": true, "depositEnable": true, "withdrawFee": "0.000001", "withdrawMax": "97.118145", "withdrawMin": "0.000049", "depositMin": "0.0002", "withdrawPrecision": "8", "depositPrecision": "18", "contractAddress": "******************************************", "needTagOrMemo": "false"}]}, "id": "BTC", "numericId": null, "code": "BTC", "precision": 1e-08, "type": "crypto", "name": "BTC", "active": true, "deposit": true, "withdraw": true, "fee": 1e-06, "fees": {}, "networks": {"BTC": {"info": {"name": "BTC", "network": "BTC", "isDefault": true, "minConfirm": "2", "withdrawEnable": true, "depositEnable": true, "withdrawFee": "0.000049", "withdrawMax": "97.118145", "withdrawMin": "0.000054", "depositMin": "0.0002", "withdrawPrecision": "8", "depositPrecision": "8", "contractAddress": "", "needTagOrMemo": "false"}, "id": "BTC", "network": "BTC", "fee": 4.9e-05, "active": true, "deposit": true, "withdraw": true, "precision": 1e-08, "limits": {"withdraw": {"min": 5.4e-05, "max": 97.118145}}}, "BEP20": {"info": {"name": "BTC", "network": "BEP20", "isDefault": false, "minConfirm": "15", "withdrawEnable": true, "depositEnable": true, "withdrawFee": "0.000001", "withdrawMax": "97.118145", "withdrawMin": "0.000049", "depositMin": "0.0002", "withdrawPrecision": "8", "depositPrecision": "18", "contractAddress": "******************************************", "needTagOrMemo": "false"}, "id": "BEP20", "network": "BEP20", "fee": 1e-06, "active": true, "deposit": true, "withdraw": true, "precision": 1e-08, "limits": {"withdraw": {"min": 4.9e-05, "max": 97.118145}}}}, "limits": {"deposit": {"min": null, "max": null}, "withdraw": {"min": 4.9e-05, "max": 97.118145}}}, "USDT": {"info": {"coin": "USDT", "name": "USDT", "networkList": [{"name": "USDT", "network": "ERC20", "isDefault": false, "minConfirm": "12", "withdrawEnable": true, "depositEnable": true, "withdrawFee": "7", "withdrawMax": "10000000", "withdrawMin": "5.5", "depositMin": "1", "withdrawPrecision": "6", "depositPrecision": "6", "contractAddress": "******************************************", "needTagOrMemo": "false"}, {"name": "USDT", "network": "TRC20", "isDefault": false, "minConfirm": "2", "withdrawEnable": true, "depositEnable": true, "withdrawFee": "1", "withdrawMax": "10000000", "withdrawMin": "8", "depositMin": "1", "withdrawPrecision": "6", "depositPrecision": "6", "contractAddress": "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t", "needTagOrMemo": "false"}, {"name": "USDT", "network": "BEP20", "isDefault": false, "minConfirm": "15", "withdrawEnable": true, "depositEnable": true, "withdrawFee": "0", "withdrawMax": "10000000", "withdrawMin": "3", "depositMin": "0.8", "withdrawPrecision": "8", "depositPrecision": "18", "contractAddress": "0x55d398326f99059ff775485246999027b3197955", "needTagOrMemo": "false"}, {"name": "USDT", "network": "SOL", "isDefault": false, "minConfirm": "50", "withdrawEnable": true, "depositEnable": true, "withdrawFee": "1", "withdrawMax": "10000000", "withdrawMin": "1.00000000000000000001", "depositMin": "0.9", "withdrawPrecision": "6", "depositPrecision": "6", "contractAddress": "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB", "needTagOrMemo": "false"}, {"name": "USDT", "network": "POLYGON", "isDefault": false, "minConfirm": "64", "withdrawEnable": true, "depositEnable": true, "withdrawFee": "0.8", "withdrawMax": "10000000", "withdrawMin": "10", "depositMin": "0.9", "withdrawPrecision": "6", "depositPrecision": "6", "contractAddress": "0xc2132d05d31c914a87c6611c10748aeb04b58e8f", "needTagOrMemo": "false"}, {"name": "USDT", "network": "ARBITRUM", "isDefault": false, "minConfirm": "30", "withdrawEnable": true, "depositEnable": true, "withdrawFee": "0.5", "withdrawMax": "10000000", "withdrawMin": "0.5", "depositMin": "5", "withdrawPrecision": "6", "depositPrecision": "6", "contractAddress": "0xfd086bc7cd5c481dcc9c85ebe478a1c0b69fcbb9", "needTagOrMemo": "false"}, {"name": "USDT", "network": "OPTIMISM", "isDefault": false, "minConfirm": "50", "withdrawEnable": false, "depositEnable": true, "withdrawFee": "1", "withdrawMax": "10000000", "withdrawMin": "1.1", "depositMin": "5", "withdrawPrecision": "6", "depositPrecision": "6", "contractAddress": "******************************************", "needTagOrMemo": "false"}, {"name": "USDT", "network": "TON", "isDefault": false, "minConfirm": "25", "withdrawEnable": true, "depositEnable": true, "withdrawFee": "0.15", "withdrawMax": "10000000", "withdrawMin": "5", "depositMin": "5", "withdrawPrecision": "6", "depositPrecision": "6", "contractAddress": "EQCxE6mUtQJKFnGfaROTKOt1lZbDiiX1kCixRv7Nw2Id_sDs", "needTagOrMemo": "false"}]}, "id": "USDT", "numericId": null, "code": "USDT", "precision": 1e-06, "type": "crypto", "name": "USDT", "active": true, "deposit": true, "withdraw": true, "fee": 0, "fees": {}, "networks": {"ERC20": {"info": {"name": "USDT", "network": "ERC20", "isDefault": false, "minConfirm": "12", "withdrawEnable": true, "depositEnable": true, "withdrawFee": "7", "withdrawMax": "10000000", "withdrawMin": "5.5", "depositMin": "1", "withdrawPrecision": "6", "depositPrecision": "6", "contractAddress": "******************************************", "needTagOrMemo": "false"}, "id": "ERC20", "network": "ERC20", "fee": 7, "active": true, "deposit": true, "withdraw": true, "precision": 1e-06, "limits": {"withdraw": {"min": 5.5, "max": 10000000}}}, "TRC20": {"info": {"name": "USDT", "network": "TRC20", "isDefault": false, "minConfirm": "2", "withdrawEnable": true, "depositEnable": true, "withdrawFee": "1", "withdrawMax": "10000000", "withdrawMin": "8", "depositMin": "1", "withdrawPrecision": "6", "depositPrecision": "6", "contractAddress": "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t", "needTagOrMemo": "false"}, "id": "TRC20", "network": "TRC20", "fee": 1, "active": true, "deposit": true, "withdraw": true, "precision": 1e-06, "limits": {"withdraw": {"min": 8, "max": 10000000}}}, "BEP20": {"info": {"name": "USDT", "network": "BEP20", "isDefault": false, "minConfirm": "15", "withdrawEnable": true, "depositEnable": true, "withdrawFee": "0", "withdrawMax": "10000000", "withdrawMin": "3", "depositMin": "0.8", "withdrawPrecision": "8", "depositPrecision": "18", "contractAddress": "0x55d398326f99059ff775485246999027b3197955", "needTagOrMemo": "false"}, "id": "BEP20", "network": "BEP20", "fee": 0, "active": true, "deposit": true, "withdraw": true, "precision": 1e-08, "limits": {"withdraw": {"min": 3, "max": 10000000}}}, "SOL": {"info": {"name": "USDT", "network": "SOL", "isDefault": false, "minConfirm": "50", "withdrawEnable": true, "depositEnable": true, "withdrawFee": "1", "withdrawMax": "10000000", "withdrawMin": "1.00000000000000000001", "depositMin": "0.9", "withdrawPrecision": "6", "depositPrecision": "6", "contractAddress": "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB", "needTagOrMemo": "false"}, "id": "SOL", "network": "SOL", "fee": 1, "active": true, "deposit": true, "withdraw": true, "precision": 1e-06, "limits": {"withdraw": {"min": 1, "max": 10000000}}}, "MATIC": {"info": {"name": "USDT", "network": "POLYGON", "isDefault": false, "minConfirm": "64", "withdrawEnable": true, "depositEnable": true, "withdrawFee": "0.8", "withdrawMax": "10000000", "withdrawMin": "10", "depositMin": "0.9", "withdrawPrecision": "6", "depositPrecision": "6", "contractAddress": "0xc2132d05d31c914a87c6611c10748aeb04b58e8f", "needTagOrMemo": "false"}, "id": "POLYGON", "network": "MATIC", "fee": 0.8, "active": true, "deposit": true, "withdraw": true, "precision": 1e-06, "limits": {"withdraw": {"min": 10, "max": 10000000}}}, "ARB": {"info": {"name": "USDT", "network": "ARBITRUM", "isDefault": false, "minConfirm": "30", "withdrawEnable": true, "depositEnable": true, "withdrawFee": "0.5", "withdrawMax": "10000000", "withdrawMin": "0.5", "depositMin": "5", "withdrawPrecision": "6", "depositPrecision": "6", "contractAddress": "0xfd086bc7cd5c481dcc9c85ebe478a1c0b69fcbb9", "needTagOrMemo": "false"}, "id": "ARBITRUM", "network": "ARB", "fee": 0.5, "active": true, "deposit": true, "withdraw": true, "precision": 1e-06, "limits": {"withdraw": {"min": 0.5, "max": 10000000}}}, "OPTIMISM": {"info": {"name": "USDT", "network": "OPTIMISM", "isDefault": false, "minConfirm": "50", "withdrawEnable": false, "depositEnable": true, "withdrawFee": "1", "withdrawMax": "10000000", "withdrawMin": "1.1", "depositMin": "5", "withdrawPrecision": "6", "depositPrecision": "6", "contractAddress": "******************************************", "needTagOrMemo": "false"}, "id": "OPTIMISM", "network": "OPTIMISM", "fee": 1, "active": false, "deposit": true, "withdraw": false, "precision": 1e-06, "limits": {"withdraw": {"min": 1.1, "max": 10000000}}}, "TON": {"info": {"name": "USDT", "network": "TON", "isDefault": false, "minConfirm": "25", "withdrawEnable": true, "depositEnable": true, "withdrawFee": "0.15", "withdrawMax": "10000000", "withdrawMin": "5", "depositMin": "5", "withdrawPrecision": "6", "depositPrecision": "6", "contractAddress": "EQCxE6mUtQJKFnGfaROTKOt1lZbDiiX1kCixRv7Nw2Id_sDs", "needTagOrMemo": "false"}, "id": "TON", "network": "TON", "fee": 0.15, "active": true, "deposit": true, "withdraw": true, "precision": 1e-06, "limits": {"withdraw": {"min": 5, "max": 10000000}}}}, "limits": {"deposit": {"min": null, "max": null}, "withdraw": {"min": 0.5, "max": 10000000}}}}}], "cancelOrder": [{"description": "spot cancel order", "method": "cancelOrder", "input": ["1735116010161176576", "LTC/USDT"], "httpResponse": {"code": "0", "msg": "", "debugMsg": "", "data": {"symbol": "LTC-USDT", "orderId": "1735116010161176576", "price": "30", "origQty": "0.17", "executedQty": "0", "cummulativeQuoteQty": "0", "status": "CANCELED", "type": "LIMIT", "side": "BUY"}}, "parsedResponse": {"info": {"symbol": "LTC-USDT", "orderId": "1735116010161176576", "price": "30", "origQty": "0.17", "executedQty": "0", "cummulativeQuoteQty": "0", "status": "CANCELED", "type": "LIMIT", "side": "BUY"}, "id": "1735116010161176576", "clientOrderId": null, "timestamp": null, "datetime": null, "lastTradeTimestamp": null, "lastUpdateTimestamp": null, "symbol": "LTC/USDT", "type": "limit", "timeInForce": null, "postOnly": null, "side": "buy", "price": 30, "stopPrice": null, "triggerPrice": null, "stopLossPrice": null, "takeProfitPrice": null, "average": null, "cost": 0, "amount": 0.17, "filled": 0, "remaining": 0.17, "status": "canceled", "fee": {"currency": "LTC", "cost": null}, "trades": [], "fees": [{"currency": "LTC", "cost": null}], "reduceOnly": null}}, {"description": "twap cancel order", "method": "cancelOrder", "input": ["5596903086063901779", null, {"twap": true}], "httpResponse": {"code": 0, "msg": "", "timestamp": 1702731661854, "data": {"symbol": "BNB-USDT", "side": "BUY", "positionSide": "LONG", "priceType": "constant", "priceVariance": "2000", "triggerPrice": "68000", "interval": 8, "amountPerOrder": "0.111", "totalAmount": "0.511", "orderStatus": "Running", "executedQty": "0.1", "duration": 800, "maxDuration": 9000, "createdTime": 1702731661854, "updateTime": 1702731661854}}, "parsedResponse": {"info": {"symbol": "BNB-USDT", "side": "BUY", "positionSide": "LONG", "priceType": "constant", "priceVariance": "2000", "triggerPrice": "68000", "interval": 8, "amountPerOrder": "0.111", "totalAmount": "0.511", "orderStatus": "Running", "executedQty": "0.1", "duration": 800, "maxDuration": 9000, "createdTime": 1702731661854, "updateTime": 1702731661854}, "id": null, "clientOrderId": null, "symbol": "BNB/USDT", "timestamp": 1702731661854, "datetime": "2023-12-16T13:01:01.854Z", "lastTradeTimestamp": 1702731661854, "lastUpdateTimestamp": 1702731661854, "type": null, "timeInForce": null, "postOnly": null, "side": "buy", "price": null, "stopPrice": null, "triggerPrice": null, "stopLossPrice": null, "takeProfitPrice": null, "average": null, "cost": null, "amount": 0.511, "filled": 0.1, "remaining": 0.411, "status": "open", "fee": {"currency": null, "cost": null}, "trades": [], "reduceOnly": null, "fees": [{"currency": null, "cost": null}]}}], "closeAllPositions": [{"description": "Fill this with a description of the method call", "method": "closeAllPositions", "input": [], "httpResponse": {"code": "0", "msg": "", "data": {"success": [1735124421028573200], "failed": null}}, "parsedResponse": [{"info": {"positionId": 1735124421028573200}, "id": "1735124421028573200", "symbol": "", "notional": null, "marginMode": null, "liquidationPrice": null, "entryPrice": null, "unrealizedPnl": null, "realizedPnl": null, "percentage": null, "contracts": null, "contractSize": null, "markPrice": null, "lastPrice": null, "side": null, "hedged": null, "timestamp": null, "datetime": null, "lastUpdateTimestamp": null, "maintenanceMargin": null, "maintenanceMarginPercentage": null, "collateral": null, "initialMargin": null, "initialMarginPercentage": null, "leverage": null, "marginRatio": null, "stopLossPrice": null, "takeProfitPrice": null}]}], "createOrder": [{"description": "spot create order limit buy", "method": "createOrder", "input": ["LTC/USDT", "limit", "buy", 0.17, 30], "httpResponse": {"code": "0", "msg": "", "debugMsg": "", "data": {"symbol": "LTC-USDT", "orderId": "1735115105391083520", "transactTime": "1702518652662", "price": "30", "origQty": "0.17", "executedQty": "0", "cummulativeQuoteQty": "0", "status": "PENDING", "type": "LIMIT", "side": "BUY"}}, "parsedResponse": {"info": {"symbol": "LTC-USDT", "orderId": "1735115105391083520", "transactTime": "1702518652662", "price": "30", "origQty": "0.17", "executedQty": "0", "cummulativeQuoteQty": "0", "status": "PENDING", "type": "LIMIT", "side": "BUY"}, "id": "1735115105391083520", "clientOrderId": null, "timestamp": 1702518652662, "datetime": "2023-12-14T01:50:52.662Z", "lastTradeTimestamp": null, "lastUpdateTimestamp": null, "symbol": "LTC/USDT", "type": "limit", "timeInForce": null, "postOnly": null, "side": "buy", "price": 30, "stopPrice": null, "triggerPrice": null, "stopLossPrice": null, "takeProfitPrice": null, "average": null, "cost": 0, "amount": 0.17, "filled": 0, "remaining": 0.17, "status": "open", "fee": {"currency": "LTC", "cost": null}, "trades": [], "fees": [{"currency": "LTC", "cost": null}], "reduceOnly": null}}, {"description": "swap create order limit", "method": "createOrder", "input": ["LTC/USDT:USDT", "limit", "buy", 0.17, 30], "httpResponse": {"code": "0", "msg": "", "data": {"order": {"symbol": "LTC-USDT", "orderId": "1735119588833849344", "side": "BUY", "positionSide": "LONG", "type": "LIMIT", "clientOrderID": "", "workingType": "MARK_PRICE"}}}, "parsedResponse": {"info": {"symbol": "LTC-USDT", "orderId": "1735119588833849344", "side": "BUY", "positionSide": "LONG", "type": "LIMIT", "clientOrderID": "", "workingType": "MARK_PRICE"}, "id": "1735119588833849344", "clientOrderId": null, "timestamp": null, "datetime": null, "lastTradeTimestamp": null, "lastUpdateTimestamp": null, "symbol": "LTC/USDT:USDT", "type": "limit", "timeInForce": null, "postOnly": null, "side": "buy", "price": null, "stopPrice": null, "triggerPrice": null, "stopLossPrice": null, "takeProfitPrice": null, "average": null, "cost": null, "amount": null, "filled": null, "remaining": null, "status": null, "fee": {"currency": "USDT", "cost": null}, "trades": [], "fees": [{"currency": "USDT", "cost": null}], "reduceOnly": null}}, {"description": "Create order with tp and sl", "method": "createOrder", "disabledCS": true, "disabledGO": true, "input": ["LTC/USDT:USDT", "market", "buy", 1, null, {"takeProfit": {"stopPrice": 100}, "stopLoss": {"stopPrice": 50}}], "httpResponse": "{\"code\":0,\"msg\":\"\",\"data\":{\"order\":{\"orderId\":1743623387810590720,\"symbol\":\"LTC-USDT\",\"positionSide\":\"LONG\",\"side\":\"BUY\",\"type\":\"MARKET\",\"price\":0,\"quantity\":1,\"stopPrice\":0,\"workingType\":\"MARK_PRICE\",\"clientOrderID\":\"\",\"timeInForce\":\"GTC\",\"priceRate\":0,\"stopLoss\":\"{\\\"stopPrice\\\":50,\\\"workingType\\\":\\\"MARK_PRICE\\\",\\\"type\\\":\\\"STOP_MARKET\\\",\\\"quantity\\\":1}\",\"takeProfit\":\"{\\\"stopPrice\\\":100,\\\"workingType\\\":\\\"MARK_PRICE\\\",\\\"type\\\":\\\"TAKE_PROFIT_MARKET\\\",\\\"quantity\\\":1}\",\"reduceOnly\":false}}}", "parsedResponse": {"info": {"orderId": "1743623387810590720", "symbol": "LTC-USDT", "positionSide": "LONG", "side": "BUY", "type": "MARKET", "price": "0", "quantity": "1", "stopPrice": "0", "workingType": "MARK_PRICE", "clientOrderID": "", "timeInForce": "GTC", "priceRate": "0", "stopLoss": {"stopPrice": "50", "workingType": "MARK_PRICE", "type": "STOP_MARKET", "quantity": "1"}, "takeProfit": {"stopPrice": "100", "workingType": "MARK_PRICE", "type": "TAKE_PROFIT_MARKET", "quantity": "1"}, "reduceOnly": false}, "id": "1743623387810590720", "clientOrderId": null, "timestamp": null, "datetime": null, "lastTradeTimestamp": null, "lastUpdateTimestamp": null, "symbol": "LTC/USDT:USDT", "type": "market", "timeInForce": "GTC", "postOnly": null, "side": "buy", "price": null, "stopPrice": 0, "triggerPrice": 0, "stopLossPrice": 50, "takeProfitPrice": 100, "average": null, "cost": null, "amount": 1, "filled": null, "remaining": null, "status": null, "fee": {"currency": "USDT", "cost": null}, "trades": [], "fees": [{"currency": "USDT", "cost": null}], "reduceOnly": null}}, {"description": "assert #20991 fix", "method": "createOrder", "input": ["LTC/USDT:USDT", "market", "buy", 0.1, null, {"positionSide": "LONG", "reduceOnly": true}], "httpResponse": {"code": "0", "msg": "", "data": {"order": {"orderId": "1751667285204103168", "symbol": "LTC-USDT", "positionSide": "LONG", "side": "BUY", "type": "MARKET", "price": "0", "quantity": "0.1", "stopPrice": "0", "workingType": "MARK_PRICE", "clientOrderID": "", "timeInForce": "GTC", "priceRate": "0", "stopLoss": "", "takeProfit": "", "reduceOnly": false}}}, "parsedResponse": {"info": {"orderId": "1751667285204103168", "symbol": "LTC-USDT", "positionSide": "LONG", "side": "BUY", "type": "MARKET", "price": "0", "quantity": "0.1", "stopPrice": "0", "workingType": "MARK_PRICE", "clientOrderID": "", "timeInForce": "GTC", "priceRate": "0", "stopLoss": "", "takeProfit": "", "reduceOnly": false}, "id": "1751667285204103168", "clientOrderId": null, "timestamp": null, "datetime": null, "lastTradeTimestamp": null, "lastUpdateTimestamp": null, "symbol": "LTC/USDT:USDT", "type": "market", "timeInForce": "GTC", "postOnly": null, "side": "buy", "price": null, "stopPrice": 0, "triggerPrice": 0, "stopLossPrice": null, "takeProfitPrice": null, "average": null, "cost": null, "amount": 0.1, "filled": null, "remaining": null, "status": null, "fee": {"currency": "USDT", "cost": null}, "trades": [], "fees": [{"currency": "USDT", "cost": null}], "reduceOnly": null}}, {"description": "create takeProfitOrder", "method": "createOrder", "input": ["LTC/USDT:USDT", "market", "sell", 0.1, null, {"takeProfitPrice": 120, "positionSide": "BOTH"}], "httpResponse": {"code": "0", "msg": "", "data": {"order": {"orderId": "1792485525883039744", "symbol": "LTC-USDT", "positionSide": "BOTH", "side": "SELL", "type": "TAKE_PROFIT_MARKET", "price": "0", "quantity": "0.1", "stopPrice": "120", "workingType": "MARK_PRICE", "clientOrderID": "", "timeInForce": "GTC", "priceRate": "0", "stopLoss": "", "takeProfit": "", "reduceOnly": false, "activationPrice": "0", "closePosition": "", "stopGuaranteed": ""}}}, "parsedResponse": {"info": {"orderId": "1792485525883039744", "symbol": "LTC-USDT", "positionSide": "BOTH", "side": "SELL", "type": "TAKE_PROFIT_MARKET", "price": "0", "quantity": "0.1", "stopPrice": "120", "workingType": "MARK_PRICE", "clientOrderID": "", "timeInForce": "GTC", "priceRate": "0", "stopLoss": "", "takeProfit": "", "reduceOnly": false, "activationPrice": "0", "closePosition": "", "stopGuaranteed": ""}, "id": "1792485525883039744", "clientOrderId": null, "symbol": "LTC/USDT:USDT", "timestamp": null, "datetime": null, "lastTradeTimestamp": null, "lastUpdateTimestamp": null, "type": "market", "timeInForce": "GTC", "postOnly": false, "side": "sell", "price": null, "stopPrice": null, "triggerPrice": null, "stopLossPrice": null, "takeProfitPrice": 120, "average": null, "cost": null, "amount": 0.1, "filled": null, "remaining": null, "status": null, "fee": {"currency": "USDT", "cost": null}, "trades": [], "reduceOnly": false, "fees": [{"currency": "USDT", "cost": null}]}}, {"description": "create trigger order", "method": "createOrder", "input": ["LTC/USDT:USDT", "market", "buy", 0.1, null, {"positionSide": "BOTH", "triggerPrice": 100}], "httpResponse": {"code": "0", "msg": "", "data": {"order": {"orderId": "1792485928376840192", "symbol": "LTC-USDT", "positionSide": "BOTH", "side": "BUY", "type": "TRIGGER_MARKET", "price": "0", "quantity": "0.1", "stopPrice": "100", "workingType": "MARK_PRICE", "clientOrderID": "", "timeInForce": "GTC", "priceRate": "0", "stopLoss": "", "takeProfit": "", "reduceOnly": false, "activationPrice": "0", "closePosition": "", "stopGuaranteed": ""}}}, "parsedResponse": {"info": {"orderId": "1792485928376840192", "symbol": "LTC-USDT", "positionSide": "BOTH", "side": "BUY", "type": "TRIGGER_MARKET", "price": "0", "quantity": "0.1", "stopPrice": "100", "workingType": "MARK_PRICE", "clientOrderID": "", "timeInForce": "GTC", "priceRate": "0", "stopLoss": "", "takeProfit": "", "reduceOnly": false, "activationPrice": "0", "closePosition": "", "stopGuaranteed": ""}, "id": "1792485928376840192", "clientOrderId": null, "symbol": "LTC/USDT:USDT", "timestamp": null, "datetime": null, "lastTradeTimestamp": null, "lastUpdateTimestamp": null, "type": "market", "timeInForce": "GTC", "postOnly": false, "side": "buy", "price": null, "stopPrice": 100, "triggerPrice": 100, "stopLossPrice": null, "takeProfitPrice": null, "average": null, "cost": null, "amount": 0.1, "filled": null, "remaining": null, "status": null, "fee": {"currency": "USDT", "cost": null}, "trades": [], "reduceOnly": false, "fees": [{"currency": "USDT", "cost": null}]}}, {"description": "swap create TWAP order", "method": "createOrder", "input": ["LTC/USDT:USDT", "twap", "buy", 1, null, {"priceType": "constant", "priceVariance": "10", "triggerPrice": "120", "interval": 8, "amountPerOrder": "0.5"}], "httpResponse": {"code": 0, "msg": "", "timestamp": 1732693774386, "data": {"mainOrderId": "4633860139993029715"}}, "parsedResponse": {"info": {"mainOrderId": "4633860139993029715"}, "id": "4633860139993029715", "clientOrderId": null, "symbol": "LTC/USDT:USDT", "timestamp": null, "datetime": null, "lastTradeTimestamp": null, "lastUpdateTimestamp": null, "type": null, "timeInForce": null, "postOnly": null, "side": null, "price": null, "stopPrice": null, "triggerPrice": null, "stopLossPrice": null, "takeProfitPrice": null, "average": null, "cost": null, "amount": null, "filled": null, "remaining": null, "status": null, "fee": {"currency": "USDT", "cost": null}, "trades": [], "reduceOnly": null, "fees": [{"currency": "USDT", "cost": null}]}}], "fetchClosedOrders": [{"description": "Spot closed order", "method": "fetchClosedOrders", "input": ["LTC/USDT", null, 1], "httpResponse": {"code": "0", "msg": "", "debugMsg": "", "data": {"orders": [{"symbol": "LTC-USDT", "orderId": "1721596796393750528", "price": "73.56", "StopPrice": "0", "origQty": "0", "executedQty": "0.0679", "cummulativeQuoteQty": "4.9954709", "status": "FILLED", "type": "MARKET", "side": "BUY", "time": "1699295637000", "updateTime": "1699295637000", "origQuoteOrderQty": "5", "fee": "-0.0000679"}]}}, "parsedResponse": [{"info": {"symbol": "LTC-USDT", "orderId": "1721596796393750528", "price": "73.56", "StopPrice": "0", "origQty": "0", "executedQty": "0.0679", "cummulativeQuoteQty": "4.9954709", "status": "FILLED", "type": "MARKET", "side": "BUY", "time": "1699295637000", "updateTime": "1699295637000", "origQuoteOrderQty": "5", "fee": "-0.0000679"}, "id": "1721596796393750528", "clientOrderId": null, "timestamp": 1699295637000, "datetime": "2023-11-06T18:33:57.000Z", "lastTradeTimestamp": 1699295637000, "lastUpdateTimestamp": 1699295637000, "symbol": "LTC/USDT", "type": "market", "timeInForce": "IOC", "postOnly": null, "side": "buy", "price": 73.56, "stopPrice": null, "triggerPrice": null, "stopLossPrice": null, "takeProfitPrice": null, "average": 73.571, "cost": 4.9954709, "amount": 0.0679, "filled": 0.0679, "remaining": 0, "status": "closed", "fee": {"currency": "LTC", "cost": "0.0000679"}, "trades": [], "fees": [{"currency": "LTC", "cost": 6.79e-05}], "reduceOnly": null}]}, {"description": "Swap closed order", "method": "fetchClosedOrders", "input": ["LTC/USDT:USDT", null, 1], "httpResponse": {"code": "0", "msg": "", "data": {"orders": [{"symbol": "LTC-USDT", "orderId": "1729799444544843776", "side": "BUY", "positionSide": "SHORT", "type": "MARKET", "origQty": "0.1", "price": "70.17", "executedQty": "0.1", "avgPrice": "70.17", "cumQuote": "7", "stopPrice": "", "profit": "0.0000", "commission": "-0.003509", "status": "FILLED", "time": "1701251300000", "updateTime": "1701251300000", "clientOrderId": "", "leverage": "", "takeProfit": {"type": "", "quantity": "0", "stopPrice": "0", "price": "0", "workingType": ""}, "stopLoss": {"type": "", "quantity": "0", "stopPrice": "0", "price": "0", "workingType": ""}, "advanceAttr": "0", "positionID": "0", "takeProfitEntrustPrice": "0", "stopLossEntrustPrice": "0", "orderType": "", "workingType": "MARK_PRICE"}]}}, "parsedResponse": [{"info": {"symbol": "LTC-USDT", "orderId": "1729799444544843776", "side": "BUY", "positionSide": "SHORT", "type": "MARKET", "origQty": "0.1", "price": "70.17", "executedQty": "0.1", "avgPrice": "70.17", "cumQuote": "7", "stopPrice": "", "profit": "0.0000", "commission": "-0.003509", "status": "FILLED", "time": "1701251300000", "updateTime": "1701251300000", "clientOrderId": "", "leverage": "", "takeProfit": {"type": "", "quantity": "0", "stopPrice": "0", "price": "0", "workingType": ""}, "stopLoss": {"type": "", "quantity": "0", "stopPrice": "0", "price": "0", "workingType": ""}, "advanceAttr": "0", "positionID": "0", "takeProfitEntrustPrice": "0", "stopLossEntrustPrice": "0", "orderType": "", "workingType": "MARK_PRICE"}, "id": "1729799444544843776", "clientOrderId": null, "timestamp": 1701251300000, "datetime": "2023-11-29T09:48:20.000Z", "lastTradeTimestamp": 1701251300000, "lastUpdateTimestamp": 1701251300000, "symbol": "LTC/USDT:USDT", "type": "market", "timeInForce": "IOC", "postOnly": null, "side": "buy", "price": 70.17, "stopPrice": null, "triggerPrice": null, "stopLossPrice": null, "takeProfitPrice": null, "average": 70.17, "cost": 7.017, "amount": 0.1, "filled": 0.1, "remaining": 0, "status": "closed", "fee": {"currency": "USDT", "cost": "0.003509"}, "trades": [], "fees": [{"currency": "USDT", "cost": 0.003509}], "reduceOnly": null}]}, {"description": "twap closed order", "method": "fetchClosedOrders", "input": ["LTC/USDT:USDT", null, null, {"twap": true}], "httpResponse": {"code": 0, "msg": "success", "timestamp": 1732697991924, "data": {"list": [{"symbol": "LTC-USDT", "mainOrderId": "774133392271760467", "side": "BUY", "positionSide": "LONG", "priceType": "constant", "priceVariance": "10.00", "triggerPrice": "120.00", "interval": 8, "amountPerOrder": "0.5", "totalAmount": "1.0", "orderStatus": "Filled", "executedQty": "1.0", "duration": 16, "maxDuration": 86400, "createdTime": 1732695030000, "updateTime": 1732695046000}]}}, "parsedResponse": [{"info": {"symbol": "LTC-USDT", "mainOrderId": "774133392271760467", "side": "BUY", "positionSide": "LONG", "priceType": "constant", "priceVariance": "10.00", "triggerPrice": "120.00", "interval": 8, "amountPerOrder": "0.5", "totalAmount": "1.0", "orderStatus": "Filled", "executedQty": "1.0", "duration": 16, "maxDuration": 86400, "createdTime": 1732695030000, "updateTime": 1732695046000}, "id": "774133392271760467", "clientOrderId": null, "symbol": "LTC/USDT:USDT", "timestamp": 1732695030000, "datetime": "2024-11-27T08:10:30.000Z", "lastTradeTimestamp": 1732695046000, "lastUpdateTimestamp": 1732695046000, "type": null, "timeInForce": null, "postOnly": null, "side": "buy", "price": null, "stopPrice": null, "triggerPrice": null, "stopLossPrice": null, "takeProfitPrice": null, "average": null, "cost": null, "amount": 1, "filled": 1, "remaining": 0, "status": "closed", "fee": {"currency": "USDT", "cost": null}, "trades": [], "reduceOnly": null, "fees": [{"currency": "USDT", "cost": null}]}]}], "fetchBalance": [{"description": "spot fetchBalance", "method": "fetchBalance", "input": [], "httpResponse": {"code": "0", "msg": "", "debugMsg": "", "data": {"balances": [{"asset": "USDT", "free": "54.049459260000006", "locked": "5"}, {"asset": "ADA", "free": "19.06092", "locked": "0"}, {"asset": "LTC", "free": "0.6921067", "locked": "0"}, {"asset": "VST", "free": "0", "locked": "0"}]}}, "parsedResponse": {"info": {"code": "0", "msg": "", "debugMsg": "", "data": {"balances": [{"asset": "USDT", "free": "54.049459260000006", "locked": "5"}, {"asset": "ADA", "free": "19.06092", "locked": "0"}, {"asset": "LTC", "free": "0.6921067", "locked": "0"}, {"asset": "VST", "free": "0", "locked": "0"}]}}, "USDT": {"free": 54.049459260000006, "used": 5, "total": 59.049459260000006}, "ADA": {"free": 19.06092, "used": 0, "total": 19.06092}, "LTC": {"free": 0.6921067, "used": 0, "total": 0.6921067}, "VST": {"free": 0, "used": 0, "total": 0}, "free": {"USDT": 54.049459260000006, "ADA": 19.06092, "LTC": 0.6921067, "VST": 0}, "used": {"USDT": 5, "ADA": 0, "LTC": 0, "VST": 0}, "total": {"USDT": 59.049459260000006, "ADA": 19.06092, "LTC": 0.6921067, "VST": 0}}}, {"description": "swap fetch balance", "method": "fetchBalance", "input": [], "httpResponse": {"code": "0", "msg": "", "data": {"balance": {"userId": "1177064765068660742", "asset": "USDT", "balance": "44.1897", "equity": "44.1897", "unrealizedProfit": "0.0000", "realisedProfit": "0.0000", "availableMargin": "43.5882", "usedMargin": "0.0000", "freezedMargin": "0.6015"}}}, "parsedResponse": {"info": {"code": "0", "msg": "", "data": {"balance": {"userId": "1177064765068660742", "asset": "USDT", "balance": "44.1897", "equity": "44.1897", "unrealizedProfit": "0.0000", "realisedProfit": "0.0000", "availableMargin": "43.5882", "usedMargin": "0.0000", "freezedMargin": "0.6015"}}}, "USDT": {"free": 43.5882, "used": 0, "total": 43.5882}, "free": {"USDT": 43.5882}, "used": {"USDT": 0}, "total": {"USDT": 43.5882}}}], "fetchMyTrades": [{"description": "Swap trade", "method": "fetchMyTrades", "input": ["LTC/USDT:USDT", null, 1], "httpResponse": {"code": "0", "msg": "", "data": {"fill_orders": [{"filledTm": "2024-01-06T21:19:45Z", "volume": "10", "price": "65.2965", "amount": "65.2965", "commission": "-0.0326", "currency": "USDT", "orderId": "1743623387810590720", "liquidatedPrice": "", "liquidatedMarginRatio": "", "filledTime": "2024-01-06T21:19:45.000+0800", "clientOrderID": "", "symbol": "LTC-USDT", "onlyOnePosition": false}]}}, "parsedResponse": [{"id": null, "info": {"filledTm": "2024-01-06T21:19:45Z", "volume": "10", "price": "65.2965", "amount": "65.2965", "commission": "-0.0326", "currency": "USDT", "orderId": "1743623387810590720", "liquidatedPrice": "", "liquidatedMarginRatio": "", "filledTime": "2024-01-06T21:19:45.000+0800", "clientOrderID": "", "symbol": "LTC-USDT", "onlyOnePosition": false}, "timestamp": 1704575985000, "datetime": "2024-01-06T21:19:45.000Z", "symbol": "LTC/USDT:USDT", "order": "1743623387810590720", "type": null, "side": null, "takerOrMaker": null, "price": 65.2965, "amount": 1, "cost": 65.2965, "fee": {"cost": 0.0326, "currency": "USDT"}, "fees": [{"cost": 0.0326, "currency": "USDT"}]}]}, {"description": "Spot trade", "method": "fetchMyTrades", "input": ["LTC/USDT", null, 1], "httpResponse": {"code": "0", "msg": "", "debugMsg": "", "data": {"fills": [{"symbol": "LTC-USDT", "id": "49884117", "orderId": "1740029773184237568", "price": "75.562", "qty": "0.1", "quoteQty": "7.5562000000000005", "commission": "-0.0001", "commissionAsset": "LTC", "time": "1703690401000", "isBuyer": true, "isMaker": false}]}}, "parsedResponse": [{"id": "49884117", "info": {"symbol": "LTC-USDT", "id": "49884117", "orderId": "1740029773184237568", "price": "75.562", "qty": "0.1", "quoteQty": "7.5562000000000005", "commission": "-0.0001", "commissionAsset": "LTC", "time": "1703690401000", "isBuyer": true, "isMaker": false}, "timestamp": 1703690401000, "datetime": "2023-12-27T15:20:01.000Z", "symbol": "LTC/USDT", "order": "1740029773184237568", "type": null, "side": "buy", "takerOrMaker": "taker", "price": 75.562, "amount": 0.1, "cost": 7.5562000000000005, "fee": {"cost": 0.0001, "currency": "LTC"}, "fees": [{"cost": 0.0001, "currency": "LTC"}]}]}], "fetchOpenOrders": [{"description": "spot open orders", "method": "fetchOpenOrders", "input": ["LTC/USDT"], "httpResponse": {"code": "0", "msg": "", "debugMsg": "", "data": {"orders": [{"symbol": "LTC-USDT", "orderId": "1752456208763060224", "price": "50", "StopPrice": "0", "origQty": "0.1", "executedQty": "0", "cummulativeQuoteQty": "0", "status": "PENDING", "type": "LIMIT", "side": "BUY", "time": "1706653093888", "updateTime": "1706653093888", "origQuoteOrderQty": "0", "clientOrderID": "", "fee": "0"}]}}, "parsedResponse": [{"info": {"symbol": "LTC-USDT", "orderId": "1752456208763060224", "price": "50", "StopPrice": "0", "origQty": "0.1", "executedQty": "0", "cummulativeQuoteQty": "0", "status": "PENDING", "type": "LIMIT", "side": "BUY", "time": "1706653093888", "updateTime": "1706653093888", "origQuoteOrderQty": "0", "clientOrderID": "", "fee": "0"}, "id": "1752456208763060224", "clientOrderId": null, "timestamp": 1706653093888, "datetime": "2024-01-30T22:18:13.888Z", "lastTradeTimestamp": 1706653093888, "lastUpdateTimestamp": 1706653093888, "symbol": "LTC/USDT", "type": "limit", "timeInForce": null, "postOnly": null, "side": "buy", "price": 50, "stopPrice": null, "triggerPrice": null, "stopLossPrice": null, "takeProfitPrice": null, "average": null, "cost": 0, "amount": 0.1, "filled": 0, "remaining": 0.1, "status": "open", "fee": {"currency": "LTC", "cost": "0"}, "trades": [], "fees": [{"currency": "LTC", "cost": 0}], "reduceOnly": null}]}, {"description": "twap open orders", "method": "fetchOpenOrders", "input": ["LTC/USDT:USDT", null, null, {"twap": true}], "httpResponse": {"code": 0, "msg": "", "timestamp": 1702731661854, "data": {"list": [{"symbol": "BNB-USDT", "side": "BUY", "positionSide": "LONG", "priceType": "constant", "priceVariance": "2000", "triggerPrice": "68000", "interval": 8, "amountPerOrder": "0.111", "totalAmount": "0.511", "orderStatus": "Running", "executedQty": "0.1", "duration": 800, "maxDuration": 9000, "createdTime": 1702731661854, "updateTime": 1702731661854}], "total": 1}}, "parsedResponse": []}], "fetchDepositAddressesByNetwork": [{"description": "usdt addresses with prefix", "method": "fetchDepositAddressesByNetwork", "input": ["USDT"], "httpResponse": {"code": "0", "timestamp": "1730886872514", "data": {"data": [{"coinId": "4", "coin": "USDT", "network": "OMNI", "address": "**********************************", "addressWithPrefix": "**********************************"}, {"coinId": "760", "coin": "USDT", "network": "ERC20", "address": "6d5a760ce95c2f8fc028661f1645f89ea1s62745", "addressWithPrefix": "0x6d5a760ce95c2f8fc028661f1645f89ea1s62745"}, {"coinId": "780", "coin": "USDT", "network": "TRC20", "address": "TabPi4x2hCN6DBKqAL86vdMRD7HxzcBnRf", "addressWithPrefix": "TabPi4x2hCN6DBKqAL86vdMRD7HxzcBnRf"}, {"coinId": "799", "coin": "USDT", "network": "BEP20", "address": "6d5a760ce95c2f8fc028661f1645f89ea1s62745", "addressWithPrefix": "6d5a760ce95c2f8fc028661f1645f89ea1s62745"}, {"coinId": "803", "coin": "USDT", "network": "HECO", "address": "6d5a760ce95c2f8fc028661f1645f89ea1s62745", "addressWithPrefix": "0x6d5a760ce95c2f8fc028661f1645f89ea1s62745"}, {"coinId": "857", "coin": "USDT", "network": "SOL", "address": "Daadgs5SaodraZEWqk5M1rjV1Drzy6Tu7EcUi6W3K685", "addressWithPrefix": "Daadgs5SaodraZEWqk5M1rjV1Drzy6Tu7EcUi6W3K685"}, {"coinId": "1192", "coin": "USDT", "network": "POLYGON", "address": "6d5a760ce95c2f8fc028661f1645f89ea1s62745", "addressWithPrefix": "0x6d5a760ce95c2f8fc028661f1645f89ea1s62745"}, {"coinId": "1367", "coin": "USDT", "network": "ARBITRUM", "address": "6d5a760ce95c2f8fc028661f1645f89ea1s62745", "addressWithPrefix": "0x6d5a760ce95c2f8fc028661f1645f89ea1s62745"}, {"coinId": "1371", "coin": "USDT", "network": "OPTIMISM", "address": "6d5a760ce95c2f8fc028661f1645f89ea1s62745", "addressWithPrefix": "0x6d5a760ce95c2f8fc028661f1645f89ea1s62745"}, {"coinId": "1967", "coin": "USDT", "network": "TON", "address": "UQDwSAybmEW6LJ5Qb1adskpasas1casdHb_Zx_G3JF1r7MV57-md5gI", "addressWithPrefix": "UQDwSAybmEW6LJ5Qb1adskpasas1casdHb_Zx_G3JF1r7MV57-md5gI"}, {"coinId": "2050", "coin": "USDT", "network": "AVAX-C", "address": "6d5a760ce95c2f8fc028661f1645f89ea1s62745", "addressWithPrefix": "0x6d5a760ce95c2f8fc028661f1645f89ea1s62745"}], "total": "11"}}, "parsedResponse": {"OMNI": {"info": {"coinId": "4", "coin": "USDT", "network": "OMNI", "address": "**********************************", "addressWithPrefix": "**********************************"}, "currency": "USDT", "network": "OMNI", "address": "**********************************", "tag": null}, "ERC20": {"info": {"coinId": "760", "coin": "USDT", "network": "ERC20", "address": "6d5a760ce95c2f8fc028661f1645f89ea1s62745", "addressWithPrefix": "0x6d5a760ce95c2f8fc028661f1645f89ea1s62745"}, "currency": "USDT", "network": "ERC20", "address": "0x6d5a760ce95c2f8fc028661f1645f89ea1s62745", "tag": null}, "TRC20": {"info": {"coinId": "780", "coin": "USDT", "network": "TRC20", "address": "TabPi4x2hCN6DBKqAL86vdMRD7HxzcBnRf", "addressWithPrefix": "TabPi4x2hCN6DBKqAL86vdMRD7HxzcBnRf"}, "currency": "USDT", "network": "TRC20", "address": "TabPi4x2hCN6DBKqAL86vdMRD7HxzcBnRf", "tag": null}, "BEP20": {"info": {"coinId": "799", "coin": "USDT", "network": "BEP20", "address": "6d5a760ce95c2f8fc028661f1645f89ea1s62745", "addressWithPrefix": "6d5a760ce95c2f8fc028661f1645f89ea1s62745"}, "currency": "USDT", "network": "BEP20", "address": "6d5a760ce95c2f8fc028661f1645f89ea1s62745", "tag": null}, "HECO": {"info": {"coinId": "803", "coin": "USDT", "network": "HECO", "address": "6d5a760ce95c2f8fc028661f1645f89ea1s62745", "addressWithPrefix": "0x6d5a760ce95c2f8fc028661f1645f89ea1s62745"}, "currency": "USDT", "network": "HECO", "address": "0x6d5a760ce95c2f8fc028661f1645f89ea1s62745", "tag": null}, "SOL": {"info": {"coinId": "857", "coin": "USDT", "network": "SOL", "address": "Daadgs5SaodraZEWqk5M1rjV1Drzy6Tu7EcUi6W3K685", "addressWithPrefix": "Daadgs5SaodraZEWqk5M1rjV1Drzy6Tu7EcUi6W3K685"}, "currency": "USDT", "network": "SOL", "address": "Daadgs5SaodraZEWqk5M1rjV1Drzy6Tu7EcUi6W3K685", "tag": null}, "MATIC": {"info": {"coinId": "1192", "coin": "USDT", "network": "POLYGON", "address": "6d5a760ce95c2f8fc028661f1645f89ea1s62745", "addressWithPrefix": "0x6d5a760ce95c2f8fc028661f1645f89ea1s62745"}, "currency": "USDT", "network": "MATIC", "address": "0x6d5a760ce95c2f8fc028661f1645f89ea1s62745", "tag": null}, "ARB": {"info": {"coinId": "1367", "coin": "USDT", "network": "ARBITRUM", "address": "6d5a760ce95c2f8fc028661f1645f89ea1s62745", "addressWithPrefix": "0x6d5a760ce95c2f8fc028661f1645f89ea1s62745"}, "currency": "USDT", "network": "ARB", "address": "0x6d5a760ce95c2f8fc028661f1645f89ea1s62745", "tag": null}, "OPTIMISM": {"info": {"coinId": "1371", "coin": "USDT", "network": "OPTIMISM", "address": "6d5a760ce95c2f8fc028661f1645f89ea1s62745", "addressWithPrefix": "0x6d5a760ce95c2f8fc028661f1645f89ea1s62745"}, "currency": "USDT", "network": "OPTIMISM", "address": "0x6d5a760ce95c2f8fc028661f1645f89ea1s62745", "tag": null}, "TON": {"info": {"coinId": "1967", "coin": "USDT", "network": "TON", "address": "UQDwSAybmEW6LJ5Qb1adskpasas1casdHb_Zx_G3JF1r7MV57-md5gI", "addressWithPrefix": "UQDwSAybmEW6LJ5Qb1adskpasas1casdHb_Zx_G3JF1r7MV57-md5gI"}, "currency": "USDT", "network": "TON", "address": "UQDwSAybmEW6LJ5Qb1adskpasas1casdHb_Zx_G3JF1r7MV57-md5gI", "tag": null}, "AVAX-C": {"info": {"coinId": "2050", "coin": "USDT", "network": "AVAX-C", "address": "6d5a760ce95c2f8fc028661f1645f89ea1s62745", "addressWithPrefix": "0x6d5a760ce95c2f8fc028661f1645f89ea1s62745"}, "currency": "USDT", "network": "AVAX-C", "address": "0x6d5a760ce95c2f8fc028661f1645f89ea1s62745", "tag": null}}}], "fetchTrades": [{"description": "public spot trades", "method": "fetchTrades", "input": ["BTC/USDT", null, 1], "httpResponse": {"code": "0", "timestamp": "1710327664622", "data": [{"id": "93549116", "price": "73239.12", "qty": "0.09624", "time": "1710327664531", "buyerMaker": false}]}, "parsedResponse": [{"id": "93549116", "info": {"id": "93549116", "price": "73239.12", "qty": "0.09624", "time": "1710327664531", "buyerMaker": false}, "timestamp": 1710327664531, "datetime": "2024-03-13T11:01:04.531Z", "symbol": "BTC/USDT", "order": null, "type": null, "side": "buy", "takerOrMaker": "taker", "price": 73239.12, "amount": 0.09624, "cost": 7048.5329088, "fee": {"cost": null, "currency": null}, "fees": []}]}], "fetchTicker": [{"description": "public spot ticker", "method": "fetchTicker", "input": ["BTC/USDT"], "httpResponse": {"code": "0", "timestamp": "1710328247428", "data": [{"symbol": "BTC-USDT", "openPrice": "72147.31", "highPrice": "73635.24", "lowPrice": "68676.72", "lastPrice": "73372.83", "priceChange": "1225.52", "priceChangePercent": "1.70%", "volume": "36378.57", "quoteVolume": "2589539809.04", "openTime": "1710241847428", "closeTime": "1710328247428", "askPrice": "73372.81", "askQty": "1.32749", "bidPrice": "73371.33", "bidQty": "5.93812"}]}, "parsedResponse": {"symbol": "BTC/USDT", "timestamp": 1710328247428, "datetime": "2024-03-13T11:10:47.428Z", "high": 73635.24, "low": 68676.72, "bid": 73371.33, "bidVolume": 5.93812, "ask": 73372.81, "askVolume": 1.32749, "vwap": 71183.11162423372, "open": 72147.31, "close": 73372.83, "last": 73372.83, "previousClose": null, "change": 1225.52, "percentage": 1.7, "average": 72760.07, "baseVolume": 36378.57, "quoteVolume": 2589539809.04, "markPrice": null, "indexPrice": null, "info": {"symbol": "BTC-USDT", "openPrice": "72147.31", "highPrice": "73635.24", "lowPrice": "68676.72", "lastPrice": "73372.83", "priceChange": "1225.52", "priceChangePercent": "1.70%", "volume": "36378.57", "quoteVolume": "2589539809.04", "openTime": "1710241847428", "closeTime": "1710328247428", "askPrice": "73372.81", "askQty": "1.32749", "bidPrice": "73371.33", "bidQty": "5.93812"}}}, {"description": "swap ticker", "method": "fetchTicker", "input": ["BTC/USDT:USDT"], "httpResponse": {"code": "0", "msg": "", "data": {"symbol": "BTC-USDT", "priceChange": "-392.1", "priceChangePercent": "-0.56", "lastPrice": "69938.9", "lastQty": "0.3018", "highPrice": "71619.4", "lowPrice": "69320.3", "volume": "74183.8679", "quoteVolume": "5237383426.37", "openPrice": "70331.0", "openTime": "1711491305590", "closeTime": "1711491283474", "askPrice": "69943.0", "askQty": "1.5921", "bidPrice": "69942.2", "bidQty": "1.3346"}}, "parsedResponse": {"symbol": "BTC/USDT:USDT", "timestamp": 1711491283474, "datetime": "2024-03-26T22:14:43.474Z", "high": 71619.4, "low": 69320.3, "bid": 69942.2, "bidVolume": 1.3346, "ask": 69943, "askVolume": 1.5921, "vwap": 70600.03171349873, "open": 70331, "close": 69938.9, "last": 69938.9, "previousClose": null, "change": -392.1, "percentage": -0.56, "average": 70134.9, "baseVolume": 74183.8679, "quoteVolume": 5237383426.37, "markPrice": null, "indexPrice": null, "info": {"symbol": "BTC-USDT", "priceChange": "-392.1", "priceChangePercent": "-0.56", "lastPrice": "69938.9", "lastQty": "0.3018", "highPrice": "71619.4", "lowPrice": "69320.3", "volume": "74183.8679", "quoteVolume": "5237383426.37", "openPrice": "70331.0", "openTime": "1711491305590", "closeTime": "1711491283474", "askPrice": "69943.0", "askQty": "1.5921", "bidPrice": "69942.2", "bidQty": "1.3346"}}}], "fetchOHLCV": [{"description": "public spot ohlcv", "method": "fetchOHLCV", "input": ["BTC/USDT", "1h", null, 1], "httpResponse": {"code": "0", "timestamp": "1710328423913", "data": [[1710327600000, 73257.7, 73422.69, 73237.66, 73413.77, 85.04, 1710331199999, 6235730.31]]}, "parsedResponse": [[1710327600000, 73257.7, 73422.69, 73237.66, 73413.77, 85.04]]}], "fetchOrder": [{"description": "fetch swap order", "method": "fetchOrder", "input": ["1770683446830788608", "LTC/USDT:USDT"], "httpResponse": {"code": "0", "msg": "", "data": {"order": {"symbol": "LTC-USDT", "orderId": "1770683446830788608", "side": "SELL", "positionSide": "LONG", "type": "MARKET", "origQty": "0.1", "price": "83.69", "executedQty": "0.1", "avgPrice": "83.71", "cumQuote": "8", "stopPrice": "", "profit": "0.0000", "commission": "-0.004186", "status": "FILLED", "time": "1710998806000", "updateTime": "1710998806000", "clientOrderId": "b39b0316-1f99-4099-8f4b-bac0f46731f8", "leverage": "5X", "takeProfit": {"type": "", "quantity": "0", "stopPrice": "0", "price": "0", "workingType": ""}, "stopLoss": {"type": "", "quantity": "0", "stopPrice": "0", "price": "0", "workingType": ""}, "advanceAttr": "0", "positionID": "0", "takeProfitEntrustPrice": "0", "stopLossEntrustPrice": "0", "orderType": "", "workingType": "MARK_PRICE", "onlyOnePosition": false, "reduceOnly": true, "stopGuaranteed": "false", "triggerOrderId": "0"}}}, "parsedResponse": {"info": {"symbol": "LTC-USDT", "orderId": "1770683446830788608", "side": "SELL", "positionSide": "LONG", "type": "MARKET", "origQty": "0.1", "price": "83.69", "executedQty": "0.1", "avgPrice": "83.71", "cumQuote": "8", "stopPrice": "", "profit": "0.0000", "commission": "-0.004186", "status": "FILLED", "time": "1710998806000", "updateTime": "1710998806000", "clientOrderId": "b39b0316-1f99-4099-8f4b-bac0f46731f8", "leverage": "5X", "takeProfit": {"type": "", "quantity": "0", "stopPrice": "0", "price": "0", "workingType": ""}, "stopLoss": {"type": "", "quantity": "0", "stopPrice": "0", "price": "0", "workingType": ""}, "advanceAttr": "0", "positionID": "0", "takeProfitEntrustPrice": "0", "stopLossEntrustPrice": "0", "orderType": "", "workingType": "MARK_PRICE", "onlyOnePosition": false, "reduceOnly": true, "stopGuaranteed": "false", "triggerOrderId": "0"}, "id": "1770683446830788608", "clientOrderId": "b39b0316-1f99-4099-8f4b-bac0f46731f8", "symbol": "LTC/USDT:USDT", "timestamp": 1710998806000, "datetime": "2024-03-21T05:26:46.000Z", "lastTradeTimestamp": 1710998806000, "lastUpdateTimestamp": 1710998806000, "type": "market", "timeInForce": "IOC", "postOnly": null, "side": "sell", "price": 83.69, "stopPrice": null, "triggerPrice": null, "stopLossPrice": 0, "takeProfitPrice": 0, "average": 83.71, "cost": 8.371, "amount": 0.1, "filled": 0.1, "remaining": 0, "status": "closed", "fee": {"currency": "USDT", "cost": "0.004186"}, "trades": [], "reduceOnly": true, "fees": [{"currency": "USDT", "cost": 0.004186}]}}, {"description": "swap stopLoss order", "method": "fetchOrder", "input": ["1792481956836519936", "LTC/USDT:USDT"], "httpResponse": {"code": "0", "msg": "", "data": {"order": {"symbol": "LTC-USDT", "orderId": "1792481956836519936", "side": "SELL", "positionSide": "BOTH", "type": "STOP_MARKET", "origQty": "0.1", "price": "0", "executedQty": "0.0", "avgPrice": "0.00", "cumQuote": "", "stopPrice": "20.00", "profit": "", "commission": "", "status": "NEW", "time": "1716195976000", "updateTime": "1716195976000", "clientOrderId": "", "leverage": "6X", "takeProfit": {"type": "", "quantity": "0", "stopPrice": "0", "price": "0", "workingType": "", "stopGuaranteed": ""}, "stopLoss": {"type": "", "quantity": "0", "stopPrice": "0", "price": "0", "workingType": "", "stopGuaranteed": ""}, "advanceAttr": "0", "positionID": "0", "takeProfitEntrustPrice": "0", "stopLossEntrustPrice": "0", "orderType": "", "workingType": "MARK_PRICE", "onlyOnePosition": true, "reduceOnly": true, "postOnly": false, "stopGuaranteed": "false", "triggerOrderId": "0", "trailingStopRate": "0", "trailingStopDistance": "0"}}}, "parsedResponse": {"info": {"symbol": "LTC-USDT", "orderId": "1792481956836519936", "side": "SELL", "positionSide": "BOTH", "type": "STOP_MARKET", "origQty": "0.1", "price": "0", "executedQty": "0.0", "avgPrice": "0.00", "cumQuote": "", "stopPrice": "20.00", "profit": "", "commission": "", "status": "NEW", "time": "1716195976000", "updateTime": "1716195976000", "clientOrderId": "", "leverage": "6X", "takeProfit": {"type": "", "quantity": "0", "stopPrice": "0", "price": "0", "workingType": "", "stopGuaranteed": ""}, "stopLoss": {"type": "", "quantity": "0", "stopPrice": "0", "price": "0", "workingType": "", "stopGuaranteed": ""}, "advanceAttr": "0", "positionID": "0", "takeProfitEntrustPrice": "0", "stopLossEntrustPrice": "0", "orderType": "", "workingType": "MARK_PRICE", "onlyOnePosition": true, "reduceOnly": true, "postOnly": false, "stopGuaranteed": "false", "triggerOrderId": "0", "trailingStopRate": "0", "trailingStopDistance": "0"}, "id": "1792481956836519936", "clientOrderId": null, "symbol": "LTC/USDT:USDT", "timestamp": 1716195976000, "datetime": "2024-05-20T09:06:16.000Z", "lastTradeTimestamp": 1716195976000, "lastUpdateTimestamp": 1716195976000, "type": "market", "timeInForce": null, "postOnly": null, "side": "sell", "price": null, "stopPrice": null, "triggerPrice": null, "stopLossPrice": 20, "takeProfitPrice": null, "average": null, "cost": null, "amount": 0.1, "filled": 0, "remaining": 0.1, "status": "open", "fee": {"currency": "USDT", "cost": null}, "trades": [], "reduceOnly": true, "fees": [{"currency": "USDT", "cost": null}]}}, {"description": "twap order", "method": "fetchOrder", "input": ["5596903086063901779", null, {"twap": true}], "httpResponse": {"code": 0, "msg": "success cancel order", "timestamp": 1732761562874, "data": {"symbol": "LTC-USDT", "mainOrderId": "5596903086063901779", "side": "BUY", "positionSide": "LONG", "priceType": "constant", "priceVariance": "10.00", "triggerPrice": "120.00", "interval": 8, "amountPerOrder": "0.5", "totalAmount": "1.0", "orderStatus": "Filled", "executedQty": "1.0", "duration": 16, "maxDuration": 86400, "createdTime": 1732693017000, "updateTime": 1732693033000}}, "parsedResponse": {"info": {"symbol": "LTC-USDT", "mainOrderId": "5596903086063901779", "side": "BUY", "positionSide": "LONG", "priceType": "constant", "priceVariance": "10.00", "triggerPrice": "120.00", "interval": 8, "amountPerOrder": "0.5", "totalAmount": "1.0", "orderStatus": "Filled", "executedQty": "1.0", "duration": 16, "maxDuration": 86400, "createdTime": 1732693017000, "updateTime": 1732693033000}, "id": "5596903086063901779", "clientOrderId": null, "symbol": "LTC/USDT:USDT", "timestamp": 1732693017000, "datetime": "2024-11-27T07:36:57.000Z", "lastTradeTimestamp": 1732693033000, "lastUpdateTimestamp": 1732693033000, "type": null, "timeInForce": null, "postOnly": null, "side": "buy", "price": null, "stopPrice": null, "triggerPrice": null, "stopLossPrice": null, "takeProfitPrice": null, "average": null, "cost": null, "amount": 1, "filled": 1, "remaining": 0, "status": "closed", "fee": {"currency": "USDT", "cost": null}, "trades": [], "reduceOnly": null, "fees": [{"currency": "USDT", "cost": null}]}}], "fetchPositions": [{"description": "fetch position", "method": "fetchPositions", "input": [["LTC/USDT:USDT"]], "httpResponse": {"code": "0", "msg": "", "data": [{"positionId": "1773289330136281088", "symbol": "LTC-USDT", "currency": "USDT", "positionAmt": "0.1", "availableAmt": "0.1", "positionSide": "LONG", "isolated": false, "avgPrice": "95.61", "initialMargin": "1.9128", "leverage": "5", "unrealizedProfit": "0.0005", "realisedProfit": "-0.0048", "liquidationPrice": "0", "pnlRatio": "0.0002", "maxMarginReduction": "0.0000", "riskRate": "0.0068", "markPrice": "95.62", "positionValue": "9.5616", "onlyOnePosition": false}]}, "parsedResponse": [{"info": {"positionId": "1773289330136281088", "symbol": "LTC-USDT", "currency": "USDT", "positionAmt": "0.1", "availableAmt": "0.1", "positionSide": "LONG", "isolated": false, "avgPrice": "95.61", "initialMargin": "1.9128", "leverage": "5", "unrealizedProfit": "0.0005", "realisedProfit": "-0.0048", "liquidationPrice": "0", "pnlRatio": "0.0002", "maxMarginReduction": "0.0000", "riskRate": "0.0068", "markPrice": "95.62", "positionValue": "9.5616", "onlyOnePosition": false}, "id": "1773289330136281088", "symbol": "LTC/USDT:USDT", "notional": 9.5616, "marginMode": "cross", "liquidationPrice": null, "entryPrice": 95.61, "unrealizedPnl": 0.0005, "realizedPnl": -0.0048, "percentage": null, "contracts": 0.1, "contractSize": 1, "markPrice": 95.62, "lastPrice": null, "side": "long", "hedged": null, "timestamp": null, "datetime": null, "lastUpdateTimestamp": null, "maintenanceMargin": null, "maintenanceMarginPercentage": null, "collateral": null, "initialMargin": 1.9128, "initialMarginPercentage": null, "leverage": 5, "marginRatio": null, "stopLossPrice": null, "takeProfitPrice": null}]}], "fetchMarginMode": [{"description": "fetch linear swap margin mode", "method": "fetchMarginMode", "input": ["BTC/USDT:USDT"], "httpResponse": {"code": "0", "msg": "", "data": {"marginType": "CROSSED", "symbol": "BTC-USDT"}}, "parsedResponse": {"info": {"marginType": "CROSSED", "symbol": "BTC-USDT"}, "symbol": "BTC/USDT:USDT", "marginMode": "cross"}}, {"description": "fetch inverse swap margin mode", "method": "fetchMarginMode", "input": ["BTC/USD:BTC"], "httpResponse": {"code": "0", "msg": "", "timestamp": "1731567460653", "data": {"symbol": "BTC-USD", "marginType": "CROSSED"}}, "parsedResponse": {"info": {"symbol": "BTC-USD", "marginType": "CROSSED"}, "symbol": "BTC/USD:BTC", "marginMode": "cross"}}], "fetchPositionHistory": [{"description": "linear swap fetch position history with a limit argument", "method": "fetchPositionHistory", "input": ["LTC/USDT:USDT", null, 1], "httpResponse": {"code": "0", "msg": "", "data": {"positionHistory": [{"positionId": "1861675561156571136", "symbol": "LTC-USDT", "isolated": false, "positionSide": "LONG", "openTime": "1732693017000", "updateTime": "1733310292000", "avgPrice": "95.18", "avgClosePrice": "129.48", "realisedProfit": "102.89", "netProfit": "99.63", "positionAmt": "30.0", "closePositionAmt": "30.0", "leverage": "6", "closeAllPositions": true, "positionCommission": "-0.33699650000000003", "totalFunding": "-2.921461693902908"}]}}, "parsedResponse": [{"info": {"positionId": "1861675561156571136", "symbol": "LTC-USDT", "isolated": false, "positionSide": "LONG", "openTime": "1732693017000", "updateTime": "1733310292000", "avgPrice": "95.18", "avgClosePrice": "129.48", "realisedProfit": "102.89", "netProfit": "99.63", "positionAmt": "30.0", "closePositionAmt": "30.0", "leverage": "6", "closeAllPositions": true, "positionCommission": "-0.33699650000000003", "totalFunding": "-2.921461693902908"}, "id": "1861675561156571136", "symbol": "LTC/USDT:USDT", "notional": null, "marginMode": "cross", "liquidationPrice": null, "entryPrice": 95.18, "unrealizedPnl": null, "realizedPnl": 102.89, "percentage": null, "contracts": 30, "contractSize": 1, "markPrice": null, "lastPrice": null, "side": "long", "hedged": null, "timestamp": 1732693017000, "datetime": "2024-11-27T07:36:57.000Z", "lastUpdateTimestamp": 1733310292000, "maintenanceMargin": null, "maintenanceMarginPercentage": null, "collateral": null, "initialMargin": null, "initialMarginPercentage": null, "leverage": 6, "marginRatio": null, "stopLossPrice": null, "takeProfitPrice": null}]}]}}