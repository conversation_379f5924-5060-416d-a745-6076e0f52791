{"exchange": "vertex", "skipKeys": [], "methods": {"fetchCurrencies": [{"description": "fetchCurrencies", "method": "fetchCurrencies", "input": [], "httpResponse": [{"product_id": "31", "ticker_id": "USDT_USDC", "market_type": "spot", "name": "Tether USD", "symbol": "USDT", "maker_fee": "0.0", "taker_fee": "0.0002", "can_withdraw": true, "can_deposit": true}], "parsedResponse": {"USDT": {"info": {"product_id": "31", "ticker_id": "USDT_USDC", "market_type": "spot", "name": "Tether USD", "symbol": "USDT", "maker_fee": "0.0", "taker_fee": "0.0002", "can_withdraw": true, "can_deposit": true}, "id": "31", "numericId": null, "code": "USDT", "precision": null, "type": null, "name": "USDT", "active": null, "deposit": true, "withdraw": true, "fee": null, "fees": {}, "networks": null, "limits": {"amount": {"min": null, "max": null}, "withdraw": {"min": null, "max": null}}}}}], "fetchMarkets": [{"description": "fetch Currencies", "method": "fetchCurrencies", "input": [], "httpResponse": {"status": "success", "data": {"symbols": {"BNB-PERP": {"type": "perp", "product_id": "8", "symbol": "BNB-PERP", "price_increment_x18": "10000000000000000", "size_increment": "50000000000000000", "min_size": "1000000000000000000", "min_depth_x18": "2500000000000000000000", "max_spread_rate_x18": "5000000000000000", "maker_fee_rate_x18": "0", "taker_fee_rate_x18": "200000000000000", "long_weight_initial_x18": "900000000000000000", "long_weight_maintenance_x18": "950000000000000000"}}}}, "parsedResponse": [{"id": "8", "symbol": "BNB/USDC:USDC", "base": "BNB-PERP", "quote": "USDC", "settle": "USDC", "baseId": "BNB-PERP", "quoteId": "USDC", "settleId": "USDC", "type": "swap", "spot": false, "margin": null, "swap": true, "future": false, "option": false, "active": true, "contract": true, "linear": true, "inverse": false, "taker": 0.0002, "maker": 0, "contractSize": 1, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.05, "price": 0.01}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 1, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": null, "max": null}}, "created": null, "info": {"type": "perp", "product_id": "8", "symbol": "BNB-PERP", "price_increment_x18": "10000000000000000", "size_increment": "50000000000000000", "min_size": "1000000000000000000", "min_depth_x18": "2500000000000000000000", "max_spread_rate_x18": "5000000000000000", "maker_fee_rate_x18": "0", "taker_fee_rate_x18": "200000000000000", "long_weight_initial_x18": "900000000000000000", "long_weight_maintenance_x18": "950000000000000000"}}]}], "fetchTime": [{"description": "fetch Time", "method": "fetchTime", "input": [], "httpResponse": "1717731369829", "parsedResponse": 1717731369829}], "fetchStatus": [{"description": "fetch Status", "method": "fetchStatus", "input": [], "httpResponse": {"status": "success", "data": "active", "request_type": "query_status"}, "parsedResponse": {"status": "ok", "updated": null, "eta": null, "url": null, "info": {"status": "success", "data": "active", "request_type": "query_status"}}}], "fetchTrades": [{"description": "fetch Trades", "method": "fetchTrades", "input": ["USDT/USDC", null, 1], "httpResponse": [{"ticker_id": "USDT_USDC", "trade_id": "20295068", "price": "0.99920012", "base_filled": "-600.0", "quote_filled": "599.520072", "timestamp": "1717672878", "trade_type": "sell"}], "parsedResponse": [{"id": "20295068", "timestamp": 1717672878000, "datetime": "2024-06-06T11:21:18.000Z", "symbol": "USDT/USDC", "side": "sell", "price": 0.99920012, "amount": 600, "cost": 599.520072, "order": null, "takerOrMaker": null, "type": null, "fee": {"cost": null, "currency": null}, "info": {"ticker_id": "USDT_USDC", "trade_id": "20295068", "price": "0.99920012", "base_filled": "-600.0", "quote_filled": "599.520072", "timestamp": "1717672878", "trade_type": "sell"}, "fees": []}]}], "fetchMyTrades": [{"description": "fetch Trades", "method": "fetchTrades", "input": ["USDT/USDC", null, 1], "httpResponse": {"matches": [{"digest": "0x6564157372747c4798048254f10ce8aa8f3b11e028c6cf9f502e5a5c12354a19", "order": {"sender": "0x754e6a9e4bc74d119263fd249fac5d876d0d10ee64656661756c740000000000", "priceX18": "999700000000000000", "amount": "250000000000000000000", "expiration": "1717497650145", "nonce": "1800926819773317349"}, "base_filled": "249999999999999999999", "quote_filled": "-249925000000000000001", "fee": "0", "sequencer_fee": "0", "cumulative_fee": "0", "cumulative_base_filled": "249999999999999999999", "cumulative_quote_filled": "-249925000000000000001", "pre_balance": {"base": {"spot": {"product_id": "31", "lp_balance": {"amount": "0"}, "balance": {"amount": "830000000000002125639", "last_cumulative_multiplier_x18": "1000000000003696582"}}}, "quote": null}, "post_balance": {"base": {"spot": {"product_id": "31", "lp_balance": {"amount": "0"}, "balance": {"amount": "1080000000000002125638", "last_cumulative_multiplier_x18": "1000000000003696582"}}}, "quote": null}, "net_entry_unrealized": "829797000000000000011", "net_entry_cumulative": "830197190038007601531", "submission_idx": "20041479"}]}, "parsedResponse": [{"id": "20041479", "timestamp": null, "datetime": null, "symbol": "USDT/USDC", "side": "buy", "price": 0.9997, "amount": 250, "cost": 249.925, "order": "0x6564157372747c4798048254f10ce8aa8f3b11e028c6cf9f502e5a5c12354a19", "takerOrMaker": null, "type": null, "fee": {"cost": 0, "currency": null}, "info": {"digest": "0x6564157372747c4798048254f10ce8aa8f3b11e028c6cf9f502e5a5c12354a19", "order": {"sender": "0x754e6a9e4bc74d119263fd249fac5d876d0d10ee64656661756c740000000000", "priceX18": "999700000000000000", "amount": "250000000000000000000", "expiration": "1717497650145", "nonce": "1800926819773317349"}, "base_filled": "249999999999999999999", "quote_filled": "-249925000000000000001", "fee": "0", "sequencer_fee": "0", "cumulative_fee": "0", "cumulative_base_filled": "249999999999999999999", "cumulative_quote_filled": "-249925000000000000001", "pre_balance": {"base": {"spot": {"product_id": "31", "lp_balance": {"amount": "0"}, "balance": {"amount": "830000000000002125639", "last_cumulative_multiplier_x18": "1000000000003696582"}}}, "quote": null}, "post_balance": {"base": {"spot": {"product_id": "31", "lp_balance": {"amount": "0"}, "balance": {"amount": "1080000000000002125638", "last_cumulative_multiplier_x18": "1000000000003696582"}}}, "quote": null}, "net_entry_unrealized": "829797000000000000011", "net_entry_cumulative": "830197190038007601531", "submission_idx": "20041479"}, "fees": [{"cost": 0, "currency": null}]}]}], "fetchOrderBook": [{"description": "fetch OrderBook", "method": "fetchOrderBook", "input": ["USDT/USDC", 1], "httpResponse": {"ticker_id": "USDT_USDC", "bids": [[0.9991, 1800]], "asks": [[0.9998, 47000]], "timestamp": "1717732337356"}, "parsedResponse": {"symbol": "USDT/USDC", "bids": [[0.9991, 1800]], "asks": [[0.9998, 47000]], "timestamp": 1717732337356, "datetime": "2024-06-07T03:52:17.356Z", "nonce": null}}], "fetchOHLCV": [{"description": "fetch OHLCV", "method": "fetchOHLCV", "input": ["USDT/USDC", "1m", null, 1], "httpResponse": {"candlesticks": [{"product_id": "31", "granularity": "60", "submission_idx": "20389014", "timestamp": "1717732860", "open_x18": "999800000000000000", "high_x18": "999800000000000000", "low_x18": "999800000000000000", "close_x18": "999800000000000000", "volume": "19999999999999999999999"}]}, "parsedResponse": [[1717732860000, 0.9998, 0.9998, 0.9998, 0.9998, 20000]]}], "fetchFundingRates": [{"description": "fetch FundingRates", "method": "fetchFundingRates", "input": [["SOL/USDC:USDC"]], "httpResponse": {"SOL-PERP_USDC": {"ticker_id": "SOL-PERP_USDC", "base_currency": "SOL-PERP", "quote_currency": "USDC", "last_price": "169.812", "base_volume": "18714.5", "quote_volume": "3216988.7085012672", "product_type": "perpetual", "contract_price": "170.51551470021332", "contract_price_currency": "USD", "open_interest": "145974.0", "open_interest_usd": "24890831.74284894", "index_price": "170.43322045028683", "mark_price": "170.02281881675358", "funding_rate": "-0.002454353266959626", "next_funding_rate_timestamp": "1717736400", "price_change_percent_24h": "-1.7922116060212385"}}, "parsedResponse": {"SOL/USDC:USDC": {"info": {"ticker_id": "SOL-PERP_USDC", "base_currency": "SOL-PERP", "quote_currency": "USDC", "last_price": "169.812", "base_volume": "18714.5", "quote_volume": "3216988.7085012672", "product_type": "perpetual", "contract_price": "170.51551470021332", "contract_price_currency": "USD", "open_interest": "145974.0", "open_interest_usd": "24890831.74284894", "index_price": "170.43322045028683", "mark_price": "170.02281881675358", "funding_rate": "-0.002454353266959626", "next_funding_rate_timestamp": "1717736400", "price_change_percent_24h": "-1.7922116060212385"}, "symbol": "SOL/USDC:USDC", "markPrice": 170.02281881675358, "indexPrice": 170.43322045028683, "interestRate": null, "estimatedSettlePrice": null, "timestamp": null, "datetime": null, "fundingRate": -0.002454353266959626, "fundingTimestamp": 1717736400000, "fundingDatetime": "2024-06-07T05:00:00.000Z", "nextFundingRate": null, "nextFundingTimestamp": null, "nextFundingDatetime": null, "previousFundingRate": null, "previousFundingTimestamp": null, "previousFundingDatetime": null, "interval": null}}}], "fetchOpenInterest": [{"description": "fetch the open interest of a unified linear swap symbol", "method": "fetchOpenInterest", "input": ["SOL/USDC:USDC"], "httpResponse": {"SOL-PERP_USDC": {"ticker_id": "SOL-PERP_USDC", "base_currency": "SOL-PERP", "quote_currency": "USDC", "last_price": "180.586", "base_volume": "11205.0", "quote_volume": "2106146.2184327813", "product_type": "perpetual", "contract_price": "180.565***********", "contract_price_currency": "USD", "open_interest": "2925.5", "open_interest_usd": "528244.**********", "index_price": "180.68562290740093", "mark_price": "180.64793109646737", "funding_rate": "-0.00020819561114687", "next_funding_rate_timestamp": "**********", "price_change_percent_24h": "-6.***************"}}, "parsedResponse": {"symbol": "SOL/USDC:USDC", "openInterestAmount": 2925.5, "openInterestValue": 528244.**********, "timestamp": null, "datetime": null, "info": {"ticker_id": "SOL-PERP_USDC", "base_currency": "SOL-PERP", "quote_currency": "USDC", "last_price": "180.586", "base_volume": "11205.0", "quote_volume": "2106146.2184327813", "product_type": "perpetual", "contract_price": "180.565***********", "contract_price_currency": "USD", "open_interest": "2925.5", "open_interest_usd": "528244.**********", "index_price": "180.68562290740093", "mark_price": "180.64793109646737", "funding_rate": "-0.00020819561114687", "next_funding_rate_timestamp": "**********", "price_change_percent_24h": "-6.***************"}, "baseVolume": null, "quoteVolume": null}}], "fetchOpenInterests": [{"description": "fetch the open interest for unified symbols", "method": "fetchOpenInterests", "input": [["SOL/USDC:USDC"]], "httpResponse": {"WLD-PERP_USDC": {"ticker_id": "WLD-PERP_USDC", "base_currency": "WLD-PERP", "quote_currency": "USDC", "last_price": "2.1508", "base_volume": "114325.0", "quote_volume": "263529.1307322623", "product_type": "perpetual", "contract_price": "2.1763030333316675", "contract_price_currency": "USD", "open_interest": "154345.0", "open_interest_usd": "335901.4916795762", "index_price": "2.1760031982399672", "mark_price": "2.176768202944781", "funding_rate": "0.000349487448554836", "next_funding_rate_timestamp": "**********", "price_change_percent_24h": "-8.224980507028002"}, "SOL-PERP_USDC": {"ticker_id": "SOL-PERP_USDC", "base_currency": "SOL-PERP", "quote_currency": "USDC", "last_price": "179.354", "base_volume": "11205.0", "quote_volume": "2106159.5237538456", "product_type": "perpetual", "contract_price": "180.565***********", "contract_price_currency": "USD", "open_interest": "2925.5", "open_interest_usd": "528244.**********", "index_price": "180.**************", "mark_price": "180.**************", "funding_rate": "-0.000197154274392456", "next_funding_rate_timestamp": "**********", "price_change_percent_24h": "-6.***************"}, "JTO-PERP_USDC": {"ticker_id": "JTO-PERP_USDC", "base_currency": "JTO-PERP", "quote_currency": "USDC", "last_price": "2.896", "base_volume": "33910.0", "quote_volume": "102783.5045504801", "product_type": "perpetual", "contract_price": "2.899911827808894", "contract_price_currency": "USD", "open_interest": "71360.0", "open_interest_usd": "206937.70803244266", "index_price": "2.899470291339763", "mark_price": "2.9009167569056", "funding_rate": "0.000497434661637557", "next_funding_rate_timestamp": "**********", "price_change_percent_24h": "-5.100260960019056"}}, "parsedResponse": {"SOL/USDC:USDC": {"symbol": "SOL/USDC:USDC", "openInterestAmount": 2925.5, "openInterestValue": 528244.**********, "timestamp": null, "datetime": null, "info": {"ticker_id": "SOL-PERP_USDC", "base_currency": "SOL-PERP", "quote_currency": "USDC", "last_price": "179.354", "base_volume": "11205.0", "quote_volume": "2106159.5237538456", "product_type": "perpetual", "contract_price": "180.565***********", "contract_price_currency": "USD", "open_interest": "2925.5", "open_interest_usd": "528244.**********", "index_price": "180.**************", "mark_price": "180.**************", "funding_rate": "-0.000197154274392456", "next_funding_rate_timestamp": "**********", "price_change_percent_24h": "-6.***************"}, "baseVolume": null, "quoteVolume": null}}}], "fetchBalance": [{"description": "fetch balance", "method": "fetchBalance", "input": [], "httpResponse": {"status": "success", "data": {"subaccount": "0x754e6a9e4bc74d119263fd249fac5d876d0d10ee64656661756c740000000000", "exists": true, "spot_balances": [{"product_id": 0, "lp_balance": {"amount": "0"}, "balance": {"amount": "60827695089644845053717", "last_cumulative_multiplier_x18": "1000000000190717746"}}, {"product_id": 1, "lp_balance": {"amount": "0"}, "balance": {"amount": "9013449455832821046", "last_cumulative_multiplier_x18": "1011212886771930487"}}, {"product_id": 3, "lp_balance": {"amount": "0"}, "balance": {"amount": "-2", "last_cumulative_multiplier_x18": "1020693004278224184"}}, {"product_id": 5, "lp_balance": {"amount": "0"}, "balance": {"amount": "0", "last_cumulative_multiplier_x18": "1804518241124285382"}}, {"product_id": 31, "lp_balance": {"amount": "0"}, "balance": {"amount": "1080000000000120456918", "last_cumulative_multiplier_x18": "1000000000003806148"}}, {"product_id": 41, "lp_balance": {"amount": "0"}, "balance": {"amount": "0", "last_cumulative_multiplier_x18": "1000000000000000000"}}]}, "request_type": "query_subaccount_info"}, "parsedResponse": {"info": {"status": "success", "data": {"subaccount": "0x754e6a9e4bc74d119263fd249fac5d876d0d10ee64656661756c740000000000", "exists": true, "spot_balances": [{"product_id": 0, "lp_balance": {"amount": "0"}, "balance": {"amount": "60827695089644845053717", "last_cumulative_multiplier_x18": "1000000000190717746"}}, {"product_id": 1, "lp_balance": {"amount": "0"}, "balance": {"amount": "9013449455832821046", "last_cumulative_multiplier_x18": "1011212886771930487"}}, {"product_id": 3, "lp_balance": {"amount": "0"}, "balance": {"amount": "-2", "last_cumulative_multiplier_x18": "1020693004278224184"}}, {"product_id": 5, "lp_balance": {"amount": "0"}, "balance": {"amount": "0", "last_cumulative_multiplier_x18": "1804518241124285382"}}, {"product_id": 31, "lp_balance": {"amount": "0"}, "balance": {"amount": "1080000000000120456918", "last_cumulative_multiplier_x18": "1000000000003806148"}}, {"product_id": 41, "lp_balance": {"amount": "0"}, "balance": {"amount": "0", "last_cumulative_multiplier_x18": "1000000000000000000"}}]}, "request_type": "query_subaccount_info"}, "USDC": {"free": null, "used": null, "total": 60827.***********}, "ARB": {"free": null, "used": null, "total": 0}, "USDT": {"free": null, "used": null, "total": 1080.*************}, "VRTX": {"free": null, "used": null, "total": 0}, "free": {"USDC": null, "ARB": null, "USDT": null, "VRTX": null}, "used": {"USDC": null, "ARB": null, "USDT": null, "VRTX": null}, "total": {"USDC": 60827.***********, "ARB": 0, "USDT": 1080.*************, "VRTX": 0}}}], "fetchFundingRate": [{"description": "fetch funding rate", "method": "fetchFundingRate", "input": ["SOL/USDC:USDC"], "httpResponse": {"product_id": 12, "funding_rate_x18": "***************", "update_time": "**********"}, "parsedResponse": {"info": {"product_id": 12, "funding_rate_x18": "***************", "update_time": "**********"}, "symbol": "SOL/USDC:USDC", "markPrice": null, "indexPrice": null, "interestRate": null, "estimatedSettlePrice": null, "timestamp": null, "datetime": null, "fundingRate": 0.000***************, "fundingTimestamp": **********000, "fundingDatetime": "2024-06-07T10:31:00.000Z", "nextFundingRate": null, "nextFundingTimestamp": null, "nextFundingDatetime": null, "previousFundingRate": null, "previousFundingTimestamp": null, "previousFundingDatetime": null, "interval": null}}], "fetchTradingFees": [{"description": "fetch trading fees", "method": "fetchTradingFees", "input": [], "httpResponse": {"status": "success", "data": {"taker_fee_rates_x18": ["0", "200000000000000", "200000000000000", "200000000000000", "200000000000000", "200000000000000", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "200000000000000", "0", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "200000000000000", "0", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "0", "0", "0", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000"], "maker_fee_rates_x18": ["0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "liquidation_sequencer_fee": "1000000000000000000", "health_check_sequencer_fee": "1000000000000000000", "taker_sequencer_fee": "0", "withdraw_sequencer_fees": ["1000000000000000000", "40000000000000", "0", "600000000000000", "0", "1000000000000000000", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "1000000000000000000", "0", "0", "0", "0", "0", "0", "0", "0", "0", "1000000000000000000", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"]}, "request_type": "query_fee_rates"}, "parsedResponse": {"BTC/USDC:USDC": {"info": {"status": "success", "data": {"taker_fee_rates_x18": ["0", "200000000000000", "200000000000000", "200000000000000", "200000000000000", "200000000000000", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "200000000000000", "0", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "200000000000000", "0", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "0", "0", "0", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000"], "maker_fee_rates_x18": ["0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "liquidation_sequencer_fee": "1000000000000000000", "health_check_sequencer_fee": "1000000000000000000", "taker_sequencer_fee": "0", "withdraw_sequencer_fees": ["1000000000000000000", "40000000000000", "0", "600000000000000", "0", "1000000000000000000", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "1000000000000000000", "0", "0", "0", "0", "0", "0", "0", "0", "0", "1000000000000000000", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"]}, "request_type": "query_fee_rates"}, "symbol": "BTC/USDC:USDC", "maker": 0, "taker": 0.0002, "percentage": true, "tierBased": false}, "ETH/USDC:USDC": {"info": {"status": "success", "data": {"taker_fee_rates_x18": ["0", "200000000000000", "200000000000000", "200000000000000", "200000000000000", "200000000000000", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "200000000000000", "0", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "200000000000000", "0", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "0", "0", "0", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000"], "maker_fee_rates_x18": ["0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "liquidation_sequencer_fee": "1000000000000000000", "health_check_sequencer_fee": "1000000000000000000", "taker_sequencer_fee": "0", "withdraw_sequencer_fees": ["1000000000000000000", "40000000000000", "0", "600000000000000", "0", "1000000000000000000", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "1000000000000000000", "0", "0", "0", "0", "0", "0", "0", "0", "0", "1000000000000000000", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"]}, "request_type": "query_fee_rates"}, "symbol": "ETH/USDC:USDC", "maker": 0, "taker": 0.0002, "percentage": true, "tierBased": false}, "ARB/USDC": {"info": {"status": "success", "data": {"taker_fee_rates_x18": ["0", "200000000000000", "200000000000000", "200000000000000", "200000000000000", "200000000000000", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "200000000000000", "0", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "200000000000000", "0", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "0", "0", "0", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000"], "maker_fee_rates_x18": ["0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "liquidation_sequencer_fee": "1000000000000000000", "health_check_sequencer_fee": "1000000000000000000", "taker_sequencer_fee": "0", "withdraw_sequencer_fees": ["1000000000000000000", "40000000000000", "0", "600000000000000", "0", "1000000000000000000", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "1000000000000000000", "0", "0", "0", "0", "0", "0", "0", "0", "0", "1000000000000000000", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"]}, "request_type": "query_fee_rates"}, "symbol": "ARB/USDC", "maker": 0, "taker": 0.0002, "percentage": true, "tierBased": false}, "XRP/USDC:USDC": {"info": {"status": "success", "data": {"taker_fee_rates_x18": ["0", "200000000000000", "200000000000000", "200000000000000", "200000000000000", "200000000000000", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "200000000000000", "0", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "200000000000000", "0", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "0", "0", "0", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000"], "maker_fee_rates_x18": ["0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "liquidation_sequencer_fee": "1000000000000000000", "health_check_sequencer_fee": "1000000000000000000", "taker_sequencer_fee": "0", "withdraw_sequencer_fees": ["1000000000000000000", "40000000000000", "0", "600000000000000", "0", "1000000000000000000", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "1000000000000000000", "0", "0", "0", "0", "0", "0", "0", "0", "0", "1000000000000000000", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"]}, "request_type": "query_fee_rates"}, "symbol": "XRP/USDC:USDC", "maker": 0, "taker": 0.0002, "percentage": true, "tierBased": false}, "SOL/USDC:USDC": {"info": {"status": "success", "data": {"taker_fee_rates_x18": ["0", "200000000000000", "200000000000000", "200000000000000", "200000000000000", "200000000000000", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "200000000000000", "0", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "200000000000000", "0", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "0", "0", "0", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000"], "maker_fee_rates_x18": ["0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "liquidation_sequencer_fee": "1000000000000000000", "health_check_sequencer_fee": "1000000000000000000", "taker_sequencer_fee": "0", "withdraw_sequencer_fees": ["1000000000000000000", "40000000000000", "0", "600000000000000", "0", "1000000000000000000", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "1000000000000000000", "0", "0", "0", "0", "0", "0", "0", "0", "0", "1000000000000000000", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"]}, "request_type": "query_fee_rates"}, "symbol": "SOL/USDC:USDC", "maker": 0, "taker": 0.0002, "percentage": true, "tierBased": false}, "LTC/USDC:USDC": {"info": {"status": "success", "data": {"taker_fee_rates_x18": ["0", "200000000000000", "200000000000000", "200000000000000", "200000000000000", "200000000000000", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "200000000000000", "0", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "200000000000000", "0", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "0", "0", "0", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000"], "maker_fee_rates_x18": ["0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "liquidation_sequencer_fee": "1000000000000000000", "health_check_sequencer_fee": "1000000000000000000", "taker_sequencer_fee": "0", "withdraw_sequencer_fees": ["1000000000000000000", "40000000000000", "0", "600000000000000", "0", "1000000000000000000", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "1000000000000000000", "0", "0", "0", "0", "0", "0", "0", "0", "0", "1000000000000000000", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"]}, "request_type": "query_fee_rates"}, "symbol": "LTC/USDC:USDC", "maker": 0, "taker": 0.0002, "percentage": true, "tierBased": false}, "USDT/USDC": {"info": {"status": "success", "data": {"taker_fee_rates_x18": ["0", "200000000000000", "200000000000000", "200000000000000", "200000000000000", "200000000000000", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "200000000000000", "0", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "200000000000000", "0", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "0", "0", "0", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000"], "maker_fee_rates_x18": ["0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "liquidation_sequencer_fee": "1000000000000000000", "health_check_sequencer_fee": "1000000000000000000", "taker_sequencer_fee": "0", "withdraw_sequencer_fees": ["1000000000000000000", "40000000000000", "0", "600000000000000", "0", "1000000000000000000", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "1000000000000000000", "0", "0", "0", "0", "0", "0", "0", "0", "0", "1000000000000000000", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"]}, "request_type": "query_fee_rates"}, "symbol": "USDT/USDC", "maker": 0, "taker": 0.0002, "percentage": true, "tierBased": false}, "VRTX/USDC": {"info": {"status": "success", "data": {"taker_fee_rates_x18": ["0", "200000000000000", "200000000000000", "200000000000000", "200000000000000", "200000000000000", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "200000000000000", "0", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "200000000000000", "0", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "0", "0", "0", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000"], "maker_fee_rates_x18": ["0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "liquidation_sequencer_fee": "1000000000000000000", "health_check_sequencer_fee": "1000000000000000000", "taker_sequencer_fee": "0", "withdraw_sequencer_fees": ["1000000000000000000", "40000000000000", "0", "600000000000000", "0", "1000000000000000000", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "1000000000000000000", "0", "0", "0", "0", "0", "0", "0", "0", "0", "1000000000000000000", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"]}, "request_type": "query_fee_rates"}, "symbol": "VRTX/USDC", "maker": 0, "taker": 0.0002, "percentage": true, "tierBased": false}, "ADA/USDC:USDC": {"info": {"status": "success", "data": {"taker_fee_rates_x18": ["0", "200000000000000", "200000000000000", "200000000000000", "200000000000000", "200000000000000", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "200000000000000", "0", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "200000000000000", "0", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "0", "0", "0", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000", "0", "200000000000000"], "maker_fee_rates_x18": ["0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "liquidation_sequencer_fee": "1000000000000000000", "health_check_sequencer_fee": "1000000000000000000", "taker_sequencer_fee": "0", "withdraw_sequencer_fees": ["1000000000000000000", "40000000000000", "0", "600000000000000", "0", "1000000000000000000", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "1000000000000000000", "0", "0", "0", "0", "0", "0", "0", "0", "0", "1000000000000000000", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"]}, "request_type": "query_fee_rates"}, "symbol": "ADA/USDC:USDC", "maker": 0, "taker": 0.0002, "percentage": true, "tierBased": false}}}], "fetchPositions": [{"description": "fetch positions", "method": "fetchPositions", "input": [], "httpResponse": {"status": "success", "data": {"subaccount": "0x754e6a9e4bc74d119263fd249fac5d876d0d10ee64656661756c740000000000", "exists": true, "perp_balances": [{"product_id": 2, "lp_balance": {"amount": "0", "last_cumulative_funding_x18": "0"}, "balance": {"amount": "-8043000000000000000", "v_quote_balance": "552732115339943081585805", "last_cumulative_funding_x18": "1061661330817530190927"}}, {"product_id": 4, "lp_balance": {"amount": "0", "last_cumulative_funding_x18": "-*****************"}, "balance": {"amount": "-11000000000000000000", "v_quote_balance": "41225108338053477999736", "last_cumulative_funding_x18": "177206620948938387728"}}, {"product_id": 6, "lp_balance": {"amount": "0", "last_cumulative_funding_x18": "0"}, "balance": {"amount": "0", "v_quote_balance": "0", "last_cumulative_funding_x18": "*****************"}}]}, "request_type": "query_subaccount_info"}, "parsedResponse": [{"info": {"product_id": 2, "lp_balance": {"amount": "0", "last_cumulative_funding_x18": "0"}, "balance": {"amount": "-8043000000000000000", "v_quote_balance": "552732115339943081585805", "last_cumulative_funding_x18": "1061661330817530190927"}}, "id": null, "symbol": "BTC/USDC:USDC", "timestamp": null, "datetime": null, "lastUpdateTimestamp": null, "initialMargin": null, "initialMarginPercentage": null, "maintenanceMargin": null, "maintenanceMarginPercentage": null, "entryPrice": null, "notional": null, "leverage": null, "unrealizedPnl": null, "contracts": null, "contractSize": -8.043, "marginRatio": null, "liquidationPrice": null, "markPrice": null, "lastPrice": null, "collateral": null, "marginMode": "cross", "marginType": null, "side": "sell", "percentage": null, "hedged": null, "stopLossPrice": null, "takeProfitPrice": null}, {"info": {"product_id": 4, "lp_balance": {"amount": "0", "last_cumulative_funding_x18": "-*****************"}, "balance": {"amount": "-11000000000000000000", "v_quote_balance": "41225108338053477999736", "last_cumulative_funding_x18": "177206620948938387728"}}, "id": null, "symbol": "ETH/USDC:USDC", "timestamp": null, "datetime": null, "lastUpdateTimestamp": null, "initialMargin": null, "initialMarginPercentage": null, "maintenanceMargin": null, "maintenanceMarginPercentage": null, "entryPrice": null, "notional": null, "leverage": null, "unrealizedPnl": null, "contracts": null, "contractSize": -11, "marginRatio": null, "liquidationPrice": null, "markPrice": null, "lastPrice": null, "collateral": null, "marginMode": "cross", "marginType": null, "side": "sell", "percentage": null, "hedged": null, "stopLossPrice": null, "takeProfitPrice": null}]}], "fetchOpenOrders": [{"description": "fetch open orders", "method": "fetchOpenOrders", "input": ["ETH/USDC:USDC"], "httpResponse": {"status": "success", "data": {"sender": "0x754e6a9e4bc74d119263fd249fac5d876d0d10ee64656661756c740000000000", "product_id": 4, "orders": [{"product_id": 4, "sender": "0x754e6a9e4bc74d119263fd249fac5d876d0d10ee64656661756c740000000000", "price_x18": "2500000000000000000000", "amount": "1000000000000000000", "expiration": "*************", "order_type": "default", "nonce": "1800752952055432168", "unfilled_amount": "1000000000000000000", "digest": "0x76b052cc9e26e0fbbad5ae556d82e848c6a51e6f95af69b878c9f8c65faa12eb", "placed_at": **********}]}, "request_type": "query_subaccount_orders"}, "parsedResponse": [{"info": {"product_id": 4, "sender": "0x754e6a9e4bc74d119263fd249fac5d876d0d10ee64656661756c740000000000", "price_x18": "2500000000000000000000", "amount": "1000000000000000000", "expiration": "*************", "order_type": "default", "nonce": "1800752952055432168", "unfilled_amount": "1000000000000000000", "digest": "0x76b052cc9e26e0fbbad5ae556d82e848c6a51e6f95af69b878c9f8c65faa12eb", "placed_at": **********}, "id": "0x76b052cc9e26e0fbbad5ae556d82e848c6a51e6f95af69b878c9f8c65faa12eb", "clientOrderId": null, "timestamp": **********000, "datetime": "2024-06-02T12:35:43.000Z", "lastTradeTimestamp": null, "lastUpdateTimestamp": null, "symbol": "ETH/USDC:USDC", "type": null, "timeInForce": "DEFAULT", "postOnly": false, "reduceOnly": null, "side": "buy", "price": 2500, "triggerPrice": null, "amount": 1, "cost": 0, "average": null, "filled": 0, "remaining": 1, "status": null, "fee": null, "trades": [], "fees": [], "stopPrice": null, "takeProfitPrice": null, "stopLossPrice": null}]}], "fetchOrder": [{"description": "fetch order", "method": "fetchOrder", "input": ["0x76b052cc9e26e0fbbad5ae556d82e848c6a51e6f95af69b878c9f8c65faa12eb", "ETH/USDC:USDC"], "httpResponse": {"status": "success", "data": {"product_id": 4, "sender": "0x754e6a9e4bc74d119263fd249fac5d876d0d10ee64656661756c740000000000", "price_x18": "2500000000000000000000", "amount": "1000000000000000000", "expiration": "*************", "order_type": "default", "nonce": "1800752952055432168", "unfilled_amount": "1000000000000000000", "digest": "0x76b052cc9e26e0fbbad5ae556d82e848c6a51e6f95af69b878c9f8c65faa12eb", "placed_at": **********}, "request_type": "query_order"}, "parsedResponse": {"info": {"product_id": 4, "sender": "0x754e6a9e4bc74d119263fd249fac5d876d0d10ee64656661756c740000000000", "price_x18": "2500000000000000000000", "amount": "1000000000000000000", "expiration": "*************", "order_type": "default", "nonce": "1800752952055432168", "unfilled_amount": "1000000000000000000", "digest": "0x76b052cc9e26e0fbbad5ae556d82e848c6a51e6f95af69b878c9f8c65faa12eb", "placed_at": **********}, "id": "0x76b052cc9e26e0fbbad5ae556d82e848c6a51e6f95af69b878c9f8c65faa12eb", "clientOrderId": null, "timestamp": **********000, "datetime": "2024-06-02T12:35:43.000Z", "lastTradeTimestamp": null, "lastUpdateTimestamp": null, "symbol": "ETH/USDC:USDC", "type": null, "timeInForce": "DEFAULT", "postOnly": false, "reduceOnly": null, "side": "buy", "price": 2500, "triggerPrice": null, "amount": 1, "cost": 0, "average": null, "filled": 0, "remaining": 1, "status": null, "fee": null, "trades": [], "fees": [], "stopPrice": null, "takeProfitPrice": null, "stopLossPrice": null}}], "fetchTickers": [{"description": "with symbol", "method": "fetchTickers", "input": [["ETH/USDC:USDC"]], "httpResponse": {"ETH-PERP_USDC": {"ticker_id": "ETH-PERP_USDC", "base_currency": "ETH-PERP", "quote_currency": "USDC", "last_price": "2584.4", "base_volume": "8537.68", "quote_volume": "22213446.163605437", "price_change_percent_24h": "0.5619659915447128"}}, "parsedResponse": {"ETH/USDC:USDC": {"symbol": "ETH/USDC:USDC", "timestamp": null, "datetime": null, "high": null, "low": null, "bid": null, "bidVolume": null, "ask": null, "askVolume": null, "vwap": 2601.8129238394313, "open": 2569.957711663371, "close": 2584.4, "last": 2584.4, "previousClose": null, "change": 14.442288336628875, "percentage": 0.5619659915447128, "average": 2577.1788558316857, "baseVolume": 8537.68, "quoteVolume": 22213446.163605437, "info": {"ticker_id": "ETH-PERP_USDC", "base_currency": "ETH-PERP", "quote_currency": "USDC", "last_price": "2584.4", "base_volume": "8537.68", "quote_volume": "22213446.163605437", "price_change_percent_24h": "0.5619659915447128"}, "indexPrice": null, "markPrice": null}}}]}}