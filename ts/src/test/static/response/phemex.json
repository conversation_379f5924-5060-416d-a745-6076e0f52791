{"exchange": "phemex", "skipKeys": [], "options": {}, "methods": {"fetchCurrencies": [{"description": "fetchCurrencies", "method": "fetchCurrencies", "input": [], "httpResponse": {"code": "0", "msg": "", "data": {"currencies": [{"currency": "BTC", "name": "Bitcoin", "code": "1", "valueScale": "8", "minValueEv": "1", "maxValueEv": "5000000000000000000", "needAddrTag": "0", "status": "Listed", "displayCurrency": "BTC", "inAssetsDisplay": "1", "perpetual": "0", "stableCoin": "0", "assetsPrecision": "8"}], "md5Checksum": "c65e09baebada10bb048ae45585a5ee7"}}, "parsedResponse": {"BTC": {"info": {"currency": "BTC", "name": "Bitcoin", "code": "1", "valueScale": "8", "minValueEv": "1", "maxValueEv": "5000000000000000000", "needAddrTag": "0", "status": "Listed", "displayCurrency": "BTC", "inAssetsDisplay": "1", "perpetual": "0", "stableCoin": "0", "assetsPrecision": "8"}, "id": "BTC", "numericId": null, "code": "BTC", "precision": 1e-08, "type": "crypto", "name": "Bitcoin", "active": true, "deposit": null, "withdraw": null, "fee": null, "fees": {}, "networks": null, "limits": {"amount": {"min": 1e-08, "max": 50000000000}, "withdraw": {"min": null, "max": null}}, "valueScale": 8}}}], "createOrder": [{"description": "swap +buy +limit +triggerPrice +triggerDirection +reduceOnly", "method": "createOrder", "input": ["SOL/USDT:USDT", "limit", "buy", 0.01, 395, {"triggerPrice": 400, "triggerDirection": "up", "reduceOnly": true}], "httpResponse": {"code": "0", "msg": "", "data": {"bizError": "0", "orderID": "fad8c1d3-edeb-4d27-bd4b-805887d4275b", "clOrdID": "CCXT1234561b1598e7648198f0", "symbol": "SOLUSDT", "side": "Buy", "actionTimeNs": "1731964615960000000", "transactTimeNs": "1731964615960000000", "orderType": "StopLimit", "priceRp": "395", "orderQtyRq": "0.01", "displayQtyRq": "0", "timeInForce": "GoodTillCancel", "closedPnlRv": "0", "closedSizeRq": "0", "cumQtyRq": "0", "cumValueRv": "0", "leavesQtyRq": "0", "leavesValueRv": "0", "stopDirection": "Rising", "stopPxRp": "400", "trigger": "ByMarkPrice", "pegOffsetValueRp": "0", "pegOffsetProportionRr": "0", "execStatus": "Registered", "pegPriceType": "UNSPECIFIED", "ordStatus": "Untriggered", "execInst": "None", "takeProfitRp": "0", "stopLossRp": "0", "slPxRp": "0", "tpPxRp": "0"}}, "parsedResponse": {"info": {"bizError": "0", "orderID": "fad8c1d3-edeb-4d27-bd4b-805887d4275b", "clOrdID": "CCXT1234561b1598e7648198f0", "symbol": "SOLUSDT", "side": "Buy", "actionTimeNs": "1731964615960000000", "transactTimeNs": "1731964615960000000", "orderType": "StopLimit", "priceRp": "395", "orderQtyRq": "0.01", "displayQtyRq": "0", "timeInForce": "GoodTillCancel", "closedPnlRv": "0", "closedSizeRq": "0", "cumQtyRq": "0", "cumValueRv": "0", "leavesQtyRq": "0", "leavesValueRv": "0", "stopDirection": "Rising", "stopPxRp": "400", "trigger": "ByMarkPrice", "pegOffsetValueRp": "0", "pegOffsetProportionRr": "0", "execStatus": "Registered", "pegPriceType": "UNSPECIFIED", "ordStatus": "Untriggered", "execInst": "None", "takeProfitRp": "0", "stopLossRp": "0", "slPxRp": "0", "tpPxRp": "0"}, "id": "fad8c1d3-edeb-4d27-bd4b-805887d4275b", "clientOrderId": "CCXT1234561b1598e7648198f0", "datetime": "2024-11-18T21:16:55.960Z", "timestamp": 1731964615960, "lastTradeTimestamp": 1731964615960, "symbol": "SOL/USDT:USDT", "type": "StopLimit", "timeInForce": "GTC", "postOnly": false, "reduceOnly": null, "side": "buy", "price": 395, "stopPrice": 400, "triggerPrice": 400, "takeProfitPrice": 0, "stopLossPrice": 0, "amount": 0.01, "filled": 0, "remaining": 0, "cost": 0, "average": null, "status": "open", "fee": null, "trades": [], "fees": [], "lastUpdateTimestamp": null}}, {"description": "swap +buy +market +triggerPrice +triggerDirection +reduceOnly", "method": "createOrder", "input": ["SOL/USDT:USDT", "market", "buy", 0.01, null, {"triggerPrice": 400, "triggerDirection": "up", "reduceOnly": true}], "httpResponse": {"code": "0", "msg": "", "data": {"bizError": "0", "orderID": "67c77790-94a2-49ae-824a-5c5fc6a98830", "clOrdID": "CCXT123456b6074ff3d4d3ad71", "symbol": "SOLUSDT", "side": "Buy", "actionTimeNs": "1731964547432000000", "transactTimeNs": "1731964547432000000", "orderType": "Stop", "priceRp": "0", "orderQtyRq": "0.01", "displayQtyRq": "0", "timeInForce": "ImmediateOrCancel", "closedPnlRv": "0", "closedSizeRq": "0", "cumQtyRq": "0", "cumValueRv": "0", "leavesQtyRq": "0", "leavesValueRv": "0", "stopDirection": "Rising", "stopPxRp": "400", "trigger": "ByMarkPrice", "pegOffsetValueRp": "0", "pegOffsetProportionRr": "0", "execStatus": "Registered", "pegPriceType": "UNSPECIFIED", "ordStatus": "Untriggered", "execInst": "None", "takeProfitRp": "0", "stopLossRp": "0", "slPxRp": "0", "tpPxRp": "0"}}, "parsedResponse": {"info": {"bizError": "0", "orderID": "67c77790-94a2-49ae-824a-5c5fc6a98830", "clOrdID": "CCXT123456b6074ff3d4d3ad71", "symbol": "SOLUSDT", "side": "Buy", "actionTimeNs": "1731964547432000000", "transactTimeNs": "1731964547432000000", "orderType": "Stop", "priceRp": "0", "orderQtyRq": "0.01", "displayQtyRq": "0", "timeInForce": "ImmediateOrCancel", "closedPnlRv": "0", "closedSizeRq": "0", "cumQtyRq": "0", "cumValueRv": "0", "leavesQtyRq": "0", "leavesValueRv": "0", "stopDirection": "Rising", "stopPxRp": "400", "trigger": "ByMarkPrice", "pegOffsetValueRp": "0", "pegOffsetProportionRr": "0", "execStatus": "Registered", "pegPriceType": "UNSPECIFIED", "ordStatus": "Untriggered", "execInst": "None", "takeProfitRp": "0", "stopLossRp": "0", "slPxRp": "0", "tpPxRp": "0"}, "id": "67c77790-94a2-49ae-824a-5c5fc6a98830", "clientOrderId": "CCXT123456b6074ff3d4d3ad71", "datetime": "2024-11-18T21:15:47.432Z", "timestamp": 1731964547432, "lastTradeTimestamp": 1731964547432, "symbol": "SOL/USDT:USDT", "type": "Stop", "timeInForce": "IOC", "postOnly": false, "reduceOnly": null, "side": "buy", "price": null, "stopPrice": 400, "triggerPrice": 400, "takeProfitPrice": 0, "stopLossPrice": 0, "amount": 0.01, "filled": 0, "remaining": 0, "cost": 0, "average": null, "status": "open", "fee": null, "trades": [], "fees": [], "lastUpdateTimestamp": null}}, {"description": "swap +sell +market +triggerPrice +triggerDirection +reduceOnly", "method": "createOrder", "input": ["SOL/USDT:USDT", "market", "sell", 0.01, null, {"triggerPrice": 400, "triggerDirection": "up", "reduceOnly": true}], "httpResponse": {"code": "0", "msg": "", "data": {"bizError": "0", "orderID": "628bd90d-65bd-41e6-9e66-9136ceda336f", "clOrdID": "CCXT12345604588208b43c8181", "symbol": "SOLUSDT", "side": "<PERSON>ll", "actionTimeNs": "1731964315582000000", "transactTimeNs": "1731964315582000000", "orderType": "MarketIfTouched", "priceRp": "0", "orderQtyRq": "0.01", "displayQtyRq": "0", "timeInForce": "ImmediateOrCancel", "closedPnlRv": "0", "closedSizeRq": "0", "cumQtyRq": "0", "cumValueRv": "0", "leavesQtyRq": "0", "leavesValueRv": "0", "stopDirection": "Rising", "stopPxRp": "400", "trigger": "ByMarkPrice", "pegOffsetValueRp": "0", "pegOffsetProportionRr": "0", "execStatus": "Registered", "pegPriceType": "UNSPECIFIED", "ordStatus": "Untriggered", "execInst": "None", "takeProfitRp": "0", "stopLossRp": "0", "slPxRp": "0", "tpPxRp": "0"}}, "parsedResponse": {"info": {"bizError": "0", "orderID": "628bd90d-65bd-41e6-9e66-9136ceda336f", "clOrdID": "CCXT12345604588208b43c8181", "symbol": "SOLUSDT", "side": "<PERSON>ll", "actionTimeNs": "1731964315582000000", "transactTimeNs": "1731964315582000000", "orderType": "MarketIfTouched", "priceRp": "0", "orderQtyRq": "0.01", "displayQtyRq": "0", "timeInForce": "ImmediateOrCancel", "closedPnlRv": "0", "closedSizeRq": "0", "cumQtyRq": "0", "cumValueRv": "0", "leavesQtyRq": "0", "leavesValueRv": "0", "stopDirection": "Rising", "stopPxRp": "400", "trigger": "ByMarkPrice", "pegOffsetValueRp": "0", "pegOffsetProportionRr": "0", "execStatus": "Registered", "pegPriceType": "UNSPECIFIED", "ordStatus": "Untriggered", "execInst": "None", "takeProfitRp": "0", "stopLossRp": "0", "slPxRp": "0", "tpPxRp": "0"}, "id": "628bd90d-65bd-41e6-9e66-9136ceda336f", "clientOrderId": "CCXT12345604588208b43c8181", "datetime": "2024-11-18T21:11:55.582Z", "timestamp": 1731964315582, "lastTradeTimestamp": 1731964315582, "symbol": "SOL/USDT:USDT", "type": "MarketIfTouched", "timeInForce": "IOC", "postOnly": false, "reduceOnly": null, "side": "sell", "price": null, "stopPrice": 400, "triggerPrice": 400, "takeProfitPrice": 0, "stopLossPrice": 0, "amount": 0.01, "filled": 0, "remaining": 0, "cost": 0, "average": null, "status": "open", "fee": null, "trades": [], "fees": [], "lastUpdateTimestamp": null}}, {"description": "swap +buy +triggerPrice +triggerDirection +reduceOnly", "method": "createOrder", "input": ["SOL/USDT:USDT", "market", "buy", 0.01, null, {"triggerPrice": 200, "triggerDirection": "down", "reduceOnly": true}], "httpResponse": {"code": "0", "msg": "", "data": {"bizError": "0", "orderID": "1d872436-64a8-46b7-a1b3-b3e9d4e504db", "clOrdID": "CCXT1234567a026aa3e4c0b839", "symbol": "SOLUSDT", "side": "Buy", "actionTimeNs": "1731963478059085100", "transactTimeNs": "1731963478059085100", "orderType": "MarketIfTouched", "priceRp": "0", "orderQtyRq": "0.01", "displayQtyRq": "0", "timeInForce": "ImmediateOrCancel", "closedPnlRv": "0", "closedSizeRq": "0", "cumQtyRq": "0", "cumValueRv": "0", "leavesQtyRq": "0", "leavesValueRv": "0", "stopDirection": "Falling", "stopPxRp": "200", "trigger": "ByMarkPrice", "pegOffsetValueRp": "0", "pegOffsetProportionRr": "0", "execStatus": "Registered", "pegPriceType": "UNSPECIFIED", "ordStatus": "Untriggered", "execInst": "None", "takeProfitRp": "0", "stopLossRp": "0", "slPxRp": "0", "tpPxRp": "0"}}, "parsedResponse": {"info": {"bizError": "0", "orderID": "1d872436-64a8-46b7-a1b3-b3e9d4e504db", "clOrdID": "CCXT1234567a026aa3e4c0b839", "symbol": "SOLUSDT", "side": "Buy", "actionTimeNs": "1731963478059085100", "transactTimeNs": "1731963478059085100", "orderType": "MarketIfTouched", "priceRp": "0", "orderQtyRq": "0.01", "displayQtyRq": "0", "timeInForce": "ImmediateOrCancel", "closedPnlRv": "0", "closedSizeRq": "0", "cumQtyRq": "0", "cumValueRv": "0", "leavesQtyRq": "0", "leavesValueRv": "0", "stopDirection": "Falling", "stopPxRp": "200", "trigger": "ByMarkPrice", "pegOffsetValueRp": "0", "pegOffsetProportionRr": "0", "execStatus": "Registered", "pegPriceType": "UNSPECIFIED", "ordStatus": "Untriggered", "execInst": "None", "takeProfitRp": "0", "stopLossRp": "0", "slPxRp": "0", "tpPxRp": "0"}, "id": "1d872436-64a8-46b7-a1b3-b3e9d4e504db", "clientOrderId": "CCXT1234567a026aa3e4c0b839", "datetime": "2024-11-18T20:57:58.059Z", "timestamp": 1731963478059, "lastTradeTimestamp": 1731963478059, "symbol": "SOL/USDT:USDT", "type": "MarketIfTouched", "timeInForce": "IOC", "postOnly": false, "reduceOnly": null, "side": "buy", "price": null, "stopPrice": 200, "triggerPrice": 200, "takeProfitPrice": 0, "stopLossPrice": 0, "amount": 0.01, "filled": 0, "remaining": 0, "cost": 0, "average": null, "status": "open", "fee": null, "trades": [], "fees": [], "lastUpdateTimestamp": null}}, {"description": "swap +marginMode +stopLoss +takeProfit +triggerLimitPrices", "method": "createOrder", "input": ["SOL/USDT:USDT", "limit", "buy", 0.01, 192, {"marginMode": "isolated", "stopLoss": {"triggerPrice": 190, "price": 189, "triggerPriceType": "mark"}, "takeProfit": {"triggerPrice": 277, "price": 278, "triggerPriceType": "last"}}], "httpResponse": {"code": "0", "msg": "", "data": {"bizError": "0", "orderID": "97dd65db-6a72-42b1-b963-edc1edeb12fa", "clOrdID": "CCXT123456efbeec36d415aadf", "symbol": "SOLUSDT", "side": "Buy", "actionTimeNs": "1731694522513451886", "transactTimeNs": "1731694522513451886", "orderType": "Limit", "priceRp": "192", "orderQtyRq": "0.01", "displayQtyRq": "0", "timeInForce": "GoodTillCancel", "closedPnlRv": "0", "closedSizeRq": "0", "cumQtyRq": "0", "cumValueRv": "0", "leavesQtyRq": "0.01", "leavesValueRv": "1.92", "stopDirection": "UNSPECIFIED", "stopPxRp": "0", "trigger": "UNSPECIFIED", "pegOffsetValueRp": "0", "pegOffsetProportionRr": "0", "execStatus": "PendingNew", "pegPriceType": "UNSPECIFIED", "ordStatus": "Created", "execInst": "None", "takeProfitRp": "277", "stopLossRp": "190", "slPxRp": "189", "tpPxRp": "278"}}, "parsedResponse": {"info": {"bizError": "0", "orderID": "97dd65db-6a72-42b1-b963-edc1edeb12fa", "clOrdID": "CCXT123456efbeec36d415aadf", "symbol": "SOLUSDT", "side": "Buy", "actionTimeNs": "1731694522513451886", "transactTimeNs": "1731694522513451886", "orderType": "Limit", "priceRp": "192", "orderQtyRq": "0.01", "displayQtyRq": "0", "timeInForce": "GoodTillCancel", "closedPnlRv": "0", "closedSizeRq": "0", "cumQtyRq": "0", "cumValueRv": "0", "leavesQtyRq": "0.01", "leavesValueRv": "1.92", "stopDirection": "UNSPECIFIED", "stopPxRp": "0", "trigger": "UNSPECIFIED", "pegOffsetValueRp": "0", "pegOffsetProportionRr": "0", "execStatus": "PendingNew", "pegPriceType": "UNSPECIFIED", "ordStatus": "Created", "execInst": "None", "takeProfitRp": "277", "stopLossRp": "190", "slPxRp": "189", "tpPxRp": "278"}, "id": "97dd65db-6a72-42b1-b963-edc1edeb12fa", "clientOrderId": "CCXT123456efbeec36d415aadf", "datetime": "2024-11-15T18:15:22.513Z", "timestamp": 1731694522513, "lastTradeTimestamp": 1731694522513, "symbol": "SOL/USDT:USDT", "type": "limit", "timeInForce": "GTC", "postOnly": false, "reduceOnly": null, "side": "buy", "price": 192, "stopPrice": null, "triggerPrice": null, "takeProfitPrice": 277, "stopLossPrice": 190, "amount": 0.01, "filled": 0, "remaining": 0.01, "cost": 0, "average": null, "status": "open", "fee": null, "trades": [], "fees": [], "lastUpdateTimestamp": null}}], "fetchBalance": [{"description": "Spot balance", "method": "fetchBalance", "input": [], "httpResponse": {"code": "0", "msg": "", "data": [{"currency": "BTC", "balanceEv": "49900", "lockedTradingBalanceEv": "0", "lockedWithdrawEv": "0", "lastUpdateTimeNs": "1700742181383657728", "walletVid": "0"}, {"currency": "USDT", "balanceEv": "3304********", "lockedTradingBalanceEv": "616000000", "lockedWithdrawEv": "0", "lastUpdateTimeNs": "17059742***********", "walletVid": "0"}]}, "parsedResponse": {"info": {"code": "0", "msg": "", "data": [{"currency": "BTC", "balanceEv": "49900", "lockedTradingBalanceEv": "0", "lockedWithdrawEv": "0", "lastUpdateTimeNs": "1700742181383657728", "walletVid": "0"}, {"currency": "USDT", "balanceEv": "3304********", "lockedTradingBalanceEv": "616000000", "lockedWithdrawEv": "0", "lastUpdateTimeNs": "17059742***********", "walletVid": "0"}]}, "BTC": {"free": 0.000499, "used": 0, "total": 0.000499}, "USDT": {"free": 3298.********, "used": 6.16, "total": 3304.********}, "timestamp": *************, "datetime": "2024-01-23T01:44:16.252Z", "free": {"BTC": 0.000499, "USDT": 3298.********}, "used": {"BTC": 0, "USDT": 6.16}, "total": {"BTC": 0.000499, "USDT": 3304.********}}}, {"description": "fetch usdt balance", "method": "fetchBalance", "input": [{"type": "swap"}], "httpResponse": {"code": "0", "msg": "", "data": {"account": {"userID": "940666", "accountId": "**********", "currency": "USDT", "accountBalanceRv": "224.************", "totalUsedBalanceRv": "65.********", "bonusBalanceRv": "0", "status": "0"}, "positions": [{"userID": "940666", "accountID": "**********", "symbol": "LTCUSDT", "currency": "USDT", "side": "Buy", "positionStatus": "Normal", "crossMargin": true, "leverageRr": "-4", "initMarginReqRr": "0.25", "maintMarginReqRr": "0.01", "riskLimitRv": "200000", "size": "0.2", "valueRv": "14.227", "avgEntryPriceRp": "71.135", "avgEntryPrice": "71.135", "posCostRv": "3.********", "assignedPosBalanceRv": "4.********", "bankruptCommRv": "0.0000012", "bankruptPriceRp": "0.01", "positionMarginRv": "163.************", "liquidationPriceRp": "0.01", "deleveragePercentileRr": "0", "buyValueToCostRr": "0.25105", "sellValueToCostRr": "0.25135", "markPriceRp": "67.57", "estimatedOrdLossRv": "0", "usedBalanceRv": "9.********", "cumClosedPnlRv": "0.195", "cumFundingFeeRv": "-122.************", "cumTransactFeeRv": "0.0330384", "transactTimeNs": "1705478400007352000", "takerFeeRateRr": "0.0006", "makerFeeRateRr": "0.0001", "term": "2", "lastTermEndTimeNs": "1692347869378900410", "lastFundingTimeNs": "1705478400000000000", "curTermRealisedPnlRv": "-32.************", "execSeq": "**********", "posSide": "<PERSON>rged", "posMode": "OneWay", "buyLeavesValueRv": "21", "sellLeavesValueRv": "0"}, {"userID": "940666", "accountID": "**********", "symbol": "BTCUSDT", "currency": "USDT", "side": "Buy", "positionStatus": "Normal", "crossMargin": false, "leverageRr": "5", "initMarginReqRr": "0.2", "maintMarginReqRr": "0.0275", "riskLimitRv": "********", "size": "0.01", "valueRv": "259.897", "avgEntryPriceRp": "25989.7", "avgEntryPrice": "25989.7", "posCostRv": "52.********", "assignedPosBalanceRv": "52.********", "bankruptCommRv": "0.********", "bankruptPriceRp": "20791.8", "positionMarginRv": "51.9794", "liquidationPriceRp": "20921.8", "deleveragePercentileRr": "0", "buyValueToCostRr": "0.20108", "sellValueToCostRr": "0.20132", "markPriceRp": "39788.*********", "estimatedOrdLossRv": "0", "usedBalanceRv": "52.********", "cumClosedPnlRv": "-0.0404", "cumFundingFeeRv": "21.************", "cumTransactFeeRv": "0.********", "transactTimeNs": "1705478400006372515", "takerFeeRateRr": "0.0006", "makerFeeRateRr": "0.0001", "term": "2", "lastTermEndTimeNs": "1675002408016449676", "lastFundingTimeNs": "1705478400000000000", "curTermRealisedPnlRv": "-21.************", "execSeq": "**********", "posSide": "<PERSON>", "posMode": "Hedged", "buyLeavesValueRv": "0", "sellLeavesValueRv": "0"}, {"userID": "940666", "accountID": "**********", "symbol": "BTCUSDT", "currency": "USDT", "side": "None", "positionStatus": "Normal", "crossMargin": false, "leverageRr": "5", "initMarginReqRr": "0.2", "maintMarginReqRr": "0.0275", "riskLimitRv": "********", "size": "0", "valueRv": "0", "avgEntryPriceRp": "0", "avgEntryPrice": "0", "posCostRv": "0", "assignedPosBalanceRv": "0", "bankruptCommRv": "0", "bankruptPriceRp": "0", "positionMarginRv": "0", "liquidationPriceRp": "0", "deleveragePercentileRr": "0", "buyValueToCostRr": "0.20108", "sellValueToCostRr": "0.20132", "markPriceRp": "39788.*********", "estimatedOrdLossRv": "0", "usedBalanceRv": "0", "cumClosedPnlRv": "0", "cumFundingFeeRv": "0", "cumTransactFeeRv": "0", "transactTimeNs": "1705478400006372515", "takerFeeRateRr": "0.0006", "makerFeeRateRr": "0.0001", "term": "1", "lastTermEndTimeNs": "0", "lastFundingTimeNs": "1705478400000000000", "curTermRealisedPnlRv": "0", "execSeq": "**********", "posSide": "Short", "posMode": "Hedged", "buyLeavesValueRv": "0", "sellLeavesValueRv": "0"}, {"userID": "940666", "accountID": "**********", "symbol": "XRPUSDT", "currency": "USDT", "side": "None", "positionStatus": "Normal", "crossMargin": true, "leverageRr": "-10", "initMarginReqRr": "0.1", "maintMarginReqRr": "0.01", "riskLimitRv": "200000", "size": "0", "valueRv": "0", "avgEntryPriceRp": "0", "avgEntryPrice": "0", "posCostRv": "0", "assignedPosBalanceRv": "0", "bankruptCommRv": "0", "bankruptPriceRp": "0", "positionMarginRv": "0", "liquidationPriceRp": "0", "deleveragePercentileRr": "0", "buyValueToCostRr": "0.10114", "sellValueToCostRr": "0.10126", "markPriceRp": "0.*********", "estimatedOrdLossRv": "0", "usedBalanceRv": "3.64104", "cumClosedPnlRv": "0.3954", "cumFundingFeeRv": "-3.************", "cumTransactFeeRv": "0.********", "transactTimeNs": "1705478400006798467", "takerFeeRateRr": "0.0006", "makerFeeRateRr": "0.0001", "term": "2", "lastTermEndTimeNs": "1691221588568672749", "lastFundingTimeNs": "1705478400000000000", "curTermRealisedPnlRv": "3.************", "execSeq": "**********", "posSide": "<PERSON>", "posMode": "Hedged", "buyLeavesValueRv": "36", "sellLeavesValueRv": "0"}, {"userID": "940666", "accountID": "**********", "symbol": "XRPUSDT", "currency": "USDT", "side": "None", "positionStatus": "Normal", "crossMargin": true, "leverageRr": "-10", "initMarginReqRr": "0.1", "maintMarginReqRr": "0.01", "riskLimitRv": "200000", "size": "0", "valueRv": "0", "avgEntryPriceRp": "0", "avgEntryPrice": "0", "posCostRv": "0", "assignedPosBalanceRv": "0", "bankruptCommRv": "0", "bankruptPriceRp": "0", "positionMarginRv": "0", "liquidationPriceRp": "0", "deleveragePercentileRr": "0", "buyValueToCostRr": "0.10114", "sellValueToCostRr": "0.10126", "markPriceRp": "0.*********", "estimatedOrdLossRv": "0", "usedBalanceRv": "0", "cumClosedPnlRv": "0", "cumFundingFeeRv": "0", "cumTransactFeeRv": "0", "transactTimeNs": "1705478400006798467", "takerFeeRateRr": "0.0006", "makerFeeRateRr": "0.0001", "term": "1", "lastTermEndTimeNs": "0", "lastFundingTimeNs": "1705478400000000000", "curTermRealisedPnlRv": "0", "execSeq": "**********", "posSide": "Short", "posMode": "Hedged", "buyLeavesValueRv": "0", "sellLeavesValueRv": "0"}]}}, "parsedResponse": {"info": {"code": "0", "msg": "", "data": {"account": {"userID": "940666", "accountId": "**********", "currency": "USDT", "accountBalanceRv": "224.************", "totalUsedBalanceRv": "65.********", "bonusBalanceRv": "0", "status": "0"}, "positions": [{"userID": "940666", "accountID": "**********", "symbol": "LTCUSDT", "currency": "USDT", "side": "Buy", "positionStatus": "Normal", "crossMargin": true, "leverageRr": "-4", "initMarginReqRr": "0.25", "maintMarginReqRr": "0.01", "riskLimitRv": "200000", "size": "0.2", "valueRv": "14.227", "avgEntryPriceRp": "71.135", "avgEntryPrice": "71.135", "posCostRv": "3.********", "assignedPosBalanceRv": "4.********", "bankruptCommRv": "0.0000012", "bankruptPriceRp": "0.01", "positionMarginRv": "163.************", "liquidationPriceRp": "0.01", "deleveragePercentileRr": "0", "buyValueToCostRr": "0.25105", "sellValueToCostRr": "0.25135", "markPriceRp": "67.57", "estimatedOrdLossRv": "0", "usedBalanceRv": "9.********", "cumClosedPnlRv": "0.195", "cumFundingFeeRv": "-122.************", "cumTransactFeeRv": "0.0330384", "transactTimeNs": "1705478400007352000", "takerFeeRateRr": "0.0006", "makerFeeRateRr": "0.0001", "term": "2", "lastTermEndTimeNs": "1692347869378900410", "lastFundingTimeNs": "1705478400000000000", "curTermRealisedPnlRv": "-32.************", "execSeq": "**********", "posSide": "<PERSON>rged", "posMode": "OneWay", "buyLeavesValueRv": "21", "sellLeavesValueRv": "0"}, {"userID": "940666", "accountID": "**********", "symbol": "BTCUSDT", "currency": "USDT", "side": "Buy", "positionStatus": "Normal", "crossMargin": false, "leverageRr": "5", "initMarginReqRr": "0.2", "maintMarginReqRr": "0.0275", "riskLimitRv": "********", "size": "0.01", "valueRv": "259.897", "avgEntryPriceRp": "25989.7", "avgEntryPrice": "25989.7", "posCostRv": "52.********", "assignedPosBalanceRv": "52.********", "bankruptCommRv": "0.********", "bankruptPriceRp": "20791.8", "positionMarginRv": "51.9794", "liquidationPriceRp": "20921.8", "deleveragePercentileRr": "0", "buyValueToCostRr": "0.20108", "sellValueToCostRr": "0.20132", "markPriceRp": "39788.*********", "estimatedOrdLossRv": "0", "usedBalanceRv": "52.********", "cumClosedPnlRv": "-0.0404", "cumFundingFeeRv": "21.************", "cumTransactFeeRv": "0.********", "transactTimeNs": "1705478400006372515", "takerFeeRateRr": "0.0006", "makerFeeRateRr": "0.0001", "term": "2", "lastTermEndTimeNs": "1675002408016449676", "lastFundingTimeNs": "1705478400000000000", "curTermRealisedPnlRv": "-21.************", "execSeq": "**********", "posSide": "<PERSON>", "posMode": "Hedged", "buyLeavesValueRv": "0", "sellLeavesValueRv": "0"}, {"userID": "940666", "accountID": "**********", "symbol": "BTCUSDT", "currency": "USDT", "side": "None", "positionStatus": "Normal", "crossMargin": false, "leverageRr": "5", "initMarginReqRr": "0.2", "maintMarginReqRr": "0.0275", "riskLimitRv": "********", "size": "0", "valueRv": "0", "avgEntryPriceRp": "0", "avgEntryPrice": "0", "posCostRv": "0", "assignedPosBalanceRv": "0", "bankruptCommRv": "0", "bankruptPriceRp": "0", "positionMarginRv": "0", "liquidationPriceRp": "0", "deleveragePercentileRr": "0", "buyValueToCostRr": "0.20108", "sellValueToCostRr": "0.20132", "markPriceRp": "39788.*********", "estimatedOrdLossRv": "0", "usedBalanceRv": "0", "cumClosedPnlRv": "0", "cumFundingFeeRv": "0", "cumTransactFeeRv": "0", "transactTimeNs": "1705478400006372515", "takerFeeRateRr": "0.0006", "makerFeeRateRr": "0.0001", "term": "1", "lastTermEndTimeNs": "0", "lastFundingTimeNs": "1705478400000000000", "curTermRealisedPnlRv": "0", "execSeq": "**********", "posSide": "Short", "posMode": "Hedged", "buyLeavesValueRv": "0", "sellLeavesValueRv": "0"}, {"userID": "940666", "accountID": "**********", "symbol": "XRPUSDT", "currency": "USDT", "side": "None", "positionStatus": "Normal", "crossMargin": true, "leverageRr": "-10", "initMarginReqRr": "0.1", "maintMarginReqRr": "0.01", "riskLimitRv": "200000", "size": "0", "valueRv": "0", "avgEntryPriceRp": "0", "avgEntryPrice": "0", "posCostRv": "0", "assignedPosBalanceRv": "0", "bankruptCommRv": "0", "bankruptPriceRp": "0", "positionMarginRv": "0", "liquidationPriceRp": "0", "deleveragePercentileRr": "0", "buyValueToCostRr": "0.10114", "sellValueToCostRr": "0.10126", "markPriceRp": "0.*********", "estimatedOrdLossRv": "0", "usedBalanceRv": "3.64104", "cumClosedPnlRv": "0.3954", "cumFundingFeeRv": "-3.************", "cumTransactFeeRv": "0.********", "transactTimeNs": "1705478400006798467", "takerFeeRateRr": "0.0006", "makerFeeRateRr": "0.0001", "term": "2", "lastTermEndTimeNs": "1691221588568672749", "lastFundingTimeNs": "1705478400000000000", "curTermRealisedPnlRv": "3.************", "execSeq": "**********", "posSide": "<PERSON>", "posMode": "Hedged", "buyLeavesValueRv": "36", "sellLeavesValueRv": "0"}, {"userID": "940666", "accountID": "**********", "symbol": "XRPUSDT", "currency": "USDT", "side": "None", "positionStatus": "Normal", "crossMargin": true, "leverageRr": "-10", "initMarginReqRr": "0.1", "maintMarginReqRr": "0.01", "riskLimitRv": "200000", "size": "0", "valueRv": "0", "avgEntryPriceRp": "0", "avgEntryPrice": "0", "posCostRv": "0", "assignedPosBalanceRv": "0", "bankruptCommRv": "0", "bankruptPriceRp": "0", "positionMarginRv": "0", "liquidationPriceRp": "0", "deleveragePercentileRr": "0", "buyValueToCostRr": "0.10114", "sellValueToCostRr": "0.10126", "markPriceRp": "0.*********", "estimatedOrdLossRv": "0", "usedBalanceRv": "0", "cumClosedPnlRv": "0", "cumFundingFeeRv": "0", "cumTransactFeeRv": "0", "transactTimeNs": "1705478400006798467", "takerFeeRateRr": "0.0006", "makerFeeRateRr": "0.0001", "term": "1", "lastTermEndTimeNs": "0", "lastFundingTimeNs": "1705478400000000000", "curTermRealisedPnlRv": "0", "execSeq": "**********", "posSide": "Short", "posMode": "Hedged", "buyLeavesValueRv": "0", "sellLeavesValueRv": "0"}]}}, "USDT": {"free": 159.************, "used": 65.********, "total": 224.************}, "free": {"USDT": 159.************}, "used": {"USDT": 65.********}, "total": {"USDT": 224.************}}}], "fetchMyTrades": [{"description": "Spot trades", "method": "fetchMyTrades", "input": [null, 1], "httpResponse": {"code": "0", "msg": "OK", "data": [{"createdAt": "1700817297607", "symbol": "LTCUSDT", "currency": "USDT", "action": "1", "tradeType": "1", "execQtyRq": "0.1", "execPriceRp": "69.82", "side": "1", "orderQtyRq": "0.1", "priceRp": "71.82", "execValueRv": "6.982", "feeRateRr": "0.0006", "execFeeRv": "0.0041892", "ordType": "1", "execId": "d789fa5", "execStatus": "7", "posSide": "3", "ptFeeRv": "0", "ptPriceRp": "0"}]}, "parsedResponse": [{"info": {"createdAt": "1700817297607", "symbol": "LTCUSDT", "currency": "USDT", "action": "1", "tradeType": "1", "execQtyRq": "0.1", "execPriceRp": "69.82", "side": "1", "orderQtyRq": "0.1", "priceRp": "71.82", "execValueRv": "6.982", "feeRateRr": "0.0006", "execFeeRv": "0.0041892", "ordType": "1", "execId": "d789fa5", "execStatus": "7", "posSide": "3", "ptFeeRv": "0", "ptPriceRp": "0"}, "id": "d789fa5", "symbol": "LTC/USDT:USDT", "timestamp": 1700817297607, "datetime": "2023-11-24T09:14:57.607Z", "order": null, "type": "market", "side": "buy", "takerOrMaker": null, "price": 69.82, "amount": 0.1, "cost": 6.982, "fee": {"cost": 0.0041892, "rate": 0.0006, "currency": "USDT"}, "fees": [{"cost": 0.0041892, "rate": 0.0006, "currency": "USDT"}]}]}, {"description": "Swap trades", "method": "fetchMyTrades", "input": ["LTC/USDT:USDT", null, 1], "httpResponse": {"code": "0", "msg": "OK", "data": [{"createdAt": "1700817297607", "symbol": "LTCUSDT", "currency": "USDT", "action": "1", "tradeType": "1", "execQtyRq": "0.1", "execPriceRp": "69.82", "side": "1", "orderQtyRq": "0.1", "priceRp": "71.82", "execValueRv": "6.982", "feeRateRr": "0.0006", "execFeeRv": "0.0041892", "ordType": "1", "execId": "d789fa5", "execStatus": "7", "posSide": "3", "ptFeeRv": "0", "ptPriceRp": "0"}]}, "parsedResponse": [{"info": {"createdAt": "1700817297607", "symbol": "LTCUSDT", "currency": "USDT", "action": "1", "tradeType": "1", "execQtyRq": "0.1", "execPriceRp": "69.82", "side": "1", "orderQtyRq": "0.1", "priceRp": "71.82", "execValueRv": "6.982", "feeRateRr": "0.0006", "execFeeRv": "0.0041892", "ordType": "1", "execId": "d789fa5", "execStatus": "7", "posSide": "3", "ptFeeRv": "0", "ptPriceRp": "0"}, "id": "d789fa5", "symbol": "LTC/USDT:USDT", "timestamp": 1700817297607, "datetime": "2023-11-24T09:14:57.607Z", "order": null, "type": "market", "side": "buy", "takerOrMaker": null, "price": 69.82, "amount": 0.1, "cost": 6.982, "fee": {"cost": 0.0041892, "rate": 0.0006, "currency": "USDT"}, "fees": [{"cost": 0.0041892, "rate": 0.0006, "currency": "USDT"}]}]}], "fetchClosedOrders": [{"description": "spot closed order", "method": "fetchClosedOrders", "input": ["ETH/USDT", null, 1], "httpResponse": {"code": "0", "msg": "OK", "data": {"total": "10", "rows": [{"orderID": "aedf8004-17db-4fe1-a542-c605d7913f1e", "clOrdID": "CCXT123456cd834798e481b601", "stopPxEp": "0", "avgPriceEp": "297891000000", "qtyType": "ByQuote", "leavesBaseQtyEv": "0", "leavesQuoteQtyEv": "0", "baseQtyEv": "0", "feeCurrency": "ETH", "stopDirection": "UNSPECIFIED", "symbol": "sETHUSDT", "side": "Buy", "quoteQtyEv": "********00", "priceEp": "325600000000", "ordType": "Market", "timeInForce": "ImmediateOrCancel", "ordStatus": "Filled", "execStatus": "Taker<PERSON><PERSON>", "createTimeNs": "1715871268108003308", "cumFeeEv": "671", "cumBaseValueEv": "671000", "cumQuoteValueEv": "1998848610", "avgTransactPriceEp": "0", "spotOrderDetailsVos": null}]}}, "parsedResponse": [{"info": {"orderID": "aedf8004-17db-4fe1-a542-c605d7913f1e", "clOrdID": "CCXT123456cd834798e481b601", "stopPxEp": "0", "avgPriceEp": "297891000000", "qtyType": "ByQuote", "leavesBaseQtyEv": "0", "leavesQuoteQtyEv": "0", "baseQtyEv": "0", "feeCurrency": "ETH", "stopDirection": "UNSPECIFIED", "symbol": "sETHUSDT", "side": "Buy", "quoteQtyEv": "********00", "priceEp": "325600000000", "ordType": "Market", "timeInForce": "ImmediateOrCancel", "ordStatus": "Filled", "execStatus": "Taker<PERSON><PERSON>", "createTimeNs": "1715871268108003308", "cumFeeEv": "671", "cumBaseValueEv": "671000", "cumQuoteValueEv": "1998848610", "avgTransactPriceEp": "0", "spotOrderDetailsVos": null}, "id": "aedf8004-17db-4fe1-a542-c605d7913f1e", "clientOrderId": "CCXT123456cd834798e481b601", "timestamp": 1715871268108, "datetime": "2024-05-16T14:54:28.108Z", "lastTradeTimestamp": null, "symbol": "ETH/USDT", "type": "market", "timeInForce": "IOC", "postOnly": false, "side": "buy", "price": 3256, "stopPrice": null, "triggerPrice": null, "amount": 0.00671, "cost": 19.9884861, "average": 2978.91, "filled": 0.00671, "remaining": 0, "status": "closed", "fee": {"cost": "0.00000671", "currency": "ETH"}, "trades": [], "fees": [{"cost": 6.71e-06, "currency": "ETH"}], "lastUpdateTimestamp": null, "reduceOnly": null, "takeProfitPrice": null, "stopLossPrice": null}]}, {"description": "swap closed order", "warning": "status does not seem correct", "method": "fetchClosedOrders", "input": ["LTC/USDT:USDT", null, 1], "httpResponse": {"code": "0", "msg": "OK", "data": [{"createdAt": "1700818131681", "symbol": "LTCUSDT", "orderQtyRq": "0.2", "side": "1", "posSide": "3", "priceRp": "50", "execQtyRq": "0", "leavesQtyRq": "0.2", "execPriceRp": "56", "orderValueRv": "10", "leavesValueRv": "10", "cumValueRv": "0", "stopDirection": "0", "stopPxRp": "0", "trigger": "0", "actionBy": "0", "execFeeRv": "0", "ordType": "2", "ordStatus": "5", "clOrdId": "f19a110", "orderId": "f19a1108-8dc0-4afb-b281-0cd2c8ca65d9", "execStatus": "18", "bizError": "0", "totalPnlRv": null, "avgTransactPriceRp": null, "orderDetailsVos": null, "tradeType": "10", "updatedAt": "1700818131683"}]}, "parsedResponse": [{"info": {"createdAt": "1700818131681", "symbol": "LTCUSDT", "orderQtyRq": "0.2", "side": "1", "posSide": "3", "priceRp": "50", "execQtyRq": "0", "leavesQtyRq": "0.2", "execPriceRp": "56", "orderValueRv": "10", "leavesValueRv": "10", "cumValueRv": "0", "stopDirection": "0", "stopPxRp": "0", "trigger": "0", "actionBy": "0", "execFeeRv": "0", "ordType": "2", "ordStatus": "5", "clOrdId": "f19a110", "orderId": "f19a1108-8dc0-4afb-b281-0cd2c8ca65d9", "execStatus": "18", "bizError": "0", "totalPnlRv": null, "avgTransactPriceRp": null, "orderDetailsVos": null, "tradeType": "10", "updatedAt": "1700818131683"}, "id": "f19a1108-8dc0-4afb-b281-0cd2c8ca65d9", "clientOrderId": "f19a110", "datetime": "2023-11-24T09:28:51.681Z", "timestamp": 1700818131681, "lastTradeTimestamp": null, "symbol": "LTC/USDT:USDT", "type": null, "timeInForce": null, "postOnly": false, "reduceOnly": null, "side": "buy", "price": 50, "stopPrice": null, "triggerPrice": null, "takeProfitPrice": null, "stopLossPrice": null, "amount": 0.2, "filled": 0, "remaining": 0.2, "cost": 0, "average": null, "status": "open", "fee": null, "trades": [], "fees": [], "lastUpdateTimestamp": null}]}, {"description": "swap closed orders without symbol", "method": "fetchClosedOrders", "input": [], "httpResponse": {"code": "0", "msg": "OK", "data": [{"createdAt": "1706652904353", "symbol": "LTCUSDT", "orderQtyRq": "0.5", "side": "1", "posSide": "3", "priceRp": "56", "execQtyRq": "0", "leavesQtyRq": "0.5", "execPriceRp": "0", "orderValueRv": "28", "leavesValueRv": "28", "cumValueRv": "0", "stopDirection": "0", "stopPxRp": "0", "trigger": "0", "actionBy": "1", "execFeeRv": "0", "ordType": "2", "ordStatus": "5", "clOrdId": "1405a8f", "orderId": "1405a8f6-6d44-4c9a-839f-11bf9c5f1833", "execStatus": "5", "bizError": "0", "totalPnlRv": null, "avgTransactPriceRp": null, "orderDetailsVos": null, "tradeType": "0", "updatedAt": "1706652904356"}]}, "parsedResponse": [{"info": {"createdAt": "1706652904353", "symbol": "LTCUSDT", "orderQtyRq": "0.5", "side": "1", "posSide": "3", "priceRp": "56", "execQtyRq": "0", "leavesQtyRq": "0.5", "execPriceRp": "0", "orderValueRv": "28", "leavesValueRv": "28", "cumValueRv": "0", "stopDirection": "0", "stopPxRp": "0", "trigger": "0", "actionBy": "1", "execFeeRv": "0", "ordType": "2", "ordStatus": "5", "clOrdId": "1405a8f", "orderId": "1405a8f6-6d44-4c9a-839f-11bf9c5f1833", "execStatus": "5", "bizError": "0", "totalPnlRv": null, "avgTransactPriceRp": null, "orderDetailsVos": null, "tradeType": "0", "updatedAt": "1706652904356"}, "id": "1405a8f6-6d44-4c9a-839f-11bf9c5f1833", "clientOrderId": "1405a8f", "datetime": "2024-01-30T22:15:04.353Z", "timestamp": 1706652904353, "lastTradeTimestamp": null, "symbol": "LTC/USDT:USDT", "type": null, "timeInForce": null, "postOnly": false, "reduceOnly": null, "side": "buy", "price": 56, "stopPrice": null, "triggerPrice": null, "takeProfitPrice": null, "stopLossPrice": null, "amount": 0.5, "filled": 0, "remaining": 0.5, "cost": 0, "average": null, "status": "open", "fee": null, "trades": [], "fees": [], "lastUpdateTimestamp": null}]}], "fetchPositions": [{"description": "fetch linear usd positions", "method": "fetchPositions", "input": [["LTC/USDT:USDT"]], "httpResponse": {"code": "0", "msg": "", "data": {"account": {"userID": "940666", "accountId": "**********", "currency": "USDT", "accountBalanceRv": "224.************", "totalUsedBalanceRv": "65.*********", "bonusBalanceRv": "0", "status": "0"}, "positions": [{"userID": "940666", "accountID": "**********", "symbol": "LTCUSDT", "currency": "USDT", "side": "Buy", "positionStatus": "Normal", "crossMargin": true, "leverageRr": "-4", "initMarginReqRr": "0.25", "maintMarginReqRr": "0.01", "riskLimitRv": "200000", "size": "0.2", "valueRv": "14.227", "avgEntryPriceRp": "71.135", "avgEntryPrice": "71.135", "posCostRv": "3.********", "assignedPosBalanceRv": "4.*********", "bankruptCommRv": "0.0000012", "bankruptPriceRp": "0.01", "positionMarginRv": "163.************", "liquidationPriceRp": "0.01", "deleveragePercentileRr": "0", "buyValueToCostRr": "0.25105", "sellValueToCostRr": "0.25135", "markPriceRp": "65.*********", "estimatedOrdLossRv": "0", "usedBalanceRv": "9.*********", "cumClosedPnlRv": "0.195", "cumFundingFeeRv": "-122.************", "cumTransactFeeRv": "0.0330384", "transactTimeNs": "1706256000006322984", "takerFeeRateRr": "0.0006", "makerFeeRateRr": "0.0001", "term": "2", "lastTermEndTimeNs": "1692347869378900410", "lastFundingTimeNs": "1706256000000000000", "curTermRealisedPnlRv": "-32.************", "execSeq": "**********", "posSide": "<PERSON>rged", "posMode": "OneWay", "buyLeavesValueRv": "21", "sellLeavesValueRv": "0"}, {"userID": "940666", "accountID": "**********", "symbol": "BTCUSDT", "currency": "USDT", "side": "Buy", "positionStatus": "Normal", "crossMargin": false, "leverageRr": "5", "initMarginReqRr": "0.2", "maintMarginReqRr": "0.0275", "riskLimitRv": "********", "size": "0.01", "valueRv": "259.897", "avgEntryPriceRp": "25989.7", "avgEntryPrice": "25989.7", "posCostRv": "52.********", "assignedPosBalanceRv": "52.********", "bankruptCommRv": "0.********", "bankruptPriceRp": "20791.8", "positionMarginRv": "51.9794", "liquidationPriceRp": "20921.8", "deleveragePercentileRr": "0", "buyValueToCostRr": "0.20108", "sellValueToCostRr": "0.20132", "markPriceRp": "39970.********", "estimatedOrdLossRv": "0", "usedBalanceRv": "52.********", "cumClosedPnlRv": "-0.0404", "cumFundingFeeRv": "21.************", "cumTransactFeeRv": "0.********", "transactTimeNs": "1706256000005523721", "takerFeeRateRr": "0.0006", "makerFeeRateRr": "0.0001", "term": "2", "lastTermEndTimeNs": "1675002408016449676", "lastFundingTimeNs": "1706256000000000000", "curTermRealisedPnlRv": "-21.************", "execSeq": "**********", "posSide": "<PERSON>", "posMode": "Hedged", "buyLeavesValueRv": "0", "sellLeavesValueRv": "0"}, {"userID": "940666", "accountID": "**********", "symbol": "BTCUSDT", "currency": "USDT", "side": "None", "positionStatus": "Normal", "crossMargin": false, "leverageRr": "5", "initMarginReqRr": "0.2", "maintMarginReqRr": "0.0275", "riskLimitRv": "********", "size": "0", "valueRv": "0", "avgEntryPriceRp": "0", "avgEntryPrice": "0", "posCostRv": "0", "assignedPosBalanceRv": "0", "bankruptCommRv": "0", "bankruptPriceRp": "0", "positionMarginRv": "0", "liquidationPriceRp": "0", "deleveragePercentileRr": "0", "buyValueToCostRr": "0.20108", "sellValueToCostRr": "0.20132", "markPriceRp": "39970.********", "estimatedOrdLossRv": "0", "usedBalanceRv": "0", "cumClosedPnlRv": "0", "cumFundingFeeRv": "0", "cumTransactFeeRv": "0", "transactTimeNs": "1706256000005523721", "takerFeeRateRr": "0.0006", "makerFeeRateRr": "0.0001", "term": "1", "lastTermEndTimeNs": "0", "lastFundingTimeNs": "1706256000000000000", "curTermRealisedPnlRv": "0", "execSeq": "**********", "posSide": "Short", "posMode": "Hedged", "buyLeavesValueRv": "0", "sellLeavesValueRv": "0"}, {"userID": "940666", "accountID": "**********", "symbol": "XRPUSDT", "currency": "USDT", "side": "None", "positionStatus": "Normal", "crossMargin": true, "leverageRr": "-10", "initMarginReqRr": "0.1", "maintMarginReqRr": "0.01", "riskLimitRv": "200000", "size": "0", "valueRv": "0", "avgEntryPriceRp": "0", "avgEntryPrice": "0", "posCostRv": "0", "assignedPosBalanceRv": "0", "bankruptCommRv": "0", "bankruptPriceRp": "0", "positionMarginRv": "0", "liquidationPriceRp": "0", "deleveragePercentileRr": "0", "buyValueToCostRr": "0.10114", "sellValueToCostRr": "0.10126", "markPriceRp": "0.*********", "estimatedOrdLossRv": "0", "usedBalanceRv": "3.64104", "cumClosedPnlRv": "0.3954", "cumFundingFeeRv": "-3.************", "cumTransactFeeRv": "0.********", "transactTimeNs": "1706256000006016936", "takerFeeRateRr": "0.0006", "makerFeeRateRr": "0.0001", "term": "2", "lastTermEndTimeNs": "1691221588568672749", "lastFundingTimeNs": "1706256000000000000", "curTermRealisedPnlRv": "3.************", "execSeq": "**********", "posSide": "<PERSON>", "posMode": "Hedged", "buyLeavesValueRv": "36", "sellLeavesValueRv": "0"}, {"userID": "940666", "accountID": "**********", "symbol": "XRPUSDT", "currency": "USDT", "side": "None", "positionStatus": "Normal", "crossMargin": true, "leverageRr": "-10", "initMarginReqRr": "0.1", "maintMarginReqRr": "0.01", "riskLimitRv": "200000", "size": "0", "valueRv": "0", "avgEntryPriceRp": "0", "avgEntryPrice": "0", "posCostRv": "0", "assignedPosBalanceRv": "0", "bankruptCommRv": "0", "bankruptPriceRp": "0", "positionMarginRv": "0", "liquidationPriceRp": "0", "deleveragePercentileRr": "0", "buyValueToCostRr": "0.10114", "sellValueToCostRr": "0.10126", "markPriceRp": "0.*********", "estimatedOrdLossRv": "0", "usedBalanceRv": "0", "cumClosedPnlRv": "0", "cumFundingFeeRv": "0", "cumTransactFeeRv": "0", "transactTimeNs": "1706256000006016936", "takerFeeRateRr": "0.0006", "makerFeeRateRr": "0.0001", "term": "1", "lastTermEndTimeNs": "0", "lastFundingTimeNs": "1706256000000000000", "curTermRealisedPnlRv": "0", "execSeq": "**********", "posSide": "Short", "posMode": "Hedged", "buyLeavesValueRv": "0", "sellLeavesValueRv": "0"}]}}, "parsedResponse": [{"info": {"userID": "940666", "accountID": "**********", "symbol": "LTCUSDT", "currency": "USDT", "side": "Buy", "positionStatus": "Normal", "crossMargin": true, "leverageRr": "-4", "initMarginReqRr": "0.25", "maintMarginReqRr": "0.01", "riskLimitRv": "200000", "size": "0.2", "valueRv": "14.227", "avgEntryPriceRp": "71.135", "avgEntryPrice": "71.135", "posCostRv": "3.********", "assignedPosBalanceRv": "4.*********", "bankruptCommRv": "0.0000012", "bankruptPriceRp": "0.01", "positionMarginRv": "163.************", "liquidationPriceRp": "0.01", "deleveragePercentileRr": "0", "buyValueToCostRr": "0.25105", "sellValueToCostRr": "0.25135", "markPriceRp": "65.*********", "estimatedOrdLossRv": "0", "usedBalanceRv": "9.*********", "cumClosedPnlRv": "0.195", "cumFundingFeeRv": "-122.************", "cumTransactFeeRv": "0.0330384", "transactTimeNs": "1706256000006322984", "takerFeeRateRr": "0.0006", "makerFeeRateRr": "0.0001", "term": "2", "lastTermEndTimeNs": "1692347869378900410", "lastFundingTimeNs": "1706256000000000000", "curTermRealisedPnlRv": "-32.************", "execSeq": "**********", "posSide": "<PERSON>rged", "posMode": "OneWay", "buyLeavesValueRv": "21", "sellLeavesValueRv": "0"}, "id": null, "symbol": "LTC/USDT:USDT", "contracts": 0.2, "contractSize": 1, "unrealizedPnl": -0.0002411514051876232, "leverage": 4, "liquidationPrice": 0.01, "collateral": 163.************, "notional": 14.227, "markPrice": 65.*********, "lastPrice": null, "entryPrice": 71.135, "timestamp": null, "lastUpdateTimestamp": null, "initialMargin": 4.*********, "initialMarginPercentage": 0.32944592500175723, "maintenanceMargin": 0.14227, "maintenanceMarginPercentage": 0.01, "marginRatio": 0.000870607377518708, "datetime": null, "marginMode": "cross", "side": "long", "hedged": false, "percentage": null, "stopLossPrice": null, "takeProfitPrice": null}]}], "fetchOpenOrders": [{"description": "open swap orders", "method": "fetchOpenOrders", "input": ["LTC/USDT:USDT", null, 1], "httpResponse": {"code": "0", "msg": "", "data": {"rows": [{"bizError": "0", "orderID": "1405a8f6-6d44-4c9a-839f-11bf9c5f1833", "clOrdID": "CCXT123456f7f27f45a490907c", "symbol": "LTCUSDT", "side": "Buy", "actionTimeNs": "1706652904353349851", "transactTimeNs": "1706652904356479329", "orderType": "Limit", "priceRp": "56", "orderQtyRq": "0.5", "displayQtyRq": "0.5", "timeInForce": "GoodTillCancel", "closedPnlRv": "0", "closedSizeRq": "0", "cumQtyRq": "0", "cumValueRv": "0", "leavesQtyRq": "0.5", "leavesValueRv": "28", "stopDirection": "UNSPECIFIED", "stopPxRp": "0", "trigger": "UNSPECIFIED", "pegOffsetValueRp": "0", "pegOffsetProportionRr": "0", "execStatus": "New", "pegPriceType": "UNSPECIFIED", "ordStatus": "New", "execInst": "None", "takeProfitRp": "0", "stopLossRp": "0", "slPxRp": "0", "tpPxRp": "0"}], "nextPageArg": ""}}, "parsedResponse": [{"info": {"bizError": "0", "orderID": "1405a8f6-6d44-4c9a-839f-11bf9c5f1833", "clOrdID": "CCXT123456f7f27f45a490907c", "symbol": "LTCUSDT", "side": "Buy", "actionTimeNs": "1706652904353349851", "transactTimeNs": "1706652904356479329", "orderType": "Limit", "priceRp": "56", "orderQtyRq": "0.5", "displayQtyRq": "0.5", "timeInForce": "GoodTillCancel", "closedPnlRv": "0", "closedSizeRq": "0", "cumQtyRq": "0", "cumValueRv": "0", "leavesQtyRq": "0.5", "leavesValueRv": "28", "stopDirection": "UNSPECIFIED", "stopPxRp": "0", "trigger": "UNSPECIFIED", "pegOffsetValueRp": "0", "pegOffsetProportionRr": "0", "execStatus": "New", "pegPriceType": "UNSPECIFIED", "ordStatus": "New", "execInst": "None", "takeProfitRp": "0", "stopLossRp": "0", "slPxRp": "0", "tpPxRp": "0"}, "id": "1405a8f6-6d44-4c9a-839f-11bf9c5f1833", "clientOrderId": "CCXT123456f7f27f45a490907c", "datetime": "2024-01-30T22:15:04.353Z", "timestamp": 1706652904353, "lastTradeTimestamp": 1706652904356, "symbol": "LTC/USDT:USDT", "type": "limit", "timeInForce": "GTC", "postOnly": false, "reduceOnly": null, "side": "buy", "price": 56, "stopPrice": null, "triggerPrice": null, "takeProfitPrice": 0, "stopLossPrice": 0, "amount": 0.5, "filled": 0, "remaining": 0.5, "cost": 0, "average": null, "status": "open", "fee": null, "trades": [], "fees": [], "lastUpdateTimestamp": null}]}], "fetchOrders": [{"description": "swap order", "method": "fetchOrders", "input": ["BTC/USDT:USDT"], "httpResponse": {"code": "0", "msg": "OK", "data": [{"createdAt": "1715818144087", "symbol": "BTCUSDT", "orderQtyRq": "0.01", "side": "1", "posSide": "2", "priceRp": "75326.2", "execQtyRq": "0.01", "leavesQtyRq": "0", "execPriceRp": "66573.7", "orderValueRv": "665.737", "leavesValueRv": "0", "cumValueRv": "665.737", "stopDirection": "1", "stopPxRp": "66500", "trigger": "1", "actionBy": "4", "execFeeRv": "0.36615535", "ordType": "3", "ordStatus": "7", "clOrdId": "XXXXXXXXXXXXXXXXXXXXXX", "orderId": "XXXXXXXXXXXXXXXXXXXXXX", "execStatus": "7", "bizError": "0", "totalPnlRv": null, "avgTransactPriceRp": null, "orderDetailsVos": null, "tradeType": "1", "updatedAt": "1715818144091"}]}, "parsedResponse": [{"info": {"createdAt": "1715818144087", "symbol": "BTCUSDT", "orderQtyRq": "0.01", "side": "1", "posSide": "2", "priceRp": "75326.2", "execQtyRq": "0.01", "leavesQtyRq": "0", "execPriceRp": "66573.7", "orderValueRv": "665.737", "leavesValueRv": "0", "cumValueRv": "665.737", "stopDirection": "1", "stopPxRp": "66500", "trigger": "1", "actionBy": "4", "execFeeRv": "0.36615535", "ordType": "3", "ordStatus": "7", "clOrdId": "XXXXXXXXXXXXXXXXXXXXXX", "orderId": "XXXXXXXXXXXXXXXXXXXXXX", "execStatus": "7", "bizError": "0", "totalPnlRv": null, "avgTransactPriceRp": null, "orderDetailsVos": null, "tradeType": "1", "updatedAt": "1715818144091"}, "id": "XXXXXXXXXXXXXXXXXXXXXX", "clientOrderId": "XXXXXXXXXXXXXXXXXXXXXX", "datetime": "2024-05-16T00:09:04.087Z", "timestamp": 1715818144087, "lastTradeTimestamp": null, "symbol": "BTC/USDT:USDT", "type": null, "timeInForce": null, "postOnly": false, "reduceOnly": null, "side": "buy", "price": 75326.2, "stopPrice": 66500, "triggerPrice": 66500, "takeProfitPrice": null, "stopLossPrice": null, "amount": 0.01, "filled": 0.01, "remaining": 0, "cost": 665.737, "average": 66573.7, "status": "closed", "fee": {"cost": "0.36615535", "currency": "USDT"}, "trades": [], "fees": [{"cost": 0.36615535, "currency": "USDT"}], "lastUpdateTimestamp": null}]}], "fetchTrades": [{"description": "public spot trades", "method": "fetchTrades", "input": ["BTC/USDT", null, 1], "httpResponse": {"error": null, "id": "0", "result": {"sequence": "32023030726", "symbol": "sBTCUSDT", "trades": [[1710327659222403800, 320230307260000, "Buy", 7325244000000, 700000], [1710327654383409000, 320230306650000, "Buy", 7325228000000, 5700], [1710327654224844500, 320230306640000, "Buy", 7325228000000, 158000], [1710327652309884700, 320230306320000, "Buy", 7325228000000, 271000], [1710327651857980400, 320230306240000, "Buy", 7325228000000, 815000], [1710327650038197200, 320230305530001, "Buy", 7325219000000, 3400], [1710327650038197200, 320230305530000, "Buy", 7325206000000, 3200], [1710327648412162300, 320230305310000, "Buy", 7325206000000, 3600], [1710327647906894800, 320230305240000, "Buy", 7325206000000, 425000], [1710327643111199500, 320230297270000, "Buy", 7325206000000, 2311000], [1710327638939466800, 320230295060000, "Buy", 7325206000000, 2112500], [1710327636148289000, 320230294600000, "Buy", 7325207000000, 2061300], [1710327636025059800, 320230294540000, "Buy", 7326006000000, 1341800], [1710327634047626500, 320230293130000, "Buy", 7326006000000, 3300], [1710327628582952400, 320230292730000, "Buy", 7326006000000, 8800], [1710327620569884200, 320230289850000, "<PERSON>ll", 7326021000000, 1045900], [1710327620569394700, 320230289840000, "<PERSON>ll", 7326021000000, 1100000], [1710327620420942800, 320230289810000, "Buy", 7326958000000, 10000], [1710327618427439900, 320230288380000, "<PERSON>ll", 7326012000000, 2326700], [1710327616435153000, 320230288020000, "<PERSON>ll", 7326008000000, 1931000], [1710327604079295700, 320230279440000, "Buy", 7325007000000, 2973600], [1710327602513800700, 320230279300000, "Buy", 7325008000000, 2473000], [1710327583314134800, 320230263800000, "<PERSON>ll", 7324643000000, 1289000], [1710327579432027400, 320230263430000, "<PERSON>ll", 7324914000000, 5300], [1710327577964335000, 320230262070000, "<PERSON>ll", 7324910000000, 1790100], [1710327575057033200, 320230260540000, "<PERSON>ll", 7324891000000, 1200000], [1710327572385402400, 320230260090000, "<PERSON>ll", 7324884000000, 70000], [1710327568831905000, 320230259390000, "<PERSON>ll", 7324873000000, 170000], [1710327567470711600, 320230259210000, "<PERSON>ll", 7324869000000, 609600], [1710327563168380400, 320230258510000, "<PERSON>ll", 7324854000000, 426500], [1710327562992228400, 320230258460000, "<PERSON>ll", 7324854000000, 200000], [1710327559843769600, 320230257160000, "<PERSON>ll", 7324843000000, 100000], [1710327557307149600, 320230256600000, "<PERSON>ll", 7324836000000, 180000], [1710327556724597500, 320230256590000, "<PERSON>ll", 7324836000000, 722000], [1710327552105566000, 320230254930000, "<PERSON>ll", 7324821000000, 52500], [1710327551115566300, 320230254840000, "<PERSON>ll", 7324818000000, 108000], [1710327549715263200, 320230254350000, "<PERSON>ll", 7324814000000, 846000], [1710327549714790000, 320230254340000, "<PERSON>ll", 7324814000000, 47000], [1710327547635469300, 320230253010000, "<PERSON>ll", 7324806000000, 600000], [1710327543530886400, 320230251670000, "<PERSON>ll", 7324794000000, 849500], [1710327539163715800, 320230249740000, "<PERSON>ll", 7324779000000, 1316000], [1710327528284586000, 320230244420000, "<PERSON>ll", 7324745000000, 1115000], [1710327518726942000, 320230238810000, "<PERSON>ll", 7324714000000, 715000], [1710327515082844000, 320230232100000, "<PERSON>ll", 7324703000000, 967000], [1710327513252094000, 320230231940000, "<PERSON>ll", 7324698000000, 1139000], [1710327508819694300, 320230231110000, "<PERSON>ll", 7324681000000, 436000], [1710327505514304500, 320230230030000, "<PERSON>ll", 7324674000000, 1370500], [1710327504555563500, 320230229950000, "<PERSON>ll", 7324670000000, 1000000], [1710327503398353200, 320230229560000, "<PERSON>ll", 7324667000000, 2531600], [1710327502207786200, 320230229300000, "<PERSON>ll", 7324663000000, 2453900], [1710327501547015400, 320230229250000, "<PERSON>ll", 7324659000000, 2110900], [1710327499749869800, 320230229160000, "<PERSON>ll", 7324655000000, 2237300], [1710327488721579800, 320230225030000, "<PERSON>ll", 7324651000000, 1610700], [1710327482471870200, 320230219390000, "Buy", 73241********, 448400], [1710327479318025500, 320230218480000, "Buy", 73241********, 1029000], [1710327477710935000, 320230218330000, "Buy", 73241********, 570000], [1710327476917756700, 320230218310000, "Buy", 73241********, 747000], [1710327473237977300, 320230217310000, "Buy", 7324111000000, 3400], [1710327472114694000, 320230216210000, "Buy", 7324111000000, 2012500], [1710327467944715000, 320230214550000, "<PERSON>ll", 7324028000000, 338800], [1710327459437821200, 320230213150000, "<PERSON>ll", 7324284000000, 210000], [1710327456441413400, 320230205850000, "Buy", 7324327000000, 990000], [1710327454700223000, 320230205660000, "<PERSON>ll", 7324284000000, 122500], [1710327449929905700, 320230203910000, "<PERSON>ll", 7324832000000, 295000], [1710327449549176800, 320230203620000, "<PERSON>ll", 7324790000000, 375000], [1710327447027385900, 320230203080000, "<PERSON>ll", 7324874000000, 950000], [1710327442525494300, 320230201170000, "<PERSON>ll", 7325559000000, 870000], [1710327441668256500, 320230200330000, "<PERSON>ll", 7325559000000, 1371800], [1710327439350127000, 320230********0, "Buy", 7325602000000, 2040900], [1710327437727240400, 320230199350000, "<PERSON>ll", 7326503000000, 760000], [1710327436501021000, 320230198230000, "Buy", 7326546000000, 936700], [1710327434245627000, 320230197470000, "Buy", 7326546000000, 1237500], [1710327431058480400, 320230196250000, "Buy", 7327101000000, 623000], [1710327428321135000, 320230194860000, "<PERSON>ll", 7327052000000, 135000], [1710327428287190800, 320230194810000, "<PERSON>ll", 7327052000000, 15700], [1710327425127649800, 320230193860000, "<PERSON>ll", 7327041000000, 650000], [1710327424037638400, 320230192790000, "Buy", 7327131000000, 650000], [1710327420568382000, 320230191040000, "<PERSON>ll", 7327028000000, 1395000], [1710327416811026400, 320230189200000, "<PERSON>ll", 7325726000000, 1600000], [1710327411869936400, 320230187300000, "<PERSON>ll", 7325712000000, 600000], [1710327410686397200, 320230185720000, "<PERSON>ll", 7325841000000, 819200], [1710327404365182700, 320230181270000, "Buy", 7326546000000, 850000], [1710327400575265500, 320230178650000, "<PERSON>ll", 7326380000000, 637000], [1710327396112901600, 320230178210000, "<PERSON>ll", 7326365000000, 1456700], [1710327392031563300, 320230170690000, "<PERSON>ll", 73257********, 866000], [1710327392031094800, 320230170680000, "<PERSON>ll", 73257********, 220000], [1710327390313303800, 320230170400000, "<PERSON>ll", 7325715000000, 1741100], [1710327383032352800, 320230168740000, "<PERSON>ll", 7325651000000, 632500], [1710327380757318400, 320230168480000, "<PERSON>ll", 7326091000000, 2179700], [1710327379553119500, 320230168280000, "<PERSON>ll", 7326316000000, 2112200], [1710327375987107000, 320230166730000, "<PERSON>ll", 7326305000000, 2870900], [1710327371969702400, 320230162980000, "<PERSON>ll", 7327359000000, 2531500], [1710327371344145400, 320230162720000, "Buy", 7327615000000, 1808000], [1710327368582987800, 320230161210000, "<PERSON>ll", 7327349000000, 2187100], [1710327365632503800, 320230159530000, "<PERSON>ll", 7327888000000, 1741600], [1710327363074651000, 320230156950000, "Buy", 7329580000000, 18000], [1710327362582330400, 320230156920000, "<PERSON>ll", 7329298000000, 965700], [1710327358608460500, 320230156110000, "<PERSON>ll", 7329283000000, 137500], [1710327357587921700, 320230156040000, "<PERSON>ll", 7329279000000, 1538000], [1710327355076464000, 320230155830000, "<PERSON>ll", 7327206000000, 1808300], [1710327352170371800, 320230154440000, "<PERSON>ll", 7328806000000, 2845400], [1710327350813514200, 320230154020000, "<PERSON>ll", 7328723000000, 2411100], [1710327347374990800, 320230151810000, "<PERSON>ll", 7328280000000, 2544800], [1710327342449134000, 320230150010001, "<PERSON>ll", 7327206000000, 44100], [1710327342449134000, 320230150010000, "<PERSON>ll", 7327655000000, 2161900], [1710327339281104000, 320230149100000, "<PERSON>ll", 7327198000000, 832500], [1710327335069320200, 320230148400000, "<PERSON>ll", 7327184000000, 1040000], [1710327333326670300, 320230148290000, "<PERSON>ll", 7327180000000, 2230000], [1710327328882155000, 320230141340000, "<PERSON>ll", 7327165000000, 73200], [1710327328257635600, 320230141300000, "<PERSON>ll", 7327165000000, 371500], [1710327326502183200, 320230141090000, "<PERSON>ll", 7327158000000, 628600], [1710327324484335400, 320230139810000, "<PERSON>ll", 7327151000000, 172200], [1710327321805438200, 320230139190000, "<PERSON>ll", 7327144000000, 299000], [1710327320705493200, 320230138530000, "<PERSON>ll", 7327140000000, 2025000], [1710327316849737200, 320230137960000, "<PERSON>ll", 7327129000000, 304000], [1710327316109130800, 320230137820000, "<PERSON>ll", 7327126000000, 476000], [1710327313581921800, 320230137500000, "<PERSON>ll", 7327119000000, 1373500], [1710327311389942500, 320230137160000, "<PERSON>ll", 7327111000000, 1656000], [1710327309796180500, 320230136990000, "<PERSON>ll", 7327108000000, 2149400], [1710327308880336100, 320230136750000, "<PERSON>ll", 7327046000000, 1798200], [1710327305611587300, 320230134820000, "<PERSON>ll", 7325621000000, 1051000], [1710327301480321300, 320230133680000, "<PERSON>ll", 7326073000000, 242500], [1710327297785313000, 320230133120000, "<PERSON>ll", 7326059000000, 130000], [1710327295794957000, 320230132570000, "<PERSON>ll", 7326052000000, 775000], [1710327291037118500, 320230131300000, "<PERSON>ll", 7326038000000, 597000], [1710327290651558700, 320230131270000, "<PERSON>ll", 7326033000000, 2176600], [1710327286842375400, 320230130230000, "<PERSON>ll", 7326023000000, 289700], [1710327285443236000, 320230130030000, "<PERSON>ll", 7325482000000, 486000], [1710327282368833000, 320230129570000, "<PERSON>ll", 7325475000000, 395000], [1710327281409672700, 320230129180000, "<PERSON>ll", 7326005000000, 561000], [1710327276907918800, 320230127330000, "<PERSON>ll", 7325991000000, 2250000], [1710327273202164200, 320230124890000, "<PERSON>ll", 7325979000000, 308500], [1710327269171994600, 320230122590000, "<PERSON>ll", 7325968000000, 1042500], [1710327264201932800, 320230114900000, "<PERSON>ll", 7325358000000, 1434000], [1710327259372338700, 320230113090000, "<PERSON>ll", 7325510000000, 1612000], [1710327255474692400, 320230112260000, "<PERSON>ll", 7325440000000, 1155000], [1710327254295222800, 320230111580000, "<PERSON>ll", 7325439000000, 1125000], [1710327251896942000, 320230111160000, "<PERSON>ll", 7324798000000, 420000], [1710327248886142000, 320230110050000, "<PERSON>ll", 7324786000000, 425000], [1710327247485866500, 320230109780000, "<PERSON>ll", 7325419000000, 946600], [1710327243203680300, 320230107740000, "<PERSON>ll", 7325405000000, 368000], [1710327240200666600, 320230107320000, "<PERSON>ll", 7325394000000, 180000], [1710327236928078300, 320230106000000, "<PERSON>ll", 7325387000000, 2274300], [1710327232274899700, 320230104800000, "<PERSON>ll", 7324625000000, 990000], [1710327230344532200, 320230101930000, "<PERSON>ll", 7324612000000, 100000], [1710327229340396500, 320230101830000, "<PERSON>ll", 7324608000000, 300000], [1710327227323929000, 320230101410000, "<PERSON>ll", 7324605000000, 2042000], [1710327223115150800, 320230100180000, "<PERSON>ll", 7324589000000, 700000], [1710327219499443000, 320230098340000, "<PERSON>ll", 7324534000000, 2092000], [1710327215379608300, 320230097380000, "<PERSON>ll", 7323781000000, 2358100], [1710327211805483300, 320230096150000, "<PERSON>ll", 7324510000000, 2088800], [1710327209864997400, 320230095330000, "<PERSON>ll", 7324506000000, 2059000], [1710327204399823600, 320230086720000, "<PERSON>ll", 7323674000000, 1300000], [1710327199771952600, 320230085170000, "<PERSON>ll", 7323659000000, 58200], [1710327199285976600, 320230084490000, "<PERSON>ll", 7323659000000, 1210000], [1710327194865250600, 320230082880000, "<PERSON>ll", 7323646000000, 290500], [1710327193751693000, 320230082740000, "<PERSON>ll", 7323643000000, 185000], [1710327193103826400, 320230082690000, "<PERSON>ll", 7323639000000, 1250000], [1710327189261681700, 320230082150000, "Buy", 7325262000000, 507500], [1710327184919584000, 320230077770000, "<PERSON>ll", 7324854000000, 680000], [1710327184471734300, 320230077740000, "<PERSON>ll", 7324854000000, 115500], [1710327182768511200, 320230077050000, "<PERSON>ll", 7324847000000, 350000], [1710327181382965000, 320230076570000, "<PERSON>ll", 7324843000000, 600500], [1710327179841233400, 320230073900000, "<PERSON>ll", 7325686000000, 823200], [1710327176485964800, 320230073240000, "<PERSON>ll", 7325677000000, 2102700], [1710327175781463000, 320230073040000, "<PERSON>ll", 7325677000000, 30200], [1710327174649133600, 320230072940000, "<PERSON>ll", 7325674000000, 2234100], [1710327170598638800, 320230069930000, "<PERSON>ll", 7324839000000, 507000], [1710327168874452200, 320230069290000, "<PERSON>ll", 7324835000000, 1933000], [1710327164988181000, 320230066510000, "<PERSON>ll", 7324107000000, 33000], [1710327162807775500, 320230066220000, "<PERSON>ll", 7324098000000, 1522000], [1710327161477118200, 320230064960000, "<PERSON>ll", 7323749000000, 558900], [1710327156540093700, 320230063760000, "<PERSON>ll", 7323731000000, 201000], [1710327156315012900, 320230062730000, "<PERSON>ll", 7323731000000, 775000], [1710327153816453000, 320230061530000, "<PERSON>ll", 7323723000000, 120000], [1710327153434005000, 320230061500000, "<PERSON>ll", 7323723000000, 1186000], [1710327151834003700, 320230061350000, "<PERSON>ll", 7323723000000, 2135800], [1710327147784380400, 320230060630000, "<PERSON>ll", 7323723000000, 674600], [1710327144362294300, 320230058800000, "<PERSON>ll", 7323723000000, 15000], [1710327142614170000, 320230058690000, "<PERSON>ll", 7323723000000, 2260000], [1710327142427237000, 320230058670000, "<PERSON>ll", 7323515000000, 3437500], [1710327141058077000, 320230051740000, "<PERSON>ll", 7323723000000, 2681500], [1710327136421556500, 320230047090000, "<PERSON>ll", 7325659000000, 136600], [1710327133891150600, 320230046100000, "<PERSON>ll", 7325659000000, 1475000], [1710327131001889300, 320230045230000, "<PERSON>ll", 7325659000000, 718600], [1710327127959557000, 320230044450000, "<PERSON>ll", 7325659000000, 1000000], [1710327124345228000, 320230043320000, "<PERSON>ll", 7325659000000, 221300], [1710327122638217500, 320230042900000, "<PERSON>ll", 7325718000000, 5500], [1710327119526092000, 320230042430000, "<PERSON>ll", 7325275000000, 70000], [1710327115230404600, 320230041290000, "<PERSON>ll", 7325263000000, 1297500], [1710327113077003500, 320230040960000, "<PERSON>ll", 7325256000000, 2716800], [1710327108190271700, 320230037610000, "<PERSON>ll", 7325666000000, 955000], [1710327106085594600, 320230036660000, "<PERSON>ll", 7326576000000, 302500], [1710327103931155200, 320230035780000, "<PERSON>ll", 7326576000000, 2100000], [1710327099575502000, 320230033580000, "<PERSON>ll", 7327640000000, 125000], [1710327097566449200, 320230033430000, "<PERSON>ll", 7327640000000, 267500], [1710327096871823600, 320230033230000, "<PERSON>ll", 7327640000000, 728500], [1710327094821310200, 320230032070000, "<PERSON>ll", 7326710000000, 387500], [1710327094029408800, 320230031640000, "<PERSON>ll", 7327640000000, 1147400], [1710327093436772900, 320230031370000, "<PERSON>ll", 7327640000000, 1750000], [1710327091212100600, 320230030700000, "<PERSON>ll", 7327640000000, 585800], [1710327087911783000, 320230027040000, "<PERSON>ll", 7327640000000, 925000], [1710327083040401700, 320230025080000, "<PERSON>ll", 7327640000000, 330000], [1710327081670453200, 320230023650000, "<PERSON>ll", 7327640000000, 109400], [1710327080872488200, 320230019950000, "<PERSON>ll", 7327640000000, 542000], [1710327079031157800, 320230019050000, "<PERSON>ll", 7327640000000, 490000], [1710327078261933600, 320230018570000, "Buy", 7329985000000, 10500], [1710327076481866200, 320230011810000, "<PERSON>ll", 7327823000000, 68200], [1710327076097826300, 320230011750000, "<PERSON>ll", 7327823000000, 200000], [1710327074943272700, 320230010800000, "<PERSON>ll", 7327823000000, 1075000], [1710327074565149200, 320230010710000, "<PERSON>ll", 7328006000000, 68200], [1710327073779374600, 320230009480000, "<PERSON>ll", 7328006000000, 1750000], [1710327072812119000, 320230009250000, "<PERSON>ll", 7326710000000, 68200], [1710327072721975300, 320230008900000, "<PERSON>ll", 7327578000000, 2704100], [1710327071536945000, 320230007960000, "<PERSON>ll", 7327944000000, 68200], [1710327070182839600, 320230007440000, "<PERSON>ll", 7326710000000, 68200], [1710327068007503400, 320230006150000, "<PERSON>ll", 7324650000000, 2081200], [1710327064061160200, 320230002500000, "<PERSON>ll", 7324605000000, 96900], [1710327063264988400, 320230002370000, "<PERSON>ll", 7326096000000, 110000], [1710327060713190100, 320230001800000, "<PERSON>ll", 7324879000000, 310000], [1710327058563406800, 320230000290000, "<PERSON>ll", 7324870000000, 370000], [1710327057289133300, 320229999470000, "<PERSON>ll", 7324856000000, 1000000], [1710327052961343700, 320229997620000, "<PERSON>ll", 7324804000000, 227300], [1710327051808498700, 320229996820000, "<PERSON>ll", 7324792000000, 500000], [1710327049919996700, 320229995130000, "<PERSON>ll", 7324764000000, 41700], [1710327047465673200, 320229994170000, "<PERSON>ll", 7326436000000, 178000], [1710327046821382400, 320229993580000, "<PERSON>ll", 7324680000000, 1145000], [1710327044974299100, 320229986260000, "<PERSON>ll", 7326385000000, 2400000], [1710327041659604200, 320229983350000, "<PERSON>ll", 7326364000000, 196800], [1710327036836724500, 320229981960000, "<PERSON>ll", 7326307000000, 629000], [1710327036232505600, 320229981710000, "<PERSON>ll", 7326295000000, 2949500], [1710327031294827300, 320229979710000, "<PERSON>ll", 7328079000000, 743900], [1710327023092449000, 320229977850000, "<PERSON>ll", 7327990000000, 654000], [1710327020963303200, 320229977240000, "<PERSON>ll", 7327958000000, 860000], [1710327019720952600, 320229976820001, "<PERSON>ll", 7327914000000, 1245000], [1710327019720952600, 320229976820000, "<PERSON>ll", 7327914000000, 2065000], [1710327019584038100, 320229976810000, "<PERSON>ll", 7327943000000, 2836200], [1710327018080486400, 320229975740001, "<PERSON>ll", 7327914000000, 261800], [1710327018080486400, 320229975740000, "<PERSON>ll", 7327928000000, 2065000], [1710327015110350800, 320229973830000, "<PERSON>ll", 7327597000000, 2785000], [1710327010352873700, 320229969800000, "<PERSON>ll", 7327539000000, 2454100], [1710327007362662400, 320229969430001, "<PERSON>ll", 7326568000000, 5610100], [1710327007362662400, 320229969430000, "<PERSON>ll", 7326568000000, 2889900], [1710327006963223000, 320229969420000, "<PERSON>ll", 7327496000000, 2109600], [1710327002370971600, 320229967230000, "<PERSON>ll", 7327438000000, 2537100], [1710327000839955700, 320229967000000, "<PERSON>ll", 7327423000000, 1875300], [1710326990523919000, 320229957200000, "<PERSON>ll", 7326014000000, 2638100], [1710326989251914000, 320229955990000, "<PERSON>ll", 7325015000000, 1852700], [1710326984440458000, 320229950630000, "<PERSON>ll", 7324956000000, 2714200], [1710326979821845200, 320229939910000, "<PERSON>ll", 7323864000000, 1899900], [1710326976718751500, 320229936540000, "<PERSON>ll", 7323758000000, 2895500], [1710326971770394400, 320229934000000, "<PERSON>ll", 7322463000000, 2770800], [1710326967967582500, 320229930060000, "<PERSON>ll", 7323721000000, 2004900], [1710326963098651400, 320229926680000, "<PERSON>ll", 7324742000000, 2226400], [1710326960758639600, 320229923490000, "<PERSON>ll", 7324713000000, 1648300], [1710326956317598000, 320229919150000, "<PERSON>ll", 7323029000000, 2419600], [1710326954020564700, 320229915950000, "<PERSON>ll", 7326776000000, 1643900], [1710326951757869300, 320229915430000, "<PERSON>ll", 7326747000000, 2800100], [1710326947079364400, 320229913240000, "<PERSON>ll", 7326689000000, 1744400], [1710326944228754700, 320229911340000, "<PERSON>ll", 7326646000000, 1994400], [1710326940077180200, 320229907420000, "<PERSON>ll", 7326603000000, 2330100], [1710326939114568400, 320229906440000, "<PERSON>ll", 7326589000000, 2076600], [1710326937738663400, 320229905960000, "<PERSON>ll", 7326575000000, 2396700], [1710326932882297600, 320229903950000, "<PERSON>ll", 7326477000000, 2249100], [1710326929884381400, 320229901000000, "<PERSON>ll", 7326462000000, 112800], [1710326929883830300, 320229900990000, "<PERSON>ll", 7326462000000, 1620000], [1710326928912130000, 320229898540000, "<PERSON>ll", 7326457000000, 1633500], [1710326928106980900, 320229897600000, "<PERSON>ll", 7326446000000, 2603200], [1710326922918002400, 320229887610000, "<PERSON>ll", 7326389000000, 2329800], [1710326919851220700, 320229885310000, "<PERSON>ll", 7325644000000, 2558400], [1710326917812406300, 320229883270000, "<PERSON>ll", 7325931000000, 2843800], [1710326912115625700, 320229879610000, "<PERSON>ll", 7325859000000, 2926200], [1710326904482853400, 320229874830000, "<PERSON>ll", 7325903000000, 2345000], [1710326899670651100, 320229869260000, "<PERSON>ll", 7325918000000, 2544100], [1710326894932229000, 320229861440000, "<PERSON>ll", 7325431000000, 2265800], [1710326893046159600, 320229859520000, "<PERSON>ll", 7325413000000, 1976900], [1710326890588321000, 320229857830000, "<PERSON>ll", 7326350000000, 1709500], [1710326872719514000, 320229841640000, "Buy", 7330000000000, 136400], [1710326868854962400, 320229838410000, "<PERSON>ll", 7325748000000, 2488600], [1710326863751667700, 320229831010000, "<PERSON>ll", 7324692000000, 2126100], [1710326859578114600, 320229821650000, "<PERSON>ll", 7324689000000, 2162300], [1710326853242061600, 320229813000000, "Buy", 7323619000000, 1309100], [1710326848315400400, 320229807220001, "<PERSON>ll", 7323035000000, 25513800], [1710326848315400400, 320229807220000, "<PERSON>ll", 7323035000000, 351200], [1710326847276261600, 320229805730000, "<PERSON>ll", 7323454000000, 2824000], [1710326842810575000, 320229803350000, "<PERSON>ll", 7323449000000, 2634400], [1710326838862162200, 320229800390000, "<PERSON>ll", 7323448000000, 2528900], [1710326834174858000, 320229798660000, "<PERSON>ll", 7323444000000, 1945000], [1710326833222716400, 320229798330001, "<PERSON>ll", 7323035000000, 3492300], [1710326833222716400, 320229798330000, "<PERSON>ll", 7324650000000, 2281700], [1710326829917273300, 320229796790000, "<PERSON>ll", 7323035000000, 15091000], [1710326829917162800, 320229796780002, "<PERSON>ll", 7323035000000, 34200], [1710326829917162800, 320229796780001, "<PERSON>ll", 7323035000000, 1712300], [1710326829917162800, 320229796780000, "<PERSON>ll", 7323036000000, 1243500], [1710326829916646400, 320229796770002, "<PERSON>ll", 7323036000000, 76900], [1710326829916646400, 320229796770001, "<PERSON>ll", 7323036000000, 1210800], [1710326829916646400, 320229796770000, "<PERSON>ll", 7323036000000, 1312300], [1710326829660321000, 320229796620000, "<PERSON>ll", 7324822000000, 2285800], [1710326829089291500, 320229796600000, "Buy", 7325000000000, 2868000], [1710326827504306000, 320229796380000, "<PERSON>ll", 7324805000000, 2507400], [1710326825844330800, 320229794140000, "Buy", 7324144000000, 2538100], [1710326824789256200, 320229793290000, "Buy", 7323292000000, 2546400], [1710326821432604700, 320229791870000, "<PERSON>ll", 7323619000000, 1662300], [1710326818897470700, 320229790740000, "Buy", 7323214000000, 2660000], [1710326814336307700, 320229788710000, "<PERSON>ll", 7322633000000, 2055200], [1710326811550964700, 320229784710000, "<PERSON>ll", 7323010000000, 2056300], [1710326809588371200, 320229771170000, "<PERSON>ll", 7318990000000, 2488300], [1710326809064797000, 320229771080000, "Buy", 7319183000000, 2210900], [1710326807880975000, 320229770870000, "<PERSON>ll", 7318989000000, 1979800], [1710326803045101800, 320229764750000, "Buy", 7319183000000, 2660500], [1710326803044708900, 320229764740000, "Buy", 7319183000000, 267000], [1710326798566402600, 320229754560000, "Buy", 7319183000000, 2408700], [1710326797475325200, 320229754270000, "<PERSON>ll", 7318982000000, 545300], [1710326797113625000, 320229754180000, "<PERSON>ll", 7318982000000, 2203000], [1710326794169852000, 320229751150000, "<PERSON>ll", 7318979000000, 1783400], [1710326793824490800, 320229750900000, "Buy", 7319573000000, 1748000], [1710326788915372800, 320229744250000, "<PERSON>ll", 7319584000000, 2319500], [1710326785794905000, 320229742550000, "<PERSON>ll", 7319790000000, 2639700], [1710326783765224000, 320229740460000, "<PERSON>ll", 7319614000000, 2126100], [1710326780779691800, 320229736390000, "Buy", 7318924000000, 2177300], [1710326777122289700, 320229723960000, "<PERSON>ll", 7315965000000, 1665000], [1710326773031293700, 320229721930000, "<PERSON>ll", 7316178000000, 950000], [1710326772126774800, 320229721790000, "<PERSON>ll", 7316178000000, 1103100], [1710326767875693600, 320229717910000, "<PERSON>ll", 7316178000000, 1912600], [1710326763127940000, 320229716390000, "<PERSON>ll", 7316178000000, 1741600], [1710326759717607200, 320229715790000, "<PERSON>ll", 7316178000000, 683000], [1710326758040926500, 320229714800000, "<PERSON>ll", 7316178000000, 1000000], [1710326754600999700, 320229711130000, "<PERSON>ll", 7316178000000, 332200], [1710326752625192700, 320229710870000, "<PERSON>ll", 7315954000000, 2650000], [1710326749429467600, 320229706700000, "<PERSON>ll", 7313614000000, 14585000], [1710326749251669200, 320229706360000, "<PERSON>ll", 7315954000000, 1933700], [1710326746739319000, 320229705140000, "<PERSON>ll", 7316173000000, 2831800], [1710326743001530400, 320229702700000, "<PERSON>ll", 7316179000000, 2599700], [1710326741763161300, 320229701530000, "<PERSON>ll", 7316397000000, 81600], [1710326741150209800, 320229700030000, "<PERSON>ll", 7316399000000, 2125300], [1710326738444519400, 320229697770000, "<PERSON>ll", 7315494000000, 1904200], [1710326737669964000, 320229697180000, "<PERSON>ll", 7315495000000, 2463300], [1710326731256330200, 320229693590000, "<PERSON>ll", 7315497000000, 2999100], [1710326725705966300, 320229686670000, "<PERSON>ll", 7315497000000, 2462200], [1710326714792326000, 320229681420000, "<PERSON>ll", 7314682000000, 2986500], [1710326713236466400, 320229679840000, "<PERSON>ll", 7314684000000, 2517600], [1710326711346890800, 320229679250000, "<PERSON>ll", 7314686000000, 2340300], [1710326705060119600, 320229675490000, "<PERSON>ll", 7314688000000, 2685100], [1710326699829230800, 320229672270002, "Buy", 7314697000000, 24300], [1710326699829230800, 320229672270001, "Buy", 7314697000000, 79100], [1710326699829230800, 320229672270000, "Buy", 7314697000000, 249500], [1710326697776315000, 320229671810000, "<PERSON>ll", 7314698000000, 2744300], [1710326695187424000, 320229667860000, "Buy", 7314700000000, 1896400], [1710326693743943000, 320229667810000, "<PERSON>ll", 7314699000000, 2908800], [1710326689379058200, 320229664090000, "<PERSON>ll", 7314693000000, 2115800], [1710326686048915200, 320229662210000, "<PERSON>ll", 7314681000000, 1944000], [1710326682220636200, 320229659580000, "<PERSON>ll", 7314698000000, 2349100], [1710326680714787300, 320229658840000, "<PERSON>ll", 7313770000000, 2263200], [1710326679537154000, 320229657870000, "<PERSON>ll", 7313788000000, 1880900], [1710326675926728700, 320229657210000, "<PERSON>ll", 7312794000000, 2689300], [1710326671477219300, 320229656210000, "<PERSON>ll", 7312623000000, 1831400], [1710326667509884200, 320229653780000, "<PERSON>ll", 7312711000000, 2500400], [1710326663824334300, 320229651170000, "<PERSON>ll", 7313211000000, 1835100], [1710326662426947300, 320229650250000, "<PERSON>ll", 7313170000000, 2027500], [1710326661488653800, 320229650070000, "<PERSON>ll", 7313129000000, 2018700], [1710326657452128500, 320229648550000, "<PERSON>ll", 7312965000000, 1817600], [1710326647961267000, 320229635800000, "<PERSON>ll", 7310890000000, 2717100], [1710326640787140000, 320229633000000, "<PERSON>ll", 7310634000000, 2939600], [1710326639586037500, 320229632920000, "<PERSON>ll", 7310592000000, 2059700], [1710326635605714200, 320229631180000, "<PERSON>ll", 7310464000000, 2441500], [1710326630691749400, 320229621560000, "<PERSON>ll", 7311978000000, 2093400], [1710326619612257000, 320229615020000, "<PERSON>ll", 7312473000000, 2404400], [1710326619153623600, 320229614710000, "<PERSON>ll", 7312612000000, 1943400], [1710326616090384600, 320229612210000, "<PERSON>ll", 7312623000000, 1884500], [1710326608553440800, 320229609200000, "<PERSON>ll", 7312337000000, 2060400], [1710326601253307400, 320229604000000, "<PERSON>ll", 7312050000000, 2038000], [1710326588998820600, 320229600430000, "<PERSON>ll", 7311626000000, 2082900], [1710326582258680800, 320229592690000, "<PERSON>ll", 7311378000000, 1830700], [1710326580895449300, 320229590480000, "<PERSON>ll", 7311338000000, 1610300], [1710326573956355600, 320229582830000, "<PERSON>ll", 7311116000000, 2367500], [1710326568080393000, 320229574640000, "<PERSON>ll", 7310902000000, 2011000], [1710326561999243500, 320229568150000, "<PERSON>ll", 7310661000000, 2591000], [1710326547481385500, 320229558230000, "<PERSON>ll", 73101********, 2637600], [1710326545936661200, 320229557260000, "<PERSON>ll", 7310079000000, 2551300], [1710326545047071500, 320229556910000, "<PERSON>ll", 7310034000000, 2542100], [1710326465504431000, 320229507380001, "Buy", 7315091000000, 5012000], [1710326465504431000, 320229507380000, "Buy", 7315090000000, 417900], [1710326465490517200, 320229507370000, "<PERSON>ll", 7309993000000, 4200], [1710326401714449700, 320229447640000, "Buy", 7316432000000, 2260000], [1710326350495736800, 320229399790000, "<PERSON>ll", 7312981000000, 20000], [1710326305229747700, 320229356490000, "<PERSON>ll", 7312975000000, 13900], [1710326297241078800, 320229350330000, "<PERSON>ll", 7312973000000, 82300], [1710326295759572500, 320229348830000, "<PERSON>ll", 7312973000000, 82300], [1710326294573918500, 320229347400000, "<PERSON>ll", 7312973000000, 82300], [1710326293184387000, 320229345090000, "<PERSON>ll", 7312973000000, 82300], [1710326291767164000, 320229342690000, "<PERSON>ll", 7312973000000, 82300], [1710326289591377400, 320229337870002, "<PERSON>ll", 7312887000000, 8300], [1710326289591377400, 320229337870001, "<PERSON>ll", 7315000000000, 51300], [1710326289591377400, 320229337870000, "<PERSON>ll", 7315000000000, 22700], [1710326288474188000, 320229336990002, "<PERSON>ll", 7315000000000, 29700], [1710326288474188000, 320229336990001, "<PERSON>ll", 7315000000000, 51300], [1710326288474188000, 320229336990000, "<PERSON>ll", 7315000000000, 1300], [1710326286190903300, 320229331980000, "<PERSON>ll", 7315001000000, 82300], [1710326190978963200, 320229228220000, "<PERSON>ll", 7318209000000, 7020400], [1710326152434051600, 320229207790004, "<PERSON>ll", 7320********0, 54822800], [1710326152434051600, 320229207790003, "<PERSON>ll", 7320********0, 2017500], [1710326152434051600, 320229207790002, "<PERSON>ll", 7321825000000, 38665100], [1710326152434051600, 320229207790001, "<PERSON>ll", 7321825000000, 2125300], [1710326152434051600, 320229207790000, "<PERSON>ll", 7321918000000, 2369300], [1710326148281316600, 320229206120000, "<PERSON>ll", 7321784000000, 1905000], [1710326144509415200, 320229195580000, "<PERSON>ll", 7319453000000, 1637500], [1710326139671385900, 320229192820000, "<PERSON>ll", 7319176000000, 2720200], [1710326132835480800, 320229188830000, "<PERSON>ll", 7318854000000, 1803800], [1710326130415832600, 320229184840000, "<PERSON>ll", 7318768000000, 1643000], [1710326119685290200, 320229173520000, "<PERSON>ll", 7318382000000, 2104300], [1710326118157173000, 320229172860000, "<PERSON>ll", 7318298000000, 2367000], [1710326113023287300, 320229170300000, "<PERSON>ll", 7318123000000, 2670500], [1710326096518990300, 320229153100000, "<PERSON>ll", 7317587000000, 2520600], [1710326092923498200, 320229149100000, "<PERSON>ll", 7317453000000, 2096900], [1710326080151180500, 320229135230000, "Buy", 7320723000000, 2645600], [1710326079311824100, 320229134860000, "<PERSON>ll", 7317359000000, 2606400], [1710326071241613000, 320229128840000, "<PERSON>ll", 7317062000000, 2970300], [1710326070324782000, 320229127940000, "<PERSON>ll", 73170********, 2210200], [1710326061621110300, 320229113630000, "<PERSON>ll", 7316718000000, 2477200], [1710326025274056400, 320229079760000, "Buy", 7317521000000, 46800], [1710326014149226800, 320229070470000, "Buy", 7317521000000, 46800], [1710326005236722200, 320229063110000, "Buy", 7317356000000, 44600], [1710325959788112400, 320229023110001, "<PERSON>ll", 7321381000000, 82400], [1710325959788112400, 320229023110000, "<PERSON>ll", 7321381000000, 56800], [1710325956905473500, 320229015340000, "Buy", 7321382000000, 2136800], [1710325951470385000, 320229006660000, "Buy", 7321382000000, 2031200], [1710325947146968600, 320229004540000, "Buy", 7321382000000, 1817000], [1710325942619687700, 320228999250000, "Buy", 7321382000000, 1959700], [1710325846821284000, 320228855160000, "Buy", 7328508000000, 2946000], [1710325843253729300, 320228850450000, "Buy", 7327834000000, 2769400], [1710325833400899600, 320228814020000, "Buy", 7323376000000, 6691900], [1710325776079562000, 320228751850000, "Buy", 7323339000000, 118900], [1710325748268606700, 320228727950000, "<PERSON>ll", 7323319000000, 1938700], [1710325748106611000, 320228727930000, "Buy", 7323339000000, 52400], [1710325741230532900, 320228724170000, "<PERSON>ll", 7323318000000, 2786500], [1710325724309095200, 320228705970000, "<PERSON>ll", 7322600000000, 42600], [1710325722583051500, 320228704690000, "Buy", 7322984000000, 9454200], [1710325722572696600, 320228704680000, "Buy", 7322984000000, 51200], [1710325720963254000, 320228704470000, "<PERSON>ll", 7322600000000, 1915600], [1710325714938817000, 320228695670000, "<PERSON>ll", 7322599000000, 1667800], [1710325713379271700, 320228695130000, "Buy", 7323339000000, 46800], [1710325710755773400, 320228694080000, "<PERSON>ll", 7322598000000, 2231600], [1710325707227998200, 320228684880000, "<PERSON>ll", 7322597000000, 2833500], [1710325705203185200, 320228684420000, "Buy", 7323339000000, 42300], [1710325704451491000, 320228683950000, "<PERSON>ll", 7322597000000, 1855400], [1710325701325807000, 320228682380000, "<PERSON>ll", 7322596000000, 2171400], [1710325698819380200, 320228681650000, "Buy", 7323024000000, 43400], [1710325696359866600, 320228680410000, "<PERSON>ll", 7322596000000, 2291500], [1710325689637368600, 320228672320000, "<PERSON>ll", 7322155000000, 2517300], [1710325689225731300, 320228672280000, "Buy", 7323246000000, 45700], [1710325677689943800, 320228656540000, "Buy", 7323246000000, 42300], [1710325673102564400, 320228655080000, "<PERSON>ll", 7322153000000, 2789500], [1710325671044793600, 320228654250000, "<PERSON>ll", 7322121000000, 1659400], [1710325670099732200, 320228652760000, "Buy", 7323246000000, 33400], [1710325665128190200, 320228649890000, "<PERSON>ll", 7322152000000, 2137400], [1710325663628757000, 320228648650000, "Buy", 7323339000000, 45600], [1710325661128379000, 320228646350000, "<PERSON>ll", 7322151000000, 2994300], [1710325652675411700, 320228637600000, "<PERSON>ll", 7322150000000, 2814700], [1710325647620610300, 320228635380000, "<PERSON>ll", 7322589000000, 1991300], [1710325646939679500, 320228635100000, "<PERSON>ll", 7322553000000, 2617100], [1710325644652107300, 320228634210000, "<PERSON>ll", 7323227000000, 2313600], [1710325640172719400, 320228623420000, "<PERSON>ll", 7323339000000, 2553900], [1710325638817889800, 320228623190000, "Buy", 7323376000000, 108100], [1710325629975426000, 320228618830000, "<PERSON>ll", 7323334000000, 2620100], [1710325627174370000, 320228617650000, "<PERSON>ll", 7321989000000, 5300], [1710325610759572200, 320228588150000, "<PERSON>ll", 7318559000000, 2947300], [1710325591517405000, 320228558200000, "<PERSON>ll", 7323373000000, 2477700], [1710325557408451600, 320228535400007, "Buy", 7323328000000, 2545400], [1710325557408451600, 320228535400006, "Buy", 7323328000000, 249300], [1710325557408451600, 320228535400005, "Buy", 7323328000000, 372600], [1710325557408451600, 320228535400004, "Buy", 7323328000000, 1051200], [1710325557408451600, 320228535400003, "Buy", 7323328000000, 253900], [1710325557408451600, 320228535400002, "Buy", 7323328000000, 251700], [1710325557408451600, 320228535400001, "Buy", 7323328000000, 181300], [1710325557408451600, 320228535400000, "Buy", 7323328000000, 608400], [1710325556768508000, 320228535210000, "<PERSON>ll", 7323327000000, 837500], [1710325553273865000, 320228533200002, "<PERSON>ll", 7320487000000, 14964200], [1710325553273865000, 320228533200001, "<PERSON>ll", 7320487000000, 2362400], [1710325553273865000, 320228533200000, "<PERSON>ll", 7323279000000, 2723900], [1710325552633362700, 320228533110000, "<PERSON>ll", 7323327000000, 2311300], [1710325549558848500, 320228531550000, "<PERSON>ll", 7323278000000, 2232400], [1710325547871066400, 320228530580000, "<PERSON>ll", 7323327000000, 2538400], [1710325545584906200, 320228522110000, "<PERSON>ll", 7323302000000, 1733400], [1710325543976800300, 320228521340000, "<PERSON>ll", 7322923000000, 2009100], [1710325534945538000, 320228513820000, "<PERSON>ll", 7323307000000, 1809200], [1710325534200371000, 320228513500001, "<PERSON>ll", 7320487000000, 18200], [1710325534200371000, 320228513500000, "<PERSON>ll", 7321381000000, 43500], [1710325510237921000, 320228477660000, "<PERSON>ll", 7319721000000, 2532800], [1710325496911276800, 320228458040000, "<PERSON>ll", 7318115000000, 38163100], [1710325496370291000, 320228457760000, "<PERSON>ll", 7319191000000, 2797100], [1710325495212224300, 320228457440000, "<PERSON>ll", 7319148000000, 2188400], [1710325493168521700, 320228456960000, "<PERSON>ll", 7319062000000, 1647500], [1710325489206415600, 320228454760000, "<PERSON>ll", 7318938000000, 2736800], [1710325487616768300, 320228452690000, "<PERSON>ll", 7318115000000, 20228800], [1710325487616323600, 320228452680000, "<PERSON>ll", 7318115000000, 6375000], [1710325487145879000, 320228451380000, "<PERSON>ll", 7318852000000, 1976200], [1710325482324600600, 320228448630000, "<PERSON>ll", 7318079000000, 2491500], [1710325478397025000, 320228447250000, "<PERSON>ll", 7317952000000, 2349500], [1710325471534038000, 320228444480000, "<PERSON>ll", 7317706000000, 1838400], [1710325467601146600, 320228435800000, "<PERSON>ll", 7317542000000, 2726600], [1710325463029924600, 320228425610000, "<PERSON>ll", 7317529000000, 1911600], [1710325458190054100, 320228422120000, "<PERSON>ll", 7317456000000, 2119800], [1710325456958022100, 320228421400000, "Buy", 7323324000000, 61800], [1710325440578461000, 320228388810001, "<PERSON>ll", 7317184000000, 2554200], [1710325440578461000, 320228388810000, "<PERSON>ll", 7317184000000, 181200], [1710325435500135700, 320228383210000, "<PERSON>ll", 7316646000000, 2601000], [1710325433977198000, 320228382210000, "<PERSON>ll", 7316934000000, 2154900], [1710325431773179600, 320228379130000, "<PERSON>ll", 7316484000000, 2113500], [1710325424110638600, 320228374240000, "<PERSON>ll", 7316215000000, 1681300], [1710325413133131500, 320228361000000, "<PERSON>ll", 7316137000000, 9500], [1710325413124388900, 320228360990000, "<PERSON>ll", 7316195000000, 2958600], [1710325315517305000, 320228210570001, "Buy", 7323376000000, 53400], [1710325315517305000, 320228210570000, "Buy", 7323376000000, 181200], [1710325281244853500, 320228128150000, "<PERSON>ll", 7321237000000, 1874300], [1710325279071139800, 320228125260000, "<PERSON>ll", 7320789000000, 2736500], [1710325265415831000, 320228094780000, "<PERSON>ll", 7323858000000, 352500], [1710325264808869000, 320228094610000, "<PERSON>ll", 7323859000000, 305700], [1710325262575067100, 320228093380000, "<PERSON>ll", 7323859000000, 164000], [1710325260559254000, 320228090850000, "<PERSON>ll", 7323859000000, 140500], [1710325257796910000, 320228089730000, "<PERSON>ll", 7323859000000, 131700], [1710325256513035300, 320228089160000, "<PERSON>ll", 7323859000000, 1000000], [1710325252695389200, 320228088070000, "<PERSON>ll", 7323859000000, 137500], [1710325251926522600, 320228088030000, "<PERSON>ll", 7323859000000, 620000], [1710325250208345000, 320228087890000, "<PERSON>ll", 7323859000000, 240000], [1710325246464744400, 320228087220000, "<PERSON>ll", 7323859000000, 8500], [1710325245784668200, 320228086750000, "<PERSON>ll", 7329633000000, 2149600], [1710325242445499100, 320228085180000, "<PERSON>ll", 7326247000000, 762500], [1710325240350472700, 320228083920000, "<PERSON>ll", 7324610000000, 1605100], [1710325235216283600, 320228077500000, "<PERSON>ll", 7324625000000, 2661800], [1710325219812916000, 320228067420000, "<PERSON>ll", 7324628000000, 2598400], [1710325219588492500, 320228067320001, "<PERSON>ll", 7324629000000, 14300], [1710325219588492500, 320228067320000, "<PERSON>ll", 7324629000000, 2875000], [1710325215895948300, 320228058870000, "<PERSON>ll", 7324590000000, 1608300], [1710325213747019000, 320228058050000, "<PERSON>ll", 7322693000000, 2742200], [1710325206417654300, 320228053370000, "<PERSON>ll", 7324332000000, 2959200], [1710325202048158700, 320228051760000, "<PERSON>ll", 7323979000000, 1829200], [1710325195409795800, 320228048130000, "<PERSON>ll", 7324367000000, 2491000], [1710325194309083400, 320228046390000, "<PERSON>ll", 7323490000000, 1764800], [1710325189781163800, 320228041980000, "<PERSON>ll", 7324123000000, 2997900], [1710325186965548300, 320228040350000, "<PERSON>ll", 7322904000000, 2257800], [1710325185155246000, 320228038940000, "<PERSON>ll", 7323984000000, 2168600], [1710325181631309000, 320228035610000, "<PERSON>ll", 7325817000000, 2377200], [1710325177836440300, 320228031130000, "<PERSON>ll", 7323612000000, 2021600], [1710325172866183400, 320228023660000, "<PERSON>ll", 7326452000000, 1618900], [1710325171729945000, 320228022810000, "<PERSON>ll", 7324726000000, 2754400], [1710325167057420300, 320228017900000, "<PERSON>ll", 7326234000000, 2629300], [1710325164985378000, 320228016970000, "<PERSON>ll", 7326153000000, 1999100], [1710325161901490700, 320228012320000, "<PERSON>ll", 7327984000000, 1936900], [1710325161207355000, 320228011350000, "<PERSON>ll", 7327943000000, 2509800], [1710325158340987600, 320228008640000, "<PERSON>ll", 7327837000000, 2393600], [1710325155942359600, 320227999430000, "<PERSON>ll", 7325470000000, 2864100], [1710325154198200300, 320227997970000, "<PERSON>ll", 7327679000000, 2323000], [1710325152763907600, 320227995490002, "Buy", 7327409000000, 272000], [1710325152763907600, 320227995490001, "Buy", 7327409000000, 1671100], [1710325152763907600, 320227995490000, "Buy", 7327409000000, 1310900], [1710325151884808400, 320227993230000, "<PERSON>ll", 7325012000000, 1646600], [1710325150389272600, 320227992620000, "<PERSON>ll", 7324970000000, 2555000], [1710325146262243000, 320227987580000, "<PERSON>ll", 7327365000000, 2174100], [1710325144665032700, 320227986500000, "<PERSON>ll", 7324645000000, 2964800], [1710325142161419300, 320227983950000, "<PERSON>ll", 7327228000000, 1812600], [1710325138504075000, 320227980160000, "<PERSON>ll", 7324234000000, 2533700], [1710325133550895600, 320227974160000, "<PERSON>ll", 7326553000000, 2703100], [1710325131203511800, 320227973070000, "<PERSON>ll", 7326401000000, 2137100], [1710325129762929700, 320227972110000, "<PERSON>ll", 7326362000000, 2401600], [1710325111877021000, 320227950400000, "<PERSON>ll", 7325893000000, 1819100], [1710325105156588300, 320227945870000, "<PERSON>ll", 7325651000000, 2632000], [1710325102852679000, 320227943750000, "<PERSON>ll", 7325563000000, 1855400], [1710325100158955500, 320227939990000, "<PERSON>ll", 7325503000000, 2148000], [1710325097051788300, 320227936700001, "<PERSON>ll", 7325140000000, 44419600], [1710325097051788300, 320227936700000, "<PERSON>ll", 7325140000000, 2138800], [1710325096745612000, 320227936290000, "<PERSON>ll", 7325401000000, 2826200], [1710325093530678300, 320227926760000, "<PERSON>ll", 7325303000000, 2956500], [1710325081752045300, 320227910250000, "<PERSON>ll", 7324992000000, 2413000], [1710325076786455800, 320227902930000, "<PERSON>ll", 7324803000000, 2571300], [1710325075029596200, 320227901120000, "<PERSON>ll", 7324770000000, 2887600], [1710325068298421500, 320227891770000, "<PERSON>ll", 7324506000000, 1651500], [1710325064389509600, 320227884640000, "<PERSON>ll", 7324410000000, 1813600], [1710325060351634400, 320227878610000, "<PERSON>ll", 7324231000000, 2019200], [1710324962030055700, 320227766300000, "Buy", 7330000000000, 9500], [1710324886587502800, 320227677540000, "Buy", 7329855000000, 277200], [1710324876013448700, 320227664850000, "<PERSON>ll", 7329854000000, 5400], [1710324860389989600, 320227652180000, "<PERSON>ll", 7322629000000, 2154200], [1710324855677956000, 320227646370000, "<PERSON>ll", 7322459000000, 2758200], [1710324853117499600, 320227644110000, "<PERSON>ll", 7322375000000, 2432300], [1710324852037311700, 320227642110000, "<PERSON>ll", 7322331000000, 2818700], [1710324843363022300, 320227628920000, "<PERSON>ll", 7317701000000, 2731500], [1710324841812782600, 320227628650000, "<PERSON>ll", 7317659000000, 2586700], [1710324835843339000, 320227624250000, "<PERSON>ll", 7317445000000, 1783000], [1710324834646580200, 320227618460000, "<PERSON>ll", 7317401000000, 1830700], [1710324831321790500, 320227613380000, "<PERSON>ll", 7317265000000, 2486500], [1710324824435284200, 320227597370000, "<PERSON>ll", 7317041000000, 2067300], [1710324813382958300, 320227581980000, "<PERSON>ll", 7316585000000, 2174400], [1710324726504311800, 320227487470002, "Buy", 7325000000000, 1653900], [1710324726504311800, 320227487470001, "Buy", 7325000000000, 181100], [1710324726504311800, 320227487470000, "Buy", 7325000000000, 251600], [1710324721679748000, 320227482940003, "<PERSON>ll", 7316252000000, 2890400], [1710324721679748000, 320227482940002, "<PERSON>ll", 7319450000000, 728600], [1710324721679748000, 320227482940001, "<PERSON>ll", 7319450000000, 1310900], [1710324721679748000, 320227482940000, "<PERSON>ll", 7319450000000, 1311100], [1710324680489434400, 320227425610000, "<PERSON>ll", 7315018000000, 1871200], [1710324678268845600, 320227424810000, "Buy", 7325563000000, 92100], [1710324676769396200, 320227422990000, "<PERSON>ll", 7314884000000, 305700], [1710324676768949000, 320227422980000, "<PERSON>ll", 7314884000000, 1763800], [1710324674177890600, 320227421440000, "<PERSON>ll", 7314798000000, 1631600], [1710324637508308200, 320227390060000, "Buy", 7325119000000, 1311300], [1710324636549369900, 320227387210000, "<PERSON>ll", 7315479000000, 1883000], [1710324634917688000, 320227385000000, "<PERSON>ll", 7315726000000, 1611200], [1710324630774284800, 320227384200000, "<PERSON>ll", 7315590000000, 1996000], [1710324611647584800, 320227339460000, "<PERSON>ll", 7317554000000, 3600], [1710324608695916800, 320227336400000, "Buy", 7317555000000, 1950200], [1710324605519657500, 320227333970000, "Buy", 7318054000000, 1660400], [1710324601289909500, 320227332190000, "Buy", 7319006000000, 2377100], [1710324587454164500, 320227323070000, "Buy", 7317555000000, 2163800], [1710324582837956600, 320227319520000, "Buy", 7318482000000, 1696100], [1710324578208637400, 320227310560000, "Buy", 7320354000000, 2042200], [1710324512515992000, 320227245330000, "Buy", 7320631000000, 2107100], [1710324506913604600, 320227233240000, "Buy", 7320904000000, 1995500], [1710324503829497000, 320227230350001, "Buy", 7321009000000, 708800], [1710324503829497000, 320227230350000, "Buy", 7321009000000, 1307300], [1710324499834011000, 320227226240000, "<PERSON>ll", 7317593000000, 2944000], [1710324497766994000, 320227224530002, "<PERSON>ll", 7319536000000, 1351500], [1710324497766994000, 320227224530001, "<PERSON>ll", 73********000, 11300], [1710324497766994000, 320227224530000, "<PERSON>ll", 7320900000000, 1365900], [1710324495520120000, 320227221890000, "Buy", 7322060000000, 1900900], [1710324493385917200, 320227221380000, "Buy", 732********00, 2902100], [1710324489925591800, 320227219420000, "Buy", 7322241000000, 2811300], [1710324485727352000, 320227217190000, "Buy", 7322271000000, 1946000], [1710324479497744000, 320227211120000, "Buy", 7322723000000, 2967600], [1710324478440081200, 320227209520000, "Buy", 7323001000000, 1625700], [1710324476950091500, 320227200510000, "<PERSON>ll", 7321694000000, 2513300], [1710324463665287400, 320227195680000, "<PERSON>ll", 7321943000000, 2562700], [1710324460072484000, 320227192400000, "<PERSON>ll", 7321809000000, 1753300], [1710324450400012300, 320227177730000, "<PERSON>ll", 7321545000000, 2099900], [1710324447599847400, 320227175460000, "<PERSON>ll", 7321456000000, 2928500], [1710324443058302700, 320227163620001, "<PERSON>ll", 7321178000000, 250400], [1710324443058302700, 320227163620000, "<PERSON>ll", 7321568000000, 2184000], [1710324439808592000, 320227156500000, "<PERSON>ll", 7321468000000, 2570200], [1710324436991365600, 320227150090000, "<PERSON>ll", 7321360000000, 1887800], [1710324433576465400, 320227145180000, "<PERSON>ll", 7321265000000, 2768600], [1710324429772355600, 320227132160000, "<PERSON>ll", 7321155000000, 2280000], [1710324427151329300, 320227130090000, "<PERSON>ll", 7321066000000, 1810200], [1710324333498507300, 320227005460000, "Buy", 7324001000000, 2202800], [1710324218131790000, 320226835430002, "<PERSON>ll", 7321955000000, 2386700], [1710324218131790000, 320226835430001, "<PERSON>ll", 7322233000000, 2323900], [1710324218131790000, 320226835430000, "<PERSON>ll", 7322234000000, 55900], [1710324217498151700, 320226835290000, "<PERSON>ll", 7322201000000, 2949900], [1710324213615418600, 320226831040000, "<PERSON>ll", 7322106000000, 1660200], [1710324210192024600, 320226822630000, "<PERSON>ll", 7322015000000, 2569000], [1710324085719141000, 320226682460000, "<PERSON>ll", 7321381000000, 409700], [1710324037750875000, 320226593590000, "<PERSON>ll", 7323389000000, 4068000], [1710324033428340500, 320226588180000, "Buy", 7330056000000, 50000], [1710324027041227300, 320226581440000, "<PERSON>ll", 7327386000000, 5300], [1710324012607966000, 320226565530000, "<PERSON>ll", 7330640000000, 2370500], [1710324009155347700, 320226563640000, "<PERSON>ll", 7330091000000, 1802800], [1710324006853191200, 320226554320000, "<PERSON>ll", 7330086000000, 2202500], [1710324004994407700, 320226551650000, "<PERSON>ll", 7330083000000, 1664500], [1710324001591774200, 320226548500000, "<PERSON>ll", 7330076000000, 724100], [1710323999898541300, 320226547640000, "<PERSON>ll", 7330071000000, 450000], [1710323997510729200, 320226547080000, "<PERSON>ll", 7330065000000, 1055000], [1710323992927013000, 320226545190000, "<PERSON>ll", 7330055000000, 1000000], [1710323988066035200, 320226541170000, "<PERSON>ll", 7330045000000, 472200], [1710323983397864000, 320226538960000, "<PERSON>ll", 7330034000000, 600000], [1710323982482488000, 320226538810000, "<PERSON>ll", 7330031000000, 1117000], [1710323979638657000, 320226537730000, "<PERSON>ll", 7330026000000, 1500000], [1710323975818420000, 320226533190000, "<PERSON>ll", 7329752000000, 1700000], [1710323971431744800, 320226525820000, "<PERSON>ll", 7327756000000, 1650000], [1710323969207063000, 320226520550000, "<PERSON>ll", 7327747000000, 1683100], [1710323967778935600, 320226518890000, "<PERSON>ll", 7328397000000, 1948000], [1710323965441025800, 320226513350000, "<PERSON>ll", 7328426000000, 2391100], [1710323963520544500, 320226505320000, "<PERSON>ll", 7329942000000, 786000], [1710323961053770800, 320226503900000, "<PERSON>ll", 7330831000000, 500000], [1710323960575784000, 320226503850000, "<PERSON>ll", 7330831000000, 908500], [1710323958854994200, 320226502990000, "<PERSON>ll", 7330929000000, 1805600], [1710323956967827200, 320226499120000, "<PERSON>ll", 7332588000000, 2935400], [1710323953316753200, 320226496220000, "<PERSON>ll", 7332589000000, 340000], [1710323951245258500, 320226495880000, "<PERSON>ll", 7332589000000, 1135000], [1710323947487199700, 320226495280000, "<PERSON>ll", 7332314000000, 1215000], [1710323943025401900, 320226494180000, "<PERSON>ll", 7331993000000, 654200], [1710323942298799900, 320226493870000, "<PERSON>ll", 7330116000000, 1482200], [1710323941512012800, 320226492810000, "<PERSON>ll", 7329656000000, 4945000], [1710323937581356500, 320226491030000, "<PERSON>ll", 7329656000000, 19000000], [1710323936045793800, 320226490610000, "<PERSON>ll", 7330116000000, 2295100], [1710323933261054500, 320226489130000, "<PERSON>ll", 7329898000000, 1678900], [1710323929757583600, 320226485320003, "<PERSON>ll", 7331999000000, 11279700], [1710323929757583600, 320226485320002, "<PERSON>ll", 7331999000000, 17215000], [1710323929757583600, 320226485320001, "<PERSON>ll", 7331999000000, 1943400], [1710323929757583600, 320226485320000, "<PERSON>ll", 7332258000000, 2061900], [1710323928889514000, 320226484450001, "<PERSON>ll", 7332111000000, 19746800], [1710323928889514000, 320226484450000, "<PERSON>ll", 7332111000000, 1240300], [1710323928889005800, 320226484440000, "<PERSON>ll", 7332111000000, 753500], [1710323928726267100, 320226484360001, "<PERSON>ll", 7332111000000, 167900], [1710323928726267100, 320226484360000, "<PERSON>ll", 7332929000000, 82100], [1710323928625933600, 320226484340000, "<PERSON>ll", 7332929000000, 2161700], [1710323925404222000, 320226480620000, "<PERSON>ll", 7331302000000, 2065400], [1710323924944627200, 320226479300000, "<PERSON>ll", 7328915000000, 968500], [1710323924718193700, 320226478950003, "<PERSON>ll", 7328915000000, 1665700], [1710323924718193700, 320226478950002, "<PERSON>ll", 7329433000000, 147400], [1710323924718193700, 320226478950001, "<PERSON>ll", 7329433000000, 655400], [1710323924718193700, 320226478950000, "<PERSON>ll", 7329433000000, 165700], [1710323924449587000, 320226477910000, "Buy", 7329416000000, 2189900], [1710323924445017600, 320226477900000, "<PERSON>ll", 7329412000000, 6800], [1710323923430743600, 320226476760000, "Buy", 7329201000000, 2560200], [1710323920642802000, 320226476000000, "Buy", 7328900000000, 2629800], [1710323850829548000, 320226433540000, "<PERSON>ll", 7324281000000, 2791300], [1710323847457776600, 320226431380000, "<PERSON>ll", 7324172000000, 2237700], [1710323843442312700, 320226423460000, "Buy", 7327463000000, 2349000], [1710323841858882000, 320226422720000, "Buy", 7327458000000, 2820700], [1710323840147099600, 320226421560000, "Buy", 7326500000000, 2537000], [1710323836264169500, 320226420020000, "Buy", 7326482000000, 2079000], [1710323831837720300, 320226418530000, "Buy", 7326458000000, 1753100], [1710323830342623200, 320226418050000, "Buy", 7326452000000, 2592800], [1710323827850169000, 320226417150000, "Buy", 7326440000000, 1699200], [1710323823706335000, 320226415490000, "<PERSON>ll", 7322543000000, 1906700], [1710323822937000700, 320226415050000, "Buy", 7325106000000, 1982500], [1710323819942167600, 320226411210000, "Buy", 7325006000000, 1869100], [1710323818056377300, 320226410110000, "<PERSON>ll", 7322256000000, 2188500], [1710323815828800800, 320226409800000, "<PERSON>ll", 7322184000000, 2609900], [1710323814512796400, 320226409600000, "Buy", 7325006000000, 1616200], [1710323813430712600, 320226409490000, "Buy", 7325042000000, 1670200], [1710323809192124400, 320226408680000, "Buy", 7325018000000, 1788500], [1710323804976341500, 320226407690000, "Buy", 7324638000000, 2620700], [1710323800487651300, 320226406400000, "Buy", 7324647000000, 2190400], [1710323796155829000, 320226405140000, "Buy", 7324654000000, 2535200], [1710323792173698000, 320226403520000, "Buy", 7324626000000, 2764300], [1710323790587954200, 320226403090000, "Buy", 7324633000000, 2385500], [1710323788213929000, 320226402070000, "Buy", 7324647000000, 2102800], [1710323779542517000, 320226391980000, "Buy", 7324626000000, 1949100], [1710323774966652700, 320226387710000, "Buy", 7324428000000, 2970600], [1710323769859697400, 320226383710000, "Buy", 7324014000000, 2257400], [1710323767790666800, 320226380980000, "Buy", 7323211000000, 2467200], [1710323764127897000, 320226379550000, "Buy", 7323633000000, 2684500], [1710323760964970800, 320226377020000, "Buy", 7323609000000, 2715500], [1710323756429062400, 320226365060000, "Buy", 7325275000000, 1792600], [1710323751957700400, 320226355910000, "Buy", 7324008000000, 2854200], [1710323749379316700, 320226352950000, "Buy", 7322965000000, 1800200], [1710323732636598500, 320226336760000, "Buy", 7320965000000, 2415800], [1710323727732762600, 320226335100000, "Buy", 7320925000000, 2544400], [1710323723833480400, 320226332670000, "Buy", 7320941000000, 1745200], [1710323713855529000, 320226324360000, "Buy", 7320909000000, 2982500], [1710323710689132800, 320226320530000, "Buy", 7320909000000, 2579600], [1710323703282925800, 320226311470000, "Buy", 7320909000000, 2754600], [1710323700136696800, 320226303730000, "Buy", 7320909000000, 2483300], [1710323697601762800, 320226301770000, "<PERSON>ll", 7320940000000, 3100], [1710323693385406500, 320226297380000, "Buy", 7321491000000, 2349300], [1710323686550876700, 320226293420000, "Buy", 7320941000000, 1707200], [1710323679883510500, 320226290960000, "Buy", 7320941000000, 2517700], [1710323675007132000, 320226289740000, "Buy", 7320941000000, 2976700], [1710323672524998000, 320226289030000, "Buy", 7320941000000, 1699900], [1710323663789921300, 320226282640000, "Buy", 7320941000000, 2449200], [1710323659391679500, 320226279190000, "Buy", 7320941000000, 1859800], [1710323655091765000, 320226269000000, "Buy", 7320941000000, 2848400], [1710323653393568800, 320226260290001, "<PERSON>ll", 7321469000000, 1307300], [1710323653393568800, 320226260290000, "<PERSON>ll", 7321469000000, 1311300], [1710323653165804000, 320226260250000, "Buy", 7321470000000, 2313200], [1710323648553306000, 320226258230000, "Buy", 7321470000000, 2574200], [1710323646960782800, 320226257870000, "Buy", 7321464000000, 1933000], [1710323640174662400, 320226255580000, "Buy", 7321108000000, 2023500], [1710323637045772500, 320226254840000, "Buy", 7321180000000, 2374100], [1710323634562782200, 320226253800000, "Buy", 7321227000000, 2277300], [1710323630103843600, 320226252350000, "Buy", 7322125000000, 3900], [1710323630024578300, 320226252330000, "Buy", 73213********, 2166100], [1710323627632534800, 320226250440000, "Buy", 7321366000000, 2510900], [1710323626268128800, 320226250100000, "Buy", 7321389000000, 103100], [1710323626267660500, 320226250090000, "Buy", 7321389000000, 2525000], [1710323610419984100, 320226229690000, "Buy", 7321716000000, 2387200], [1710323606183101700, 320226224690000, "Buy", 7321810000000, 2506600], [1710323604981354500, 320226224180000, "Buy", 7321833000000, 1890900], [1710323602243318300, 320226216820000, "Buy", 7321906000000, 2228400], [1710323600767198500, 320226216430000, "Buy", 7321930000000, 1678400], [1710323597998905600, 320226213720000, "Buy", 7321980000000, 1650500], [1710323576017159200, 320226169790001, "Buy", 7322437000000, 1289400], [1710323576017159200, 320226169790000, "Buy", 7322436000000, 1311800], [1710323565828665300, 320226160420000, "Buy", 7322659000000, 2533800], [1710323561030248200, 320226150330000, "Buy", 7322754000000, 1759200], [1710323557692857300, 320226147940001, "Buy", 7322852000000, 27376500], [1710323557692857300, 320226147940000, "Buy", 7322852000000, 2015100], [1710323556977580300, 320226147880001, "Buy", 7322852000000, 110600], [1710323556977580300, 320226147880000, "Buy", 7322828000000, 2015100], [1710323545113472300, 320226128350000, "Buy", 7326760000000, 2690300], [1710323543422155500, 320226127960001, "Buy", 7326784000000, 521700], [1710323543422155500, 320226127960000, "Buy", 7326783000000, 1308400], [1710323458998557200, 320226014780000, "<PERSON>ll", 7321242000000, 2492500], [1710323416940871400, 320225966240002, "<PERSON>ll", 7321999000000, 837100], [1710323416940871400, 320225966240001, "<PERSON>ll", 7323529000000, 1311700], [1710323416940871400, 320225966240000, "<PERSON>ll", 7323529000000, 3280500], [1710323393250821000, 320225915760000, "Buy", 7321686000000, 26500], [1710323389526636800, 320225911020000, "<PERSON>ll", 7321685000000, 2064100], [1710323334734888400, 320225849240005, "<PERSON>ll", 7325000000000, 2000], [1710323334734888400, 320225849240004, "<PERSON>ll", 7325915000000, 56200], [1710323334734888400, 320225849240003, "<PERSON>ll", 7326741000000, 73800], [1710323334734888400, 320225849240002, "<PERSON>ll", 7326741000000, 81600], [1710323334734888400, 320225849240001, "<PERSON>ll", 7326841000000, 80400], [1710323334734888400, 320225849240000, "<PERSON>ll", 7326841000000, 82700], [1710323324503269400, 320225838880000, "Buy", 7328960000000, 26500], [1710323317341282000, 320225835150004, "<PERSON>ll", 7327995000000, 69400], [1710323317341282000, 320225835150003, "<PERSON>ll", 7327995000000, 102500], [1710323317341282000, 320225835150002, "<PERSON>ll", 7327995000000, 100300], [1710323317341282000, 320225835150001, "<PERSON>ll", 7327995000000, 110200], [1710323317341282000, 320225835150000, "<PERSON>ll", 7327995000000, 47100], [1710323313729071400, 320225829910000, "Buy", 7332899000000, 2682000], [1710323313520146000, 320225829760000, "<PERSON>ll", 7327995000000, 1400], [1710323309910997200, 320225825270000, "Buy", 7329357000000, 22000], [1710323303359325200, 320225807420000, "Buy", 7329357000000, 25300], [1710323294680835600, 320225790900000, "Buy", 7329723000000, 42000], [1710323282438553000, 320225751690003, "<PERSON>ll", 7330047000000, 24200], [1710323282438553000, 320225751690002, "<PERSON>ll", 7330047000000, 104700], [1710323282438553000, 320225751690001, "<PERSON>ll", 7330047000000, 103600], [1710323282438553000, 320225751690000, "<PERSON>ll", 7330047000000, 108000], [1710323271638794000, 320225731190000, "<PERSON>ll", 7332625000000, 19900], [1710323267197120000, 320225726310003, "<PERSON>ll", 7332625000000, 14200], [1710323267197120000, 320225726310002, "<PERSON>ll", 7332625000000, 31900], [1710323267197120000, 320225726310001, "<PERSON>ll", 7332625000000, 25300], [1710323267197120000, 320225726310000, "<PERSON>ll", 7332625000000, 28600], [1710323244168935000, 320225695570000, "<PERSON>ll", 7332838000000, 2054400], [1710323241818537000, 320225694790000, "<PERSON>ll", 7333309000000, 2969900], [1710323238152814800, 320225693270000, "<PERSON>ll", 7332513000000, 2084100], [1710323223852291300, 320225679680000, "Buy", 7330048000000, 46300], [1710323209287555300, 320225667900003, "Buy", 7332625000000, 167200], [1710323209287555300, 320225667900002, "Buy", 7332624000000, 1317500], [1710323209287555300, 320225667900001, "Buy", 7332624000000, 3295000], [1710323209287555300, 320225667900000, "Buy", 7332624000000, 655100], [1710323209232359700, 320225667840000, "<PERSON>ll", 7330339000000, 1972800], [1710323197493528300, 320225638520000, "<PERSON>ll", 73323********, 2473100], [1710323193716141600, 320225624730000, "<PERSON>ll", 7332469000000, 1718000], [1710323191490663200, 320225619490000, "<PERSON>ll", 7336173000000, 2562000], [1710323189120270600, 320225618800001, "<PERSON>ll", 7334090000000, 477200], [1710323189120270600, 320225618800000, "<PERSON>ll", 7336136000000, 2131200], [1710323185143108900, 320225614560000, "<PERSON>ll", 7336031000000, 2110800], [1710323183847708200, 320225613690000, "<PERSON>ll", 7335970000000, 1977400], [1710323183847256300, 320225613680000, "<PERSON>ll", 7335970000000, 310000], [1710323176625208600, 320225594870000, "<PERSON>ll", 7339681000000, 2374100], [1710323172202568000, 320225592650000, "<PERSON>ll", 7339550000000, 2975200], [1710323171277108200, 320225591550000, "<PERSON>ll", 7339519000000, 2170700], [1710323169388432600, 320225589330000, "<PERSON>ll", 7339488000000, 1687900], [1710323165369005800, 320225585280000, "<PERSON>ll", 7339362000000, 2878500], [1710323160872251600, 320225577770000, "<PERSON>ll", 7339241000000, 2844100], [1710323157696601900, 320225573660000, "<PERSON>ll", 7339151000000, 2052300], [1710323156488259000, 320225572470000, "<PERSON>ll", 7339121000000, 2677000], [1710323155238442500, 320225571360000, "<PERSON>ll", 7339091000000, 1911400], [1710323153399285800, 320225570170000, "<PERSON>ll", 7339032000000, 2087400], [1710323150303712300, 320225568790000, "<PERSON>ll", 7338403000000, 2927500], [1710323148017295000, 320225566930000, "<PERSON>ll", 7338342000000, 2298400], [1710323143016200200, 320225562150000, "<PERSON>ll", 7338192000000, 2848000], [1710323141645950700, 320225561660000, "<PERSON>ll", 7338160000000, 2111200], [1710323138219497500, 320225559490000, "<PERSON>ll", 7337538000000, 2278500], [1710323137210374000, 320225559370000, "<PERSON>ll", 7337506000000, 2624400], [1710323136504134100, 320225559320000, "<PERSON>ll", 7337474000000, 2145200], [1710323134927417900, 320225559090000, "<PERSON>ll", 7337444000000, 109600], [1710323134926933500, 320225559080000, "<PERSON>ll", 7337444000000, 2500000], [1710323134029475000, 320225558550000, "<PERSON>ll", 7337412000000, 1796300], [1710323131196659500, 320225556080000, "<PERSON>ll", 7337351000000, 2875800], [1710323129996030700, 320225554770000, "<PERSON>ll", 7337319000000, 2725000], [1710323128958806300, 320225554640000, "<PERSON>ll", 7337288000000, 2904400], [1710323125957086200, 320225553940000, "<PERSON>ll", 7337195000000, 2726500], [1710323122135705900, 320225543890000, "<PERSON>ll", 7337101000000, 2256500], [1710323120128786200, 320225542140000, "<PERSON>ll", 7337036000000, 1882100], [1710323115706636800, 320225537790000, "<PERSON>ll", 7334031000000, 2946300], [1710323114051872500, 320225536730000, "<PERSON>ll", 7333999000000, 2423200], [1710323111876986400, 320225536250000, "<PERSON>ll", 7333937000000, 1661100], [1710323106990414800, 320225533640000, "<PERSON>ll", 7333804000000, 2808000], [1710323103391554300, 320225524690000, "<PERSON>ll", 7333697000000, 2822100], [1710323100240895200, 320225522030000, "<PERSON>ll", 7333601000000, 2036600], [1710323099378109700, 320225521520000, "<PERSON>ll", 7333576000000, 2360800], [1710323096746824400, 320225518420000, "<PERSON>ll", 7333531000000, 2026300], [1710323095251126300, 320225517510000, "<PERSON>ll", 7333506000000, 2611700], [1710323093339962000, 320225516410000, "<PERSON>ll", 7333457000000, 2053300], [1710323089932334000, 320225513190000, "<PERSON>ll", 7333389000000, 670600], [1710323089439291600, 320225512300000, "<PERSON>ll", 7333389000000, 1654300], [1710323087158801400, 320225510510003, "<PERSON>ll", 7333319000000, 290700], [1710323087158801400, 320225510510002, "<PERSON>ll", 73333********, 1321500], [1710323087158801400, 320225510510001, "<PERSON>ll", 73333********, 145100], [1710323087158801400, 320225510510000, "<PERSON>ll", 73333********, 409400], [1710323085833000400, 320225508010000, "<PERSON>ll", 7333296000000, 1663300], [1710323083580978400, 320225505730000, "<PERSON>ll", 7333253000000, 1926700], [1710322991347433200, 320225436190000, "Buy", 7339161000000, 2165200], [1710322987764358000, 320225432960000, "Buy", 7339216000000, 1985500], [1710322982704824000, 320225428220000, "Buy", 7339307000000, 1786900], [1710322975875388200, 320225421540000, "Buy", 7339417000000, 2787500], [1710322965639000000, 320225413120000, "Buy", 7339565000000, 2614100], [1710322962064760800, 320225411560000, "Buy", 73396********, 2624300], [1710322959717159400, 320225411010000, "Buy", 7339658000000, 2495300], [1710322952860335900, 320225409200000, "Buy", 7339772000000, 2100800], [1710322952747710700, 320225409110000, "<PERSON>ll", 7338771000000, 72200], [1710322931430416600, 320225386680001, "Buy", 7337895000000, 473900], [1710322931430416600, 320225386680000, "Buy", 7337895000000, 1322500], [1710322924521571800, 320225382300000, "<PERSON>ll", 7333279000000, 2279200], [1710322919882848000, 320225379970000, "<PERSON>ll", 7332789000000, 2459800], [1710322911785194500, 320225376340000, "<PERSON>ll", 7330864000000, 1617100], [1710322906966850300, 320225373070000, "<PERSON>ll", 7330804000000, 1816300], [1710322895932160800, 320225356950000, "<PERSON>ll", 7333315000000, 762500], [1710322891561465300, 320225350610000, "<PERSON>ll", 7333315000000, 32200], [1710322888542204200, 320225347490000, "<PERSON>ll", 7333315000000, 12000], [1710322888366608000, 320225347460000, "<PERSON>ll", 7333315000000, 100000], [1710322888066272300, 320225347160000, "<PERSON>ll", 7333315000000, 270000], [1710322886942507800, 320225346000000, "<PERSON>ll", 7333315000000, 370000], [1710322886283956500, 320225345100000, "<PERSON>ll", 7333315000000, 950600], [1710322883192652500, 320225342760000, "<PERSON>ll", 7333315000000, 860000], [1710322878348826400, 320225336560000, "<PERSON>ll", 7332525000000, 637200], [1710322875693991700, 320225334490006, "<PERSON>ll", 7334555000000, 1230000], [1710322875693991700, 320225334490005, "<PERSON>ll", 7334556000000, 254000], [1710322875693991700, 320225334490004, "<PERSON>ll", 7334556000000, 1137300], [1710322875693991700, 320225334490003, "<PERSON>ll", 7334556000000, 1309300], [1710322875693991700, 320225334490002, "<PERSON>ll", 7334556000000, 1309800], [1710322875693991700, 320225334490001, "<PERSON>ll", 7334556000000, 50600], [1710322875693991700, 320225334490000, "<PERSON>ll", 7334556000000, 147800], [1710322875665935400, 320225334470000, "Buy", 7337822000000, 1684000], [1710322871517305000, 320225325370000, "<PERSON>ll", 7334287000000, 2983400], [1710322869937659600, 320225324280000, "<PERSON>ll", 7332593000000, 11350000], [1710322869317891800, 320225323250001, "<PERSON>ll", 7332593000000, 16114600], [1710322869317891800, 320225323250000, "<PERSON>ll", 7332593000000, 1885400], [1710322869195609900, 320225323190000, "<PERSON>ll", 7333604000000, 1833400], [1710322867067192000, 320225321630000, "<PERSON>ll", 7333364000000, 1855700], [1710322865064599300, 320225320120000, "<PERSON>ll", 7332579000000, 2408400], [1710322863105886200, 320225319360000, "<PERSON>ll", 7332379000000, 2509000], [1710322860221656800, 320225318460000, "<PERSON>ll", 7332350000000, 2438200], [1710322859552023800, 320225317940000, "<PERSON>ll", 7332337000000, 200000], [1710322856038646000, 320225316050000, "<PERSON>ll", 7331704000000, 2741200], [1710322854362485800, 320225315140000, "<PERSON>ll", 7331690000000, 2541800], [1710322850554010000, 320225312840000, "<PERSON>ll", 7330758000000, 1668100], [1710322838001520000, 320225303810000, "Buy", 7330048000000, 49600], [1710322827492089900, 320225295080000, "Buy", 7330048000000, 26400], [1710322820934231000, 320225293060000, "Buy", 7330048000000, 19800], [1710322812517551900, 320225286230000, "Buy", 7330048000000, 23100], [1710322804396051200, 320225281060000, "Buy", 7330048000000, 22000], [1710322683161606000, 320225170810000, "Buy", 7330048000000, 102500], [1710322680117836500, 320225166010000, "<PERSON>ll", 7330123000000, 2035900], [1710322676552507400, 320225162370000, "<PERSON>ll", 7330079000000, 2777400], [1710322671113266000, 320225158180003, "<PERSON>ll", 7330006000000, 1006300], [1710322671113266000, 320225158180002, "<PERSON>ll", 7330006000000, 49400], [1710322671113266000, 320225158180001, "<PERSON>ll", 7330006000000, 50700], [1710322671113266000, 320225158180000, "<PERSON>ll", 7330006000000, 655800], [1710322667902471700, 320225153690000, "<PERSON>ll", 7329979000000, 2446300], [1710322655379031300, 320225141080000, "<PERSON>ll", 7326334000000, 2502100], [1710322653710809900, 320225139510000, "<PERSON>ll", 73259********, 1698400], [1710322647963094500, 320225135520000, "Buy", 7328830000000, 49600], [1710322638971531000, 320225122970000, "Buy", 7330843000000, 52900], [1710322633126270700, 320225117790000, "Buy", 7330998000000, 47300], [1710322630913565200, 320225111210000, "<PERSON>ll", 7331236000000, 1364000], [1710322628480788700, 320225109240000, "Buy", 7331237000000, 43000], [1710322626264576800, 320225107330000, "<PERSON>ll", 7331236000000, 2122300], [1710322623148436000, 320225099090000, "<PERSON>ll", 7331246000000, 1826300], [1710322620869733600, 320225097070000, "<PERSON>ll", 7331245000000, 2286500], [1710322619230925000, 320225094940000, "<PERSON>ll", 7331246000000, 2549200], [1710322616380597500, 320225093980000, "<PERSON>ll", 7331245000000, 2599300], [1710322614287623400, 320225093040000, "<PERSON>ll", 7330699000000, 2659400], [1710322610971532300, 320225091020000, "<PERSON>ll", 7330699000000, 2817700], [1710322606444893200, 320225087570000, "<PERSON>ll", 7330699000000, 2518500], [1710322600285973800, 320225084980000, "<PERSON>ll", 7330699000000, 2262200], [1710322594605040600, 320225081910000, "<PERSON>ll", 7330699000000, 2411200], [1710322591896577300, 320225080680000, "<PERSON>ll", 7330659000000, 1775200], [1710322588576967000, 320225076250000, "<PERSON>ll", 7330533000000, 1691500], [1710322587129344800, 320225071790001, "<PERSON>ll", 7330699000000, 58342200], [1710322587129344800, 320225071790000, "<PERSON>ll", 7330699000000, 1670400], [1710322585932975000, 320225069890000, "<PERSON>ll", 7330956000000, 2081400], [1710322550183216600, 320225022800000, "<PERSON>ll", 7329629000000, 1832200], [1710322546048859000, 320225021450000, "<PERSON>ll", 7329501000000, 205200], [1710322545410322000, 320225021390000, "<PERSON>ll", 7329501000000, 2085500], [1710322541579720700, 320225019790000, "<PERSON>ll", 7329325000000, 2253300], [1710322515410022400, 320224999250000, "<PERSON>ll", 7328390000000, 2270300], [1710322513719844400, 320224997820000, "<PERSON>ll", 7328306000000, 1653200], [1710322501044013300, 320224983240000, "<PERSON>ll", 7327840000000, 2456800], [1710322499235469000, 320224982390000, "<PERSON>ll", 7327798000000, 2485200], [1710322496095123700, 320224979730000, "<PERSON>ll", 7327668000000, 2492900], [1710322481227121400, 320224963550000, "Buy", 7331247000000, 5444200], [1710322481208497400, 320224963540000, "<PERSON>ll", 7327118000000, 1667500], [1710322397282478300, 320224847780000, "<PERSON>ll", 7340000000000, 3070900], [1710322395008539100, 320224842620000, "Buy", 7340001000000, 2567600], [1710322388875245800, 320224837450000, "Buy", 7340001000000, 2807300], [1710322384350223400, 320224834360000, "Buy", 7340001000000, 2216200], [1710322380184031000, 320224831900000, "Buy", 7340001000000, 2537200], [1710322374132482800, 320224829530000, "Buy", 7340001000000, 1932300], [1710322371157502200, 320224828630000, "Buy", 7340001000000, 2275800], [1710322367272929500, 320224825760000, "Buy", 7340111000000, 2627400], [1710322362066310000, 320224816440000, "Buy", 7340111000000, 2024000], [1710322360288060700, 320224815480000, "Buy", 7340111000000, 2247300], [1710322353645590000, 320224814490000, "Buy", 7340111000000, 1958200], [1710322347490876200, 320224812790000, "Buy", 7340111000000, 2497000], [1710322340856094700, 320224811060000, "Buy", 7340111000000, 2472200], [1710322339210060800, 320224808720000, "Buy", 7340111000000, 2933700], [1710322333446705400, 320224805400000, "<PERSON>ll", 7340000000000, 1301800], [1710322330440623900, 320224803830000, "Buy", 7340111000000, 2567100], [1710322326783385300, 320224801110000, "Buy", 7340111000000, 2578300], [1710322322359611000, 320224799130000, "Buy", 7340111000000, 2819000], [1710322318917073200, 320224797850000, "Buy", 7340400000000, 2951000], [1710322317778944500, 320224796680000, "Buy", 7340401000000, 2467100], [1710322316229244000, 320224796100000, "Buy", 7340883000000, 2058200], [1710322314362762800, 320224795690000, "Buy", 7340883000000, 2225100], [1710322301065060900, 320224783030000, "<PERSON>ll", 7340000000000, 750000], [1710322266293374000, 320224762760000, "Buy", 7342242000000, 2694600], [1710322262781208800, 320224761220000, "Buy", 7342250000000, 1996200], [1710322258680609300, 320224756020000, "Buy", 7342263000000, 2611500], [1710322249214343200, 320224745240000, "Buy", 7342796000000, 2972000], [1710322217959928300, 320224715520000, "Buy", 7343690000000, 3800], [1710322217766242300, 320224715160002, "<PERSON>ll", 7340001000000, 389900], [1710322217766242300, 320224715160001, "<PERSON>ll", 7343184000000, 1395900], [1710322217766242300, 320224715160000, "<PERSON>ll", 7343184000000, 214200], [1710322215241561000, 320224708150000, "Buy", 7343696000000, 2620400], [1710322212810506000, 320224706750000, "Buy", 7343702000000, 2609200], [1710322209962169600, 320224705470000, "Buy", 7343708000000, 1722500], [1710322207145595100, 320224704520000, "Buy", 7343717000000, 2703100]], "type": "snapshot"}}, "parsedResponse": [{"info": [1710327659222403800, 320230307260000, "Buy", 7325244000000, 700000], "id": "320230307260000", "symbol": "BTC/USDT", "timestamp": 1710327659222, "datetime": "2024-03-13T11:00:59.222Z", "order": null, "type": null, "side": "buy", "takerOrMaker": null, "price": 73252.44, "amount": 0.007, "cost": 512.76708, "fee": {"cost": null, "currency": null}, "fees": []}]}], "fetchTicker": [{"description": "public spot ticker", "method": "fetchTicker", "input": ["BTC/USDT"], "httpResponse": {"error": null, "id": "0", "result": {"askEp": "7333309000000", "bidEp": "7333308000000", "highEp": "7365097000000", "indexEp": "7341073140000", "lastEp": "7332757000000", "lowEp": "6899979000000", "openEp": "7208000000000", "symbol": "sBTCUSDT", "timestamp": "1710328245236138597", "turnoverEv": "2787473830482084", "volumeEv": "38813663600"}}, "parsedResponse": {"symbol": "BTC/USDT", "timestamp": 1710328245236, "datetime": "2024-03-13T11:10:45.236Z", "high": 73650.97, "low": 68999.79, "bid": 73333.08, "bidVolume": null, "ask": 73333.09, "askVolume": null, "vwap": 71816.81840727048, "open": 72080, "close": 73327.57, "last": 73327.57, "previousClose": null, "change": 1247.57, "percentage": 1.730812985571587, "average": 72703.78, "baseVolume": 388.136636, "quoteVolume": 27874738.30482084, "markPrice": null, "indexPrice": null, "info": {"askEp": "7333309000000", "bidEp": "7333308000000", "highEp": "7365097000000", "indexEp": "7341073140000", "lastEp": "7332757000000", "lowEp": "6899979000000", "openEp": "7208000000000", "symbol": "sBTCUSDT", "timestamp": "1710328245236138597", "turnoverEv": "2787473830482084", "volumeEv": "38813663600"}}}], "fetchFundingRate": [{"description": "linear swap fetch funding rate", "method": "fetchFundingRate", "input": ["BTC/USDT:USDT"], "httpResponse": {"error": null, "id": "0", "result": {"closeRp": "100425", "fundingRateRr": "0.0001", "highRp": "102500", "indexPriceRp": "100448.96664571", "lowRp": "99168.5", "markPriceRp": "100425", "openInterestRv": "0", "openRp": "100580", "predFundingRateRr": "0.0001", "symbol": "BTCUSDT", "timestamp": "1734087634274355552", "turnoverRv": "1307890761.3133", "volumeRq": "13036.35"}}, "parsedResponse": {"info": {"closeRp": "100425", "fundingRateRr": "0.0001", "highRp": "102500", "indexPriceRp": "100448.96664571", "lowRp": "99168.5", "markPriceRp": "100425", "openInterestRv": "0", "openRp": "100580", "predFundingRateRr": "0.0001", "symbol": "BTCUSDT", "timestamp": "1734087634274355552", "turnoverRv": "1307890761.3133", "volumeRq": "13036.35"}, "symbol": "BTC/USDT:USDT", "markPrice": 100425, "indexPrice": 100448.96664571, "interestRate": null, "estimatedSettlePrice": null, "timestamp": 1734087634274, "datetime": "2024-12-13T11:00:34.274Z", "fundingRate": 0.0001, "fundingTimestamp": null, "fundingDatetime": null, "nextFundingRate": 0.0001, "nextFundingTimestamp": null, "nextFundingDatetime": null, "previousFundingRate": null, "previousFundingTimestamp": null, "previousFundingDatetime": null, "interval": null}}], "fetchConvertQuote": [{"description": "fetch convert quote", "method": "fetchConvertQuote", "disabled": true, "input": ["BTC", "USDT", 0.001], "httpResponse": {"code": 0, "msg": "OK", "data": {"code": "GLBUICF5MAHC5MMAJKLEEDAG4DUVJYTAATOIRVXPQI7WR6HTZU2566ZCOF2W65DFIJXSEOT3EJTHE33NIN2XE4TFNZRXSIR2EJBFIQZCFQRHI32DOVZHEZLOMN4SEORCKVJUIVBCFQRG64TJM5UW4IR2GEYDAMBQGAWCE4DSNFRWKIR2EI4TMMBRGAXDKOBWGIYDOMJQEIWCE4DSNFRWKUZYEI5DSNRQGEYDKOBWGIYDOMJQFQRHA4TJMNSUM33SINQWYYZCHI4TMMBRGAXDKOBWGIYDOMJQGAYDAMBQGAYDAMBQGAYDAMBMEJYHE33DMVSWI4ZCHIRDSNROGAYTANJYGYZDAIRMEJYHE33DMVSWI42FOYRDUOJWGAYTANJYGYZDALBCMNXW45TFOJZWS33OIZSWKSLUMVWXGIR2PMRGG33OOZSXE43JN5XEMZLFKJ2WYZLTEI5FWISHKJAUIVKBJRPUMRKFFUYSEXJMEJ2G65DBNRDGKZKSMF2GKIR2GAXDAMBRGAYDALBCORXXIYLMIZSWKRLWEI5DCMBQFQRGEYLTMVDGKZKSMF2GKIR2GAXDAMBRGAYDALBCMJQXGZKGMVSUK5RCHIYTAMBMEJTXEYLEOVQWYRTFMVJGC5DFEI5DALRQGAYTAMBQFQRGO4TBMR2WC3CGMVSUK5RCHIYTAMBMEJYGK3TBNR2HSRTFMVJGC5DFEI5G45LMNQWCE4DFNZQWY5DZIZSWKRLWEI5DALBCO5UGS5DFJRUXG5CGMVSVEYLUMURDU3TVNRWCYITXNBUXIZKMNFZXIRTFMVCXMIR2GAWCEZDFMZQXK3DUIZSWKUTBORSSEOTOOVWGYLBCMRSWMYLVNR2EMZLFIV3CEORQFQRHI2DJOJSFAYLSOR4UMZLFKJQXIZJCHJXHK3DMFQRHI2DJOJSFAYLSOR4UMZLFIV3CEORQFQRHA4TJMNSSEOTOOVWGYLBCOBZGSY3FKR4XAZJCHJXHK3DMPUWCE5DJMNVUS3TGN4RDU6ZCOBZGSY3FKR4XAZJCHIRFERSREIWCE4TFMZJXS3LCN5WCEORCFZBFIQ2VKNCFIUSGKERCYITQOJUWGZJCHI4TMMJQGY3DSMRZFQRHGY3BNRSSEORUFQRHI2LNMVZXIYLNOARDUMJXGM4TQOBTGM2DOMBUGE2DCMRQHAYSYITUN5BGC43FIN2XE4TFNZRXSIR2ORZHKZL5FQRG233WMVHXAIR2GAWCEZLYOBUXEZKBOQRDUMJXGM4TQOBTGM2TKMJZGAWCE4TFOF2WK43UIF2CEORRG4ZTSOBYGMZTINZRHEYCYITROVXXIZKBOQRDUMJXGM4TQOBTGM2DOMBUGEWCE4LVN52GKSLEEI5DCNZTHE4DQMZTGQ3TANBRFQRHC5LPORSVA4TJMNSVGNBCHI4TMMJQGY3DSMRZFQRG64TJM5UW4UTREI5DALRQGAYTAMBQGAYCYITROVXXIZKQOJUWGZJCHI4TMMJQGYXDMOJSHEWCE4DSN5RWKZLEONJHCIR2HE3C4MBRGA2TQNRSGA3TCLBCMJZGSZDHMURDUZTBNRZWK7JMEJ2XGZLSJFSCEORUHAYTINBTGQWCEYTJMQRDUIRCFQRHC5LPORSUWZLZEI5CEYJZGYZWGNDGMMWWMMRWHAWTIODFMQWTQOJSMIWTAODCMQ3DCYRRMMYTGNJCPXLJAJIZSUAQAAA", "quoteArgs": {"origin": 100000, "price": "96010.58620710", "proceeds": "96.01058620", "ttlMs": 7000, "expireAt": 1739883355190, "requestAt": 1739883347190, "quoteAt": 1739883347041}}}, "parsedResponse": {"info": {"code": "GLBUICF5MAHC5MMAJKLEEDAG4DUVJYTAATOIRVXPQI7WR6HTZU2566ZCOF2W65DFIJXSEOT3EJTHE33NIN2XE4TFNZRXSIR2EJBFIQZCFQRHI32DOVZHEZLOMN4SEORCKVJUIVBCFQRG64TJM5UW4IR2GEYDAMBQGAWCE4DSNFRWKIR2EI4TMMBRGAXDKOBWGIYDOMJQEIWCE4DSNFRWKUZYEI5DSNRQGEYDKOBWGIYDOMJQFQRHA4TJMNSUM33SINQWYYZCHI4TMMBRGAXDKOBWGIYDOMJQGAYDAMBQGAYDAMBQGAYDAMBMEJYHE33DMVSWI4ZCHIRDSNROGAYTANJYGYZDAIRMEJYHE33DMVSWI42FOYRDUOJWGAYTANJYGYZDALBCMNXW45TFOJZWS33OIZSWKSLUMVWXGIR2PMRGG33OOZSXE43JN5XEMZLFKJ2WYZLTEI5FWISHKJAUIVKBJRPUMRKFFUYSEXJMEJ2G65DBNRDGKZKSMF2GKIR2GAXDAMBRGAYDALBCORXXIYLMIZSWKRLWEI5DCMBQFQRGEYLTMVDGKZKSMF2GKIR2GAXDAMBRGAYDALBCMJQXGZKGMVSUK5RCHIYTAMBMEJTXEYLEOVQWYRTFMVJGC5DFEI5DALRQGAYTAMBQFQRGO4TBMR2WC3CGMVSUK5RCHIYTAMBMEJYGK3TBNR2HSRTFMVJGC5DFEI5G45LMNQWCE4DFNZQWY5DZIZSWKRLWEI5DALBCO5UGS5DFJRUXG5CGMVSVEYLUMURDU3TVNRWCYITXNBUXIZKMNFZXIRTFMVCXMIR2GAWCEZDFMZQXK3DUIZSWKUTBORSSEOTOOVWGYLBCMRSWMYLVNR2EMZLFIV3CEORQFQRHI2DJOJSFAYLSOR4UMZLFKJQXIZJCHJXHK3DMFQRHI2DJOJSFAYLSOR4UMZLFIV3CEORQFQRHA4TJMNSSEOTOOVWGYLBCOBZGSY3FKR4XAZJCHJXHK3DMPUWCE5DJMNVUS3TGN4RDU6ZCOBZGSY3FKR4XAZJCHIRFERSREIWCE4TFMZJXS3LCN5WCEORCFZBFIQ2VKNCFIUSGKERCYITQOJUWGZJCHI4TMMJQGY3DSMRZFQRHGY3BNRSSEORUFQRHI2LNMVZXIYLNOARDUMJXGM4TQOBTGM2DOMBUGE2DCMRQHAYSYITUN5BGC43FIN2XE4TFNZRXSIR2ORZHKZL5FQRG233WMVHXAIR2GAWCEZLYOBUXEZKBOQRDUMJXGM4TQOBTGM2TKMJZGAWCE4TFOF2WK43UIF2CEORRG4ZTSOBYGMZTINZRHEYCYITROVXXIZKBOQRDUMJXGM4TQOBTGM2DOMBUGEWCE4LVN52GKSLEEI5DCNZTHE4DQMZTGQ3TANBRFQRHC5LPORSVA4TJMNSVGNBCHI4TMMJQGY3DSMRZFQRG64TJM5UW4UTREI5DALRQGAYTAMBQGAYCYITROVXXIZKQOJUWGZJCHI4TMMJQGYXDMOJSHEWCE4DSN5RWKZLEONJHCIR2HE3C4MBRGA2TQNRSGA3TCLBCMJZGSZDHMURDUZTBNRZWK7JMEJ2XGZLSJFSCEORUHAYTINBTGQWCEYTJMQRDUIRCFQRHC5LPORSUWZLZEI5CEYJZGYZWGNDGMMWWMMRWHAWTIODFMQWTQOJSMIWTAODCMQ3DCYRRMMYTGNJCPXLJAJIZSUAQAAA", "quoteArgs": {"origin": 100000, "price": "96010.58620710", "proceeds": "96.01058620", "ttlMs": 7000, "expireAt": 1739883355190, "requestAt": 1739883347190, "quoteAt": 1739883347041}}, "timestamp": 1739883347190, "datetime": "2025-02-18T12:55:47.190Z", "id": "GLBUICF5MAHC5MMAJKLEEDAG4DUVJYTAATOIRVXPQI7WR6HTZU2566ZCOF2W65DFIJXSEOT3EJTHE33NIN2XE4TFNZRXSIR2EJBFIQZCFQRHI32DOVZHEZLOMN4SEORCKVJUIVBCFQRG64TJM5UW4IR2GEYDAMBQGAWCE4DSNFRWKIR2EI4TMMBRGAXDKOBWGIYDOMJQEIWCE4DSNFRWKUZYEI5DSNRQGEYDKOBWGIYDOMJQFQRHA4TJMNSUM33SINQWYYZCHI4TMMBRGAXDKOBWGIYDOMJQGAYDAMBQGAYDAMBQGAYDAMBMEJYHE33DMVSWI4ZCHIRDSNROGAYTANJYGYZDAIRMEJYHE33DMVSWI42FOYRDUOJWGAYTANJYGYZDALBCMNXW45TFOJZWS33OIZSWKSLUMVWXGIR2PMRGG33OOZSXE43JN5XEMZLFKJ2WYZLTEI5FWISHKJAUIVKBJRPUMRKFFUYSEXJMEJ2G65DBNRDGKZKSMF2GKIR2GAXDAMBRGAYDALBCORXXIYLMIZSWKRLWEI5DCMBQFQRGEYLTMVDGKZKSMF2GKIR2GAXDAMBRGAYDALBCMJQXGZKGMVSUK5RCHIYTAMBMEJTXEYLEOVQWYRTFMVJGC5DFEI5DALRQGAYTAMBQFQRGO4TBMR2WC3CGMVSUK5RCHIYTAMBMEJYGK3TBNR2HSRTFMVJGC5DFEI5G45LMNQWCE4DFNZQWY5DZIZSWKRLWEI5DALBCO5UGS5DFJRUXG5CGMVSVEYLUMURDU3TVNRWCYITXNBUXIZKMNFZXIRTFMVCXMIR2GAWCEZDFMZQXK3DUIZSWKUTBORSSEOTOOVWGYLBCMRSWMYLVNR2EMZLFIV3CEORQFQRHI2DJOJSFAYLSOR4UMZLFKJQXIZJCHJXHK3DMFQRHI2DJOJSFAYLSOR4UMZLFIV3CEORQFQRHA4TJMNSSEOTOOVWGYLBCOBZGSY3FKR4XAZJCHJXHK3DMPUWCE5DJMNVUS3TGN4RDU6ZCOBZGSY3FKR4XAZJCHIRFERSREIWCE4TFMZJXS3LCN5WCEORCFZBFIQ2VKNCFIUSGKERCYITQOJUWGZJCHI4TMMJQGY3DSMRZFQRHGY3BNRSSEORUFQRHI2LNMVZXIYLNOARDUMJXGM4TQOBTGM2DOMBUGE2DCMRQHAYSYITUN5BGC43FIN2XE4TFNZRXSIR2ORZHKZL5FQRG233WMVHXAIR2GAWCEZLYOBUXEZKBOQRDUMJXGM4TQOBTGM2TKMJZGAWCE4TFOF2WK43UIF2CEORRG4ZTSOBYGMZTINZRHEYCYITROVXXIZKBOQRDUMJXGM4TQOBTGM2DOMBUGEWCE4LVN52GKSLEEI5DCNZTHE4DQMZTGQ3TANBRFQRHC5LPORSVA4TJMNSVGNBCHI4TMMJQGY3DSMRZFQRG64TJM5UW4UTREI5DALRQGAYTAMBQGAYCYITROVXXIZKQOJUWGZJCHI4TMMJQGYXDMOJSHEWCE4DSN5RWKZLEONJHCIR2HE3C4MBRGA2TQNRSGA3TCLBCMJZGSZDHMURDUZTBNRZWK7JMEJ2XGZLSJFSCEORUHAYTINBTGQWCEYTJMQRDUIRCFQRHC5LPORSUWZLZEI5CEYJZGYZWGNDGMMWWMMRWHAWTIODFMQWTQOJSMIWTAODCMQ3DCYRRMMYTGNJCPXLJAJIZSUAQAAA", "fromCurrency": "BTC", "fromAmount": 0.001, "toCurrency": "USDT", "toAmount": 96.0105862, "price": 96010.5862071, "fee": null}}], "createConvertTrade": [{"description": "create a conversion trade", "method": "createConvertTrade", "input": ["GLL45PA3J4BDXXXS24HYT542UN4V6MASXYMMC54W2NJXHBKNDD5OG6ZCOF2W65DFIJXSEOT3EJTHE33NIN2XE4TFNZRXSIR2EJKVGRCUEIWCE5DPIN2XE4TFNZRXSIR2EJBFIQZCFQRG64TJM5UW4IR2GEYDAMBQGAYDAMBQFQRHA4TJMNSSEORCGAXDAMBQGAYTAMZVEIWCE4DSNFRWKUZYEI5DCMBTGUWCE4DSNFRWKRTPOJBWC3DDEI5DALRQGAYDAMJQGM2TSMRXGE3TQMJVGYYTIMRZGQWCE4DSN5RWKZLEOMRDUIRQFYYDAMBRGAZTKOJCFQRHA4TPMNSWKZDTIV3CEORRGAZTKOJMEJRW63TWMVZHG2LPNZDGKZKJORSW24ZCHJ5SEY3PNZ3GK4TTNFXW4RTFMVJHK3DFOMRDUWZCI5JECRCVIFGF6RSFIUWTCIS5FQRHI33UMFWEMZLFKJQXIZJCHIYC4MBQGEYDAMBMEJ2G65DBNRDGKZKFOYRDUMJQGAYDAMBQFQRGEYLTMVDGKZKSMF2GKIR2GAXDAMBRGAYDALBCMJQXGZKGMVSUK5RCHIYTAMBQGAYDALBCM5ZGCZDVMFWEMZLFKJQXIZJCHIYC4MBQGEYDAMBMEJTXEYLEOVQWYRTFMVCXMIR2GEYDAMBQGAYCYITQMVXGC3DUPFDGKZKSMF2GKIR2NZ2WY3BMEJYGK3TBNR2HSRTFMVCXMIR2GAWCE53INF2GKTDJON2EMZLFKJQXIZJCHJXHK3DMFQRHO2DJORSUY2LTORDGKZKFOYRDUMBMEJSGKZTBOVWHIRTFMVJGC5DFEI5G45LMNQWCEZDFMZQXK3DUIZSWKRLWEI5DALBCORUGS4TEKBQXE5DZIZSWKUTBORSSEOTOOVWGYLBCORUGS4TEKBQXE5DZIZSWKRLWEI5DALBCOBZGSY3FEI5G45LMNQWCE4DSNFRWKVDZOBSSEOTOOVWGY7JMEJ2GSY3LJFXGM3ZCHJ5SE4DSNFRWKVDZOBSSEORCKZLUCUC7KBJESQ2FL5EU2UCBINKCELBCOJSWMU3ZNVRG63BCHIRHGQSUINKVGRCUEIWCE4DSNFRWKIR2HE3DIMZVGM2TAMBQGAYDALBCONRWC3DFEI5DQLBCORUW2ZLTORQW24BCHIYTOMZZHE3DOMRYGI2DSMZWGY3DKNRZFQRHI32CMFZWKQ3VOJZGK3TDPERDUZTBNRZWK7JMEJWW65TFJ5YCEORQFQRGK6DQNFZGKQLUEI5DCNZTHE4TMNZSHEYTIMJRFQRHEZLROVSXG5CBOQRDUMJXGM4TSNRXGI4DGNBRGEWCE4LVN52GKQLUEI5DCNZTHE4TMNZSHAZDIOJTFQRHC5LPORSUSZBCHIYTOMZZHE3DOMRYGI2DSMZMEJYXK33UMVIHE2LDMVJTIIR2HE3DIMZVGM2TAMBQGAYDALBCN5ZGSZ3JNZJHCIR2GEYC4MBQGAYDAMBQGAWCE4LVN52GKUDSNFRWKIR2HE3DIMZVFYZTKMBQGAYDAMBMEJYHE33DMVSWI42SOERDUMBOGAYDAMJQGM2TSMRXGE3TQMJVGYYTIMRZGQ4SYITCOJUWIZ3FEI5GMYLMONSX2LBCOVZWK4SJMQRDUOJUGA3DMNRMEJRGSZBCHIRCELBCOF2W65DFJNSXSIR2EI3DMODGMUZDSNBNHFRTMMZNGQ2TMMBNHFTDEMZNMM4GIZJVGMZTKZRUG44SE7NUKQTB5FIBAAAA", "USDT", "BTC", 10], "httpResponse": {"code": 0, "msg": "OK", "data": {"moveOp": 0, "fromCurrency": "USDT", "toCurrency": "BTC", "fromAmountEv": 1000000000, "toAmountEv": 10352, "linkKey": "11bc87f1-b28f-4313-a7dd-4b297b032310", "status": 10}}, "parsedResponse": {"info": {"moveOp": 0, "fromCurrency": "USDT", "toCurrency": "BTC", "fromAmountEv": 1000000000, "toAmountEv": 10352, "linkKey": "11bc87f1-b28f-4313-a7dd-4b297b032310", "status": 10}, "timestamp": null, "datetime": null, "id": null, "fromCurrency": "USDT", "fromAmount": 10, "toCurrency": "BTC", "toAmount": 0.00010352, "price": null, "fee": null}}], "fetchConvertTradeHistory": [{"description": "fetch the history of conversions from USDT", "method": "fetchConvertTradeHistory", "input": ["USDT", null, 1], "httpResponse": {"code": 0, "msg": "OK", "data": {"total": 6, "rows": [{"linkKey": "668fe294-9c63-4560-9f23-c8de5335f479", "createTime": *************, "fromCurrency": "USDT", "toCurrency": "BTC", "fromAmountEv": 1000000000, "toAmountEv": 10359, "status": 10, "conversionRate": 1035, "errorCode": 0}]}}, "parsedResponse": [{"info": {"linkKey": "668fe294-9c63-4560-9f23-c8de5335f479", "createTime": *************, "fromCurrency": "USDT", "toCurrency": "BTC", "fromAmountEv": 1000000000, "toAmountEv": 10359, "status": 10, "conversionRate": 1035, "errorCode": 0}, "timestamp": *************, "datetime": "2025-02-19T12:14:44.000Z", "id": null, "fromCurrency": "USDT", "fromAmount": 10, "toCurrency": "BTC", "toAmount": 0.********, "price": null, "fee": null}]}], "fetchDepositAddress": [{"description": "fetch the USDT deposit address", "method": "fetchDepositAddress", "input": ["USDT"], "httpResponse": {"code": 0, "msg": "OK", "data": {"address": "******************************************", "tag": "", "notice": false, "accountType": 1, "contractName": null, "chainTokenUrl": null, "sign": null}}, "parsedResponse": {"info": {"code": 0, "msg": "OK", "data": {"address": "******************************************", "tag": "", "notice": false, "accountType": 1, "contractName": null, "chainTokenUrl": null, "sign": null}}, "currency": "USDT", "network": null, "address": "******************************************", "tag": null}}]}}