{"exchange": "bitmart", "skipKeys": ["apply_time"], "options": {}, "methods": {"fetchDepositAddress": [{"description": "fetch TRUMP deposit address", "method": "fetchDepositAddress", "input": ["TRUMP"], "httpResponse": {"message": "OK", "code": 1000, "trace": "4fa0e4fecf08417cb4093f8bae79bd35.69.17394376034439885", "data": {"currency": "TRUMP-SOL", "chain": "Solana", "address": "AaNyvxszcCVKD4dSddN1a9mwhStzgTneL8nfCYsETmNV", "address_memo": ""}}, "parsedResponse": {"info": {"currency": "TRUMP-SOL", "chain": "Solana", "address": "AaNyvxszcCVKD4dSddN1a9mwhStzgTneL8nfCYsETmNV", "address_memo": ""}, "currency": "TRUMP", "network": "SOL", "address": "AaNyvxszcCVKD4dSddN1a9mwhStzgTneL8nfCYsETmNV", "tag": null}}, {"description": "fetch USDT deposit address", "method": "fetchDepositAddress", "input": ["USDT"], "httpResponse": {"message": "OK", "code": 1000, "trace": "9eaec51cd80d46d48a1c6b447206c4d6.67.17394377658295303", "data": {"currency": "USDT-TRC20", "chain": "TRON", "address": "TF7HBu9JEALW8fyQTk9P5nFdk9JxAirZHN", "address_memo": ""}}, "parsedResponse": {"info": {"currency": "USDT-TRC20", "chain": "TRON", "address": "TF7HBu9JEALW8fyQTk9P5nFdk9JxAirZHN", "address_memo": ""}, "currency": "USDT", "network": "TRC20", "address": "TF7HBu9JEALW8fyQTk9P5nFdk9JxAirZHN", "tag": null}}, {"description": "fetch USDT deposit address with ERC20 network", "method": "fetchDepositAddress", "input": ["USDT", {"network": "ERC20"}], "httpResponse": {"message": "OK", "code": 1000, "trace": "9eaec51cd80d46d48a1c6b447206c4d6.78.17394379137356390", "data": {"currency": "USDT-ERC20", "chain": "Ethereum", "address": "******************************************", "address_memo": ""}}, "parsedResponse": {"info": {"currency": "USDT-ERC20", "chain": "Ethereum", "address": "******************************************", "address_memo": ""}, "currency": "USDT", "network": "ERC20", "address": "******************************************", "tag": null}}], "fetchDeposits": [{"description": "fetch USDT deposits", "method": "fetchDeposits", "input": ["USDT"], "httpResponse": {"message": "OK", "code": 1000, "trace": "619294ecef584282b26a3be322b1e01f.90.17394426105542179", "data": {"records": [{"withdraw_id": "", "deposit_id": "19017278", "operation_type": "deposit", "currency": "USDT-TRC20", "apply_time": 1695978904000, "arrival_amount": "69", "fee": "0", "status": 3, "address": "TF7HBu9JEALW8fyQTk9P5nFdk9JxAirZHN", "address_memo": "", "tx_id": "0be8fb435d874454538572e087c60946b2dda0fac1850c1a82f98a0d39d816e9"}, {"withdraw_id": "", "deposit_id": "14233682", "operation_type": "deposit", "currency": "USDT-TRC20", "apply_time": 1664789416000, "arrival_amount": "30", "fee": "0", "status": 3, "address": "TF7HBu9JEALW8fyQTk9P5nFdk9JxAirZHN", "address_memo": "", "tx_id": "ec5060a2dad280a24413a872e80875ec8ee14cb3aeb4f78d15011ea589eb8e53"}, {"withdraw_id": "", "deposit_id": "13632954", "operation_type": "deposit", "currency": "USDT-TRC20", "apply_time": 1658846077000, "arrival_amount": "10", "fee": "0", "status": 3, "address": "TF7HBu9JEALW8fyQTk9P5nFdk9JxAirZHN", "address_memo": "", "tx_id": "bb25d9259193994874bfc4b712d9965669754cd2b85022f1878e273dd2d23037"}, {"withdraw_id": "", "deposit_id": "12250959", "operation_type": "deposit", "currency": "USDT-TRC20", "apply_time": 1646162180000, "arrival_amount": "30", "fee": "0", "status": 3, "address": "TF7HBu9JEALW8fyQTk9P5nFdk9JxAirZHN", "address_memo": "", "tx_id": "5675c57e00b1adc677f01d1d894a509f8375ccc5a982635f7bee4c3d043c7681"}]}}, "parsedResponse": [{"info": {"withdraw_id": "", "deposit_id": "12250959", "operation_type": "deposit", "currency": "USDT-TRC20", "apply_time": "1646162180000", "arrival_amount": "30", "fee": "0", "status": 3, "address": "TF7HBu9JEALW8fyQTk9P5nFdk9JxAirZHN", "address_memo": "", "tx_id": "5675c57e00b1adc677f01d1d894a509f8375ccc5a982635f7bee4c3d043c7681"}, "id": "12250959", "currency": "USDT", "amount": 30, "network": "TRC20", "address": "TF7HBu9JEALW8fyQTk9P5nFdk9JxAirZHN", "addressFrom": null, "addressTo": null, "tag": null, "tagFrom": null, "tagTo": null, "status": "ok", "type": "deposit", "updated": null, "txid": "5675c57e00b1adc677f01d1d894a509f8375ccc5a982635f7bee4c3d043c7681", "internal": null, "comment": null, "timestamp": 1646162180000, "datetime": "2022-03-01T19:16:20.000Z", "fee": {"cost": 0, "currency": "USDT"}}, {"info": {"withdraw_id": "", "deposit_id": "13632954", "operation_type": "deposit", "currency": "USDT-TRC20", "apply_time": "1658846077000", "arrival_amount": "10", "fee": "0", "status": 3, "address": "TF7HBu9JEALW8fyQTk9P5nFdk9JxAirZHN", "address_memo": "", "tx_id": "bb25d9259193994874bfc4b712d9965669754cd2b85022f1878e273dd2d23037"}, "id": "13632954", "currency": "USDT", "amount": 10, "network": "TRC20", "address": "TF7HBu9JEALW8fyQTk9P5nFdk9JxAirZHN", "addressFrom": null, "addressTo": null, "tag": null, "tagFrom": null, "tagTo": null, "status": "ok", "type": "deposit", "updated": null, "txid": "bb25d9259193994874bfc4b712d9965669754cd2b85022f1878e273dd2d23037", "internal": null, "comment": null, "timestamp": 1658846077000, "datetime": "2022-07-26T14:34:37.000Z", "fee": {"cost": 0, "currency": "USDT"}}, {"info": {"withdraw_id": "", "deposit_id": "14233682", "operation_type": "deposit", "currency": "USDT-TRC20", "apply_time": "1664789416000", "arrival_amount": "30", "fee": "0", "status": 3, "address": "TF7HBu9JEALW8fyQTk9P5nFdk9JxAirZHN", "address_memo": "", "tx_id": "ec5060a2dad280a24413a872e80875ec8ee14cb3aeb4f78d15011ea589eb8e53"}, "id": "14233682", "currency": "USDT", "amount": 30, "network": "TRC20", "address": "TF7HBu9JEALW8fyQTk9P5nFdk9JxAirZHN", "addressFrom": null, "addressTo": null, "tag": null, "tagFrom": null, "tagTo": null, "status": "ok", "type": "deposit", "updated": null, "txid": "ec5060a2dad280a24413a872e80875ec8ee14cb3aeb4f78d15011ea589eb8e53", "internal": null, "comment": null, "timestamp": 1664789416000, "datetime": "2022-10-03T09:30:16.000Z", "fee": {"cost": 0, "currency": "USDT"}}, {"info": {"withdraw_id": "", "deposit_id": "19017278", "operation_type": "deposit", "currency": "USDT-TRC20", "apply_time": "1695978904000", "arrival_amount": "69", "fee": "0", "status": 3, "address": "TF7HBu9JEALW8fyQTk9P5nFdk9JxAirZHN", "address_memo": "", "tx_id": "0be8fb435d874454538572e087c60946b2dda0fac1850c1a82f98a0d39d816e9"}, "id": "19017278", "currency": "USDT", "amount": 69, "network": "TRC20", "address": "TF7HBu9JEALW8fyQTk9P5nFdk9JxAirZHN", "addressFrom": null, "addressTo": null, "tag": null, "tagFrom": null, "tagTo": null, "status": "ok", "type": "deposit", "updated": null, "txid": "0be8fb435d874454538572e087c60946b2dda0fac1850c1a82f98a0d39d816e9", "internal": null, "comment": null, "timestamp": 1695978904000, "datetime": "2023-09-29T09:15:04.000Z", "fee": {"cost": 0, "currency": "USDT"}}]}, {"description": "Fetch deposits for USDT with since and until", "method": "fetchDeposits", "input": ["USDT", 1662552257155, null, {"until": 1670328257154}], "httpResponse": {"message": "OK", "code": 1000, "trace": "6a4ee3034edf4a6db5002cbf5cddf961.73.17403125405173083", "data": {"records": [{"withdraw_id": "", "deposit_id": "14233682", "operation_type": "deposit", "currency": "USDT-TRC20", "apply_time": 1664789416000, "arrival_amount": "30", "fee": "0", "status": 3, "address": "TF7HBu9JEALW8fyQTk9P5nFdk9JxAirZHN", "address_memo": "", "tx_id": "ec5060a2dad280a24413a872e80875ec8ee14cb3aeb4f78d15011ea589eb8e53"}]}}, "parsedResponse": [{"info": {"withdraw_id": "", "deposit_id": "14233682", "operation_type": "deposit", "currency": "USDT-TRC20", "apply_time": "1664789416000", "arrival_amount": "30", "fee": "0", "status": 3, "address": "TF7HBu9JEALW8fyQTk9P5nFdk9JxAirZHN", "address_memo": "", "tx_id": "ec5060a2dad280a24413a872e80875ec8ee14cb3aeb4f78d15011ea589eb8e53"}, "id": "14233682", "currency": "USDT", "amount": 30, "network": "TRC20", "address": "TF7HBu9JEALW8fyQTk9P5nFdk9JxAirZHN", "addressFrom": null, "addressTo": null, "tag": null, "tagFrom": null, "tagTo": null, "status": "ok", "type": "deposit", "updated": null, "txid": "ec5060a2dad280a24413a872e80875ec8ee14cb3aeb4f78d15011ea589eb8e53", "internal": null, "comment": null, "timestamp": 1664789416000, "datetime": "2022-10-03T09:30:16.000Z", "fee": {"cost": 0, "currency": "USDT"}}]}], "fetchClosedOrders": [{"description": "Fetch swap closed orders", "method": "fetchClosedOrders", "input": ["LTC/USDT:USDT", null, 1], "httpResponse": {"code": "1000", "message": "Ok", "data": [{"order_id": "231116363679831", "client_order_id": "", "price": "100", "size": "1", "symbol": "LTCUSDT", "state": "4", "side": "4", "type": "limit", "leverage": "1", "open_type": "cross", "deal_avg_price": "0", "deal_size": "0", "create_time": "1699638121672", "update_time": "1699638158472"}], "trace": "4caf855074664097ac6ba5257c47305d.74.17002156265082169"}, "parsedResponse": [{"id": "231116363679831", "clientOrderId": null, "info": {"order_id": "231116363679831", "client_order_id": "", "price": "100", "size": "1", "symbol": "LTCUSDT", "state": "4", "side": "4", "type": "limit", "leverage": "1", "open_type": "cross", "deal_avg_price": "0", "deal_size": "0", "create_time": "1699638121672", "update_time": "1699638158472"}, "timestamp": 1699638121672, "datetime": "2023-11-10T17:42:01.672Z", "lastTradeTimestamp": 1699638158472, "symbol": "LTC/USDT:USDT", "type": "limit", "timeInForce": null, "postOnly": null, "side": "sell", "price": 100, "stopPrice": null, "triggerPrice": null, "amount": 1, "cost": 0, "average": null, "filled": 0, "remaining": 1, "status": "closed", "fee": null, "trades": [], "fees": [], "lastUpdateTimestamp": null, "reduceOnly": null, "takeProfitPrice": null, "stopLossPrice": null}]}, {"description": "Spot closed order", "method": "fetchClosedOrders", "input": ["LTC/USDT", null, 1], "httpResponse": {"code": "1000", "message": "success", "data": [{"orderId": "201060879563236943", "clientOrderId": "201060879563236943", "symbol": "LTC_USDT", "side": "sell", "orderMode": "spot", "type": "market", "state": "filled", "price": "0.0000", "priceAvg": "71.0566", "size": "0.100", "filledSize": "0.100", "notional": "0.00000000", "filledNotional": "7.10566000", "createTime": "1700215845644", "updateTime": "1700215845697"}], "trace": "f3786e6984b742a8b357ea2377e9883f.59.17002158518723115"}, "parsedResponse": [{"id": "201060879563236943", "clientOrderId": "201060879563236943", "info": {"orderId": "201060879563236943", "clientOrderId": "201060879563236943", "symbol": "LTC_USDT", "side": "sell", "orderMode": "spot", "type": "market", "state": "filled", "price": "0.0000", "priceAvg": "71.0566", "size": "0.100", "filledSize": "0.100", "notional": "0.00000000", "filledNotional": "7.10566000", "createTime": "1700215845644", "updateTime": "1700215845697"}, "timestamp": 1700215845644, "datetime": "2023-11-17T10:10:45.644Z", "lastTradeTimestamp": null, "symbol": "LTC/USDT", "type": "market", "timeInForce": "IOC", "postOnly": null, "side": "sell", "price": 71.0566, "stopPrice": null, "triggerPrice": null, "amount": 0.1, "cost": 7.10566, "average": 71.0566, "filled": 0.1, "remaining": 0, "status": "closed", "fee": null, "trades": [], "fees": [], "lastUpdateTimestamp": null, "reduceOnly": null, "takeProfitPrice": null, "stopLossPrice": null}]}], "createOrder": [{"description": "spot create limit buy order", "method": "createOrder", "input": ["LTC/USDT", "limit", "buy", 0.1, 50], "httpResponse": {"message": "OK", "code": "1000", "trace": "c749335273a64c5b8698e4e2a662d185.76.17033755834924989", "data": {"order_id": "209626977087068257"}}, "parsedResponse": {"id": "209626977087068257", "clientOrderId": null, "info": {"order_id": "209626977087068257"}, "timestamp": null, "datetime": null, "lastTradeTimestamp": null, "symbol": "LTC/USDT", "type": "limit", "timeInForce": null, "postOnly": null, "side": "buy", "price": 50, "stopPrice": null, "triggerPrice": null, "amount": 0.1, "cost": null, "average": null, "filled": null, "remaining": null, "status": null, "fee": null, "trades": [], "fees": [], "lastUpdateTimestamp": null, "reduceOnly": null, "takeProfitPrice": null, "stopLossPrice": null}}], "cancelOrder": [{"description": "spot cancel order", "method": "cancelOrder", "input": ["209628171087991494", "LTC/USDT"], "httpResponse": {"message": "OK", "code": "1000", "trace": "fde85afe772c423684c1a0715bdb918d.72.17033755220918623", "data": {"result": true}}, "parsedResponse": {"id": "209628171087991494", "clientOrderId": null, "info": {}, "timestamp": null, "datetime": null, "lastTradeTimestamp": null, "symbol": "LTC/USDT", "type": null, "timeInForce": null, "postOnly": null, "side": null, "price": null, "stopPrice": null, "triggerPrice": null, "amount": null, "cost": null, "average": null, "filled": null, "remaining": null, "status": null, "fee": null, "trades": [], "fees": [], "lastUpdateTimestamp": null, "reduceOnly": null, "takeProfitPrice": null, "stopLossPrice": null}}], "fetchMyTrades": [{"description": "spot trades", "method": "fetchMyTrades", "input": ["LTC/USDT", null, 1], "httpResponse": {"code": "1000", "trace": "4c166d073d49475daabf72f27c992525.67.17067043428007529", "message": "success", "data": [{"tradeId": "226559426091255813", "orderId": "229121051960828591", "clientOrderId": "229121051960828591", "symbol": "LTC_USDT", "side": "buy", "orderMode": "spot", "type": "market", "price": "68.2782", "size": "0.087", "notional": "5.94020340", "fee": "0.00594020", "feeCoinName": "USDT", "tradeRole": "taker", "createTime": "1706704336944", "updateTime": "1706704336944"}]}, "parsedResponse": [{"info": {"tradeId": "226559426091255813", "orderId": "229121051960828591", "clientOrderId": "229121051960828591", "symbol": "LTC_USDT", "side": "buy", "orderMode": "spot", "type": "market", "price": "68.2782", "size": "0.087", "notional": "5.94020340", "fee": "0.00594020", "feeCoinName": "USDT", "tradeRole": "taker", "createTime": "1706704336944", "updateTime": "1706704336944"}, "id": "226559426091255813", "order": "229121051960828591", "timestamp": 1706704336944, "datetime": "2024-01-31T12:32:16.944Z", "symbol": "LTC/USDT", "type": "market", "side": "buy", "price": 68.2782, "amount": 0.087, "cost": 5.9402034, "takerOrMaker": "taker", "fee": {"cost": 0.0059402, "currency": "USDT"}, "fees": [{"cost": 0.0059402, "currency": "USDT"}]}]}], "fetchTrades": [{"description": "spot trades", "method": "fetchTrades", "input": ["LTC/USDT", null, 1], "httpResponse": {"code": "1000", "trace": "8c82ad6ce8b74f30a7a14995d36ba5d9.100.17172382683017021", "message": "success", "data": [["LTC_USDT", "1717238257307", "83.4299", "0.218", "sell"]]}, "parsedResponse": [{"info": ["LTC_USDT", "1717238257307", "83.4299", "0.218", "sell"], "id": null, "order": null, "timestamp": 1717238257307, "datetime": "2024-06-01T10:37:37.307Z", "symbol": "LTC/USDT", "type": null, "side": "sell", "price": 83.4299, "amount": 0.218, "cost": 18.1877182, "takerOrMaker": null, "fee": {"cost": null, "currency": null}, "fees": []}]}], "fetchTicker": [{"description": "spot ticker", "method": "fetchTicker", "input": ["BTC/USDT"], "httpResponse": {"code": "1000", "trace": "328fc9dcd3c84888bf573d4038eee4be.108.17172400530034865", "message": "success", "data": {"symbol": "BTC_USDT", "last": "71257.99", "v_24h": "8222.21532", "qv_24h": "583939083.43", "open_24h": "70892.25", "high_24h": "71660.00", "low_24h": "70119.89", "fluctuation": "0.00516", "bid_px": "71257.99", "bid_sz": "0.00531", "ask_px": "71258", "ask_sz": "0.00398", "ts": "1717737574061"}}, "parsedResponse": {"symbol": "BTC/USDT", "timestamp": 1717737574061, "datetime": "2024-06-07T05:19:34.061Z", "high": 71660, "low": 70119.89, "bid": 71257.99, "bidVolume": 0.00531, "ask": 71258, "askVolume": 0.00398, "vwap": 71019.6778731404, "open": 70892.25, "close": 71257.99, "last": 71257.99, "previousClose": null, "change": 365.74, "percentage": 0.516, "average": 71075.12, "baseVolume": 8222.21532, "quoteVolume": 583939083.43, "markPrice": null, "indexPrice": null, "info": {"symbol": "BTC_USDT", "last": "71257.99", "v_24h": "8222.21532", "qv_24h": "583939083.43", "open_24h": "70892.25", "high_24h": "71660.00", "low_24h": "70119.89", "fluctuation": "0.00516", "bid_px": "71257.99", "bid_sz": "0.00531", "ask_px": "71258", "ask_sz": "0.00398", "ts": "1717737574061"}}}], "fetchOHLCV": [{"description": "public spot ohlcv", "method": "fetchOHLCV", "input": ["BTC/USDT", "1h", null, 1], "httpResponse": {"code": "1000", "trace": "fa253db6cdc54bdcbd831389d2ca682a.107.17103284225947337", "message": "success", "data": [["1710327600", "73259.99", "73425.4", "73238.69", "73425.4", "94.52076", "6931725.77"]]}, "parsedResponse": [[1710327600000, 73259.99, 73425.4, 73238.69, 73425.4, 94.52076]]}], "fetchBalance": [{"description": "spot balance", "method": "fetchBalance", "input": [], "httpResponse": {"message": "OK", "code": "1000", "trace": "d536b57d32174fa688417c03c10826f2.116.17230279959966225", "data": {"wallet": [{"id": "TRX", "name": "TRON", "available": "0.00016000", "frozen": "0.00000000", "total": "0.00016000"}, {"id": "ADA", "name": "Cardano", "available": "0.71091660", "frozen": "0.00000000", "total": "0.71091660"}, {"id": "USDT", "name": "Tether USD", "available": "19.62994956", "frozen": "0.00000000", "total": "19.62994956"}, {"id": "LTC", "name": "LiteCoin", "available": "0.55900000", "frozen": "0.00000000", "total": "0.55900000"}, {"id": "BTC", "name": "Bitcoin", "available": "0.00039000", "frozen": "0.00000000", "total": "0.00039000"}]}}, "parsedResponse": {"info": {"message": "OK", "code": "1000", "trace": "d536b57d32174fa688417c03c10826f2.116.17230279959966225", "data": {"wallet": [{"id": "TRX", "name": "TRON", "available": "0.00016000", "frozen": "0.00000000", "total": "0.00016000"}, {"id": "ADA", "name": "Cardano", "available": "0.71091660", "frozen": "0.00000000", "total": "0.71091660"}, {"id": "USDT", "name": "Tether USD", "available": "19.62994956", "frozen": "0.00000000", "total": "19.62994956"}, {"id": "LTC", "name": "LiteCoin", "available": "0.55900000", "frozen": "0.00000000", "total": "0.55900000"}, {"id": "BTC", "name": "Bitcoin", "available": "0.00039000", "frozen": "0.00000000", "total": "0.00039000"}]}}, "TRX": {"free": 0.00016, "used": 0, "total": 0.00016}, "ADA": {"free": 0.7109166, "used": 0, "total": 0.7109166}, "USDT": {"free": 19.62994956, "used": 0, "total": 19.62994956}, "LTC": {"free": 0.559, "used": 0, "total": 0.559}, "BTC": {"free": 0.00039, "used": 0, "total": 0.00039}, "free": {"TRX": 0.00016, "ADA": 0.7109166, "USDT": 19.62994956, "LTC": 0.559, "BTC": 0.00039}, "used": {"TRX": 0, "ADA": 0, "USDT": 0, "LTC": 0, "BTC": 0}, "total": {"TRX": 0.00016, "ADA": 0.7109166, "USDT": 19.62994956, "LTC": 0.559, "BTC": 0.00039}}}, {"description": "swap balance", "method": "fetchBalance", "input": [{"type": "swap"}], "httpResponse": {"code": "1000", "message": "Ok", "data": [{"currency": "USDT", "available_balance": "5", "frozen_balance": "0", "unrealized": "0", "equity": "5", "position_deposit": "0"}, {"currency": "BTC", "available_balance": "0", "frozen_balance": "0", "unrealized": "0", "equity": "0", "position_deposit": "0"}, {"currency": "ETH", "available_balance": "0", "frozen_balance": "0", "unrealized": "0", "equity": "0", "position_deposit": "0"}], "trace": "d536b57d32174fa688417c03c10826f2.115.17230279047760209"}, "parsedResponse": {"info": {"code": "1000", "message": "Ok", "data": [{"currency": "USDT", "available_balance": "5", "frozen_balance": "0", "unrealized": "0", "equity": "5", "position_deposit": "0"}, {"currency": "BTC", "available_balance": "0", "frozen_balance": "0", "unrealized": "0", "equity": "0", "position_deposit": "0"}, {"currency": "ETH", "available_balance": "0", "frozen_balance": "0", "unrealized": "0", "equity": "0", "position_deposit": "0"}], "trace": "d536b57d32174fa688417c03c10826f2.115.17230279047760209"}, "USDT": {"free": 5, "used": 0, "total": 5}, "BTC": {"free": 0, "used": 0, "total": 0}, "ETH": {"free": 0, "used": 0, "total": 0}, "free": {"USDT": 5, "BTC": 0, "ETH": 0}, "used": {"USDT": 0, "BTC": 0, "ETH": 0}, "total": {"USDT": 5, "BTC": 0, "ETH": 0}}}], "fetchOrder": [{"description": "fetchOrder", "method": "fetchOrder", "input": ["3000013086011694", "LTC/USDT:USDT"], "httpResponse": {"code": "1000", "message": "Ok", "data": {"symbol": "LTCUSDT", "order_id": "3000013086011694", "client_order_id": "", "side": "1", "type": "market", "leverage": "1", "open_type": "cross", "deal_avg_price": "58.45", "deal_size": "1", "price": "58.48", "size": "1", "state": "4", "activation_price": "", "callback_rate": "", "activation_price_type": "0", "executive_order_id": "", "preset_take_profit_price_type": "0", "preset_stop_loss_price_type": "0", "preset_take_profit_price": "", "preset_stop_loss_price": "", "create_time": "1723028432693", "update_time": "1723028432793"}, "trace": "d536b57d32174fa688417c03c10826f2.97.17230286752111635"}, "parsedResponse": {"id": "3000013086011694", "clientOrderId": null, "info": {"symbol": "LTCUSDT", "order_id": "3000013086011694", "client_order_id": "", "side": "1", "type": "market", "leverage": "1", "open_type": "cross", "deal_avg_price": "58.45", "deal_size": "1", "price": "58.48", "size": "1", "state": "4", "activation_price": "", "callback_rate": "", "activation_price_type": "0", "executive_order_id": "", "preset_take_profit_price_type": "0", "preset_stop_loss_price_type": "0", "preset_take_profit_price": "", "preset_stop_loss_price": "", "create_time": "1723028432693", "update_time": "1723028432793"}, "timestamp": 1723028432693, "datetime": "2024-08-07T11:00:32.693Z", "lastTradeTimestamp": 1723028432793, "symbol": "LTC/USDT:USDT", "type": "market", "timeInForce": "IOC", "postOnly": null, "side": "buy", "price": 58.48, "stopPrice": null, "triggerPrice": null, "amount": 1, "cost": 0.05845, "average": 58.45, "filled": 1, "remaining": 0, "status": "closed", "fee": null, "trades": [], "fees": [], "lastUpdateTimestamp": null, "reduceOnly": null, "takeProfitPrice": null, "stopLossPrice": null}}], "fetchPosition": [{"description": "fetchPosition", "method": "fetchPosition", "input": ["LTC/USDT:USDT"], "httpResponse": {"code": "1000", "message": "Ok", "data": [{"symbol": "LTCUSDT", "leverage": "1", "timestamp": "1723028824417", "current_fee": "0", "open_timestamp": "1723028586053", "current_value": "0.05825", "mark_price": "0.05824406044", "position_value": "0.05827", "position_cross": "0.058339924", "maintenance_margin": "0.00145675", "close_vol": "0", "close_avg_price": "0", "open_avg_price": "58.27", "entry_price": "58.27", "current_amount": "1", "unrealized_value": "0.00002", "realized_value": "-0.000034962", "position_type": "2"}, {"symbol": "LTCUSDT", "leverage": "1", "timestamp": "1723028824417", "current_fee": "0", "open_timestamp": "1723028432792", "current_value": "0.05825", "mark_price": "0.05824406044", "position_value": "0.05845", "position_cross": "0.05848507", "maintenance_margin": "0.00146125", "close_vol": "0", "close_avg_price": "0", "open_avg_price": "58.45", "entry_price": "58.45", "current_amount": "1", "unrealized_value": "-0.0002", "realized_value": "-0.00003507", "position_type": "1"}], "trace": "d536b57d32174fa688417c03c10826f2.97.17230288243562267"}, "parsedResponse": {"info": {"symbol": "LTCUSDT", "leverage": "1", "timestamp": "1723028824417", "current_fee": "0", "open_timestamp": "1723028586053", "current_value": "0.05825", "mark_price": "0.05824406044", "position_value": "0.05827", "position_cross": "0.058339924", "maintenance_margin": "0.00145675", "close_vol": "0", "close_avg_price": "0", "open_avg_price": "58.27", "entry_price": "58.27", "current_amount": "1", "unrealized_value": "0.00002", "realized_value": "-0.000034962", "position_type": "2"}, "id": null, "symbol": "LTC/USDT:USDT", "timestamp": 1723028824417, "datetime": "2024-08-07T11:07:04.417Z", "lastUpdateTimestamp": null, "hedged": null, "side": "short", "contracts": 1, "contractSize": 0.001, "entryPrice": 58.27, "markPrice": 0.05824406044, "lastPrice": null, "notional": 0.05825, "leverage": 1, "collateral": 0.058339924, "initialMargin": null, "initialMarginPercentage": null, "maintenanceMargin": 0.00145675, "maintenanceMarginPercentage": 0.025008583690987123, "unrealizedPnl": 2e-05, "realizedPnl": -3.4962e-05, "liquidationPrice": null, "marginMode": null, "percentage": null, "marginRatio": 0.024970035956851776, "stopLossPrice": null, "takeProfitPrice": null}}], "fetchBorrowInterest": [{"description": "isolated borrow interest", "method": "fetchBorrowInterest", "input": ["USDT", "ETH/USDT", null, 1], "httpResponse": {"message": "OK", "code": "1000", "trace": "d56d6fe2db344bbeb979604275343640.85.17314814975034218", "data": {"records": [{"borrow_id": "17314813190079LvqkGNtHZ0QSBPT", "symbol": "ETH_USDT", "currency": "USDT", "borrow_amount": "10.20000000", "daily_interest": "0.00055000", "hourly_interest": "0.00002291", "interest_amount": null, "create_time": "1731481319634"}]}}, "parsedResponse": [{"info": {"borrow_id": "17314813190079LvqkGNtHZ0QSBPT", "symbol": "ETH_USDT", "currency": "USDT", "borrow_amount": "10.20000000", "daily_interest": "0.00055000", "hourly_interest": "0.00002291", "interest_amount": null, "create_time": "1731481319634"}, "symbol": "ETH/USDT", "currency": "USDT", "interest": null, "interestRate": 2.291e-05, "amountBorrowed": 10.2, "marginMode": "isolated", "timestamp": 1731481319634, "datetime": "2024-11-13T07:01:59.634Z"}]}], "fetchFundingRateHistory": [{"description": "fetch funding rate history with symbol and limit arguments", "method": "fetchFundingRateHistory", "input": ["BTC/USDT:USDT", null, 1], "httpResponse": {"code": "1000", "message": "Ok", "data": {"list": [{"symbol": "BTCUSDT", "funding_rate": "0.000091412174", "funding_time": "1734336000000"}]}, "trace": "********************************.74.17343482807603181"}, "parsedResponse": [{"info": {"symbol": "BTCUSDT", "funding_rate": "0.000091412174", "funding_time": "1734336000000"}, "symbol": "BTC/USDT:USDT", "fundingRate": 9.1412174e-05, "timestamp": 1734336000000, "datetime": "2024-12-16T08:00:00.000Z"}]}], "fetchLedger": [{"description": "fetch ledger with code and limit arguments", "method": "fetchLedger", "input": ["USDT", null, 1], "httpResponse": {"code": "1000", "message": "Ok", "data": [{"time": "*************", "type": "Funding Fee", "amount": "-0.********", "asset": "USDT", "symbol": "LTCUSDT", "tran_id": "*************", "flow_type": "3"}], "trace": "4cd11f83c71e4ed3b9b844790f07241e.64.*****************"}, "parsedResponse": [{"id": "*************", "timestamp": *************, "datetime": "2024-12-17T08:00:02.121Z", "direction": "out", "account": null, "referenceId": null, "referenceAccount": null, "type": "fee", "currency": "USDT", "amount": 8.253e-05, "before": null, "after": null, "status": null, "fee": null, "info": {"time": "*************", "type": "Funding Fee", "amount": "-0.********", "asset": "USDT", "symbol": "LTCUSDT", "tran_id": "*************", "flow_type": "3"}}]}], "fetchFundingHistory": [{"description": "fetch funding history with symbol and limit arguments", "method": "fetchFundingHistory", "input": ["LTC/USDT:USDT", null, 1], "httpResponse": {"code": "1000", "message": "Ok", "data": [{"time": "*************", "type": "Funding Fee", "amount": "-0.********", "asset": "USDT", "symbol": "LTCUSDT", "tran_id": "*************", "flow_type": "3"}], "trace": "d73d949bbd8645f6a40c8fc7f5ae6738.71.*****************"}, "parsedResponse": [{"info": {"time": "*************", "type": "Funding Fee", "amount": "-0.********", "asset": "USDT", "symbol": "LTCUSDT", "tran_id": "*************", "flow_type": "3"}, "symbol": "LTC/USDT:USDT", "code": "USDT", "timestamp": *************, "datetime": "2024-12-17T08:00:02.121Z", "id": "*************", "amount": -8.253e-05}]}]}}