{"exchange": "oxfun", "options": {}, "methods": {"fetchCurrencies": [{"description": "fetchCurrencies", "method": "fetchCurrencies", "input": [], "httpResponse": {"success": true, "data": [{"asset": "USDT", "isCollateral": true, "loanToValue": "0.950000000", "loanToValueFactor": "0.000000000", "networkList": [{"network": "Ethereum", "tokenId": "******************************************", "transactionPrecision": "6", "isWithdrawalFeeChargedToUser": true, "canDeposit": true, "canWithdraw": true, "minDeposit": "0.0001", "minWithdrawal": "0.01"}]}, {"asset": "USDT.BSC", "isCollateral": true, "loanToValue": "0.950000000", "loanToValueFactor": "0.000000000", "networkList": [{"network": "BNBSmartChain", "tokenId": "******************************************", "transactionPrecision": "18", "isWithdrawalFeeChargedToUser": true, "canDeposit": true, "canWithdraw": true, "minDeposit": "0.0001", "minWithdrawal": "0.01"}]}, {"asset": "USDT.ARB", "isCollateral": true, "loanToValue": "0.950000000", "loanToValueFactor": "0.000000000", "networkList": [{"network": "Arbitrum", "tokenId": "******************************************", "transactionPrecision": "6", "isWithdrawalFeeChargedToUser": true, "canDeposit": true, "canWithdraw": true, "minDeposit": "0.0001", "minWithdrawal": "0.01"}]}, {"asset": "USDT.SPL", "isCollateral": true, "loanToValue": "0.950000000", "loanToValueFactor": "0.000000000", "networkList": [{"network": "Solana", "tokenId": "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB", "transactionPrecision": "6", "isWithdrawalFeeChargedToUser": true, "canDeposit": true, "canWithdraw": true, "minDeposit": "0.0001", "minWithdrawal": "0.01"}]}, {"asset": "USDT.TRC20", "isCollateral": true, "loanToValue": "0.950000000", "loanToValueFactor": "0.000000000", "networkList": [{"network": "Tron", "tokenId": "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t", "transactionPrecision": "6", "isWithdrawalFeeChargedToUser": true, "canDeposit": true, "canWithdraw": true, "minDeposit": "0.0001", "minWithdrawal": "0.01"}]}, {"asset": "USDT.OPT", "isCollateral": true, "loanToValue": "0.950000000", "loanToValueFactor": "0.000000000", "networkList": [{"network": "Optimism", "tokenId": "0x94b008aA00579c1307B0EF2c499aD98a8ce58e58", "transactionPrecision": "6", "isWithdrawalFeeChargedToUser": true, "canDeposit": true, "canWithdraw": true, "minDeposit": "0.0001", "minWithdrawal": "0.01"}]}, {"asset": "USDT.AVAX", "isCollateral": true, "loanToValue": "0.950000000", "loanToValueFactor": "0.000000000", "networkList": [{"network": "Avalanche", "tokenId": "0x9702230A8Ea53601f5cD2dc00fDBc13d4dF4A8c7", "transactionPrecision": "6", "isWithdrawalFeeChargedToUser": true, "canDeposit": true, "canWithdraw": true, "minDeposit": "0.0001", "minWithdrawal": "0.0001"}]}, {"asset": "USDT.BASE", "isCollateral": true, "loanToValue": "0.950000000", "loanToValueFactor": "0.000000000", "networkList": [{"network": "Base", "tokenId": "******************************************", "transactionPrecision": "6", "isWithdrawalFeeChargedToUser": true, "canDeposit": true, "canWithdraw": true, "minDeposit": "0.0001", "minWithdrawal": "0.0001"}]}]}, "parsedResponse": {"USDT": {"info": [{"asset": "USDT", "isCollateral": true, "loanToValue": "0.950000000", "loanToValueFactor": "0.000000000", "networkList": [{"network": "Ethereum", "tokenId": "******************************************", "transactionPrecision": "6", "isWithdrawalFeeChargedToUser": true, "canDeposit": true, "canWithdraw": true, "minDeposit": "0.0001", "minWithdrawal": "0.01"}]}, {"asset": "USDT.BSC", "isCollateral": true, "loanToValue": "0.950000000", "loanToValueFactor": "0.000000000", "networkList": [{"network": "BNBSmartChain", "tokenId": "******************************************", "transactionPrecision": "18", "isWithdrawalFeeChargedToUser": true, "canDeposit": true, "canWithdraw": true, "minDeposit": "0.0001", "minWithdrawal": "0.01"}]}, {"asset": "USDT.ARB", "isCollateral": true, "loanToValue": "0.950000000", "loanToValueFactor": "0.000000000", "networkList": [{"network": "Arbitrum", "tokenId": "******************************************", "transactionPrecision": "6", "isWithdrawalFeeChargedToUser": true, "canDeposit": true, "canWithdraw": true, "minDeposit": "0.0001", "minWithdrawal": "0.01"}]}, {"asset": "USDT.SPL", "isCollateral": true, "loanToValue": "0.950000000", "loanToValueFactor": "0.000000000", "networkList": [{"network": "Solana", "tokenId": "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB", "transactionPrecision": "6", "isWithdrawalFeeChargedToUser": true, "canDeposit": true, "canWithdraw": true, "minDeposit": "0.0001", "minWithdrawal": "0.01"}]}, {"asset": "USDT.TRC20", "isCollateral": true, "loanToValue": "0.950000000", "loanToValueFactor": "0.000000000", "networkList": [{"network": "Tron", "tokenId": "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t", "transactionPrecision": "6", "isWithdrawalFeeChargedToUser": true, "canDeposit": true, "canWithdraw": true, "minDeposit": "0.0001", "minWithdrawal": "0.01"}]}, {"asset": "USDT.OPT", "isCollateral": true, "loanToValue": "0.950000000", "loanToValueFactor": "0.000000000", "networkList": [{"network": "Optimism", "tokenId": "0x94b008aA00579c1307B0EF2c499aD98a8ce58e58", "transactionPrecision": "6", "isWithdrawalFeeChargedToUser": true, "canDeposit": true, "canWithdraw": true, "minDeposit": "0.0001", "minWithdrawal": "0.01"}]}, {"asset": "USDT.AVAX", "isCollateral": true, "loanToValue": "0.950000000", "loanToValueFactor": "0.000000000", "networkList": [{"network": "Avalanche", "tokenId": "0x9702230A8Ea53601f5cD2dc00fDBc13d4dF4A8c7", "transactionPrecision": "6", "isWithdrawalFeeChargedToUser": true, "canDeposit": true, "canWithdraw": true, "minDeposit": "0.0001", "minWithdrawal": "0.0001"}]}, {"asset": "USDT.BASE", "isCollateral": true, "loanToValue": "0.950000000", "loanToValueFactor": "0.000000000", "networkList": [{"network": "Base", "tokenId": "******************************************", "transactionPrecision": "6", "isWithdrawalFeeChargedToUser": true, "canDeposit": true, "canWithdraw": true, "minDeposit": "0.0001", "minWithdrawal": "0.0001"}]}], "id": "USDT", "numericId": null, "code": "USDT", "precision": 1e-06, "type": null, "name": null, "active": true, "deposit": true, "withdraw": true, "fee": null, "fees": {}, "networks": {"ERC20": {"id": "Ethereum", "network": "ERC20", "margin": null, "deposit": true, "withdraw": true, "active": true, "fee": null, "precision": 1e-06, "limits": {"deposit": {"min": 0.0001, "max": null}, "withdraw": {"min": 0.01, "max": null}}, "info": {"network": "Ethereum", "tokenId": "******************************************", "transactionPrecision": "6", "isWithdrawalFeeChargedToUser": true, "canDeposit": true, "canWithdraw": true, "minDeposit": "0.0001", "minWithdrawal": "0.01"}}, "BNB": {"id": "BNBSmartChain", "network": "BNB", "margin": null, "deposit": true, "withdraw": true, "active": true, "fee": null, "precision": 1e-18, "limits": {"deposit": {"min": 0.0001, "max": null}, "withdraw": {"min": 0.01, "max": null}}, "info": {"network": "BNBSmartChain", "tokenId": "******************************************", "transactionPrecision": "18", "isWithdrawalFeeChargedToUser": true, "canDeposit": true, "canWithdraw": true, "minDeposit": "0.0001", "minWithdrawal": "0.01"}}, "ARB": {"id": "Arbitrum", "network": "ARB", "margin": null, "deposit": true, "withdraw": true, "active": true, "fee": null, "precision": 1e-06, "limits": {"deposit": {"min": 0.0001, "max": null}, "withdraw": {"min": 0.01, "max": null}}, "info": {"network": "Arbitrum", "tokenId": "******************************************", "transactionPrecision": "6", "isWithdrawalFeeChargedToUser": true, "canDeposit": true, "canWithdraw": true, "minDeposit": "0.0001", "minWithdrawal": "0.01"}}, "SOL": {"id": "Solana", "network": "SOL", "margin": null, "deposit": true, "withdraw": true, "active": true, "fee": null, "precision": 1e-06, "limits": {"deposit": {"min": 0.0001, "max": null}, "withdraw": {"min": 0.01, "max": null}}, "info": {"network": "Solana", "tokenId": "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB", "transactionPrecision": "6", "isWithdrawalFeeChargedToUser": true, "canDeposit": true, "canWithdraw": true, "minDeposit": "0.0001", "minWithdrawal": "0.01"}}, "Tron": {"id": "Tron", "network": "Tron", "margin": null, "deposit": true, "withdraw": true, "active": true, "fee": null, "precision": 1e-06, "limits": {"deposit": {"min": 0.0001, "max": null}, "withdraw": {"min": 0.01, "max": null}}, "info": {"network": "Tron", "tokenId": "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t", "transactionPrecision": "6", "isWithdrawalFeeChargedToUser": true, "canDeposit": true, "canWithdraw": true, "minDeposit": "0.0001", "minWithdrawal": "0.01"}}, "OPTIMISM": {"id": "Optimism", "network": "OPTIMISM", "margin": null, "deposit": true, "withdraw": true, "active": true, "fee": null, "precision": 1e-06, "limits": {"deposit": {"min": 0.0001, "max": null}, "withdraw": {"min": 0.01, "max": null}}, "info": {"network": "Optimism", "tokenId": "0x94b008aA00579c1307B0EF2c499aD98a8ce58e58", "transactionPrecision": "6", "isWithdrawalFeeChargedToUser": true, "canDeposit": true, "canWithdraw": true, "minDeposit": "0.0001", "minWithdrawal": "0.01"}}, "AVAX": {"id": "Avalanche", "network": "AVAX", "margin": null, "deposit": true, "withdraw": true, "active": true, "fee": null, "precision": 1e-06, "limits": {"deposit": {"min": 0.0001, "max": null}, "withdraw": {"min": 0.0001, "max": null}}, "info": {"network": "Avalanche", "tokenId": "0x9702230A8Ea53601f5cD2dc00fDBc13d4dF4A8c7", "transactionPrecision": "6", "isWithdrawalFeeChargedToUser": true, "canDeposit": true, "canWithdraw": true, "minDeposit": "0.0001", "minWithdrawal": "0.0001"}}, "BASE": {"id": "Base", "network": "BASE", "margin": null, "deposit": true, "withdraw": true, "active": true, "fee": null, "precision": 1e-06, "limits": {"deposit": {"min": 0.0001, "max": null}, "withdraw": {"min": 0.0001, "max": null}}, "info": {"network": "Base", "tokenId": "******************************************", "transactionPrecision": "6", "isWithdrawalFeeChargedToUser": true, "canDeposit": true, "canWithdraw": true, "minDeposit": "0.0001", "minWithdrawal": "0.0001"}}}, "limits": {"withdraw": {"min": 0.0001, "max": null}, "deposit": {"min": 0.0001, "max": null}}}}}], "createOrder": [{"description": "spot limit buy", "method": "createOrder", "input": ["ETH/USDT", "limit", "buy", 0.01, 10], "httpResponse": {"success": true, "data": [{"notice": "OrderOpened", "accountId": "106464", "orderId": "*************", "submitted": true, "clientOrderId": "0", "marketCode": "ETH-USDT", "status": "OPEN", "side": "BUY", "price": "10.0", "isTriggered": false, "quantity": "0.01", "amount": "0.0", "orderType": "LIMIT", "timeInForce": "GTC", "createdAt": "*************", "source": "11", "displayQuantity": "0.01"}]}, "parsedResponse": {"id": "*************", "clientOrderId": "0", "timestamp": *************, "datetime": "2024-05-30T15:33:28.567Z", "lastTradeTimestamp": null, "lastUpdateTimestamp": null, "status": "open", "symbol": "ETH/USDT", "type": "limit", "timeInForce": "GTC", "side": "buy", "price": 10, "average": null, "amount": 0.01, "filled": null, "remaining": null, "triggerPrice": null, "stopLossPrice": null, "cost": null, "trades": [], "fee": null, "info": {"notice": "OrderOpened", "accountId": "106464", "orderId": "*************", "submitted": true, "clientOrderId": "0", "marketCode": "ETH-USDT", "status": "OPEN", "side": "BUY", "price": "10.0", "isTriggered": false, "quantity": "0.01", "amount": "0.0", "orderType": "LIMIT", "timeInForce": "GTC", "createdAt": "*************", "source": "11", "displayQuantity": "0.01"}, "fees": [], "postOnly": false, "reduceOnly": null, "stopPrice": null, "takeProfitPrice": null}}], "fetchOrderBook": [{"description": "Fill this with a description of the method call", "method": "fetchOrderBook", "input": ["ETH/USDT", 3], "httpResponse": {"success": true, "level": "3", "data": {"marketCode": "ETH-USDT", "lastUpdatedAt": "*************", "asks": [], "bids": [[99, 0.01], [10, 0.01]]}}, "parsedResponse": {"symbol": "ETH/USDT", "bids": [[99, 0.01], [10, 0.01]], "asks": [], "timestamp": *************, "datetime": "2024-05-30T15:57:16.971Z", "nonce": null}}], "fetchOrder": [{"description": "fetch spot order by id", "method": "fetchOrder", "input": [*************, "ETH/USDT"], "httpResponse": {"success": true, "data": {"orderId": "*************", "clientOrderId": "0", "marketCode": "ETH-USDT", "status": "OPEN", "side": "BUY", "price": "10.0", "isTriggered": false, "remainQuantity": "0.01", "totalQuantity": "0.01", "amount": "0", "displayQuantity": "0.01", "cumulativeMatchedQuantity": "0", "orderType": "LIMIT", "timeInForce": "GTC", "source": "11", "createdAt": "1717083208542"}}, "parsedResponse": {"id": "*************", "clientOrderId": "0", "timestamp": 1717083208542, "datetime": "2024-05-30T15:33:28.542Z", "lastTradeTimestamp": null, "lastUpdateTimestamp": null, "status": "open", "symbol": "ETH/USDT", "type": "limit", "timeInForce": "GTC", "side": "buy", "price": 10, "average": null, "amount": 0.01, "filled": 0, "remaining": 0.01, "triggerPrice": null, "stopLossPrice": null, "cost": 0, "trades": [], "fee": null, "info": {"orderId": "*************", "clientOrderId": "0", "marketCode": "ETH-USDT", "status": "OPEN", "side": "BUY", "price": "10.0", "isTriggered": false, "remainQuantity": "0.01", "totalQuantity": "0.01", "amount": "0", "displayQuantity": "0.01", "cumulativeMatchedQuantity": "0", "orderType": "LIMIT", "timeInForce": "GTC", "source": "11", "createdAt": "1717083208542"}, "fees": [], "postOnly": false, "reduceOnly": null, "stopPrice": null, "takeProfitPrice": null}}], "fetchMyTrades": [{"description": "Fill this with a description of the method call", "method": "fetchMyTrades", "input": ["ETH/USD:OX", 1716966000000], "httpResponse": {"success": true, "data": [{"orderId": "1000117638529", "clientOrderId": "1717024731867", "matchId": "400017157974541552", "marketCode": "ETH-USD-SWAP-LIN", "side": "SELL", "matchedQuantity": "0.01", "matchPrice": "3794.7", "total": "3794.7", "orderMatchType": "TAKER", "feeAsset": "OX", "fee": "2.65629", "source": "0", "matchedAt": "1717024733539"}]}, "parsedResponse": [{"id": "400017157974541552", "timestamp": 1717024733539, "datetime": "2024-05-29T23:18:53.539Z", "symbol": "ETH/USD:OX", "type": null, "order": "1000117638529", "side": "sell", "takerOrMaker": "taker", "price": 3794.7, "amount": 0.01, "cost": 37.947, "fee": {"cost": 2.65629, "currency": "OX"}, "info": {"orderId": "1000117638529", "clientOrderId": "1717024731867", "matchId": "400017157974541552", "marketCode": "ETH-USD-SWAP-LIN", "side": "SELL", "matchedQuantity": "0.01", "matchPrice": "3794.7", "total": "3794.7", "orderMatchType": "TAKER", "feeAsset": "OX", "fee": "2.65629", "source": "0", "matchedAt": "1717024733539"}, "fees": [{"cost": 2.65629, "currency": "OX"}]}]}, {"description": "Fetch my trades for swap with a limit and until arguments", "method": "fetchMyTrades", "input": ["ETH/USD:OX", 1716966000000, 2, {"until": 1717077000000}], "httpResponse": {"success": true, "data": [{"orderId": "1000117638529", "clientOrderId": "1717024731867", "matchId": "400017157974541552", "marketCode": "ETH-USD-SWAP-LIN", "side": "SELL", "matchedQuantity": "0.01", "matchPrice": "3794.7", "total": "3794.7", "orderMatchType": "TAKER", "feeAsset": "OX", "fee": "2.65629", "source": "0", "matchedAt": "1717024733539"}]}, "parsedResponse": [{"id": "400017157974541552", "timestamp": 1717024733539, "datetime": "2024-05-29T23:18:53.539Z", "symbol": "ETH/USD:OX", "type": null, "order": "1000117638529", "side": "sell", "takerOrMaker": "taker", "price": 3794.7, "amount": 0.01, "cost": 37.947, "fee": {"cost": 2.65629, "currency": "OX"}, "info": {"orderId": "1000117638529", "clientOrderId": "1717024731867", "matchId": "400017157974541552", "marketCode": "ETH-USD-SWAP-LIN", "side": "SELL", "matchedQuantity": "0.01", "matchPrice": "3794.7", "total": "3794.7", "orderMatchType": "TAKER", "feeAsset": "OX", "fee": "2.65629", "source": "0", "matchedAt": "1717024733539"}, "fees": [{"cost": 2.65629, "currency": "OX"}]}]}], "fetchTicker": [{"description": "Fetch ticker for ETH/USDT", "method": "fetchTicker", "input": ["ETH/USDT"], "httpResponse": {"success": true, "data": [{"marketCode": "ETH-USDT", "markPrice": "3775.4", "open24h": "3764.4", "high24h": "3801.6", "low24h": "3703.1", "volume24h": "0", "currencyVolume24h": "0", "openInterest": "0", "lastTradedPrice": "0", "lastTradedQuantity": "0", "lastUpdatedAt": "1717085734197"}]}, "parsedResponse": {"symbol": "ETH/USDT", "timestamp": 1717085734197, "datetime": "2024-05-30T16:15:34.197Z", "high": 3801.6, "low": 3703.1, "bid": null, "bidVolume": null, "ask": null, "askVolume": null, "vwap": null, "open": 3764.4, "close": null, "last": null, "previousClose": null, "change": null, "percentage": null, "average": null, "baseVolume": 0, "quoteVolume": null, "markPrice": 3775.4, "indexPrice": null, "info": {"marketCode": "ETH-USDT", "markPrice": "3775.4", "open24h": "3764.4", "high24h": "3801.6", "low24h": "3703.1", "volume24h": "0", "currencyVolume24h": "0", "openInterest": "0", "lastTradedPrice": "0", "lastTradedQuantity": "0", "lastUpdatedAt": "1717085734197"}}}], "fetchOHLCV": [{"description": "Fetch OHLCV for ETH/USDT", "method": "fetchOHLCV", "input": ["ETH/USDT", "1m", 1717085760000], "httpResponse": {"success": true, "timeframe": "60s", "data": [{"open": "3776.80000000", "high": "3777.90000000", "low": "3775.90000000", "close": "3776.80000000", "volume": "0", "currencyVolume": "0", "openedAt": "1717085880000"}, {"open": "3775.00000000", "high": "3777.30000000", "low": "3775.00000000", "close": "3776.80000000", "volume": "0", "currencyVolume": "0", "openedAt": "1717085820000"}, {"open": "3775.00000000", "high": "3775.90000000", "low": "3773.70000000", "close": "3775.00000000", "volume": "0", "currencyVolume": "0", "openedAt": "1717085760000"}]}, "parsedResponse": [[1717085760000, 3775, 3775.9, 3773.7, 3775, 0], [1717085820000, 3775, 3777.3, 3775, 3776.8, 0], [1717085880000, 3776.8, 3777.9, 3775.9, 3776.8, 0]]}, {"description": "Fetch candles with a limit and until arguments", "method": "fetchOHLCV", "input": ["ETH/USDT", "5m", 1717085760000, 3, {"until": 1717385870000}], "httpResponse": {"success": true, "timeframe": "300s", "data": [{"open": "3799.90000000", "high": "3802.90000000", "low": "3797.60000000", "close": "3798.50000000", "volume": "0", "currencyVolume": "0", "openedAt": "1717385700000"}, {"open": "3795.50000000", "high": "3799.90000000", "low": "3795.50000000", "close": "3799.90000000", "volume": "0", "currencyVolume": "0", "openedAt": "1717385400000"}, {"open": "3795.40000000", "high": "3797.20000000", "low": "3795.00000000", "close": "3795.50000000", "volume": "0", "currencyVolume": "0", "openedAt": "1717385100000"}]}, "parsedResponse": [[1717385100000, 3795.4, 3797.2, 3795, 3795.5, 0], [1717385400000, 3795.5, 3799.9, 3795.5, 3799.9, 0], [1717385700000, 3799.9, 3802.9, 3797.6, 3798.5, 0]]}], "fetchFundingRateHistory": [{"description": "Fetch funding rate history with a limit and until arguments", "method": "fetchFundingRateHistory", "input": ["ETH/USD:OX", 1717581601006, 3, {"until": 1717681601006}], "httpResponse": {"success": true, "data": [{"marketCode": "ETH-USD-SWAP-LIN", "fundingRate": "0.*********", "createdAt": "*************"}, {"marketCode": "ETH-USD-SWAP-LIN", "fundingRate": "0.000126000", "createdAt": "*************"}, {"marketCode": "ETH-USD-SWAP-LIN", "fundingRate": "0.000127000", "createdAt": "1717671600805"}]}, "parsedResponse": [{"info": {"marketCode": "ETH-USD-SWAP-LIN", "fundingRate": "0.000127000", "createdAt": "1717671600805"}, "symbol": "ETH/USD:OX", "fundingRate": 0.000127, "timestamp": 1717671600805, "datetime": "2024-06-06T11:00:00.805Z"}, {"info": {"marketCode": "ETH-USD-SWAP-LIN", "fundingRate": "0.000126000", "createdAt": "*************"}, "symbol": "ETH/USD:OX", "fundingRate": 0.000126, "timestamp": *************, "datetime": "2024-06-06T12:00:00.954Z"}, {"info": {"marketCode": "ETH-USD-SWAP-LIN", "fundingRate": "0.*********", "createdAt": "*************"}, "symbol": "ETH/USD:OX", "fundingRate": 0.000124, "timestamp": *************, "datetime": "2024-06-06T13:00:01.549Z"}]}], "fetchBalance": [{"description": "Fetch balance for USDT", "method": "fetchBalance", "input": [{"asset": "USDT"}], "httpResponse": {"success": true, "data": [{"accountId": "106464", "name": "main", "balances": [{"asset": "USDT", "total": "11.338980", "available": "11.338980", "reserved": "0", "lastUpdatedAt": "*************"}]}]}, "parsedResponse": {"info": {"accountId": "106464", "name": "main", "balances": [{"asset": "USDT", "total": "11.338980", "available": "11.338980", "reserved": "0", "lastUpdatedAt": "*************"}]}, "USDT": {"free": 11.33898, "used": 0, "total": 11.33898}, "free": {"USDT": 11.33898}, "used": {"USDT": 0}, "total": {"USDT": 11.33898}}}], "fetchTransfers": [{"description": "Fetch transfers with a limit and until arguments", "method": "fetchTransfers", "input": ["OX", *************, 1, {"until": *************}], "httpResponse": {"success": true, "data": [{"asset": "OX", "quantity": "10", "fromAccount": "106464", "toAccount": "106570", "id": "974895240428093443", "status": "COMPLETED", "transferredAt": "*************"}]}, "parsedResponse": [{"id": "974895240428093443", "timestamp": *************, "datetime": "2024-06-05T10:53:36.624Z", "currency": "OX", "amount": 10, "fromAccount": "106464", "toAccount": "106570", "status": "ok", "info": {"asset": "OX", "quantity": "10", "fromAccount": "106464", "toAccount": "106570", "id": "974895240428093443", "status": "COMPLETED", "transferredAt": "*************"}}]}], "fetchDepositAddress": [{"description": "Fetch deposit address for a specific network", "method": "fetchDepositAddress", "input": ["ETH", {"network": "ERC20"}], "httpResponse": {"success": true, "data": {"address": "******************************************"}}, "parsedResponse": {"currency": "ETH", "address": "******************************************", "tag": null, "network": null, "info": {"address": "******************************************"}}}], "fetchDeposits": [{"description": "Fill this with a description of the method call", "method": "fetchDeposits", "input": ["USDC", *************, 1, {"until": *************}], "httpResponse": {"success": true, "data": [{"asset": "USDC", "network": "Ethereum", "address": "******************************************", "quantity": "50", "id": "5914", "status": "COMPLETED", "txId": "0xf5e79663830a0c6f94d46638dcfbc134566c12facf1832396f81ecb55d3c75dc", "creditedAt": "1714821645154", "type": "deposit"}]}, "parsedResponse": [{"info": {"asset": "USDC", "network": "Ethereum", "address": "******************************************", "quantity": "50", "id": "5914", "status": "COMPLETED", "txId": "0xf5e79663830a0c6f94d46638dcfbc134566c12facf1832396f81ecb55d3c75dc", "creditedAt": "1714821645154"}, "id": "5914", "txid": "0xf5e79663830a0c6f94d46638dcfbc134566c12facf1832396f81ecb55d3c75dc", "timestamp": 1714821645154, "datetime": "2024-05-04T11:20:45.154Z", "network": "ERC20", "address": "******************************************", "addressTo": null, "addressFrom": null, "tag": null, "tagTo": null, "tagFrom": null, "type": "deposit", "amount": 50, "currency": "USDC", "status": "ok", "updated": null, "internal": null, "comment": null, "fee": null}]}]}}