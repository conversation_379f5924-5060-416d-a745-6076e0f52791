{"exchange": "xt", "skipKeys": [], "methods": {"fetchOpenOrders": [{"description": "swap open orders no symbol", "method": "fetchOpenOrders", "input": [null, null, null, {"type": "swap"}], "httpResponse": {"returnCode": "0", "msgInfo": "success", "error": null, "result": {"page": null, "ps": null, "total": "1", "items": [{"orderId": "497979918306034304", "clientOrderId": null, "symbol": "ltc_usdt", "contractSize": "0.100000000000000000", "orderType": "LIMIT", "orderSide": "BUY", "positionSide": "LONG", "positionType": "CROSSED", "timeInForce": "GTC", "closePosition": false, "price": "50", "origQty": "1", "avgPrice": "0", "executedQty": "0", "marginFrozen": "0.********", "remark": null, "sourceId": null, "sourceType": "DEFAULT", "forceClose": false, "leverage": "35", "openPrice": null, "closeProfit": null, "state": "NEW", "createdTime": "*************", "updatedTime": "*************", "welfareAccount": false, "triggerPriceType": null, "triggerProfitPrice": null, "profitDelegateOrderType": null, "profitDelegateTimeInForce": null, "profitDelegatePrice": null, "triggerStopPrice": null, "stopDelegateOrderType": null, "stopDelegateTimeInForce": null, "stopDelegatePrice": null, "markPrice": "96.12", "profit": false}]}}, "parsedResponse": [{"info": {"orderId": "497979918306034304", "clientOrderId": null, "symbol": "ltc_usdt", "contractSize": "0.100000000000000000", "orderType": "LIMIT", "orderSide": "BUY", "positionSide": "LONG", "positionType": "CROSSED", "timeInForce": "GTC", "closePosition": false, "price": "50", "origQty": "1", "avgPrice": "0", "executedQty": "0", "marginFrozen": "0.********", "remark": null, "sourceId": null, "sourceType": "DEFAULT", "forceClose": false, "leverage": "35", "openPrice": null, "closeProfit": null, "state": "NEW", "createdTime": "*************", "updatedTime": "*************", "welfareAccount": false, "triggerPriceType": null, "triggerProfitPrice": null, "profitDelegateOrderType": null, "profitDelegateTimeInForce": null, "profitDelegatePrice": null, "triggerStopPrice": null, "stopDelegateOrderType": null, "stopDelegateTimeInForce": null, "stopDelegatePrice": null, "markPrice": "96.12", "profit": false}, "id": "497979918306034304", "clientOrderId": null, "timestamp": *************, "datetime": "2025-05-28T10:54:24.543Z", "lastTradeTimestamp": *************, "lastUpdateTimestamp": *************, "symbol": "LTC/USDT:USDT", "type": "limit", "timeInForce": "GTC", "postOnly": false, "side": "buy", "price": 50, "triggerPrice": null, "stopLoss": null, "takeProfit": null, "amount": 0.1, "filled": 0, "remaining": 0.1, "cost": 0, "average": null, "status": "open", "fee": {"currency": null, "cost": null}, "trades": [], "fees": [{"currency": null, "cost": null}], "reduceOnly": null, "stopPrice": null, "takeProfitPrice": null, "stopLossPrice": null}]}, {"description": "spot open orders", "method": "fetchOpenOrders", "input": [], "httpResponse": {"rc": "0", "mc": "SUCCESS", "ma": [], "result": [{"symbol": "ltc_usdt", "orderId": "497979342666793344", "clientOrderId": null, "baseCurrency": "ltc", "quoteCurrency": "usdt", "side": "BUY", "type": "LIMIT", "timeInForce": "GTC", "price": "50.00", "origQty": "0.100", "origQuoteQty": "5.00", "executedQty": "0.000", "leavingQty": "0.100", "tradeBase": "0.000", "tradeQuote": "0.00", "avgPrice": null, "fee": null, "feeCurrency": null, "nftId": null, "symbolType": "normal", "deductServices": [], "origRestFee": null, "origFeeCurrency": null, "platFormCurrencyFee": null, "platFormCurrency": null, "couponAmount": null, "couponCurrency": null, "couponDeductFee": null, "closed": false, "state": "NEW", "time": "1748429527300", "updatedTime": null, "ip": "************"}]}, "parsedResponse": [{"info": {"symbol": "ltc_usdt", "orderId": "497979342666793344", "clientOrderId": null, "baseCurrency": "ltc", "quoteCurrency": "usdt", "side": "BUY", "type": "LIMIT", "timeInForce": "GTC", "price": "50.00", "origQty": "0.100", "origQuoteQty": "5.00", "executedQty": "0.000", "leavingQty": "0.100", "tradeBase": "0.000", "tradeQuote": "0.00", "avgPrice": null, "fee": null, "feeCurrency": null, "nftId": null, "symbolType": "normal", "deductServices": [], "origRestFee": null, "origFeeCurrency": null, "platFormCurrencyFee": null, "platFormCurrency": null, "couponAmount": null, "couponCurrency": null, "couponDeductFee": null, "closed": false, "state": "NEW", "time": "1748429527300", "updatedTime": null, "ip": "************"}, "id": "497979342666793344", "clientOrderId": null, "timestamp": 1748429527300, "datetime": "2025-05-28T10:52:07.300Z", "lastTradeTimestamp": null, "lastUpdateTimestamp": null, "symbol": "LTC/USDT", "type": "limit", "timeInForce": "GTC", "postOnly": false, "side": "buy", "price": 50, "triggerPrice": null, "stopLoss": null, "takeProfit": null, "amount": 0.1, "filled": 0, "remaining": 0.1, "cost": 0, "average": null, "status": "open", "fee": {"currency": null, "cost": null}, "trades": [], "fees": [{"currency": null, "cost": null}], "reduceOnly": null, "stopPrice": null, "takeProfitPrice": null, "stopLossPrice": null}]}], "fetchMyTrades": [{"description": "spot trades", "method": "fetchMyTrades", "input": ["LTC/USDT", null, 1], "httpResponse": {"rc": "0", "mc": "SUCCESS", "ma": [], "result": {"hasPrev": false, "hasNext": true, "items": [{"symbol": "ltc_usdt", "tradeId": "371185260471294598", "orderId": "371185259911612992", "orderSide": "SELL", "orderType": "MARKET", "bizType": "SPOT", "time": "1718199462385", "price": "78.35", "quantity": "0.100", "quoteQty": "7.835", "baseCurrency": "ltc", "quoteCurrency": "usdt", "fee": "0.01567", "feeCurrency": "usdt", "nftId": null, "symbolType": "normal", "takerMaker": "TAKER"}]}}, "parsedResponse": [{"info": {"symbol": "ltc_usdt", "tradeId": "371185260471294598", "orderId": "371185259911612992", "orderSide": "SELL", "orderType": "MARKET", "bizType": "SPOT", "time": "1718199462385", "price": "78.35", "quantity": "0.100", "quoteQty": "7.835", "baseCurrency": "ltc", "quoteCurrency": "usdt", "fee": "0.01567", "feeCurrency": "usdt", "nftId": null, "symbolType": "normal", "takerMaker": "TAKER"}, "id": "371185260471294598", "timestamp": 1718199462385, "datetime": "2024-06-12T13:37:42.385Z", "symbol": "LTC/USDT", "order": "371185259911612992", "type": "market", "side": "sell", "takerOrMaker": "taker", "price": 78.35, "amount": 0.1, "cost": 7.835, "fee": {"currency": "USDT", "cost": 0.01567}, "fees": [{"currency": "USDT", "cost": 0.01567}]}]}, {"description": "swap trades", "method": "fetchMyTrades", "input": ["LTC/USDT:USDT", null, 1], "httpResponse": {"returnCode": "0", "msgInfo": "success", "error": null, "result": {"page": "1", "ps": "1", "total": "2", "items": [{"orderId": "371185640502963264", "execId": "371185641561523328", "symbol": "ltc_usdt", "quantity": "1", "price": "78.3", "fee": "0.004698", "feeCoin": "usdt", "timestamp": "*************", "takerMaker": "TAKER"}]}}, "parsedResponse": [{"info": {"orderId": "371185640502963264", "execId": "371185641561523328", "symbol": "ltc_usdt", "quantity": "1", "price": "78.3", "fee": "0.004698", "feeCoin": "usdt", "timestamp": "*************", "takerMaker": "TAKER"}, "id": "371185641561523328", "timestamp": *************, "datetime": "2024-06-12T13:39:13.244Z", "symbol": "LTC/USDT:USDT", "order": "371185640502963264", "type": null, "side": null, "takerOrMaker": "taker", "price": 78.3, "amount": 0.1, "cost": 0.783, "fee": {"currency": "USDT", "cost": 0.004698}, "fees": [{"currency": "USDT", "cost": 0.004698}]}]}], "fetchClosedOrders": [{"description": "spot closed orders", "method": "fetchClosedOrders", "input": ["LTC/USDT", null, 1], "httpResponse": {"rc": "0", "mc": "SUCCESS", "ma": [], "result": {"hasPrev": false, "hasNext": true, "items": [{"symbol": "ltc_usdt", "orderId": "371185259911612992", "clientOrderId": null, "baseCurrency": "ltc", "quoteCurrency": "usdt", "side": "SELL", "type": "MARKET", "timeInForce": "FOK", "price": null, "origQty": "0.100", "origQuoteQty": null, "executedQty": "0.100", "leavingQty": "0.000", "tradeBase": "0.100", "tradeQuote": "7.835", "avgPrice": "78.35", "fee": "0.01567", "feeCurrency": "usdt", "nftId": null, "symbolType": "normal", "closed": true, "state": "FILLED", "time": "1718199462251", "updatedTime": "1718199462385", "ip": "*************"}]}}, "parsedResponse": [{"info": {"symbol": "ltc_usdt", "orderId": "371185259911612992", "clientOrderId": null, "baseCurrency": "ltc", "quoteCurrency": "usdt", "side": "SELL", "type": "MARKET", "timeInForce": "FOK", "price": null, "origQty": "0.100", "origQuoteQty": null, "executedQty": "0.100", "leavingQty": "0.000", "tradeBase": "0.100", "tradeQuote": "7.835", "avgPrice": "78.35", "fee": "0.01567", "feeCurrency": "usdt", "nftId": null, "symbolType": "normal", "closed": true, "state": "FILLED", "time": "1718199462251", "updatedTime": "1718199462385", "ip": "*************"}, "id": "371185259911612992", "clientOrderId": null, "timestamp": 1718199462251, "datetime": "2024-06-12T13:37:42.251Z", "lastTradeTimestamp": 1718199462385, "lastUpdateTimestamp": 1718199462385, "symbol": "LTC/USDT", "type": "market", "timeInForce": "FOK", "postOnly": false, "side": "sell", "price": 78.35, "stopPrice": null, "stopLoss": null, "takeProfit": null, "amount": 0.1, "filled": 0.1, "remaining": 0, "cost": 7.835, "average": 78.35, "status": "closed", "fee": {"currency": "USDT", "cost": 0.01567}, "trades": [], "fees": [{"currency": "USDT", "cost": 0.01567}], "reduceOnly": null, "triggerPrice": null, "takeProfitPrice": null, "stopLossPrice": null}]}, {"description": "swap orders", "method": "fetchClosedOrders", "input": ["LTC/USDT:USDT", null, 1], "httpResponse": {"returnCode": "0", "msgInfo": "success", "error": null, "result": {"page": "1", "ps": "1", "total": "9", "items": [{"orderId": "371185640502963264", "clientOrderId": null, "symbol": "ltc_usdt", "orderType": "MARKET", "orderSide": "SELL", "positionSide": "SHORT", "timeInForce": "IOC", "closePosition": false, "price": "0", "origQty": "1", "avgPrice": "78.3", "executedQty": "1", "marginFrozen": "0.161298", "remark": null, "sourceId": null, "sourceType": "DEFAULT", "forceClose": false, "leverage": "50", "openPrice": null, "closeProfit": null, "state": "FILLED", "createdTime": "*************", "updatedTime": "*************", "welfareAccount": false, "triggerPriceType": null, "triggerProfitPrice": null, "profitDelegateOrderType": null, "profitDelegateTimeInForce": null, "profitDelegatePrice": null, "triggerStopPrice": null, "stopDelegateOrderType": null, "stopDelegateTimeInForce": null, "stopDelegatePrice": null, "profit": false}]}}, "parsedResponse": [{"info": {"orderId": "371185640502963264", "clientOrderId": null, "symbol": "ltc_usdt", "orderType": "MARKET", "orderSide": "SELL", "positionSide": "SHORT", "timeInForce": "IOC", "closePosition": false, "price": "0", "origQty": "1", "avgPrice": "78.3", "executedQty": "1", "marginFrozen": "0.161298", "remark": null, "sourceId": null, "sourceType": "DEFAULT", "forceClose": false, "leverage": "50", "openPrice": null, "closeProfit": null, "state": "FILLED", "createdTime": "*************", "updatedTime": "*************", "welfareAccount": false, "triggerPriceType": null, "triggerProfitPrice": null, "profitDelegateOrderType": null, "profitDelegateTimeInForce": null, "profitDelegatePrice": null, "triggerStopPrice": null, "stopDelegateOrderType": null, "stopDelegateTimeInForce": null, "stopDelegatePrice": null, "profit": false}, "id": "371185640502963264", "clientOrderId": null, "timestamp": *************, "datetime": "2024-06-12T13:39:12.991Z", "lastTradeTimestamp": *************, "lastUpdateTimestamp": *************, "symbol": "LTC/USDT:USDT", "type": "market", "timeInForce": "IOC", "postOnly": false, "side": "sell", "price": 78.3, "stopPrice": null, "stopLoss": null, "takeProfit": null, "amount": 0.1, "filled": 0.1, "remaining": 0, "cost": 0.783, "average": 78.3, "status": "closed", "fee": {"currency": null, "cost": null}, "trades": [], "fees": [{"currency": null, "cost": null}], "reduceOnly": null, "triggerPrice": null, "takeProfitPrice": null, "stopLossPrice": null}]}], "fetchTrades": [{"description": "spot & limit", "method": "fetchTrades", "input": ["BTC/USDT", null, 5], "httpResponse": {"rc": "0", "mc": "SUCCESS", "ma": [], "result": [{"i": "423029665070881152", "t": "1730560131664", "p": "69243.26", "q": "0.00552", "v": "382.2227952", "b": false}, {"i": "423029660033522048", "t": "1730560130463", "p": "69243.26", "q": "0.00057", "v": "39.4686582", "b": false}, {"i": "423029655998601600", "t": "1730560129501", "p": "69243.26", "q": "0.00549", "v": "380.1454974", "b": false}, {"i": "423029650105604480", "t": "1730560128096", "p": "69243.26", "q": "0.00501", "v": "346.9087326", "b": false}, {"i": "423029643818342784", "t": "1730560126597", "p": "69243.25", "q": "0.10917", "v": "7559.2856025", "b": true}]}, "parsedResponse": [{"info": {"i": "423029643818342784", "t": "1730560126597", "p": "69243.25", "q": "0.10917", "v": "7559.2856025", "b": true}, "id": "423029643818342784", "timestamp": 1730560126597, "datetime": "2024-11-02T15:08:46.597Z", "symbol": "BTC/USDT", "order": null, "type": null, "side": "sell", "takerOrMaker": "taker", "price": 69243.25, "amount": 0.10917, "cost": 7559.2856025, "fee": {"cost": null, "currency": null}, "fees": []}, {"info": {"i": "423029650105604480", "t": "1730560128096", "p": "69243.26", "q": "0.00501", "v": "346.9087326", "b": false}, "id": "423029650105604480", "timestamp": 1730560128096, "datetime": "2024-11-02T15:08:48.096Z", "symbol": "BTC/USDT", "order": null, "type": null, "side": "buy", "takerOrMaker": "taker", "price": 69243.26, "amount": 0.00501, "cost": 346.9087326, "fee": {"cost": null, "currency": null}, "fees": []}, {"info": {"i": "423029655998601600", "t": "1730560129501", "p": "69243.26", "q": "0.00549", "v": "380.1454974", "b": false}, "id": "423029655998601600", "timestamp": 1730560129501, "datetime": "2024-11-02T15:08:49.501Z", "symbol": "BTC/USDT", "order": null, "type": null, "side": "buy", "takerOrMaker": "taker", "price": 69243.26, "amount": 0.00549, "cost": 380.1454974, "fee": {"cost": null, "currency": null}, "fees": []}, {"info": {"i": "423029660033522048", "t": "1730560130463", "p": "69243.26", "q": "0.00057", "v": "39.4686582", "b": false}, "id": "423029660033522048", "timestamp": 1730560130463, "datetime": "2024-11-02T15:08:50.463Z", "symbol": "BTC/USDT", "order": null, "type": null, "side": "buy", "takerOrMaker": "taker", "price": 69243.26, "amount": 0.00057, "cost": 39.4686582, "fee": {"cost": null, "currency": null}, "fees": []}, {"info": {"i": "423029665070881152", "t": "1730560131664", "p": "69243.26", "q": "0.00552", "v": "382.2227952", "b": false}, "id": "423029665070881152", "timestamp": 1730560131664, "datetime": "2024-11-02T15:08:51.664Z", "symbol": "BTC/USDT", "order": null, "type": null, "side": "buy", "takerOrMaker": "taker", "price": 69243.26, "amount": 0.00552, "cost": 382.2227952, "fee": {"cost": null, "currency": null}, "fees": []}]}], "editOrder": [{"description": "linear swap edit an order", "method": "editOrder", "input": ["483869393976071040", "BTC/USDT:USDT", "limit", "buy", 2, 54000], "httpResponse": {"returnCode": 0, "msgInfo": "success", "error": null, "result": "483870626049393728"}, "parsedResponse": {"info": {"returnCode": 0, "msgInfo": "success", "error": null, "result": "483870626049393728"}, "id": "483870626049393728", "clientOrderId": null, "timestamp": null, "datetime": null, "lastTradeTimestamp": null, "lastUpdateTimestamp": null, "symbol": "BTC/USDT:USDT", "type": null, "timeInForce": null, "postOnly": null, "side": null, "price": null, "triggerPrice": null, "stopLoss": null, "takeProfit": null, "amount": null, "filled": null, "remaining": null, "cost": null, "average": null, "status": null, "fee": {"currency": null, "cost": null}, "trades": [], "fees": [{"currency": null, "cost": null}], "reduceOnly": null, "stopPrice": null, "takeProfitPrice": null, "stopLossPrice": null}}, {"description": "spot edit an order", "method": "editOrder", "input": ["484203027161892224", "BTC/USDT", "limit", "buy", 0.002, 52000], "httpResponse": {"rc": 0, "mc": "SUCCESS", "ma": [], "result": {"orderId": "484203027161892224", "modifyId": "484205800137559104", "clientModifyId": null}}, "parsedResponse": {"info": {"orderId": "484203027161892224", "modifyId": "484205800137559104", "clientModifyId": null}, "id": "484203027161892224", "clientOrderId": null, "timestamp": null, "datetime": null, "lastTradeTimestamp": null, "lastUpdateTimestamp": null, "symbol": "BTC/USDT", "type": null, "timeInForce": null, "postOnly": null, "side": null, "price": null, "triggerPrice": null, "stopLoss": null, "takeProfit": null, "amount": null, "filled": null, "remaining": null, "cost": null, "average": null, "status": null, "fee": {"currency": null, "cost": null}, "trades": [], "fees": [{"currency": null, "cost": null}], "reduceOnly": null, "stopPrice": null, "takeProfitPrice": null, "stopLossPrice": null}}, {"description": "swap edit a stopLoss order", "method": "editOrder", "input": ["484221817085868800", "BTC/USDT:USDT", "limit", "sell", 2, null, {"stopLoss": 68000}], "httpResponse": {"returnCode": 0, "msgInfo": "success", "error": null, "result": "484221817085868800"}, "parsedResponse": {"info": {"returnCode": 0, "msgInfo": "success", "error": null, "result": "484221817085868800"}, "id": "484221817085868800", "clientOrderId": null, "timestamp": null, "datetime": null, "lastTradeTimestamp": null, "lastUpdateTimestamp": null, "symbol": "BTC/USDT:USDT", "type": null, "timeInForce": null, "postOnly": null, "side": null, "price": null, "triggerPrice": null, "stopLoss": null, "takeProfit": null, "amount": null, "filled": null, "remaining": null, "cost": null, "average": null, "status": null, "fee": {"currency": null, "cost": null}, "trades": [], "fees": [{"currency": null, "cost": null}], "reduceOnly": null, "stopPrice": null, "takeProfitPrice": null, "stopLossPrice": null}}, {"description": "swap edit a takeProfit order", "method": "editOrder", "input": ["484222823387497216", "BTC/USDT:USDT", "limit", "sell", 2, null, {"takeProfit": 98000}], "httpResponse": {"returnCode": 0, "msgInfo": "success", "error": null, "result": "484222823387497216"}, "parsedResponse": {"info": {"returnCode": 0, "msgInfo": "success", "error": null, "result": "484222823387497216"}, "id": "484222823387497216", "clientOrderId": null, "timestamp": null, "datetime": null, "lastTradeTimestamp": null, "lastUpdateTimestamp": null, "symbol": "BTC/USDT:USDT", "type": null, "timeInForce": null, "postOnly": null, "side": null, "price": null, "triggerPrice": null, "stopLoss": null, "takeProfit": null, "amount": null, "filled": null, "remaining": null, "cost": null, "average": null, "status": null, "fee": {"currency": null, "cost": null}, "trades": [], "fees": [{"currency": null, "cost": null}], "reduceOnly": null, "stopPrice": null, "takeProfitPrice": null, "stopLossPrice": null}}]}}