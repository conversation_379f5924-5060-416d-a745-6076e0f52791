{"exchange": "coinbasepro", "skipKeys": [], "methods": {"fetchCurrencies": [{"description": "fetchCurrencies", "method": "fetchCurrencies", "input": [], "httpResponse": [{"id": "DAI", "name": "Dai", "min_size": "0.00001", "status": "online", "message": "", "max_precision": "0.00001", "convertible_to": [], "details": {"type": "crypto", "symbol": null, "network_confirmations": "14", "sort_order": "100", "crypto_address_link": "https://etherscan.io/token/******************************************?a={{address}}", "crypto_transaction_link": "https://etherscan.io/tx/0x{{txId}}", "push_payment_methods": ["crypto"], "group_types": [], "display_name": null, "processing_time_seconds": null, "min_withdrawal_amount": "0.01", "max_withdrawal_amount": "3000000"}, "default_network": "ethereum", "supported_networks": [{"id": "arbitrum", "name": "Arbitrum", "status": "online", "contract_address": "******************************************", "crypto_address_link": "https://arbiscan.io/token/******************************************?a={{address}}", "crypto_transaction_link": "https://arbiscan.io/tx/{{txId}}", "min_withdrawal_amount": "0.01", "max_withdrawal_amount": "150000", "network_confirmations": null, "processing_time_seconds": null}, {"id": "ethereum", "name": "Ethereum", "status": "online", "contract_address": "******************************************", "crypto_address_link": "https://etherscan.io/token/******************************************?a={{address}}", "crypto_transaction_link": "https://etherscan.io/tx/0x{{txId}}", "min_withdrawal_amount": "0.01", "max_withdrawal_amount": "3000000", "network_confirmations": "14", "processing_time_seconds": null}, {"id": "optimism", "name": "Optimism", "status": "online", "contract_address": "******************************************", "crypto_address_link": "https://optimistic.etherscan.io/token/******************************************?a={{address}}", "crypto_transaction_link": "https://optimistic.etherscan.io/tx/{{txId}}", "min_withdrawal_amount": "0.01", "max_withdrawal_amount": "60000", "network_confirmations": null, "processing_time_seconds": null}], "display_name": "DAI"}], "parsedResponse": {"DAI": {"info": {"id": "DAI", "name": "Dai", "min_size": "0.00001", "status": "online", "message": "", "max_precision": "0.00001", "convertible_to": [], "details": {"type": "crypto", "symbol": null, "network_confirmations": "14", "sort_order": "100", "crypto_address_link": "https://etherscan.io/token/******************************************?a={{address}}", "crypto_transaction_link": "https://etherscan.io/tx/0x{{txId}}", "push_payment_methods": ["crypto"], "group_types": [], "display_name": null, "processing_time_seconds": null, "min_withdrawal_amount": "0.01", "max_withdrawal_amount": "3000000"}, "default_network": "ethereum", "supported_networks": [{"id": "arbitrum", "name": "Arbitrum", "status": "online", "contract_address": "******************************************", "crypto_address_link": "https://arbiscan.io/token/******************************************?a={{address}}", "crypto_transaction_link": "https://arbiscan.io/tx/{{txId}}", "min_withdrawal_amount": "0.01", "max_withdrawal_amount": "150000", "network_confirmations": null, "processing_time_seconds": null}, {"id": "ethereum", "name": "Ethereum", "status": "online", "contract_address": "******************************************", "crypto_address_link": "https://etherscan.io/token/******************************************?a={{address}}", "crypto_transaction_link": "https://etherscan.io/tx/0x{{txId}}", "min_withdrawal_amount": "0.01", "max_withdrawal_amount": "3000000", "network_confirmations": "14", "processing_time_seconds": null}, {"id": "optimism", "name": "Optimism", "status": "online", "contract_address": "******************************************", "crypto_address_link": "https://optimistic.etherscan.io/token/******************************************?a={{address}}", "crypto_transaction_link": "https://optimistic.etherscan.io/tx/{{txId}}", "min_withdrawal_amount": "0.01", "max_withdrawal_amount": "60000", "network_confirmations": null, "processing_time_seconds": null}], "display_name": "DAI"}, "id": "DAI", "numericId": null, "code": "DAI", "precision": 1e-05, "type": "crypto", "name": "Dai", "active": true, "deposit": null, "withdraw": null, "fee": null, "fees": {}, "networks": {"ARBONE": {"id": "arbitrum", "name": "Arbitrum", "network": "ARBONE", "active": true, "withdraw": null, "deposit": null, "fee": null, "precision": null, "limits": {"withdraw": {"min": 0.01, "max": 150000}}, "contract": "******************************************", "info": {"id": "arbitrum", "name": "Arbitrum", "status": "online", "contract_address": "******************************************", "crypto_address_link": "https://arbiscan.io/token/******************************************?a={{address}}", "crypto_transaction_link": "https://arbiscan.io/tx/{{txId}}", "min_withdrawal_amount": "0.01", "max_withdrawal_amount": "150000", "network_confirmations": null, "processing_time_seconds": null}}, "ETH": {"id": "ethereum", "name": "Ethereum", "network": "ETH", "active": true, "withdraw": null, "deposit": null, "fee": null, "precision": null, "limits": {"withdraw": {"min": 0.01, "max": 3000000}}, "contract": "******************************************", "info": {"id": "ethereum", "name": "Ethereum", "status": "online", "contract_address": "******************************************", "crypto_address_link": "https://etherscan.io/token/******************************************?a={{address}}", "crypto_transaction_link": "https://etherscan.io/tx/0x{{txId}}", "min_withdrawal_amount": "0.01", "max_withdrawal_amount": "3000000", "network_confirmations": "14", "processing_time_seconds": null}}, "OP": {"id": "optimism", "name": "Optimism", "network": "OP", "active": true, "withdraw": null, "deposit": null, "fee": null, "precision": null, "limits": {"withdraw": {"min": 0.01, "max": 60000}}, "contract": "******************************************", "info": {"id": "optimism", "name": "Optimism", "status": "online", "contract_address": "******************************************", "crypto_address_link": "https://optimistic.etherscan.io/token/******************************************?a={{address}}", "crypto_transaction_link": "https://optimistic.etherscan.io/tx/{{txId}}", "min_withdrawal_amount": "0.01", "max_withdrawal_amount": "60000", "network_confirmations": null, "processing_time_seconds": null}}}, "limits": {"amount": {"min": null, "max": null}, "withdraw": {"min": 0.01, "max": 3000000}, "deposit": {"min": null, "max": null}}}}}], "fetchTrades": [{"description": "public spot trades", "method": "fetchTrades", "input": ["BTC/USDT", null, 1], "httpResponse": [{"trade_id": "18502034", "side": "sell", "size": "0.00007448", "price": "73271.63000000", "time": "2024-03-13T11:00:17.710679Z"}], "parsedResponse": [{"id": "18502034", "order": null, "info": {"trade_id": "18502034", "side": "sell", "size": "0.00007448", "price": "73271.63000000", "time": "2024-03-13T11:00:17.710679Z"}, "timestamp": 1710327617710, "datetime": "2024-03-13T11:00:17.710Z", "symbol": "BTC/USDT", "type": null, "takerOrMaker": null, "side": "buy", "price": 73271.63, "amount": 7.448e-05, "fee": {"cost": null, "currency": null}, "cost": 5.4572710024, "fees": []}]}], "fetchTicker": [{"description": "public spot ticker", "method": "fetchTicker", "input": ["BTC/USDT"], "httpResponse": {"ask": "73369.19", "bid": "73360.34", "volume": "1207.50668151", "trade_id": "18502105", "price": "73367.8", "size": "0.00730812", "time": "2024-03-13T11:10:41.270099Z", "rfq_volume": "7.669923"}, "parsedResponse": {"symbol": "BTC/USDT", "timestamp": 1710328241270, "datetime": "2024-03-13T11:10:41.270Z", "high": null, "low": null, "bid": 73360.34, "bidVolume": null, "ask": 73369.19, "askVolume": null, "vwap": null, "open": null, "close": 73367.8, "last": 73367.8, "previousClose": null, "change": null, "percentage": null, "average": null, "baseVolume": 1207.50668151, "quoteVolume": null, "markPrice": null, "indexPrice": null, "info": {"ask": "73369.19", "bid": "73360.34", "volume": "1207.50668151", "trade_id": "18502105", "price": "73367.8", "size": "0.00730812", "time": "2024-03-13T11:10:41.270099Z", "rfq_volume": "7.669923"}}}], "fetchOHLCV": [{"description": "public spot ohlcv", "method": "fetchOHLCV", "input": ["BTC/USDT", "1h", null, 1], "httpResponse": [[1710327600, 73241.5, 73402.47, 73243.33, 73370.65, 1.56794406]], "parsedResponse": [[1710327600000, 73243.33, 73402.47, 73241.5, 73370.65, 1.56794406]]}]}}