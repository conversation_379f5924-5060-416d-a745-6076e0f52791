{"exchange": "mexc", "options": {}, "methods": {"createOrder": [{"description": "spot market buy order", "method": "createOrder", "input": ["ATLAS/USDT", "market", "buy", 1000], "httpResponse": {"symbol": "ATLASUSDT", "orderId": "C02__474424498318368768043", "orderListId": "-1", "price": "0.003143", "origQty": "1000", "type": "MARKET", "side": "BUY", "transactTime": "1728893850646"}, "parsedResponse": {"id": "C02__474424498318368768043", "clientOrderId": null, "timestamp": 1728893850646, "datetime": "2024-10-14T08:17:30.646Z", "lastTradeTimestamp": null, "status": null, "symbol": "ATLAS/USDT", "type": "market", "timeInForce": "IOC", "side": "buy", "price": 0.003143, "stopPrice": null, "triggerPrice": null, "average": null, "amount": 1000, "cost": null, "filled": null, "remaining": null, "fee": null, "trades": [], "info": {"symbol": "ATLASUSDT", "orderId": "C02__474424498318368768043", "orderListId": "-1", "price": "0.003143", "origQty": "1000", "type": "MARKET", "side": "BUY", "transactTime": "1728893850646"}, "fees": [], "lastUpdateTimestamp": null, "postOnly": null, "reduceOnly": null, "takeProfitPrice": null, "stopLossPrice": null}}, {"description": "tif I<PERSON>", "method": "createOrder", "input": ["ADA/USDT", "limit", "buy", 0.5, 2, {"timeInForce": "IOC"}], "httpResponse": {"symbol": "ADAUSDT", "orderId": "C02__545584045430513665029", "orderListId": "-1", "price": "2", "origQty": "0.5", "type": "IMMEDIATE_OR_CANCEL", "side": "BUY", "transactTime": "1745859608758"}, "parsedResponse": {"id": "C02__545584045430513665029", "clientOrderId": null, "timestamp": 1745859608758, "datetime": "2025-04-28T17:00:08.758Z", "lastTradeTimestamp": null, "status": null, "symbol": "ADA/USDT", "type": "limit", "timeInForce": "IOC", "side": "buy", "price": 2, "triggerPrice": null, "average": null, "amount": 0.5, "cost": null, "filled": null, "remaining": null, "fee": null, "trades": [], "info": {"symbol": "ADAUSDT", "orderId": "C02__545584045430513665029", "orderListId": "-1", "price": "2", "origQty": "0.5", "type": "IMMEDIATE_OR_CANCEL", "side": "BUY", "transactTime": "1745859608758"}, "fees": [], "lastUpdateTimestamp": null, "postOnly": false, "reduceOnly": null, "stopPrice": null, "takeProfitPrice": null, "stopLossPrice": null}}, {"description": "tif FOK", "method": "createOrder", "input": ["ADA/USDT", "limit", "buy", 0.5, 2, {"timeInForce": "FOK"}], "httpResponse": {"symbol": "ADAUSDT", "orderId": "C02__545583953424261120029", "orderListId": "-1", "price": "2", "origQty": "0.5", "type": "FILL_OR_KILL", "side": "BUY", "transactTime": "1745859586822"}, "parsedResponse": {"id": "C02__545583953424261120029", "clientOrderId": null, "timestamp": 1745859586822, "datetime": "2025-04-28T16:59:46.822Z", "lastTradeTimestamp": null, "status": null, "symbol": "ADA/USDT", "type": "limit", "timeInForce": "FOK", "side": "buy", "price": 2, "triggerPrice": null, "average": null, "amount": 0.5, "cost": null, "filled": null, "remaining": null, "fee": null, "trades": [], "info": {"symbol": "ADAUSDT", "orderId": "C02__545583953424261120029", "orderListId": "-1", "price": "2", "origQty": "0.5", "type": "FILL_OR_KILL", "side": "BUY", "transactTime": "1745859586822"}, "fees": [], "lastUpdateTimestamp": null, "postOnly": false, "reduceOnly": null, "stopPrice": null, "takeProfitPrice": null, "stopLossPrice": null}}], "fetchOrderBook": [{"description": "Spot orderbook", "method": "fetchOrderBook", "input": ["BTC/USDT", 1], "httpResponse": {"lastUpdateId": "20453289489", "bids": [["45995.47", "10.337165"]], "asks": [["45995.48", "13.457322"]], "timestamp": "1704908526228"}, "parsedResponse": {"symbol": "BTC/USDT", "bids": [[45995.47, 10.337165]], "asks": [[45995.48, 13.457322]], "timestamp": 1704908526228, "datetime": "2024-01-10T17:42:06.228Z", "nonce": 20453289489}}], "fetchDepositAddress": [{"description": "fetch USDT address (should return erc20)", "method": "fetchDepositAddress", "input": ["USDT"], "httpResponse": [{"coin": "USDT", "network": "BNB Smart Chain(BEP20)", "address": "******************************************", "memo": null, "chainName": "BNB Smart Chain(BEP20)", "chainDisplayName": "BNB Smart Chain(BEP20)", "netWork": "BSC"}, {"coin": "USDT", "network": "Ethereum(ERC20)", "address": "******************************************", "memo": null, "chainName": "Ethereum(ERC20)", "chainDisplayName": "Ethereum(ERC20)", "netWork": "ETH"}, {"coin": "USDT", "network": "Tron(TRC20)", "address": "TGKyBGx7m3QGNJcteeS1eEr7eVMJQB3afC", "memo": null, "chainName": "Tron(TRC20)", "chainDisplayName": "Tron(TRC20)", "netWork": "TRX"}], "parsedResponse": {"info": {"coin": "USDT", "network": "Ethereum(ERC20)", "address": "******************************************", "memo": null, "chainName": "Ethereum(ERC20)", "chainDisplayName": "Ethereum(ERC20)", "netWork": "ETH"}, "currency": "USDT", "network": "ERC20", "address": "******************************************", "tag": null}}, {"description": "fetch usdt trc20 address", "method": "fetchDepositAddress", "input": ["USDT", {"network": "TRC20"}], "httpResponse": [{"coin": "USDT", "network": "BNB Smart Chain(BEP20)", "address": "******************************************", "memo": null, "chainName": "BNB Smart Chain(BEP20)", "chainDisplayName": "BNB Smart Chain(BEP20)", "netWork": "BSC"}, {"coin": "USDT", "network": "Ethereum(ERC20)", "address": "******************************************", "memo": null, "chainName": "Ethereum(ERC20)", "chainDisplayName": "Ethereum(ERC20)", "netWork": "ETH"}, {"coin": "USDT", "network": "Tron(TRC20)", "address": "TGKyBGx7m3QGNJcteeF1eEr7eAMJQA33AD", "memo": null, "chainName": "Tron(TRC20)", "chainDisplayName": "Tron(TRC20)", "netWork": "TRX"}], "parsedResponse": {"info": {"coin": "USDT", "network": "Tron(TRC20)", "address": "TGKyBGx7m3QGNJcteeF1eEr7eAMJQA33AD", "memo": null, "chainName": "Tron(TRC20)", "chainDisplayName": "Tron(TRC20)", "netWork": "TRX"}, "currency": "USDT", "network": "TRC20", "address": "TGKyBGx7m3QGNJcteeF1eEr7eAMJQA33AD", "tag": null}}], "fetchMyTrades": [{"description": "user trade", "method": "fetchMyTrades", "input": ["ATLAS/USDT", null, 1], "httpResponse": [{"symbol": "ATLASUSDT", "id": "dd44f79dda764c2e927a190ac5e7a1e5", "orderId": "C02__385413065019117569043", "orderListId": "-1", "price": "0.004542", "qty": "1220", "quoteQty": "5.54124", "commission": "0", "commissionAsset": "USDT", "time": "1707671871000", "isBuyer": false, "isMaker": false, "isBestMatch": true, "isSelfTrade": false, "clientOrderId": null}], "parsedResponse": [{"id": "dd44f79dda764c2e927a190ac5e7a1e5", "order": "C02__385413065019117569043", "timestamp": 1707671871000, "datetime": "2024-02-11T17:17:51.000Z", "symbol": "ATLAS/USDT", "type": null, "side": "sell", "takerOrMaker": "taker", "price": 0.004542, "amount": 1220, "cost": 5.54124, "fee": {"cost": 0, "currency": "USDT"}, "info": {"symbol": "ATLASUSDT", "id": "dd44f79dda764c2e927a190ac5e7a1e5", "orderId": "C02__385413065019117569043", "orderListId": "-1", "price": "0.004542", "qty": "1220", "quoteQty": "5.54124", "commission": "0", "commissionAsset": "USDT", "time": "1707671871000", "isBuyer": false, "isMaker": false, "isBestMatch": true, "isSelfTrade": false, "clientOrderId": null}, "fees": [{"currency": "USDT", "cost": 0}]}]}], "fetchOrders": [{"description": "fetch orders", "method": "fetchOrders", "input": ["ATLAS/USDT", null, 1], "httpResponse": [{"symbol": "ATLASUSDT", "orderId": "C02__385413065019117569043", "orderListId": "-1", "clientOrderId": null, "price": "0.004327", "origQty": "1220", "executedQty": "1220", "cummulativeQuoteQty": "5.54124", "status": "FILLED", "timeInForce": null, "type": "MARKET", "side": "SELL", "stopPrice": null, "icebergQty": null, "time": "1707671871000", "updateTime": "1707671871000", "isWorking": true, "origQuoteOrderQty": "5.27894"}], "parsedResponse": [{"id": "C02__385413065019117569043", "clientOrderId": null, "timestamp": 1707671871000, "datetime": "2024-02-11T17:17:51.000Z", "lastTradeTimestamp": null, "status": "closed", "symbol": "ATLAS/USDT", "type": "market", "timeInForce": "IOC", "side": "sell", "price": 0.004327, "stopPrice": null, "triggerPrice": null, "average": 0.004542, "amount": 1220, "cost": 5.54124, "filled": 1220, "remaining": 0, "fee": null, "trades": [], "info": {"symbol": "ATLASUSDT", "orderId": "C02__385413065019117569043", "orderListId": "-1", "clientOrderId": null, "price": "0.004327", "origQty": "1220", "executedQty": "1220", "cummulativeQuoteQty": "5.54124", "status": "FILLED", "timeInForce": null, "type": "MARKET", "side": "SELL", "stopPrice": null, "icebergQty": null, "time": "1707671871000", "updateTime": "1707671871000", "isWorking": true, "origQuoteOrderQty": "5.27894"}, "fees": [], "lastUpdateTimestamp": null, "postOnly": null, "reduceOnly": null, "takeProfitPrice": null, "stopLossPrice": null}]}], "fetchTrades": [{"description": "public spot trades", "method": "fetchTrades", "input": ["BTC/USDT", null, 1], "httpResponse": [{"a": null, "f": null, "l": null, "p": "73238.7", "q": "0.000112", "T": "1710327661000", "m": false, "M": true}], "parsedResponse": [{"id": "1710327661000-buy-0.000112-73238.7-taker", "order": null, "timestamp": 1710327661000, "datetime": "2024-03-13T11:01:01.000Z", "symbol": "BTC/USDT", "type": null, "side": "buy", "takerOrMaker": "taker", "price": 73238.7, "amount": 0.000112, "cost": 8.2027344, "fee": {"cost": null, "currency": null}, "info": {"a": null, "f": null, "l": null, "p": "73238.7", "q": "0.000112", "T": "1710327661000", "m": false, "M": true}, "fees": []}]}], "fetchTicker": [{"description": "public spot ticker", "method": "fetchTicker", "input": ["BTC/USDT"], "httpResponse": {"symbol": "BTCUSDT", "priceChange": "1248.51", "priceChangePercent": "0.0173", "prevClosePrice": "72149.26", "lastPrice": "73397.77", "bidPrice": "73369.83", "bidQty": "10.010045", "askPrice": "73369.84", "askQty": "6.337532", "openPrice": "72149.26", "highPrice": "73639.99", "lowPrice": "68629.42", "volume": "7729.258925", "quoteVolume": "554877037.44", "openTime": "1710328232149", "closeTime": "1710328251172", "count": null}, "parsedResponse": {"symbol": "BTC/USDT", "timestamp": 1710328251172, "datetime": "2024-03-13T11:10:51.172Z", "open": 72149.26, "high": 73639.99, "low": 68629.42, "close": 73397.77, "bid": 73369.83, "bidVolume": 10.010045, "ask": 73369.84, "askVolume": 6.337532, "vwap": 71789.16411316884, "previousClose": 72149.26, "change": 1248.51, "percentage": 1.73, "average": 72773.51, "baseVolume": 7729.258925, "quoteVolume": 554877037.44, "markPrice": null, "indexPrice": null, "info": {"symbol": "BTCUSDT", "priceChange": "1248.51", "priceChangePercent": "0.0173", "prevClosePrice": "72149.26", "lastPrice": "73397.77", "bidPrice": "73369.83", "bidQty": "10.010045", "askPrice": "73369.84", "askQty": "6.337532", "openPrice": "72149.26", "highPrice": "73639.99", "lowPrice": "68629.42", "volume": "7729.258925", "quoteVolume": "554877037.44", "openTime": "1710328232149", "closeTime": "1710328251172", "count": null}, "last": 73397.77}}], "fetchOHLCV": [{"description": "public spot ohlcv", "method": "fetchOHLCV", "input": ["BTC/USDT", "1h", null, 1], "httpResponse": [[1710327600000, "73260.0", "73442.95", "73238.69", "73414.21", "49.560236", 1710331200000, "3633995.45"]], "parsedResponse": [[1710327600000, 73260, 73442.95, 73238.69, 73414.21, 49.560236]]}]}}