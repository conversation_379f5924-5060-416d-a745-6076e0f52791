{"exchange": "gate", "options": {}, "methods": {"fetchDepositAddress": [{"description": "usdt on ERC20", "method": "fetchDepositAddress", "input": ["USDT", {"network": "ERC20"}], "httpResponse": {"currency": "USDT", "address": "******************************************", "multichain_addresses": [{"chain": "ETH", "address": "******************************************", "payment_id": "", "payment_name": "", "obtain_failed": "0", "min_confirms": "10"}, {"chain": "APT", "address": "0xeedf3a7a8bddd275f8e58b4f4a6626653636430a8db9cc7eca36dee5a9d8c02b", "payment_id": "", "payment_name": "", "obtain_failed": "0", "min_confirms": "600"}, {"chain": "ARBEVM", "address": "******************************************", "payment_id": "", "payment_name": "", "obtain_failed": "0", "min_confirms": "300"}, {"chain": "BSC", "address": "******************************************", "payment_id": "", "payment_name": "", "obtain_failed": "0", "min_confirms": "15"}, {"chain": "AVAX_C", "address": "******************************************", "payment_id": "", "payment_name": "", "obtain_failed": "0", "min_confirms": "6"}, {"chain": "CELO", "address": "******************************************", "payment_id": "", "payment_name": "", "obtain_failed": "0", "min_confirms": "180"}, {"chain": "DOTSM", "address": "15jBY6LxpjbMprV47rcEZfPE1xU7iNLEPYDA1mC359mTp18w", "payment_id": "", "payment_name": "", "obtain_failed": "0", "min_confirms": "20"}, {"chain": "GTEVM", "address": "******************************************", "payment_id": "", "payment_name": "", "obtain_failed": "0", "min_confirms": "25"}, {"chain": "KAIA", "address": "******************************************", "payment_id": "", "payment_name": "", "obtain_failed": "0", "min_confirms": "200"}, {"chain": "KAVAEVM", "address": "******************************************", "payment_id": "", "payment_name": "", "obtain_failed": "0", "min_confirms": "20"}, {"chain": "MATIC", "address": "******************************************", "payment_id": "", "payment_name": "", "obtain_failed": "0", "min_confirms": "100"}, {"chain": "NEAR", "address": "9d343dd0c97e3e03328f20e331ab8088df70fea432e52e819af226d204761332", "payment_id": "", "payment_name": "", "obtain_failed": "0", "min_confirms": "100"}, {"chain": "OKT", "address": "******************************************", "payment_id": "", "payment_name": "", "obtain_failed": "0", "min_confirms": "60"}, {"chain": "OPETH", "address": "******************************************", "payment_id": "", "payment_name": "", "obtain_failed": "0", "min_confirms": "60"}, {"chain": "SOL", "address": "53wCea2kFkR4kkKz1By99EDert3e3kq5FVr7Ew2ofpeil", "payment_id": "", "payment_name": "", "obtain_failed": "0", "min_confirms": "20"}, {"chain": "TON", "address": "EQC0lrj3O0af8GotieYsTXChA_wijIIVN7Sd_wkgYLwoH07q", "payment_id": "855032211", "payment_name": "Memo", "obtain_failed": "0", "min_confirms": "35"}, {"chain": "TRX", "address": "TEe8JxcJWPsF24K4wu71X6maCtxqsxfOp2", "payment_id": "", "payment_name": "", "obtain_failed": "0", "min_confirms": "20"}, {"chain": "XTZ", "address": "tz2aeJdNXyRKC7weUxtvRdBbBjFp1xcmprol", "payment_id": "", "payment_name": "", "obtain_failed": "0", "min_confirms": "10"}], "min_deposit_amount": "0.01", "min_confirms": null}, "parsedResponse": {"info": {"chain": "ETH", "address": "******************************************", "payment_id": "", "payment_name": "", "obtain_failed": "0", "min_confirms": "10"}, "currency": null, "address": "******************************************", "tag": null, "network": "ERC20"}}], "fetchCurrencies": [{"description": "fetchCurrencies", "method": "fetchCurrencies", "input": [], "httpResponse": [{"currency": "USDT", "name": "<PERSON><PERSON>", "delisted": false, "withdraw_disabled": false, "withdraw_delayed": false, "deposit_disabled": false, "trade_disabled": false, "fixed_rate": "", "chain": "ETH", "chains": [{"name": "ETH", "addr": "******************************************", "withdraw_disabled": false, "withdraw_delayed": false, "deposit_disabled": false}, {"name": "ALGO", "addr": "312769", "withdraw_disabled": true, "withdraw_delayed": false, "deposit_disabled": true}, {"name": "APT", "addr": "0x357b0b74bc833e95a115ad22604854d6b0fca151cecd94111770e5d6ffc9dc2b", "withdraw_disabled": false, "withdraw_delayed": false, "deposit_disabled": false}, {"name": "ARBEVM", "addr": "******************************************", "withdraw_disabled": false, "withdraw_delayed": false, "deposit_disabled": false}, {"name": "BSC", "addr": "******************************************", "withdraw_disabled": false, "withdraw_delayed": false, "deposit_disabled": false}, {"name": "BTC", "addr": "aaaaa000000111111xxx00000xxx000111", "withdraw_disabled": true, "withdraw_delayed": false, "deposit_disabled": true}, {"name": "AVAX_C", "addr": "******************************************", "withdraw_disabled": false, "withdraw_delayed": false, "deposit_disabled": false}, {"name": "CELO", "addr": "******************************************", "withdraw_disabled": false, "withdraw_delayed": false, "deposit_disabled": false}, {"name": "DOTSM", "addr": "1984", "withdraw_disabled": false, "withdraw_delayed": false, "deposit_disabled": false}, {"name": "EOS", "addr": "USDT@tethertether", "withdraw_disabled": true, "withdraw_delayed": false, "deposit_disabled": true}, {"name": "GTEVM", "addr": "******************************************", "withdraw_disabled": false, "withdraw_delayed": false, "deposit_disabled": false}, {"name": "KAIA", "addr": "******************************************", "withdraw_disabled": false, "withdraw_delayed": false, "deposit_disabled": false}, {"name": "KAVAEVM", "addr": "******************************************", "withdraw_disabled": false, "withdraw_delayed": false, "deposit_disabled": false}, {"name": "KSMSM", "addr": "1984", "withdraw_disabled": true, "withdraw_delayed": false, "deposit_disabled": true}, {"name": "MATIC", "addr": "******************************************", "withdraw_disabled": false, "withdraw_delayed": false, "deposit_disabled": false}, {"name": "NEAR", "addr": "usdt.tether-token.near", "withdraw_disabled": false, "withdraw_delayed": false, "deposit_disabled": false}, {"name": "OKT", "addr": "******************************************", "withdraw_disabled": true, "withdraw_delayed": false, "deposit_disabled": false}, {"name": "OPETH", "addr": "******************************************", "withdraw_disabled": false, "withdraw_delayed": false, "deposit_disabled": false}, {"name": "SOL", "addr": "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB", "withdraw_disabled": false, "withdraw_delayed": false, "deposit_disabled": false}, {"name": "TON", "addr": "EQCxE6mUtQJKFnGfaROTKOt1lZbDiiX1kCixRv7Nw2Id_sDs", "withdraw_disabled": false, "withdraw_delayed": false, "deposit_disabled": false}, {"name": "TRX", "addr": "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t", "withdraw_disabled": false, "withdraw_delayed": false, "deposit_disabled": false}, {"name": "XTZ", "addr": "KT1XnTn74bUtxHfDtBmm2bGZAQfhPbvKWR8o", "withdraw_disabled": false, "withdraw_delayed": false, "deposit_disabled": false}]}], "parsedResponse": {"USDT": {"info": {"currency": "USDT", "name": "<PERSON><PERSON>", "delisted": false, "withdraw_disabled": false, "withdraw_delayed": false, "deposit_disabled": false, "trade_disabled": false, "fixed_rate": "", "chain": "ETH", "chains": [{"name": "ETH", "addr": "******************************************", "withdraw_disabled": false, "withdraw_delayed": false, "deposit_disabled": false}, {"name": "ALGO", "addr": "312769", "withdraw_disabled": true, "withdraw_delayed": false, "deposit_disabled": true}, {"name": "APT", "addr": "0x357b0b74bc833e95a115ad22604854d6b0fca151cecd94111770e5d6ffc9dc2b", "withdraw_disabled": false, "withdraw_delayed": false, "deposit_disabled": false}, {"name": "ARBEVM", "addr": "******************************************", "withdraw_disabled": false, "withdraw_delayed": false, "deposit_disabled": false}, {"name": "BSC", "addr": "******************************************", "withdraw_disabled": false, "withdraw_delayed": false, "deposit_disabled": false}, {"name": "BTC", "addr": "aaaaa000000111111xxx00000xxx000111", "withdraw_disabled": true, "withdraw_delayed": false, "deposit_disabled": true}, {"name": "AVAX_C", "addr": "******************************************", "withdraw_disabled": false, "withdraw_delayed": false, "deposit_disabled": false}, {"name": "CELO", "addr": "******************************************", "withdraw_disabled": false, "withdraw_delayed": false, "deposit_disabled": false}, {"name": "DOTSM", "addr": "1984", "withdraw_disabled": false, "withdraw_delayed": false, "deposit_disabled": false}, {"name": "EOS", "addr": "USDT@tethertether", "withdraw_disabled": true, "withdraw_delayed": false, "deposit_disabled": true}, {"name": "GTEVM", "addr": "******************************************", "withdraw_disabled": false, "withdraw_delayed": false, "deposit_disabled": false}, {"name": "KAIA", "addr": "******************************************", "withdraw_disabled": false, "withdraw_delayed": false, "deposit_disabled": false}, {"name": "KAVAEVM", "addr": "******************************************", "withdraw_disabled": false, "withdraw_delayed": false, "deposit_disabled": false}, {"name": "KSMSM", "addr": "1984", "withdraw_disabled": true, "withdraw_delayed": false, "deposit_disabled": true}, {"name": "MATIC", "addr": "******************************************", "withdraw_disabled": false, "withdraw_delayed": false, "deposit_disabled": false}, {"name": "NEAR", "addr": "usdt.tether-token.near", "withdraw_disabled": false, "withdraw_delayed": false, "deposit_disabled": false}, {"name": "OKT", "addr": "******************************************", "withdraw_disabled": true, "withdraw_delayed": false, "deposit_disabled": false}, {"name": "OPETH", "addr": "******************************************", "withdraw_disabled": false, "withdraw_delayed": false, "deposit_disabled": false}, {"name": "SOL", "addr": "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB", "withdraw_disabled": false, "withdraw_delayed": false, "deposit_disabled": false}, {"name": "TON", "addr": "EQCxE6mUtQJKFnGfaROTKOt1lZbDiiX1kCixRv7Nw2Id_sDs", "withdraw_disabled": false, "withdraw_delayed": false, "deposit_disabled": false}, {"name": "TRX", "addr": "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t", "withdraw_disabled": false, "withdraw_delayed": false, "deposit_disabled": false}, {"name": "XTZ", "addr": "KT1XnTn74bUtxHfDtBmm2bGZAQfhPbvKWR8o", "withdraw_disabled": false, "withdraw_delayed": false, "deposit_disabled": false}]}, "id": "USDT", "numericId": null, "code": "USDT", "precision": 0.0001, "type": "crypto", "name": "<PERSON><PERSON>", "active": true, "deposit": true, "withdraw": true, "fee": null, "fees": {}, "networks": {"ERC20": {"info": {"name": "ETH", "addr": "******************************************", "withdraw_disabled": false, "withdraw_delayed": false, "deposit_disabled": false}, "id": "ETH", "network": "ERC20", "active": true, "deposit": true, "withdraw": true, "fee": null, "precision": 0.0001, "limits": {"deposit": {"min": null, "max": null}, "withdraw": {"min": null, "max": null}}}, "ALGO": {"info": {"name": "ALGO", "addr": "312769", "withdraw_disabled": true, "withdraw_delayed": false, "deposit_disabled": true}, "id": "ALGO", "network": "ALGO", "active": false, "deposit": false, "withdraw": false, "fee": null, "precision": 0.0001, "limits": {"deposit": {"min": null, "max": null}, "withdraw": {"min": null, "max": null}}}, "APT": {"info": {"name": "APT", "addr": "0x357b0b74bc833e95a115ad22604854d6b0fca151cecd94111770e5d6ffc9dc2b", "withdraw_disabled": false, "withdraw_delayed": false, "deposit_disabled": false}, "id": "APT", "network": "APT", "active": true, "deposit": true, "withdraw": true, "fee": null, "precision": 0.0001, "limits": {"deposit": {"min": null, "max": null}, "withdraw": {"min": null, "max": null}}}, "ARBONE": {"info": {"name": "ARBEVM", "addr": "******************************************", "withdraw_disabled": false, "withdraw_delayed": false, "deposit_disabled": false}, "id": "ARBEVM", "network": "ARBONE", "active": true, "deposit": true, "withdraw": true, "fee": null, "precision": 0.0001, "limits": {"deposit": {"min": null, "max": null}, "withdraw": {"min": null, "max": null}}}, "BEP20": {"info": {"name": "BSC", "addr": "******************************************", "withdraw_disabled": false, "withdraw_delayed": false, "deposit_disabled": false}, "id": "BSC", "network": "BEP20", "active": true, "deposit": true, "withdraw": true, "fee": null, "precision": 0.0001, "limits": {"deposit": {"min": null, "max": null}, "withdraw": {"min": null, "max": null}}}, "BTC": {"info": {"name": "BTC", "addr": "aaaaa000000111111xxx00000xxx000111", "withdraw_disabled": true, "withdraw_delayed": false, "deposit_disabled": true}, "id": "BTC", "network": "BTC", "active": false, "deposit": false, "withdraw": false, "fee": null, "precision": 0.0001, "limits": {"deposit": {"min": null, "max": null}, "withdraw": {"min": null, "max": null}}}, "AVAXC": {"info": {"name": "AVAX_C", "addr": "******************************************", "withdraw_disabled": false, "withdraw_delayed": false, "deposit_disabled": false}, "id": "AVAX_C", "network": "AVAXC", "active": true, "deposit": true, "withdraw": true, "fee": null, "precision": 0.0001, "limits": {"deposit": {"min": null, "max": null}, "withdraw": {"min": null, "max": null}}}, "CELO": {"info": {"name": "CELO", "addr": "******************************************", "withdraw_disabled": false, "withdraw_delayed": false, "deposit_disabled": false}, "id": "CELO", "network": "CELO", "active": true, "deposit": true, "withdraw": true, "fee": null, "precision": 0.0001, "limits": {"deposit": {"min": null, "max": null}, "withdraw": {"min": null, "max": null}}}, "POLKADOT": {"info": {"name": "DOTSM", "addr": "1984", "withdraw_disabled": false, "withdraw_delayed": false, "deposit_disabled": false}, "id": "DOTSM", "network": "POLKADOT", "active": true, "deposit": true, "withdraw": true, "fee": null, "precision": 0.0001, "limits": {"deposit": {"min": null, "max": null}, "withdraw": {"min": null, "max": null}}}, "EOS": {"info": {"name": "EOS", "addr": "USDT@tethertether", "withdraw_disabled": true, "withdraw_delayed": false, "deposit_disabled": true}, "id": "EOS", "network": "EOS", "active": false, "deposit": false, "withdraw": false, "fee": null, "precision": 0.0001, "limits": {"deposit": {"min": null, "max": null}, "withdraw": {"min": null, "max": null}}}, "GATECHAIN": {"info": {"name": "GTEVM", "addr": "******************************************", "withdraw_disabled": false, "withdraw_delayed": false, "deposit_disabled": false}, "id": "GTEVM", "network": "GATECHAIN", "active": true, "deposit": true, "withdraw": true, "fee": null, "precision": 0.0001, "limits": {"deposit": {"min": null, "max": null}, "withdraw": {"min": null, "max": null}}}, "KAIA": {"info": {"name": "KAIA", "addr": "******************************************", "withdraw_disabled": false, "withdraw_delayed": false, "deposit_disabled": false}, "id": "KAIA", "network": "KAIA", "active": true, "deposit": true, "withdraw": true, "fee": null, "precision": 0.0001, "limits": {"deposit": {"min": null, "max": null}, "withdraw": {"min": null, "max": null}}}, "KAVAEVM": {"info": {"name": "KAVAEVM", "addr": "******************************************", "withdraw_disabled": false, "withdraw_delayed": false, "deposit_disabled": false}, "id": "KAVAEVM", "network": "KAVAEVM", "active": true, "deposit": true, "withdraw": true, "fee": null, "precision": 0.0001, "limits": {"deposit": {"min": null, "max": null}, "withdraw": {"min": null, "max": null}}}, "KUSAMA": {"info": {"name": "KSMSM", "addr": "1984", "withdraw_disabled": true, "withdraw_delayed": false, "deposit_disabled": true}, "id": "KSMSM", "network": "KUSAMA", "active": false, "deposit": false, "withdraw": false, "fee": null, "precision": 0.0001, "limits": {"deposit": {"min": null, "max": null}, "withdraw": {"min": null, "max": null}}}, "MATIC": {"info": {"name": "MATIC", "addr": "******************************************", "withdraw_disabled": false, "withdraw_delayed": false, "deposit_disabled": false}, "id": "MATIC", "network": "MATIC", "active": true, "deposit": true, "withdraw": true, "fee": null, "precision": 0.0001, "limits": {"deposit": {"min": null, "max": null}, "withdraw": {"min": null, "max": null}}}, "NEAR": {"info": {"name": "NEAR", "addr": "usdt.tether-token.near", "withdraw_disabled": false, "withdraw_delayed": false, "deposit_disabled": false}, "id": "NEAR", "network": "NEAR", "active": true, "deposit": true, "withdraw": true, "fee": null, "precision": 0.0001, "limits": {"deposit": {"min": null, "max": null}, "withdraw": {"min": null, "max": null}}}, "OKC": {"info": {"name": "OKT", "addr": "******************************************", "withdraw_disabled": true, "withdraw_delayed": false, "deposit_disabled": false}, "id": "OKT", "network": "OKC", "active": false, "deposit": true, "withdraw": false, "fee": null, "precision": 0.0001, "limits": {"deposit": {"min": null, "max": null}, "withdraw": {"min": null, "max": null}}}, "OP": {"info": {"name": "OPETH", "addr": "******************************************", "withdraw_disabled": false, "withdraw_delayed": false, "deposit_disabled": false}, "id": "OPETH", "network": "OP", "active": true, "deposit": true, "withdraw": true, "fee": null, "precision": 0.0001, "limits": {"deposit": {"min": null, "max": null}, "withdraw": {"min": null, "max": null}}}, "SOL": {"info": {"name": "SOL", "addr": "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB", "withdraw_disabled": false, "withdraw_delayed": false, "deposit_disabled": false}, "id": "SOL", "network": "SOL", "active": true, "deposit": true, "withdraw": true, "fee": null, "precision": 0.0001, "limits": {"deposit": {"min": null, "max": null}, "withdraw": {"min": null, "max": null}}}, "TON": {"info": {"name": "TON", "addr": "EQCxE6mUtQJKFnGfaROTKOt1lZbDiiX1kCixRv7Nw2Id_sDs", "withdraw_disabled": false, "withdraw_delayed": false, "deposit_disabled": false}, "id": "TON", "network": "TON", "active": true, "deposit": true, "withdraw": true, "fee": null, "precision": 0.0001, "limits": {"deposit": {"min": null, "max": null}, "withdraw": {"min": null, "max": null}}}, "TRC20": {"info": {"name": "TRX", "addr": "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t", "withdraw_disabled": false, "withdraw_delayed": false, "deposit_disabled": false}, "id": "TRX", "network": "TRC20", "active": true, "deposit": true, "withdraw": true, "fee": null, "precision": 0.0001, "limits": {"deposit": {"min": null, "max": null}, "withdraw": {"min": null, "max": null}}}, "XTZ": {"info": {"name": "XTZ", "addr": "KT1XnTn74bUtxHfDtBmm2bGZAQfhPbvKWR8o", "withdraw_disabled": false, "withdraw_delayed": false, "deposit_disabled": false}, "id": "XTZ", "network": "XTZ", "active": true, "deposit": true, "withdraw": true, "fee": null, "precision": 0.0001, "limits": {"deposit": {"min": null, "max": null}, "withdraw": {"min": null, "max": null}}}}, "limits": {"deposit": {"min": null, "max": null}, "withdraw": {"min": null, "max": null}}}}}], "fetchLiquidations": [{"description": "fetch liquidations BTC/USDT:USDT", "method": "fetchLiquidations", "input": ["BTC/USDT:USDT"], "httpResponse": [{"contract": "BTC_USDT", "left": "0", "size": "33", "fill_price": "36454.4", "order_price": "36309.2", "time": "1700267258"}, {"contract": "BTC_USDT", "left": "0", "size": "287", "fill_price": "36454.4", "order_price": "36315.6", "time": "1700267258"}], "parsedResponse": [{"info": {"contract": "BTC_USDT", "left": "0", "size": "33", "fill_price": "36454.4", "order_price": "36309.2", "time": "1700267258"}, "symbol": "BTC/USDT:USDT", "contracts": 33, "contractSize": null, "price": 36454.4, "baseValue": 0.0033, "quoteValue": 120.29952, "timestamp": 1700267258000, "datetime": "2023-11-18T00:27:38.000Z"}, {"info": {"contract": "BTC_USDT", "left": "0", "size": "287", "fill_price": "36454.4", "order_price": "36315.6", "time": "1700267258"}, "symbol": "BTC/USDT:USDT", "contracts": 287, "contractSize": null, "price": 36454.4, "baseValue": 0.0287, "quoteValue": 1046.24128, "timestamp": 1700267258000, "datetime": "2023-11-18T00:27:38.000Z"}]}], "fetchPositions": [{"description": "Fetch positions", "method": "fetchPositions", "input": [["LTC/USDT:USDT"]], "httpResponse": [{"value": "7.443", "leverage": "5", "mode": "single", "realised_point": "0", "contract": "LTC_USDT", "entry_price": "74.35", "mark_price": "74.43", "history_point": "0", "realised_pnl": "-0.0037175", "close_order": null, "size": "1", "cross_leverage_limit": "1", "pending_orders": "0", "adl_ranking": "5", "maintenance_rate": "0.01", "unrealised_pnl": "0.008", "user": "10406147", "leverage_max": "50", "history_pnl": "-5.50521537", "risk_limit": "500000", "margin": "1.49257625", "last_close_pnl": "-5.547508534", "liq_price": "60.07", "update_time": "1705052585", "update_id": "10", "initial_margin": "0", "maintenance_margin": "0", "open_time": "1705052585", "trade_max_size": "0"}], "parsedResponse": [{"info": {"value": "7.443", "leverage": "5", "mode": "single", "realised_point": "0", "contract": "LTC_USDT", "entry_price": "74.35", "mark_price": "74.43", "history_point": "0", "realised_pnl": "-0.0037175", "close_order": null, "size": "1", "cross_leverage_limit": "1", "pending_orders": "0", "adl_ranking": "5", "maintenance_rate": "0.01", "unrealised_pnl": "0.008", "user": "10406147", "leverage_max": "50", "history_pnl": "-5.50521537", "risk_limit": "500000", "margin": "1.49257625", "last_close_pnl": "-5.547508534", "liq_price": "60.07", "update_time": "1705052585", "update_id": "10", "initial_margin": "0", "maintenance_margin": "0", "open_time": "1705052585", "trade_max_size": "0"}, "id": null, "symbol": "LTC/USDT:USDT", "timestamp": 1705052585000, "datetime": "2024-01-12T09:43:05.000Z", "lastUpdateTimestamp": 1705052585000, "initialMargin": 1.49418225, "initialMarginPercentage": 0.20075, "maintenanceMargin": 0.07443, "maintenanceMarginPercentage": 0.01, "entryPrice": 74.35, "notional": 7.443, "leverage": 5, "unrealizedPnl": 0.008, "realizedPnl": -0.0037175, "contracts": 1, "contractSize": 0.1, "marginRatio": null, "liquidationPrice": 60.07, "markPrice": 74.43, "lastPrice": null, "collateral": 1.49257625, "marginMode": "isolated", "side": "long", "percentage": null, "stopLossPrice": null, "takeProfitPrice": null}]}], "fetchMyTrades": [{"description": "Spot trade", "method": "fetchMyTrades", "input": ["LTC/USDT"], "httpResponse": [{"id": "7342180137", "create_time": "**********", "create_time_ms": "**********841.325000", "currency_pair": "LTC_USDT", "side": "buy", "role": "taker", "amount": "0.025", "price": "68.02", "order_id": "************", "fee": "0.000050", "fee_currency": "LTC", "point_fee": "0.0", "gt_fee": "0.0", "amend_text": "-", "sequence_id": "1388786", "text": "apiv4"}, {"id": "7342180136", "create_time": "**********", "create_time_ms": "**********841.313000", "currency_pair": "LTC_USDT", "side": "buy", "role": "taker", "amount": "0.0485", "price": "68.01", "order_id": "************", "fee": "0.000097", "fee_currency": "LTC", "point_fee": "0.0", "gt_fee": "0.0", "amend_text": "-", "sequence_id": "1388785", "text": "apiv4"}], "parsedResponse": [{"info": {"id": "7342180136", "create_time": "**********", "create_time_ms": "**********841.313000", "currency_pair": "LTC_USDT", "side": "buy", "role": "taker", "amount": "0.0485", "price": "68.01", "order_id": "************", "fee": "0.000097", "fee_currency": "LTC", "point_fee": "0.0", "gt_fee": "0.0", "amend_text": "-", "sequence_id": "1388785", "text": "apiv4"}, "id": "7342180136", "timestamp": **********841, "datetime": "2024-01-27T19:04:23.841Z", "symbol": "LTC/USDT", "order": "************", "type": null, "side": "buy", "takerOrMaker": "taker", "price": 68.01, "amount": 0.0485, "cost": 3.298485, "fee": {"cost": 9.7e-05, "currency": "LTC"}, "fees": [{"cost": 9.7e-05, "currency": "LTC"}]}, {"info": {"id": "7342180137", "create_time": "**********", "create_time_ms": "**********841.325000", "currency_pair": "LTC_USDT", "side": "buy", "role": "taker", "amount": "0.025", "price": "68.02", "order_id": "************", "fee": "0.000050", "fee_currency": "LTC", "point_fee": "0.0", "gt_fee": "0.0", "amend_text": "-", "sequence_id": "1388786", "text": "apiv4"}, "id": "7342180137", "timestamp": **********841, "datetime": "2024-01-27T19:04:23.841Z", "symbol": "LTC/USDT", "order": "************", "type": null, "side": "buy", "takerOrMaker": "taker", "price": 68.02, "amount": 0.025, "cost": 1.7005, "fee": {"cost": 5e-05, "currency": "LTC"}, "fees": [{"cost": 5e-05, "currency": "LTC"}]}]}, {"description": "Swap trade", "method": "fetchMyTrades", "input": ["LTC/USDT:USDT", null, 1], "httpResponse": [{"price": "68.42", "text": "api", "fee": "0.003421", "create_time": "1705950365.664", "point_fee": "0", "trade_id": "29768497", "contract": "LTC_USDT", "role": "taker", "order_id": "402052804778", "size": "-1", "biz_info": "ch:ccxt", "amend_text": "ch:ccxt"}], "parsedResponse": [{"info": {"price": "68.42", "text": "api", "fee": "0.003421", "create_time": "1705950365.664", "point_fee": "0", "trade_id": "29768497", "contract": "LTC_USDT", "role": "taker", "order_id": "402052804778", "size": "-1", "biz_info": "ch:ccxt", "amend_text": "ch:ccxt"}, "id": "29768497", "timestamp": 1705950365664, "datetime": "2024-01-22T19:06:05.664Z", "symbol": "LTC/USDT:USDT", "order": "402052804778", "type": null, "side": "sell", "takerOrMaker": "taker", "price": 68.42, "amount": 1, "cost": 6.842, "fee": {"cost": 0.003421, "currency": "USDT"}, "fees": [{"cost": 0.003421, "currency": "USDT"}]}]}], "fetchClosedOrders": [{"description": "spot closed orders", "disabledGO": true, "method": "fetchClosedOrders", "input": ["LTC/USDT", null, 1], "httpResponse": [{"id": "************", "text": "apiv4", "amend_text": "-", "create_time": "**********", "update_time": "**********", "create_time_ms": "**********841", "update_time_ms": "**********841", "status": "closed", "currency_pair": "LTC_USDT", "type": "market", "account": "spot", "side": "buy", "amount": "5.0", "price": "0.0", "time_in_force": "ioc", "iceberg": "0.0", "left": "0.001015", "fill_price": "4.998985", "filled_total": "4.998985", "avg_deal_price": "68.02", "fee": "0.000147", "fee_currency": "LTC", "point_fee": "0.0", "gt_fee": "0.0", "gt_maker_fee": "0.0", "gt_taker_fee": "0.0", "gt_discount": false, "rebated_fee": "0.0", "rebated_fee_currency": "USDT", "finish_as": "filled"}], "parsedResponse": [{"id": "************", "clientOrderId": "apiv4", "timestamp": **********841, "datetime": "2024-01-27T19:04:23.841Z", "lastTradeTimestamp": **********841, "status": "closed", "symbol": "LTC/USDT", "type": "market", "timeInForce": "IOC", "postOnly": false, "reduceOnly": null, "side": "buy", "price": 68.02, "stopPrice": null, "triggerPrice": null, "average": 68.02, "amount": 0.*****************, "cost": 5, "filled": 0.*****************, "remaining": 1.4922081740664e-05, "fee": null, "fees": [{"currency": "GT", "cost": 0}, {"currency": "LTC", "cost": 0.000147}, {"currency": "USDT", "cost": 0}], "trades": [], "info": {"id": "************", "text": "apiv4", "amend_text": "-", "create_time": "**********", "update_time": "**********", "create_time_ms": "**********841", "update_time_ms": "**********841", "status": "closed", "currency_pair": "LTC_USDT", "type": "market", "account": "spot", "side": "buy", "amount": "5.0", "price": "0.0", "time_in_force": "ioc", "iceberg": "0.0", "left": "0.001015", "fill_price": "4.998985", "filled_total": "4.998985", "avg_deal_price": "68.02", "fee": "0.000147", "fee_currency": "LTC", "point_fee": "0.0", "gt_fee": "0.0", "gt_maker_fee": "0.0", "gt_taker_fee": "0.0", "gt_discount": false, "rebated_fee": "0.0", "rebated_fee_currency": "USDT", "finish_as": "filled"}, "lastUpdateTimestamp": null, "takeProfitPrice": null, "stopLossPrice": null}]}, {"description": "spot & trigger", "method": "fetchClosedOrders", "input": ["BTC/USDT", null, null, {"trigger": true}], "httpResponse": [{"market": "BTC_USDT", "user": "3368716", "trigger": {"price": "64444", "rule": "<=", "expiration": "86400"}, "put": {"type": "limit", "side": "buy", "price": "61075.1", "amount": "0.00010000000000000000", "account": "normal", "time_in_force": "gtc", "auto_borrow": false, "auto_repay": false, "text": ""}, "id": "*********", "ctime": "**********", "ftime": "**********", "status": "canceled"}], "parsedResponse": [{"id": "*********", "clientOrderId": null, "timestamp": **********000, "datetime": "2024-10-17T14:42:04.000Z", "lastTradeTimestamp": null, "status": "canceled", "symbol": "BTC/USDT", "type": "limit", "timeInForce": "GTC", "postOnly": false, "reduceOnly": null, "side": "buy", "price": 61075.1, "stopPrice": 64444, "triggerPrice": 64444, "average": null, "amount": 0.0001, "cost": 0, "filled": 0, "remaining": 0.0001, "fee": null, "fees": [], "trades": [], "info": {"market": "BTC_USDT", "user": "3368716", "trigger": {"price": "64444", "rule": "<=", "expiration": "86400"}, "put": {"type": "limit", "side": "buy", "price": "61075.1", "amount": "0.00010000000000000000", "account": "normal", "time_in_force": "gtc", "auto_borrow": false, "auto_repay": false, "text": ""}, "id": "*********", "ctime": "**********", "ftime": "**********", "status": "canceled"}, "lastUpdateTimestamp": null, "takeProfitPrice": null, "stopLossPrice": null}]}, {"description": "swap & trigger", "method": "fetchClosedOrders", "input": ["BTC/USDT:USDT", null, null, {"trigger": true}], "httpResponse": [{"user": "3368716", "trigger": {"strategy_type": "0", "price_type": "0", "price": "67203.5", "rule": "2", "expiration": "0"}, "initial": {"contract": "BTC_USDT", "size": "5", "price": "67203.5", "tif": "gtc", "text": "web", "iceberg": "0", "is_close": false, "is_reduce_only": false, "auto_size": "", "default_leverage": "10"}, "id": "********", "id_string": "********", "trade_id": "************", "trade_id_string": "************", "status": "finished", "finish_as": "succeeded", "reason": "", "create_time": "**********", "finish_time": "**********", "is_stop_order": false, "stop_trigger": {"rule": "0", "trigger_price": "", "order_price": ""}, "me_order_id": "0", "me_order_id_string": "0", "order_type": "", "in_dual_mode": false, "parent_id": "0", "parent_id_string": "0", "stop_profit_price": "", "stop_loss_price": "", "batch_id": ""}], "parsedResponse": [{"id": "********", "clientOrderId": null, "timestamp": **********000, "datetime": "2024-10-21T16:50:06.000Z", "lastTradeTimestamp": **********000, "status": "closed", "symbol": "BTC/USDT:USDT", "type": "limit", "timeInForce": "GTC", "postOnly": false, "reduceOnly": null, "side": "buy", "price": 67203.5, "stopPrice": 67203.5, "triggerPrice": 67203.5, "average": null, "amount": 5, "cost": 0, "filled": 0, "remaining": 5, "fee": null, "fees": [], "trades": [], "info": {"user": "3368716", "trigger": {"strategy_type": "0", "price_type": "0", "price": "67203.5", "rule": "2", "expiration": "0"}, "initial": {"contract": "BTC_USDT", "size": "5", "price": "67203.5", "tif": "gtc", "text": "web", "iceberg": "0", "is_close": false, "is_reduce_only": false, "auto_size": "", "default_leverage": "10"}, "id": "********", "id_string": "********", "trade_id": "************", "trade_id_string": "************", "status": "finished", "finish_as": "succeeded", "reason": "", "create_time": "**********", "finish_time": "**********", "is_stop_order": false, "stop_trigger": {"rule": "0", "trigger_price": "", "order_price": ""}, "me_order_id": "0", "me_order_id_string": "0", "order_type": "", "in_dual_mode": false, "parent_id": "0", "parent_id_string": "0", "stop_profit_price": "", "stop_loss_price": "", "batch_id": ""}, "lastUpdateTimestamp": null, "takeProfitPrice": null, "stopLossPrice": null}]}, {"description": "closed swap orders", "method": "fetchClosedOrders", "input": ["LTC/USDT:USDT", null, 1], "httpResponse": [{"status": "finished", "size": "-1", "left": "0", "id": "402052804778", "is_liq": false, "is_close": false, "contract": "LTC_USDT", "text": "api", "fill_price": "68.42", "finish_as": "filled", "iceberg": "0", "tif": "ioc", "is_reduce_only": false, "create_time": "1705950365.663", "finish_time": "1705950365.664", "price": "0", "biz_info": "ch:ccxt", "amend_text": "-", "stp_act": "-", "stp_id": "0"}], "parsedResponse": [{"id": "402052804778", "clientOrderId": "api", "timestamp": 1705950365663, "datetime": "2024-01-22T19:06:05.663Z", "lastTradeTimestamp": 1705950365664, "status": "closed", "symbol": "LTC/USDT:USDT", "type": "market", "timeInForce": "IOC", "postOnly": false, "reduceOnly": false, "side": "sell", "price": 68.42, "stopPrice": null, "triggerPrice": null, "average": 68.42, "amount": 1, "cost": 6.842, "filled": 1, "remaining": 0, "fee": null, "fees": [], "trades": [], "info": {"status": "finished", "size": "-1", "left": "0", "id": "402052804778", "is_liq": false, "is_close": false, "contract": "LTC_USDT", "text": "api", "fill_price": "68.42", "finish_as": "filled", "iceberg": "0", "tif": "ioc", "is_reduce_only": false, "create_time": "1705950365.663", "finish_time": "1705950365.664", "price": "0", "biz_info": "ch:ccxt", "amend_text": "-", "stp_act": "-", "stp_id": "0"}, "lastUpdateTimestamp": null, "takeProfitPrice": null, "stopLossPrice": null}]}], "fetchTrades": [{"description": "public spot trades", "method": "fetchTrades", "input": ["BTC/USDT", null, 1], "httpResponse": [{"id": "7835390042", "create_time": "1710327648", "create_time_ms": "1710327648162.035000", "currency_pair": "BTC_USDT", "side": "buy", "amount": "0.00020", "price": "73236.4", "sequence_id": "29014746"}], "parsedResponse": [{"info": {"id": "7835390042", "create_time": "1710327648", "create_time_ms": "1710327648162.035000", "currency_pair": "BTC_USDT", "side": "buy", "amount": "0.00020", "price": "73236.4", "sequence_id": "29014746"}, "id": "7835390042", "timestamp": 1710327648162, "datetime": "2024-03-13T11:00:48.162Z", "symbol": "BTC/USDT", "order": null, "type": null, "side": "buy", "takerOrMaker": null, "price": 73236.4, "amount": 0.0002, "cost": 14.64728, "fee": {"cost": null, "currency": null}, "fees": []}]}], "fetchTicker": [{"description": "public spot ticker", "method": "fetchTicker", "input": ["BTC/USDT"], "httpResponse": [{"currency_pair": "BTC_USDT", "last": "73373.9", "lowest_ask": "73364.2", "highest_bid": "73364.1", "change_percentage": "1.71", "base_volume": "3410.0783503636", "quote_volume": "243764368.46418", "high_24h": "73636.3", "low_24h": "68629.9"}], "parsedResponse": {"symbol": "BTC/USDT", "timestamp": null, "datetime": null, "high": 73636.3, "low": 68629.9, "bid": 73364.1, "bidVolume": null, "ask": 73364.2, "askVolume": null, "vwap": 71483.5095909713, "open": 72140.30085537312, "close": 73373.9, "last": 73373.9, "previousClose": null, "change": 1233.5991446268804, "percentage": 1.71, "average": 72757.1, "baseVolume": 3410.0783503636, "quoteVolume": 243764368.46418, "markPrice": null, "indexPrice": null, "info": {"currency_pair": "BTC_USDT", "last": "73373.9", "lowest_ask": "73364.2", "highest_bid": "73364.1", "change_percentage": "1.71", "base_volume": "3410.0783503636", "quote_volume": "243764368.46418", "high_24h": "73636.3", "low_24h": "68629.9"}}}], "fetchOHLCV": [{"description": "public spot ohlcv", "method": "fetchOHLCV", "input": ["BTC/USDT", "1h", null, 1], "httpResponse": [["1710327600", "992134.61604178", "73408.9", "73435.8", "73236.3", "73236.9", "13.********", "false"]], "parsedResponse": [[*************, 73236.9, 73435.8, 73236.3, 73408.9, 13.********]]}], "fetchOpenOrders": [{"description": "default", "method": "fetchOpenOrders", "input": ["BTC/USDT"], "httpResponse": [{"currency_pair": "BTC_USDT", "total": "1", "orders": [{"id": "************", "text": "web", "amend_text": "-", "create_time": "**********", "update_time": "**********", "create_time_ms": "**********718", "update_time_ms": "**********718", "status": "open", "currency_pair": "BTC_USDT", "type": "limit", "account": "spot", "side": "sell", "amount": "0.00009", "price": "69662.3", "time_in_force": "gtc", "iceberg": "0", "left": "0.00009", "filled_amount": "0", "fill_price": "0", "filled_total": "0", "fee": "0", "fee_currency": "USDT", "point_fee": "0", "gt_fee": "0", "gt_maker_fee": "0", "gt_taker_fee": "0", "gt_discount": false, "rebated_fee": "0", "rebated_fee_currency": "BTC", "finish_as": "open"}]}], "parsedResponse": [{"id": "************", "clientOrderId": "web", "timestamp": **********718, "datetime": "2024-10-17T15:52:16.718Z", "lastTradeTimestamp": **********718, "status": "open", "symbol": "BTC/USDT", "type": "limit", "timeInForce": "GTC", "postOnly": false, "reduceOnly": null, "side": "sell", "price": 69662.3, "stopPrice": null, "triggerPrice": null, "average": null, "amount": 9e-05, "cost": 0, "filled": 0, "remaining": 9e-05, "fee": null, "fees": [{"currency": "GT", "cost": 0}, {"currency": "USDT", "cost": 0}, {"currency": "BTC", "cost": 0}], "trades": [], "info": {"id": "************", "text": "web", "amend_text": "-", "create_time": "**********", "update_time": "**********", "create_time_ms": "**********718", "update_time_ms": "**********718", "status": "open", "currency_pair": "BTC_USDT", "type": "limit", "account": "spot", "side": "sell", "amount": "0.00009", "price": "69662.3", "time_in_force": "gtc", "iceberg": "0", "left": "0.00009", "filled_amount": "0", "fill_price": "0", "filled_total": "0", "fee": "0", "fee_currency": "USDT", "point_fee": "0", "gt_fee": "0", "gt_maker_fee": "0", "gt_taker_fee": "0", "gt_discount": false, "rebated_fee": "0", "rebated_fee_currency": "BTC", "finish_as": "open"}, "lastUpdateTimestamp": null, "takeProfitPrice": null, "stopLossPrice": null}]}, {"description": "trigger orders", "method": "fetchOpenOrders", "input": ["BTC/USDT", null, null, {"trigger": true, "type": "spot"}], "httpResponse": [{"market": "BTC_USDT", "user": "3368716", "trigger": {"price": "64777", "rule": "<=", "expiration": "86400"}, "put": {"type": "limit", "side": "sell", "price": "67736.3", "amount": "0.00009000000000000000", "account": "normal", "time_in_force": "gtc", "auto_borrow": false, "auto_repay": false, "text": ""}, "id": "*********", "ctime": "**********", "status": "open"}], "parsedResponse": [{"id": "*********", "clientOrderId": null, "timestamp": **********000, "datetime": "2024-10-17T15:47:55.000Z", "lastTradeTimestamp": null, "status": "open", "symbol": "BTC/USDT", "type": "limit", "timeInForce": "GTC", "postOnly": false, "reduceOnly": null, "side": "sell", "price": 67736.3, "stopPrice": 64777, "triggerPrice": 64777, "average": null, "amount": 9e-05, "cost": 0, "filled": 0, "remaining": 9e-05, "fee": null, "fees": [], "trades": [], "info": {"market": "BTC_USDT", "user": "3368716", "trigger": {"price": "64777", "rule": "<=", "expiration": "86400"}, "put": {"type": "limit", "side": "sell", "price": "67736.3", "amount": "0.00009000000000000000", "account": "normal", "time_in_force": "gtc", "auto_borrow": false, "auto_repay": false, "text": ""}, "id": "*********", "ctime": "**********", "status": "open"}, "lastUpdateTimestamp": null, "takeProfitPrice": null, "stopLossPrice": null}]}, {"description": "swap & trigger", "method": "fetchOpenOrders", "input": ["BTC/USDT:USDT", null, null, {"trigger": true, "type": "swap"}], "httpResponse": [{"user": "3368716", "trigger": {"strategy_type": "0", "price_type": "0", "price": "67203.5", "rule": "2", "expiration": "0"}, "initial": {"contract": "BTC_USDT", "size": "5", "price": "67203.5", "tif": "gtc", "text": "web", "iceberg": "0", "is_close": false, "is_reduce_only": false, "auto_size": ""}, "id": "********", "id_string": "********", "trade_id": "0", "trade_id_string": "0", "status": "open", "reason": "", "create_time": "**********", "finish_time": "**********", "is_stop_order": false, "stop_trigger": {"rule": "0", "trigger_price": "", "order_price": ""}, "me_order_id": "0", "me_order_id_string": "0", "order_type": "", "in_dual_mode": false, "parent_id": "0", "parent_id_string": "0", "stop_profit_price": "", "stop_loss_price": "", "batch_id": ""}], "parsedResponse": [{"id": "********", "clientOrderId": null, "timestamp": **********000, "datetime": "2024-10-21T16:57:02.000Z", "lastTradeTimestamp": **********000, "status": "open", "symbol": "BTC/USDT:USDT", "type": "limit", "timeInForce": "GTC", "postOnly": false, "reduceOnly": null, "side": "buy", "price": 67203.5, "stopPrice": 67203.5, "triggerPrice": 67203.5, "average": null, "amount": 5, "cost": 0, "filled": 0, "remaining": 5, "fee": null, "fees": [], "trades": [], "info": {"user": "3368716", "trigger": {"strategy_type": "0", "price_type": "0", "price": "67203.5", "rule": "2", "expiration": "0"}, "initial": {"contract": "BTC_USDT", "size": "5", "price": "67203.5", "tif": "gtc", "text": "web", "iceberg": "0", "is_close": false, "is_reduce_only": false, "auto_size": ""}, "id": "********", "id_string": "********", "trade_id": "0", "trade_id_string": "0", "status": "open", "reason": "", "create_time": "**********", "finish_time": "**********", "is_stop_order": false, "stop_trigger": {"rule": "0", "trigger_price": "", "order_price": ""}, "me_order_id": "0", "me_order_id_string": "0", "order_type": "", "in_dual_mode": false, "parent_id": "0", "parent_id_string": "0", "stop_profit_price": "", "stop_loss_price": "", "batch_id": ""}, "lastUpdateTimestamp": null, "takeProfitPrice": null, "stopLossPrice": null}]}], "fetchTime": [{"description": "fetch time response", "method": "fetchTime", "input": [], "httpResponse": {"server_time": "1731448079476"}, "parsedResponse": 1731448079476}], "fetchFundingRates": [{"description": "Fetch inverse market funding rate", "method": "fetchFundingRates", "input": [["BTC/USD:BTC"]], "httpResponse": [{"funding_rate_indicative": "0.0001", "mark_price_round": "0.01", "funding_offset": 0, "in_delisting": false, "risk_limit_base": "100", "interest_rate": "0.0003", "index_price": "89025.82000000001", "order_price_round": "0.1", "order_size_min": 1, "ref_rebate_rate": "0.2", "name": "BTC_USD", "ref_discount_rate": "0", "order_price_deviate": "0.5", "maintenance_rate": "0.005", "mark_type": "index", "funding_interval": 28800, "type": "inverse", "risk_limit_step": "100", "enable_bonus": false, "enable_credit": true, "leverage_min": "1", "funding_rate": "0.0001", "last_price": "88811.7", "mark_price": "89029.86", "order_size_max": 1000000, "funding_next_apply": 1741363200, "short_users": 207, "config_change_time": 1732783287, "create_time": 1545235200, "trade_size": 59939537641, "position_size": 11835253, "long_users": 486, "quanto_multiplier": "0", "funding_impact_value": "0.5", "leverage_max": "100", "cross_leverage_default": "10", "risk_limit_max": "800", "maker_fee_rate": "-0.0002", "taker_fee_rate": "0.00075", "orders_limit": 50, "trade_id": 46136002, "orderbook_id": 4871277879, "funding_cap_ratio": "0.75", "voucher_leverage": "0", "is_pre_market": false}], "parsedResponse": {"BTC/USD:BTC": {"info": {"funding_rate_indicative": "0.0001", "mark_price_round": "0.01", "funding_offset": 0, "in_delisting": false, "risk_limit_base": "100", "interest_rate": "0.0003", "index_price": "89025.82000000001", "order_price_round": "0.1", "order_size_min": 1, "ref_rebate_rate": "0.2", "name": "BTC_USD", "ref_discount_rate": "0", "order_price_deviate": "0.5", "maintenance_rate": "0.005", "mark_type": "index", "funding_interval": 28800, "type": "inverse", "risk_limit_step": "100", "enable_bonus": false, "enable_credit": true, "leverage_min": "1", "funding_rate": "0.0001", "last_price": "88811.7", "mark_price": "89029.86", "order_size_max": 1000000, "funding_next_apply": 1741363200, "short_users": 207, "config_change_time": 1732783287, "create_time": 1545235200, "trade_size": 59939537641, "position_size": 11835253, "long_users": 486, "quanto_multiplier": "0", "funding_impact_value": "0.5", "leverage_max": "100", "cross_leverage_default": "10", "risk_limit_max": "800", "maker_fee_rate": "-0.0002", "taker_fee_rate": "0.00075", "orders_limit": 50, "trade_id": 46136002, "orderbook_id": 4871277879, "funding_cap_ratio": "0.75", "voucher_leverage": "0", "is_pre_market": false}, "symbol": "BTC/USD:BTC", "markPrice": 89029.86, "indexPrice": 89025.82, "interestRate": 0.0003, "estimatedSettlePrice": null, "timestamp": null, "datetime": null, "fundingRate": 0.0001, "fundingTimestamp": 1741363200000, "fundingDatetime": "2025-03-07T16:00:00.000Z", "nextFundingRate": 0.0001, "nextFundingTimestamp": null, "nextFundingDatetime": null, "previousFundingRate": null, "previousFundingTimestamp": null, "previousFundingDatetime": null, "interval": "8h"}}}]}}