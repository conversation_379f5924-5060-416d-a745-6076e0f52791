{"exchange": "woo", "skipKeys": [], "options": {}, "methods": {"fetchStatus": [{"description": "fetchStatus", "method": "fetchStatus", "input": [], "httpResponse": {"success": true, "data": {"status": 0, "msg": "System is functioning properly.", "estimatedEndTime": 1749963600362}, "timestamp": 1751442989564}, "parsedResponse": {"status": "ok", "updated": null, "eta": null, "url": null, "info": {"success": true, "data": {"status": 0, "msg": "System is functioning properly.", "estimatedEndTime": 1749963600362}, "timestamp": 1751442989564}}}], "fetchTime": [{"description": "fetchTime", "method": "fetchTime", "input": [], "httpResponse": {"success": true, "data": {"status": 0, "msg": "System is functioning properly.", "estimatedEndTime": 1749963600362}, "timestamp": 1751442989564}, "parsedResponse": 1751442989564}], "fetchMarkets": [{"description": "fetchMarkets", "method": "fetchMarkets", "input": [], "httpResponse": {"success": true, "data": {"rows": [{"symbol": "SPOT_AAVE_USDT", "status": "TRADING", "baseAsset": "AAVE", "baseAssetMultiplier": 1, "quoteAsset": "USDT", "quoteMin": "0", "quoteMax": "100000", "quoteTick": "0.01", "baseMin": "0.005", "baseMax": "5000", "baseTick": "0.0001", "minNotional": "1", "bidCapRatio": "1.1", "bidFloorRatio": null, "askCapRatio": null, "askFloorRatio": "0.9", "orderMode": "NORMAL", "impactNotional": null, "isAllowedRpi": false, "tickGranularity": null}]}, "timestamp": 1751512951338}, "parsedResponse": [{"id": "SPOT_AAVE_USDT", "symbol": "AAVE/USDT", "base": "AAVE", "quote": "USDT", "settle": null, "baseId": "AAVE", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": true, "swap": false, "future": false, "option": false, "active": true, "contract": false, "linear": null, "inverse": null, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.0001, "price": 0.01}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 0.005, "max": 5000}, "price": {"min": 0, "max": 100000}, "cost": {"min": 1, "max": null}}, "created": null, "info": {"symbol": "SPOT_AAVE_USDT", "status": "TRADING", "baseAsset": "AAVE", "baseAssetMultiplier": 1, "quoteAsset": "USDT", "quoteMin": "0", "quoteMax": "100000", "quoteTick": "0.01", "baseMin": "0.005", "baseMax": "5000", "baseTick": "0.0001", "minNotional": "1", "bidCapRatio": "1.1", "bidFloorRatio": null, "askCapRatio": null, "askFloorRatio": "0.9", "orderMode": "NORMAL", "impactNotional": null, "isAllowedRpi": false, "tickGranularity": null}}]}], "withdraw": [{"description": "withdraw USDT on ERC20", "method": "withdraw", "input": ["USDT", 10, "******************************************", null, {"network": "ETH"}], "httpResponse": {"withdraw_id": "25052918110800327", "success": true}, "parsedResponse": {"info": {"withdraw_id": "25052918110800327", "success": true}, "id": "25052918110800327", "txid": null, "timestamp": null, "datetime": null, "address": null, "addressFrom": null, "addressTo": null, "tag": null, "tagFrom": null, "tagTo": null, "type": null, "amount": null, "currency": "USDT", "status": null, "updated": null, "comment": null, "internal": null, "fee": null, "network": null}}, {"description": "withdraw USDT on ETH", "method": "withdraw", "input": ["USDT", 10, "******************************************", null, {"network": "ETH"}], "httpResponse": {"withdraw_id": "25052917354800318", "success": true}, "parsedResponse": {"info": {"withdraw_id": "25052917354800318", "success": true}, "id": "25052917354800318", "txid": null, "timestamp": null, "datetime": null, "address": null, "addressFrom": null, "addressTo": null, "tag": null, "tagFrom": null, "tagTo": null, "type": null, "amount": null, "currency": "USDT", "status": null, "updated": null, "comment": null, "internal": null, "fee": null, "network": null}}], "fetchDepositAddress": [{"description": "fetchDepositAddress USDT on ETH", "method": "fetchDepositAddress", "input": ["USDT", {"network": "ETH"}], "httpResponse": {"success": true, "data": {"address": "******************************************", "extra": ""}, "timestamp": 1721300689532}, "parsedResponse": {"info": {"address": "******************************************", "extra": ""}, "currency": "USDT", "network": null, "address": "******************************************", "tag": null}}], "fetchMyTrades": [{"description": "fetchMyTrades", "method": "fetchMyTrades", "input": ["LTC/USDT", null, 1], "httpResponse": {"success": true, "data": {"rows": [{"id": 1734947821, "symbol": "SPOT_LTC_USDT", "orderId": 60780383217, "executedPrice": 87.86, "executedQuantity": 0.1, "fee": 0.0001, "realizedPnl": null, "feeAsset": "LTC", "orderTag": "default", "side": "BUY", "executedTimestamp": "1752055173.630", "isMaker": 0}], "meta": {"total": 1, "recordsPerPage": 100, "currentPage": 1}}, "timestamp": 1752055545121}, "parsedResponse": [{"id": "1734947821", "timestamp": 1752055173630, "datetime": "2025-07-09T09:59:33.630Z", "symbol": "LTC/USDT", "side": "buy", "price": 87.86, "amount": 0.1, "cost": 8.786, "order": "60780383217", "takerOrMaker": "taker", "type": null, "fee": {"currency": "LTC", "cost": 0.0001}, "info": {"id": 1734947821, "symbol": "SPOT_LTC_USDT", "orderId": 60780383217, "executedPrice": 87.86, "executedQuantity": 0.1, "fee": 0.0001, "realizedPnl": null, "feeAsset": "LTC", "orderTag": "default", "side": "BUY", "executedTimestamp": "1752055173.630", "isMaker": 0}, "fees": [{"currency": "LTC", "cost": 0.0001}]}]}], "createOrder": [{"description": "create order", "method": "createOrder", "input": ["LTC/USDT:USDT", "limit", "buy", 0.1, 60], "httpResponse": {"success": true, "data": {"orderId": 60667653330, "clientOrderId": 0, "type": "LIMIT", "price": 60, "quantity": 0.1, "amount": null, "bidAskLevel": null}, "timestamp": 1751871779855}, "parsedResponse": {"id": "60667653330", "clientOrderId": null, "timestamp": 1751871779855, "datetime": "2025-07-07T07:02:59.855Z", "lastTradeTimestamp": null, "lastUpdateTimestamp": null, "status": null, "symbol": "LTC/USDT:USDT", "type": "limit", "timeInForce": null, "postOnly": null, "reduceOnly": null, "side": null, "price": 60, "triggerPrice": null, "takeProfitPrice": null, "stopLossPrice": null, "average": null, "amount": 0.1, "filled": null, "remaining": null, "cost": null, "trades": [], "fee": {"cost": null, "currency": null}, "info": {"orderId": 60667653330, "clientOrderId": 0, "type": "LIMIT", "price": 60, "quantity": 0.1, "amount": null, "bidAskLevel": null, "timestamp": "1751871779855"}, "fees": [{"cost": null, "currency": null}], "stopPrice": null}}, {"description": "create algo order", "method": "createOrder", "input": ["LTC/USDT:USDT", "limit", "buy", 0.1, 60, {"triggerPrice": 65}], "httpResponse": {"success": true, "data": {"rows": [{"algoType": "TAKE_PROFIT", "quantity": 0.1, "algoOrderId": 10361896, "clientAlgoOrderId": 0}]}, "timestamp": 1751872246324}, "parsedResponse": {"id": "10361896", "clientOrderId": null, "timestamp": 1751872246324, "datetime": "2025-07-07T07:10:46.324Z", "lastTradeTimestamp": null, "lastUpdateTimestamp": null, "status": null, "symbol": "LTC/USDT:USDT", "type": null, "timeInForce": null, "postOnly": null, "reduceOnly": null, "side": null, "price": null, "triggerPrice": null, "takeProfitPrice": null, "stopLossPrice": null, "average": null, "amount": 0.1, "filled": null, "remaining": null, "cost": null, "trades": [], "fee": {"cost": null, "currency": null}, "info": {"algoType": "TAKE_PROFIT", "quantity": 0.1, "algoOrderId": 10361896, "clientAlgoOrderId": 0, "timestamp": "1751872246324"}, "fees": [{"cost": null, "currency": null}], "stopPrice": null}}], "fetchPosition": [{"description": "Linear swap position", "method": "fetchPosition", "input": ["LTC/USDT:USDT"], "httpResponse": {"success": true, "data": {"positions": [{"symbol": "PERP_LTC_USDT", "holding": "0.1", "pendingLongQty": "0", "pendingShortQty": "0", "settlePrice": "96.87", "averageOpenPrice": "96.87", "pnl24H": "0", "fee24H": "0.0048435", "markPrice": "96.83793449", "estLiqPrice": "0", "timestamp": 1752500555823, "adlQuantile": 2, "positionSide": "BOTH", "marginMode": "CROSS", "isolatedMarginToken": "", "isolatedMarginAmount": "0", "isolatedFrozenLong": "0", "isolatedFrozenShort": "0", "leverage": 10}]}, "timestamp": 1752500579848}, "parsedResponse": {"info": {"symbol": "PERP_LTC_USDT", "holding": "0.1", "pendingLongQty": "0", "pendingShortQty": "0", "settlePrice": "96.87", "averageOpenPrice": "96.87", "pnl24H": "0", "fee24H": "0.0048435", "markPrice": "96.83793449", "estLiqPrice": "0", "timestamp": 1752500555823, "adlQuantile": 2, "positionSide": "BOTH", "marginMode": "CROSS", "isolatedMarginToken": "", "isolatedMarginAmount": "0", "isolatedFrozenLong": "0", "isolatedFrozenShort": "0", "leverage": 10}, "id": null, "symbol": "LTC/USDT:USDT", "timestamp": 1752500555823, "datetime": "2025-07-14T13:42:35.823Z", "lastUpdateTimestamp": null, "initialMargin": null, "initialMarginPercentage": null, "maintenanceMargin": null, "maintenanceMarginPercentage": null, "entryPrice": 96.87, "notional": 9.683793449, "leverage": 10, "unrealizedPnl": -0.003206551, "contracts": 0.1, "contractSize": 1, "marginRatio": null, "liquidationPrice": 0, "markPrice": 96.83793449, "lastPrice": null, "collateral": null, "marginMode": "cross", "side": "long", "percentage": null, "hedged": false, "stopLossPrice": null, "takeProfitPrice": null}}], "fetchPositions": [{"description": "Linear swap position", "method": "fetchPositions", "input": [], "httpResponse": {"success": true, "data": {"positions": [{"symbol": "PERP_LTC_USDT", "holding": "0.1", "pendingLongQty": "0", "pendingShortQty": "0", "settlePrice": "96.87", "averageOpenPrice": "96.87", "pnl24H": "0", "fee24H": "0.0048435", "markPrice": "96.83793449", "estLiqPrice": "0", "timestamp": 1752500555823, "adlQuantile": 2, "positionSide": "BOTH", "marginMode": "CROSS", "isolatedMarginToken": "", "isolatedMarginAmount": "0", "isolatedFrozenLong": "0", "isolatedFrozenShort": "0", "leverage": 10}]}, "timestamp": 1752500579848}, "parsedResponse": [{"info": {"symbol": "PERP_LTC_USDT", "holding": "0.1", "pendingLongQty": "0", "pendingShortQty": "0", "settlePrice": "96.87", "averageOpenPrice": "96.87", "pnl24H": "0", "fee24H": "0.0048435", "markPrice": "96.83793449", "estLiqPrice": "0", "timestamp": 1752500555823, "adlQuantile": 2, "positionSide": "BOTH", "marginMode": "CROSS", "isolatedMarginToken": "", "isolatedMarginAmount": "0", "isolatedFrozenLong": "0", "isolatedFrozenShort": "0", "leverage": 10}, "id": null, "symbol": "LTC/USDT:USDT", "timestamp": 1752500555823, "datetime": "2025-07-14T13:42:35.823Z", "lastUpdateTimestamp": null, "initialMargin": null, "initialMarginPercentage": null, "maintenanceMargin": null, "maintenanceMarginPercentage": null, "entryPrice": 96.87, "notional": 9.683793449, "leverage": 10, "unrealizedPnl": -0.003206551, "contracts": 0.1, "contractSize": 1, "marginRatio": null, "liquidationPrice": 0, "markPrice": 96.83793449, "lastPrice": null, "collateral": null, "marginMode": "cross", "side": "long", "percentage": null, "hedged": false, "stopLossPrice": null, "takeProfitPrice": null}]}], "fetchTrades": [{"description": "public spot trades", "method": "fetchTrades", "input": ["BTC/USDT", null, 1], "httpResponse": {"success": true, "data": {"rows": [{"symbol": "SPOT_BTC_USDT", "side": "SELL", "source": 0, "executedPrice": "108741.01", "executedQuantity": "0.02477", "executedTimestamp": 1751513940144}]}, "timestamp": 1751513988543}, "parsedResponse": [{"id": null, "timestamp": 1751513940144, "datetime": "2025-07-03T03:39:00.144Z", "symbol": "BTC/USDT", "side": "sell", "price": 108741.01, "amount": 0.02477, "cost": 2693.5148177, "order": null, "takerOrMaker": null, "type": null, "fee": {"cost": null, "currency": null}, "info": {"symbol": "SPOT_BTC_USDT", "side": "SELL", "source": 0, "executedPrice": "108741.01", "executedQuantity": "0.02477", "executedTimestamp": 1751513940144}, "fees": []}]}], "fetchOHLCV": [{"description": "public spot ohlcv", "method": "fetchOHLCV", "input": ["BTC/USDT", "1h", null, 1], "httpResponse": {"success": true, "data": {"rows": [{"symbol": "SPOT_BTC_USDT", "open": "108754.44", "close": "108995.4", "high": "109068.24", "low": "108754.44", "volume": "19.302362", "amount": "2103316.08159279", "type": "1h", "startTimestamp": 1751619600000, "endTimestamp": 1751623200000}]}, "timestamp": 1751623204602}, "parsedResponse": [[1751619600000, 108754.44, 109068.24, 108754.44, 108995.4, 19.302362]]}], "fetchClosedOrders": [{"description": "fetchOpenOrders", "method": "fetchOpenOrders", "input": ["LTC/USDT", null, 1], "httpResponse": {"success": true, "data": {"rows": [{"orderId": 60780315704, "clientOrderId": 0, "symbol": "SPOT_LTC_USDT", "orderTag": "default", "side": "BUY", "quantity": 0.1, "amount": null, "type": "LIMIT", "status": "NEW", "price": 60, "executed": 0, "visible": 0.1, "averageExecutedPrice": 0, "totalFee": 0, "feeAsset": "LTC", "totalRebate": 0, "rebateAsset": "USDT", "reduceOnly": false, "createdTime": "1752049062.496", "realizedPnl": null, "positionSide": "BOTH", "bidAskLevel": null}], "meta": {"total": 11, "recordsPerPage": 1, "currentPage": 1}}, "timestamp": 1752053575184}, "parsedResponse": [{"id": "60780315704", "clientOrderId": null, "timestamp": 1752049062496, "datetime": "2025-07-09T08:17:42.496Z", "lastTradeTimestamp": null, "lastUpdateTimestamp": null, "status": "open", "symbol": "LTC/USDT", "type": "limit", "timeInForce": null, "postOnly": null, "reduceOnly": false, "side": "buy", "price": 60, "triggerPrice": null, "takeProfitPrice": null, "stopLossPrice": null, "average": null, "amount": 0.1, "filled": null, "remaining": null, "cost": null, "trades": [], "fee": {"cost": 0, "currency": "LTC"}, "info": {"orderId": 60780315704, "clientOrderId": 0, "symbol": "SPOT_LTC_USDT", "orderTag": "default", "side": "BUY", "quantity": 0.1, "amount": null, "type": "LIMIT", "status": "NEW", "price": 60, "executed": 0, "visible": 0.1, "averageExecutedPrice": 0, "totalFee": 0, "feeAsset": "LTC", "totalRebate": 0, "rebateAsset": "USDT", "reduceOnly": false, "createdTime": "1752049062.496", "realizedPnl": null, "positionSide": "BOTH", "bidAskLevel": null}, "fees": [{"cost": 0, "currency": "LTC"}], "stopPrice": null}]}, {"description": "fetchOrders algo", "method": "fetchOrders", "input": ["LTC/USDT", null, 1, {"stop": true}], "httpResponse": {"success": true, "data": {"rows": [{"algoOrderId": 10399260, "clientAlgoOrderId": 0, "rootAlgoOrderId": 10399260, "parentAlgoOrderId": 0, "symbol": "SPOT_LTC_USDT", "algoOrderTag": "default", "algoType": "TAKE_PROFIT", "side": "BUY", "quantity": 0.1, "isTriggered": false, "triggerPrice": 65, "triggerStatus": "USELESS", "type": "LIMIT", "rootAlgoStatus": "NEW", "algoStatus": "NEW", "triggerPriceType": "MARKET_PRICE", "price": 60, "triggerTime": "0", "totalExecutedQuantity": 0, "visibleQuantity": 0.1, "averageExecutedPrice": 0, "totalFee": 0, "feeAsset": "", "totalRebate": 0, "rebateAsset": "", "reduceOnly": false, "createdTime": "1752049747.730", "updatedTime": "1752049747.730", "positionSide": "BOTH"}], "meta": {"total": 7, "recordsPerPage": 1, "currentPage": 1}}, "timestamp": 1752053592050}, "parsedResponse": [{"id": "10399260", "clientOrderId": null, "timestamp": 1752049747730, "datetime": "2025-07-09T08:29:07.730Z", "lastTradeTimestamp": null, "lastUpdateTimestamp": 1752049747730, "status": "open", "symbol": "LTC/USDT", "type": "limit", "timeInForce": null, "postOnly": null, "reduceOnly": false, "side": "buy", "price": 60, "triggerPrice": 65, "takeProfitPrice": null, "stopLossPrice": null, "average": null, "amount": 0.1, "filled": null, "remaining": null, "cost": null, "trades": [], "fee": {"cost": 0, "currency": null}, "info": {"algoOrderId": 10399260, "clientAlgoOrderId": 0, "rootAlgoOrderId": 10399260, "parentAlgoOrderId": 0, "symbol": "SPOT_LTC_USDT", "algoOrderTag": "default", "algoType": "TAKE_PROFIT", "side": "BUY", "quantity": 0.1, "isTriggered": false, "triggerPrice": 65, "triggerStatus": "USELESS", "type": "LIMIT", "rootAlgoStatus": "NEW", "algoStatus": "NEW", "triggerPriceType": "MARKET_PRICE", "price": 60, "triggerTime": "0", "totalExecutedQuantity": 0, "visibleQuantity": 0.1, "averageExecutedPrice": 0, "totalFee": 0, "feeAsset": "", "totalRebate": 0, "rebateAsset": "", "reduceOnly": false, "createdTime": "1752049747.730", "updatedTime": "1752049747.730", "positionSide": "BOTH"}, "fees": [{"cost": 0, "currency": null}], "stopPrice": 65}]}], "fetchOpenOrders": [{"description": "fetchOpenOrders", "method": "fetchOpenOrders", "input": ["LTC/USDT", null, 1], "httpResponse": {"success": true, "data": {"rows": [{"orderId": 60780315704, "clientOrderId": 0, "symbol": "SPOT_LTC_USDT", "orderTag": "default", "side": "BUY", "quantity": 0.1, "amount": null, "type": "LIMIT", "status": "NEW", "price": 60, "executed": 0, "visible": 0.1, "averageExecutedPrice": 0, "totalFee": 0, "feeAsset": "LTC", "totalRebate": 0, "rebateAsset": "USDT", "reduceOnly": false, "createdTime": "1752049062.496", "realizedPnl": null, "positionSide": "BOTH", "bidAskLevel": null}], "meta": {"total": 11, "recordsPerPage": 1, "currentPage": 1}}, "timestamp": 1752053575184}, "parsedResponse": [{"id": "60780315704", "clientOrderId": null, "timestamp": 1752049062496, "datetime": "2025-07-09T08:17:42.496Z", "lastTradeTimestamp": null, "lastUpdateTimestamp": null, "status": "open", "symbol": "LTC/USDT", "type": "limit", "timeInForce": null, "postOnly": null, "reduceOnly": false, "side": "buy", "price": 60, "triggerPrice": null, "takeProfitPrice": null, "stopLossPrice": null, "average": null, "amount": 0.1, "filled": null, "remaining": null, "cost": null, "trades": [], "fee": {"cost": 0, "currency": "LTC"}, "info": {"orderId": 60780315704, "clientOrderId": 0, "symbol": "SPOT_LTC_USDT", "orderTag": "default", "side": "BUY", "quantity": 0.1, "amount": null, "type": "LIMIT", "status": "NEW", "price": 60, "executed": 0, "visible": 0.1, "averageExecutedPrice": 0, "totalFee": 0, "feeAsset": "LTC", "totalRebate": 0, "rebateAsset": "USDT", "reduceOnly": false, "createdTime": "1752049062.496", "realizedPnl": null, "positionSide": "BOTH", "bidAskLevel": null}, "fees": [{"cost": 0, "currency": "LTC"}], "stopPrice": null}]}, {"description": "fetchOrders algo", "method": "fetchOrders", "input": ["LTC/USDT", null, 1, {"stop": true}], "httpResponse": {"success": true, "data": {"rows": [{"algoOrderId": 10399260, "clientAlgoOrderId": 0, "rootAlgoOrderId": 10399260, "parentAlgoOrderId": 0, "symbol": "SPOT_LTC_USDT", "algoOrderTag": "default", "algoType": "TAKE_PROFIT", "side": "BUY", "quantity": 0.1, "isTriggered": false, "triggerPrice": 65, "triggerStatus": "USELESS", "type": "LIMIT", "rootAlgoStatus": "NEW", "algoStatus": "NEW", "triggerPriceType": "MARKET_PRICE", "price": 60, "triggerTime": "0", "totalExecutedQuantity": 0, "visibleQuantity": 0.1, "averageExecutedPrice": 0, "totalFee": 0, "feeAsset": "", "totalRebate": 0, "rebateAsset": "", "reduceOnly": false, "createdTime": "1752049747.730", "updatedTime": "1752049747.730", "positionSide": "BOTH"}], "meta": {"total": 7, "recordsPerPage": 1, "currentPage": 1}}, "timestamp": 1752053592050}, "parsedResponse": [{"id": "10399260", "clientOrderId": null, "timestamp": 1752049747730, "datetime": "2025-07-09T08:29:07.730Z", "lastTradeTimestamp": null, "lastUpdateTimestamp": 1752049747730, "status": "open", "symbol": "LTC/USDT", "type": "limit", "timeInForce": null, "postOnly": null, "reduceOnly": false, "side": "buy", "price": 60, "triggerPrice": 65, "takeProfitPrice": null, "stopLossPrice": null, "average": null, "amount": 0.1, "filled": null, "remaining": null, "cost": null, "trades": [], "fee": {"cost": 0, "currency": null}, "info": {"algoOrderId": 10399260, "clientAlgoOrderId": 0, "rootAlgoOrderId": 10399260, "parentAlgoOrderId": 0, "symbol": "SPOT_LTC_USDT", "algoOrderTag": "default", "algoType": "TAKE_PROFIT", "side": "BUY", "quantity": 0.1, "isTriggered": false, "triggerPrice": 65, "triggerStatus": "USELESS", "type": "LIMIT", "rootAlgoStatus": "NEW", "algoStatus": "NEW", "triggerPriceType": "MARKET_PRICE", "price": 60, "triggerTime": "0", "totalExecutedQuantity": 0, "visibleQuantity": 0.1, "averageExecutedPrice": 0, "totalFee": 0, "feeAsset": "", "totalRebate": 0, "rebateAsset": "", "reduceOnly": false, "createdTime": "1752049747.730", "updatedTime": "1752049747.730", "positionSide": "BOTH"}, "fees": [{"cost": 0, "currency": null}], "stopPrice": 65}]}], "fetchOrder": [{"description": "fetchOrder", "method": "fetchOrder", "input": ["60780315704"], "httpResponse": {"success": true, "data": {"orderId": 60780315704, "clientOrderId": 0, "symbol": "SPOT_LTC_USDT", "orderTag": "default", "side": "BUY", "quantity": 0.1, "amount": null, "type": "LIMIT", "status": "NEW", "price": 60, "executed": 0, "visible": 0.1, "averageExecutedPrice": 0, "totalFee": 0, "feeAsset": "LTC", "totalRebate": null, "rebateAsset": "USDT", "reduceOnly": false, "createdTime": "1752049062.441", "realizedPnl": null, "positionSide": "BOTH", "bidAskLevel": null}, "timestamp": 1752049228485}, "parsedResponse": {"id": "60780315704", "clientOrderId": null, "timestamp": 1752049062441, "datetime": "2025-07-09T08:17:42.441Z", "lastTradeTimestamp": null, "lastUpdateTimestamp": null, "status": "open", "symbol": "LTC/USDT", "type": "limit", "timeInForce": null, "postOnly": null, "reduceOnly": false, "side": "buy", "price": 60, "triggerPrice": null, "takeProfitPrice": null, "stopLossPrice": null, "average": null, "amount": 0.1, "filled": null, "remaining": null, "cost": null, "trades": [], "fee": {"cost": 0, "currency": "LTC"}, "info": {"orderId": 60780315704, "clientOrderId": 0, "symbol": "SPOT_LTC_USDT", "orderTag": "default", "side": "BUY", "quantity": 0.1, "amount": null, "type": "LIMIT", "status": "NEW", "price": 60, "executed": 0, "visible": 0.1, "averageExecutedPrice": 0, "totalFee": 0, "feeAsset": "LTC", "totalRebate": null, "rebateAsset": "USDT", "reduceOnly": false, "createdTime": "1752049062.441", "realizedPnl": null, "positionSide": "BOTH", "bidAskLevel": null}, "fees": [{"cost": 0, "currency": "LTC"}], "stopPrice": null}}, {"description": "fetchOrder algo", "method": "fetchOrder", "input": ["10399260", null, {"stop": true}], "httpResponse": {"success": true, "data": {"algoOrderId": 10399260, "clientAlgoOrderId": 0, "rootAlgoOrderId": 10399260, "parentAlgoOrderId": 0, "symbol": "SPOT_LTC_USDT", "algoOrderTag": "default", "algoType": "TAKE_PROFIT", "side": "BUY", "quantity": 0.1, "isTriggered": false, "triggerPrice": 65, "triggerStatus": "USELESS", "type": "LIMIT", "rootAlgoStatus": "NEW", "algoStatus": "NEW", "triggerPriceType": "MARKET_PRICE", "price": 60, "triggerTime": "0", "totalExecutedQuantity": 0, "visibleQuantity": 0.1, "averageExecutedPrice": 0, "totalFee": 0, "feeAsset": "", "totalRebate": 0, "rebateAsset": "", "reduceOnly": false, "createdTime": "1752049747.732", "updatedTime": "1752049747.732", "positionSide": "BOTH"}, "timestamp": 1752049767550}, "parsedResponse": {"id": "10399260", "clientOrderId": null, "timestamp": 1752049747732, "datetime": "2025-07-09T08:29:07.732Z", "lastTradeTimestamp": null, "lastUpdateTimestamp": 1752049747732, "status": "open", "symbol": "LTC/USDT", "type": "limit", "timeInForce": null, "postOnly": null, "reduceOnly": false, "side": "buy", "price": 60, "triggerPrice": 65, "takeProfitPrice": null, "stopLossPrice": null, "average": null, "amount": 0.1, "filled": null, "remaining": null, "cost": null, "trades": [], "fee": {"cost": 0, "currency": null}, "info": {"algoOrderId": 10399260, "clientAlgoOrderId": 0, "rootAlgoOrderId": 10399260, "parentAlgoOrderId": 0, "symbol": "SPOT_LTC_USDT", "algoOrderTag": "default", "algoType": "TAKE_PROFIT", "side": "BUY", "quantity": 0.1, "isTriggered": false, "triggerPrice": 65, "triggerStatus": "USELESS", "type": "LIMIT", "rootAlgoStatus": "NEW", "algoStatus": "NEW", "triggerPriceType": "MARKET_PRICE", "price": 60, "triggerTime": "0", "totalExecutedQuantity": 0, "visibleQuantity": 0.1, "averageExecutedPrice": 0, "totalFee": 0, "feeAsset": "", "totalRebate": 0, "rebateAsset": "", "reduceOnly": false, "createdTime": "1752049747.732", "updatedTime": "1752049747.732", "positionSide": "BOTH"}, "fees": [{"cost": 0, "currency": null}], "stopPrice": 65}}], "fetchOrders": [{"description": "fetchOrders", "method": "fetchOrders", "input": ["LTC/USDT", null, 1], "httpResponse": {"success": true, "data": {"rows": [{"orderId": 60780315704, "clientOrderId": 0, "symbol": "SPOT_LTC_USDT", "orderTag": "default", "side": "BUY", "quantity": 0.1, "amount": null, "type": "LIMIT", "status": "NEW", "price": 60, "executed": 0, "visible": 0.1, "averageExecutedPrice": 0, "totalFee": 0, "feeAsset": "LTC", "totalRebate": 0, "rebateAsset": "USDT", "reduceOnly": false, "createdTime": "1752049062.496", "realizedPnl": null, "positionSide": "BOTH", "bidAskLevel": null}], "meta": {"total": 11, "recordsPerPage": 1, "currentPage": 1}}, "timestamp": 1752053575184}, "parsedResponse": [{"id": "60780315704", "clientOrderId": null, "timestamp": 1752049062496, "datetime": "2025-07-09T08:17:42.496Z", "lastTradeTimestamp": null, "lastUpdateTimestamp": null, "status": "open", "symbol": "LTC/USDT", "type": "limit", "timeInForce": null, "postOnly": null, "reduceOnly": false, "side": "buy", "price": 60, "triggerPrice": null, "takeProfitPrice": null, "stopLossPrice": null, "average": null, "amount": 0.1, "filled": null, "remaining": null, "cost": null, "trades": [], "fee": {"cost": 0, "currency": "LTC"}, "info": {"orderId": 60780315704, "clientOrderId": 0, "symbol": "SPOT_LTC_USDT", "orderTag": "default", "side": "BUY", "quantity": 0.1, "amount": null, "type": "LIMIT", "status": "NEW", "price": 60, "executed": 0, "visible": 0.1, "averageExecutedPrice": 0, "totalFee": 0, "feeAsset": "LTC", "totalRebate": 0, "rebateAsset": "USDT", "reduceOnly": false, "createdTime": "1752049062.496", "realizedPnl": null, "positionSide": "BOTH", "bidAskLevel": null}, "fees": [{"cost": 0, "currency": "LTC"}], "stopPrice": null}]}, {"description": "fetchOrders algo", "method": "fetchOrders", "input": ["LTC/USDT", null, 1, {"stop": true}], "httpResponse": {"success": true, "data": {"rows": [{"algoOrderId": 10399260, "clientAlgoOrderId": 0, "rootAlgoOrderId": 10399260, "parentAlgoOrderId": 0, "symbol": "SPOT_LTC_USDT", "algoOrderTag": "default", "algoType": "TAKE_PROFIT", "side": "BUY", "quantity": 0.1, "isTriggered": false, "triggerPrice": 65, "triggerStatus": "USELESS", "type": "LIMIT", "rootAlgoStatus": "NEW", "algoStatus": "NEW", "triggerPriceType": "MARKET_PRICE", "price": 60, "triggerTime": "0", "totalExecutedQuantity": 0, "visibleQuantity": 0.1, "averageExecutedPrice": 0, "totalFee": 0, "feeAsset": "", "totalRebate": 0, "rebateAsset": "", "reduceOnly": false, "createdTime": "1752049747.730", "updatedTime": "1752049747.730", "positionSide": "BOTH"}], "meta": {"total": 7, "recordsPerPage": 1, "currentPage": 1}}, "timestamp": 1752053592050}, "parsedResponse": [{"id": "10399260", "clientOrderId": null, "timestamp": 1752049747730, "datetime": "2025-07-09T08:29:07.730Z", "lastTradeTimestamp": null, "lastUpdateTimestamp": 1752049747730, "status": "open", "symbol": "LTC/USDT", "type": "limit", "timeInForce": null, "postOnly": null, "reduceOnly": false, "side": "buy", "price": 60, "triggerPrice": 65, "takeProfitPrice": null, "stopLossPrice": null, "average": null, "amount": 0.1, "filled": null, "remaining": null, "cost": null, "trades": [], "fee": {"cost": 0, "currency": null}, "info": {"algoOrderId": 10399260, "clientAlgoOrderId": 0, "rootAlgoOrderId": 10399260, "parentAlgoOrderId": 0, "symbol": "SPOT_LTC_USDT", "algoOrderTag": "default", "algoType": "TAKE_PROFIT", "side": "BUY", "quantity": 0.1, "isTriggered": false, "triggerPrice": 65, "triggerStatus": "USELESS", "type": "LIMIT", "rootAlgoStatus": "NEW", "algoStatus": "NEW", "triggerPriceType": "MARKET_PRICE", "price": 60, "triggerTime": "0", "totalExecutedQuantity": 0, "visibleQuantity": 0.1, "averageExecutedPrice": 0, "totalFee": 0, "feeAsset": "", "totalRebate": 0, "rebateAsset": "", "reduceOnly": false, "createdTime": "1752049747.730", "updatedTime": "1752049747.730", "positionSide": "BOTH"}, "fees": [{"cost": 0, "currency": null}], "stopPrice": 65}]}], "addMargin": [{"description": "Add margin with position side long", "method": "add<PERSON><PERSON>gin", "input": ["XRP/USDT:USDT", 0.2, {"position_side": "LONG"}], "httpResponse": {"success": true, "timestamp": "1721138361297"}, "parsedResponse": {"success": true, "timestamp": "1721138361297"}}, {"description": "Add margin with position side short", "method": "add<PERSON><PERSON>gin", "input": ["XRP/USDT:USDT", 0.2, {"position_side": "SHORT"}], "httpResponse": {"success": true, "timestamp": "1721138455234"}, "parsedResponse": {"success": true, "timestamp": "1721138455234"}}], "reduceMargin": [{"description": "Reduce margin with position side long", "method": "reduce<PERSON><PERSON>gin", "input": ["XRP/USDT:USDT", 0.2, {"position_side": "LONG"}], "httpResponse": {"success": true, "timestamp": "1721139040566"}, "parsedResponse": {"success": true, "timestamp": "1721139040566"}}, {"description": "Reduce margin with position side short", "method": "reduce<PERSON><PERSON>gin", "input": ["XRP/USDT:USDT", 0.2, {"position_side": "SHORT"}], "httpResponse": {"success": true, "timestamp": "1721139110860"}, "parsedResponse": {"success": true, "timestamp": "1721139110860"}}], "fetchFundingHistory": [{"description": "fetchFundingHistory", "method": "fetchFundingHistory", "input": [], "httpResponse": {"success": true, "data": {"meta": {"total": 670, "recordsPerPage": 25, "currentPage": 1}, "rows": [{"id": 1286360, "symbol": "PERP_BTC_USDT", "fundingRate": -1.445e-05, "markPrice": "26930.60000000", "fundingFee": "9.56021744", "fundingIntervalHours": 8, "paymentType": "Pay", "status": "COMPLETED", "createdTime": 1696060873259, "updatedTime": 1696060873286}]}, "timestamp": 1721351502594}, "parsedResponse": [{"info": {"id": 1286360, "symbol": "PERP_BTC_USDT", "fundingRate": -1.445e-05, "markPrice": "26930.60000000", "fundingFee": "9.56021744", "fundingIntervalHours": 8, "paymentType": "Pay", "status": "COMPLETED", "createdTime": 1696060873259, "updatedTime": 1696060873286}, "symbol": "BTC/USDT:USDT", "code": "USD", "timestamp": 1696060873286, "datetime": "2023-09-30T08:01:13.286Z", "id": "1286360", "amount": -9.56021744, "rate": -1.445e-05}]}], "fetchOrderBook": [{"description": "fetchOrderBook", "method": "fetchOrderBook", "input": ["LTC/USDT", 1], "httpResponse": {"success": true, "timestamp": 1751621356649, "data": {"asks": [{"price": "87.97", "quantity": "3.410253"}], "bids": [{"price": "87.95", "quantity": "0.047358"}]}}, "parsedResponse": {"symbol": "LTC/USDT", "bids": [[87.95, 0.047358]], "asks": [[87.97, 3.410253]], "timestamp": 1751621356649, "datetime": "2025-07-04T09:29:16.649Z", "nonce": null}}], "fetchFundingRate": [{"description": "fetchFundingRate", "method": "fetchFundingRate", "input": ["BTC/USDT:USDT"], "httpResponse": {"success": true, "data": {"rows": [{"symbol": "PERP_BTC_USDT", "estFundingRate": "-0.00000441", "estFundingRateTimestamp": 1751623979022, "lastFundingRate": "-0.00004953", "lastFundingRateTimestamp": 1751616000000, "nextFundingTime": 1751644800000, "lastFundingIntervalHours": 8, "estFundingIntervalHours": 8}]}, "timestamp": 1751624037798}, "parsedResponse": {"info": {"symbol": "PERP_BTC_USDT", "estFundingRate": "-0.00000441", "estFundingRateTimestamp": 1751623979022, "lastFundingRate": "-0.00004953", "lastFundingRateTimestamp": 1751616000000, "nextFundingTime": 1751644800000, "lastFundingIntervalHours": 8, "estFundingIntervalHours": 8}, "symbol": "BTC/USDT:USDT", "markPrice": null, "indexPrice": null, "interestRate": 0, "estimatedSettlePrice": null, "timestamp": 1751623979022, "datetime": "2025-07-04T10:12:59.022Z", "fundingRate": -4.41e-06, "fundingTimestamp": 1751644800000, "fundingDatetime": "2025-07-04T16:00:00.000Z", "nextFundingRate": null, "nextFundingTimestamp": null, "nextFundingDatetime": null, "previousFundingRate": -4.953e-05, "previousFundingTimestamp": 1751616000000, "previousFundingDatetime": "2025-07-04T08:00:00.000Z", "interval": "8h"}}], "fetchFundingRates": [{"description": "fetchFundingRates", "method": "fetchFundingRates", "input": [["BTC/USDT:USDT"]], "httpResponse": {"success": true, "data": {"rows": [{"symbol": "PERP_BTC_USDT", "estFundingRate": "-0.00000441", "estFundingRateTimestamp": 1751623979022, "lastFundingRate": "-0.00004953", "lastFundingRateTimestamp": 1751616000000, "nextFundingTime": 1751644800000, "lastFundingIntervalHours": 8, "estFundingIntervalHours": 8}]}, "timestamp": 1751624037798}, "parsedResponse": {"BTC/USDT:USDT": {"info": {"symbol": "PERP_BTC_USDT", "estFundingRate": "-0.00000441", "estFundingRateTimestamp": 1751623979022, "lastFundingRate": "-0.00004953", "lastFundingRateTimestamp": 1751616000000, "nextFundingTime": 1751644800000, "lastFundingIntervalHours": 8, "estFundingIntervalHours": 8}, "symbol": "BTC/USDT:USDT", "markPrice": null, "indexPrice": null, "interestRate": 0, "estimatedSettlePrice": null, "timestamp": 1751623979022, "datetime": "2025-07-04T10:12:59.022Z", "fundingRate": -4.41e-06, "fundingTimestamp": 1751644800000, "fundingDatetime": "2025-07-04T16:00:00.000Z", "nextFundingRate": null, "nextFundingTimestamp": null, "nextFundingDatetime": null, "previousFundingRate": -4.953e-05, "previousFundingTimestamp": 1751616000000, "previousFundingDatetime": "2025-07-04T08:00:00.000Z", "interval": "8h"}}}], "fetchFundingRateHistory": [{"description": "fetchFundingRateHistory", "method": "fetchFundingRateHistory", "input": ["BTC/USDT:USDT"], "httpResponse": {"success": true, "data": {"rows": [{"symbol": "PERP_BTC_USDT", "fundingRate": "-0.00004953", "fundingRateTimestamp": 1751616000000, "nextFundingTime": 1751644800000, "markPrice": "108708"}], "meta": {"total": 11690, "recordsPerPage": 25, "currentPage": 1}}, "timestamp": 1751632390031}, "parsedResponse": [{"info": {"symbol": "PERP_BTC_USDT", "fundingRate": "-0.00004953", "fundingRateTimestamp": 1751616000000, "nextFundingTime": 1751644800000, "markPrice": "108708"}, "symbol": "BTC/USDT:USDT", "fundingRate": -4.953e-05, "timestamp": 1751616000000, "datetime": "2025-07-04T08:00:00.000Z"}]}], "fetchTradingFee": [{"description": "fetchTradingFee", "method": "fetchTradingFee", "input": ["BTC/USDT:USDT"], "httpResponse": {"success": true, "data": {"symbol": "PERP_BTC_USDT", "takerFee": "5", "makerFee": "2"}, "timestamp": 1751859421833}, "parsedResponse": {"info": {"symbol": "PERP_BTC_USDT", "takerFee": "5", "makerFee": "2"}, "symbol": "BTC/USDT:USDT", "maker": 0.02, "taker": 0.05, "percentage": null, "tierBased": null}}], "cancelOrder": [{"description": "cancelOrder", "method": "cancelOrder", "input": ["10376200", "BTC/USDT"], "httpResponse": {"success": true, "data": {"status": "CANCEL_SENT"}, "timestamp": 1751940315838}, "parsedResponse": {"id": "10376200", "clientOrderId": null, "timestamp": 1751940315838, "datetime": "2025-07-08T02:05:15.838Z", "lastTradeTimestamp": null, "lastUpdateTimestamp": null, "status": "canceled", "symbol": "BTC/USDT", "type": null, "timeInForce": null, "postOnly": null, "reduceOnly": null, "side": null, "price": null, "triggerPrice": null, "takeProfitPrice": null, "stopLossPrice": null, "average": null, "amount": null, "filled": null, "remaining": null, "cost": null, "trades": [], "fee": {"cost": null, "currency": null}, "info": {"status": "CANCEL_SENT", "timestamp": "1751940315838", "orderId": "10376200"}, "fees": [{"cost": null, "currency": null}], "stopPrice": null}}], "cancelAllOrders": [{"description": "cancelAllOrders", "disabled": true, "method": "cancelAllOrders", "input": [], "httpResponse": {"success": true, "data": {"status": "CANCEL_All_SENT"}, "timestamp": 1751940315838}, "parsedResponse": {"success": true, "data": {"status": "CANCEL_All_SENT"}, "timestamp": 1751940315838}}], "cancelAllOrdersAfter": [{"description": "cancelAllOrdersAfter", "method": "cancelAllOrdersAfter", "input": [10000], "httpResponse": {"success": true, "timestamp": 123, "data": {"expectedTriggerTime": 123}}, "parsedResponse": {"success": true, "timestamp": 123, "data": {"expectedTriggerTime": 123}}}], "fetchDeposits": [{"description": "fetchDeposits", "method": "fetchDeposits", "input": [], "httpResponse": {"success": true, "data": {"rows": [{"createdTime": "**********.126", "updatedTime": "**********.727", "id": "*****************", "externalId": "***************", "applicationId": "251bf5c4-f3c8-4544-bb8b-80001007c3c0", "token": "TRON_USDT", "targetAddress": "TTsY9uu2Y3aBH1vSir1md7oRqZe7DscA4v", "sourceAddress": "TSaRZDiBPD8Rd5vrvX8a4zgunHczM9mj8S", "extra": "", "type": "BALANCE", "tokenSide": "DEPOSIT", "amount": "5.********", "txId": "dc3dbf3185f519036adfeeb4a2390c7646028e20e06e536fb22f36247111494a", "feeToken": "", "feeAmount": "0", "status": "COMPLETED", "confirmingThreshold": null, "confirmedNumber": null}], "meta": {"total": 1, "records_per_page": 25, "current_page": 1}}, "timestamp": *************}, "parsedResponse": [{"info": {"createdTime": "**********.126", "updatedTime": "**********.727", "id": "*****************", "externalId": "***************", "applicationId": "251bf5c4-f3c8-4544-bb8b-80001007c3c0", "token": "TRON_USDT", "targetAddress": "TTsY9uu2Y3aBH1vSir1md7oRqZe7DscA4v", "sourceAddress": "TSaRZDiBPD8Rd5vrvX8a4zgunHczM9mj8S", "extra": "", "type": "BALANCE", "tokenSide": "DEPOSIT", "amount": "5.********", "txId": "dc3dbf3185f519036adfeeb4a2390c7646028e20e06e536fb22f36247111494a", "feeToken": "", "feeAmount": "0", "status": "COMPLETED", "confirmingThreshold": null, "confirmedNumber": null}, "id": "*****************", "txid": "dc3dbf3185f519036adfeeb4a2390c7646028e20e06e536fb22f36247111494a", "timestamp": **********126, "datetime": "2024-03-23T12:46:54.126Z", "address": null, "addressFrom": "TSaRZDiBPD8Rd5vrvX8a4zgunHczM9mj8S", "addressTo": "TTsY9uu2Y3aBH1vSir1md7oRqZe7DscA4v", "tag": null, "tagFrom": null, "tagTo": null, "type": "deposit", "amount": 5, "currency": "USDT", "status": "ok", "updated": **********727, "comment": null, "internal": null, "fee": {"cost": "0", "currency": null}, "network": null, "tokenSide": "DEPOSIT"}]}], "fetchWithdrawals": [{"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "method": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "input": [], "httpResponse": {"success": true, "data": {"rows": [{"createdTime": "1734964440.523", "updatedTime": "1734964614.081", "id": "24122314340000585", "externalId": "241223143600621", "applicationId": "251bf5c4-f3c8-4544-bb8b-80001007c3c0", "token": "ARB_USDCNATIVE", "targetAddress": "******************************************", "sourceAddress": "******************************************", "extra": "", "type": "BALANCE", "tokenSide": "WITHDRAW", "amount": "10.********", "txId": "0x891ade0a47fd55466bb9d06702bea4edcb75ed9367d9afbc47b93a84f496d2e6", "feeToken": "USDC", "feeAmount": "2", "status": "COMPLETED", "confirmingThreshold": null, "confirmedNumber": null}], "meta": {"total": 1, "records_per_page": 25, "current_page": 1}}, "timestamp": 1752495962106}, "parsedResponse": [{"info": {"createdTime": "1734964440.523", "updatedTime": "1734964614.081", "id": "24122314340000585", "externalId": "241223143600621", "applicationId": "251bf5c4-f3c8-4544-bb8b-80001007c3c0", "token": "ARB_USDCNATIVE", "targetAddress": "******************************************", "sourceAddress": "******************************************", "extra": "", "type": "BALANCE", "tokenSide": "WITHDRAW", "amount": "10.********", "txId": "0x891ade0a47fd55466bb9d06702bea4edcb75ed9367d9afbc47b93a84f496d2e6", "feeToken": "USDC", "feeAmount": "2", "status": "COMPLETED", "confirmingThreshold": null, "confirmedNumber": null}, "id": "24122314340000585", "txid": "0x891ade0a47fd55466bb9d06702bea4edcb75ed9367d9afbc47b93a84f496d2e6", "timestamp": 1734964440523, "datetime": "2024-12-23T14:34:00.523Z", "address": null, "addressFrom": "******************************************", "addressTo": "******************************************", "tag": null, "tagFrom": null, "tagTo": null, "type": "withdrawal", "amount": 10, "currency": "USDCNATIVE", "status": "ok", "updated": 1734964614081, "comment": null, "internal": null, "fee": {"cost": "2", "currency": "USDC"}, "network": null, "tokenSide": "WITHDRAW"}]}], "fetchLedger": [{"description": "fetchLedger", "method": "fetchLedger", "input": [], "httpResponse": {"success": true, "data": {"rows": [{"createdTime": "**********.126", "updatedTime": "**********.727", "id": "*****************", "externalId": "***************", "applicationId": "251bf5c4-f3c8-4544-bb8b-80001007c3c0", "token": "TRON_USDT", "targetAddress": "TTsY9uu2Y3aBH1vSir1md7oRqZe7DscA4v", "sourceAddress": "TSaRZDiBPD8Rd5vrvX8a4zgunHczM9mj8S", "extra": "", "type": "BALANCE", "tokenSide": "DEPOSIT", "amount": "5.********", "txId": "dc3dbf3185f519036adfeeb4a2390c7646028e20e06e536fb22f36247111494a", "feeToken": "", "feeAmount": "0", "status": "COMPLETED", "confirmingThreshold": null, "confirmedNumber": null}], "meta": {"total": 1, "records_per_page": 25, "current_page": 1}}, "timestamp": *************}, "parsedResponse": [{"id": "*****************", "timestamp": **********126, "datetime": "2024-03-23T12:46:54.126Z", "direction": "in", "account": null, "referenceId": "dc3dbf3185f519036adfeeb4a2390c7646028e20e06e536fb22f36247111494a", "referenceAccount": null, "type": "transaction", "currency": "TRON_USDT", "amount": 5, "before": null, "after": null, "status": "ok", "fee": {"cost": 0, "currency": null}, "info": {"createdTime": "**********.126", "updatedTime": "**********.727", "id": "*****************", "externalId": "***************", "applicationId": "251bf5c4-f3c8-4544-bb8b-80001007c3c0", "token": "TRON_USDT", "targetAddress": "TTsY9uu2Y3aBH1vSir1md7oRqZe7DscA4v", "sourceAddress": "TSaRZDiBPD8Rd5vrvX8a4zgunHczM9mj8S", "extra": "", "type": "BALANCE", "tokenSide": "DEPOSIT", "amount": "5.********", "txId": "dc3dbf3185f519036adfeeb4a2390c7646028e20e06e536fb22f36247111494a", "feeToken": "", "feeAmount": "0", "status": "COMPLETED", "confirmingThreshold": null, "confirmedNumber": null}}]}], "fetchDepositsWithdrawals": [{"description": "fetchDepositsWithdrawals", "method": "fetchDepositsWithdrawals", "input": [], "httpResponse": {"success": true, "data": {"rows": [{"createdTime": "**********.126", "updatedTime": "**********.727", "id": "*****************", "externalId": "***************", "applicationId": "251bf5c4-f3c8-4544-bb8b-80001007c3c0", "token": "TRON_USDT", "targetAddress": "TTsY9uu2Y3aBH1vSir1md7oRqZe7DscA4v", "sourceAddress": "TSaRZDiBPD8Rd5vrvX8a4zgunHczM9mj8S", "extra": "", "type": "BALANCE", "tokenSide": "DEPOSIT", "amount": "5.********", "txId": "dc3dbf3185f519036adfeeb4a2390c7646028e20e06e536fb22f36247111494a", "feeToken": "", "feeAmount": "0", "status": "COMPLETED", "confirmingThreshold": null, "confirmedNumber": null}], "meta": {"total": 1, "records_per_page": 25, "current_page": 1}}, "timestamp": *************}, "parsedResponse": [{"info": {"createdTime": "**********.126", "updatedTime": "**********.727", "id": "*****************", "externalId": "***************", "applicationId": "251bf5c4-f3c8-4544-bb8b-80001007c3c0", "token": "TRON_USDT", "targetAddress": "TTsY9uu2Y3aBH1vSir1md7oRqZe7DscA4v", "sourceAddress": "TSaRZDiBPD8Rd5vrvX8a4zgunHczM9mj8S", "extra": "", "type": "BALANCE", "tokenSide": "DEPOSIT", "amount": "5.********", "txId": "dc3dbf3185f519036adfeeb4a2390c7646028e20e06e536fb22f36247111494a", "feeToken": "", "feeAmount": "0", "status": "COMPLETED", "confirmingThreshold": null, "confirmedNumber": null}, "id": "*****************", "txid": "dc3dbf3185f519036adfeeb4a2390c7646028e20e06e536fb22f36247111494a", "timestamp": **********126, "datetime": "2024-03-23T12:46:54.126Z", "address": null, "addressFrom": "TSaRZDiBPD8Rd5vrvX8a4zgunHczM9mj8S", "addressTo": "TTsY9uu2Y3aBH1vSir1md7oRqZe7DscA4v", "tag": null, "tagFrom": null, "tagTo": null, "type": "deposit", "amount": 5, "currency": "USDT", "status": "ok", "updated": **********727, "comment": null, "internal": null, "fee": {"cost": "0", "currency": null}, "network": null}]}], "fetchTransfers": [{"description": "fetchTransfers", "method": "fetchTransfers", "input": [], "httpResponse": {"success": true, "data": {"rows": [{"id": 225, "token": "USDT", "amount": "1000000", "status": "COMPLETED", "from": {"applicationId": "046b5c5c-5b44-4d27-9593-ddc32c0a08ae", "accountName": "Main"}, "to": {"applicationId": "082ae5ae-e26a-4fb1-be5b-03e5b4867663", "accountName": "sub001"}, "createdTime": "**********.534", "updatedTime": "**********.950"}], "meta": {"total": 46, "recordsPerPage": 1, "currentPage": 1}}, "timestamp": *************}, "parsedResponse": [{"id": "225", "timestamp": **********534, "datetime": "2022-01-20T06:42:21.534Z", "currency": "USDT", "amount": 1000000, "fromAccount": "046b5c5c-5b44-4d27-9593-ddc32c0a08ae", "toAccount": "082ae5ae-e26a-4fb1-be5b-03e5b4867663", "status": "ok", "info": {"id": 225, "token": "USDT", "amount": "1000000", "status": "COMPLETED", "from": {"applicationId": "046b5c5c-5b44-4d27-9593-ddc32c0a08ae", "accountName": "Main"}, "to": {"applicationId": "082ae5ae-e26a-4fb1-be5b-03e5b4867663", "accountName": "sub001"}, "createdTime": "**********.534", "updatedTime": "**********.950"}}]}], "fetchLeverage": [{"description": "fetchLeverage", "method": "fetchLeverage", "input": ["LTC/USDT"], "httpResponse": {"success": true, "data": {"applicationId": "251bf5c4-f3c8-4544-bb8b-80001007c3c0", "account": "<EMAIL>", "alias": "<EMAIL>", "otpauth": true, "accountMode": "FUTURES", "positionMode": "ONE_WAY", "leverage": 0, "marginRatio": "10", "openMarginRatio": "10", "initialMarginRatio": "10", "maintenanceMarginRatio": "0.03", "totalCollateral": "165.********", "freeCollateral": "165.********", "totalAccountValue": "167.********", "totalTradingValue": "167.********", "totalVaultValue": "0", "totalStakingValue": "0", "totalLaunchpadValue": "0", "totalEarnValue": "0", "referrerID": null, "accountType": "Main"}, "timestamp": *************}, "parsedResponse": {"info": {"applicationId": "251bf5c4-f3c8-4544-bb8b-80001007c3c0", "account": "<EMAIL>", "alias": "<EMAIL>", "otpauth": true, "accountMode": "FUTURES", "positionMode": "ONE_WAY", "leverage": 0, "marginRatio": "10", "openMarginRatio": "10", "initialMarginRatio": "10", "maintenanceMarginRatio": "0.03", "totalCollateral": "165.********", "freeCollateral": "165.********", "totalAccountValue": "167.********", "totalTradingValue": "167.********", "totalVaultValue": "0", "totalStakingValue": "0", "totalLaunchpadValue": "0", "totalEarnValue": "0", "referrerID": null, "accountType": "Main"}, "symbol": "LTC/USDT", "marginMode": null, "longLeverage": null, "shortLeverage": null}}, {"description": "fetchLeverage - swap", "method": "fetchLeverage", "input": ["LTC/USDT:USDT", {"positionMode": "ONE_WAY"}], "httpResponse": {"success": true, "data": {"symbol": "PERP_LTC_USDT", "marginMode": "CROSS", "positionMode": "ONE_WAY", "details": [{"positionSide": "LONG", "leverage": 10}, {"positionSide": "SHORT", "leverage": 10}]}, "timestamp": *************}, "parsedResponse": {"info": {"symbol": "PERP_LTC_USDT", "marginMode": "CROSS", "positionMode": "ONE_WAY", "details": [{"positionSide": "LONG", "leverage": 10}, {"positionSide": "SHORT", "leverage": 10}]}, "symbol": "LTC/USDT:USDT", "marginMode": "cross", "longLeverage": 10, "shortLeverage": 10}}]}}