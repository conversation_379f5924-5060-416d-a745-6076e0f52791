{"exchange": "cex", "skipKeys": [], "methods": {"fetchCurrencies": [{"description": "fetchCurrencies", "method": "fetchCurrencies", "input": [], "httpResponse": {"ok": "ok", "data": {"USDT": {"name": "<PERSON><PERSON>", "currency": "USDT", "fiat": false, "precision": "8", "walletPrecision": "6", "walletDeposit": true, "walletWithdrawal": true, "blockchains": {"ethereum": {"type": "ERC20", "deposit": "enabled", "minDeposit": "5", "withdrawal": "enabled", "minWithdrawal": "20", "withdrawalFee": "10", "withdrawalFeePercent": "0", "depositConfirmations": "25"}, "tron": {"type": "TRC20", "deposit": "enabled", "minDeposit": "5", "withdrawal": "enabled", "minWithdrawal": "10", "withdrawalFee": "5", "withdrawalFeePercent": "0", "depositConfirmations": "21"}, "solana": {"type": "SPL", "deposit": "enabled", "minDeposit": "5", "withdrawal": "enabled", "minWithdrawal": "10", "withdrawalFee": "1", "withdrawalFeePercent": "0", "depositConfirmations": "1"}, "binancesmartchain": {"type": "BEP20", "deposit": "enabled", "minDeposit": "5", "withdrawal": "enabled", "minWithdrawal": "10", "withdrawalFee": "2", "withdrawalFeePercent": "0", "depositConfirmations": "25"}}}}}, "parsedResponse": {"USDT": {"info": {"currency": "USDT", "fiat": false, "precision": "8", "walletPrecision": "6", "walletDeposit": true, "walletWithdrawal": true, "name": "<PERSON><PERSON>", "blockchains": {"ethereum": {"type": "ERC20", "deposit": "enabled", "minDeposit": "5", "withdrawal": "enabled", "minWithdrawal": "20", "withdrawalFee": "10", "withdrawalFeePercent": "0", "depositConfirmations": "25"}, "tron": {"type": "TRC20", "deposit": "enabled", "minDeposit": "5", "withdrawal": "enabled", "minWithdrawal": "10", "withdrawalFee": "5", "withdrawalFeePercent": "0", "depositConfirmations": "21"}, "solana": {"type": "SPL", "deposit": "enabled", "minDeposit": "5", "withdrawal": "enabled", "minWithdrawal": "10", "withdrawalFee": "1", "withdrawalFeePercent": "0", "depositConfirmations": "1"}, "binancesmartchain": {"type": "BEP20", "deposit": "enabled", "minDeposit": "5", "withdrawal": "enabled", "minWithdrawal": "10", "withdrawalFee": "2", "withdrawalFeePercent": "0", "depositConfirmations": "25"}}}, "id": "USDT", "numericId": null, "code": "USDT", "precision": 1e-08, "type": "crypto", "name": null, "active": true, "deposit": true, "withdraw": true, "fee": 1, "fees": {}, "networks": {"ethereum": {"id": "ethereum", "network": "ethereum", "margin": null, "deposit": true, "withdraw": true, "active": true, "fee": 10, "precision": 1e-08, "limits": {"deposit": {"min": 5, "max": null}, "withdraw": {"min": 20, "max": null}}, "info": {"type": "ERC20", "deposit": "enabled", "minDeposit": "5", "withdrawal": "enabled", "minWithdrawal": "20", "withdrawalFee": "10", "withdrawalFeePercent": "0", "depositConfirmations": "25"}}, "TRC20": {"id": "tron", "network": "TRC20", "margin": null, "deposit": true, "withdraw": true, "active": true, "fee": 5, "precision": 1e-08, "limits": {"deposit": {"min": 5, "max": null}, "withdraw": {"min": 10, "max": null}}, "info": {"type": "TRC20", "deposit": "enabled", "minDeposit": "5", "withdrawal": "enabled", "minWithdrawal": "10", "withdrawalFee": "5", "withdrawalFeePercent": "0", "depositConfirmations": "21"}}, "SOLANA": {"id": "solana", "network": "SOLANA", "margin": null, "deposit": true, "withdraw": true, "active": true, "fee": 1, "precision": 1e-08, "limits": {"deposit": {"min": 5, "max": null}, "withdraw": {"min": 10, "max": null}}, "info": {"type": "SPL", "deposit": "enabled", "minDeposit": "5", "withdrawal": "enabled", "minWithdrawal": "10", "withdrawalFee": "1", "withdrawalFeePercent": "0", "depositConfirmations": "1"}}, "BSC20": {"id": "binancesmartchain", "network": "BSC20", "margin": null, "deposit": true, "withdraw": true, "active": true, "fee": 2, "precision": 1e-08, "limits": {"deposit": {"min": 5, "max": null}, "withdraw": {"min": 10, "max": null}}, "info": {"type": "BEP20", "deposit": "enabled", "minDeposit": "5", "withdrawal": "enabled", "minWithdrawal": "10", "withdrawalFee": "2", "withdrawalFeePercent": "0", "depositConfirmations": "25"}}}, "limits": {"amount": {"min": null, "max": null}, "withdraw": {"min": 10, "max": null}, "deposit": {"min": 5, "max": null}}}}}], "createOrder": [{"description": "limit buy", "method": "createOrder", "input": ["LTC/USDT", "limit", "buy", 0.23, 54, {"accountId": "sub1"}], "httpResponse": {"ok": "ok", "data": {"messageType": "executionReport", "clientId": "up132245425", "orderId": "1320423", "clientOrderId": "8449c1c7-55a6-4b26-b553-52ab2249cfeb", "accountId": "sub1", "status": "NEW", "currency1": "LTC", "currency2": "USDT", "side": "BUY", "executedAmountCcy1": "0.********", "executedAmountCcy2": "0.********", "requestedAmountCcy1": "0.********", "requestedAmountCcy2": null, "orderType": "Limit", "timeInForce": "GTC", "comment": null, "executionType": "New", "executionId": "1726747121903_100_43790", "transactTime": "2024-10-17T12:39:04.002Z", "expireTime": null, "effectiveTime": null, "price": "54.00", "averagePrice": null, "feeAmount": "0.********", "feeCurrency": "USDT", "clientCreateTimestamp": "*************", "serverCreateTimestamp": "*************", "lastUpdateTimestamp": "*************"}}, "parsedResponse": {"id": "1320423", "clientOrderId": "8449c1c7-55a6-4b26-b553-52ab2249cfeb", "timestamp": *************, "datetime": "2024-10-17T12:39:03.886Z", "lastUpdateTimestamp": *************, "lastTradeTimestamp": null, "symbol": "LTC/USDT", "type": "limit", "timeInForce": "GTC", "postOnly": false, "side": "buy", "price": 54, "stopPrice": null, "amount": 0.23, "cost": 0, "average": null, "filled": 0, "remaining": 0.23, "status": "open", "fee": {"currency": "USDT", "cost": 0}, "trades": [], "info": {"messageType": "executionReport", "clientId": "up132245425", "orderId": "1320423", "clientOrderId": "8449c1c7-55a6-4b26-b553-52ab2249cfeb", "accountId": "sub1", "status": "NEW", "currency1": "LTC", "currency2": "USDT", "side": "BUY", "executedAmountCcy1": "0.********", "executedAmountCcy2": "0.********", "requestedAmountCcy1": "0.********", "requestedAmountCcy2": null, "orderType": "Limit", "timeInForce": "GTC", "comment": null, "executionType": "New", "executionId": "1726747121903_100_43790", "transactTime": "2024-10-17T12:39:04.002Z", "expireTime": null, "effectiveTime": null, "price": "54.00", "averagePrice": null, "feeAmount": "0.********", "feeCurrency": "USDT", "clientCreateTimestamp": "*************", "serverCreateTimestamp": "*************", "lastUpdateTimestamp": "*************"}, "fees": [{"currency": "USDT", "cost": 0}], "reduceOnly": null, "triggerPrice": null, "takeProfitPrice": null, "stopLossPrice": null}}, {"description": "market buy", "method": "createOrder", "input": ["LTC/USDT", "market", "buy", 0.23, null, {"accountId": "sub1"}], "httpResponse": {"ok": "ok", "data": {"messageType": "executionReport", "clientId": "up132245425", "orderId": "1320428", "clientOrderId": "805551cb-109d-4c57-b639-8a721ababa1c", "accountId": "sub1", "status": "FILLED", "currency1": "LTC", "currency2": "USDT", "side": "BUY", "executedAmountCcy1": "0.********", "executedAmountCcy2": "16.********", "requestedAmountCcy1": "0.********", "requestedAmountCcy2": null, "orderType": "Market", "timeInForce": null, "comment": null, "executionType": "Trade", "executionId": "1726747124624_101_44017", "transactTime": "2024-10-17T12:41:05.770Z", "expireTime": null, "effectiveTime": null, "averagePrice": "71.22", "lastQuantity": "0.********", "lastAmountCcy1": "0.********", "lastAmountCcy2": "16.********", "lastPrice": "71.22", "feeAmount": "0.********", "feeCurrency": "USDT", "clientCreateTimestamp": "*************", "serverCreateTimestamp": "*************", "lastUpdateTimestamp": "*************"}}, "parsedResponse": {"id": "1320428", "clientOrderId": "805551cb-109d-4c57-b639-8a721ababa1c", "timestamp": *************, "datetime": "2024-10-17T12:41:04.453Z", "lastUpdateTimestamp": *************, "lastTradeTimestamp": null, "symbol": "LTC/USDT", "type": "market", "timeInForce": "IOC", "postOnly": null, "side": "buy", "price": 71.22, "stopPrice": null, "amount": 0.23, "cost": 16.3806, "average": 71.22, "filled": 0.23, "remaining": 0, "status": "closed", "fee": {"currency": "USDT", "cost": 0.0409515}, "trades": [], "info": {"messageType": "executionReport", "clientId": "up132245425", "orderId": "1320428", "clientOrderId": "805551cb-109d-4c57-b639-8a721ababa1c", "accountId": "sub1", "status": "FILLED", "currency1": "LTC", "currency2": "USDT", "side": "BUY", "executedAmountCcy1": "0.********", "executedAmountCcy2": "16.********", "requestedAmountCcy1": "0.********", "requestedAmountCcy2": null, "orderType": "Market", "timeInForce": null, "comment": null, "executionType": "Trade", "executionId": "1726747124624_101_44017", "transactTime": "2024-10-17T12:41:05.770Z", "expireTime": null, "effectiveTime": null, "averagePrice": "71.22", "lastQuantity": "0.********", "lastAmountCcy1": "0.********", "lastAmountCcy2": "16.********", "lastPrice": "71.22", "feeAmount": "0.********", "feeCurrency": "USDT", "clientCreateTimestamp": "*************", "serverCreateTimestamp": "*************", "lastUpdateTimestamp": "*************"}, "fees": [{"currency": "USDT", "cost": 0.0409515}], "reduceOnly": null, "triggerPrice": null, "takeProfitPrice": null, "stopLossPrice": null}}, {"description": "market sell", "method": "createOrder", "input": ["LTC/USDT", "market", "sell", 0.23, null, {"accountId": "sub1"}], "httpResponse": {"ok": "ok", "data": {"messageType": "executionReport", "clientId": "up132245425", "orderId": "1320433", "clientOrderId": "7361b0fc-36f8-4d4c-9965-244890986fa3", "accountId": "sub1", "status": "FILLED", "currency1": "LTC", "currency2": "USDT", "side": "SELL", "executedAmountCcy1": "0.********", "executedAmountCcy2": "16.********", "requestedAmountCcy1": "0.********", "requestedAmountCcy2": null, "orderType": "Market", "timeInForce": null, "comment": null, "executionType": "Trade", "executionId": "1726747124624_101_44027", "transactTime": "2024-10-17T12:42:59.081Z", "expireTime": null, "effectiveTime": null, "averagePrice": "71.20", "lastQuantity": "0.********", "lastAmountCcy1": "0.********", "lastAmountCcy2": "16.********", "lastPrice": "71.20", "feeAmount": "0.********", "feeCurrency": "USDT", "clientCreateTimestamp": "*************", "serverCreateTimestamp": "*************", "lastUpdateTimestamp": "*************"}}, "parsedResponse": {"id": "1320433", "clientOrderId": "7361b0fc-36f8-4d4c-9965-244890986fa3", "timestamp": *************, "datetime": "2024-10-17T12:42:57.930Z", "lastUpdateTimestamp": *************, "lastTradeTimestamp": null, "symbol": "LTC/USDT", "type": "market", "timeInForce": "IOC", "postOnly": null, "side": "sell", "price": 71.2, "stopPrice": null, "amount": 0.23, "cost": 16.376, "average": 71.2, "filled": 0.23, "remaining": 0, "status": "closed", "fee": {"currency": "USDT", "cost": 0.04094}, "trades": [], "info": {"messageType": "executionReport", "clientId": "up132245425", "orderId": "1320433", "clientOrderId": "7361b0fc-36f8-4d4c-9965-244890986fa3", "accountId": "sub1", "status": "FILLED", "currency1": "LTC", "currency2": "USDT", "side": "SELL", "executedAmountCcy1": "0.********", "executedAmountCcy2": "16.********", "requestedAmountCcy1": "0.********", "requestedAmountCcy2": null, "orderType": "Market", "timeInForce": null, "comment": null, "executionType": "Trade", "executionId": "1726747124624_101_44027", "transactTime": "2024-10-17T12:42:59.081Z", "expireTime": null, "effectiveTime": null, "averagePrice": "71.20", "lastQuantity": "0.********", "lastAmountCcy1": "0.********", "lastAmountCcy2": "16.********", "lastPrice": "71.20", "feeAmount": "0.********", "feeCurrency": "USDT", "clientCreateTimestamp": "*************", "serverCreateTimestamp": "*************", "lastUpdateTimestamp": "*************"}, "fees": [{"currency": "USDT", "cost": 0.04094}], "reduceOnly": null, "triggerPrice": null, "takeProfitPrice": null, "stopLossPrice": null}}], "cancelOrder": [{"description": "default", "method": "cancelOrder", "input": [1320439], "httpResponse": {"ok": "ok", "data": {}}, "parsedResponse": {"id": null, "clientOrderId": null, "timestamp": null, "datetime": null, "lastUpdateTimestamp": null, "lastTradeTimestamp": null, "symbol": null, "type": null, "timeInForce": null, "postOnly": null, "side": null, "price": null, "stopPrice": null, "amount": null, "cost": null, "average": null, "filled": null, "remaining": null, "status": null, "fee": {}, "trades": [], "info": {}, "fees": [{"cost": null}], "reduceOnly": null, "triggerPrice": null, "takeProfitPrice": null, "stopLossPrice": null}}], "cancelAllOrders": [{"description": "default", "method": "cancelAllOrders", "input": [], "httpResponse": {"ok": "ok", "data": {"clientOrderIds": ["8449c1c7-55a6-4b26-b553-52ab2249cfeb"]}}, "parsedResponse": [{"id": null, "clientOrderId": "8449c1c7-55a6-4b26-b553-52ab2249cfeb", "timestamp": null, "datetime": null, "lastUpdateTimestamp": null, "lastTradeTimestamp": null, "symbol": null, "type": null, "timeInForce": null, "postOnly": null, "side": null, "price": null, "stopPrice": null, "amount": null, "cost": null, "average": null, "filled": null, "remaining": null, "status": null, "fee": {}, "trades": [], "info": {"clientOrderId": "8449c1c7-55a6-4b26-b553-52ab2249cfeb"}, "fees": [{"cost": null}], "reduceOnly": null, "triggerPrice": null, "takeProfitPrice": null, "stopLossPrice": null}]}], "fetchOrderBook": [{"description": "default", "method": "fetchOrderBook", "input": ["BTC/USDT"], "httpResponse": {"ok": "ok", "data": {"timestamp": "1729169057110", "currency1": "BTC", "currency2": "USDT", "bids": [["67123.0", "1.40936601"], ["67122.7", "0.74515000"], ["67122.6", "0.00282000"], ["67122.5", "0.00232000"], ["67122.4", "0.00130000"], ["67122.2", "0.00447000"]], "asks": [["67123.1", "0.53413826"], ["67123.4", "1.02788603"], ["67123.5", "0.00697441"], ["67123.7", "0.00001500"], ["67123.9", "0.08835108"], ["67124.0", "0.25252000"]]}}, "parsedResponse": {"symbol": "BTC/USDT", "bids": [[67123, 1.40936601], [67122.7, 0.74515], [67122.6, 0.00282], [67122.5, 0.00232], [67122.4, 0.0013], [67122.2, 0.00447]], "asks": [[67123.1, 0.53413826], [67123.4, 1.02788603], [67123.5, 0.00697441], [67123.7, 1.5e-05], [67123.9, 0.08835108], [67124, 0.25252]], "timestamp": 1729169057110, "datetime": "2024-10-17T12:44:17.110Z", "nonce": null}}], "fetchBalance": [{"description": "default", "method": "fetchBalance", "input": [], "httpResponse": {"ok": "ok", "data": {"BTC": {"balance": "0.********"}, "XRP": {"balance": "0.000000"}, "TRX": {"balance": "0.039369"}, "USD": {"balance": "0.02"}, "AI": {"balance": "0.000000"}, "USDT": {"balance": "0.877611"}}}, "parsedResponse": {"info": {"BTC": {"balance": "0.********"}, "XRP": {"balance": "0.000000"}, "TRX": {"balance": "0.039369"}, "USD": {"balance": "0.02"}, "AI": {"balance": "0.000000"}, "USDT": {"balance": "0.877611"}}, "BTC": {"used": null, "free": null, "total": 0}, "XRP": {"used": null, "free": null, "total": 0}, "TRX": {"used": null, "free": null, "total": 0.039369}, "USD": {"used": null, "free": null, "total": 0.02}, "AI": {"used": null, "free": null, "total": 0}, "USDT": {"used": null, "free": null, "total": 0.877611}, "total": {"BTC": 0, "XRP": 0, "TRX": 0.039369, "USD": 0.02, "AI": 0, "USDT": 0.877611}, "used": {"BTC": null, "XRP": null, "TRX": null, "USD": null, "AI": null, "USDT": null}, "free": {"BTC": null, "XRP": null, "TRX": null, "USD": null, "AI": null, "USDT": null}}}], "fetchOpenOrders": [{"description": "default", "method": "fetchOpenOrders", "input": [], "httpResponse": {"ok": "ok", "data": [{"orderId": "1320439", "clientOrderId": "a3daff06-c8a7-411d-9955-48c13d629218", "clientId": "up132245425", "accountId": "sub1", "status": "NEW", "statusIsFinal": false, "currency1": "LTC", "currency2": "USDT", "side": "BUY", "orderType": "Limit", "timeInForce": "GTC", "comment": null, "rejectCode": null, "rejectReason": null, "initialOnHoldAmountCcy1": null, "initialOnHoldAmountCcy2": "12.********", "executedAmountCcy1": null, "executedAmountCcy2": null, "requestedAmountCcy1": "0.********", "requestedAmountCcy2": null, "feeAmount": "0.********", "feeCurrency": "USDT", "price": "54.00", "averagePrice": null, "clientCreateTimestamp": "*************", "serverCreateTimestamp": "*************", "lastUpdateTimestamp": "*************", "expireTime": null, "effectiveTime": null}]}, "parsedResponse": [{"id": "1320439", "clientOrderId": "a3daff06-c8a7-411d-9955-48c13d629218", "timestamp": *************, "datetime": "2024-10-17T12:51:12.087Z", "lastUpdateTimestamp": *************, "lastTradeTimestamp": null, "symbol": "LTC/USDT", "type": "limit", "timeInForce": "GTC", "postOnly": false, "side": "buy", "price": 54, "stopPrice": null, "amount": 0.23, "cost": null, "average": null, "filled": null, "remaining": null, "status": "open", "fee": {"currency": "USDT", "cost": 0}, "trades": [], "info": {"orderId": "1320439", "clientOrderId": "a3daff06-c8a7-411d-9955-48c13d629218", "clientId": "up132245425", "accountId": "sub1", "status": "NEW", "statusIsFinal": false, "currency1": "LTC", "currency2": "USDT", "side": "BUY", "orderType": "Limit", "timeInForce": "GTC", "comment": null, "rejectCode": null, "rejectReason": null, "initialOnHoldAmountCcy1": null, "initialOnHoldAmountCcy2": "12.********", "executedAmountCcy1": null, "executedAmountCcy2": null, "requestedAmountCcy1": "0.********", "requestedAmountCcy2": null, "feeAmount": "0.********", "feeCurrency": "USDT", "price": "54.00", "averagePrice": null, "clientCreateTimestamp": "*************", "serverCreateTimestamp": "*************", "lastUpdateTimestamp": "*************", "expireTime": null, "effectiveTime": null}, "fees": [{"currency": "USDT", "cost": 0}], "reduceOnly": null, "triggerPrice": null, "takeProfitPrice": null, "stopLossPrice": null}]}], "fetchClosedOrders": [{"description": "detault", "method": "fetchClosedOrders", "input": [null, null, 3], "httpResponse": {"ok": "ok", "data": [{"orderId": "1320439", "clientOrderId": "a3daff06-c8a7-411d-9955-48c13d629218", "clientId": "up132245425", "accountId": "sub1", "status": "CANCELLED", "statusIsFinal": true, "currency1": "LTC", "currency2": "USDT", "side": "BUY", "orderType": "Limit", "timeInForce": "GTC", "comment": null, "rejectCode": null, "rejectReason": null, "initialOnHoldAmountCcy1": null, "initialOnHoldAmountCcy2": "12.********", "executedAmountCcy1": null, "executedAmountCcy2": null, "requestedAmountCcy1": "0.********", "requestedAmountCcy2": null, "feeAmount": "0.********", "feeCurrency": "USDT", "price": "54.00", "averagePrice": null, "clientCreateTimestamp": "*************", "serverCreateTimestamp": "*************", "lastUpdateTimestamp": "*************", "expireTime": null, "effectiveTime": null}, {"orderId": "1320433", "clientOrderId": "7361b0fc-36f8-4d4c-9965-244890986fa3", "clientId": "up132245425", "accountId": "sub1", "status": "FILLED", "statusIsFinal": true, "currency1": "LTC", "currency2": "USDT", "side": "SELL", "orderType": "Market", "timeInForce": null, "comment": null, "rejectCode": null, "rejectReason": null, "initialOnHoldAmountCcy1": "0.********", "initialOnHoldAmountCcy2": null, "executedAmountCcy1": "0.********", "executedAmountCcy2": "16.********", "requestedAmountCcy1": "0.********", "requestedAmountCcy2": null, "feeAmount": "0.********", "feeCurrency": "USDT", "price": null, "averagePrice": "71.20", "clientCreateTimestamp": "*************", "serverCreateTimestamp": "*************", "lastUpdateTimestamp": "*************", "expireTime": null, "effectiveTime": null}, {"orderId": "1320428", "clientOrderId": "805551cb-109d-4c57-b639-8a721ababa1c", "clientId": "up132245425", "accountId": "sub1", "status": "FILLED", "statusIsFinal": true, "currency1": "LTC", "currency2": "USDT", "side": "BUY", "orderType": "Market", "timeInForce": null, "comment": null, "rejectCode": null, "rejectReason": null, "initialOnHoldAmountCcy1": null, "initialOnHoldAmountCcy2": "16.********", "executedAmountCcy1": "0.********", "executedAmountCcy2": "16.********", "requestedAmountCcy1": "0.********", "requestedAmountCcy2": null, "feeAmount": "0.********", "feeCurrency": "USDT", "price": null, "averagePrice": "71.22", "clientCreateTimestamp": "*************", "serverCreateTimestamp": "*************", "lastUpdateTimestamp": "*************", "expireTime": null, "effectiveTime": null}]}, "parsedResponse": [{"id": "1320428", "clientOrderId": "805551cb-109d-4c57-b639-8a721ababa1c", "timestamp": *************, "datetime": "2024-10-17T12:41:04.453Z", "lastUpdateTimestamp": *************, "lastTradeTimestamp": null, "symbol": "LTC/USDT", "type": "market", "timeInForce": "IOC", "postOnly": null, "side": "buy", "price": 71.22, "stopPrice": null, "amount": 0.23, "cost": 16.3806, "average": 71.22, "filled": 0.23, "remaining": 0, "status": "closed", "fee": {"currency": "USDT", "cost": 0.0409515}, "trades": [], "info": {"orderId": "1320428", "clientOrderId": "805551cb-109d-4c57-b639-8a721ababa1c", "clientId": "up132245425", "accountId": "sub1", "status": "FILLED", "statusIsFinal": true, "currency1": "LTC", "currency2": "USDT", "side": "BUY", "orderType": "Market", "timeInForce": null, "comment": null, "rejectCode": null, "rejectReason": null, "initialOnHoldAmountCcy1": null, "initialOnHoldAmountCcy2": "16.********", "executedAmountCcy1": "0.********", "executedAmountCcy2": "16.********", "requestedAmountCcy1": "0.********", "requestedAmountCcy2": null, "feeAmount": "0.********", "feeCurrency": "USDT", "price": null, "averagePrice": "71.22", "clientCreateTimestamp": "*************", "serverCreateTimestamp": "*************", "lastUpdateTimestamp": "*************", "expireTime": null, "effectiveTime": null}, "fees": [{"currency": "USDT", "cost": 0.0409515}], "reduceOnly": null, "triggerPrice": null, "takeProfitPrice": null, "stopLossPrice": null}, {"id": "1320433", "clientOrderId": "7361b0fc-36f8-4d4c-9965-244890986fa3", "timestamp": *************, "datetime": "2024-10-17T12:42:57.930Z", "lastUpdateTimestamp": *************, "lastTradeTimestamp": null, "symbol": "LTC/USDT", "type": "market", "timeInForce": "IOC", "postOnly": null, "side": "sell", "price": 71.2, "stopPrice": null, "amount": 0.23, "cost": 16.376, "average": 71.2, "filled": 0.23, "remaining": 0, "status": "closed", "fee": {"currency": "USDT", "cost": 0.04094}, "trades": [], "info": {"orderId": "1320433", "clientOrderId": "7361b0fc-36f8-4d4c-9965-244890986fa3", "clientId": "up132245425", "accountId": "sub1", "status": "FILLED", "statusIsFinal": true, "currency1": "LTC", "currency2": "USDT", "side": "SELL", "orderType": "Market", "timeInForce": null, "comment": null, "rejectCode": null, "rejectReason": null, "initialOnHoldAmountCcy1": "0.********", "initialOnHoldAmountCcy2": null, "executedAmountCcy1": "0.********", "executedAmountCcy2": "16.********", "requestedAmountCcy1": "0.********", "requestedAmountCcy2": null, "feeAmount": "0.********", "feeCurrency": "USDT", "price": null, "averagePrice": "71.20", "clientCreateTimestamp": "*************", "serverCreateTimestamp": "*************", "lastUpdateTimestamp": "*************", "expireTime": null, "effectiveTime": null}, "fees": [{"currency": "USDT", "cost": 0.04094}], "reduceOnly": null, "triggerPrice": null, "takeProfitPrice": null, "stopLossPrice": null}, {"id": "1320439", "clientOrderId": "a3daff06-c8a7-411d-9955-48c13d629218", "timestamp": *************, "datetime": "2024-10-17T12:51:12.087Z", "lastUpdateTimestamp": *************, "lastTradeTimestamp": null, "symbol": "LTC/USDT", "type": "limit", "timeInForce": "GTC", "postOnly": false, "side": "buy", "price": 54, "stopPrice": null, "amount": 0.23, "cost": null, "average": null, "filled": null, "remaining": null, "status": "canceled", "fee": {"currency": "USDT", "cost": 0}, "trades": [], "info": {"orderId": "1320439", "clientOrderId": "a3daff06-c8a7-411d-9955-48c13d629218", "clientId": "up132245425", "accountId": "sub1", "status": "CANCELLED", "statusIsFinal": true, "currency1": "LTC", "currency2": "USDT", "side": "BUY", "orderType": "Limit", "timeInForce": "GTC", "comment": null, "rejectCode": null, "rejectReason": null, "initialOnHoldAmountCcy1": null, "initialOnHoldAmountCcy2": "12.********", "executedAmountCcy1": null, "executedAmountCcy2": null, "requestedAmountCcy1": "0.********", "requestedAmountCcy2": null, "feeAmount": "0.********", "feeCurrency": "USDT", "price": "54.00", "averagePrice": null, "clientCreateTimestamp": "*************", "serverCreateTimestamp": "*************", "lastUpdateTimestamp": "*************", "expireTime": null, "effectiveTime": null}, "fees": [{"currency": "USDT", "cost": 0}], "reduceOnly": null, "triggerPrice": null, "takeProfitPrice": null, "stopLossPrice": null}]}]}}