{"exchange": "hitbtc", "skipKeys": [], "options": {}, "methods": {"fetchCurrencies": [{"description": "fetchCurrencies", "method": "fetchCurrencies", "input": [], "httpResponse": {"USDT": {"full_name": "<PERSON><PERSON>", "crypto": true, "payin_enabled": true, "payout_enabled": true, "transfer_enabled": true, "transfer_to_wallet_enabled": true, "transfer_to_exchange_enabled": true, "sign": "₮", "account_top_order": "3", "crypto_payment_id_name": "", "crypto_explorer": "http://omniexplorer.info/lookuptx.aspx?txid={tx}", "precision_transfer": "0.000001", "delisted": false, "networks": [{"code": "AVAC", "network_name": "Avalanche C-Chain", "network": "AVAC", "protocol": "ERC-20", "default": false, "is_ens_available": false, "payin_enabled": true, "payout_enabled": true, "precision_payout": "0.************000001", "payout_fee": "10.************", "payout_is_payment_id": false, "payin_payment_id": false, "payin_confirmations": "5", "contract_address": "******************************************", "low_processing_time": "6.252", "high_processing_time": "26.35", "avg_processing_time": "13.**************", "crypto_payment_id_name": "", "crypto_explorer": "", "is_multichain": true, "asset_id": {"contract_address": "******************************************"}}, {"code": "BSC", "network_name": "Binance Smart Chain", "network": "BSC", "protocol": "BEP-20", "default": false, "is_ens_available": true, "payin_enabled": true, "payout_enabled": true, "precision_payout": "0.************000001", "payout_fee": "10.************", "payout_is_payment_id": false, "payin_payment_id": false, "payin_confirmations": "10", "contract_address": "******************************************", "low_processing_time": "2.847", "high_processing_time": "1029.318", "avg_processing_time": "29.264989795918368", "crypto_payment_id_name": "", "crypto_explorer": "https://bscscan.com/tx/{tx}", "is_multichain": true, "asset_id": {"contract_address": "******************************************"}}, {"code": "BTC", "network_name": "Bitcoin", "network": "BTC", "protocol": "OMNI", "default": false, "is_ens_available": false, "payin_enabled": false, "payout_enabled": false, "precision_payout": "0.00000001", "payout_fee": "50.************", "payout_is_payment_id": false, "payin_payment_id": false, "payin_confirmations": "2", "crypto_payment_id_name": "", "crypto_explorer": "", "is_multichain": true}, {"code": "ETH", "network_name": "Ethereum", "network": "ETH", "protocol": "ERC-20", "default": true, "is_ens_available": true, "payin_enabled": true, "payout_enabled": true, "precision_payout": "0.000001", "payout_fee": "19.************", "payout_is_payment_id": false, "payin_payment_id": false, "payin_confirmations": "2", "contract_address": "******************************************", "low_processing_time": "19.963", "high_processing_time": "7832.583", "avg_processing_time": "591.6112631578948", "crypto_payment_id_name": "", "crypto_explorer": "https://etherscan.io/tx/{tx}", "is_multichain": true, "asset_id": {"contract_address": "******************************************"}}, {"code": "NEAR", "network_name": "Near", "network": "NEAR", "protocol": "NEP141", "default": false, "is_ens_available": false, "payin_enabled": true, "payout_enabled": true, "precision_payout": "0.000001", "payout_fee": "10.************", "payout_is_payment_id": false, "payin_payment_id": false, "payin_confirmations": "60", "contract_address": "usdt.tether-token.near", "crypto_payment_id_name": "", "crypto_explorer": "", "is_multichain": true, "asset_id": {"contract_address": "usdt.tether-token.near"}}, {"code": "SOL", "network_name": "Solana", "network": "SOL", "protocol": "SPL", "default": false, "is_ens_available": true, "payin_enabled": true, "payout_enabled": true, "precision_payout": "0.000001", "payout_fee": "10.************", "payout_is_payment_id": false, "payin_payment_id": false, "payin_confirmations": "5", "low_processing_time": "1.491", "high_processing_time": "6076.44", "avg_processing_time": "276.17768749999993", "crypto_payment_id_name": "", "crypto_explorer": "https://solscan.io/tx/{tx}", "is_multichain": true, "asset_id": {"id": "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB"}}, {"code": "TON", "network_name": "<PERSON><PERSON>in", "network": "TON", "protocol": "JETTON", "default": false, "is_ens_available": false, "payin_enabled": true, "payout_enabled": true, "precision_payout": "0.000001", "payout_fee": "10.************", "payout_is_payment_id": true, "payin_payment_id": false, "payin_confirmations": "2", "contract_address": "EQCxE6mUtQJKFnGfaROTKOt1lZbDiiX1kCixRv7Nw2Id_sDs", "address_regex": "^(?:(?:EQ|UQ|kQ)[A-Za-z0-9_-]{46}|0:[0-9a-fA-F]{64})$", "low_processing_time": "24.741", "high_processing_time": "24.741", "avg_processing_time": "24.74099999999999", "crypto_payment_id_name": "", "crypto_explorer": "", "is_multichain": true, "asset_id": {"contract_address": "EQCxE6mUtQJKFnGfaROTKOt1lZbDiiX1kCixRv7Nw2Id_sDs"}}, {"code": "TRX", "network_name": "Tron", "network": "TRX", "protocol": "TRC-20", "default": false, "is_ens_available": true, "payin_enabled": true, "payout_enabled": true, "precision_payout": "0.000001", "payout_fee": "10.************", "payout_is_payment_id": false, "payin_payment_id": false, "payin_confirmations": "3", "low_processing_time": "0.859", "high_processing_time": "1622.715", "avg_processing_time": "42.53982653061226", "crypto_payment_id_name": "", "crypto_explorer": "https://tronscan.org/#/transaction/{tx}", "is_multichain": true, "asset_id": {"id": "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t"}}, {"code": "XTZ", "network_name": "Tezos", "network": "XTZ", "protocol": "FA2", "default": false, "is_ens_available": false, "payin_enabled": true, "payout_enabled": true, "precision_payout": "0.000001", "payout_fee": "10.************", "payout_is_payment_id": false, "payin_payment_id": false, "payin_confirmations": "30", "contract_address": "KT1XnTn74bUtxHfDtBmm2bGZAQfhPbvKWR8o", "crypto_payment_id_name": "", "crypto_explorer": "", "is_multichain": true, "asset_id": {"contract_address": "KT1XnTn74bUtxHfDtBmm2bGZAQfhPbvKWR8o", "id": "0"}}]}}, "parsedResponse": {"USDT": {"info": {"full_name": "<PERSON><PERSON>", "crypto": true, "payin_enabled": true, "payout_enabled": true, "transfer_enabled": true, "transfer_to_wallet_enabled": true, "transfer_to_exchange_enabled": true, "sign": "₮", "account_top_order": "3", "crypto_payment_id_name": "", "crypto_explorer": "http://omniexplorer.info/lookuptx.aspx?txid={tx}", "precision_transfer": "0.000001", "delisted": false, "networks": [{"code": "AVAC", "network_name": "Avalanche C-Chain", "network": "AVAC", "protocol": "ERC-20", "default": false, "is_ens_available": false, "payin_enabled": true, "payout_enabled": true, "precision_payout": "0.************000001", "payout_fee": "10.************", "payout_is_payment_id": false, "payin_payment_id": false, "payin_confirmations": "5", "contract_address": "******************************************", "low_processing_time": "6.252", "high_processing_time": "26.35", "avg_processing_time": "13.**************", "crypto_payment_id_name": "", "crypto_explorer": "", "is_multichain": true, "asset_id": {"contract_address": "******************************************"}}, {"code": "BSC", "network_name": "Binance Smart Chain", "network": "BSC", "protocol": "BEP-20", "default": false, "is_ens_available": true, "payin_enabled": true, "payout_enabled": true, "precision_payout": "0.************000001", "payout_fee": "10.************", "payout_is_payment_id": false, "payin_payment_id": false, "payin_confirmations": "10", "contract_address": "******************************************", "low_processing_time": "2.847", "high_processing_time": "1029.318", "avg_processing_time": "29.264989795918368", "crypto_payment_id_name": "", "crypto_explorer": "https://bscscan.com/tx/{tx}", "is_multichain": true, "asset_id": {"contract_address": "******************************************"}}, {"code": "BTC", "network_name": "Bitcoin", "network": "BTC", "protocol": "OMNI", "default": false, "is_ens_available": false, "payin_enabled": false, "payout_enabled": false, "precision_payout": "0.00000001", "payout_fee": "50.************", "payout_is_payment_id": false, "payin_payment_id": false, "payin_confirmations": "2", "crypto_payment_id_name": "", "crypto_explorer": "", "is_multichain": true}, {"code": "ETH", "network_name": "Ethereum", "network": "ETH", "protocol": "ERC-20", "default": true, "is_ens_available": true, "payin_enabled": true, "payout_enabled": true, "precision_payout": "0.000001", "payout_fee": "19.************", "payout_is_payment_id": false, "payin_payment_id": false, "payin_confirmations": "2", "contract_address": "******************************************", "low_processing_time": "19.963", "high_processing_time": "7832.583", "avg_processing_time": "591.6112631578948", "crypto_payment_id_name": "", "crypto_explorer": "https://etherscan.io/tx/{tx}", "is_multichain": true, "asset_id": {"contract_address": "******************************************"}}, {"code": "NEAR", "network_name": "Near", "network": "NEAR", "protocol": "NEP141", "default": false, "is_ens_available": false, "payin_enabled": true, "payout_enabled": true, "precision_payout": "0.000001", "payout_fee": "10.************", "payout_is_payment_id": false, "payin_payment_id": false, "payin_confirmations": "60", "contract_address": "usdt.tether-token.near", "crypto_payment_id_name": "", "crypto_explorer": "", "is_multichain": true, "asset_id": {"contract_address": "usdt.tether-token.near"}}, {"code": "SOL", "network_name": "Solana", "network": "SOL", "protocol": "SPL", "default": false, "is_ens_available": true, "payin_enabled": true, "payout_enabled": true, "precision_payout": "0.000001", "payout_fee": "10.************", "payout_is_payment_id": false, "payin_payment_id": false, "payin_confirmations": "5", "low_processing_time": "1.491", "high_processing_time": "6076.44", "avg_processing_time": "276.17768749999993", "crypto_payment_id_name": "", "crypto_explorer": "https://solscan.io/tx/{tx}", "is_multichain": true, "asset_id": {"id": "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB"}}, {"code": "TON", "network_name": "<PERSON><PERSON>in", "network": "TON", "protocol": "JETTON", "default": false, "is_ens_available": false, "payin_enabled": true, "payout_enabled": true, "precision_payout": "0.000001", "payout_fee": "10.************", "payout_is_payment_id": true, "payin_payment_id": false, "payin_confirmations": "2", "contract_address": "EQCxE6mUtQJKFnGfaROTKOt1lZbDiiX1kCixRv7Nw2Id_sDs", "address_regex": "^(?:(?:EQ|UQ|kQ)[A-Za-z0-9_-]{46}|0:[0-9a-fA-F]{64})$", "low_processing_time": "24.741", "high_processing_time": "24.741", "avg_processing_time": "24.74099999999999", "crypto_payment_id_name": "", "crypto_explorer": "", "is_multichain": true, "asset_id": {"contract_address": "EQCxE6mUtQJKFnGfaROTKOt1lZbDiiX1kCixRv7Nw2Id_sDs"}}, {"code": "TRX", "network_name": "Tron", "network": "TRX", "protocol": "TRC-20", "default": false, "is_ens_available": true, "payin_enabled": true, "payout_enabled": true, "precision_payout": "0.000001", "payout_fee": "10.************", "payout_is_payment_id": false, "payin_payment_id": false, "payin_confirmations": "3", "low_processing_time": "0.859", "high_processing_time": "1622.715", "avg_processing_time": "42.53982653061226", "crypto_payment_id_name": "", "crypto_explorer": "https://tronscan.org/#/transaction/{tx}", "is_multichain": true, "asset_id": {"id": "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t"}}, {"code": "XTZ", "network_name": "Tezos", "network": "XTZ", "protocol": "FA2", "default": false, "is_ens_available": false, "payin_enabled": true, "payout_enabled": true, "precision_payout": "0.000001", "payout_fee": "10.************", "payout_is_payment_id": false, "payin_payment_id": false, "payin_confirmations": "30", "contract_address": "KT1XnTn74bUtxHfDtBmm2bGZAQfhPbvKWR8o", "crypto_payment_id_name": "", "crypto_explorer": "", "is_multichain": true, "asset_id": {"contract_address": "KT1XnTn74bUtxHfDtBmm2bGZAQfhPbvKWR8o", "id": "0"}}]}, "id": "USDT", "numericId": null, "code": "USDT", "precision": 1e-06, "type": null, "name": "<PERSON><PERSON>", "active": true, "deposit": true, "withdraw": true, "fee": 10, "fees": {}, "networks": {"ERC-20": {"info": {"code": "ETH", "network_name": "Ethereum", "network": "ETH", "protocol": "ERC-20", "default": true, "is_ens_available": true, "payin_enabled": true, "payout_enabled": true, "precision_payout": "0.000001", "payout_fee": "19.************", "payout_is_payment_id": false, "payin_payment_id": false, "payin_confirmations": "2", "contract_address": "******************************************", "low_processing_time": "19.963", "high_processing_time": "7832.583", "avg_processing_time": "591.6112631578948", "crypto_payment_id_name": "", "crypto_explorer": "https://etherscan.io/tx/{tx}", "is_multichain": true, "asset_id": {"contract_address": "******************************************"}}, "id": "ERC-20", "network": "ERC-20", "active": true, "fee": 19, "deposit": true, "withdraw": true, "precision": 1e-06, "limits": {"withdraw": {"min": null, "max": null}}}, "BEP-20": {"info": {"code": "BSC", "network_name": "Binance Smart Chain", "network": "BSC", "protocol": "BEP-20", "default": false, "is_ens_available": true, "payin_enabled": true, "payout_enabled": true, "precision_payout": "0.************000001", "payout_fee": "10.************", "payout_is_payment_id": false, "payin_payment_id": false, "payin_confirmations": "10", "contract_address": "******************************************", "low_processing_time": "2.847", "high_processing_time": "1029.318", "avg_processing_time": "29.264989795918368", "crypto_payment_id_name": "", "crypto_explorer": "https://bscscan.com/tx/{tx}", "is_multichain": true, "asset_id": {"contract_address": "******************************************"}}, "id": "BEP-20", "network": "BEP-20", "active": true, "fee": 10, "deposit": true, "withdraw": true, "precision": 1e-18, "limits": {"withdraw": {"min": null, "max": null}}}, "OMNI": {"info": {"code": "BTC", "network_name": "Bitcoin", "network": "BTC", "protocol": "OMNI", "default": false, "is_ens_available": false, "payin_enabled": false, "payout_enabled": false, "precision_payout": "0.00000001", "payout_fee": "50.************", "payout_is_payment_id": false, "payin_payment_id": false, "payin_confirmations": "2", "crypto_payment_id_name": "", "crypto_explorer": "", "is_multichain": true}, "id": "OMNI", "network": "OMNI", "active": false, "fee": 50, "deposit": false, "withdraw": false, "precision": 1e-08, "limits": {"withdraw": {"min": null, "max": null}}}, "NEP141": {"info": {"code": "NEAR", "network_name": "Near", "network": "NEAR", "protocol": "NEP141", "default": false, "is_ens_available": false, "payin_enabled": true, "payout_enabled": true, "precision_payout": "0.000001", "payout_fee": "10.************", "payout_is_payment_id": false, "payin_payment_id": false, "payin_confirmations": "60", "contract_address": "usdt.tether-token.near", "crypto_payment_id_name": "", "crypto_explorer": "", "is_multichain": true, "asset_id": {"contract_address": "usdt.tether-token.near"}}, "id": "NEP141", "network": "NEP141", "active": true, "fee": 10, "deposit": true, "withdraw": true, "precision": 1e-06, "limits": {"withdraw": {"min": null, "max": null}}}, "SPL": {"info": {"code": "SOL", "network_name": "Solana", "network": "SOL", "protocol": "SPL", "default": false, "is_ens_available": true, "payin_enabled": true, "payout_enabled": true, "precision_payout": "0.000001", "payout_fee": "10.************", "payout_is_payment_id": false, "payin_payment_id": false, "payin_confirmations": "5", "low_processing_time": "1.491", "high_processing_time": "6076.44", "avg_processing_time": "276.17768749999993", "crypto_payment_id_name": "", "crypto_explorer": "https://solscan.io/tx/{tx}", "is_multichain": true, "asset_id": {"id": "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB"}}, "id": "SPL", "network": "SPL", "active": true, "fee": 10, "deposit": true, "withdraw": true, "precision": 1e-06, "limits": {"withdraw": {"min": null, "max": null}}}, "JETTON": {"info": {"code": "TON", "network_name": "<PERSON><PERSON>in", "network": "TON", "protocol": "JETTON", "default": false, "is_ens_available": false, "payin_enabled": true, "payout_enabled": true, "precision_payout": "0.000001", "payout_fee": "10.************", "payout_is_payment_id": true, "payin_payment_id": false, "payin_confirmations": "2", "contract_address": "EQCxE6mUtQJKFnGfaROTKOt1lZbDiiX1kCixRv7Nw2Id_sDs", "address_regex": "^(?:(?:EQ|UQ|kQ)[A-Za-z0-9_-]{46}|0:[0-9a-fA-F]{64})$", "low_processing_time": "24.741", "high_processing_time": "24.741", "avg_processing_time": "24.74099999999999", "crypto_payment_id_name": "", "crypto_explorer": "", "is_multichain": true, "asset_id": {"contract_address": "EQCxE6mUtQJKFnGfaROTKOt1lZbDiiX1kCixRv7Nw2Id_sDs"}}, "id": "JETTON", "network": "JETTON", "active": true, "fee": 10, "deposit": true, "withdraw": true, "precision": 1e-06, "limits": {"withdraw": {"min": null, "max": null}}}, "TRC-20": {"info": {"code": "TRX", "network_name": "Tron", "network": "TRX", "protocol": "TRC-20", "default": false, "is_ens_available": true, "payin_enabled": true, "payout_enabled": true, "precision_payout": "0.000001", "payout_fee": "10.************", "payout_is_payment_id": false, "payin_payment_id": false, "payin_confirmations": "3", "low_processing_time": "0.859", "high_processing_time": "1622.715", "avg_processing_time": "42.53982653061226", "crypto_payment_id_name": "", "crypto_explorer": "https://tronscan.org/#/transaction/{tx}", "is_multichain": true, "asset_id": {"id": "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t"}}, "id": "TRC-20", "network": "TRC-20", "active": true, "fee": 10, "deposit": true, "withdraw": true, "precision": 1e-06, "limits": {"withdraw": {"min": null, "max": null}}}, "FA2": {"info": {"code": "XTZ", "network_name": "Tezos", "network": "XTZ", "protocol": "FA2", "default": false, "is_ens_available": false, "payin_enabled": true, "payout_enabled": true, "precision_payout": "0.000001", "payout_fee": "10.************", "payout_is_payment_id": false, "payin_payment_id": false, "payin_confirmations": "30", "contract_address": "KT1XnTn74bUtxHfDtBmm2bGZAQfhPbvKWR8o", "crypto_payment_id_name": "", "crypto_explorer": "", "is_multichain": true, "asset_id": {"contract_address": "KT1XnTn74bUtxHfDtBmm2bGZAQfhPbvKWR8o", "id": "0"}}, "id": "FA2", "network": "FA2", "active": true, "fee": 10, "deposit": true, "withdraw": true, "precision": 1e-06, "limits": {"withdraw": {"min": null, "max": null}}}}, "limits": {"amount": {"min": null, "max": null}, "deposit": {"min": null, "max": null}, "withdraw": {"min": null, "max": null}}}}}], "fetchMyTrades": [{"description": "Spot trade", "method": "fetchMyTrades", "input": ["TRX/USDT", null, 1], "httpResponse": [{"id": "2338127619", "order_id": "1162784607158", "client_order_id": "XAO_g_2kfiRcNhOwtEz8YoTAcJGcsWjN", "symbol": "TRXUSDT", "side": "sell", "quantity": "55", "price": "0.1035696", "fee": "0.014240820000", "timestamp": "2024-01-07T02:45:32.642Z", "taker": true}], "parsedResponse": [{"info": {"id": "2338127619", "order_id": "1162784607158", "client_order_id": "XAO_g_2kfiRcNhOwtEz8YoTAcJGcsWjN", "symbol": "TRXUSDT", "side": "sell", "quantity": "55", "price": "0.1035696", "fee": "0.014240820000", "timestamp": "2024-01-07T02:45:32.642Z", "taker": true}, "id": "2338127619", "order": "XAO_g_2kfiRcNhOwtEz8YoTAcJGcsWjN", "timestamp": 1704595532642, "datetime": "2024-01-07T02:45:32.642Z", "symbol": "TRX/USDT", "type": null, "side": "sell", "takerOrMaker": "taker", "price": 0.1035696, "amount": 55, "cost": 5.696328, "fee": {"cost": 0.01424082, "currency": "USDT"}, "fees": [{"cost": 0.01424082, "currency": "USDT"}]}]}, {"description": "Swap trade", "method": "fetchMyTrades", "input": ["TRX/USDT:USDT", null, 1], "httpResponse": [{"id": "942515880", "order_id": "122184140181", "client_order_id": "03703c3c074c1c8d3d1eabde37736035", "symbol": "TRXUSDT_PERP", "side": "sell", "quantity": "1", "price": "0.1009391", "fee": "0.000070657370", "timestamp": "2023-12-19T09:45:17.794Z", "taker": true, "position_id": "235035720", "pnl": "-0.000045500000", "liquidation": false}], "parsedResponse": [{"info": {"id": "942515880", "order_id": "122184140181", "client_order_id": "03703c3c074c1c8d3d1eabde37736035", "symbol": "TRXUSDT_PERP", "side": "sell", "quantity": "1", "price": "0.1009391", "fee": "0.000070657370", "timestamp": "2023-12-19T09:45:17.794Z", "taker": true, "position_id": "235035720", "pnl": "-0.000045500000", "liquidation": false}, "id": "942515880", "order": "03703c3c074c1c8d3d1eabde37736035", "timestamp": 1702979117794, "datetime": "2023-12-19T09:45:17.794Z", "symbol": "TRX/USDT:USDT", "type": null, "side": "sell", "takerOrMaker": "taker", "price": 0.1009391, "amount": 1, "cost": 0.1009391, "fee": {"cost": 7.065737e-05, "currency": "USDT"}, "fees": [{"cost": 7.065737e-05, "currency": "USDT"}]}]}], "fetchClosedOrders": [{"description": "spot closed order", "method": "fetchClosedOrders", "input": ["TRX/USDT", null, 1], "httpResponse": [{"id": "1162784607158", "client_order_id": "XAO_g_2kfiRcNhOwtEz8YoTAcJGcsWjN", "symbol": "TRXUSDT", "side": "sell", "status": "filled", "type": "market", "time_in_force": "FOK", "quantity": "55", "quantity_cumulative": "55", "price": "0.1035696", "price_average": "0.1035696", "created_at": "2024-01-07T02:45:32.642Z", "updated_at": "2024-01-07T02:45:32.642Z"}], "parsedResponse": [{"info": {"id": "1162784607158", "client_order_id": "XAO_g_2kfiRcNhOwtEz8YoTAcJGcsWjN", "symbol": "TRXUSDT", "side": "sell", "status": "filled", "type": "market", "time_in_force": "FOK", "quantity": "55", "quantity_cumulative": "55", "price": "0.1035696", "price_average": "0.1035696", "created_at": "2024-01-07T02:45:32.642Z", "updated_at": "2024-01-07T02:45:32.642Z"}, "id": "XAO_g_2kfiRcNhOwtEz8YoTAcJGcsWjN", "clientOrderId": "XAO_g_2kfiRcNhOwtEz8YoTAcJGcsWjN", "timestamp": 1704595532642, "datetime": "2024-01-07T02:45:32.642Z", "lastTradeTimestamp": null, "lastUpdateTimestamp": null, "symbol": "TRX/USDT", "price": 0.1035696, "amount": 55, "type": "market", "side": "sell", "timeInForce": "FOK", "postOnly": false, "reduceOnly": null, "filled": 55, "remaining": 0, "cost": 5.696328, "status": "closed", "average": 0.1035696, "trades": [], "fee": null, "stopPrice": null, "triggerPrice": null, "takeProfitPrice": null, "stopLossPrice": null, "fees": []}]}], "createOrder": [{"description": "create spot order", "method": "createOrder", "input": ["TRX/USDT", "limit", "buy", 50, 0.05], "httpResponse": {"id": "1175851866285", "client_order_id": "6JLM0TNi5bGneW_kRkeMvB8t_KUHJTTj", "symbol": "TRXUSDT", "side": "buy", "status": "new", "type": "limit", "time_in_force": "GTC", "quantity": "50", "quantity_cumulative": "0", "price": "0.0500000", "post_only": false, "created_at": "2024-01-31T11:51:23.748Z", "updated_at": "2024-01-31T11:51:23.748Z"}, "parsedResponse": {"info": {"id": "1175851866285", "client_order_id": "6JLM0TNi5bGneW_kRkeMvB8t_KUHJTTj", "symbol": "TRXUSDT", "side": "buy", "status": "new", "type": "limit", "time_in_force": "GTC", "quantity": "50", "quantity_cumulative": "0", "price": "0.0500000", "post_only": false, "created_at": "2024-01-31T11:51:23.748Z", "updated_at": "2024-01-31T11:51:23.748Z"}, "id": "6JLM0TNi5bGneW_kRkeMvB8t_KUHJTTj", "clientOrderId": "6JLM0TNi5bGneW_kRkeMvB8t_KUHJTTj", "timestamp": 1706701883748, "datetime": "2024-01-31T11:51:23.748Z", "lastTradeTimestamp": null, "lastUpdateTimestamp": null, "symbol": "TRX/USDT", "price": 0.05, "amount": 50, "type": "limit", "side": "buy", "timeInForce": "GTC", "postOnly": false, "reduceOnly": null, "filled": 0, "remaining": 50, "cost": 0, "status": "open", "average": null, "trades": [], "fee": null, "stopPrice": null, "triggerPrice": null, "takeProfitPrice": null, "stopLossPrice": null, "fees": []}}], "fetchOpenOrders": [{"description": "spot open orders", "method": "fetchOpenOrders", "input": ["TRX/USDT", null, 1], "httpResponse": [{"id": "1175851866285", "client_order_id": "6JLM0TNi5bGneW_kRkeMvB8t_KUHJTTj", "symbol": "TRXUSDT", "side": "buy", "status": "new", "type": "limit", "time_in_force": "GTC", "quantity": "50", "quantity_cumulative": "0", "price": "0.0500000", "post_only": false, "created_at": "2024-01-31T11:51:23.748Z", "updated_at": "2024-01-31T11:51:23.748Z"}], "parsedResponse": [{"info": {"id": "1175851866285", "client_order_id": "6JLM0TNi5bGneW_kRkeMvB8t_KUHJTTj", "symbol": "TRXUSDT", "side": "buy", "status": "new", "type": "limit", "time_in_force": "GTC", "quantity": "50", "quantity_cumulative": "0", "price": "0.0500000", "post_only": false, "created_at": "2024-01-31T11:51:23.748Z", "updated_at": "2024-01-31T11:51:23.748Z"}, "id": "6JLM0TNi5bGneW_kRkeMvB8t_KUHJTTj", "clientOrderId": "6JLM0TNi5bGneW_kRkeMvB8t_KUHJTTj", "timestamp": 1706701883748, "datetime": "2024-01-31T11:51:23.748Z", "lastTradeTimestamp": null, "lastUpdateTimestamp": null, "symbol": "TRX/USDT", "price": 0.05, "amount": 50, "type": "limit", "side": "buy", "timeInForce": "GTC", "postOnly": false, "reduceOnly": null, "filled": 0, "remaining": 50, "cost": 0, "status": "open", "average": null, "trades": [], "fee": null, "stopPrice": null, "triggerPrice": null, "takeProfitPrice": null, "stopLossPrice": null, "fees": []}]}], "fetchTrades": [{"description": "public spot trades", "method": "fetchTrades", "input": ["BTC/USDT", null, 1], "httpResponse": [{"id": "2371822918", "price": "73265.87", "qty": "0.05094", "side": "buy", "timestamp": "2024-03-13T11:00:21.983Z"}], "parsedResponse": [{"info": {"id": "2371822918", "price": "73265.87", "qty": "0.05094", "side": "buy", "timestamp": "2024-03-13T11:00:21.983Z"}, "id": "2371822918", "order": null, "timestamp": 1710327621983, "datetime": "2024-03-13T11:00:21.983Z", "symbol": "BTC/USDT", "type": null, "side": "buy", "takerOrMaker": "taker", "price": 73265.87, "amount": 0.05094, "cost": 3732.1634178, "fee": {"cost": null, "currency": null}, "fees": []}]}], "fetchTicker": [{"description": "public spot ticker", "method": "fetchTicker", "input": ["BTC/USDT"], "httpResponse": {"ask": "73369.04", "bid": "73369.03", "last": "73386.24", "low": "68555.00", "high": "73619.97", "open": "72173.99", "volume": "1319.26278", "volume_quote": "94847393.3889236", "timestamp": "2024-03-13T11:10:43.612Z"}, "parsedResponse": {"symbol": "BTC/USDT", "timestamp": 1710328243612, "datetime": "2024-03-13T11:10:43.612Z", "high": 73619.97, "low": 68555, "bid": 73369.03, "bidVolume": null, "ask": 73369.04, "askVolume": null, "vwap": 71894.23883308798, "open": 72173.99, "close": 73386.24, "last": 73386.24, "previousClose": null, "change": 1212.25, "percentage": 1.6796217030539673, "average": 72780.11, "baseVolume": 1319.26278, "quoteVolume": 94847393.3889236, "markPrice": null, "indexPrice": null, "info": {"ask": "73369.04", "bid": "73369.03", "last": "73386.24", "low": "68555.00", "high": "73619.97", "open": "72173.99", "volume": "1319.26278", "volume_quote": "94847393.3889236", "timestamp": "2024-03-13T11:10:43.612Z"}}}], "fetchOHLCV": [{"description": "public spot ohlcv", "method": "fetchOHLCV", "input": ["BTC/USDT", "1h", null, 1], "httpResponse": [{"timestamp": "2024-03-13T11:00:00.000Z", "open": "73264.77", "close": "73435.86", "min": "73245.05", "max": "73436.75", "volume": "2.98510", "volume_quote": "218883.0328372"}], "parsedResponse": [[1710327600000, 73264.77, 73436.75, 73245.05, 73435.86, 2.9851]]}], "fetchOpenInterest": [{"description": "fetch open interest", "method": "fetchOpenInterest", "input": ["BTC/USDT:USDT"], "httpResponse": {"contract_type": "perpetual", "mark_price": "96997.13", "index_price": "97003.05", "funding_rate": "-0.000183473092423284", "open_interest": "94.1502", "next_funding_time": "2024-12-20T08:00:00.000Z", "indicative_funding_rate": "-0.000274006550393978", "premium_index": "-0.000987503270808292", "avg_premium_index": "-0.000683473092423284", "interest_rate": "0.0001", "timestamp": "2024-12-20T05:20:21.693Z"}, "parsedResponse": {"symbol": "BTC/USDT:USDT", "openInterestAmount": null, "openInterestValue": 94.1502, "timestamp": 1734672021693, "datetime": "2024-12-20T05:20:21.693Z", "info": {"contract_type": "perpetual", "mark_price": "96997.13", "index_price": "97003.05", "funding_rate": "-0.000183473092423284", "open_interest": "94.1502", "next_funding_time": "2024-12-20T08:00:00.000Z", "indicative_funding_rate": "-0.000274006550393978", "premium_index": "-0.000987503270808292", "avg_premium_index": "-0.000683473092423284", "interest_rate": "0.0001", "timestamp": "2024-12-20T05:20:21.693Z"}, "baseVolume": null, "quoteVolume": null}}], "fetchOpenInterests": [{"description": "fetch linear swap open interests with a symbols argument", "method": "fetchOpenInterests", "input": [["BTC/USDT:USDT"]], "httpResponse": {"BTCUSDT_PERP": {"contract_type": "perpetual", "mark_price": "97163.86", "index_price": "97169.87", "funding_rate": "-0.000183473092423284", "open_interest": "94.1498", "next_funding_time": "2024-12-20T08:00:00.000Z", "indicative_funding_rate": "-0.000273139953764934", "premium_index": "-0.000833428680318160", "avg_premium_index": "-0.000683473092423284", "interest_rate": "0.0001", "timestamp": "2024-12-20T05:18:09.693Z"}}, "parsedResponse": {"BTC/USDT:USDT": {"symbol": "BTC/USDT:USDT", "openInterestAmount": null, "openInterestValue": 94.1498, "timestamp": 1734671889693, "datetime": "2024-12-20T05:18:09.693Z", "info": {"contract_type": "perpetual", "mark_price": "97163.86", "index_price": "97169.87", "funding_rate": "-0.000183473092423284", "open_interest": "94.1498", "next_funding_time": "2024-12-20T08:00:00.000Z", "indicative_funding_rate": "-0.000273139953764934", "premium_index": "-0.000833428680318160", "avg_premium_index": "-0.000683473092423284", "interest_rate": "0.0001", "timestamp": "2024-12-20T05:18:09.693Z"}, "baseVolume": null, "quoteVolume": null}}}]}}