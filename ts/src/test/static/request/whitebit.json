{"exchange": "whitebit", "skipKeys": ["nonce", "clientOrderId"], "outputType": "json", "methods": {"fetchCurrencies": [{"description": "fetchCurrencies", "method": "fetchCurrencies", "url": "https://whitebit.com/api/v4/public/assets", "input": [], "output": null}], "createOrder": [{"description": "Spot limit buy", "method": "createOrder", "url": "https://whitebit.com/api/v4/order/new", "input": ["DOGE/USDT", "limit", "buy", 100, 0.09], "output": "{\"request\":\"/api/v4/order/new\",\"nonce\":\"*************\",\"market\":\"DOGE_USDT\",\"side\":\"buy\",\"amount\":\"100\",\"clientOrderId\":\"ccxt79f2861ea4fbae32\",\"price\":\"0.09\"}"}, {"description": "Spot market buy", "method": "createOrder", "url": "https://whitebit.com/api/v4/order/stock_market", "input": ["DOGE/USDT", "market", "buy", 100], "output": "{\"request\":\"/api/v4/order/stock_market\",\"nonce\":\"*************\",\"market\":\"DOGE_USDT\",\"side\":\"buy\",\"amount\":\"100\",\"clientOrderId\":\"ccxt79f2861ea4fbae32\"}"}, {"description": "Swap limit buy", "method": "createOrder", "url": "https://whitebit.com/api/v4/order/collateral/limit", "input": ["DOGE/USDT:USDT", "limit", "buy", 100, 0.09], "output": "{\"request\":\"/api/v4/order/collateral/limit\",\"nonce\":\"*************\",\"market\":\"DOGE_PERP\",\"side\":\"buy\",\"amount\":\"100\",\"clientOrderId\":\"ccxt79f2861ea4fbae32\",\"price\":\"0.09\"}"}, {"description": "Swap market buy", "method": "createOrder", "url": "https://whitebit.com/api/v4/order/collateral/market", "input": ["DOGE/USDT:USDT", "market", "buy", 100], "output": "{\"request\":\"/api/v4/order/collateral/market\",\"nonce\":\"*************\",\"market\":\"DOGE_PERP\",\"side\":\"buy\",\"amount\":\"100\",\"clientOrderId\":\"ccxt79f2861ea4fbae32\"}"}, {"description": "Spot market buy spot order", "method": "createOrder", "url": "https://whitebit.com/api/v4/order/stop_market", "input": ["DOGE/USDT", "market", "buy", 100, null, {"triggerPrice": "0.08"}], "output": "{\"request\":\"/api/v4/order/stop_market\",\"nonce\":\"*************\",\"market\":\"DOGE_USDT\",\"side\":\"buy\",\"amount\":\"100\",\"clientOrderId\":\"ccxt79f2861ea4fbae32\",\"activation_price\":\"0.08\"}"}, {"description": "Spot limit buy spot order", "method": "createOrder", "url": "https://whitebit.com/api/v4/order/stop_limit", "input": ["DOGE/USDT", "limit", "buy", 100, 0.07, {"triggerPrice": "0.08"}], "output": "{\"request\":\"/api/v4/order/stop_limit\",\"nonce\":\"*************\",\"market\":\"DOGE_USDT\",\"side\":\"buy\",\"amount\":\"100\",\"clientOrderId\":\"ccxt79f2861ea4fbae32\",\"activation_price\":\"0.08\",\"price\":\"0.07\"}"}, {"description": "Swap market buy spot order", "method": "createOrder", "url": "https://whitebit.com/api/v4/order/collateral/trigger-market", "input": ["DOGE/USDT:USDT", "market", "buy", 100, null, {"triggerPrice": "0.08"}], "output": "{\"request\":\"/api/v4/order/collateral/trigger-market\",\"nonce\":\"*************\",\"market\":\"DOGE_PERP\",\"side\":\"buy\",\"amount\":\"100\",\"clientOrderId\":\"ccxt79f2861ea4fbae32\",\"activation_price\":\"0.08\"}"}, {"description": "market buy +cost", "method": "createOrder", "url": "https://whitebit.com/api/v4/order/market", "input": ["BTC/USDT", "market", "buy", 0, null, {"cost": 7.1}], "output": "{\"request\":\"/api/v4/order/market\",\"nonce\":\"1716315544734\",\"market\":\"BTC_USDT\",\"side\":\"buy\",\"amount\":\"7.1\",\"clientOrderId\":\"ccxt014ab638a4a5883b\"}"}], "editOrder": [{"description": "Spot limit buy", "method": "editOrder", "url": "https://whitebit.com/api/v4/order/modify", "input": ["123456789", "DOGE/USDT", "limit", "buy", 100, 0.09], "output": "{\"request\":\"/api/v4/order/modify\",\"nonce\":\"*************\",\"market\":\"DOGE_USDT\",\"orderId\":\"123456789\",\"amount\":\"100\",\"price\":\"0.09\"}"}, {"description": "Swap limit buy", "method": "editOrder", "url": "https://whitebit.com/api/v4/order/modify", "input": ["123456789", "DOGE/USDT:USDT", "limit", "buy", 100, 0.09], "output": "{\"request\":\"/api/v4/order/modify\",\"nonce\":\"*************\",\"market\":\"DOGE_PERP\",\"orderId\":\"123456789\",\"amount\":\"100\",\"price\":\"0.09\"}"}, {"description": "Spot market buy spot order", "method": "editOrder", "url": "https://whitebit.com/api/v4/order/modify", "input": ["123456789", "DOGE/USDT", "market", "buy", 100, null, {"triggerPrice": "0.08"}], "output": "{\"request\":\"/api/v4/order/modify\",\"nonce\":\"*************\",\"market\":\"DOGE_USDT\",\"orderId\":\"123456789\",\"total\":\"100\",\"activation_price\":\"0.08\"}"}, {"description": "Spot limit buy spot order", "method": "editOrder", "url": "https://whitebit.com/api/v4/order/modify", "input": ["123456789", "DOGE/USDT", "limit", "buy", 100, 0.07, {"triggerPrice": "0.08"}], "output": "{\"request\":\"/api/v4/order/modify\",\"nonce\":\"*************\",\"market\":\"DOGE_USDT\",\"orderId\":\"123456789\",\"amount\":\"100\",\"activation_price\":\"0.08\",\"price\":\"0.07\"}"}, {"description": "Swap market buy spot order", "method": "editOrder", "url": "https://whitebit.com/api/v4/order/modify", "input": ["123456789", "DOGE/USDT:USDT", "market", "buy", 100, null, {"triggerPrice": "0.08"}], "output": "{\"request\":\"/api/v4/order/modify\",\"nonce\":\"*************\",\"market\":\"DOGE_PERP\",\"orderId\":\"123456789\",\"total\":\"100\",\"activation_price\":\"0.08\"}"}], "cancelAllOrders": [{"description": "cancel all orders - spot", "method": "cancelAllOrders", "url": "https://whitebit.com/api/v4/order/cancel/all", "input": ["BTC/USDT"], "output": "{\"request\":\"/api/v4/order/cancel/all\",\"nonce\":\"*************\",\"market\":\"BTC_USDT\",\"type\":[\"spot\"]}"}, {"description": "cancel all orders - margin", "method": "cancelAllOrders", "url": "https://whitebit.com/api/v4/order/cancel/all", "input": ["BTC/USDT", {"isMargin": true}], "output": "{\"request\":\"/api/v4/order/cancel/all\",\"nonce\":\"*************\",\"market\":\"BTC_USDT\",\"type\":[\"margin\"]}"}, {"description": "cancel all orders - swap", "method": "cancelAllOrders", "url": "https://whitebit.com/api/v4/order/cancel/all", "input": ["BTC/USDT:USDT"], "output": "{\"request\":\"/api/v4/order/cancel/all\",\"nonce\":\"*************\",\"market\":\"BTC_PERP\",\"type\":[\"futures\"]}"}], "cancelAllOrdersAfter": [{"description": "Cancel all orders after", "method": "cancelAllOrdersAfter", "url": "https://whitebit.com/api/v4/order/kill-switch", "input": [10000, {"symbol": "BTC/USDT"}], "output": "{\"request\":\"/api/v4/order/kill-switch\",\"nonce\":\"*************\",\"market\":\"BTC_USDT\",\"timeout\":\"10\"}"}, {"description": "Close cancel all orders after", "method": "cancelAllOrdersAfter", "url": "https://whitebit.com/api/v4/order/kill-switch", "input": [0, {"symbol": "BTC/USDT"}], "output": "{\"request\":\"/api/v4/order/kill-switch\",\"nonce\":\"*************\",\"market\":\"BTC_USDT\",\"timeout\":\"null\"}"}], "fetchBalance": [{"description": "Fetch Balance - trade", "method": "fetchBalance", "url": "https://whitebit.com/api/v4/trade-account/balance", "input": [], "output": "{\"request\":\"/api/v4/trade-account/balance\",\"nonce\":\"*************\"}"}, {"description": "Fetch Balance - main", "method": "fetchBalance", "url": "https://whitebit.com/api/v4/main-account/balance", "input": [{"account": "main"}], "output": "{\"request\":\"/api/v4/main-account/balance\",\"nonce\":\"*************\"}"}, {"description": "Fetch Balance - trade", "method": "fetchBalance", "url": "https://whitebit.com/api/v4/collateral-account/balance", "input": [{"type": "swap"}], "output": "{\"request\":\"/api/v4/collateral-account/balance\",\"nonce\":\"*************\"}"}], "fetchDepositAddress": [{"description": "<PERSON>tch Deposit Address", "method": "fetchDepositAddress", "url": "https://whitebit.com/api/v4/main-account/fiat-deposit-url", "input": ["USD", {"provider": "provider", "amount": 10, "uniqueId": "uniqueId"}], "output": "{\"request\":\"/api/v4/main-account/fiat-deposit-url\",\"nonce\":\"*************\",\"ticker\":\"USD\",\"provider\":\"provider\",\"amount\":10,\"uniqueId\":\"uniqueId\"}"}, {"description": "<PERSON>tch Deposit Address", "method": "fetchDepositAddress", "url": "https://whitebit.com/api/v4/main-account/address", "input": ["BTC"], "output": "{\"request\":\"/api/v4/main-account/address\",\"nonce\":\"*************\",\"ticker\":\"BTC\"}"}], "fetchTime": [{"description": "fetchTime", "method": "fetchTime", "url": "https://whitebit.com/api/v4/public/time", "input": []}], "fetchTrades": [{"description": "spot fetchTrades", "method": "fetchTrades", "url": "https://whitebit.com/api/v4/public/trades/BTC_USDT", "input": ["BTC/USDT"]}, {"description": "swap fetchTrades", "method": "fetchTrades", "url": "https://whitebit.com/api/v4/public/trades/BTC_PERP", "input": ["BTC/USDT:USDT"]}], "fetchOrderBook": [{"description": "spot orderbook", "method": "fetchOrderBook", "url": "https://whitebit.com/api/v4/public/orderbook/BTC_USDT", "input": ["BTC/USDT"]}, {"description": "swap orderbook", "method": "fetchOrderBook", "url": "https://whitebit.com/api/v4/public/orderbook/BTC_PERP", "input": ["BTC/USDT:USDT"]}], "fetchTicker": [{"description": "Swap ticker", "method": "fetchTicker", "url": "https://whitebit.com/api/v1/public/ticker?market=BTC_PERP", "input": ["BTC/USDT:USDT"]}, {"description": "spot ticker", "method": "fetchTicker", "url": "https://whitebit.com/api/v1/public/ticker?market=BTC_USDT", "input": ["BTC/USDT"]}], "fetchTickers": [{"description": "fetchTickers using v2", "method": "fetchTickers", "url": "https://whitebit.com/api/v2/public/ticker", "input": [null, {"method": "v2PublicGetTicker"}], "output": null}, {"description": "spot tickers", "method": "fetchTickers", "url": "https://whitebit.com/api/v4/public/ticker", "input": [["BTC/USDT", "ETH/USDT"]]}, {"description": "swap tickers", "method": "fetchTickers", "url": "https://whitebit.com/api/v4/public/ticker", "input": [["BTC/USDT:USDT", "ETH/USDT:USDT"]]}], "fetchOHLCV": [{"description": "spot ohlcv", "method": "fetchOHLCV", "url": "https://whitebit.com/api/v1/public/kline?market=BTC_USDT&interval=1m", "input": ["BTC/USDT"]}, {"description": "swap ohlcv", "method": "fetchOHLCV", "url": "https://whitebit.com/api/v1/public/kline?market=BTC_PERP&interval=1m", "input": ["BTC/USDT:USDT"]}], "fetchFundingRate": [{"description": "fundingRate", "method": "fetchFundingRate", "url": "https://whitebit.com/api/v4/public/futures", "input": ["BTC/USDT:USDT"]}], "fetchDepositsWithdrawals": [{"description": "Fetch Deposits Withdrawals", "method": "fetchDepositsWithdrawals", "url": "https://whitebit.com/api/v4/main-account/history", "input": ["USDT"], "output": "{\"request\":\"/api/v4/main-account/history\",\"nonce\":\"*************\",\"ticker\":\"USDT\"}"}], "fetchConvertQuote": [{"description": "Fetch a conversion quote", "method": "fetchConvertQuote", "url": "https://whitebit.com/api/v4/convert/estimate", "input": ["USDT", "BTC", 4], "output": "{\"request\":\"/api/v4/convert/estimate\",\"nonce\":\"*************\",\"from\":\"USDT\",\"to\":\"BTC\",\"amount\":\"4\",\"direction\":\"from\"}"}], "createConvertTrade": [{"description": "Create a conversion trade", "method": "createConvertTrade", "url": "https://whitebit.com/api/v4/convert/confirm", "input": ["1741105", "USDT", "BTC", 4], "output": "{\"request\":\"/api/v4/convert/confirm\",\"nonce\":\"*************\",\"quoteId\":\"1741105\"}"}], "fetchConvertTradeHistory": [{"description": "Fetch conversion trade history", "method": "fetchConvertTradeHistory", "url": "https://whitebit.com/api/v4/convert/history", "input": ["USDT"], "output": "{\"request\":\"/api/v4/convert/history\",\"nonce\":\"*************\",\"fromTicker\":\"USDT\"}"}], "fetchPosition": [{"description": "fetch a swap position", "method": "fetchPosition", "url": "https://whitebit.com/api/v4/collateral-account/positions/open", "input": ["BTC/USDT:USDT"], "output": "{\"request\":\"/api/v4/collateral-account/positions/open\",\"nonce\":\"*************\",\"symbol\":\"BTC_PERP\"}"}], "fetchPositions": [{"description": "Fetch multiple swap positions", "method": "fetchPositions", "url": "https://whitebit.com/api/v4/collateral-account/positions/open", "input": [], "output": "{\"request\":\"/api/v4/collateral-account/positions/open\",\"nonce\":\"*************\"}"}], "fetchPositionHistory": [{"description": "Fetch position history", "method": "fetchPositionHistory", "url": "https://whitebit.com/api/v4/collateral-account/positions/history", "input": ["BTC/USDT:USDT"], "output": "{\"request\":\"/api/v4/collateral-account/positions/history\",\"nonce\":\"*************\",\"market\":\"BTC_PERP\"}"}]}}