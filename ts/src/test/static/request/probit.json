{"exchange": "probit", "skipKeys": ["start_time", "end_time"], "outputType": "json", "methods": {"fetchCurrencies": [{"description": "fetchCurrencies", "method": "fetchCurrencies", "url": "https://api.probit.com/api/exchange/v1/currency_with_platform", "input": [], "output": null}], "createOrder": [{"description": "Spot limit buy order", "method": "createOrder", "url": "https://api.probit.com/api/exchange/v1/new_order", "input": ["LTC/USDT", "limit", "buy", 0.1, 50], "output": "{\"market_id\":\"LTC-USDT\",\"type\":\"limit\",\"side\":\"buy\",\"time_in_force\":\"gtc\",\"limit_price\":\"50\",\"quantity\":\"0.1\"}"}, {"description": "Spot market buy order", "method": "createOrder", "url": "https://api.probit.com/api/exchange/v1/new_order", "input": ["LTC/USDT", "market", "buy", 1, 5], "output": "{\"market_id\":\"LTC-USDT\",\"type\":\"market\",\"side\":\"buy\",\"time_in_force\":\"ioc\",\"cost\":\"5\"}"}, {"description": "Spot market sell order", "method": "createOrder", "url": "https://api.probit.com/api/exchange/v1/new_order", "input": ["LTC/USDT", "market", "sell", 0.05], "output": "{\"market_id\":\"LTC-USDT\",\"type\":\"market\",\"side\":\"sell\",\"time_in_force\":\"ioc\",\"quantity\":\"0.05\"}"}, {"description": "Spot market buy with createMarketBuyOrderRequiresPrice set to false", "method": "createOrder", "url": "https://api.probit.com/api/exchange/v1/new_order", "input": ["BTC/USDT", "market", "buy", 10, null, {"createMarketBuyOrderRequiresPrice": false}], "output": "{\"market_id\":\"BTC-USDT\",\"type\":\"market\",\"side\":\"buy\",\"time_in_force\":\"ioc\",\"cost\":\"10\"}"}, {"description": "Spot market buy order using the cost param", "method": "createOrder", "url": "https://api.probit.com/api/exchange/v1/new_order", "input": ["BTC/USDT", "market", "buy", 0, null, {"cost": 10}], "output": "{\"market_id\":\"BTC-USDT\",\"type\":\"market\",\"side\":\"buy\",\"time_in_force\":\"ioc\",\"cost\":\"10\"}"}], "createMarketBuyOrderWithCost": [{"description": "Spot market buy order with cost", "method": "createMarketBuyOrderWithCost", "url": "https://api.probit.com/api/exchange/v1/new_order", "input": ["BTC/USDT", 10], "output": "{\"market_id\":\"BTC-USDT\",\"type\":\"market\",\"side\":\"buy\",\"time_in_force\":\"ioc\",\"cost\":\"10\"}"}], "fetchTime": [{"description": "fetchTime", "method": "fetchTime", "url": "https://api.probit.com/api/exchange/v1/time", "input": []}], "fetchTrades": [{"description": "spot fetchTrades", "method": "fetchTrades", "url": "https://api.probit.com/api/exchange/v1/trade?market_id=BTC-USDT&start_time=1970-01-01T00%3A00%3A00.000Z&end_time=2024-03-09T14%3A03%3A08.588Z&limit=1000", "input": ["BTC/USDT"]}], "fetchOrderBook": [{"description": "spot orderbook", "method": "fetchOrderBook", "url": "https://api.probit.com/api/exchange/v1/order_book?market_id=BTC-USDT", "input": ["BTC/USDT"]}], "fetchTicker": [{"description": "spot ticker", "method": "fetchTicker", "url": "https://api.probit.com/api/exchange/v1/ticker?market_ids=BTC-USDT", "input": ["BTC/USDT"]}], "fetchTickers": [{"description": "spot tickers", "method": "fetchTickers", "url": "https://api.probit.com/api/exchange/v1/ticker?market_ids=BTC-USDT%2CETH-USDT", "input": [["BTC/USDT", "ETH/USDT"]]}], "fetchOHLCV": [{"description": "spot ohlcv", "method": "fetchOHLCV", "url": "https://api.probit.com/api/exchange/v1/candle?market_ids=BTC-USDT&interval=1m&sort=asc&limit=101&start_time=2024-03-09T12%3A23%3A00.000Z&end_time=2024-03-09T14%3A04%3A00.000Z", "input": ["BTC/USDT"]}, {"description": "fetchOHLCV with since", "method": "fetchOHLCV", "url": "https://api.probit.com/api/exchange/v1/candle?market_ids=BTC-USDT&interval=1m&sort=asc&limit=101&start_time=2024-01-01T00%3A00%3A00.000Z&end_time=2024-01-01T01%3A41%3A00.000Z", "input": ["BTC/USDT", "1m", 1704067200000]}, {"description": "fetchOHLCV with limit", "method": "fetchOHLCV", "url": "https://api.probit.com/api/exchange/v1/candle?market_ids=BTC-USDT&interval=1m&sort=asc&limit=5&start_time=2025-01-24T05%3A21%3A00.000Z&end_time=2025-01-24T05%3A25%3A00.000Z", "input": ["BTC/USDT", "1m", null, 4]}, {"description": "fetchOHLCV with until", "method": "fetchOHLCV", "url": "https://api.probit.com/api/exchange/v1/candle?market_ids=BTC-USDT&interval=1m&sort=asc&limit=101&start_time=2023-12-31T22%3A22%3A00.000Z&end_time=2024-01-01T00%3A02%3A00.000Z&until=1704067320000", "input": ["BTC/USDT", "1m", null, null, {"until": 1704067320000}]}, {"description": "fetchOHLCV with since, and limit", "method": "fetchOHLCV", "url": "https://api.probit.com/api/exchange/v1/candle?market_ids=BTC-USDT&interval=1m&sort=asc&limit=5&start_time=2024-01-01T00%3A00%3A00.000Z&end_time=2024-01-01T00%3A05%3A00.000Z", "input": ["BTC/USDT", "1m", 1704067200000, 4]}, {"description": "fetchOHLCV with since and until", "method": "fetchOHLCV", "url": "https://api.probit.com/api/exchange/v1/candle?market_ids=BTC-USDT&interval=1m&sort=asc&limit=101&start_time=2024-01-01T00%3A00%3A00.000Z&end_time=2024-01-01T00%3A02%3A00.000Z&until=1704067320000", "input": ["BTC/USDT", "1m", 1704067200000, null, {"until": 1704067320000}]}, {"description": "fetchOHLCV with limit and until", "method": "fetchOHLCV", "url": "https://api.probit.com/api/exchange/v1/candle?market_ids=BTC-USDT&interval=1m&sort=asc&limit=5&start_time=2023-12-31T23%3A58%3A00.000Z&end_time=2024-01-01T00%3A02%3A00.000Z&until=1704067320000", "input": ["BTC/USDT", "1m", null, 4, {"until": 1704067320000}]}, {"description": "fetchOHLCV with until - since less than limit", "method": "fetchOHLCV", "url": "https://api.probit.com/api/exchange/v1/candle?market_ids=BTC-USDT&interval=1m&sort=asc&limit=3&start_time=2024-01-01T00%3A00%3A00.000Z&end_time=2024-01-01T00%3A02%3A00.000Z&until=1704067320000", "input": ["BTC/USDT", "1m", 1704067200000, 2, {"until": 1704067320000}]}, {"description": "fetchOHLCV with until - since equal to limit", "method": "fetchOHLCV", "url": "https://api.probit.com/api/exchange/v1/candle?market_ids=BTC-USDT&interval=1m&sort=asc&limit=4&start_time=2024-01-01T00%3A00%3A00.000Z&end_time=2024-01-01T00%3A02%3A00.000Z&until=1704067320000", "input": ["BTC/USDT", "1m", 1704067200000, 3, {"until": 1704067320000}]}, {"description": "fetchOHLCV with until - since greater than limit", "method": "fetchOHLCV", "url": "https://api.probit.com/api/exchange/v1/candle?market_ids=BTC-USDT&interval=1m&sort=asc&limit=5&start_time=2024-01-01T00%3A00%3A00.000Z&end_time=2024-01-01T00%3A02%3A00.000Z&until=1704067320000", "input": ["BTC/USDT", "1m", 1704067200000, 4, {"until": 1704067320000}]}]}}