{"exchange": "latoken", "skipKeys": ["timestamp", "clientOrderId"], "outputType": "json", "methods": {"fetchCurrencies": [{"description": "fetchCurrencies", "method": "fetchCurrencies", "url": "https://api.latoken.com/v2/currency", "input": [], "output": null}], "fetchOrders": [{"description": "Spot orders", "method": "fetchOrders", "url": "https://api.latoken.com/v2/auth/order/pair/0d02fdfc-9555-4cd9-8398-006003033a9e/0c3a106d-bde3-4c13-a26e-3fd2394529e5", "input": ["LTC/USDT"]}], "fetchMyTrades": [{"description": "Spot private trades", "method": "fetchMyTrades", "url": "https://api.latoken.com/v2/auth/trade?limit=5", "input": [null, 1699457638000, 5]}, {"description": "Spot private trades", "method": "fetchMyTrades", "url": "https://api.latoken.com/v2/auth/trade/pair/0d02fdfc-9555-4cd9-8398-006003033a9e/0c3a106d-bde3-4c13-a26e-3fd2394529e5?limit=5", "input": ["LTC/USDT", 1699457638000, 5]}], "fetchOpenOrders": [{"description": "Spot open orders", "method": "fetchOpenOrders", "url": "https://api.latoken.com/v2/auth/order/pair/0d02fdfc-9555-4cd9-8398-006003033a9e/0c3a106d-bde3-4c13-a26e-3fd2394529e5/active", "input": ["LTC/USDT"]}], "cancelAllOrders": [{"description": "Cancel spot orders", "method": "cancelAllOrders", "url": "https://api.latoken.com/v2/auth/order/cancelAll/0d02fdfc-9555-4cd9-8398-006003033a9e/0c3a106d-bde3-4c13-a26e-3fd2394529e5", "input": ["LTC/USDT"], "output": "{}"}], "fetchBalance": [{"description": "Fetch spot Balance", "method": "fetchBalance", "url": "https://api.latoken.com/v2/auth/account?type=spot", "input": [{"type": "spot"}]}, {"description": "Fetch swap Balance", "method": "fetchBalance", "url": "https://api.latoken.com/v2/auth/account?type=swap", "input": [{"type": "swap"}]}], "createOrder": [{"description": "Spot limit buy", "method": "createOrder", "url": "https://api.latoken.com/v2/auth/order/place", "input": ["LTC/USDT", "limit", "buy", 0.1, 50], "output": "{\"baseCurrency\":\"0d02fdfc-9555-4cd9-8398-006003033a9e\",\"quoteCurrency\":\"0c3a106d-bde3-4c13-a26e-3fd2394529e5\",\"side\":\"BUY\",\"condition\":\"GTC\",\"type\":\"LIMIT\",\"clientOrderId\":\"377af565-40d4-4653-af1f-ec9074aaac32\",\"quantity\":\"0.1\",\"timestamp\":**********,\"price\":\"50\"}"}], "fetchTradingFee": [{"description": "Fetch Trading Fee - private", "method": "fetchTradingFee", "url": "https://api.latoken.com/v2/auth/trade/fee/0d02fdfc-9555-4cd9-8398-006003033a9e/0c3a106d-bde3-4c13-a26e-3fd2394529e5", "input": ["LTC/USDT"]}, {"description": "Fetch Trading Fee - public", "method": "fetchTradingFee", "url": "https://api.latoken.com/v2/trade/fee/0d02fdfc-9555-4cd9-8398-006003033a9e/0c3a106d-bde3-4c13-a26e-3fd2394529e5", "input": ["LTC/USDT", {"method": "fetchPublicTradingFee"}]}], "transfer": [{"description": "transfer - email", "method": "transfer", "url": "https://api.latoken.com/v2/auth/transfer/email", "input": ["USDT", 1, "", "<EMAIL>"], "output": "{\"currency\":\"0c3a106d-bde3-4c13-a26e-3fd2394529e5\",\"recipient\":\"<EMAIL>\",\"value\":\"1\"}"}, {"description": "transfer - email", "method": "transfer", "url": "https://api.latoken.com/v2/auth/transfer/id", "input": ["USDT", 1, "", "e6fc4ace-7750-44e4-b7e9-6af038ac7107"], "output": "{\"currency\":\"0c3a106d-bde3-4c13-a26e-3fd2394529e5\",\"recipient\":\"e6fc4ace-7750-44e4-b7e9-6af038ac7107\",\"value\":\"1\"}"}, {"description": "transfer - phone", "method": "transfer", "url": "https://api.latoken.com/v2/auth/transfer/phone", "input": ["USDT", 1, "", "1234567890"], "output": "{\"currency\":\"0c3a106d-bde3-4c13-a26e-3fd2394529e5\",\"recipient\":\"1234567890\",\"value\":\"1\"}"}], "fetchTime": [{"description": "fetchTime", "method": "fetchTime", "url": "https://api.latoken.com/v2/time", "input": []}], "fetchTrades": [{"description": "spot fetchTrades", "method": "fetchTrades", "url": "https://api.latoken.com/v2/trade/history/92151d82-df98-4d88-9a4d-284fa9eca49f/0c3a106d-bde3-4c13-a26e-3fd2394529e5", "input": ["BTC/USDT"]}], "fetchOrderBook": [{"description": "spot orderbook", "method": "fetchOrderBook", "url": "https://api.latoken.com/v2/book/92151d82-df98-4d88-9a4d-284fa9eca49f/0c3a106d-bde3-4c13-a26e-3fd2394529e5", "input": ["BTC/USDT"]}], "fetchTicker": [{"description": "spot ticker", "method": "fetchTicker", "url": "https://api.latoken.com/v2/ticker/92151d82-df98-4d88-9a4d-284fa9eca49f/0c3a106d-bde3-4c13-a26e-3fd2394529e5", "input": ["BTC/USDT"]}], "fetchTickers": [{"description": "spot tickers", "method": "fetchTickers", "url": "https://api.latoken.com/v2/ticker", "input": [["BTC/USDT", "ETH/USDT"]]}]}}