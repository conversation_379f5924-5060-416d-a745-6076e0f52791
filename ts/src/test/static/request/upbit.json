{"exchange": "upbit", "skipKeys": [], "outputType": "json", "methods": {"fetchMarkets": [{"description": "fetchMarkets", "method": "fetchMarkets", "url": "https://api.upbit.com/v1/market/all", "input": []}], "withdraw": [{"description": "withdraw usdt", "method": "withdraw", "url": "https://api.upbit.com/v1/withdraws/coin", "input": ["USDT", 1, "1234", null, {"network": "TRX"}], "output": "{\"amount\":1,\"net_type\":\"TRX\",\"address\":\"1234\",\"currency\":\"USDT\"}"}, {"description": "withdraw krw", "method": "withdraw", "url": "https://api.upbit.com/v1/withdraws/krw", "input": ["KRW", 1, "1234"], "output": "{\"amount\":1}"}], "createOrder": [{"description": "market buy with params.cost: Buying BTC at market price for the params.cost KRW", "method": "createOrder", "url": "https://api.upbit.com/v1/orders", "input": ["BTC/KRW", "market", "buy", 0, 0, {"cost": 5000}], "output": "{\"market\":\"KRW-BTC\",\"side\":\"bid\",\"ord_type\":\"price\",\"price\":\"5000\"}"}, {"description": "market buy with createMarketBuyOrderRequiresPrice = false: Buying BTC at market price for the amount of KRW specified in amount (quote amount)", "method": "createOrder", "options": {"createMarketBuyOrderRequiresPrice": false}, "url": "https://api.upbit.com/v1/orders", "input": ["BTC/KRW", "market", "buy", 5000, 0], "output": "{\"market\":\"KRW-BTC\",\"side\":\"bid\",\"ord_type\":\"price\",\"price\":\"5000\"}"}, {"description": "market buy with createMarketBuyOrderRequiresPrice = false: Buying BTC at the best market price for the amount of KRW specified in amount (quote amount)", "method": "createOrder", "options": {"createMarketBuyOrderRequiresPrice": false}, "url": "https://api.upbit.com/v1/orders", "input": ["BTC/KRW", "market", "buy", 5000, 0, {"ordType": "best", "timeInForce": "ioc"}], "output": "{\"market\":\"KRW-BTC\",\"side\":\"bid\",\"ord_type\":\"best\",\"price\":\"5000\",\"time_in_force\":\"ioc\"}"}, {"description": "limit sell: selling 0.0001 BTC at a limit price of 150,000,000 KRW with identifier and time_in_force", "method": "createOrder", "url": "https://api.upbit.com/v1/orders", "input": ["BTC/KRW", "limit", "sell", 0.0001, 150000000, {"clientOrderId": "test_250425_02", "timeInForce": "ioc"}], "output": "{\"market\":\"KRW-BTC\",\"side\":\"ask\",\"ord_type\":\"limit\",\"price\":\"150000000\",\"volume\":\"0.0001\",\"identifier\":\"test_250425_02\",\"time_in_force\":\"ioc\"}"}, {"description": "limit sell: selling 0.0001 BTC at a limit price of 150,000,000 KRW with identifier", "method": "createOrder", "url": "https://api.upbit.com/v1/orders", "input": ["BTC/KRW", "limit", "sell", 0.0001, 150000000, {"clientOrderId": "test_250425_01"}], "output": "{\"market\":\"KRW-BTC\",\"side\":\"ask\",\"ord_type\":\"limit\",\"price\":\"150000000\",\"volume\":\"0.0001\",\"identifier\":\"test_250425_01\"}"}, {"description": "limit sell: selling 0.0001 BTC at a limit price of 150,000,000 KRW with time_in_force", "method": "createOrder", "url": "https://api.upbit.com/v1/orders", "input": ["BTC/KRW", "limit", "sell", 0.0001, 150000000, {"time_in_force": "ioc"}], "output": "{\"market\":\"KRW-BTC\",\"side\":\"ask\",\"ord_type\":\"limit\",\"price\":\"150000000\",\"volume\":\"0.0001\",\"time_in_force\":\"ioc\"}"}, {"description": "best sell: Selling the amount of BTC specified in amount at the best market price", "method": "createOrder", "url": "https://api.upbit.com/v1/orders", "input": ["BTC/KRW", "market", "sell", 0.0001, 0, {"ordType": "best", "timeInForce": "ioc"}], "output": "{\"market\":\"KRW-BTC\",\"side\":\"ask\",\"ord_type\":\"best\",\"volume\":\"0.0001\",\"time_in_force\":\"ioc\"}"}, {"description": "best buy with params.cost: Buying BTC at the best market price for the params.cost KRW", "method": "createOrder", "url": "https://api.upbit.com/v1/orders", "input": ["BTC/KRW", "market", "buy", 0, 0, {"ordType": "best", "timeInForce": "ioc", "cost": 5000}], "output": "{\"market\":\"KRW-BTC\",\"side\":\"bid\",\"ord_type\":\"best\",\"price\":\"5000\",\"time_in_force\":\"ioc\"}"}, {"description": "best buy with createMarketBuyOrderRequiresPrice = false: Buying BTC at the best market price for the amount of KRW specified in amount (quote amount)", "method": "createOrder", "options": {"createMarketBuyOrderRequiresPrice": false}, "url": "https://api.upbit.com/v1/orders", "input": ["BTC/KRW", "market", "buy", 5000, 0, {"ordType": "best", "timeInForce": "ioc"}], "output": "{\"market\":\"KRW-BTC\",\"side\":\"bid\",\"ord_type\":\"best\",\"price\":\"5000\",\"time_in_force\":\"ioc\"}"}, {"description": "best buy with createMarketBuyOrderRequiresPrice = true: Buying BTC at the best market price for a total of (amount × price) KRW", "method": "createOrder", "url": "https://api.upbit.com/v1/orders", "input": ["BTC/KRW", "market", "buy", 0.0001, 100000000, {"ordType": "best", "timeInForce": "ioc"}], "output": "{\"market\":\"KRW-BTC\",\"side\":\"bid\",\"ord_type\":\"best\",\"price\":\"10000\",\"time_in_force\":\"ioc\"}"}, {"description": "market sell: Selling the amount of BTC specified in amount at market price", "method": "createOrder", "url": "https://api.upbit.com/v1/orders", "input": ["BTC/KRW", "market", "sell", 0.0002, 0], "output": "{\"market\":\"KRW-BTC\",\"side\":\"ask\",\"ord_type\":\"market\",\"volume\":\"0.0002\"}"}, {"description": "market buy with params.cost: Buying BTC at market price for the params.cost KRW", "method": "createOrder", "url": "https://api.upbit.com/v1/orders", "input": ["BTC/KRW", "market", "buy", 0, 0, {"cost": 5000}], "output": "{\"market\":\"KRW-BTC\",\"side\":\"bid\",\"ord_type\":\"price\",\"price\":\"5000\"}"}, {"description": "market buy with createMarketBuyOrderRequiresPrice = false: Buying BTC at market price for the amount of KRW specified in amount (quote amount)", "method": "createOrder", "options": {"createMarketBuyOrderRequiresPrice": false}, "url": "https://api.upbit.com/v1/orders", "input": ["BTC/KRW", "market", "buy", 5000, 0], "output": "{\"market\":\"KRW-BTC\",\"side\":\"bid\",\"ord_type\":\"price\",\"price\":\"5000\"}"}, {"description": "market buy with createMarketBuyOrderRequiresPrice = true: Buying BTC at market price for a total of (amount × price) KRW", "method": "createOrder", "url": "https://api.upbit.com/v1/orders", "input": ["BTC/KRW", "market", "buy", 6e-05, 100000000], "output": "{\"market\":\"KRW-BTC\",\"side\":\"bid\",\"ord_type\":\"price\",\"price\":\"6000\"}"}, {"description": "limit sell: selling 0.0001 BTC at a limit price of 150,000,000 KRW", "method": "createOrder", "url": "https://api.upbit.com/v1/orders", "input": ["BTC/KRW", "limit", "sell", 0.0001, 150000000], "output": "{\"market\":\"KRW-BTC\",\"side\":\"ask\",\"ord_type\":\"limit\",\"price\":\"150000000\",\"volume\":\"0.0001\"}"}, {"description": "limit buy: Buying 0.0001 BTC at a limit price of 100,000,000 KRW", "method": "createOrder", "url": "https://api.upbit.com/v1/orders", "input": ["BTC/KRW", "limit", "buy", 0.0001, 100000000], "output": "{\"market\":\"KRW-BTC\",\"side\":\"bid\",\"ord_type\":\"limit\",\"price\":\"100000000\",\"volume\":\"0.0001\"}"}, {"description": "limit buy: Buying 0.0001 BTC at a limit price of 100,000,000 KRW", "method": "createOrder", "url": "https://api.upbit.com/v1/orders", "input": ["BTC/KRW", "limit", "buy", 0.0001, 100000000], "output": "{\"market\":\"KRW-BTC\",\"side\":\"bid\",\"ord_type\":\"limit\",\"price\":\"100000000\",\"volume\":\"0.0001\"}"}, {"description": "Spot market buy order with createMarketBuyOrderRequiresPrice set to true", "method": "createOrder", "url": "https://sg-api.upbit.com/v1/orders", "input": ["BTC/USDT", "market", "buy", 0.00015, 49998], "output": "{\"market\":\"USDT-BTC\",\"side\":\"bid\",\"ord_type\":\"price\",\"price\":\"7.4997\"}"}, {"description": "Spot market buy order with createMarketBuyOrderRequiresPrice set to false", "method": "createOrder", "options": {"createMarketBuyOrderRequiresPrice": false}, "url": "https://sg-api.upbit.com/v1/orders", "input": ["BTC/USDT", "market", "buy", 5, 0], "output": "{\"market\":\"USDT-BTC\",\"side\":\"bid\",\"ord_type\":\"price\",\"price\":\"5\"}"}, {"description": "Spot market buy order using the cost param", "method": "createOrder", "url": "https://sg-api.upbit.com/v1/orders", "input": ["BTC/USDT", "market", "buy", 0, 0, {"cost": 5}], "output": "{\"market\":\"USDT-BTC\",\"side\":\"bid\",\"ord_type\":\"price\",\"price\":\"5\"}"}, {"description": "Spot limit buy order", "method": "createOrder", "url": "https://sg-api.upbit.com/v1/orders", "input": ["BTC/USDT", "limit", "buy", 0.00015, 42000], "output": "{\"market\":\"USDT-BTC\",\"side\":\"bid\",\"price\":\"42000\",\"ord_type\":\"limit\",\"volume\":\"0.00015\"}"}, {"description": "Spot limit sell order", "method": "createOrder", "url": "https://sg-api.upbit.com/v1/orders", "input": ["BTC/USDT", "limit", "sell", 0.00015, 55000], "output": "{\"market\":\"USDT-BTC\",\"side\":\"ask\",\"price\":\"55000\",\"ord_type\":\"limit\",\"volume\":\"0.00015\"}"}, {"description": "Spot market sell order", "method": "createOrder", "url": "https://sg-api.upbit.com/v1/orders", "input": ["BTC/USDT", "market", "sell", 0.00015, null], "output": "{\"market\":\"USDT-BTC\",\"side\":\"ask\",\"ord_type\":\"market\",\"volume\":\"0.00015\"}"}, {"description": "Spot IOC limit sell order", "method": "createOrder", "url": "https://sg-api.upbit.com/v1/orders", "input": ["XRP/SGD", "limit", "sell", 10, 1.5, {"timeInForce": "IOC"}], "output": "{\"market\":\"SGD-XRP\",\"side\":\"ask\",\"price\":\"1.5\",\"ord_type\":\"limit\",\"volume\":\"10\",\"time_in_force\":\"ioc\"}"}, {"description": "Spot FOK limit sell order", "method": "createOrder", "url": "https://sg-api.upbit.com/v1/orders", "input": ["XRP/SGD", "limit", "sell", 10, 1.5, {"timeInForce": "FOK"}], "output": "{\"market\":\"SGD-XRP\",\"side\":\"ask\",\"price\":\"1.5\",\"ord_type\":\"limit\",\"volume\":\"10\",\"time_in_force\":\"fok\"}"}], "createMarketBuyOrderWithCost": [{"description": "Create market buy order with cost", "method": "createMarketBuyOrderWithCost", "url": "https://sg-api.upbit.com/v1/orders", "input": ["BTC/USDT", 5], "output": "{\"market\":\"USDT-BTC\",\"side\":\"bid\",\"ord_type\":\"price\",\"price\":\"5\"}"}], "cancelOrder": [{"description": "Spot cancel sell order", "method": "cancelOrder", "url": "https://sg-api.upbit.com/v1/order?uuid=4321cb06-94cc-4fd5-bb15-a514c721ff56", "input": ["4321cb06-94cc-4fd5-bb15-a514c721ff56"]}], "fetchWithdrawal": [{"description": "Fetch a withdrawal", "method": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://sg-api.upbit.com/v1/withdraw?uuid=95ef274b-23a6-4de4-95b0-5cbef4ca658f", "input": ["95ef274b-23a6-4de4-95b0-5cbef4ca658f"]}], "fetchDeposit": [{"description": "Fetch a deposit", "method": "fetchDeposit", "url": "https://sg-api.upbit.com/v1/deposit?uuid=7f54527e-2eee-4268-860e-fd8b9d7fe3c7", "input": ["7f54527e-2eee-4268-860e-fd8b9d7fe3c7"]}], "fetchTrades": [{"description": "spot fetchTrades", "method": "fetchTrades", "url": "https://api.upbit.com/v1/trades/ticks?market=USDT-BTC&count=200", "input": ["BTC/USDT"]}], "fetchOrderBook": [{"description": "spot orderbook", "method": "fetchOrderBook", "url": "https://api.upbit.com/v1/orderbook?markets=USDT-BTC", "input": ["BTC/USDT"]}], "fetchTicker": [{"description": "spot ticker", "method": "fetchTicker", "url": "https://api.upbit.com/v1/ticker?markets=USDT-BTC", "input": ["BTC/USDT"]}], "fetchTickers": [{"description": "spot tickers", "method": "fetchTickers", "url": "https://api.upbit.com/v1/ticker?markets=USDT-BTC%2CUSDT-ETH", "input": [["BTC/USDT", "ETH/USDT"]]}], "fetchOHLCV": [{"description": "spot ohlcv", "method": "fetchOHLCV", "url": "https://api.upbit.com/v1/candles/minutes/1?market=USDT-BTC&count=200", "input": ["BTC/USDT"]}], "fetchOpenOrders": [{"description": "Spot fetch open orders", "method": "fetchOpenOrders", "url": "https://sg-api.upbit.com/v1/orders/open?market=SGD-XRP", "input": ["XRP/SGD"]}], "fetchClosedOrders": [{"description": "Spot fetch closed orders", "method": "fetchClosedOrders", "url": "https://sg-api.upbit.com/v1/orders/closed?state=done&market=SGD-XRP", "input": ["XRP/SGD"]}], "fetchCanceledOrders": [{"description": "Spot fetch canceled orders", "method": "fetchCanceledOrders", "url": "https://sg-api.upbit.com/v1/orders/closed?state=cancel&market=SGD-XRP", "input": ["XRP/SGD"]}]}}