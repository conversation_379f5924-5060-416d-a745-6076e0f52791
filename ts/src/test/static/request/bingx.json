{"exchange": "bingx", "skipKeys": ["timestamp", "signature", "startTs", "endTs"], "outputType": "u<PERSON><PERSON><PERSON>", "methods": {"fetchTransfers": [{"description": "fetch transfers from spot to swap", "method": "fetchTransfers", "url": "https://open-api.bingx.com/openApi/api/v3/asset/transferRecord?fromAccount=spot&timestamp=*************&toAccount=USDTMPerp&signature=e918279c2b9e18bd94ac254a159e347ed0b1ee695ac96e862cc4f5cc4053bc60", "input": ["USDT", null, null, {"fromAccount": "spot", "toAccount": "swap"}]}], "fetchCurrencies": [{"description": "fetchCurrencies", "method": "fetchCurrencies", "disabled": true, "url": "https://open-api.bingx.com/openApi/wallets/v1/capital/config/getall?timestamp=*************&signature=6e1ce65a1c34a706d933e692b11b58f5295e2a610269e8b0d35acbb58739b9fd", "input": [], "output": null}], "withdraw": [{"description": "sol", "method": "withdraw", "url": "https://open-api.bingx.com/openApi/wallets/v1/capital/withdraw/apply?address=eWA8obxPaabuRhNjc2LHVeZ8PpNx3juFe8LKQVzX4QJ&amount=2.31157&coin=TRUMPSOL&network=SOL&timestamp=*************&walletType=1&signature=b98a71c9cf4a30724f65223ed5c11f8990ba929c57731524e17cadabab8c5cea", "input": ["TRUMPSOL", 2.31157, "eWA8obxPaabuRhNjc2LHVeZ8PpNx3juFe8LKQVzX4QJ", null, {"network": "SOL"}], "output": null}], "closeAllPositions": [{"description": "Close all open positions", "method": "closeAllPositions", "url": "https://open-api.bingx.com/openApi/swap/v2/trade/closeAllPositions?recvWindow=5000&timestamp=1700747597612&signature=d55a7e4f7f9dbe56c4004c9f3ab340869d3cb004e2f0b5b861e5fbd1762fd9a0", "input": [{"recvWindow": 5000}]}, {"description": "Close all open inverse positions", "method": "closeAllPositions", "url": "https://open-api.bingx.com/openApi/cswap/v1/trade/closeAllPositions?recvWindow=5000&timestamp=1720772255776&signature=f16320d5cea8782bb0a5f9a9865a642fd2280b212235b0e2051940965e7b9b5f", "input": [{"subType": "inverse"}]}], "closePosition": [{"description": "close linear position", "method": "closePosition", "url": "https://open-api.bingx.com/openApi/swap/v2/trade/closeAllPositions?symbol=LTC-USDT&timestamp=1703414588459&signature=f42b4b61669032b0d38c746d6d9bb156f24355c7ca8055942aa61b9b512e40ae", "input": ["LTC/USDT:USDT"]}, {"description": "close inverse position", "method": "closePosition", "url": "https://open-api.bingx.com/openApi/cswap/v1/trade/closeAllPositions?symbol=SOL-USD&timestamp=1720771600788&signature=0cc7ae4bf764a09c5b80b9bc1fb3af691830eff7eb3d0062cdc0e9b796892d49", "input": ["SOL/USD:SOL"]}], "createOrder": [{"description": "Spot market buy", "method": "createOrder", "url": "https://open-api.bingx.com/openApi/spot/v1/trade/order?quoteOrderQty=5&side=BUY&symbol=LTC-USDT&timestamp=1698777135343&type=MARKET&signature=d55a7e4f7f9dbe56c4004c9f3ab340869d3cb004e2f0b5b861e5fbd1762fd9a0", "input": ["LTC/USDT", "market", "buy", 0.1, 50]}, {"description": "Spot limit buy order", "method": "createOrder", "url": "https://open-api.bingx.com/openApi/spot/v1/trade/order?price=25000&quantity=0.0002&side=BUY&symbol=BTC-USDT&timestamp=1699068816253&type=LIMIT&signature=a29974ac7175d552ba1be899b454b3701224c7756d3f073ce060303c79963bfd", "input": ["BTC/USDT", "limit", "buy", 0.0002, 25000]}, {"description": "spot stopLoss market order", "method": "createOrder", "url": "https://open-api.bingx.com/openApi/spot/v1/trade/order?quantity=0.1&side=SELL&stopPrice=30&symbol=LTC-USDT&timestamp=1714140496563&type=TAKE_STOP_MARKET&signature=79a629862e052eab3a7ff71f82802cdf56ab7ccac2596325c2304bc8b0045d0b", "input": ["LTC/USDT", "market", "sell", 0.1, null, {"stopLossPrice": 30}]}, {"description": "spot limit takeProfit order", "method": "createOrder", "url": "https://open-api.bingx.com/openApi/spot/v1/trade/order?price=150&quantity=0.1&side=SELL&stopPrice=150&symbol=LTC-USDT&timestamp=1714140580532&type=TAKE_STOP_LIMIT&signature=07abb982040e225f36697d0782ca10c62d8c2bd6322439cfdff55915ea889be4", "input": ["LTC/USDT", "limit", "sell", 0.1, 150, {"takeProfitPrice": 150}]}, {"description": "Swap market buy order", "method": "createOrder", "url": "https://open-api.bingx.com/openApi/swap/v2/trade/order?positionSide=BOTH&quantity=0.1&side=BUY&symbol=LTC-USDT&timestamp=1699295761502&type=MARKET&signature=f060bcc332d11056fe031ddfee894c40d7493a5c856bf8ea84d7e0aa612bdc6b", "input": ["LTC/USDT:USDT", "market", "buy", 0.1]}, {"description": "Swap limit buy order", "method": "createOrder", "url": "https://open-api.bingx.com/openApi/swap/v2/trade/order?positionSide=BOTH&price=25000&quantity=0.0001&side=BUY&symbol=BTC-USDT&timestamp=1699069372440&type=LIMIT&signature=a275f322f3af34d0c7a2b25c5a58e8f76912d9d7c10734209989b4c46cb0e924", "input": ["BTC/USDT:USDT", "limit", "buy", 0.0001, 25000]}, {"description": "spot limit buy trigger order", "method": "createOrder", "url": "https://open-api.bingx.com/openApi/spot/v1/trade/order?price=50&quantity=0.1&side=BUY&stopPrice=55&symbol=LTC-USDT&timestamp=1707482604152&type=TRIGGER_LIMIT&signature=9fdea0e815810ae5a96ab326340badae98e24d4cc550298e215ffc09009d26f7", "input": ["LTC/USDT", "limit", "buy", 0.1, 50, {"triggerPrice": 55}]}, {"description": "spot market buy trigger order", "method": "createOrder", "url": "https://open-api.bingx.com/openApi/spot/v1/trade/order?quoteOrderQty=5&side=BUY&stopPrice=55&symbol=LTC-USDT&timestamp=1707482711763&type=TRIGGER_MARKET&signature=564862e81e0e60f6ed61c14f67380a818a57742d8abb04b55bdfc1d3095b2d3a", "input": ["LTC/USDT", "market", "buy", 0.1, 50, {"triggerPrice": 55}]}, {"description": "spot market sell using base amount", "method": "createOrder", "url": "https://open-api.bingx.com/openApi/spot/v1/trade/order?quantity=0.1&side=SELL&symbol=LTC-USDT&timestamp=1707482854356&type=MARKET&signature=0306b8e407921788c34d239e5470bba1a509312f7a5988275458e25f54c315fd", "input": ["LTC/USDT", "market", "sell", 0.1]}, {"description": "spot market buy using base amount", "method": "createOrder", "url": "https://open-api.bingx.com/openApi/spot/v1/trade/order?quantity=0.1&side=BUY&symbol=LTC-USDT&timestamp=1707482889820&type=MARKET&signature=1b41aff50df6774fa3af97c5efd2e185fccad850399cdbf51ceac91dd16e6f10", "input": ["LTC/USDT", "market", "buy", 0.1]}, {"description": "Swap attach a take profit market order to an existing long position", "method": "createOrder", "url": "https://open-api.bingx.com/openApi/swap/v2/trade/order?positionSide=BOTH&quantity=0.0001&side=SELL&stopPrice=40000&symbol=BTC-USDT&timestamp=1699343342279&type=TAKE_PROFIT_MARKET&signature=26051175a041231ba47518249dd2e70c7ccc45185f32bb5551b4cccf49e9cb9d", "input": ["BTC/USDT:USDT", "market", "sell", 0.0001, null, {"takeProfitPrice": "40000"}]}, {"description": "Swap attach a stop loss market order to an existing long position", "method": "createOrder", "url": "https://open-api.bingx.com/openApi/swap/v2/trade/order?positionSide=BOTH&quantity=0.0001&side=SELL&stopPrice=30000&symbol=BTC-USDT&timestamp=1699343474612&type=STOP_MARKET&signature=2eef7b8d45e2c6fba7f38299c154c8f0112eb135c1ca59650b72aed5e54a7533", "input": ["BTC/USDT:USDT", "market", "sell", 0.0001, null, {"stopLossPrice": "30000"}]}, {"description": "Swap attach a take profit limit order to an existing long position", "method": "createOrder", "url": "https://open-api.bingx.com/openApi/swap/v2/trade/order?positionSide=BOTH&price=39000&quantity=0.0001&side=SELL&stopPrice=40000&symbol=BTC-USDT&timestamp=1699343931549&type=TAKE_PROFIT&signature=d38f30c621fe59fadd3b8a5874442b14c7a6ff08e6a8d2b052d2aed263058a8b", "input": ["BTC/USDT:USDT", "limit", "sell", 0.0001, 39000, {"takeProfitPrice": "40000"}]}, {"description": "Swap attach a stop loss limit order to an existing long position", "method": "createOrder", "url": "https://open-api.bingx.com/openApi/swap/v2/trade/order?positionSide=BOTH&price=31000&quantity=0.0001&side=SELL&stopPrice=30000&symbol=BTC-USDT&timestamp=1699343849547&type=STOP&signature=8c3cb440c84bbb09defdc6b44d7c22b35e1e4942f338d476e9952c30d9be4eba", "input": ["BTC/USDT:USDT", "limit", "sell", 0.0001, 31000, {"stopLossPrice": 30000}]}, {"description": "Swap attach a take profit market order to an existing short position", "method": "createOrder", "url": "https://open-api.bingx.com/openApi/swap/v2/trade/order?positionSide=BOTH&quantity=0.0001&side=BUY&stopPrice=30000&symbol=BTC-USDT&timestamp=1699344115407&type=TAKE_PROFIT_MARKET&signature=937af5613b9503cf6a57f226a9dd5117127bf062e459a1341865858079c0c48b", "input": ["BTC/USDT:USDT", "market", "buy", 0.0001, null, {"takeProfitPrice": 30000}]}, {"description": "Swap attach a stop loss market order to an existing short position", "method": "createOrder", "url": "https://open-api.bingx.com/openApi/swap/v2/trade/order?positionSide=BOTH&quantity=0.0001&side=BUY&stopPrice=40000&symbol=BTC-USDT&timestamp=1699344170850&type=STOP_MARKET&signature=185c506151d6b405651c187500768dda9d2a6eb0aa8555564356766d59939e15", "input": ["BTC/USDT:USDT", "market", "buy", 0.0001, null, {"stopLossPrice": 40000}]}, {"description": "Swap attach a take profit limit order to an existing short position", "method": "createOrder", "url": "https://open-api.bingx.com/openApi/swap/v2/trade/order?positionSide=BOTH&price=31000&quantity=0.0001&side=BUY&stopPrice=30000&symbol=BTC-USDT&timestamp=1699344371397&type=TAKE_PROFIT&signature=56d80e251692435252f773ec1962762ca6986ae13a46f3e061ac2c328ba46587", "input": ["BTC/USDT:USDT", "limit", "buy", 0.0001, 31000, {"takeProfitPrice": 30000}]}, {"description": "Swap attach a stop loss limit order to an existing short position", "method": "createOrder", "url": "https://open-api.bingx.com/openApi/swap/v2/trade/order?positionSide=BOTH&price=39000&quantity=0.0001&side=BUY&stopPrice=40000&symbol=BTC-USDT&timestamp=1699344308575&type=STOP&signature=197178df2ae9cbe1b90f833fd4cb4a70a19171630415d91264e2a10e45a74ccc", "input": ["BTC/USDT:USDT", "limit", "buy", 0.0001, 39000, {"stopLossPrice": 40000}]}, {"description": "Swap trailing amount order", "method": "createOrder", "url": "https://open-api.bingx.com/openApi/swap/v2/trade/order?positionSide=BOTH&price=100&quantity=0.0001&side=SELL&symbol=BTC-USDT&reduceOnly=true&timestamp=1703311717708&type=TRAILING_STOP_MARKET&signature=af450249b24a125292d4a81bbcda3445ea20a85dd858734e6ddacf36e999cf21", "input": ["BTC/USDT:USDT", "market", "sell", 0.0001, null, {"trailingAmount": 100, "reduceOnly": true}]}, {"description": "Swap trailing percent order", "method": "createOrder", "url": "https://open-api.bingx.com/openApi/swap/v2/trade/order?positionSide=BOTH&reduceOnly=true&priceRate=0.1&quantity=0.0001&side=SELL&symbol=BTC-USDT&timestamp=1703312086044&type=TRAILING_STOP_MARKET&signature=d2f180a1a328e08f679de56f255c452d9ad469f4eb445659397ef297390e6cfe", "input": ["BTC/USDT:USDT", "market", "sell", 0.0001, null, {"trailingPercent": 10, "reduceOnly": true}]}, {"description": "Swap order with attached tp and sl", "disabledGO": true, "method": "createOrder", "url": "https://open-api.bingx.com/openApi/swap/v2/trade/order?positionSide=BOTH&quantity=1&side=BUY&stopLoss=%7B%22stopPrice%22%3A50%2C%22workingType%22%3A%22MARK_PRICE%22%2C%22type%22%3A%22STOP_MARKET%22%2C%22quantity%22%3A1%7D&symbol=LTC-USDT&takeProfit=%7B%22stopPrice%22%3A150%2C%22workingType%22%3A%22MARK_PRICE%22%2C%22type%22%3A%22TAKE_PROFIT_MARKET%22%2C%22quantity%22%3A1%7D&timestamp=1704026482160&type=MARKET&signature=cbc5f76613bb456d642bd90f556a58d674cafab85559804ddd19c28a4daadddc", "input": ["LTC/USDT:USDT", "market", "buy", 1, null, {"takeProfit": {"stopPrice": 150}, "stopLoss": {"stopPrice": 50}}]}, {"description": "swap order with clientOrderId", "method": "createOrder", "url": "https://open-api.bingx.com/openApi/swap/v2/trade/order?clientOrderID=myorder1&positionSide=BOTH&price=50&quantity=0.1&side=BUY&symbol=LTC-USDT&timestamp=1704360714849&type=LIMIT&signature=2881d13ae74453035b973ab795af3f1052e4cbb88829d407d3cbe687bcb03afe", "input": ["LTC/USDT:USDT", "limit", "buy", 0.1, 50, {"clientOrderId": "myorder1"}]}, {"description": "spot order with clientOrderId", "method": "createOrder", "url": "https://open-api.bingx.com/openApi/spot/v1/trade/order?&newClientOrderId=myorder1&price=50&quantity=0.1&side=BUY&symbol=LTC-USDT&timestamp=1704360855187&type=LIMIT&signature=db4adedce73fa75cfda1dafccc45d6b2b16566bbcc751a5df7ef27bc7e5e3bef", "input": ["LTC/USDT", "limit", "buy", 0.1, 50, {"clientOrderId": "myorder1"}]}, {"description": "Swap send test order", "method": "createOrder", "url": "https://open-api.bingx.com/openApi/swap/v2/trade/order/test?positionSide=BOTH&quantity=0.0001&side=SELL&stopPrice=40000&symbol=BTC-USDT&timestamp=1699343342279&type=TAKE_PROFIT_MARKET&signature=26051175a041231ba47518249dd2e70c7ccc45185f32bb5551b4cccf49e9cb9d", "input": ["BTC/USDT:USDT", "market", "sell", 0.0001, null, {"takeProfitPrice": "40000", "test": true}]}, {"description": "spot create PO order", "method": "createOrder", "url": "https://open-api.bingx.com/openApi/spot/v1/trade/order?price=50&quantity=0.01&side=BUY&symbol=LTC-USDT&timeInForce=PostOnly&timestamp=1710732113438&type=LIMIT&signature=1e6e9d2e156dae853fba61211e487b54137dd95af833e1fc9fbeb21ef3b14bee", "input": ["LTC/USDT", "limit", "buy", 0.01, 50, {"timeInForce": "PO"}]}, {"description": "spot create GTC order", "method": "createOrder", "url": "https://open-api.bingx.com/openApi/spot/v1/trade/order?price=50&quantity=0.01&side=BUY&symbol=LTC-USDT&timeInForce=GTC&timestamp=1710732113438&type=LIMIT&signature=1e6e9d2e156dae853fba61211e487b54137dd95af833e1fc9fbeb21ef3b14bee", "input": ["LTC/USDT", "limit", "buy", 0.01, 50, {"timeInForce": "GTC"}]}, {"description": "spot create IOC order", "method": "createOrder", "url": "https://open-api.bingx.com/openApi/spot/v1/trade/order?price=50&quantity=0.01&side=BUY&symbol=LTC-USDT&timeInForce=IOC&timestamp=1710732113438&type=LIMIT&signature=1e6e9d2e156dae853fba61211e487b54137dd95af833e1fc9fbeb21ef3b14bee", "input": ["LTC/USDT", "limit", "buy", 0.01, 50, {"timeInForce": "IOC"}]}, {"description": "swap create PO order", "method": "createOrder", "url": "https://open-api.bingx.com/openApi/swap/v2/trade/order?positionSide=BOTH&price=50&quantity=0.1&side=BUY&symbol=LTC-USDT&timeInForce=PostOnly&timestamp=1710732308502&type=LIMIT&signature=a4a21d756105a9a45b545550611ae8fd352c9cb03bc027944d8360aee2326ad6", "input": ["LTC/USDT:USDT", "limit", "buy", 0.1, 50, {"timeInForce": "PO"}]}, {"description": "swap create GTC order", "method": "createOrder", "url": "https://open-api.bingx.com/openApi/swap/v2/trade/order?positionSide=BOTH&price=50&quantity=0.1&side=BUY&symbol=LTC-USDT&timeInForce=GTC&timestamp=1710732308502&type=LIMIT&signature=a4a21d756105a9a45b545550611ae8fd352c9cb03bc027944d8360aee2326ad6", "input": ["LTC/USDT:USDT", "limit", "buy", 0.1, 50, {"timeInForce": "GTC"}]}, {"description": "swap create IOC order", "method": "createOrder", "url": "https://open-api.bingx.com/openApi/swap/v2/trade/order?positionSide=BOTH&price=50&quantity=0.1&side=BUY&symbol=LTC-USDT&timeInForce=IOC&timestamp=1710732308502&type=LIMIT&signature=a4a21d756105a9a45b545550611ae8fd352c9cb03bc027944d8360aee2326ad6", "input": ["LTC/USDT:USDT", "limit", "buy", 0.1, 50, {"timeInForce": "IOC"}]}, {"description": "swap create FOK order", "method": "createOrder", "url": "https://open-api.bingx.com/openApi/swap/v2/trade/order?positionSide=BOTH&price=50&quantity=0.1&side=BUY&symbol=LTC-USDT&timeInForce=FOK&timestamp=1710732308502&type=LIMIT&signature=a4a21d756105a9a45b545550611ae8fd352c9cb03bc027944d8360aee2326ad6", "input": ["LTC/USDT:USDT", "limit", "buy", 0.1, 50, {"timeInForce": "FOK"}]}, {"description": "swap order with clientOrderId", "method": "createOrder", "url": "https://open-api.bingx.com/openApi/swap/v2/trade/order?clientOrderID=myorder222&positionSide=BOTH&price=50&quantity=0.1&side=BUY&symbol=LTC-USDT&timestamp=1714141045332&type=LIMIT&signature=b0b9849fd0b5828bab58cfa6085dc9dfd7d4c77503aa94aa275d316f6ae87ab6", "input": ["LTC/USDT:USDT", "limit", "buy", 0.1, 50, {"clientOrderId": "myorder222", "positionSide": "BOTH"}]}, {"description": "Inverse swap create order", "method": "createOrder", "url": "https://open-api.bingx.com/openApi/cswap/v1/trade/order?positionSide=BOTH&price=100&quantity=1&side=BUY&symbol=SOL-USD&timestamp=1720334783819&type=LIMIT&signature=7343c8212f5cfaca9c064c80e50ecc2f071e658ac98bea4581d164e1ed5643f5", "input": ["SOL/USD:SOL", "limit", "buy", 1, 100]}, {"description": "swap reduceOnly order", "method": "createOrder", "url": "https://open-api.bingx.com/openApi/swap/v2/trade/order?positionSide=BOTH&quantity=0.1&reduceOnly=true&side=SELL&symbol=LTC-USDT&timestamp=1725185164341&type=MARKET&signature=829da92de2a03fa34f28ab646785466677cb60b9792b8249a35e79225a6b7907", "input": ["LTC/USDT:USDT", "market", "sell", 0.1, null, {"reduceOnly": true}]}, {"description": "swap create TWAP order", "method": "createOrder", "url": "https://open-api.bingx.com/openApi/swap/v1/twap/order?amountPerOrder=0.5&interval=8&positionSide=LONG&priceType=constant&priceVariance=10&side=BUY&symbol=LTC-USDT&timestamp=1732693774172&totalAmount=1&triggerPrice=120&signature=628848cc3080ef44034deb561c9ca5015171de1717458378bea67df657131795", "input": ["LTC/USDT:USDT", "twap", "buy", 1, null, {"priceType": "constant", "priceVariance": "10", "triggerPrice": "120", "interval": 8, "amountPerOrder": "0.5"}]}], "createOrders": [{"description": "Spot create multiple limit orders at once", "disabledGO": true, "method": "createOrders", "url": "https://open-api.bingx.com/openApi/spot/v1/trade/batchOrders?data=%5B%7B%22symbol%22%3A%22BTC-USDT%22%2C%22type%22%3A%22LIMIT%22%2C%22side%22%3A%22BUY%22%2C%22quantity%22%3A0.0002%2C%22price%22%3A25000%7D%2C%7B%22symbol%22%3A%22BTC-USDT%22%2C%22type%22%3A%22LIMIT%22%2C%22side%22%3A%22BUY%22%2C%22quantity%22%3A0.0002%2C%22price%22%3A27000%7D%5D&timestamp=1699073035068&signature=6864c0fb646b46b6f7ff266fe4cbd12cf8aa3d565cc57f74ce3ac229c42a686e", "input": [[{"symbol": "BTC/USDT", "type": "limit", "side": "buy", "amount": 0.0002, "price": 25000}, {"symbol": "BTC/USDT", "type": "limit", "side": "buy", "amount": 0.0002, "price": 27000}]]}, {"description": "Swap create multiple limit orders at once", "disabledGO": true, "method": "createOrders", "url": "https://open-api.bingx.com/openApi/swap/v2/trade/batchOrders?batchOrders=%5B%7B%22symbol%22%3A%22BTC-USDT%22%2C%22type%22%3A%22LIMIT%22%2C%22side%22%3A%22BUY%22%2C%22price%22%3A25000%2C%22positionSide%22%3A%22BOTH%22%2C%22quantity%22%3A0.0001%7D%2C%7B%22symbol%22%3A%22BTC-USDT%22%2C%22type%22%3A%22LIMIT%22%2C%22side%22%3A%22BUY%22%2C%22price%22%3A27000%2C%22positionSide%22%3A%22BOTH%22%2C%22quantity%22%3A0.0001%7D%5D&timestamp=1699073296743&signature=0e9387a6bdfe14bce939479b3ad9ec3124081e34aa878725c5812b70febe1545", "input": [[{"symbol": "BTC/USDT:USDT", "type": "limit", "side": "buy", "amount": 0.0001, "price": 25000}, {"symbol": "BTC/USDT:USDT", "type": "limit", "side": "buy", "amount": 0.0001, "price": 27000}]]}], "createMarketOrderWithCost": [{"description": "Fill this with a description of the method call", "method": "createMarketOrderWithCost", "url": "https://open-api.bingx.com/openApi/spot/v1/trade/order?quoteOrderQty=10&side=BUY&symbol=BTC-USDT&timestamp=1701413688001&type=MARKET&signature=fed01fa9ea12fdeae745f7c7cee390763e21af614d93bc92bde5419dad250399", "input": ["BTC/USDT", "buy", 10]}], "createMarketBuyOrderWithCost": [{"description": "Fill this with a description of the method call", "method": "createMarketBuyOrderWithCost", "url": "https://open-api.bingx.com/openApi/spot/v1/trade/order?quoteOrderQty=10&side=BUY&symbol=BTC-USDT&timestamp=1701413753563&type=MARKET&signature=4194ff6c2eb53217252960743199e47e8e45dea7755c1f3b055552a0d4b6d93c", "input": ["BTC/USDT", 10]}], "createMarketSellOrderWithCost": [{"description": "Fill this with a description of the method call", "method": "createMarketSellOrderWithCost", "url": "https://open-api.bingx.com/openApi/spot/v1/trade/order?quoteOrderQty=20&side=SELL&symbol=BTC-USDT&timestamp=1701413792846&type=MARKET&signature=0e18f383d49758634c6c66f0a8fad5374762e9996bc2d45882fda91e8998a94b", "input": ["BTC/USDT", 20]}], "createTrailingAmountOrder": [{"description": "Swap create a trailing amount order", "method": "createTrailingAmountOrder", "url": "https://open-api.bingx.com/openApi/swap/v2/trade/order?positionSide=BOTH&reduceOnly=true&price=1000&quantity=0.0001&side=SELL&symbol=BTC-USDT&timestamp=1704859549691&type=TRAILING_STOP_MARKET&signature=f026f00618ba73fdc6f07d69601e8d6d66f861cfaa0d46d08b6bb7eaaa5eb194", "input": ["BTC/USDT:USDT", "market", "sell", 0.0001, null, 1000, null, {"reduceOnly": true, "trailingAmount": 1000}]}], "createTrailingPercentOrder": [{"description": "Swap create a trailing percent order", "method": "createTrailingPercentOrder", "url": "https://open-api.bingx.com/openApi/swap/v2/trade/order?positionSide=BOTH&reduceOnly=true&priceRate=0.05&quantity=0.0001&side=SELL&symbol=BTC-USDT&timestamp=1704859767615&type=TRAILING_STOP_MARKET&signature=682c1e28fea424756445d9c41f4ff1c864293ca1f77b3eec31ca847402afa426", "input": ["BTC/USDT:USDT", "market", "sell", 0.0001, null, 5, null, {"reduceOnly": true, "trailingPercent": 5}]}], "cancelOrder": [{"description": "Spot cancel order", "method": "cancelOrder", "url": "https://open-api.bingx.com/openApi/spot/v1/trade/cancel?orderId=1720645449355886592&symbol=BTC-USDT&timestamp=1699068989110&signature=785059a4f44e65251fb928d05899515a3a664f35f550cf403760ea1e8ce3b109", "input": ["1720645449355886592", "BTC/USDT"]}, {"description": "Swap cancel order", "method": "cancelOrder", "url": "https://open-api.bingx.com/openApi/swap/v2/trade/order?orderId=1720647776204967936&symbol=BTC-USDT&timestamp=1699069503847&signature=f519686d645b1c3c3d1dc505089fc62f3d9bdce9e3c4454c08bf2d78703ea7f9", "input": ["1720647776204967936", "BTC/USDT:USDT"]}, {"description": "Spot cancel order with clientOrderId", "method": "cancelOrder", "url": "https://open-api.bingx.com/openApi/spot/v1/trade/cancel?clientOrderID=myorder2&symbol=LTC-USDT&timestamp=1704380693563&signature=6ac964ce62bb594df37c7529ae4bd2010ff025ca06644efee2f0dcd1f5e0805f", "input": ["", "LTC/USDT", {"clientOrderId": "myorder2"}]}, {"description": "Swap cancel order with clientOrderId", "method": "cancelOrder", "url": "https://open-api.bingx.com/openApi/swap/v2/trade/order?clientOrderID=myorder2&symbol=LTC-USDT&timestamp=1704380774663&signature=0f091dc8f55fb10ab73e69d675fdf902a86daf14c3383227a4047190390c1dfb", "input": ["", "LTC/USDT:USDT", {"clientOrderId": "myorder2"}]}, {"description": "cancel trigger order", "method": "cancelOrder", "url": "https://open-api.bingx.com/openApi/swap/v2/trade/order?orderId=1792480780099366912&symbol=LTC-USDT&timestamp=1716195827169&signature=0f1b1278f4f38b7e53ea9a25914ea0613bad9e37ffc530a8d2998e1ed8014172", "input": ["1792480780099366912", "LTC/USDT:USDT"]}, {"description": "Inverse swap cancel order", "method": "cancelOrder", "url": "https://open-api.bingx.com/openApi/cswap/v1/trade/cancelOrder?orderId=1816002957423951872&symbol=SOL-USD&timestamp=1721803854831&signature=b0bcc0da4c0354baaa87558cb429c19d56852fd079d043f2ac015bab125add79", "input": ["1816002957423951872", "SOL/USD:SOL"]}, {"description": "twap cancel order", "method": "cancelOrder", "url": "https://open-api.bingx.com/openApi/swap/v1/twap/cancelOrder?mainOrderId=5596903086063901779&timestamp=1721803854831&signature=b0bcc0da4c0354baaa87558cb429c19d56852fd079d043f2ac015bab125add79", "input": ["5596903086063901779", null, {"twap": true}]}], "cancelOrders": [{"description": "spot cancel orders", "method": "cancelOrders", "url": "https://open-api.bingx.com/openApi/spot/v1/trade/cancelOrders?orderIds=1741063422788763648&symbol=LTC-USDT&timestamp=1704106632880&signature=bcb17f764ececd8ae8d64603b65b0f359b6983a79ac9b5dd36b25a1ad0452ad6", "input": [["1741063422788763648"], "LTC/USDT"]}, {"description": "spot cancel orders by client order ids", "method": "cancelOrders", "url": "https://open-api.bingx.com/openApi/spot/v1/trade/cancelOrders?clientOrderIDs=1741063422788763648&symbol=LTC-USDT&timestamp=1704106632880&signature=bcb17f764ececd8ae8d64603b65b0f359b6983a79ac9b5dd36b25a1ad0452ad6", "input": [[], "LTC/USDT", {"clientOrderIds": ["1741063422788763648"]}]}, {"description": "cancel swap orders", "method": "cancelOrders", "url": "https://open-api.bingx.com/openApi/swap/v2/trade/batchOrders?orderIdList=%5B1741775869694930944%5D&symbol=LTC-USDT&timestamp=1704106731589&signature=63170e2bdcc7341f0565a93e2e5e70398aeedcc668bccd301ecccd5bf5c5fc99", "input": [["1741775869694930944"], "LTC/USDT:USDT"]}, {"description": "swap cancel orders by client order ids", "method": "cancelOrders", "url": "https://open-api.bingx.com/openApi/swap/v2/trade/batchOrders?clientOrderIDList=%5B%221741775869694930944%22%5D&symbol=LTC-USDT&timestamp=1704106731589&signature=63170e2bdcc7341f0565a93e2e5e70398aeedcc668bccd301ecccd5bf5c5fc99", "input": [[], "LTC/USDT:USDT", {"clientOrderIds": ["1741775869694930944"]}]}], "cancelAllOrders": [{"description": "Linear swap cancel all orders", "method": "cancelAllOrders", "url": "https://open-api.bingx.com/openApi/swap/v2/trade/allOpenOrders?symbol=BTC-USDT&timestamp=1699071702130&signature=581d72f82f5c1c7d133bd0556f0513ae5f22445207a9e49babf5d13d70a421b4", "input": ["BTC/USDT:USDT"]}, {"description": "Linear swap cancel all orders", "method": "cancelAllOrders", "url": "https://open-api.bingx.com/openApi/swap/v2/trade/allOpenOrders?symbol=LTC-USDT&timestamp=1699458295121&signature=c2bdfd0494cee5e13259629af9f815637a9a42ed87fd47a2eb3b552c0574d945", "input": ["LTC/USDT:USDT"]}, {"description": "Spot cancel all orders", "method": "cancelAllOrders", "url": "https://open-api.bingx.com/openApi/spot/v1/trade/cancelOpenOrders?symbol=LTC-USDT&timestamp=1703847561251&signature=e2c804484ea920957823019aed0ab49073125407426079dea637eef341ab0dc8", "input": ["LTC/USDT"]}, {"description": "Inverse swap cancel all orders", "method": "cancelAllOrders", "url": "https://open-api.bingx.com/openApi/cswap/v1/trade/allOpenOrders?symbol=SOL-USD&timestamp=1720501467377&signature=19add2557b74a8a1803773daa42713b0a8862423abf6b68b6f7840ebdf1e2886", "input": ["SOL/USD:SOL"]}], "cancelAllOrdersAfter": [{"description": "Swap cancel all orders after", "method": "cancelAllOrdersAfter", "url": "https://open-api.bingx.com/openApi/swap/v2/trade/cancelAllAfter?timeOut=100&type=ACTIVATE&timestamp=1699071702130&signature=581d72f82f5c1c7d133bd0556f0513ae5f22445207a9e49babf5d13d70a421b4", "input": [100000, {"type": "swap"}]}, {"description": "Swap close cancel all orders after", "method": "cancelAllOrdersAfter", "url": "https://open-api.bingx.com/openApi/swap/v2/trade/cancelAllAfter?timeOut=0&type=CLOSE&timestamp=1699071702130&signature=581d72f82f5c1c7d133bd0556f0513ae5f22445207a9e49babf5d13d70a421b4", "input": [0, {"type": "swap"}]}, {"description": "Spot cancel all orders after", "method": "cancelAllOrdersAfter", "url": "https://open-api.bingx.com/openApi/spot/v1/trade/cancelAllAfter?timeOut=100&type=ACTIVATE&timestamp=1699458295121&signature=c2bdfd0494cee5e13259629af9f815637a9a42ed87fd47a2eb3b552c0574d945", "input": [100000, {"type": "spot"}]}, {"description": "Spot close cancel all orders after", "method": "cancelAllOrdersAfter", "url": "https://open-api.bingx.com/openApi/spot/v1/trade/cancelAllAfter?timeOut=0&type=CLOSE&timestamp=1699458295121&signature=c2bdfd0494cee5e13259629af9f815637a9a42ed87fd47a2eb3b552c0574d945", "input": [0, {"type": "spot"}]}], "fetchOpenOrders": [{"description": "Spot open orders", "method": "fetchOpenOrders", "url": "https://open-api.bingx.com/openApi/spot/v1/trade/openOrders?symbol=LTC-USDT&timestamp=1699458294086&signature=53513e8b347e982ae257694c54b06333af66b702884d7db3036b1c8c60c219e7", "input": ["LTC/USDT"]}, {"description": "Swap open orders", "method": "fetchOpenOrders", "url": "https://open-api.bingx.com/openApi/swap/v2/trade/openOrders?symbol=LTC-USDT&timestamp=*************&signature=0386bcc46ee9b65c4a2b770c9eb8bbdeec387ea6230d8456d37a4c0c103979e0", "input": ["LTC/USDT:USDT"]}, {"description": "Spot open orders without symbol", "method": "fetchOpenOrders", "url": "https://open-api.bingx.com/openApi/spot/v1/trade/openOrders?timestamp=1699458294086&signature=53513e8b347e982ae257694c54b06333af66b702884d7db3036b1c8c60c219e7", "input": [null, null, null, {"type": "spot"}]}, {"description": "Swap open orders without symbol", "method": "fetchOpenOrders", "url": "https://open-api.bingx.com/openApi/swap/v2/trade/openOrders?timestamp=*************&signature=0386bcc46ee9b65c4a2b770c9eb8bbdeec387ea6230d8456d37a4c0c103979e0", "input": [null, null, null, {"type": "swap"}]}, {"description": "Inverse swap fetch open orders", "method": "fetchOpenOrders", "url": "https://open-api.bingx.com/openApi/cswap/v1/trade/openOrders?symbol=SOL-USD&timestamp=1721806459310&signature=d4d74676f855a257c4482aa92bc9d86772ce099c4eb59a011dd81937c5ea6ff6", "input": ["SOL/USD:SOL"]}, {"description": "twap open orders", "method": "fetchOpenOrders", "url": "https://open-api.bingx.com/openApi/swap/v1/twap/openOrders?symbol=LTC-USDT&timestamp=1732695725572&signature=11f93b06b30cefdf494d0d651514beccb0790650d9ad1f30dfb2fa4acf00f06f", "input": ["LTC/USDT:USDT", null, null, {"twap": true}]}], "fetchClosedOrders": [{"description": "fetch closed orders with limit", "method": "fetchClosedOrders", "url": "https://open-api.bingx.com/openApi/spot/v1/trade/historyOrders?pageSize=50&symbol=LTC-USDT&timestamp=1740518148275&signature=2797718cce7debcdd081aa813738210bf8f56e100aeefa9e51871847c15d0ddf", "input": ["LTC/USDT", null, 50], "output": null}, {"description": "Spot closed orders", "method": "fetchClosedOrders", "url": "https://open-api.bingx.com/openApi/spot/v1/trade/historyOrders?symbol=LTC-USDT&timestamp=1699458294623&signature=80016769e53019f01380e74b927ad3e36f5980e32c3de4a3a4bcfe9fc68efa7a", "input": ["LTC/USDT"]}, {"description": "Linear swap fetch closed orders", "method": "fetchClosedOrders", "url": "https://open-api.bingx.com/openApi/swap/v2/trade/allOrders?symbol=LTC-USDT&timestamp=1699458294851&signature=d1622ab21a981576341cb05abaece32622e00a0b296c69e937e792c157e3e687", "input": ["LTC/USDT:USDT"]}, {"description": "Inverse swap fetch closed orders", "method": "fetchClosedOrders", "url": "https://open-api.bingx.com/openApi/cswap/v1/trade/orderHistory?symbol=SOL-USD&timestamp=1721961756401&signature=e9cfa075457e0a4e8346e2dd32fa082cfb631ecaf87ca7ff414a1fe1096c5a8c", "input": ["SOL/USD:SOL"]}, {"description": "fetch twap orders", "method": "fetchClosedOrders", "url": "https://open-api.bingx.com/openApi/swap/v1/twap/historyOrders?endTime=1732697788022&pageIndex=1&pageSize=100&startTime=1&symbol=LTC-USDT&timestamp=1732697788123&signature=3868b3a579f21d348426ba3b38513b4e02fcf76dd932d56775b9f3fca694cace", "input": ["LTC/USDT:USDT", null, null, {"twap": true, "until": 1732697788022}]}], "fetchCanceledOrders": [{"description": "Spot fetch canceled orders", "method": "fetchCanceledOrders", "url": "https://open-api.bingx.com/openApi/spot/v1/trade/historyOrders?symbol=BTC-USDT&timestamp=1721962412294&signature=ef116f0066be1352ec1f8f02c63de0201c902090c12243f0653b2bff86101a58", "input": ["BTC/USDT"]}, {"description": "Linear swap fetch canceled orders", "method": "fetchCanceledOrders", "url": "https://open-api.bingx.com/openApi/swap/v2/trade/allOrders?symbol=BTC-USDT&timestamp=1721962374236&signature=39820f50817c13794281f718696a45d3f2726be8f0393f49c8ad07ccc1aa5801", "input": ["BTC/USDT:USDT"]}, {"description": "Inverse swap fetch canceled orders", "method": "fetchCanceledOrders", "url": "https://open-api.bingx.com/openApi/cswap/v1/trade/orderHistory?symbol=SOL-USD&timestamp=1721962310068&signature=d29e60b0b0da7bd115a42881b27bc9e3cbe865734802badb1df6d12875ac89c8", "input": ["SOL/USD:SOL"]}, {"description": "fetch twap orders", "method": "fetchCanceledOrders", "url": "https://open-api.bingx.com/openApi/swap/v1/twap/historyOrders?endTime=1732697788022&pageIndex=1&pageSize=100&startTime=1&symbol=LTC-USDT&timestamp=1732697788123&signature=3868b3a579f21d348426ba3b38513b4e02fcf76dd932d56775b9f3fca694cace", "input": ["LTC/USDT:USDT", null, null, {"twap": true, "until": 1732697788022}]}], "fetchOrders": [{"description": "Swap orders", "method": "fetchOrders", "url": "https://open-api.bingx.com/openApi/swap/v1/trade/fullOrder?symbol=LTC-USDT&timestamp=*************&signature=0386bcc46ee9b65c4a2b770c9eb8bbdeec387ea6230d8456d37a4c0c103979e0", "input": ["LTC/USDT:USDT"]}, {"description": "Swap orders without symbol", "method": "fetchOrders", "url": "https://open-api.bingx.com/openApi/swap/v1/trade/fullOrder?timestamp=*************&signature=0386bcc46ee9b65c4a2b770c9eb8bbdeec387ea6230d8456d37a4c0c103979e0", "input": [null, null, null, {"type": "swap"}]}], "fetchBalance": [{"description": "Fetch spot Balance", "method": "fetchBalance", "url": "https://open-api.bingx.com/openApi/spot/v1/account/balance?timestamp=*************&signature=1a1e94a11aafb72eaa8b1ee228d7f0330f043fdcfaaaffc41063a8d6304d9295", "input": [{"type": "spot"}]}, {"description": "Linear swap fetch balance", "method": "fetchBalance", "url": "https://open-api.bingx.com/openApi/swap/v2/user/balance?timestamp=*************&signature=9ac6bdbb0f9dda6650951407abbf317de3c8c9eab00418dbdd285be82531dc9c", "input": [{"type": "swap"}]}, {"description": "Inverse swap fetch balance", "method": "fetchBalance", "url": "https://open-api.bingx.com/openApi/cswap/v1/user/balance?timestamp=*************&signature=d3850755c7051578a7adaa294676fd1b4a63de55a3a9dd42c7dcdbabae901e20", "input": [{"type": "swap", "subType": "inverse"}]}], "fetchPositions": [{"description": "Fetch linear position", "method": "fetchPositions", "url": "https://open-api.bingx.com/openApi/swap/v2/user/positions?timestamp=*************&signature=93152aeb9f616ec2e2ceb540f0777b9d0e2adbbfa45b460540f682c7cd583474", "input": [["LTC/USDT:USDT"]]}, {"description": "Fetch inverse positions", "method": "fetchPositions", "url": "https://open-api.bingx.com/openApi/cswap/v1/user/positions?timestamp=1721107174594&signature=b2504a634ce468010d77aa40e8e863abe15a907c99705f59852ca1d12aac7910", "input": [["SOL/USD:SOL"]]}], "fetchPosition": [{"description": "Linear swap fetch position", "method": "fetchPosition", "url": "https://open-api.bingx.com/openApi/swap/v2/user/positions?symbol=LTC-USDT&timestamp=1721117826435&signature=fb2ec2c92b0b1a9461a6920afbb88126c2c60b0fa06cfd42117bf0585f3b5a03", "input": ["LTC/USDT:USDT"]}, {"description": "Inverse swap fetch position", "method": "fetchPosition", "url": "https://open-api.bingx.com/openApi/cswap/v1/user/positions?symbol=SOL-USD&timestamp=1721117705958&signature=e4abe8036bd04de44f2b76c6099dd0c164482c74db42345c1ffad54297b75878", "input": ["SOL/USD:SOL"]}], "fetchDeposits": [{"description": "Fetch deposits", "method": "fetchDeposits", "url": "https://open-api.bingx.com/openApi/api/v3/capital/deposit/hisrec?timestamp=1699458296035&signature=10ada6f50500dd8090dfb7ecb8c03d093a7278b66be5568f03ad7e0dc4c3b371", "input": []}], "fetchWithdrawals": [{"description": "Fetch withdrawals", "method": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://open-api.bingx.com/openApi/api/v3/capital/withdraw/history?timestamp=*************&signature=b11060a953c0a537fba24da3346f55d0a05196748704bc34f40fcfbf0b0a4ea6", "input": []}], "transfer": [{"description": "transfer LTC from inverse swap to spot using the subType parameter", "disabledGO": true, "method": "transfer", "url": "https://open-api.bingx.com/openApi/api/asset/v1/transfer?amount=0.05&asset=LTC&fromAccount=USDTMPerp&timestamp=*************&toAccount=spot&signature=1759012ec5ebbca1ad6a823da98ce4689dbf667aa5a01ee9ea553e23f2f1e010", "input": ["LTC", 0.05, "swap", "spot", {"subType": "inverse"}]}, {"description": "transfer USDT from spot to linear swap", "disabledGO": true, "method": "transfer", "url": "https://open-api.bingx.com/openApi/api/asset/v1/transfer?amount=10&asset=USDT&fromAccount=spot&timestamp=*************&toAccount=USDTMPerp&signature=a0da8f16bac616605f88b75f7a3200625e35df8693b7ceca4d2872790fda7314", "input": ["USDT", 10, "spot", "swap"]}], "fetchDepositAddress": [{"description": "call without params['network']", "method": "fetchDepositAddress", "url": "https://open-api.bingx.com/openApi/wallets/v1/capital/deposit/address?coin=USDT&limit=1000&offset=0&recvWindow=5000&timestamp=*************&signature=5a8a671eaaf15e5dfeb21d88e73b979ae6baf1b166cb9f0a21c4ccb73e6662d1", "input": ["USDT"]}, {"description": "call with params['network']", "method": "fetchDepositAddress", "url": "https://open-api.bingx.com/openApi/wallets/v1/capital/deposit/address?coin=USDT&limit=1000&offset=0&recvWindow=5000&timestamp=*************&signature=163735031e089055bf663eb52b57942021a8876a0abd14f8c6b7805478e4f56b", "input": ["USDT", {"network": "TRC20"}]}], "fetchDepositAddressesByNetwork": [{"description": "basic call", "method": "fetchDepositAddressesByNetwork", "url": "https://open-api.bingx.com/openApi/wallets/v1/capital/deposit/address?coin=USDT&limit=1000&offset=0&recvWindow=5000&timestamp=1707439590326&signature=0736d7b95d80300a63dec4f2d5e5bac7cc33c21605f46b89e9e2a8d527dbb800", "input": ["USDT"]}], "setPositionMode": [{"description": "Set position mode", "method": "setPositionMode", "url": "https://open-api.bingx.com/openApi/swap/v1/positionSide/dual?dualSidePosition=true&timestamp=1698777135343&signature=d55a7e4f7f9dbe56c4004c9f3ab340869d3cb004e2f0b5b861e5fbd1762fd9a0", "input": [true]}], "fetchMyTrades": [{"description": "fetch my trades - spot", "method": "fetchMyTrades", "url": "https://open-api.bingx.com/openApi/spot/v1/trade/myTrades?symbol=LTC-USDT&timestamp=1699460638640&signature=e5942598ddd9d11cceec8f7e2acb3269c8c2dfe237642314eca3b5d8a5c308e0", "input": ["LTC/USDT"]}, {"description": "fetch my trades - swap", "method": "fetchMyTrades", "url": "https://open-api.bingx.com/openApi/swap/v2/trade/allFillOrders?endTs=1705040628035&startTs=1687964205000&symbol=LTC-USDT&tradingUnit=CONT&timestamp=1699460638640&signature=e5942598ddd9d11cceec8f7e2acb3269c8c2dfe237642314eca3b5d8a5c308e0", "input": ["LTC/USDT:USDT", 1687964205000]}, {"description": "Inverse swap fetch my trades with an orderId parameter", "method": "fetchMyTrades", "url": "https://open-api.bingx.com/openApi/cswap/v1/trade/allFillOrders?orderId=1817441228670648320&timestamp=1722148745974&signature=a926e3297d82ba03838a706b1b8827677065edab600277afe7256e3e43d83d05", "input": ["SOL/USD:SOL", null, null, {"orderId": "1817441228670648320"}]}], "fetchMarkOHLCV": [{"description": "Fetch mark ohlcv", "method": "fetchMarkOHLCV", "url": "https://open-api.bingx.com/openApi/swap/v1/market/markPriceKlines?interval=1h&startTime=4&symbol=BTC-USDT&timestamp=1706521154506", "input": ["BTC/USDT:USDT", "1h", 5]}], "editOrder": [{"description": "edit spot order", "method": "editOrder", "url": "https://open-api.bingx.com/openApi/spot/v1/trade/order/cancelReplace?cancelOrderId=1755603335580221440&cancelReplaceMode=STOP_ON_FAILURE&price=51&quantity=0.11&side=BUY&symbol=LTC-USDT&timestamp=1707403466447&type=LIMIT&signature=0bee060e6424f8eba8ed39077e38408c743148d3c6c92e3d1d5d0f540a71d5d8", "input": ["1755603335580221440", "LTC/USDT", "limit", "buy", 0.11, 51]}, {"description": "edit swap order", "method": "editOrder", "url": "https://open-api.bingx.com/openApi/swap/v1/trade/cancelReplace?cancelOrderId=1755603986301546496&cancelReplaceMode=STOP_ON_FAILURE&positionSide=BOTH&price=51&quantity=0.1&side=BUY&symbol=LTC-USDT&timestamp=1707403614874&type=LIMIT&signature=a1c7f4e3b91ee4f615fac9164cc031abfa2ee5a33783341400a6258f845d21c0", "input": ["1755603986301546496", "LTC/USDT:USDT", "limit", "buy", 0.11, 51]}, {"description": "edit limit order with tp/sl", "disabled": true, "method": "editOrder", "url": "https://open-api.bingx.com/openApi/swap/v1/trade/cancelReplace?cancelOrderId=1783855116533854208&cancelReplaceMode=STOP_ON_FAILURE&positionSide=BOTH&price=50&quantity=1&side=BUY&stopLoss%5BstopPrice%5D=31&symbol=LTC-USDT&takeProfit%5BstopPrice%5D=151&timestamp=1714139224397&type=LIMIT&signature=3d7478c3f8a6d96717f2478c9b03c926295fbb66977023f81013d3861ce29719", "input": ["1783855116533854208", "LTC/USDT:USDT", "limit", "buy", 1, 50, {"takeProfit": {"stopPrice": 151}, "stopLoss": {"stopPrice": 31}, "positionSide": "BOTH"}]}, {"description": "edit spot takeProfit limit order", "method": "editOrder", "url": "https://open-api.bingx.com/openApi/spot/v1/trade/order/cancelReplace?cancelOrderId=1783861005164478464&cancelReplaceMode=STOP_ON_FAILURE&price=151&quantity=0.1&side=SELL&stopPrice=145&symbol=LTC-USDT&takeProfitPrice=145&timestamp=1714140647187&type=TAKE_STOP_LIMIT&signature=f7f996ed4d886028cb510a123213fa91cdb871387c29ca1d02954e967b514e07", "input": ["1783861005164478464", "LTC/USDT", "limit", "sell", 0.1, 151, {"takeProfitPrice": 145}]}, {"description": "edit spot market stopLoss order", "method": "editOrder", "url": "https://open-api.bingx.com/openApi/spot/v1/trade/order/cancelReplace?cancelOrderId=1783861529486032896&cancelReplaceMode=STOP_ON_FAILURE&quantity=0.1&side=SELL&stopLossPrice=35&stopPrice=35&symbol=LTC-USDT&timestamp=1714140765141&type=TAKE_STOP_MARKET&signature=220a7571f2810f148e6a12587ae48528cad6fb58026968254e50948b761658d1", "input": ["1783861529486032896", "LTC/USDT", "market", "sell", 0.1, null, {"stopLossPrice": 35}]}], "fetchOrder": [{"description": "fetch canceled swap order", "method": "fetchOrder", "url": "https://open-api.bingx.com/openApi/swap/v2/trade/order?orderId=1755604122901639168&symbol=LTC-USDT&timestamp=1707404139157&signature=8fad2c2153024cbe332cb21665f2ccd7e0dbdae296ee68ca8081dac561aadc3e", "input": ["1755604122901639168", "LTC/USDT:USDT"]}, {"description": "fetch canceled spot order", "method": "fetchOrder", "url": "https://open-api.bingx.com/openApi/spot/v1/trade/query?orderId=1755603500760301568&symbol=LTC-USDT&timestamp=1707404205248&signature=08baae73177b16e8088332a7dd9bc48409ae4711207c220517f6588880048d8d", "input": ["1755603500760301568", "LTC/USDT"]}, {"description": "Inverse swap fetch order", "method": "fetchOrder", "url": "https://open-api.bingx.com/openApi/cswap/v1/trade/orderDetail?orderId=1816342420721254400&symbol=SOL-USD&timestamp=1721884780520&signature=4523f7d1eac99df0343592a6cb8ab185bf46c42ce63541c47b29b091bbc3c794", "input": ["1816342420721254400", "SOL/USD:SOL"]}, {"description": "twap fetch order", "method": "fetchOrder", "url": "https://open-api.bingx.com/openApi/swap/v1/twap/orderDetail?mainOrderId=5596903086063901779&timestamp=1732761562641&signature=c6e2add38151575bcbf8bdd8f813670679e61b63d5aa050b6b70d1f29f83c7f8", "input": ["5596903086063901779", null, {"twap": true}]}], "fetchPositionMode": [{"description": "fetch position mode", "method": "fetchPositionMode", "url": "https://open-api.bingx.com/openApi/swap/v1/positionSide/dual?timestamp=1709028142925&signature=209d60d4da49e3e26e8e5b5181b0141a0e44d047e342957565cd78ee44e878d2", "input": ["BTC/USDT:USDT"]}], "setMarginMode": [{"description": "Linear swap set the margin mode", "method": "setMarginMode", "url": "https://open-api.bingx.com/openApi/swap/v2/trade/marginType?marginType=ISOLATED&symbol=BTC-USDT&timestamp=1709612405970&signature=ad4ff86204ba2cb9963a64e13a858215a2a1e31d2f10616ce5b0bbf64558800d", "input": ["isolated", "BTC/USDT:USDT"]}, {"description": "Inverse swap set the margin mode", "method": "setMarginMode", "url": "https://open-api.bingx.com/openApi/cswap/v1/trade/marginType?marginType=CROSSED&symbol=SOL-USD&timestamp=1721977131380&signature=ab360e84bb56b27440fe1ca0835e07d936eaa75b4bdf57feb6771de45865e2bc", "input": ["cross", "SOL/USD:SOL"]}], "fetchMarginMode": [{"description": "Linear swap fetch the margin mode", "method": "fetchMarginMode", "url": "https://open-api.bingx.com/openApi/swap/v2/trade/marginType?symbol=BTC-USDT&timestamp=1709612311793&signature=a327894546ea7337cc55fa527bc183bba415213c48f0b9dafdc27bb6c38d8df9", "input": ["BTC/USDT:USDT"]}, {"description": "Inverse swap fetch the margin mode", "method": "fetchMarginMode", "url": "https://open-api.bingx.com/openApi/cswap/v1/trade/marginType?symbol=SOL-USD&timestamp=1721966067969&signature=72cd480f000e3d2eabd520ba5e5778f58dc2508ff9720cbc8fc1c3870f6a968b", "input": ["SOL/USD:SOL"]}], "fetchTime": [{"description": "fetchTime", "method": "fetchTime", "url": "https://open-api.bingx.com/openApi/swap/v2/server/time?timestamp=1709992982189", "input": []}], "fetchTrades": [{"description": "spot fetchTrades", "method": "fetchTrades", "url": "https://open-api.bingx.com/openApi/spot/v1/market/trades?symbol=BTC-USDT&timestamp=1709992984605", "input": ["BTC/USDT"]}, {"description": "swap fetchTrades", "method": "fetchTrades", "url": "https://open-api.bingx.com/openApi/swap/v2/quote/trades?symbol=BTC-USDT&timestamp=1709992984861", "input": ["BTC/USDT:USDT"]}], "fetchOrderBook": [{"description": "spot orderbook", "method": "fetchOrderBook", "url": "https://open-api.bingx.com/openApi/spot/v1/market/depth?symbol=BTC-USDT&timestamp=1709992985098", "input": ["BTC/USDT"]}, {"description": "swap orderbook", "method": "fetchOrderBook", "url": "https://open-api.bingx.com/openApi/swap/v2/quote/depth?symbol=BTC-USDT&timestamp=1709992985380", "input": ["BTC/USDT:USDT"]}, {"description": "inverse swap orderbook with symbol and limit arguments", "method": "fetchOrderBook", "url": "https://open-api.bingx.com/openApi/cswap/v1/market/depth?limit=5&symbol=BTC-USD&timestamp=1720164891997", "input": ["BTC/USD:BTC", 5]}], "fetchTicker": [{"description": "Swap ticker", "method": "fetchTicker", "url": "https://open-api.bingx.com/openApi/swap/v2/quote/ticker?symbol=BTC-USDT&timestamp=1709992985652", "input": ["BTC/USDT:USDT"]}, {"description": "spot ticker", "method": "fetchTicker", "url": "https://open-api.bingx.com/openApi/spot/v1/ticker/24hr?symbol=BTC-USDT&timestamp=1709992985897", "input": ["BTC/USDT"]}, {"description": "Inverse swap fetch ticker", "method": "fetchTicker", "url": "https://open-api.bingx.com/openApi/cswap/v1/market/ticker?symbol=SOL-USD&timestamp=1720647610512", "input": ["SOL/USD:SOL"]}], "fetchTickers": [{"description": "spot tickers", "method": "fetchTickers", "url": "https://open-api.bingx.com/openApi/spot/v1/ticker/24hr?timestamp=1709992986125", "input": [["BTC/USDT", "ETH/USDT"]]}, {"description": "swap tickers", "method": "fetchTickers", "url": "https://open-api.bingx.com/openApi/swap/v2/quote/ticker?timestamp=1709992986457", "input": [["BTC/USDT:USDT", "ETH/USDT:USDT"]]}, {"description": "Inverse swap fetch tickers", "method": "fetchTickers", "url": "https://open-api.bingx.com/openApi/cswap/v1/market/ticker?timestamp=1720649348918", "input": [null, {"type": "swap", "subType": "inverse"}]}], "fetchOHLCV": [{"description": "spot ohlcv", "method": "fetchOHLCV", "url": "https://open-api.bingx.com/openApi/spot/v1/market/kline?interval=1m&symbol=BTC-USDT&timestamp=1709992986762", "input": ["BTC/USDT"]}, {"description": "swap ohlcv", "method": "fetchOHLCV", "url": "https://open-api.bingx.com/openApi/swap/v3/quote/klines?interval=1m&symbol=BTC-USDT&timestamp=1709992987069", "input": ["BTC/USDT:USDT"]}, {"description": "inverse swap ohlcv with timeframe and limit arguments", "method": "fetchOHLCV", "url": "https://open-api.bingx.com/openApi/cswap/v1/market/klines?interval=1m&limit=3&symbol=BTC-USD&timestamp=1720124710777", "input": ["BTC/USD:BTC", "1m", null, 3]}], "fetchFundingRateHistory": [{"description": "fundingRateHistory", "method": "fetchFundingRateHistory", "url": "https://open-api.bingx.com/openApi/swap/v2/quote/fundingRate?symbol=BTC-USDT&timestamp=1709992987329", "input": ["BTC/USDT:USDT"]}], "fetchFundingRate": [{"description": "fundingRate", "method": "fetchFundingRate", "url": "https://open-api.bingx.com/openApi/swap/v2/quote/premiumIndex?symbol=BTC-USDT&timestamp=1709992987600", "input": ["BTC/USDT:USDT"]}, {"description": "inverse swap fetch funding rate", "method": "fetchFundingRate", "url": "https://open-api.bingx.com/openApi/cswap/v1/market/premiumIndex?symbol=BTC-USD&timestamp=1720252368816", "input": ["BTC/USD:BTC"]}], "addMargin": [{"description": "<PERSON><PERSON>", "method": "add<PERSON><PERSON>gin", "url": "https://open-api.bingx.com/openApi/swap/v2/trade/positionMargin?amount=1&symbol=BTC-USDT&timestamp=1711660769522&type=1&signature=00b43a627d45679f409ae85edb17d8c457c3dd3c91d8ab1058de7ad004aea448", "input": ["BTC/USDT:USDT", 1]}], "reduceMargin": [{"description": "reduce<PERSON><PERSON>gin", "method": "reduce<PERSON><PERSON>gin", "url": "https://open-api.bingx.com/openApi/swap/v2/trade/positionMargin?amount=1&symbol=BTC-USDT&timestamp=1711660858980&type=2&signature=bde504579e677b43e8450197833dd7992680de74fa1926c4814578dd2b1a5ed9", "input": ["BTC/USDT:USDT", 1]}], "fetchMyLiquidations": [{"disabled": true, "description": "Linear swap fetch my liquidations", "method": "fetchMyLiquidations", "url": "https://open-api.bingx.com/openApi/swap/v2/trade/forceOrders?autoCloseType=LIQUIDATION&symbol=BTC-USDT&timestamp=1713364573101&signature=f8eb5457ce479316113f59ab7c9be200d7bd2b39e394a480143b0f2ec59a1d91", "input": ["BTC/USDT:USDT"]}, {"disabled": true, "description": "Inverse swap fetch my liquidations", "method": "fetchMyLiquidations", "url": "https://open-api.bingx.com/openApi/cswap/v1/trade/forceOrders?autoCloseType=LIQUIDATION&symbol=SOL-USD&timestamp=1721280070629&signature=32b7e26877af1f0ed4f3936811cb38f68bc27eac5e44b63022de5a92123f6b7f", "input": ["SOL/USD:SOL"]}], "fetchOpenInterest": [{"description": "Linear swap fetch open interest", "method": "fetchOpenInterest", "url": "https://open-api.bingx.com/openApi/swap/v2/quote/openInterest?symbol=BTC-USDT&timestamp=1720328413135", "input": ["BTC/USDT:USDT"]}, {"description": "Inverse swap fetch open interest", "method": "fetchOpenInterest", "url": "https://open-api.bingx.com/openApi/cswap/v1/market/openInterest?symbol=BTC-USD&timestamp=1720328338678", "input": ["BTC/USD:BTC"]}], "fetchLeverage": [{"description": "Linear swap fetch leverage", "method": "fetchLeverage", "url": "https://open-api.bingx.com/openApi/swap/v2/trade/leverage?symbol=BTC-USDT&timestamp=1720683570758&signature=313bec0e260abef0a888e7ffe2bbfe6c44a6c250ad5bf31b4795cb551cf7641a", "input": ["BTC/USDT:USDT"]}, {"description": "Inverse swap fetch leverage", "method": "fetchLeverage", "url": "https://open-api.bingx.com/openApi/cswap/v1/trade/leverage?symbol=SOL-USD&timestamp=1720684074921&signature=8afc0c1f4a6a6cd3416479af6be10a2532718c8e5d50cc2e944bec2cbf55ad39", "input": ["SOL/USD:SOL"]}], "setLeverage": [{"description": "Linear swap set leverage", "method": "setLeverage", "url": "https://open-api.bingx.com/openApi/swap/v2/trade/leverage?leverage=10&side=BOTH&symbol=BTC-USDT&timestamp=1720724882147&signature=e3fb41d7b2f23d667bf289c5998dc6fac9a3a45bf2e6f022ce3c6429a440dc75", "input": [10, "BTC/USDT:USDT", {"side": "BOTH"}]}, {"description": "Inverse swap set leverage", "method": "setLeverage", "url": "https://open-api.bingx.com/openApi/cswap/v1/trade/leverage?leverage=10&side=LONG&symbol=SOL-USD&timestamp=1720725057859&signature=c30d2e7a593d5e54304bf026193a21ad682492a2c4d026f63fe0a68a8c367b3f", "input": [10, "SOL/USD:SOL", {"side": "LONG"}]}], "fetchTradingFee": [{"description": "Spot fetch trading fee", "method": "fetchTradingFee", "url": "https://open-api.bingx.com/openApi/spot/v1/user/commissionRate?symbol=BTC-USDT&timestamp=1721369051762&signature=9fc2fc12f8f0139fb5b47fa5dfd7b7be239da2b6c55e99a84e67c19df1022c87", "input": ["BTC/USDT"]}, {"description": "linear swap trading trading fee", "method": "fetchTradingFee", "url": "https://open-api.bingx.com/openApi/swap/v2/user/commissionRate?timestamp=1721381183575&signature=4265388b5907de2e012b4567fb6bf146a4d44951f92fb8e8003a3c31cf204259", "input": ["BTC/USDT:USDT"]}, {"description": "Inverse swap fetch trading fee", "method": "fetchTradingFee", "url": "https://open-api.bingx.com/openApi/cswap/v1/user/commissionRate?timestamp=1721370753542&signature=82ecf511c5fdb7655ef37166633af2dbacc75b0c9a3e6f6994cddd77db45096f", "input": ["BTC/USD:BTC"]}], "fetchMarkPrices": [{"description": "fetchMarkPrices", "method": "fetchMarkPrices", "url": "https://open-api.bingx.com/openApi/swap/v2/quote/premiumIndex?timestamp=1700747597612", "input": []}], "fetchMarkPrice": [{"description": "fetchMarkPrice", "method": "fetchMarkPrice", "url": "https://open-api.bingx.com/openApi/swap/v2/quote/premiumIndex?timestamp=1700747597612&symbol=BTC-USDT", "input": ["BTC/USDT"]}, {"description": "fetchMarkPrice", "method": "fetchMarkPrice", "url": "https://open-api.bingx.com/openApi/cswap/v1/market/premiumIndex?timestamp=1700747597612&symbol=BTC-USD", "input": ["BTC/USD:BTC"]}], "fetchPositionHistory": [{"description": "linear swap fetch position history with a limit argument", "method": "fetchPositionHistory", "url": "https://open-api.bingx.com/openApi/swap/v1/trade/positionHistory?pageSize=1&symbol=LTC-USDT&timestamp=1733560485047&signature=67ac344ca78b626723c3ce2cc9cd8351915cff034880edf8539f42d5461ddc8e", "input": ["LTC/USDT:USDT", null, 1]}]}}