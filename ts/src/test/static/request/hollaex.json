{"exchange": "holla<PERSON>", "skipKeys": ["to"], "outputType": "json", "methods": {"fetchOHLCV": [{"description": "fetchOHLCV with since", "method": "fetchOHLCV", "url": "https://api.hollaex.com/v2/chart?symbol=eth-usdt&resolution=1m&from=1750165658&to=1750195658", "input": ["ETH/USDT", "1m", 1750165658608], "output": null}], "createOrder": [{"description": "create a limit test order", "method": "createOrder", "url": "https://api.sandbox.hollaex.com/v2/order", "input": ["SHIB/USDT", "limit", "buy", 1000, 1e-06], "output": "{\"symbol\":\"shib-usdt\",\"side\":\"buy\",\"size\":\"1000\",\"type\":\"limit\",\"price\":\"0.000001\"}"}], "fetchCurrencies": [{"description": "fetchCurrencies", "method": "fetchCurrencies", "url": "https://api.hollaex.com/v2/constants", "input": [], "output": null}], "fetchOrderBook": [{"description": "Spot fetch order book", "method": "fetchOrderBook", "url": "https://api.hollaex.com/v2/orderbook?symbol=btc-usdt", "input": ["BTC/USDT"]}, {"description": "spot orderbook", "method": "fetchOrderBook", "url": "https://api.hollaex.com/v2/orderbook?symbol=btc-usdt", "input": ["BTC/USDT"]}], "fetchTrades": [{"description": "spot fetchTrades", "method": "fetchTrades", "url": "https://api.hollaex.com/v2/trades?symbol=btc-usdt", "input": ["BTC/USDT"]}], "fetchTicker": [{"description": "spot ticker", "method": "fetchTicker", "url": "https://api.hollaex.com/v2/ticker?symbol=btc-usdt", "input": ["BTC/USDT"]}], "fetchTickers": [{"description": "spot tickers", "method": "fetchTickers", "url": "https://api.hollaex.com/v2/tickers", "input": [["BTC/USDT", "ETH/USDT"]]}], "fetchDeposits": [{"description": "fetch deposits", "method": "fetchDeposits", "url": "https://api.sandbox.hollaex.com/v2/user/deposits", "input": []}, {"description": "fetch deposits with a currency", "method": "fetchDeposits", "url": "https://api.sandbox.hollaex.com/v2/user/deposits?currency=usdt", "input": ["USDT"]}], "fetchWithdrawals": [{"description": "fetch Withdrawals", "method": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://api.sandbox.hollaex.com/v2/user/withdrawals", "input": []}, {"description": "fetch Withdrawals with a currency", "method": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://api.sandbox.hollaex.com/v2/user/withdrawals?currency=usdt", "input": ["USDT"]}], "fetchDepositAddresses": [{"description": "fetch deposit addresses", "method": "fetchDepositAddresses", "url": "https://api.sandbox.hollaex.com/v2/user", "input": []}, {"description": "fetch deposit addresses with codes argument", "method": "fetchDepositAddresses", "url": "https://api.sandbox.hollaex.com/v2/user", "input": [["ETH"]]}], "fetchMyTrades": [{"description": "fetch my trades", "method": "fetchMyTrades", "url": "https://api.sandbox.hollaex.com/v2/user/trades", "input": []}, {"description": "fetch my trades with symbol", "method": "fetchMyTrades", "url": "https://api.sandbox.hollaex.com/v2/user/trades?symbol=btc-usdt", "input": ["BTC/USDT"]}], "fetchDepositWithdrawFees": [{"description": "<PERSON><PERSON> Deposit <PERSON>draw <PERSON>", "method": "fetchDepositWithdrawFees", "url": "https://api.sandbox.hollaex.com/v2/constants", "input": []}, {"description": "fetch Deposit Withdraw Fees with codes argument", "method": "fetchDepositWithdrawFees", "url": "https://api.sandbox.hollaex.com/v2/constants", "input": [["ETH"]]}]}}