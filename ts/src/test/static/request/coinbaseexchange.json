{"exchange": "coinbasepro", "skipKeys": [], "outputType": "json", "methods": {"fetchCurrencies": [{"description": "fetchCurrencies", "method": "fetchCurrencies", "url": "https://api.exchange.coinbase.com/currencies", "input": [], "output": null}], "createOrder": [{"description": "Spot limit buy", "method": "createOrder", "url": "https://api.pro.coinbase.com/orders", "input": ["XRP/USDT", "limit", "buy", 5, 0.5], "output": "{\"type\":\"limit\",\"side\":\"buy\",\"product_id\":\"XRP-USDT\",\"price\":\"0.5\",\"size\":\"5\"}"}, {"description": "Spot market buy", "method": "createOrder", "url": "https://api.pro.coinbase.com/orders", "input": ["XRP/USDT", "market", "buy", 5], "output": "{\"type\":\"market\",\"side\":\"buy\",\"product_id\":\"XRP-USDT\",\"size\":\"5\"}"}, {"description": "Spot market sell", "method": "createOrder", "url": "https://api.pro.coinbase.com/orders", "input": ["XRP/USDT", "market", "sell", 5], "output": "{\"type\":\"market\",\"side\":\"sell\",\"product_id\":\"XRP-USDT\",\"size\":\"5\"}"}, {"description": "Spot limit buy with postOnly", "method": "createOrder", "url": "https://api.pro.coinbase.com/orders", "input": ["XRP/USDT", "limit", "buy", 5, 0.5, {"postOnly": true}], "output": "{\"type\":\"limit\",\"side\":\"buy\",\"product_id\":\"XRP-USDT\",\"post_only\":true,\"price\":\"0.5\",\"size\":\"5\"}"}, {"description": "Spot limit buy with triggerPrice", "method": "createOrder", "url": "https://api.pro.coinbase.com/orders", "input": ["XRP/USDT", "limit", "buy", 5, 0.5, {"triggerPrice": 90}], "output": "{\"type\":\"limit\",\"side\":\"buy\",\"product_id\":\"XRP-USDT\",\"stop_price\":\"90\",\"price\":\"0.5\",\"size\":\"5\"}"}], "cancelOrder": [{"description": "Cancel spot order", "method": "cancelOrder", "url": "https://api.pro.coinbase.com/orders/81072801-6509-408a-9ed5-e03906040f62", "input": ["81072801-6509-408a-9ed5-e03906040f62", "XRP/USDT"], "output": "{\"product_id\":\"XRP/USDT\"}"}], "fetchOrder": [{"description": "Fetch spot order", "method": "fetchOrder", "url": "https://api.pro.coinbase.com/orders/91fad5ff-e7bb-4346-a581-312a6665b74b", "input": ["91fad5ff-e7bb-4346-a581-312a6665b74b", "XRP/USDT"]}], "fetchOpenOrders": [{"description": "fetch spot open orders", "method": "fetchOpenOrders", "url": "https://api.pro.coinbase.com/orders", "input": []}], "fetchMyTrades": [{"description": "Spot fetch my trades", "method": "fetchMyTrades", "url": "https://api.pro.coinbase.com/fills?product_id=XRP-USDT", "input": ["XRP/USDT"]}], "fetchBalance": [{"description": "Fetch spot Balance", "method": "fetchBalance", "url": "https://api.pro.coinbase.com/accounts?type=spot", "input": [{"type": "spot"}]}, {"description": "Fetch swap Balance", "method": "fetchBalance", "url": "https://api.pro.coinbase.com/accounts?type=swap", "input": [{"type": "swap"}]}], "fetchDeposits": [{"description": "Fetch deposits", "method": "fetchDeposits", "url": "https://api.pro.coinbase.com/transfers?type=deposit", "input": []}], "fetchWithdrawals": [{"description": "Fetch withdrawals", "method": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://api.pro.coinbase.com/transfers?type=withdraw", "input": []}], "cancelAllOrders": [{"description": "Cancel all spot orders", "method": "cancelAllOrders", "url": "https://api.pro.coinbase.com/orders", "input": ["XRP/USDT"], "output": "{\"product_id\":\"XRP/USDT\"}"}], "fetchTime": [{"description": "fetchTime", "method": "fetchTime", "url": "https://api.pro.coinbase.com/time", "input": []}], "fetchTrades": [{"description": "spot fetchTrades", "method": "fetchTrades", "url": "https://api.pro.coinbase.com/products/BTC-USDT/trades", "input": ["BTC/USDT"]}], "fetchOrderBook": [{"description": "spot orderbook", "method": "fetchOrderBook", "url": "https://api.pro.coinbase.com/products/BTC-USDT/book?level=2", "input": ["BTC/USDT"]}], "fetchTicker": [{"description": "spot ticker", "method": "fetchTicker", "url": "https://api.pro.coinbase.com/products/BTC-USDT/ticker", "input": ["BTC/USDT"]}], "fetchTickers": [{"description": "spot tickers", "method": "fetchTickers", "url": "https://api.pro.coinbase.com/products/spark-lines", "input": [["BTC/USDT", "ETH/USDT"]]}], "fetchOHLCV": [{"description": "spot ohlcv", "method": "fetchOHLCV", "url": "https://api.pro.coinbase.com/products/BTC-USDT/candles?granularity=60", "input": ["BTC/USDT"]}]}}