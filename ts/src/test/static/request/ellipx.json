{"exchange": "ellipx", "skipKeys": ["_key", "_time", "_nonce", "_sign"], "outputType": "both", "apiKey": "key-hwlf2a-u2hj-fh7m-is6j-5vbu5ta3", "secret": "vSQk28OnxEED39SxG5aiLNgPQaVm8wRvU-XY_loL7g374GLRGrbZzRm-5dgW_GFX3Eu079FmxWF8Rc1ed0uioQ", "methods": {"fetchCurrencies": [{"description": "fetchCurrencies", "method": "fetchCurrencies", "url": "https://app.ellipx.com/_rest/Crypto/Token/Info?Can_Deposit=Y&results_per_page=100&_expand=%2FCrypto_Token%2C%2FCrypto_Chain", "input": [], "output": null}], "fetchOrder": [{"description": "fetch order", "method": "fetchOrder", "url": "https://app.ellipx.com/_rest/Market/Order/mktor-hqufk4-emrb-gk5o-jkes-mqk4gqnu?_key=key-hwlf2a-u2hj-fh7m-is6j-5vbu5tr4&_time=1732370018&_nonce=996ee6af-8444-4516-ae60-8e59ce292b2e&orderUuid=mktor-hqufk4-emrb-gk5o-jkes-mqk4gqnu&_sign=XAIBVGcgHY4eDJLaJ0SeCO4MTlmFVVUhZtzees%2FEoUYnwWzyPW0WwIYTMap8xptUb0myuGEwRCJGqbenuDqQDg%3D%3D", "input": ["mktor-hqufk4-emrb-gk5o-jkes-mqk4gqnu"], "output": null}], "createOrder": [{"description": "limit buy order", "method": "createOrder", "url": "https://app.ellipx.com/_rest/Market/DOGE_USDC/Order?_key=key-hwlf2a-u2hj-fh7m-is6j-5vbu5tr4&_time=1732625297&_nonce=f990cba1-08a7-485e-bc91-e45059d8b8a9&currencyPair=DOGE_USDC&Type=bid&Amount=10&Price=0.3&_sign=SMLaV4UhvdkadAHvKuNW7U%2FuCL%2Fx%2Fg2Ie1yupZE1HpsPca4xNbuufTSTunZ8BOa5IAj053dB9Wj4LAnXeCLECA%3D%3D", "input": ["DOGE/USDC", "limit", "buy", 10, 0.3], "output": "{\"currencyPair\":\"DOGE_USDC\",\"Type\":\"bid\",\"Amount\":\"10\",\"Price\":\"0.3\"}"}, {"description": "market buy order", "method": "createOrder", "url": "https://app.ellipx.com/_rest/Market/DOGE_USDC/Order?_key=key-hwlf2a-u2hj-fh7m-is6j-5vbu5tr4&_time=1732369899&_nonce=10cc21f1-6574-4488-975d-de8f477e893d&currencyPair=DOGE_USDC&Type=bid&Amount=10&_sign=n8uvGz28t0yP7XtGWPpVTWuvwmPaXRCN940RH7zWGUAH%2BlXNiueZfuJ3%2FR5UulW9LKM0up8WC3SA2jowYcLBCg%3D%3D", "input": ["DOGE/USDC", "market", "buy", 10], "output": "{\"currencyPair\":\"DOGE_USDC\",\"Type\":\"bid\",\"Amount\":\"10\"}"}], "fetchOHLCV": [{"description": "fetch ohlcv", "method": "fetchOHLCV", "url": "https://data.ellipx.com/Market/BTC_USDC:getGraph?currencyPair=BTC_USDC&interval=1m", "input": ["BTC/USDC"], "output": null}], "fetchTrades": [{"description": "fetch ticker", "method": "fetchTrades", "url": "https://data.ellipx.com/Market/BTC_USDC:getTrades?currencyPair=BTC_USDC", "input": ["BTC/USDC"], "output": null}], "fetchTicker": [{"description": "fetch ticker", "method": "fetchTicker", "url": "https://data.ellipx.com/Market/BTC_USDC:ticker?currencyPair=BTC_USDC", "input": ["BTC/USDC"], "output": null}], "fetchBalance": [{"description": "fetch balance", "method": "fetchBalance", "url": "https://app.ellipx.com/_rest/User/Wallet?_key=key-hwlf2a-u2hj-fh7m-is6j-5vbu5tr4&_time=1732277464&_nonce=085a8b7b-c8dc-48d6-a48e-35b773deaf51&_sign=%2FR5MpsGpemosA0eMDQgmKBfLqIL3HTBNscNG8lW1adrNRAvZM%2BxQV%2BVBHGZFM9RB5iUq6k5gKrp0o3rPzY4HDA%3D%3D", "input": [], "output": null}]}}