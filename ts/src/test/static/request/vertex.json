{"exchange": "vertex", "skipKeys": ["sender", "nonce", "expiration", "signature", "subaccount", "recvTime", "cancel_signature"], "walletAddress": "******************************************", "privateKey": "c33b1eb4b53108bf52e10f636d8c1236c04c33a712357ba3543ab45f48a5cb0b", "options": {"v1contracts": {"chain_id": "42161", "endpoint_addr": "******************************************", "book_addrs": ["******************************************", "******************************************", "******************************************", "******************************************", "******************************************", "******************************************", "******************************************", "******************************************", "******************************************", "******************************************", "******************************************", "******************************************", "******************************************", "******************************************", "******************************************", "******************************************", "0x5ecf68f983253a818ca8c17a56a4f2fb48d6ec6b", "******************************************", "0xba3f57a977f099905531f7c2f294aad7b56ed254", "******************************************", "0x0ac8c26d207d0c6aabb3644fea18f530c4d6fc8e", "******************************************", "0x8bd80ad7630b3864bed66cf28f548143ea43dc3b", "******************************************", "0x045391227fc4b2cdd27b95f066864225afc9314e", "******************************************", "0x7d512bef2e6cfd7e7f5f6b2f8027e3728eb7b6c3", "******************************************", "0x678a6c5003b56b5e9a81559e9a0df880407c796f", "******************************************", "0x14b5a17208fa98843cc602b3f74e31c95ded3567", "0xe442a89a07b3888ab10579fbb2824aeceff3a282", "******************************************", "******************************************", "0xac28ac205275d7c2d6877bea8657cebe04fd9ae9", "******************************************", "0xed811409bfea901e75cb19ba347c08a154e860c9", "******************************************", "0x0f7afcb1612b305626cff84f84e4169ba2d0f12c", "******************************************", "0xe4b8d903db2ce2d3891ef04cfc3ac56330c1b0c3", "0x5f44362bad629846b7455ad9d36bbc3759a3ef62", "******************************************", "******************************************", "0xa64e04ed4b223a71e524dc7ebb7f28e422ccfdde", "******************************************", "0x2ee573caab73c1d8cf0ca6bd3589b67de79628a4", "******************************************", "0x01bb96883a8a478d4410387d4aaf11067edc2c74", "******************************************", "0xe7ed0c559d905436a867cddf07e06921d572363c", "******************************************", "0xa94f9e3433c92a5cd1925494811a67b1943557d9", "******************************************", "0xa63de7f89ba1270b85f3dcc193ff1a1390a7c7c7", "******************************************", "0xc8b0b37dffe3a711a076dc86dd617cc203f36121", "******************************************", "0x646df48947ff785fe609969ff634e7be9d1c34cd", "******************************************", "0x42582b404b0bec4a266631a0e178840b107a0c69", "******************************************", "0x36a94bc3edb1b629d1413091e22dc65fa050f17f", "******************************************", "0xb398d00b5a336f0ad33cfb352fd7646171cec442", "******************************************", "0xb4bc3b00de98e1c0498699379f6607b1f00bd5a1", "******************************************", "0xfe8b7baf68952bac2c04f386223d2013c1b4c601", "******************************************", "0x9c8764ec71f175c97c6c2fd558eb6546fcdbea32", "******************************************", "0x94d31188982c8eccf243e555b22dc57de1dba4e1", "******************************************", "0x407c5e2fadd7555be927c028bc358daa907c797a", "******************************************", "0x7e97da2dbbbdd7fb313cf9dc0581ac7cec999c70", "******************************************", "0x7f8d2662f64dd468c423805f98a6579ad59b28fa", "******************************************", "0x3398adf63fed17cbadd6080a1fb771e6a2a55958", "******************************************", "0xba8910a1d7ab62129729047d453091a1e6356170", "******************************************", "0xdc054bce222fe725da0f17abcef38253bd8bb745", "******************************************", "0xca21693467d0a5ea9e10a5a7c5044b9b3837e694", "******************************************", "0xe0b02de2139256dbae55cf350094b882fbe629ea", "******************************************", "0x02c38368a6f53858aab5a3a8d91d73eb59edf9b9", "******************************************", "******************************************", "******************************************", "******************************************", "******************************************", "0xfe8c4778843c3cb047ffe7c0c0154a724c05cab9", "******************************************", "0xe2e88862d9b7379e21c82fc4aec8d71bddbcdb4b", "******************************************", "0xbbaff9e73b30f9cea5c01481f12de75050947fd6", "******************************************", "******************************************", "******************************************", "******************************************", "******************************************", "******************************************", "******************************************", "******************************************", "******************************************", "******************************************"]}}, "outputType": "json", "methods": {"createOrder": [{"description": "swap limit order", "method": "createOrder", "url": "https://gateway.prod.vertexprotocol.com/v1/execute", "input": ["SOL/USDC:USDC", "limit", "buy", 3, 50], "output": "{\"place_order\":{\"id\":5930043274845996, \"product_id\":12,\"order\":{\"sender\":\"0xc950889d14a3717f541ec246bc253d7a9e98c78f64656661756c740000000000\",\"nonce\":\"1800218739978798056\",\"expiration\":\"1716822372631\",\"priceX18\":\"50000000000000000000\",\"amount\":\"3000000000000000000\"},\"signature\":\"0x7fb86566dbce3f52361a7ca37c59430af1b1536b5efa00e65753739c205af0f83ef5497345af99addc1ed671504bb613783e6ce3be6dd0ab82432625e41f12781c\"}}"}, {"description": "swap post only order", "method": "createOrder", "url": "https://gateway.prod.vertexprotocol.com/v1/execute", "input": ["SOL/USDC:USDC", "limit", "buy", 3, 50, {"postOnly": true}], "output": "{\"place_order\":{ \"id\":5930043274845996, \"product_id\":12,\"order\":{\"sender\":\"0xc950889d14a3717f541ec246bc253d7a9e98c78f64656661756c740000000000\",\"nonce\":\"1800219268351001576\",\"expiration\":\"13835059772105040238\",\"priceX18\":\"50000000000000000000\",\"amount\":\"3000000000000000000\"},\"signature\":\"0x3d6b60f42c34846dbe065d8cea0886944fd1def02d0621c81076759903e5cc1c68323db4ce14f89dae0ab5bb138bd2d2e645c12f1159c79828da4d9c74602e581b\"}}"}, {"description": "spot order", "method": "createOrder", "url": "https://gateway.prod.vertexprotocol.com/v1/execute", "input": ["USDT/USDC", "limit", "buy", 251, 1], "output": "{\"place_order\":{\"id\":5930043274845996, \"product_id\":31,\"order\":{\"sender\":\"0xc950889d14a3717f541ec246bc253d7a9e98c78f64656661756c740000000000\",\"priceX18\":\"1000000000000000000\",\"amount\":\"250000000000000000000\",\"expiration\":\"1717434643111\",\"nonce\":\"1800860752109634536\"},\"signature\":\"0xc7531db7448be295305cbaba9d7443ddb1e2edb20dc0866b3ad741d08f09b9793d52e8c047cd64ba1ae60c00f67c45ec6eb180330bbe15e27c3c92a782ab73371b\"}}"}, {"description": "spot IOC order", "method": "createOrder", "url": "https://gateway.prod.vertexprotocol.com/v1/execute", "input": ["USDT/USDC", "limit", "buy", 10, 1, {"timeInForce": "IOC"}], "output": "{\"place_order\":{\"product_id\":31,\"order\":{\"sender\":\"0xc950889d14a3717f541ec246bc253d7a9e98c78f64656661756c740000000000\",\"priceX18\":\"1000000000000000000\",\"amount\":\"10000000000000000000\",\"expiration\":\"4611687736024352375\",\"nonce\":\"1801030958392017896\"},\"signature\":\"0x21b6e776eeeef64616b62a8f2038723fe91dbc011460f49d90ddc13a6e6e741173876d20a3a41707d3ed62b4d951b818cd9325b8c2d9626233a730aac0719b341c\",\"id\":5930043274845996}}"}, {"description": "swap IOC order", "method": "createOrder", "url": "https://gateway.prod.vertexprotocol.com/v1/execute", "input": ["SOL/USDC:USDC", "limit", "buy", 0.5, 172, {"timeInForce": "IOC"}], "output": "{\"place_order\":{\"product_id\":12,\"order\":{\"sender\":\"0xc950889d14a3717f541ec246bc253d7a9e98c78f64656661756c740000000000\",\"priceX18\":\"172000000000000000000\",\"amount\":\"500000000000000000\",\"expiration\":\"4611687736024459077\",\"nonce\":\"1801031070277174248\"},\"signature\":\"0x605f0d743d6ee27f173469c047d60cce7ddc8c71200e2ecda730a2fc4fa4070f0e5d70c3e0772ce4f0d00f6c0797e49b173150d79aec454c6bf172e6d551c2c21b\",\"id\":5930043274845996}}"}, {"description": "swap trigger order", "method": "createOrder", "url": "https://gateway.prod.vertexprotocol.com/v1/execute", "input": ["SOL/USDC:USDC", "limit", "buy", 0.5, 172, {"triggerPrice": "175"}], "output": "{\"place_order\":{\"product_id\":12,\"order\":{\"sender\":\"0xc950889d14a3717f541ec246bc253d7a9e98c78f64656661756c740000000000\",\"priceX18\":\"172000000000000000000\",\"amount\":\"500000000000000000\",\"expiration\":\"4611687736024459077\",\"nonce\":\"1801031070277174248\"},\"trigger\":{\"last_price_below\":\"175000000000000000000\"},\"signature\":\"0x605f0d743d6ee27f173469c047d60cce7ddc8c71200e2ecda730a2fc4fa4070f0e5d70c3e0772ce4f0d00f6c0797e49b173150d79aec454c6bf172e6d551c2c21b\",\"id\":5930043274845996}}"}, {"description": "swap take profit order", "method": "createOrder", "url": "https://gateway.prod.vertexprotocol.com/v1/execute", "input": ["SOL/USDC:USDC", "limit", "buy", 0.5, 172, {"takeProfitPrice": "170"}], "output": "{\"place_order\":{\"product_id\":12,\"order\":{\"sender\":\"0xc950889d14a3717f541ec246bc253d7a9e98c78f64656661756c740000000000\",\"priceX18\":\"172000000000000000000\",\"amount\":\"500000000000000000\",\"expiration\":\"4611687736024459077\",\"nonce\":\"1801031070277174248\"},\"trigger\":{\"last_price_above\":\"170000000000000000000\"},\"signature\":\"0x605f0d743d6ee27f173469c047d60cce7ddc8c71200e2ecda730a2fc4fa4070f0e5d70c3e0772ce4f0d00f6c0797e49b173150d79aec454c6bf172e6d551c2c21b\",\"id\":5930043274845996}}"}], "editOrder": [{"description": "edit swap limit order", "method": "editOrder", "url": "https://gateway.prod.vertexprotocol.com/v1/execute", "input": ["0x75fa35487acb72ffc55548aaad3ac70c546d63ff247a58ff70053faeaca4211d", "SOL/USDC:USDC", "limit", "buy", 3, 50], "output": "{\"cancel_and_place\":{\"cancel_tx\":{\"sender\":\"******************************************64656661756c740000000000\",\"productIds\":[12],\"digests\":[\"0x75fa35487acb72ffc55548aaad3ac70c546d63ff247a58ff70053faeaca4211d\"],\"nonce\":\"1801192771625681896\"},\"cancel_signature\":\"0xf706fe302936d618dcc345c743dde95494e90464b7ec5f3904627c2b7efbf03240d20069ee0bedc1e4647e15263e2736296fba23297a967614e63693ffe586ab1b\",\"place_order\":{\"product_id\":12,\"order\":{\"sender\":\"******************************************64656661756c740000000000\",\"priceX18\":\"50000000000000000000\",\"amount\":\"3000000000000000000\",\"expiration\":\"1717751281596\",\"nonce\":\"1801192771625681896\"},\"signature\":\"0x21b1bd4c38912680d9d7f546e78ad98992b24a8b1b4fa890c6bd2a733d7c2cd55e7e8259b47f6ed37450324fff6d8aba1864567389523d810a4f26994d4b29281c\",\"id\":5930043274845996}}}"}], "cancelOrder": [{"description": "cancel swap order", "method": "cancelOrder", "url": "https://gateway.prod.vertexprotocol.com/v1/execute", "input": ["0x21973f66e6832c2d62fa290aea21818a0754dc8f0236e55b0058bc7014674416", "SOL/USDC:USDC"], "output": "{\"cancel_orders\":{\"tx\":{\"sender\":\"0xc950889d14a3717f541ec246bc253d7a9e98c78f64656661756c740000000000\",\"nonce\":\"1800218880221643752\",\"productIds\":[12],\"digests\":[\"0x21973f66e6832c2d62fa290aea21818a0754dc8f0236e55b0058bc7014674416\"]},\"signature\":\"0xa991fbcf9a56d987f46f8edcbc71d2705bf39d793a64855d5990feda2886675f66ca01f26ec3d3ee1bf988329ce8b5aaa7ce28989e515cc822ea182fd73e599b1c\"}}"}, {"description": "cancel swap stop order", "method": "cancelOrder", "url": "https://trigger.prod.vertexprotocol.com/v1/execute", "input": ["0x21973f66e6832c2d62fa290aea21818a0754dc8f0236e55b0058bc7014674416", "SOL/USDC:USDC", {"trigger": true}], "output": "{\"cancel_orders\":{\"tx\":{\"sender\":\"0xc950889d14a3717f541ec246bc253d7a9e98c78f64656661756c740000000000\",\"nonce\":\"1800218880221643752\",\"productIds\":[12],\"digests\":[\"0x21973f66e6832c2d62fa290aea21818a0754dc8f0236e55b0058bc7014674416\"]},\"signature\":\"0xa991fbcf9a56d987f46f8edcbc71d2705bf39d793a64855d5990feda2886675f66ca01f26ec3d3ee1bf988329ce8b5aaa7ce28989e515cc822ea182fd73e599b1c\"}}"}], "fetchOrder": [{"description": "fetch order", "method": "fetchOrder", "url": "https://gateway.prod.vertexprotocol.com/v1/query?type=order&product_id=12&digest=0x64dcbf8baae563de7538be03dc485e6ca24ba5a43fe3186a2327d6c06197a1f0", "input": ["0x64dcbf8baae563de7538be03dc485e6ca24ba5a43fe3186a2327d6c06197a1f0", "SOL/USDC:USDC"]}], "fetchOrders": [{"description": "fetch stop orders", "method": "fetchOrders", "url": "https://trigger.prod.vertexprotocol.com/v1/query", "input": ["SOL/USDC:USDC", 1, 10, {"trigger": true}], "output": "{\"type\":\"list_trigger_orders\",\"pending\":false,\"product_id\":12,\"signature\":\"0x2113930070346738340551582344e31580634bb7e5a398c035efd829448efda56755744af9952326771f45e3618b40ac4154cf722873feb9d686756c4090b18c1b\",\"tx\":{\"sender\":\"******************************************64656661756c740000000000\",\"recvTime\":\"1717658443168\"},\"limit\":10}"}], "cancelAllOrders": [{"description": "cancel all orders", "method": "cancelAllOrders", "url": "https://gateway.prod.vertexprotocol.com/v1/execute", "input": ["SOL/USDC:USDC"], "output": "{\"cancel_product_orders\":{\"tx\":{\"sender\":\"0xc950889d14a3717f541ec246bc253d7a9e98c78f64656661756c740000000000\",\"nonce\":\"1800219871512888296\",\"productIds\":[12]},\"signature\":\"0x5037f0d52e7cb9823fe87db92ff84bb4444d3e5d41e3b95676e8818232dddcb362c3be7bb308a36fe49e42022557572dcdc84a4804388556afce27a0d211a2581b\"}}"}, {"description": "cancel all stop orders", "method": "cancelAllOrders", "url": "https://trigger.prod.vertexprotocol.com/v1/execute", "input": ["SOL/USDC:USDC", {"trigger": true}], "output": "{\"cancel_product_orders\":{\"tx\":{\"sender\":\"0xc950889d14a3717f541ec246bc253d7a9e98c78f64656661756c740000000000\",\"nonce\":\"1800219871512888296\",\"productIds\":[12]},\"signature\":\"0x5037f0d52e7cb9823fe87db92ff84bb4444d3e5d41e3b95676e8818232dddcb362c3be7bb308a36fe49e42022557572dcdc84a4804388556afce27a0d211a2581b\"}}"}], "fetchMyTrades": [{"description": "swap trades", "method": "fetchMyTrades", "url": "https://archive.prod.vertexprotocol.com/v1", "input": ["SOL/USDC:USDC"], "output": "{\"matches\":{\"subaccount\":\"0xc950889d14a3717f541ec246bc253d7a9e98c78f64656661756c740000000000\",\"product_ids\":[12]}}"}, {"description": "spot trades", "method": "fetchMyTrades", "url": "https://archive.prod.vertexprotocol.com/v1", "input": ["USDT/USDC"], "output": "{\"matches\":{\"subaccount\":\"0xc950889d14a3717f541ec246bc253d7a9e98c78f64656661756c740000000000\",\"product_ids\":[31]}}"}], "fetchOpenOrders": [{"description": "swap open orders", "method": "fetchOpenOrders", "url": "https://gateway.prod.vertexprotocol.com/v1/query", "input": ["SOL/USDC:USDC"], "output": "{\"product_id\":12,\"type\":\"subaccount_orders\",\"sender\":\"0xc950889d14a3717f541ec246bc253d7a9e98c78f64656661756c740000000000\"}"}, {"description": "spot open orders", "method": "fetchOpenOrders", "url": "https://gateway.prod.vertexprotocol.com/v1/query", "input": ["USDT/USDC"], "output": "{\"product_id\":31,\"type\":\"subaccount_orders\",\"sender\":\"0xc950889d14a3717f541ec246bc253d7a9e98c78f64656661756c740000000000\"}"}], "fetchBalance": [{"description": "balance", "method": "fetchBalance", "url": "https://gateway.prod.vertexprotocol.com/v1/query?type=subaccount_info&subaccount=0xc950889d14a3717f541ec246bc253d7a9e98c78f64656661756c740000000000", "input": []}], "fetchPositions": [{"description": "positions", "method": "fetchPositions", "url": "https://gateway.prod.vertexprotocol.com/v1/query?type=subaccount_info&subaccount=0xc950889d14a3717f541ec246bc253d7a9e98c78f64656661756c740000000000", "input": []}], "fetchCurrencies": [{"description": "fetchCurrencies", "method": "fetchCurrencies", "url": "https://gateway.prod.vertexprotocol.com/v2/assets", "input": []}], "fetchMarkets": [{"description": "markets", "method": "fetchMarkets", "url": "https://gateway.prod.vertexprotocol.com/v1/query?type=symbols", "input": []}], "fetchTime": [{"description": "time", "method": "fetchTime", "url": "https://gateway.prod.vertexprotocol.com/v1/time", "input": []}], "fetchStatus": [{"description": "status", "method": "fetchStatus", "url": "https://gateway.prod.vertexprotocol.com/v1/query?type=status", "input": []}], "fetchTrades": [{"description": "trades", "method": "fetchTrades", "url": "https://gateway.prod.vertexprotocol.com/v2/trades?ticker_id=USDT_USDC&limit=1", "input": ["USDT/USDC", null, 1]}], "fetchOrderBook": [{"description": "orderbook", "method": "fetchOrderBook", "url": "https://gateway.prod.vertexprotocol.com/v2/orderbook?ticker_id=USDT_USDC&depth=1", "input": ["USDT/USDC", 1]}], "fetchTradingFees": [{"description": "tradingFees", "method": "fetchTradingFees", "url": "https://gateway.prod.vertexprotocol.com/v1/query?type=fee_rates&sender=******************************************64656661756c740000000000", "input": []}], "fetchOHLCV": [{"description": "OHLCV", "method": "fetchOHLCV", "url": "https://gateway.prod.vertexprotocol.com/v1", "input": ["USDT/USDC", "1m", null, 1], "output": "{\"candlesticks\":{\"product_id\":31,\"granularity\":60,\"limit\":1}}"}], "fetchFundingRate": [{"description": "fundingRate", "method": "fetchFundingRate", "url": "https://gateway.prod.vertexprotocol.com/v1", "input": ["USDT/USDC"], "output": "{\"funding_rate\":{\"product_id\":31}}"}], "fetchFundingRates": [{"description": "fundingRates", "method": "fetchFundingRates", "url": "https://gateway.prod.vertexprotocol.com/v2/contracts", "input": []}], "fetchOpenInterest": [{"description": "openInterest", "method": "fetchOpenInterest", "url": "https://gateway.prod.vertexprotocol.com/v2/contracts", "input": ["SOL/USDC:USDC"]}], "fetchOpenInterests": [{"description": "fetch the open interest for unified symbols", "method": "fetchOpenInterests", "url": "https://archive.prod.vertexprotocol.com/v2/contracts", "input": [["SOL/USDC:USDC"]]}], "fetchTickers": [{"description": "tickers", "method": "fetchTickers", "url": "https://gateway.prod.vertexprotocol.com/v2/tickers", "input": []}], "cancelOrders": [{"description": "cancelOrders", "method": "cancelOrders", "url": "https://gateway.prod.vertexprotocol.com/v1/execute", "input": [["0x21973f66e6832c2d62fa290aea21818a0754dc8f0236e55b0058bc7014674416"], "SOL/USDC:USDC"], "output": "{\"cancel_orders\":{\"tx\":{\"sender\":\"******************************************64656661756c740000000000\",\"productIds\":[12],\"digests\":[\"0x21973f66e6832c2d62fa290aea21818a0754dc8f0236e55b0058bc7014674416\"],\"nonce\":\"1801105366368388072\"},\"signature\":\"0xbf4b04276d29abedd5c3ac8a50a071e3ab65522d0954141ee0c6826f8079396274d8ab5220678cefee2ec530af920e6588c4913dd8d6dfaa870f789441c47e331c\"}}"}]}}