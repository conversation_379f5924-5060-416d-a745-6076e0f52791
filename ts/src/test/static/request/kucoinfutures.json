{"exchange": "kucoinfutures", "skipKeys": ["clientOid", "to", "bizNo"], "outputType": "json", "methods": {"createOrder": [{"description": "create isolated order by using the unified syntax", "method": "createOrder", "url": "https://api-futures.kucoin.com/api/v1/orders", "input": ["LTC/USDT:USDT", "market", "sell", 1, null, {"marginMode": "isolated"}], "output": "{\"clientOid\":\"1728bf29-96fd-487c-99b8-61cfe44f309e\",\"side\":\"sell\",\"symbol\":\"LTCUSDTM\",\"type\":\"market\",\"leverage\":1,\"marginMode\":\"ISOLATED\",\"size\":1}"}, {"description": "create cross order by using the unified syntax", "method": "createOrder", "url": "https://api-futures.kucoin.com/api/v1/orders", "input": ["LTC/USDT:USDT", "market", "sell", 1, null, {"marginMode": "cross"}], "output": "{\"clientOid\":\"ec64526a-cb92-4eca-9fda-1efb64d1fe09\",\"side\":\"sell\",\"symbol\":\"LTCUSDTM\",\"type\":\"market\",\"leverage\":1,\"marginMode\":\"CROSS\",\"size\":1}"}, {"description": "Swap market buy", "method": "createOrder", "url": "https://api-futures.kucoin.com/api/v1/orders", "input": ["LTC/USDT:USDT", "market", "buy", 1], "output": "{\"clientOid\":\"19f54d44-a498-4d4f-99f2-b3ce202f77db\",\"side\":\"buy\",\"symbol\":\"LTCUSDTM\",\"type\":\"market\",\"size\":1,\"leverage\":1}"}, {"description": "Swap market sell", "method": "createOrder", "url": "https://api-futures.kucoin.com/api/v1/orders", "input": ["LTC/USDT:USDT", "market", "sell", 1], "output": "{\"clientOid\":\"0749b299-7639-49e0-b100-5d55cc166c0a\",\"side\":\"sell\",\"symbol\":\"LTCUSDTM\",\"type\":\"market\",\"size\":1,\"leverage\":1}"}, {"description": "Swap limit buy", "method": "createOrder", "url": "https://api-futures.kucoin.com/api/v1/orders", "input": ["LTC/USDT:USDT", "limit", "buy", 1, 50], "output": "{\"clientOid\":\"f93da405-f3b7-410b-ae44-542f6821108f\",\"side\":\"buy\",\"symbol\":\"LTCUSDTM\",\"type\":\"limit\",\"size\":1,\"leverage\":1,\"price\":\"50\"}"}, {"description": "test order with triggerPrice", "method": "createOrder", "url": "https://api-futures.kucoin.com/api/v1/orders/test", "input": ["LTC/USDT:USDT", "market", "buy", 1, null, {"triggerPrice": 90, "test": true}], "output": "{\"clientOid\":\"4c74b57d-3c63-4bcf-966b-e40aabd2f656\",\"side\":\"buy\",\"symbol\":\"LTCUSDTM\",\"type\":\"market\",\"size\":1,\"leverage\":1,\"stop\":\"up\",\"stopPrice\":\"90\",\"stopPriceType\":\"MP\"}"}, {"description": "Stop loss test order", "method": "createOrder", "url": "https://api-futures.kucoin.com/api/v1/orders/test", "input": ["LTC/USDT:USDT", "market", "sell", 1, null, {"stopLossPrice": 40, "test": true}], "output": "{\"clientOid\":\"a2c6585b-45b7-42ef-b6b6-51302f83c6f5\",\"side\":\"sell\",\"symbol\":\"LTCUSDTM\",\"type\":\"market\",\"size\":1,\"leverage\":1,\"stop\":\"down\",\"stopPrice\":\"40\",\"reduceOnly\":true,\"stopPriceType\":\"MP\"}"}, {"description": "take profit test order", "method": "createOrder", "url": "https://api-futures.kucoin.com/api/v1/orders/test", "input": ["LTC/USDT:USDT", "market", "sell", 1, null, {"takeProfitPrice": 100, "test": true}], "output": "{\"clientOid\":\"ea0dd8f4-476a-485d-8af7-5d615c4859ab\",\"side\":\"sell\",\"symbol\":\"LTCUSDTM\",\"type\":\"market\",\"size\":1,\"leverage\":1,\"stop\":\"up\",\"stopPrice\":\"100\",\"reduceOnly\":true,\"stopPriceType\":\"MP\"}"}, {"description": "position with tp+sl", "method": "createOrder", "url": "https://api-futures.kucoin.com/api/v1/st-orders", "input": ["ADA/USDT:USDT", "market", "buy", 4, null, {"stopLoss": {"triggerPrice": 0.2}, "takeProfit": {"triggerPrice": 0.5}}], "output": "{\"clientOid\":\"9424d62f-6389-4005-ac84-d5b3309dbda0\",\"side\":\"buy\",\"symbol\":\"ADAUSDTM\",\"type\":\"market\",\"size\":4,\"leverage\":1,\"triggerStopDownPrice\":\"0.2\",\"triggerStopUpPrice\":\"0.5\",\"stopPriceType\":\"MP\"}"}, {"description": "position with sl", "method": "createOrder", "url": "https://api-futures.kucoin.com/api/v1/st-orders", "input": ["ADA/USDT:USDT", "market", "buy", 4, null, {"stopLoss": {"triggerPrice": 0.2}}], "output": "{\"clientOid\":\"be4eeb70-2371-46de-9afa-88fec3070f1f\",\"side\":\"buy\",\"symbol\":\"ADAUSDTM\",\"type\":\"market\",\"size\":4,\"leverage\":1,\"triggerStopDownPrice\":\"0.2\",\"stopPriceType\":\"MP\"}"}, {"description": "linear swap create order with a cost param", "method": "createOrder", "url": "https://api-futures.kucoin.com/api/v1/orders", "input": ["BTC/USDT:USDT", "limit", "buy", 25, 25000, {"cost": "25"}], "output": "{\"clientOid\":\"dab82652-e3a0-44b7-a118-b37a54233141\",\"side\":\"buy\",\"symbol\":\"XBTUSDTM\",\"type\":\"limit\",\"leverage\":1,\"valueQty\":\"25\",\"price\":\"25000\"}"}], "createOrders": [{"description": "Swap market buy orders", "method": "createOrders", "url": "https://api-futures.kucoin.com/api/v1/orders/multi", "input": [[{"symbol": "LTC/USDT:USDT", "amount": 1, "side": "buy", "type": "limit", "price": 60}, {"symbol": "LTC/USDT:USDT", "amount": 1, "side": "buy", "type": "limit", "price": 61}]], "output": "[{\"clientOid\":\"77f3887d-e4e7-4c8f-9502-1c35debab7d3\",\"side\":\"buy\",\"symbol\":\"LTCUSDTM\",\"type\":\"limit\",\"size\":1,\"leverage\":1,\"price\":\"60\"},{\"clientOid\":\"8e9c0d99-96bb-460a-aa61-38c0409657bc\",\"side\":\"buy\",\"symbol\":\"LTCUSDTM\",\"type\":\"limit\",\"size\":1,\"leverage\":1,\"price\":\"61\"}]"}], "fetchMyTrades": [{"description": "Swap private trades", "method": "fetchMyTrades", "url": "https://api-futures.kucoin.com/api/v1/fills?symbol=LTCUSDTM&startAt=1699457638000&pageSize=5", "input": ["LTC/USDT:USDT", 1699457638000, 5]}], "fetchOpenOrders": [{"description": "Swap open orders", "method": "fetchOpenOrders", "url": "https://api-futures.kucoin.com/api/v1/orders?status=active&symbol=LTCUSDTM", "input": ["LTC/USDT:USDT"]}], "fetchClosedOrders": [{"description": "Swap closed orders", "method": "fetchClosedOrders", "url": "https://api-futures.kucoin.com/api/v1/orders?status=done&symbol=LTCUSDTM", "input": ["LTC/USDT:USDT"]}], "cancelOrders": [{"description": "cancelOrders with id", "method": "cancelOrders", "url": "https://api-futures.kucoin.com/api/v1/orders/multi-cancel", "input": [["235624148146139136", "235624167473491968"]], "output": "{\"orderIdsList\":[\"235624148146139136\",\"235624167473491968\"]}"}, {"description": "cancel orders with clientOrderId", "method": "cancelOrders", "url": "https://api-futures.kucoin.com/api/v1/orders/multi-cancel", "input": [[], "ADA/USDT:USDT", {"clientOrderIds": ["<PERSON><PERSON><PERSON><PERSON>"]}], "output": "{\"clientOidsList\":[{\"symbol\":\"ADAUSDTM\",\"clientOid\":\"adaorder\"}]}"}], "cancelAllOrders": [{"description": "Cancel swap orders", "method": "cancelAllOrders", "url": "https://api-futures.kucoin.com/api/v1/orders?symbol=LTCUSDTM", "input": ["LTC/USDT:USDT"]}, {"description": "Cancel swap stop orders", "method": "cancelAllOrders", "url": "https://api-futures.kucoin.com/api/v1/stopOrders?symbol=LTCUSDTM", "input": ["LTC/USDT:USDT", {"stop": true}]}], "cancelOrder": [{"description": "cancel order", "method": "cancelOrder", "url": "https://api-futures.kucoin.com/api/v1/orders/126611245292855296", "input": ["126611245292855296"]}, {"description": "Cancel order with clientOrderId", "method": "cancelOrder", "url": "https://api-futures.kucoin.com/api/v1/orders/client-order/order2?symbol=LTCUSDTM", "input": ["", "LTC/USDT:USDT", {"clientOrderId": "order2"}]}], "fetchBalance": [{"description": "Fetch spot Balance", "method": "fetchBalance", "url": "https://api-futures.kucoin.com/api/v1/account-overview?currency=USDT&type=spot", "input": [{"type": "spot"}]}, {"description": "Fetch swap Balance", "method": "fetchBalance", "url": "https://api-futures.kucoin.com/api/v1/account-overview?currency=USDT&type=swap", "input": [{"type": "swap"}]}], "fetchPositions": [{"description": "Fetch linear position", "method": "fetchPositions", "url": "https://api-futures.kucoin.com/api/v1/positions", "input": [["LTC/USDT:USDT"]]}], "fetchDeposits": [{"description": "Fetch deposits", "method": "fetchDeposits", "url": "https://api-futures.kucoin.com/api/v1/deposit-list", "input": []}], "fetchWithdrawals": [{"description": "Fetch withdrawals", "method": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://api-futures.kucoin.com/api/v1/withdrawal-list", "input": []}], "fetchLedger": [{"description": "fetch USDT ledger", "method": "fetchLedger", "options": {"hfMigrated": false}, "url": "https://openapi-v2.kucoin.com/api/v1/accounts/ledgers?currency=USDT", "input": ["USDT"]}], "fetchDepositAddress": [{"description": "fetch USDT deposit address", "method": "fetchDepositAddress", "url": "https://api-futures.kucoin.com/api/v1/deposit-address?currency=USDT", "input": ["USDT"]}], "fetchOrdersByStatus": [{"description": "Fetch open swap orders", "method": "fetchOrdersByStatus", "url": "https://api-futures.kucoin.com/api/v1/orders?status=active&symbol=LTCUSDTM", "input": ["open", "LTC/USDT:USDT"]}, {"description": "Fetch open swap stop orders", "method": "fetchOrdersByStatus", "url": "https://api-futures.kucoin.com/api/v1/stopOrders?symbol=LTCUSDTM&startAt=1", "input": ["open", "LTC/USDT:USDT", 1, 100, {"stop": true}]}], "fetchOrder": [{"description": "Fetch order by order id", "method": "fetchOrder", "url": "https://api-futures.kucoin.com/api/v1/orders/**********", "input": ["**********", "LTC/USDT:USDT"]}, {"description": "Fetch order by client order id", "method": "fetchOrder", "url": "https://api-futures.kucoin.com/api/v1/orders/?clientOid=2234567890", "input": ["", "LTC/USDT:USDT", {"clientOid": "2234567890"}]}], "closePosition": [{"description": "Closes position for the ADA/USDT:USDT market", "method": "closePosition", "url": "https://api-futures.kucoin.com/api/v1/orders", "input": ["ADA/USDT:USDT"], "output": "{\"symbol\":\"ADAUSDTM\",\"closeOrder\":true,\"clientOid\":\"1702639752693\",\"type\":\"market\"}"}], "fetchFundingRateHistory": [{"description": "fetch funding rate history", "method": "fetchFundingRateHistory", "url": "https://api-futures.kucoin.com/api/v1/contract/funding-rates?symbol=XBTUSDTM&from=1702209600000&to=1704283200000", "input": ["BTC/USDT:USDT", 1702209600000, null, {"until": 1704283200000}]}, {"description": "fundingRateHistory", "method": "fetchFundingRateHistory", "url": "https://api-futures.kucoin.com/api/v1/contract/funding-rates?symbol=XBTUSDTM&from=0&to=1709992986415", "input": ["BTC/USDT:USDT"]}], "fetchFundingRate": [{"description": "linear funding rate", "method": "fetchFundingRate", "url": "https://api-futures.kucoin.com/api/v1/funding-rate/XBTUSDTM/current", "input": ["BTC/USDT:USDT"]}, {"description": "fundingRate", "method": "fetchFundingRate", "url": "https://api-futures.kucoin.com/api/v1/funding-rate/XBTUSDTM/current", "input": ["BTC/USDT:USDT"]}], "fetchTime": [{"description": "fetchTime", "method": "fetchTime", "url": "https://api-futures.kucoin.com/api/v1/timestamp", "input": []}], "fetchTrades": [{"description": "swap fetchTrades", "method": "fetchTrades", "url": "https://api-futures.kucoin.com/api/v1/trade/history?symbol=XBTUSDTM", "input": ["BTC/USDT:USDT"]}], "fetchOrderBook": [{"description": "swap orderbook", "method": "fetchOrderBook", "url": "https://api-futures.kucoin.com/api/v1/level2/depth20?symbol=XBTUSDTM", "input": ["BTC/USDT:USDT"]}], "fetchTicker": [{"description": "Swap ticker", "method": "fetchTicker", "url": "https://api-futures.kucoin.com/api/v1/ticker?symbol=XBTUSDTM", "input": ["BTC/USDT:USDT"]}], "fetchTickers": [{"description": "without args", "method": "fetchTickers", "url": "https://api-futures.kucoin.com/api/v1/contracts/active", "input": []}, {"description": "+symbols", "method": "fetchTickers", "url": "https://api-futures.kucoin.com/api/v1/contracts/active", "input": [["BTC/USDT:USDT"]]}], "fetchOHLCV": [{"description": "swap ohlcv", "method": "fetchOHLCV", "url": "https://api-futures.kucoin.com/api/v1/kline/query?symbol=XBTUSDTM&granularity=1&to=1709992986055", "input": ["BTC/USDT:USDT"]}], "fetchTradingFee": [{"description": "fetchTradingFee", "method": "fetchTradingFee", "url": "https://openapi-v2.kucoin.com/api/v1/trade-fees?symbols=XBTUSDTM", "input": ["BTC/USDT:USDT"]}], "fetchPositionsHistory": [{"description": "fetchPositionsHistory", "method": "fetchPositionsHistory", "url": "https://api-futures.kucoin.com/api/v1/history-positions?limit=200", "input": []}], "fetchMarginMode": [{"description": "linear swap", "method": "fetchMarginMode", "url": "https://api-futures.kucoin.com/api/v2/position/getMarginMode?symbol=XBTUSDTM", "input": ["BTC/USDT:USDT"]}], "setMarginMode": [{"description": "linear swap - isolated", "method": "setMarginMode", "url": "https://api-futures.kucoin.com/api/v2/position/changeMarginMode", "input": ["isolated", "BTC/USDT:USDT"], "output": "{\"symbol\":\"XBTUSDTM\",\"marginMode\":\"ISOLATED\"}"}, {"description": "linear swap - cross", "method": "setMarginMode", "url": "https://api-futures.kucoin.com/api/v2/position/changeMarginMode", "input": ["cross", "BTC/USDT:USDT"], "output": "{\"symbol\":\"XBTUSDTM\",\"marginMode\":\"CROSS\"}"}], "fetchLeverage": [{"description": "linear swap - cross", "method": "fetchLeverage", "url": "https://api-futures.kucoin.com/api/v2/getCrossUserLeverage?symbol=XBTUSDTM", "input": ["BTC/USDT:USDT", {"marginMode": "cross"}]}], "setLeverage": [{"description": "linear swap - cross", "method": "setLeverage", "url": "https://api-futures.kucoin.com/api/v2/changeCrossUserLeverage", "input": [4, "BTC/USDT:USDT", {"marginMode": "cross"}], "output": "{\"symbol\":\"XBTUSDTM\",\"leverage\":\"4\"}"}], "fetchFundingInterval": [{"description": "linear swap fetch the funding interval", "method": "fetchFundingInterval", "url": "https://api-futures.kucoin.com/api/v1/funding-rate/XBTUSDTM/current", "input": ["BTC/USDT:USDT"]}], "fetchStatus": [{"description": "fetch the exchange status", "method": "fetchStatus", "url": "https://api-futures.kucoin.com/api/v1/status", "input": []}], "fetchMarkPrice": [{"description": "fetch the mark price", "method": "fetchMarkPrice", "url": "https://api-futures.kucoin.com/api/v1/mark-price/XBTUSDTM/current", "input": ["BTC/USDT:USDT"]}], "fetchBidsAsks": [{"description": "fetch bids asks for multiple symbols at once", "method": "fetchBidsAsks", "url": "https://api-futures.kucoin.com/api/v1/allTickers", "input": [["BTC/USDT:USDT", "ETH/USDT:USDT"]]}], "fetchFundingHistory": [{"description": "fetch funding history for a linear swap symbol with a limit argument", "method": "fetchFundingHistory", "url": "https://api-futures.kucoin.com/api/v1/funding-history?symbol=XBTUSDTM&maxCount=3", "input": ["BTC/USDT:USDT", null, 3]}, {"description": "fetch funding history for an inverse swap symbol with a limit argument", "method": "fetchFundingHistory", "url": "https://api-futures.kucoin.com/api/v1/funding-history?symbol=XBTUSDM&maxCount=3", "input": ["BTC/USD:BTC", null, 3]}], "fetchPosition": [{"description": "fetch inverse swap position", "method": "fetchPosition", "url": "https://api-futures.kucoin.com/api/v1/position?symbol=XBTUSDM", "input": ["BTC/USD:BTC"]}, {"description": "fetch linear swap position", "method": "fetchPosition", "url": "https://api-futures.kucoin.com/api/v1/position?symbol=XBTUSDTM", "input": ["BTC/USDT:USDT"]}], "fetchMarketLeverageTiers": [{"description": "fetch linear swap market leverage tiers", "method": "fetchMarketLeverageTiers", "url": "https://api-futures.kucoin.com/api/v1/contracts/risk-limit/XBTUSDTM", "input": ["BTC/USDT:USDT"]}, {"description": "fetch inverse swap market leverage tiers", "method": "fetchMarketLeverageTiers", "url": "https://api-futures.kucoin.com/api/v1/contracts/risk-limit/XBTUSDM", "input": ["BTC/USD:BTC"]}], "addMargin": [{"description": "add margin to a linear swap position", "method": "add<PERSON><PERSON>gin", "url": "https://api-futures.kucoin.com/api/v1/position/margin/deposit-margin", "input": ["ETH/USDT:USDT", 1], "output": "{\"symbol\":\"ETHUSDTM\",\"margin\":\"1\",\"bizNo\":\"0091c851-35bd-4fbd-8abd-40a7dad05645\"}"}], "transfer": [{"description": "transfer from spot to future", "method": "transfer", "url": "https://api-futures.kucoin.com/api/v1/transfer-in", "input": ["USDT", 5, "spot", "future"], "output": "{\"currency\":\"USDT\",\"amount\":\"5\",\"payAccountType\":\"TRADE\"}"}, {"description": "transfer from future to spot account", "method": "transfer", "url": "https://api-futures.kucoin.com/api/v2/transfer-out", "input": ["USDT", 5, "future", "spot"], "output": "{\"currency\":\"USDT\",\"amount\":\"5\",\"recAccountType\":\"TRADE\"}"}, {"description": "transfer from future to funding account", "method": "transfer", "url": "https://api-futures.kucoin.com/api/v2/transfer-out", "input": ["USDT", 5, "future", "funding"], "output": "{\"currency\":\"USDT\",\"amount\":\"5\",\"recAccountType\":\"MAIN\"}"}, {"description": "transfer from funding to future account", "method": "transfer", "url": "https://api-futures.kucoin.com/api/v1/transfer-in", "input": ["USDT", 5, "funding", "future"], "output": "{\"currency\":\"USDT\",\"amount\":\"5\",\"payAccountType\":\"MAIN\"}"}, {"description": "transfer from spot to swap account", "method": "transfer", "url": "https://api-futures.kucoin.com/api/v1/transfer-in", "input": ["USDT", 5, "spot", "swap"], "output": "{\"currency\":\"USDT\",\"amount\":\"5\",\"payAccountType\":\"TRADE\"}"}]}}