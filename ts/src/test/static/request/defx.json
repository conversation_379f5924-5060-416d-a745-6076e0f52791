{"exchange": "defx", "skipKeys": [], "outputType": "u<PERSON><PERSON><PERSON>", "methods": {"setLeverage": [{"description": "set leverage", "method": "setLeverage", "url": "https://api.defx.com/v1/auth/api/users/metadata/leverage", "input": [11, "DOGE/USDC:USDC"], "output": "{\"leverage\":\"11\",\"symbol\":\"DOGE_USDC\"}"}], "fetchStatus": [{"description": "fetchStatus", "method": "fetchStatus", "url": "https://api.defx.com/v1/open/healthcheck/ping", "input": []}], "fetchTime": [{"description": "fetchTime", "method": "fetchTime", "url": "https://api.defx.com/v1/open/healthcheck/ping", "input": []}], "fetchMarkets": [{"description": "fetchMarkets", "method": "fetchMarkets", "disabled": true, "url": "https://api.defx.com/v1/open/c/markets/metadata?type=perps", "input": []}], "fetchTicker": [{"description": "fetchTicker", "method": "fetchTicker", "url": "https://api.defx.com/v1/open/symbols/SOL_USDC/ticker/24hr", "input": ["SOL/USDC:USDC"]}], "fetchTickers": [{"description": "fetchTickers", "method": "fetchTickers", "url": "https://api.defx.com/v1/open/ticker/24HrAgg", "input": [["SOL/USDC:USDC"]]}], "fetchOHLCV": [{"description": "fetchOHLCV - since", "method": "fetchOHLCV", "url": "https://api.defx.com/v1/open/symbols/SOL_USDC/ohlc?endTime=1730266085312&interval=1m&limit=3&startTime=1730265905312", "input": ["SOL/USDC:USDC", "1m", 1730265905312, 3]}, {"description": "fetchOHLCV - until", "method": "fetchOHLCV", "url": "https://api.defx.com/v1/open/symbols/SOL_USDC/ohlc?endTime=1730265905312&interval=1m&limit=3&startTime=0", "input": ["SOL/USDC:USDC", "1m", null, 3, {"until": 1730265905312}]}], "fetchTrades": [{"description": "fetchTrades", "method": "fetchTrades", "url": "https://api.defx.com/v1/open/symbols/SOL_USDC/trades?limit=3", "input": ["SOL/USDC:USDC", 1731947390413, 3]}], "fetchMyTrades": [{"description": "fetchMyTrades", "method": "fetchMyTrades", "url": "https://api.defx.com/v1/auth/api/trades?symbols=SOL_USDC&pageSize=3", "input": ["SOL/USDC:USDC", 1731947390413, 3]}], "fetchOrderBook": [{"description": "fetchOrderBook", "method": "fetchOrderBook", "url": "https://api.defx.com/v1/open/symbols/SOL_USDC/depth/10/0.001", "input": ["SOL/USDC:USDC", 10]}], "fetchMarkPrice": [{"description": "fetchMarkPrice", "method": "fetchMarkPrice", "url": "https://api.defx.com/v1/open/symbols/SOL_USDC/prices", "input": ["SOL/USDC:USDC"]}], "fetchFundingRate": [{"description": "fetchFundingRate", "method": "fetchFundingRate", "url": "https://api.defx.com/v1/open/symbols/SOL_USDC/prices", "input": ["SOL/USDC:USDC"]}], "fetchBalance": [{"description": "fetchBalance", "method": "fetchBalance", "url": "https://api.defx.com/v1/auth/api/wallet/balance", "input": []}], "createOrder": [{"description": "market buy order", "method": "createOrder", "url": "https://api.defx.com/v1/auth/api/order", "input": ["DOGE/USDC:USDC", "market", "buy", 250, null], "output": "{\"quantity\":\"250\",\"side\":\"BUY\",\"symbol\":\"DOGE_USDC\",\"type\":\"MARKET\"}"}, {"description": "market sell order", "method": "createOrder", "url": "https://api.defx.com/v1/auth/api/order", "input": ["DOGE/USDC:USDC", "market", "sell", 250, null], "output": "{\"quantity\":\"250\",\"side\":\"SELL\",\"symbol\":\"DOGE_USDC\",\"type\":\"MARKET\"}"}, {"description": "createOrder", "method": "createOrder", "url": "https://api.defx.com/v1/auth/api/order", "input": ["SOL/USDC:USDC", "limit", "sell", 0.5, 200, {"timeInForce": "GTC"}], "output": "{\"price\":\"200\",\"quantity\":\"0.5\",\"side\":\"SELL\",\"symbol\":\"SOL_USDC\",\"timeInForce\":\"GTC\",\"type\":\"LIMIT\"}"}], "cancelOrder": [{"description": "cancelOrder", "method": "cancelOrder", "url": "https://api.defx.com/v1/auth/api/order/752101209672255152?idType=orderId", "input": ["752101209672255152", "SOL/USDC:USDC"]}], "cancelAllOrders": [{"description": "cancelAllOrders", "method": "cancelAllOrders", "url": "https://api.defx.com/v1/auth/api/orders/allOpen", "input": ["SOL/USDC:USDC"], "output": "{\"symbols\":[\"SOL_USDC\"]}"}], "fetchPosition": [{"description": "fetchPosition", "method": "fetchPosition", "url": "https://api.defx.com/v1/auth/api/position/active?symbol=SOL_USDC", "input": ["SOL/USDC:USDC"]}], "fetchPositions": [{"description": "fetchPositions", "method": "fetchPositions", "url": "https://api.defx.com/v1/auth/api/position/active", "input": [["SOL/USDC:USDC"]]}], "fetchOrder": [{"description": "fetchPosition", "method": "fetchPosition", "url": "https://api.defx.com/v1/auth/api/order/752101209672255152?idType=orderId", "input": ["752101209672255152", "SOL/USDC:USDC"]}, {"description": "fetchOrder", "method": "fetchOrder", "url": "https://api.defx.com/v1/auth/api/order/0192e678-02d9-7657-96fb-fbfaa03e604d?idType=clientOrderId&symbol=SOL_USDC", "input": ["", "SOL/USDC:USDC", {"clientOrderId": "0192e678-02d9-7657-96fb-fbfaa03e604d"}]}], "fetchOrders": [{"description": "fetchOrders", "method": "fetchOrders", "url": "https://api.defx.com/v1/auth/api/orders?end=2024-11-04T06:59:50.000Z&pageSize=3&start=2024-10-23T17:13:10.000Z&symbols=SOL_USDC", "input": ["SOL/USDC:USDC", 1729703590000, 3, {"until": 1730703590000}]}], "fetchOpenOrders": [{"description": "fetchOpenOrders", "method": "fetchOpenOrders", "url": "https://api.defx.com/v1/auth/api/orders?end=2024-11-04T06:59:50.000Z&pageSize=1&start=2024-10-23T17:13:10.000Z&statuses=OPEN&symbols=SOL_USDC", "input": ["SOL/USDC:USDC", 1729703590000, 1, {"until": 1730703590000}]}], "fetchClosedOrders": [{"description": "fetchClosedOrders", "method": "fetchClosedOrders", "url": "https://api.defx.com/v1/auth/api/orders?end=2024-11-04T06:59:50.000Z&pageSize=1&start=2024-10-23T17:13:10.000Z&statuses=FILLED&symbols=SOL_USDC", "input": ["SOL/USDC:USDC", 1729703590000, 1, {"until": 1730703590000}]}], "fetchCanceledOrders": [{"description": "fetchCanceledOrders", "method": "fetchCanceledOrders", "url": "https://api.defx.com/v1/auth/api/orders?end=2024-11-04T06:59:50.000Z&pageSize=1&start=2024-10-23T17:13:10.000Z&statuses=CANCELED&symbols=SOL_USDC", "input": ["SOL/USDC:USDC", 1729703590000, 1, {"until": 1730703590000}]}], "closePosition": [{"description": "closePosition", "method": "closePosition", "url": "https://api.defx.com/v1/auth/api/position/0192fa60-9728-7849-987e-b1c8e0bfa0f3", "input": ["", null, {"positionId": "0192fa60-9728-7849-987e-b1c8e0bfa0f3", "type": "MARKET", "quantity": "0.5"}], "output": "{\"quantity\":\"0.5\",\"type\":\"MARKET\"}"}, {"description": "closePosition", "method": "closePosition", "url": "https://api.defx.com/v1/auth/api/position/0192fa60-9728-7849-987e-b1c8e0bfa0f3", "input": ["", null, {"positionId": "0192fa60-9728-7849-987e-b1c8e0bfa0f3", "type": "LIMIT", "quantity": "0.5", "price": "200"}], "output": "{\"quantity\":\"0.5\",\"type\":\"LIMIT\",\"price\":\"200\"}"}], "closeAllPositions": [{"description": "closeAllPositions", "method": "closeAllPositions", "url": "https://api.defx.com/v1/auth/api/position/all", "input": [], "output": "{}"}], "fetchLedger": [{"description": "fetchLedger", "method": "fetchLedger", "url": "https://api.defx.com/v1/auth/api/wallet/transactions?end=1730703590000&start=1730265905312", "input": [null, 1730265905312, null, {"until": 1730703590000}]}], "withdraw": [{"description": "withdraw", "method": "withdraw", "url": "https://api.defx.com/v1/auth/api/transfers/bridge/withdrawal", "input": ["USDC", 5, "", null, {"network": "ARB_SEPOLIA", "chainId": "421614"}], "output": "{\"amount\":\"5\",\"asset\":\"USDC\",\"chainId\":\"421614\",\"network\":\"ARB_SEPOLIA\"}"}]}}