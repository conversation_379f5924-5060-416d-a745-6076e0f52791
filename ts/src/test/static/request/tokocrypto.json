{"exchange": "tokocrypto", "skipKeys": ["timestamp", "signature"], "outputType": "u<PERSON><PERSON><PERSON>", "methods": {"fetchOrders": [{"description": "Spot orders", "method": "fetchOrders", "url": "https://www.tokocrypto.com/open/v1/orders?timestamp=1699458294362&symbol=LTC_USDT&recvWindow=5000&signature=79379270223cd6255439afe8acda70881e76c4bf0279c71f12d421678798916a", "input": ["LTC/USDT"]}], "fetchMyTrades": [{"description": "Spot private trades", "method": "fetchMyTrades", "url": "https://www.tokocrypto.com/open/v1/orders/trades?timestamp=1699458294959&symbol=LTC_USDT&startTime=1699457638000&limit=5&recvWindow=5000&signature=acc1e981cc7b9c49f6399ae6b6252d2278918b2a617ed581b39f1138c08f5f82", "input": ["LTC/USDT", 1699457638000, 5]}], "fetchOpenOrders": [{"description": "Spot open orders", "method": "fetchOpenOrders", "url": "https://www.tokocrypto.com/open/v1/orders?timestamp=1699458295197&symbol=LTC_USDT&type=1&recvWindow=5000&signature=a558e15afbb20d7ccd1056ac55c3be07cbd9ba994e487ebd5082357ae6c959f3", "input": ["LTC/USDT"]}], "fetchClosedOrders": [{"description": "Spot closed orders", "method": "fetchClosedOrders", "url": "https://www.tokocrypto.com/open/v1/orders?timestamp=*************&symbol=LTC_USDT&type=2&recvWindow=5000&signature=a5dd3854080fc02915c05c1f516235b34c95f83ef2848089f6599fe1116b38ac", "input": ["LTC/USDT"]}], "fetchBalance": [{"description": "Fetch spot Balance", "method": "fetchBalance", "url": "https://www.tokocrypto.com/open/v1/account/spot?timestamp=*************&type=spot&recvWindow=5000&signature=aec168e97e31f358327b40394e5cb7335ec783b41f65ea02b9732bccb0abf538", "input": [{"type": "spot"}]}, {"description": "Fetch swap Balance", "method": "fetchBalance", "url": "https://www.tokocrypto.com/open/v1/account/spot?timestamp=*************&type=swap&recvWindow=5000&signature=aa7107b045631a6b179c9ee67973adc4545a98fb675c40af21386ee64b29f1b0", "input": [{"type": "swap"}]}], "fetchDeposits": [{"description": "Fetch deposits", "method": "fetchDeposits", "url": "https://www.tokocrypto.com/open/v1/deposits?timestamp=*************&recvWindow=5000&signature=7bccd2b5821a893c12de13e62c7303ae9b1dc3ffbe0edff5068ddececaf15c78", "input": []}], "fetchWithdrawals": [{"description": "Fetch withdrawals", "method": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://www.tokocrypto.com/open/v1/withdraws?timestamp=*************&recvWindow=5000&signature=dc94e5e98d90a0ddc9d502d74a900397554fe1b3e745b674ba5c38685216d0af", "input": []}], "createOrder": [{"description": "Spot limit buy", "method": "createOrder", "url": "https://www.tokocrypto.com/open/v1/orders", "input": ["LTC/USDT", "limit", "buy", 0.1, 50], "output": "timestamp=*************&symbol=LTC_USDT&type=1&side=0&quantity=0.1&price=50&recvWindow=5000&signature=b76f6c95fd5741bd01ec7f9f40098e613af736014b4ebc79f6be3519993312ee"}, {"description": "Spot limit sell", "method": "createOrder", "url": "https://www.tokocrypto.com/open/v1/orders", "input": ["BTC/USDT", "limit", "sell", 0.0003, 55000], "output": "timestamp=1702519397155&symbol=BTC_USDT&type=1&side=1&quantity=0.0003&price=55000&recvWindow=5000&signature=d9641e4a38fee87c316452c7aba5a0fbcfcbbc9a69f0a865cba34caabe158526"}, {"description": "Spot market buy order with createMarketBuyOrderRequiresPrice set to true", "method": "createOrder", "url": "https://www.tokocrypto.com/open/v1/orders", "input": ["BTC/USDT", "market", "buy", 0.0002, 42800, {"createMarketBuyOrderRequiresPrice": true}], "output": "timestamp=1702519660875&symbol=BTC_USDT&type=2&side=0&quoteOrderQty=8.56&recvWindow=5000&signature=18eb61bc2a4b1c4b3a19837c758d88648061d44a3bf3630733ca1a0e3fc8eabd"}, {"description": "Spot market buy order with createMarketBuyOrderRequiresPrice set to false", "method": "createOrder", "url": "https://www.tokocrypto.com/open/v1/orders", "input": ["BTC/USDT", "market", "buy", 5, null, {"createMarketBuyOrderRequiresPrice": false}], "output": "timestamp=1702518844125&symbol=BTC_USDT&type=2&side=0&quoteOrderQty=5&recvWindow=5000&signature=2b0b9058ee03db0bcf656365318c2aa3f264e6edc23d5393f1f9f3b2144817dd"}, {"description": "Spot market buy order using the cost param", "method": "createOrder", "url": "https://www.tokocrypto.com/open/v1/orders", "input": ["BTC/USDT", "market", "buy", 0, null, {"cost": 5}], "output": "timestamp=1702518965030&symbol=BTC_USDT&type=2&side=0&quoteOrderQty=5&recvWindow=5000&signature=01f34ffa1d9975825a39f57d9f4aaf227667d73aecb1ef85a02ff45003ddff8e"}, {"description": "Spot market sell order", "method": "createOrder", "url": "https://www.tokocrypto.com/open/v1/orders", "input": ["ADA/USDT", "market", "sell", 85, null], "output": "timestamp=1702518741220&symbol=ADA_USDT&type=2&side=1&quantity=85&recvWindow=5000&signature=251d9fd61eb2158d8fc43b5b51ec7e527aa3800642d7a1414c04078361a2d407"}], "createMarketBuyOrderWithCost": [{"description": "Spot market buy order with cost", "method": "createMarketBuyOrderWithCost", "url": "https://www.tokocrypto.com/open/v1/orders", "input": ["BTC/USDT", 5], "output": "timestamp=1702519194821&symbol=BTC_USDT&type=2&side=0&quoteOrderQty=5&recvWindow=5000&signature=5fc8e640a2b7bc80ec767f4471a7767c4ada4562f238db13f6e75a20c652379d"}], "cancelOrder": [{"description": "Spot cancel order", "method": "cancelOrder", "url": "https://www.tokocrypto.com/open/v1/orders/cancel", "input": ["215077394"], "output": "timestamp=1702519492365&orderId=215077394&recvWindow=5000&signature=7a8dc92435e00bbc34efd37edd22ff4db50fadc8587c177749cd6c0699553b73"}], "fetchTickers": [{"description": "Fetch tickers", "method": "fetchTickers", "url": "https://api.binance.com/api/v3/ticker/24hr", "input": []}], "fetchTrades": [{"description": "Fetch trades", "method": "fetchTrades", "url": "https://api.binance.com/api/v3/trades?symbol=LTCUSDT", "input": ["LTC/USDT"]}, {"description": "spot fetchTrades", "method": "fetchTrades", "url": "https://api.binance.com/api/v3/trades?symbol=BTCUSDT", "input": ["BTC/USDT"]}], "fetchTime": [{"description": "fetchTime", "method": "fetchTime", "url": "https://www.tokocrypto.com/open/v1/common/time", "input": []}], "fetchOrderBook": [{"description": "spot orderbook", "method": "fetchOrderBook", "url": "https://api.binance.com/api/v3/depth?symbol=BTCUSDT", "input": ["BTC/USDT"]}], "fetchBidsAsks": [{"description": "spot bidsasks", "method": "fetchBidsAsks", "url": "https://api.binance.com/api/v3/ticker/bookTicker", "input": [["BTC/USDT", "ETH/USDT"]]}], "fetchOHLCV": [{"description": "spot ohlcv", "method": "fetchOHLCV", "url": "https://api.binance.com/api/v3/klines?interval=1m&limit=500&symbol=BTCUSDT", "input": ["BTC/USDT"]}]}}