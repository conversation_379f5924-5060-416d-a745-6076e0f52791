{"exchange": "xt", "skipKeys": [], "outputType": "both", "methods": {"setMarginMode": [{"description": "set margin mode isolated", "method": "setMarginMode", "url": "https://fapi.xt.com/future/user/v1/position/change-type", "input": ["isolated", "BTC/USDT:USDT", {"positionSide": "LONG"}], "output": "{\"positionType\":\"ISOLATED\",\"positionSide\":\"LONG\",\"symbol\":\"btc_usdt\"}"}, {"description": "set margin mode long", "method": "setMarginMode", "url": "https://fapi.xt.com/future/user/v1/position/change-type", "input": ["cross", "BTC/USDT:USDT", {"positionSide": "LONG"}], "output": "{\"positionType\":\"CROSSED\",\"positionSide\":\"LONG\",\"symbol\":\"btc_usdt\"}"}], "createOrder": [{"description": "limit spot order", "method": "createOrder", "url": "https://sapi.xt.com/v4/order", "input": ["LTC/USDT", "limit", "buy", 0.1, 50], "output": "{\"symbol\":\"ltc_usdt\",\"side\":\"BUY\",\"type\":\"LIMIT\",\"bizType\":\"SPOT\",\"price\":\"50\",\"quantity\":\"0.1\",\"timeInForce\":\"GTC\",\"media\":\"CCXT\"}"}, {"description": "create swap order", "method": "createOrder", "url": "https://fapi.xt.com/future/trade/v1/order/create", "input": ["LTC/USDT:USDT", "limit", "buy", 1, 50], "output": "{\"symbol\":\"ltc_usdt\",\"origQty\":\"1\",\"positionSide\":\"LONG\",\"price\":\"50\",\"orderSide\":\"BUY\",\"orderType\":\"LIMIT\",\"clientMedia\":\"CCXT\"}"}, {"description": "spot market buy", "method": "createOrder", "url": "https://sapi.xt.com/v4/order", "input": ["LTC/USDT", "market", "buy", 1, 5], "output": "{\"symbol\":\"ltc_usdt\",\"side\":\"BUY\",\"type\":\"MARKET\",\"bizType\":\"SPOT\",\"quoteQty\":\"5\",\"timeInForce\":\"FOK\",\"media\":\"CCXT\"}"}, {"description": "spot market sell", "method": "createOrder", "url": "https://sapi.xt.com/v4/order", "input": ["LTC/USDT", "market", "sell", 0.1], "output": "{\"symbol\":\"ltc_usdt\",\"side\":\"SELL\",\"type\":\"MARKET\",\"bizType\":\"SPOT\",\"quantity\":\"0.1\",\"timeInForce\":\"FOK\",\"media\":\"CCXT\"}"}, {"description": "swap market buy", "method": "createOrder", "url": "https://fapi.xt.com/future/trade/v1/order/create", "input": ["LTC/USDT:USDT", "market", "buy", 1], "output": "{\"symbol\":\"ltc_usdt\",\"origQty\":\"1\",\"positionSide\":\"LONG\",\"orderSide\":\"BUY\",\"orderType\":\"MARKET\",\"clientMedia\":\"CCXT\"}"}, {"description": "swap market sell", "method": "createOrder", "url": "https://fapi.xt.com/future/trade/v1/order/create", "input": ["LTC/USDT:USDT", "market", "sell", 1], "output": "{\"symbol\":\"ltc_usdt\",\"origQty\":\"1\",\"positionSide\":\"SHORT\",\"orderSide\":\"SELL\",\"orderType\":\"MARKET\",\"clientMedia\":\"CCXT\"}"}], "fetchOrders": [{"description": "Spot orders", "method": "fetchOrders", "url": "https://sapi.xt.com/v4/history-order?bizType=SPOT&symbol=ltc_usdt", "input": ["LTC/USDT"]}, {"description": "Swap orders", "method": "fetchOrders", "url": "https://fapi.xt.com/future/trade/v1/order/list-history?symbol=ltc_usdt", "input": ["LTC/USDT:USDT"]}], "fetchMyTrades": [{"description": "Spot private trades", "method": "fetchMyTrades", "url": "https://sapi.xt.com/v4/trade?bizType=SPOT&limit=5&startTime=1699457638000&symbol=ltc_usdt", "input": ["LTC/USDT", 1699457638000, 5]}, {"description": "Swap private trades", "method": "fetchMyTrades", "url": "https://fapi.xt.com/future/trade/v1/order/trade-list?size=5&startTime=1699457638000&symbol=ltc_usdt", "input": ["LTC/USDT:USDT", 1699457638000, 5]}], "fetchOpenOrders": [{"description": "swap open orders no symbol", "method": "fetchOpenOrders", "url": "https://fapi.xt.com/future/trade/v1/order/list?state=UNFINISHED", "input": [null, null, null, {"type": "swap"}], "output": null}, {"description": "Spot open orders", "method": "fetchOpenOrders", "url": "https://sapi.xt.com/v4/open-order?bizType=SPOT&symbol=ltc_usdt", "input": ["LTC/USDT"]}, {"description": "Swap open orders", "method": "fetchOpenOrders", "url": "https://fapi.xt.com/future/trade/v1/order/list?state=UNFINISHED&symbol=ltc_usdt", "input": ["LTC/USDT:USDT"]}], "fetchClosedOrders": [{"description": "Spot closed orders", "method": "fetchClosedOrders", "url": "https://sapi.xt.com/v4/history-order?bizType=SPOT&state=FILLED&symbol=ltc_usdt", "input": ["LTC/USDT"]}, {"description": "Swap closed orders", "method": "fetchClosedOrders", "url": "https://fapi.xt.com/future/trade/v1/order/list?state=FILLED&symbol=ltc_usdt", "input": ["LTC/USDT:USDT"]}], "fetchBalance": [{"description": "Fetch spot Balance", "method": "fetchBalance", "url": "https://sapi.xt.com/v4/balances", "input": [{"type": "spot"}]}, {"description": "Fetch swap Balance", "method": "fetchBalance", "url": "https://fapi.xt.com/future/user/v1/balance/list", "input": [{"type": "swap"}]}], "fetchDeposits": [{"description": "Fetch deposits", "method": "fetchDeposits", "url": "https://sapi.xt.com/v4/deposit/history", "input": []}], "cancelOrder": [{"description": "cancel spot order", "method": "cancelOrder", "url": "https://sapi.xt.com/v4/order/371184502105500800", "input": ["371184502105500800", "LTC/USDT"]}, {"description": "cancel swap order", "method": "cancelOrder", "url": "https://fapi.xt.com/future/trade/v1/order/cancel", "input": ["371184779575507520", "LTC/USDT:USDT"], "output": "{\"orderId\":\"371184779575507520\"}"}], "fetchFundingInterval": [{"description": "linear swap fetch the funding interval", "method": "fetchFundingInterval", "url": "https://fapi.xt.com/future/market/v1/public/q/funding-rate?symbol=btc_usdt", "input": ["BTC/USDT:USDT"]}], "editOrder": [{"description": "Linear swap edit an order", "method": "editOrder", "url": "https://fapi.xt.com/future/trade/v1/order/update", "input": ["483869393976071040", "BTC/USDT:USDT", "limit", "buy", 2, 54000], "output": "{\"orderId\":\"483869393976071040\",\"price\":\"54000\",\"origQty\":\"2\"}"}, {"description": "Spot edit an order", "method": "editOrder", "url": "https://sapi.xt.com/v4/order/484203027161892224", "input": ["484203027161892224", "BTC/USDT", "limit", "buy", 0.002, 52000], "output": "{\"price\":\"52000\",\"quantity\":\"0.002\"}"}, {"description": "edit a stoploss order", "method": "editOrder", "url": "https://fapi.xt.com/future/trade/v1/entrust/update-profit-stop", "input": ["484221817085868800", "BTC/USDT:USDT", "limit", "sell", 2, null, {"stopLoss": 68000}], "output": "{\"profitId\":\"484221817085868800\",\"triggerStopPrice\":\"68000\"}"}, {"description": "edit a takeProfit order", "method": "editOrder", "url": "https://fapi.xt.com/future/trade/v1/entrust/update-profit-stop", "input": ["484222823387497216", "BTC/USDT:USDT", "limit", "sell", 2, null, {"takeProfit": 98000}], "output": "{\"profitId\":\"484222823387497216\",\"triggerProfitPrice\":\"98000\"}"}]}}