{"exchange": "bitrue", "skipKeys": ["timestamp", "signature"], "outputType": "both", "methods": {"createOrder": [{"description": "Spot market buy", "method": "createOrder", "url": "https://www.bitrue.com/api/v1/order", "input": ["LTC/USDT", "market", "buy", 0.2], "output": "timestamp=1700667100215&recvWindow=5000&side=BUY&type=MARKET&symbol=LTCUSDT&quantity=0.2&signature=e7055632b348105337e21151c8a0ea36dc466767d49eadb428b1a132f8e32dd5"}, {"description": "Spot market sell", "method": "createOrder", "url": "https://www.bitrue.com/api/v1/order", "input": ["LTC/USDT", "market", "sell", 0.2], "output": "timestamp=1700667143220&recvWindow=5000&side=SELL&type=MARKET&symbol=LTCUSDT&quantity=0.2&signature=365a3b9612d53caec57874d40de2ff06fa437d81a60c8d04e16c1cf20d5b4e47"}, {"description": "Spot limit buy", "method": "createOrder", "url": "https://www.bitrue.com/api/v1/order", "input": ["LTC/USDT", "limit", "buy", 0.2, "50"], "output": "timestamp=1700667180381&recvWindow=5000&side=BUY&type=LIMIT&price=50&symbol=LTCUSDT&quantity=0.2&signature=7e0639853ef89a213ed80711a4fc155a0b8b25d97b7dfc72027646c1925f4213"}, {"description": "Swap limit sell with postOnly and leverage", "method": "createOrder", "url": "https://fapi.bitrue.com/fapi/v2/order", "input": ["LTC/USDT:USDT", "limit", "sell", 0.1, "100", {"postOnly": true, "leverage": 5}], "output": "{\"recvWindow\":5000,\"side\":\"SELL\",\"type\":\"POST_ONLY\",\"price\":\"100\",\"contractName\":\"E-LTC-USDT\",\"amount\":0.1,\"volume\":0.1,\"positionType\":1,\"open\":\"OPEN\",\"leverage\":5,\"postOnly\":true}"}, {"description": "Swap market buy", "method": "createOrder", "url": "https://fapi.bitrue.com/fapi/v2/order", "input": ["LTC/USDT:USDT", "market", "buy", "15", "1", {"leverage": 1}], "output": "{\"recvWindow\":5000,\"side\":\"BUY\",\"type\":\"MARKET\",\"contractName\":\"E-LTC-USDT\",\"amount\":\"15\",\"volume\":\"15\",\"positionType\":1,\"open\":\"OPEN\",\"leverage\":1}"}, {"description": "Swap market buy using cost in params", "method": "createOrder", "url": "https://fapi.bitrue.com/fapi/v2/order", "input": ["LTC/USDT:USDT", "market", "buy", 0, null, {"cost": 15}], "output": "{\"recvWindow\":5000,\"side\":\"BUY\",\"type\":\"MARKET\",\"contractName\":\"E-LTC-USDT\",\"amount\":\"15\",\"volume\":\"15\",\"positionType\":1,\"open\":\"OPEN\",\"leverage\":1}"}, {"description": "Swap market buy with createMarketBuyOrderRequiresPrice set to false", "method": "createOrder", "url": "https://fapi.bitrue.com/fapi/v2/order", "input": ["BTC/USDT:USDT", "market", "buy", 50, null, {"createMarketBuyOrderRequiresPrice": false, "leverage": 10}], "output": "{\"recvWindow\":5000,\"side\":\"BUY\",\"type\":\"MARKET\",\"contractName\":\"E-BTC-USDT\",\"amount\":50,\"volume\":50,\"positionType\":1,\"open\":\"OPEN\",\"leverage\":10}"}, {"description": "Swap market sell", "method": "createOrder", "url": "https://fapi.bitrue.com/fapi/v2/order", "input": ["BTC/USDT:USDT", "market", "sell", 0.0011, null, {"reduceOnly": true, "leverage": 10}], "output": "{\"recvWindow\":5000,\"side\":\"SELL\",\"type\":\"MARKET\",\"contractName\":\"E-BTC-USDT\",\"amount\":0.0011,\"volume\":0.0011,\"positionType\":1,\"open\":\"CLOSE\",\"leverage\":10}"}], "createMarketBuyOrderWithCost": [{"description": "Swap market buy order with cost", "method": "createMarketBuyOrderWithCost", "url": "https://fapi.bitrue.com/fapi/v2/order", "input": ["BTC/USDT:USDT", 50, {"leverage": 10, "createMarketBuyOrderRequiresPrice": false}], "output": "{\"recvWindow\":5000,\"side\":\"BUY\",\"type\":\"MARKET\",\"contractName\":\"E-BTC-USDT\",\"amount\":50,\"volume\":50,\"positionType\":1,\"open\":\"OPEN\",\"leverage\":10}"}], "fetchMyTrades": [{"description": "Spot private trades", "method": "fetchMyTrades", "url": "https://www.bitrue.com/api/v2/myTrades?timestamp=1699458295104&recvWindow=5000&symbol=LTCUSDT&startTime=1699457638000&limit=5&signature=3a6f1085519a0e0db807f9726babcc5e0c2a614a120db45c310b103642c68115", "input": ["LTC/USDT", 1699457638000, 5]}, {"description": "Swap private trades", "method": "fetchMyTrades", "url": "https://fapi.bitrue.com/fapi/v2/myTrades?contractName=E-LTC-USDT", "input": ["LTC/USDT:USDT"]}], "fetchOpenOrders": [{"description": "Spot open orders", "method": "fetchOpenOrders", "url": "https://www.bitrue.com/api/v1/openOrders?timestamp=1699458295339&recvWindow=5000&symbol=LTCUSDT&signature=874343429f5aafb6e632520f5525e0679808a8cdc3a03c35d0e4bbee2b2dec2c", "input": ["LTC/USDT"]}, {"description": "Swap open orders", "method": "fetchOpenOrders", "url": "https://fapi.bitrue.com/fapi/v2/openOrders?contractName=E-ETH-USDT", "input": ["ETH/USDT:USDT"]}, {"description": "Swap open orders", "method": "fetchOpenOrders", "url": "https://dapi.bitrue.com/dapi/v2/openOrders?contractName=E-ETH-USD", "input": ["ETH/USD:ETH"]}], "fetchClosedOrders": [{"description": "Spot closed orders", "method": "fetchClosedOrders", "url": "https://www.bitrue.com/api/v1/allOrders?timestamp=*************&recvWindow=5000&symbol=LTCUSDT&signature=3cefafed031707bcca221c14f25f4c5d2595cac27919cd8aed878aed4b40741a", "input": ["LTC/USDT"]}], "fetchOrder": [{"description": "Fetch swap order", "method": "fetchOrder", "url": "https://fapi.bitrue.com/fapi/v2/order?orderId=1941111689217512895&contractName=E-LTC-USDT", "input": ["1941111689217512895", "LTC/USDT:USDT"]}], "fetchBalance": [{"description": "Spot balance", "method": "fetchBalance", "url": "https://www.bitrue.com/api/v1/account?timestamp=*************&recvWindow=5000&signature=e07b956abe71330a20470a6db83451040ba6fcd5315351899e64fb553ac6da89", "input": []}, {"description": "Swap balance", "method": "fetchBalance", "url": "https://fapi.bitrue.com/fapi/v2/account?", "input": [{"type": "swap"}]}], "cancelOrder": [{"description": "Cancel spot order", "method": "cancelOrder", "url": "https://www.bitrue.com/api/v1/order?timestamp=*************&recvWindow=5000&orderId=482075019769610240&symbol=LTCUSDT&signature=9b1b6fbab9a003ebb0af7128dd0f4f3597b5e3fc40d7ed195d5ba255562160cc", "input": ["482075019769610240", "LTC/USDT"]}, {"description": "Cancel swap order", "method": "cancelOrder", "url": "https://fapi.bitrue.com/fapi/v2/cancel", "input": ["1941111689217512895", "LTC/USDT:USDT"], "output": "{\"recvWindow\":5000,\"orderId\":\"1941111689217512895\",\"contractName\":\"E-LTC-USDT\"}"}], "fetchWithdrawals": [{"description": "<PERSON><PERSON>", "method": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://www.bitrue.com/api/v1/withdraw/history?timestamp=*************&recvWindow=5000&coin=usdc&status=5&signature=5f27a957b7e087e9eb4ade590ae3969695b19d9f3d6de68713d75a0376fbce0d", "input": ["USDC"]}], "fetchTime": [{"description": "fetchTime", "method": "fetchTime", "url": "https://www.bitrue.com/api/v1/time", "input": []}], "fetchTrades": [{"description": "spot fetchTrades", "method": "fetchTrades", "url": "https://www.bitrue.com/api/v1/trades?symbol=BTCUSDT", "input": ["BTC/USDT"]}], "fetchOrderBook": [{"description": "spot orderbook", "method": "fetchOrderBook", "url": "https://www.bitrue.com/api/v1/depth?symbol=BTCUSDT", "input": ["BTC/USDT"]}, {"description": "swap orderbook", "method": "fetchOrderBook", "url": "https://fapi.bitrue.com/fapi/v1/depth?contractName=E-BTC-USDT", "input": ["BTC/USDT:USDT"]}], "fetchTicker": [{"description": "Swap ticker", "method": "fetchTicker", "url": "https://fapi.bitrue.com/fapi/v1/ticker?contractName=E-BTC-USDT", "input": ["BTC/USDT:USDT"]}, {"description": "spot ticker", "method": "fetchTicker", "url": "https://www.bitrue.com/api/v1/ticker/24hr?symbol=BTCUSDT", "input": ["BTC/USDT"]}], "fetchTickers": [{"description": "spot tickers", "method": "fetchTickers", "url": "https://www.bitrue.com/api/v1/ticker/24hr", "input": [["BTC/USDT", "ETH/USDT"]]}], "fetchBidsAsks": [{"description": "spot bidsasks", "method": "fetchBidsAsks", "url": "https://www.bitrue.com/api/v1/ticker/bookTicker?symbol=BTCUSDT", "input": [["BTC/USDT", "ETH/USDT"]]}], "fetchOHLCV": [{"description": "spot ohlcv", "method": "fetchOHLCV", "url": "https://www.bitrue.com/api/v1/market/kline?symbol=BTCUSDT&scale=1m", "input": ["BTC/USDT"]}, {"description": "swap ohlcv", "method": "fetchOHLCV", "url": "https://fapi.bitrue.com/fapi/v1/klines?contractName=E-BTC-USDT&interval=1min", "input": ["BTC/USDT:USDT"]}]}}