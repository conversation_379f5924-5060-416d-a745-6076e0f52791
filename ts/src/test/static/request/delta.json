{"exchange": "delta", "skipKeys": ["end", "start"], "outputType": "json", "methods": {"fetchCurrencies": [{"description": "fetchCurrencies", "method": "fetchCurrencies", "url": "https://api.delta.exchange/v2/assets", "input": [], "output": null}], "fetchOpenOrders": [{"description": "Fetch Open Orders", "method": "fetchOpenOrders", "url": "https://api.delta.exchange/v2/orders?product_ids=8320", "input": ["BTC/USDT"]}], "fetchClosedOrders": [{"description": "Fetch Closed Orders", "method": "fetchClosedOrders", "url": "https://api.delta.exchange/v2/orders/history?product_ids=8320", "input": ["BTC/USDT"]}], "fetchMarginMode": [{"description": "Fetch the set margin mode", "method": "fetchMarginMode", "url": "https://testnet-api.delta.exchange/v2/profile", "input": ["BTC/USDT:USDT"]}], "fetchTime": [{"description": "fetchTime", "method": "fetchTime", "url": "https://api.delta.exchange/v2/settings", "input": []}], "fetchTrades": [{"description": "spot fetchTrades", "method": "fetchTrades", "url": "https://api.delta.exchange/v2/trades/BTC_USDT", "input": ["BTC/USDT"]}, {"description": "swap fetchTrades", "method": "fetchTrades", "url": "https://api.delta.exchange/v2/trades/BTCUSDT", "input": ["BTC/USDT:USDT"]}], "fetchOrderBook": [{"description": "spot orderbook", "method": "fetchOrderBook", "url": "https://api.delta.exchange/v2/l2orderbook/BTC_USDT", "input": ["BTC/USDT"]}, {"description": "swap orderbook", "method": "fetchOrderBook", "url": "https://api.delta.exchange/v2/l2orderbook/BTCUSDT", "input": ["BTC/USDT:USDT"]}], "fetchTicker": [{"description": "Swap ticker", "method": "fetchTicker", "url": "https://api.delta.exchange/v2/tickers/BTCUSDT", "input": ["BTC/USDT:USDT"]}, {"description": "spot ticker", "method": "fetchTicker", "url": "https://api.delta.exchange/v2/tickers/BTC_USDT", "input": ["BTC/USDT"]}], "fetchTickers": [{"description": "spot tickers", "method": "fetchTickers", "url": "https://api.delta.exchange/v2/tickers", "input": [["BTC/USDT"]]}, {"description": "swap tickers", "method": "fetchTickers", "url": "https://api.delta.exchange/v2/tickers", "input": [["BTC/USDT:USDT"]]}], "fetchOHLCV": [{"description": "spot ohlcv", "method": "fetchOHLCV", "url": "https://api.delta.exchange/v2/history/candles?resolution=1m&end=1709992992&start=1709872992&symbol=BTC_USDT", "input": ["BTC/USDT"]}, {"description": "Since and until defined", "method": "fetchOHLCV", "url": "https://api.delta.exchange/v2/history/candles?resolution=1h&start=1735603200&end=173568960&symbol=BTC_USDT", "input": ["BTC/USDT", "1h", 1735603200000, null, {"until": 1735689600000}]}, {"description": "Since, limit and until defined", "method": "fetchOHLCV", "url": "https://api.delta.exchange/v2/history/candles?resolution=1h&start=1735603200&end=1735689600&symbol=BTC_USDT", "input": ["BTC/USDT", "1h", 1735603200000, 3, {"until": 1735689600000}]}, {"description": "Fill this with a description of the method call", "method": "fetchOHLCV", "url": "https://api.delta.exchange/v2/history/candles?resolution=1h&end=1735689600&start=1728489600&symbol=BTC_USDT", "input": ["BTC/USDT", "1h", null, null, {"until": 1735689600000}]}, {"description": "swap ohlcv", "method": "fetchOHLCV", "url": "https://api.delta.exchange/v2/history/candles?resolution=1m&end=1709992993&start=1709872993&symbol=BTCUSDT", "input": ["BTC/USDT:USDT"]}, {"description": "Swap since limit until defined", "method": "fetchOHLCV", "url": "https://api.delta.exchange/v2/history/candles?resolution=1h&start=1735603200&end=1735689600&symbol=BTCUSDT", "input": ["BTC/USDT:USDT", "1h", 1735603200000, 3, {"until": 1735689600000}]}, {"description": "Swap until defined", "method": "fetchOHLCV", "url": "https://api.delta.exchange/v2/history/candles?resolution=1h&end=1735689600&start=1728489600&symbol=BTCUSDT", "input": ["BTC/USDT:USDT", "1h", null, null, {"until": 1735689600000}]}], "fetchFundingRate": [{"description": "fundingRate", "method": "fetchFundingRate", "url": "https://api.delta.exchange/v2/tickers/BTCUSDT", "input": ["BTC/USDT:USDT"]}], "fetchDepositAddress": [{"description": "fetch deposit address", "method": "fetchDepositAddress", "url": "https://api.delta.exchange/v2/deposits/address?asset_symbol=BTC", "input": ["BTC"]}], "fetchOption": [{"description": "Fetch an option contract", "method": "fetchOption", "url": "https://api.delta.exchange/v2/tickers/P-BTC-65000-290324", "input": ["BTC/USDT:USDT-240329-65000-P"]}]}}