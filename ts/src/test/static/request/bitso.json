{"exchange": "bitso", "skipKeys": [], "outputType": "json", "methods": {"createOrder": [{"description": "market buy order", "method": "createOrder", "url": "https://bitso.com/api/v3/orders", "input": ["XRP/USDT", "market", "buy", 10], "output": "{\"book\":\"xrp_usdt\",\"side\":\"buy\",\"type\":\"market\",\"major\":\"10\"}"}, {"description": "market sell order", "method": "createOrder", "url": "https://bitso.com/api/v3/orders", "input": ["XRP/USDT", "market", "sell", 10], "output": "{\"book\":\"xrp_usdt\",\"side\":\"sell\",\"type\":\"market\",\"major\":\"10\"}"}, {"description": "limit buy order", "method": "createOrder", "url": "https://bitso.com/api/v3/orders", "input": ["XRP/USDT", "limit", "buy", 10, 0.4], "output": "{\"book\":\"xrp_usdt\",\"side\":\"buy\",\"type\":\"limit\",\"major\":\"10\",\"price\":\"0.4\"}"}], "cancelOrder": [{"description": "cancel order", "method": "cancelOrder", "url": "https://bitso.com/api/v3/orders/hG5WlCiC4OWRt2up", "input": ["hG5WlCiC4OWRt2up", "XRP/USDT"]}], "cancelAllOrders": [{"description": "cancelAllOrders", "method": "cancelAllOrders", "url": "https://bitso.com/api/v3/orders/all", "input": []}], "fetchBalance": [{"description": "Fetch spot Balance", "method": "fetchBalance", "url": "https://bitso.com/api/v3/balance?type=spot", "input": [{"type": "spot"}]}, {"description": "Fetch swap Balance", "method": "fetchBalance", "url": "https://bitso.com/api/v3/balance?type=swap", "input": [{"type": "swap"}]}], "fetchDeposits": [{"description": "Fetch deposits", "method": "fetchDeposits", "url": "https://bitso.com/api/v3/fundings", "input": []}], "fetchLedger": [{"description": "fetch USDT ledger", "method": "fetchLedger", "url": "https://bitso.com/api/v3/ledger", "input": ["USDT"]}], "fetchTrades": [{"description": "spot fetchTrades", "method": "fetchTrades", "url": "https://bitso.com/api/v3/trades?book=btc_usdt", "input": ["BTC/USDT"]}], "fetchOrderBook": [{"description": "spot orderbook", "method": "fetchOrderBook", "url": "https://bitso.com/api/v3/order_book?book=btc_usdt", "input": ["BTC/USDT"]}], "fetchTicker": [{"description": "spot ticker", "method": "fetchTicker", "url": "https://bitso.com/api/v3/ticker?book=btc_usdt", "input": ["BTC/USDT"]}], "fetchMyTrades": [{"description": "Fetch my trades", "method": "fetchMyTrades", "url": "https://bitso.com/api/v3/user_trades?book=btc_usdt&limit=25", "input": ["BTC/USDT"]}]}}