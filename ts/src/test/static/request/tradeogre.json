{"exchange": "tradeogre", "skipKeys": [], "outputType": "both", "methods": {"fetchOHLCV": [{"description": "OHLCV", "method": "fetchOHLCV", "url": "https://tradeogre.com/api/v1/chart/1m/BTC-USDT", "input": ["BTC/USDT", "1m"], "output": null}], "fetchTickers": [{"description": "Fetch tickers", "method": "fetchTickers", "url": "https://tradeogre.com/api/v1/markets", "input": [], "output": null}], "fetchTicker": [{"description": "fetch ticker", "method": "fetchTicker", "url": "https://tradeogre.com/api/v1/ticker/BTC-USDT", "input": ["BTC/USDT"]}], "fetchTrades": [{"description": "fetch trades", "method": "fetchTrades", "url": "https://tradeogre.com/api/v1/history/BTC-USDT", "input": ["BTC/USDT"]}], "fetchOrderBook": [{"description": "fetch orderbook", "method": "fetchOrderBook", "url": "https://tradeogre.com/api/v1/orders/LTC-USDT", "input": ["LTC/USDT"]}], "fetchOpenOrders": [{"description": "fetch open orders", "method": "fetchOpenOrders", "url": "https://tradeogre.com/api/v1/account/orders", "input": ["LTC/USDT"], "output": "market=LTC-USDT"}], "createOrder": [{"description": "limit buy order", "method": "createOrder", "url": "https://tradeogre.com/api/v1/order/buy", "input": ["LTC/USDT", "limit", "buy", 0.1, 50], "output": "market=LTC-USDT&quantity=0.1&price=50"}, {"description": "limit sell order", "method": "createOrder", "url": "https://tradeogre.com/api/v1/order/sell", "input": ["LTC/USDT", "limit", "sell", 0.1, 87], "output": "market=LTC-USDT&quantity=0.1&price=87"}], "cancelOrder": [{"description": "Fill this with a description of the method call", "method": "cancelOrder", "url": "https://tradeogre.com/api/v1/order/cancel", "input": ["b8f62742-bb59-4f87-a13a-bfd71560847c"], "output": "uuid=b8f62742-bb59-4f87-a13a-bfd71560847c"}], "fetchBalance": [{"description": "fetch single currency balance", "method": "fetchBalance", "url": "https://tradeogre.com/api/v1/account/balance", "input": [{"currency": "USDT"}], "output": "currency=USDT"}, {"description": "fetch balance", "method": "fetchBalance", "url": "https://tradeogre.com/api/v1/account/balances", "input": []}], "fetchOrder": [{"description": "fetch order", "method": "fetchOrder", "url": "https://tradeogre.com/api/v1/account/order/fb3545c8-837a-4245-bdfa-79e857230d82", "input": ["fb3545c8-837a-4245-bdfa-79e857230d82"]}]}}