{"exchange": "coincatch", "skipKeys": ["startTime", "endTime"], "outputType": "both", "methods": {"fetchTime": [{"description": "Fetch time", "method": "fetchTime", "url": "https://api.coincatch.com/api/spot/v1/public/time", "input": []}], "fetchCurrencies": [{"description": "fetchCurrencies", "method": "fetchCurrencies", "url": "https://api.coincatch.com/api/spot/v1/public/currencies", "input": [], "output": null}, {"description": "Fetch currencies", "method": "fetchCurrencies", "url": "https://api.coincatch.com/api/spot/v1/public/currencies", "input": []}], "fetchTicker": [{"description": "Fetch ticker", "method": "fetchTicker", "url": "https://api.coincatch.com/api/spot/v1/market/ticker?symbol=ETHUSDT_SPBL", "input": ["ETH/USDT"]}, {"description": "Fetch ticker for swap", "method": "fetchTicker", "url": "https://api.coincatch.com/api/mix/v1/market/ticker?symbol=ETHUSDT_UMCBL", "input": ["ETH/USDT:USDT"]}], "fetchTickers": [{"description": "Fetch tickers", "method": "fetchTickers", "url": "https://api.coincatch.com/api/spot/v1/market/tickers", "input": [["ETH/USDT", "BTC/USDT"]]}], "fetchOrderBook": [{"description": "Fetch order book", "method": "fetchOrderBook", "url": "https://api.coincatch.com/api/spot/v1/market/merge-depth?symbol=ETHUSDT_SPBL&limit=1", "input": ["ETH/USDT", 1]}, {"description": "Fetch order book for swap", "method": "fetchOrderBook", "url": "https://api.coincatch.com/api/mix/v1/market/merge-depth?symbol=ETHUSDT_UMCBL&limit=1", "input": ["ETH/USDT:USDT", 1]}], "fetchOHLCV": [{"description": "Fetch OHLCV", "method": "fetchOHLCV", "url": "https://api.coincatch.com/api/spot/v1/market/candles?symbol=ETHUSDT_SPBL&period=1min", "input": ["ETH/USDT"]}, {"description": "Fetch OHLCV for swap", "method": "fetchOHLCV", "url": "https://api.coincatch.com/api/mix/v1/market/candles?symbol=ETHUSDT_UMCBL&granularity=1m&startTime=1729437331987&endTime=1729497331987", "input": ["ETH/USDT:USDT"]}, {"description": "Fetch OHLCV with since and limit", "method": "fetchOHLCV", "url": "https://api.coincatch.com/api/spot/v1/market/candles?symbol=ETHUSDT_SPBL&limit=1000&period=1min&after=1726956360000", "input": ["ETH/USDT", "1m", 1726956360000, 1]}], "fetchTrades": [{"description": "Fetch trades", "method": "fetchTrades", "url": "https://api.coincatch.com/api/spot/v1/market/fills-history?symbol=ETHUSDT_SPBL", "input": ["ETH/USDT"]}, {"description": "Fetch trades for swap", "method": "fetchTrades", "url": "https://api.coincatch.com/api/mix/v1/market/fills-history?symbol=ETHUSDT_UMCBL", "input": ["ETH/USDT:USDT"]}, {"description": "Fetch trades for swap with since and limit", "method": "fetchTrades", "url": "https://api.coincatch.com/api/mix/v1/market/fills-history?symbol=ETHUSDT_UMCBL&startTime=1726929987000&limit=1000", "input": ["ETH/USDT:USDT", 1726929987000, 1]}], "fetchFundingRate": [{"description": "Fetch funding rate", "method": "fetchFundingRate", "url": "https://api.coincatch.com/api/mix/v1/market/current-fundRate?symbol=ETHUSDT_UMCBL&productType=UMCBL", "input": ["ETH/USDT:USDT"]}], "fetchFundingRateHistory": [{"description": "Fetch funding rate history", "method": "fetchFundingRateHistory", "url": "https://api.coincatch.com/api/mix/v1/market/history-fundRate?symbol=ETHUSDT_UMCBL", "input": ["ETH/USDT:USDT"]}, {"description": "Fetch funding rate history with since and limit", "method": "fetchFundingRateHistory", "url": "https://api.coincatch.com/api/mix/v1/market/history-fundRate?symbol=ETHUSDT_UMCBL&pageSize=100", "input": ["ETH/USDT:USDT", *************, 1]}], "fetchBalance": [{"description": "Fetch balance", "method": "fetchBalance", "url": "https://api.coincatch.com/api/spot/v1/account/assets", "input": []}, {"description": "Fetch balance for swap", "method": "fetchBalance", "url": "https://api.coincatch.com/api/mix/v1/account/accounts?productType=umcbl", "input": [{"type": "swap"}]}, {"description": "swap balance with defaultType = swap", "method": "fetchBalance", "options": {"defaultType": "swap"}, "url": "https://api.coincatch.com/api/mix/v1/account/accounts?productType=umcbl", "input": []}], "fetchDepositAddress": [{"description": "Fill this with a description of the method call", "method": "fetchDepositAddress", "url": "https://api.coincatch.com/api/spot/v1/wallet/deposit-address?coin=USDT&chain=TRC20", "input": ["USDT", {"network": "TRC20"}]}], "fetchDeposits": [{"description": "Fetch deposits", "method": "fetchDeposits", "url": "https://api.coincatch.com/api/spot/v1/wallet/deposit-list?coin=USDT&startTime=*************&endTime=*************", "input": ["USDT", *************, 1, {"until": *************}]}], "createOrder": [{"description": "Spot limit buy order", "method": "createOrder", "url": "https://api.coincatch.com/api/spot/v1/trade/orders", "input": ["BTC/USDT", "limit", "buy", 0.0002, 25000], "output": "{\"symbol\":\"BTCUSDT_SPBL\",\"side\":\"buy\",\"orderType\":\"limit\",\"price\":25000,\"quantity\":\"0.0002\",\"force\":\"normal\"}"}, {"description": "Spot cross limit buy order", "method": "createOrder", "url": "https://api.coincatch.com/api/spot/v1/trade/orders", "input": ["BTC/USDT", "limit", "buy", 0.0002, 25000, {"marginMode": "cross", "methodName": "createOrder"}], "output": "{\"symbol\":\"BTCUSDT_SPBL\",\"side\":\"buy\",\"orderType\":\"limit\",\"price\":25000,\"quantity\":\"0.0002\",\"force\":\"normal\",\"marginMode\":\"cross\"}"}, {"description": "Swap cross limit buy order", "method": "createOrder", "url": "https://api.coincatch.com/api/mix/v1/order/placeOrder", "input": ["BTC/USDT:USDT", "limit", "buy", 0.001, 25000, {"marginMode": "cross", "methodName": "createOrder"}], "output": "{\"symbol\":\"BTCUSDT_UMCBL\",\"marginCoin\":\"USDT\",\"size\":\"0.001\",\"price\":\"25000\",\"orderType\":\"limit\",\"side\":\"buy_single\",\"marginMode\":\"cross\"}"}, {"description": "Swap cross limit buy order with trigger price", "method": "createOrder", "url": "https://api.coincatch.com/api/mix/v1/plan/placePlan", "input": ["BTC/USDT:USDT", "limit", "buy", 0.001, 25000, {"marginMode": "cross", "triggerPrice": 26000, "methodName": "createOrder"}], "output": "{\"symbol\":\"BTCUSDT_UMCBL\",\"marginCoin\":\"USDT\",\"size\":\"0.001\",\"orderType\":\"limit\",\"side\":\"buy_single\",\"triggerType\":\"fill_price\",\"executePrice\":\"25000\",\"triggerPrice\":\"26000\",\"marginMode\":\"cross\"}"}, {"description": "Spot market buy order", "method": "createOrder", "url": "https://api.coincatch.com/api/spot/v1/trade/orders", "input": ["BTC/USDT", "market", "buy", 0.0002, 40000], "output": "{\"symbol\":\"BTCUSDT_SPBL\",\"side\":\"buy\",\"orderType\":\"market\",\"quantity\":\"8\",\"force\":\"normal\"}"}, {"description": "Spot market sell order", "method": "createOrder", "url": "https://api.coincatch.com/api/spot/v1/trade/orders", "input": ["BTC/USDT", "market", "sell", 0.0001], "output": "{\"symbol\":\"BTCUSDT_SPBL\",\"side\":\"sell\",\"orderType\":\"market\",\"quantity\":\"0.0001\",\"force\":\"normal\"}"}, {"description": "spot margin market buy with createMarketBuyOrderRequiresPrice = false", "method": "createOrder", "url": "https://api.coincatch.com/api/spot/v1/trade/orders", "input": ["LTC/USDT", "market", "buy", 10, null, {"createMarketBuyOrderRequiresPrice": false, "marginMode": "cross", "methodName": "createOrder"}], "output": "{\"symbol\":\"LTCUSDT_SPBL\",\"side\":\"buy\",\"orderType\":\"market\",\"quantity\":\"10\",\"force\":\"normal\",\"marginMode\":\"cross\"}"}, {"description": "Spot margin market buy", "method": "createOrder", "url": "https://api.coincatch.com/api/spot/v1/trade/orders", "input": ["LTC/USDT", "market", "buy", 6, 1, {"marginMode": "cross", "methodName": "createOrder"}], "output": "{\"symbol\":\"LTCUSDT_SPBL\",\"side\":\"buy\",\"orderType\":\"market\",\"quantity\":\"6\",\"force\":\"normal\",\"marginMode\":\"cross\"}"}, {"description": "Spot limit buy with post only", "method": "createOrder", "url": "https://api.coincatch.com/api/mix/v1/order/placeOrder", "input": ["BTC/USDT:USDT", "limit", "buy", 0.001, 25000, {"postOnly": true, "methodName": "createOrder"}], "output": "{\"symbol\":\"BTCUSDT_UMCBL\",\"marginCoin\":\"USDT\",\"size\":\"0.001\",\"timeInForceValue\":\"post_only\",\"price\":\"25000\",\"orderType\":\"limit\",\"side\":\"buy_single\"}"}, {"description": "Swap limit sell order with postOnly and reduceOnly", "method": "createOrder", "url": "https://api.coincatch.com/api/mix/v1/order/placeOrder", "input": ["BTC/USDT:USDT", "limit", "sell", 0.001, 67753.9, {"postOnly": true, "reduceOnly": true, "marginMode": "cross", "methodName": "createOrder"}], "output": "{\"symbol\":\"BTCUSDT_UMCBL\",\"marginCoin\":\"USDT\",\"size\":\"0.001\",\"timeInForceValue\":\"post_only\",\"price\":\"67753.9\",\"orderType\":\"limit\",\"side\":\"sell_single\",\"marginMode\":\"cross\"}"}], "createMarketBuyOrderWithCost": [{"description": "Spot create market buy order with cost", "method": "createMarketBuyOrderWithCost", "url": "https://api.coincatch.com/api/spot/v1/trade/orders", "input": ["BTC/USDT", 2], "output": "{\"symbol\":\"BTCUSDT_SPBL\",\"side\":\"buy\",\"orderType\":\"market\",\"quantity\":\"2\",\"force\":\"normal\"}"}], "fetchOpenOrders": [{"description": "Fetch open orders", "method": "fetchOpenOrders", "url": "https://api.coincatch.com/api/spot/v1/trade/open-orders", "input": ["ETH/USDT"], "output": "{\"symbol\":\"ETHUSDT_SPBL\"}"}, {"description": "Fetch open orders for swap", "method": "fetchOpenOrders", "url": "https://api.coincatch.com/api/mix/v1/order/current?symbol=BTCUSDT_UMCBL", "input": ["BTC/USDT:USDT"]}], "fetchCanceledAndClosedOrders": [{"description": "Fetch canceled and closed orders", "method": "fetchCanceledAndClosedOrders", "url": "https://api.coincatch.com/api/spot/v1/trade/history", "input": ["ETH/USDT"], "output": "{\"symbol\":\"ETHUSDT_SPBL\"}"}], "cancelAllOrders": [{"description": "Cancel all orders for swap", "method": "cancelAllOrders", "url": "https://api.coincatch.com/api/mix/v1/order/cancel-symbol-orders", "input": ["BTC/USDT:USDT"], "output": "{\"symbol\":\"BTCUSDT_UMCBL\",\"marginCoin\":\"USDT\"}"}], "fetchMyTrades": [{"description": "Fill this with a description of the method call", "method": "fetchMyTrades", "url": "https://api.coincatch.com/api/spot/v1/trade/fills", "input": ["ETH/USDT"], "output": "{\"symbol\":\"ETHUSDT_SPBL\"}"}, {"description": "Fetch my trades with since and limit", "method": "fetchMyTrades", "url": "https://api.coincatch.com/api/spot/v1/trade/fills", "input": ["ETH/USDT", *************, 1], "output": "{\"symbol\":\"ETHUSDT_SPBL\",\"limit\":500}"}], "fetchMarginMode": [{"description": "Fetch margin mode", "method": "fetchMarginMode", "url": "https://api.coincatch.com/api/mix/v1/account/account?symbol=ETHUSDT_UMCBL&marginCoin=USDT", "input": ["ETH/USDT:USDT"]}], "setMarginMode": [{"description": "Set margin mode", "method": "setMarginMode", "url": "https://api.coincatch.com/api/mix/v1/account/setMarginMode", "input": ["cross", "ETH/USDT:USDT"], "output": "{\"symbol\":\"ETHUSDT_UMCBL\",\"marginCoin\":\"USDT\",\"marginMode\":\"crossed\"}"}], "fetchPositionMode": [{"description": "Fetch position mode", "method": "fetchPositionMode", "url": "https://api.coincatch.com/api/mix/v1/account/account?symbol=ETHUSDT_UMCBL&marginCoin=USDT", "input": ["ETH/USDT:USDT"]}], "setPositionMode": [{"description": "Set position mode", "method": "setPositionMode", "url": "https://api.coincatch.com/api/mix/v1/account/setPositionMode", "input": [true, "ETH/USDT:USDT"], "output": "{\"productType\":\"umcbl\",\"holdMode\":\"double_hold\"}"}], "fetchLeverage": [{"description": "Fetch leverage", "method": "fetchLeverage", "url": "https://api.coincatch.com/api/mix/v1/account/account?symbol=ETHUSDT_UMCBL&marginCoin=USDT", "input": ["ETH/USDT:USDT"]}], "setLeverage": [{"description": "Set leverage", "method": "setLeverage", "url": "https://api.coincatch.com/api/mix/v1/account/setLeverage", "input": [5, "ETH/USDT:USDT"], "output": "{\"symbol\":\"ETHUSDT_UMCBL\",\"marginCoin\":\"USDT\",\"leverage\":5}"}], "fetchPosition": [{"description": "Fetch position", "method": "fetchPosition", "url": "https://api.coincatch.com/api/mix/v1/position/singlePosition-v2?symbol=ETHUSDT_UMCBL&marginCoin=USDT", "input": ["ETH/USDT:USDT"]}]}}