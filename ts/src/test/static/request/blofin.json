{"exchange": "blofin", "skipKeys": [], "outputType": "both", "methods": {"fetchOpenOrders": [{"description": "fetch tpsl  orders", "method": "fetchOpenOrders", "url": "https://openapi.blofin.com/api/v1/trade/orders-tpsl-pending", "input": [null, null, null, {"tpsl": true}], "output": null}, {"description": "fetch trigger orders", "method": "fetchOpenOrders", "url": "https://openapi.blofin.com/api/v1/trade/orders-algo-pending?orderType=trigger", "input": [null, null, null, {"trigger": true}], "output": null}], "createOrder": [{"description": "triger order", "method": "createOrder", "url": "https://openapi.blofin.com/api/v1/trade/order-algo", "input": ["LTC/USDT:USDT", "limit", "buy", 1, 90, {"triggerPrice": 91}], "output": "{\"instId\":\"LTC-USDT\",\"side\":\"buy\",\"orderType\":\"trigger\",\"size\":\"1\",\"brokerId\":\"ec6dd3a7dd982d0b\",\"marginMode\":\"cross\",\"orderPrice\":\"90\",\"triggerPrice\":91}"}, {"description": "trigger market order", "method": "createOrder", "url": "https://openapi.blofin.com/api/v1/trade/order-algo", "input": ["ADA/USDT:USDT", "market", "sell", 1, null, {"triggerPrice": "4", "positionSide": "short", "marginMode": "isolated"}], "output": "{\"instId\":\"ADA-USDT\",\"side\":\"sell\",\"orderType\":\"trigger\",\"size\":\"1\",\"brokerId\":\"ec6dd3a7dd982d0b\",\"marginMode\":\"isolated\",\"triggerPrice\":\"4\",\"orderPrice\":\"-1\",\"positionSide\":\"short\"}"}, {"description": "--report", "method": "createOrder", "url": "https://openapi.blofin.com/api/v1/trade/order-algo", "input": ["LTC/USDT:USDT", "limit", "buy", 1, 90, {"triggerPrice": 91}], "output": "{\"instId\":\"LTC-USDT\",\"side\":\"buy\",\"orderType\":\"trigger\",\"size\":\"1\",\"brokerId\":\"ec6dd3a7dd982d0b\",\"marginMode\":\"cross\",\"orderPrice\":\"90\",\"triggerPrice\":91}"}, {"description": "open position with tp+sl", "method": "createOrder", "url": "https://openapi.blofin.com/api/v1/trade/order", "input": ["LTC/USDT:USDT", "market", "buy", 1, null, {"takeProfit": {"triggerPrice": 100}, "stopLoss": {"triggerPrice": 30}}], "output": "{\"instId\":\"LTC-USDT\",\"side\":\"buy\",\"orderType\":\"market\",\"size\":\"1\",\"brokerId\":\"ec6dd3a7dd982d0b\",\"marginMode\":\"cross\",\"slTriggerPrice\":\"30\",\"slOrderPrice\":\"-1\",\"tpTriggerPrice\":\"100\",\"tpOrderPrice\":\"-1\"}"}, {"description": "limit order with clientOrderId", "method": "createOrder", "url": "https://openapi.blofin.com/api/v1/trade/order", "input": ["LTC/USDT:USDT", "limit", "buy", 1, 50, {"clientOrderId": "myorder1"}], "output": "{\"instId\":\"LTC-USDT\",\"side\":\"buy\",\"orderType\":\"limit\",\"size\":\"1\",\"brokerId\":\"ec6dd3a7dd982d0b\",\"marginMode\":\"cross\",\"price\":\"50\",\"clientOrderId\":\"myorder1\"}"}, {"description": "market sell order", "method": "createOrder", "url": "https://openapi.blofin.com/api/v1/trade/order", "input": ["LTC/USDT:USDT", "market", "sell", 1], "output": "{\"instId\":\"LTC-USDT\",\"side\":\"sell\",\"orderType\":\"market\",\"size\":\"1\",\"brokerId\":\"ec6dd3a7dd982d0b\",\"marginMode\":\"cross\"}"}, {"description": "reduce only market buy order", "method": "createOrder", "url": "https://openapi.blofin.com/api/v1/trade/order", "input": ["LTC/USDT:USDT", "market", "buy", 1, null, {"reduceOnly": true}], "output": "{\"instId\":\"LTC-USDT\",\"side\":\"buy\",\"orderType\":\"market\",\"size\":\"1\",\"brokerId\":\"ec6dd3a7dd982d0b\",\"marginMode\":\"cross\",\"reduceOnly\":\"true\"}"}, {"description": "create take profit (type2) order", "method": "createOrder", "url": "https://openapi.blofin.com/api/v1/trade/order-tpsl", "input": ["LTC/USDT:USDT", "market", "sell", 1, null, {"takeProfitPrice": 200}], "output": "{\"instId\":\"LTC-USDT\",\"side\":\"sell\",\"positionSide\":\"net\",\"brokerId\":\"ec6dd3a7dd982d0b\",\"size\":\"1\",\"tpTriggerPrice\":\"200\",\"tpOrderPrice\":\"-1\",\"marginMode\":\"cross\"}"}, {"description": "create stop loss (type2) order", "method": "createOrder", "url": "https://openapi.blofin.com/api/v1/trade/order-tpsl", "input": ["LTC/USDT:USDT", "market", "sell", 1, null, {"stopLossPrice": 50}], "output": "{\"instId\":\"LTC-USDT\",\"side\":\"sell\",\"positionSide\":\"net\",\"brokerId\":\"ec6dd3a7dd982d0b\",\"size\":\"1\",\"slTriggerPrice\":\"50\",\"slOrderPrice\":\"-1\",\"marginMode\":\"cross\"}"}], "cancelOrder": [{"description": "cancel trigger order", "method": "cancelOrder", "url": "https://openapi.blofin.com/api/v1/trade/cancel-algo", "input": [61322087, "LTC/USDT:USDT", {"trigger": true}], "output": "{\"instId\":\"LTC-USDT\",\"algoId\":\"61322087\"}"}, {"description": "cancel limit order", "method": "cancelOrder", "url": "https://openapi.blofin.com/api/v1/trade/cancel-order", "input": ["2075628262", "LTC/USDT:USDT"], "output": "{\"instId\":\"LTC-USDT\",\"orderId\":\"2075628262\"}"}, {"description": "cancel tpsl order", "method": "cancelOrder", "url": "https://openapi.blofin.com/api/v1/trade/cancel-tpsl", "input": ["10811191", "LTC/USDT:USDT", {"tpsl": true}], "output": "[{\"tpslId\":\"10811191\",\"instId\":\"LTC-USDT\"}]"}], "cancelOrders": [{"description": "cancel orders", "method": "cancelOrders", "url": "https://openapi.blofin.com/api/v1/trade/cancel-batch-orders", "input": [["2075704519"], "LTC/USDT:USDT"], "output": "[{\"orderId\":\"2075704519\",\"instId\":\"LTC-USDT\"}]"}], "fetchMyTrades": [{"description": "swap trades", "method": "fetchMyTrades", "url": "https://openapi.blofin.com/api/v1/trade/fills-history?instId=LTC-USDT", "input": ["LTC/USDT:USDT"]}], "fetchClosedOrders": [{"description": "swap closed orders", "method": "fetchClosedOrders", "url": "https://openapi.blofin.com/api/v1/trade/orders-history", "input": []}], "setLeverage": [{"description": "set swap leverage", "method": "setLeverage", "url": "https://openapi.blofin.com/api/v1/account/set-leverage", "input": [5, "BTC/USDT:USDT"], "output": "{\"leverage\":5,\"marginMode\":\"cross\",\"instId\":\"BTC-USDT\"}"}], "fetchLeverage": [{"description": "fetch swap leverage", "method": "fetchLeverage", "url": "https://openapi.blofin.com/api/v1/account/leverage-info?instId=BTC-USDT&marginMode=cross", "input": ["BTC/USDT:USDT"]}], "fetchLeverages": [{"description": "fetch multiple leverages at once", "method": "fetchLeverages", "url": "https://openapi.blofin.com/api/v1/account/batch-leverage-info?instId=BTC-USDT%2CETH-USDT&marginMode=cross", "input": [["BTC/USDT:USDT", "ETH/USDT:USDT"]]}], "closePosition": [{"description": "close short position", "method": "closePosition", "url": "https://openapi.blofin.com/api/v1/trade/close-position", "input": ["LTC/USDT:USDT"], "output": "{\"instId\":\"LTC-USDT\",\"marginMode\":\"cross\"}"}], "fetchPosition": [{"description": "fetch position", "method": "fetchPosition", "url": "https://openapi.blofin.com/api/v1/account/positions?instId=LTC-USDT", "input": ["LTC/USDT:USDT"]}], "fetchPositions": [{"description": "fetch linear positions", "method": "fetchPositions", "url": "https://openapi.blofin.com/api/v1/account/positions", "input": [["LTC/USDT:USDT"]]}], "fetchTrades": [{"description": "fetch trades", "method": "fetchTrades", "url": "https://openapi.blofin.com/api/v1/market/trades?instId=BTC-USDT&limit=5", "input": ["BTC/USDT:USDT", null, 5]}, {"description": "swap fetchTrades", "method": "fetchTrades", "url": "https://openapi.blofin.com/api/v1/market/trades?instId=BTC-USDT", "input": ["BTC/USDT:USDT"]}], "fetchOrderBook": [{"description": "fetch orderbook", "method": "fetchOrderBook", "url": "https://openapi.blofin.com/api/v1/market/books?instId=BTC-USDT&size=5", "input": ["BTC/USDT:USDT", 5]}, {"description": "swap orderbook", "method": "fetchOrderBook", "url": "https://openapi.blofin.com/api/v1/market/books?instId=BTC-USDT&size=50", "input": ["BTC/USDT:USDT"]}], "fetchOHLCV": [{"description": "fetch ohlcv", "method": "fetchOHLCV", "url": "https://openapi.blofin.com/api/v1/market/candles?instId=BTC-USDT&bar=1D&limit=5", "input": ["BTC/USDT:USDT", "1d", null, 5]}, {"description": "swap ohlcv", "method": "fetchOHLCV", "url": "https://openapi.blofin.com/api/v1/market/candles?instId=BTC-USDT&bar=1m&limit=100", "input": ["BTC/USDT:USDT"]}], "fetchTicker": [{"description": "fetch ticker", "method": "fetchTicker", "url": "https://openapi.blofin.com/api/v1/market/tickers?instId=BTC-USDT", "input": ["BTC/USDT:USDT"]}, {"description": "Swap ticker", "method": "fetchTicker", "url": "https://openapi.blofin.com/api/v1/market/tickers?instId=BTC-USDT", "input": ["BTC/USDT:USDT"]}], "fetchTickers": [{"description": "fetch tickers", "method": "fetchTickers", "url": "https://openapi.blofin.com/api/v1/market/tickers", "input": [["BTC/USDT:USDT"]]}, {"description": "swap tickers", "method": "fetchTickers", "url": "https://openapi.blofin.com/api/v1/market/tickers", "input": [["BTC/USDT:USDT", "ETH/USDT:USDT"]]}], "fetchDeposits": [{"description": "fetch deposits", "method": "fetchDeposits", "url": "https://openapi.blofin.com/api/v1/asset/deposit-history", "input": []}], "fetchWithdrawals": [{"description": "fetch withdrawals", "method": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://openapi.blofin.com/api/v1/asset/withdrawal-history", "input": []}], "fetchLedger": [{"description": "fetch USDT ledger", "method": "fetchLedger", "url": "https://openapi.blofin.com/api/v1/asset/bills?currency=USDT", "input": ["USDT"]}], "transfer": [{"description": "transfer from swap to funding", "method": "transfer", "url": "https://openapi.blofin.com/api/v1/asset/transfer", "input": ["USDT", 1, "swap", "funding"], "output": "{\"currency\":\"USDT\",\"amount\":\"1\",\"fromAccount\":\"futures\",\"toAccount\":\"funding\"}"}], "fetchFundingRate": [{"description": "fetch funding rate", "method": "fetchFundingRate", "url": "https://openapi.blofin.com/api/v1/market/funding-rate?instId=LTC-USDT", "input": ["LTC/USDT:USDT"]}, {"description": "fundingRate", "method": "fetchFundingRate", "url": "https://openapi.blofin.com/api/v1/market/funding-rate?instId=BTC-USDT", "input": ["BTC/USDT:USDT"]}], "fetchBalance": [{"description": "fetch balance using accountType", "method": "fetchBalance", "url": "https://openapi.blofin.com/api/v1/asset/balances?accountType=futures", "input": [{"accountType": "futures"}]}, {"description": "fetchBalance", "method": "fetchBalance", "url": "https://openapi.blofin.com/api/v1/account/balance", "input": []}], "fetchMarginMode": [{"description": "Fetch the accounts set margin mode", "method": "fetchMarginMode", "url": "https://openapi.blofin.com/api/v1/account/margin-mode", "input": ["BTC/USDT:USDT"]}], "fetchFundingRateHistory": [{"description": "fundingRateHistory", "method": "fetchFundingRateHistory", "url": "https://openapi.blofin.com/api/v1/market/funding-rate-history?instId=BTC-USDT", "input": ["BTC/USDT:USDT"]}], "setMarginMode": [{"description": "set marginMode", "method": "setMarginMode", "url": "https://openapi.blofin.com/api/v1/account/set-margin-mode", "input": ["isolated"], "output": "{\"marginMode\":\"isolated\"}"}], "fetchPositionMode": [{"description": "fetch positionMode", "method": "fetchPositionMode", "url": "https://openapi.blofin.com/api/v1/account/position-mode", "input": []}], "setPositionMode": [{"description": "set positionMode", "method": "setPositionMode", "url": "https://openapi.blofin.com/api/v1/account/set-position-mode", "input": [true], "output": "{\"positionMode\":\"long_short_mode\"}"}]}}