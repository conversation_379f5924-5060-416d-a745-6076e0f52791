{"exchange": "binance", "skipKeys": ["signature", "timestamp", "recvWindow", "newClientOrderId", "newClientStrategyId"], "outputType": "u<PERSON><PERSON><PERSON>", "options": {"leverageBrackets": {"BTC/USDT:USDT": [["0", "0.004"], ["50000", "0.005"], ["250000", "0.01"], ["3000000", "0.025"], ["20000000", "0.05"], ["40000000", "0.1"], ["100000000", "0.125"], ["120000000", "0.15"], ["200000000", "0.25"], ["300000000", "0.5"]], "ETH/USDT:USDT": [["0", "0.005"], ["100000", "0.0065"], ["250000", "0.01"], ["2000000", "0.02"], ["15000000", "0.05"], ["30000000", "0.1"], ["60000000", "0.125"], ["80000000", "0.15"], ["100000000", "0.25"], ["150000000", "0.5"]], "LTC/USDT:USDT": [["0", "0.0065"], ["10000", "0.01"], ["50000", "0.02"], ["250000", "0.05"], ["1000000", "0.1"], ["2000000", "0.125"], ["5000000", "0.15"], ["10000000", "0.25"]], "ADA/USDT:USDT": [["0", "0.0065"], ["10000", "0.01"], ["50000", "0.02"], ["250000", "0.05"], ["1000000", "0.1"], ["2000000", "0.125"], ["5000000", "0.15"], ["10000000", "0.25"]], "BTC/USD:BTC": [["0", "0.004"], ["5", "0.005"], ["10", "0.01"], ["20", "0.025"], ["50", "0.05"], ["100", "0.1"], ["200", "0.125"], ["400", "0.15"], ["1000", "0.25"], ["1500", "0.5"]]}}, "methods": {"createOrder": [{"description": "spot +selfTradePrevention +iceberg", "method": "createOrder", "url": "https://api.binance.com/api/v3/order", "input": ["SOL/USDT", "limit", "buy", 0.05, 241, {"selfTradePrevention": "expire_maker", "icebergAmount": 0.03}], "output": "timestamp=1737635122583&symbol=SOLUSDT&side=BUY&newClientOrderId=x-R4BD3S82a715e5f92ae442d8989388&newOrderRespType=FULL&type=LIMIT&quantity=0.05&price=241&timeInForce=GTC&selfTradePreventionMode=EXPIRE_MAKER&icebergQty=0.03&recvWindow=10000&signature=5994e0b818abb5289cc35adb708b61926ba09da4f80b0ca451f6fa3eb4a48cdc"}, {"description": "Spot market buy order", "method": "createOrder", "url": "https://testnet.binance.vision/api/v3/order", "input": ["LTC/USDT", "market", "buy", 0.2, 50], "output": "timestamp=1698772556546&symbol=LTCUSDT&side=BUY&newClientOrderId=x-R4BD3S8265d26698ad954db1b3fee5&newOrderRespType=FULL&type=MARKET&quoteOrderQty=10&recvWindow=10000&signature=bb8a423f6cfa0c0aa85c1234f514ff0fdad73b41921f31d0f4b9d5a24045ea8b"}, {"description": "Spot limit buy order", "method": "createOrder", "url": "https://testnet.binance.vision/api/v3/order", "input": ["LTC/USDT", "limit", "buy", 0.2, 50], "output": "timestamp=1698772601904&symbol=LTCUSDT&side=BUY&newClientOrderId=x-R4BD3S826527eb0fd34e445cba9d94&newOrderRespType=FULL&type=LIMIT&quantity=0.2&price=50&timeInForce=GTC&recvWindow=10000&signature=b1caba89e3a305463b03d9bd73e0b34a2117f3b3949cdf065d29837fa845b4b0"}, {"description": "Spot limit buy with postOnly", "method": "createOrder", "url": "https://api.binance.com/api/v3/order", "input": ["LTC/USDT", "limit", "buy", 0.2, 50, {"postOnly": true}], "output": "timestamp=1707174812474&symbol=LTCUSDT&side=BUY&newOrderRespType=FULL&newClientOrderId=x-R4BD3S8281350e594e8f4383a36f19&type=LIMIT_MAKER&quantity=0.2&price=50&recvWindow=10000&signature=4e01e6b644312f26fce0be6524ed2eda08079dec6b6f1f2670a685362e39c6f1"}, {"description": "spot order with weird amount", "method": "createOrder", "url": "https://testnet.binance.vision/api/v3/order", "input": ["BTC/USDT", "limit", "buy", 0.0007856509959147719, 55000], "output": "timestamp=1714835089402&symbol=BTCUSDT&side=BUY&newClientOrderId=x-R4BD3S8248a31eddb97c4bd496a6b0&newOrderRespType=FULL&type=LIMIT&quantity=0.00078&price=55000&timeInForce=GTC&recvWindow=10000&signature=50aeda30bb2261723ead2d178f4fa1c8a270af2ec9d787cf4ae8cfe44c11516f"}, {"description": "spot order with weird price/amount", "method": "createOrder", "url": "https://testnet.binance.vision/api/v3/order", "input": ["LTC/USDT", "limit", "buy", 0.4234234234, 55.324242343], "output": "timestamp=1714922933332&symbol=LTCUSDT&side=BUY&newClientOrderId=x-R4BD3S82bf9e78bb5d9948fa89b20d&newOrderRespType=FULL&type=LIMIT&quantity=0.423&price=55.32&timeInForce=GTC&recvWindow=10000&signature=84f7265b067e1fd1a244e6b1af168109652359c1204942330a6ac77ba7f7c93e"}, {"description": "spot limit buy with weird price/amount", "method": "createOrder", "url": "https://testnet.binancefuture.com/fapi/v1/order", "input": ["LTC/USDT:USDT", "limit", "buy", 0.4234234234, 55.324242343], "output": "timestamp=1714923006076&symbol=LTCUSDT&side=BUY&newClientOrderId=x-xcKtGhcu8cef5fd759c2493f8eff0b&newOrderRespType=RESULT&type=LIMIT&quantity=0.423&price=55.32&timeInForce=GTC&recvWindow=10000&signature=e0c49a6da870de7264a84603c2a5f33bb5d8e24b8935cb4fdb303ee2dbf39d79"}, {"description": "swap limit buy with weird price/amount", "method": "createOrder", "url": "https://testnet.binancefuture.com/fapi/v1/order", "input": ["LTC/USDT:USDT", "limit", "buy", 0.132434234, 50.234234234], "output": "timestamp=1718723127466&symbol=LTCUSDT&side=BUY&newClientOrderId=x-xcKtGhcu29733e7109944f4b825049&newOrderRespType=RESULT&type=LIMIT&quantity=0.132&price=50.23&timeInForce=GTC&recvWindow=10000&signature=31ec4a15ca25663539a8d29991b663a90d3354ed8cad3e8360e1aa92677d5c97"}, {"description": "Swap market buy order", "method": "createOrder", "url": "https://testnet.binancefuture.com/fapi/v1/order", "input": ["LTC/USDT:USDT", "market", "buy", 0.1], "output": "timestamp=1698772686486&symbol=LTCUSDT&side=BUY&newClientOrderId=x-xcKtGhcud0e56169437e41d2a50414&newOrderRespType=RESULT&type=MARKET&quantity=0.1&recvWindow=10000&signature=a703d0332fd54447dc9439dc60ddcc515d170c2357b12041504f5eb228002c09"}, {"description": "Swap limit buy order", "method": "createOrder", "url": "https://testnet.binancefuture.com/fapi/v1/order", "input": ["LTC/USDT:USDT", "limit", "buy", 0.1, 55], "output": "timestamp=1698772722784&symbol=LTCUSDT&side=BUY&newClientOrderId=x-xcKtGhcu857a045c78a148bda82a3b&newOrderRespType=RESULT&type=LIMIT&quantity=0.1&price=55&timeInForce=GTC&recvWindow=10000&signature=ea0c2c8986c05546309cfb5248febc8a551c1a7ccf7252305e26bb60e41a8d60"}, {"description": "Spot limit buy with triggerPrice", "method": "createOrder", "url": "https://api.binance.com/api/v3/order", "input": ["LTC/USDT", "limit", "buy", 0.2, 50, {"triggerPrice": 100}], "output": "timestamp=1699113377107&symbol=LTCUSDT&side=BUY&newClientOrderId=x-R4BD3S828025d8ae9f754437aa1bde&newOrderRespType=FULL&type=STOP_LOSS_LIMIT&quantity=0.2&price=50&timeInForce=GTC&stopPrice=100&recvWindow=10000&signature=FFsWE8MefjyN%2BBkm0w4KE4dRJh%2FOeJDbH4nOjhFS7rbmaGb64q9cK2RmRbgLKqMbDcPmgy752h7YnjqhF7wWixXEZmJR0Ks3ap%2BhvhAEr4Cd1bPJ2buEe316rXIHgPiDQBpDbsbipEoX2dQnUdjxGItAgyTVSThxqk0t8BDrqkHlnwv8JF9Yp1ekwFccXYBM5jMz09feygyLPzyiK3bqjPndwohxCLoL%2B6wp0x%2FTM0Vm39XTvOAPUj88FF67C76sFlVReIjzwo94xOj%2BHzynGZs7apVLV227P434MARwyKQM9XsSQ%2FMsSakoxZjOrErH2iwWlsJL60C7HcPl%2B%2BZD%2BQ%3D%3D"}, {"description": "Swap limit buy with triggerPrice", "method": "createOrder", "url": "https://testnet.binancefuture.com/fapi/v1/order", "input": ["LTC/USDT:USDT", "limit", "buy", 0.2, 50, {"triggerPrice": 100}], "output": "timestamp=1699113425660&symbol=LTCUSDT&side=BUY&newClientOrderId=x-xcKtGhcu0a6b33edf43043799149a0&newOrderRespType=RESULT&type=STOP&quantity=0.2&price=50&stopPrice=100&recvWindow=10000&signature=dcbd99666b985518f0bc818d2fdc9c7215f4607b044ad921a025fec70f41ae61"}, {"description": "Spot limit buy with test endpoint", "method": "createOrder", "url": "https://api.binance.com/api/v3/order/test", "input": ["LTC/USDT", "limit", "buy", 0.2, 50, {"test": true}], "output": "timestamp=1699113722660&symbol=LTCUSDT&side=BUY&newClientOrderId=x-R4BD3S82dbdbef155f0b479ca03ac1&newOrderRespType=FULL&type=LIMIT&quantity=0.2&price=50&timeInForce=GTC&recvWindow=10000&signature=RAxM3BDlCxN9yJtKYKhyrJ0dOb7ng6o5Iy1Pz6I9snLDHRQVNuv9%2FgnWWfsUayfILjZQO7ns3VeyqGfiXBlGXa5eap37%2BHXjcYBA9Jc6OCXAKDtNmcdkFB9QvV0bG7lO%2BIMx6nQAgUILod%2FlVWd1MJSOVYlAoDH7N5aiHtrcImoUT4NRJkT31E99yT7W7VIqvTD3AcFUodgaajB7G0oBTqp69w9uyUJEvDFG6RisU77Zeuzcy7sYdXpVueTycreAjQg225F1WQNSDClOJKMDhJ27d5akwOj%2Flxo6pRjT21Jr7G3GGzceAiHXKgSSB%2Fgu2jCe48hnr8C92YVqfp2LyQ%3D%3D"}, {"description": "Spot margin (cross) limit buy", "method": "createOrder", "url": "https://api.binance.com/sapi/v1/margin/order", "input": ["LTC/USDT", "limit", "buy", 0.2, 50, {"marginMode": "cross"}], "output": "timestamp=1699113838137&symbol=LTCUSDT&side=BUY&newClientOrderId=x-R4BD3S8277d937b569914d1bb5f784&newOrderRespType=FULL&type=LIMIT&quantity=0.2&price=50&timeInForce=GTC&recvWindow=10000&signature=0e12b57b83c758aceb6d88c4950c1259f41cc0b86d7e6259fcdb3202cd3d3c32"}, {"description": "Swap limit sell", "method": "createOrder", "url": "https://testnet.binancefuture.com/fapi/v1/order", "input": ["LTC/USDT:USDT", "limit", "sell", 0.2, 100], "output": "timestamp=1699114065223&symbol=LTCUSDT&side=SELL&newClientOrderId=x-xcKtGhcu6e4e96867ac345ee9151b9&newOrderRespType=RESULT&type=LIMIT&quantity=0.2&price=100&timeInForce=GTC&recvWindow=10000&signature=057d2e77971840b5e2726b88a7a1e2fc740c032437bc4ea38c89e6d1c2968b9a"}, {"description": "Swap limit sell with takeProfitPrice (type 2)", "method": "createOrder", "url": "https://testnet.binancefuture.com/fapi/v1/order", "input": ["LTC/USDT:USDT", "limit", "sell", 0.2, 100, {"takeProfitPrice": 105}], "output": "timestamp=1699285858348&symbol=LTCUSDT&side=SELL&newClientOrderId=x-xcKtGhcu2784a323f70a4bdf8b1725&newOrderRespType=RESULT&type=TAKE_PROFIT&quantity=0.2&price=100&stopPrice=105&recvWindow=10000&signature=6434d52fc670894917cac2b03407eac0646f81ce4bcff3b640c11d4c8c2d4ab1"}, {"description": "Swap limit buy with stopLossPrice (type 2)", "method": "createOrder", "url": "https://testnet.binancefuture.com/fapi/v1/order", "input": ["LTC/USDT:USDT", "limit", "sell", 0.2, 49, {"stopLossPrice": 50}], "output": "timestamp=1699285907701&symbol=LTCUSDT&side=SELL&newClientOrderId=x-xcKtGhcuc8a51b6fd0a344069c2703&newOrderRespType=RESULT&type=STOP&quantity=0.2&price=49&stopPrice=50&recvWindow=10000&signature=4a09fa15b8c91f1215f7175b163080853291f5e316bd25025ddefd5b5688508a"}, {"description": "Swap limit buy with reduce only (closing short position)", "method": "createOrder", "url": "https://testnet.binancefuture.com/fapi/v1/order", "input": ["LTC/USDT:USDT", "limit", "buy", 0.2, 60, {"reduceOnly": true}], "output": "timestamp=1699380630765&symbol=LTCUSDT&side=BUY&newClientOrderId=x-xcKtGhcuad6ae547a99e48b686c911&newOrderRespType=RESULT&type=LIMIT&quantity=0.2&price=60&timeInForce=GTC&reduceOnly=true&recvWindow=10000&signature=3d99de3afdd8a6ad846d201fe4f446b75dd956db59e986db345d453b79b7bea2"}, {"description": "Swap limit buy with postOnly", "method": "createOrder", "url": "https://testnet.binancefuture.com/fapi/v1/order", "input": ["LTC/USDT:USDT", "limit", "buy", 0.2, 50, {"postOnly": true}], "output": "timestamp=1699380695835&symbol=LTCUSDT&side=BUY&newClientOrderId=x-xcKtGhcu392bafca1fb740208ee8d3&newOrderRespType=RESULT&type=LIMIT&quantity=0.2&price=50&timeInForce=GTX&recvWindow=10000&signature=27dfce7c02d210d4d88f029e34550f153283e849b34bbf80aebbc7f860937b84"}, {"description": "spot isolated", "method": "createOrder", "url": "https://api.binance.com/sapi/v1/margin/order", "input": ["LTC/USDT", "limit", "buy", 0.15, 40, {"marginMode": "isolated"}], "output": "timestamp=1726126410920&symbol=LTCUSDT&side=BUY&newClientOrderId=x-R4BD3S829ee307a9c5b6406893c9fd&isIsolated=true&newOrderRespType=FULL&type=LIMIT&quantity=0.15&price=40&timeInForce=GTC&recvWindow=10000&signature=cdda411d70279bbc920d218fcb6c772c7e0d7114041854c4bf611c1bfb001eb2"}, {"description": "spot cross", "method": "createOrder", "url": "https://api.binance.com/sapi/v1/margin/order", "input": ["LTC/USDT", "limit", "buy", 0.15, 40, {"marginMode": "cross"}], "output": "timestamp=1726126711973&symbol=LTCUSDT&side=BUY&newClientOrderId=x-R4BD3S82ad18e3768ed94e88bde664&newOrderRespType=FULL&type=LIMIT&quantity=0.15&price=40&timeInForce=GTC&recvWindow=10000&signature=c71bd2e5740c2f2d8f4eb3c8530c933e94a5d718cc286687cff398566533b7da"}, {"description": "Swap trailingPercent order", "method": "createOrder", "url": "https://testnet.binancefuture.com/fapi/v1/order", "input": ["BTC/USDT:USDT", "market", "sell", 0.1, null, {"trailingPercent": 5, "reduceOnly": true}], "output": "timestamp=1704512493514&symbol=BTCUSDT&side=SELL&callbackRate=5&newClientOrderId=x-xcKtGhcufdcb378c830d451392c9a2&newOrderRespType=RESULT&type=TRAILING_STOP_MARKET&quantity=0.1&reduceOnly=true&recvWindow=10000&signature=381024e31ce5868d69dd3e12668b9b47514c1211e7d0021a7939cb26d07f679a"}, {"description": "Linear swap portfolio margin limit buy order", "method": "createOrder", "url": "https://papi.binance.com/papi/v1/um/order", "input": ["BTC/USDT:USDT", "limit", "buy", 0.01, 35000, {"portfolioMargin": true}], "output": "timestamp=1707112464420&symbol=BTCUSDT&newOrderRespType=RESULT&side=BUY&newClientOrderId=x-xcKtGhcu0b020e257d4943d49c0ffe&type=LIMIT&quantity=0.01&price=35000&timeInForce=GTC&recvWindow=10000&signature=6d8b1ae407634704c7d7d26cbb62f0f438a09e9863b81a8a89a24461c4997808"}, {"description": "Inverse swap portfolio margin limit buy order", "method": "createOrder", "url": "https://papi.binance.com/papi/v1/cm/order", "input": ["ETH/USD:ETH", "limit", "buy", 1, 2000, {"portfolioMargin": true}], "output": "timestamp=1707112388003&symbol=ETHUSD_PERP&newOrderRespType=RESULT&side=BUY&newClientOrderId=x-xcKtGhcu69273341d6114bd8b800aa&type=LIMIT&quantity=1&price=2000&timeInForce=GTC&recvWindow=10000&signature=4e4f549a6358aa1731060e825069022389780706305a60eaa58fbbf0c4a7d03f"}, {"description": "Linear swap portfolio margin conditional limit buy order", "method": "createOrder", "url": "https://papi.binance.com/papi/v1/um/conditional/order", "input": ["BTC/USDT:USDT", "limit", "buy", 0.01, 35000, {"portfolioMargin": true, "triggerPrice": 45000}], "output": "timestamp=1707112624772&symbol=BTCUSDT&newOrderRespType=RESULT&side=BUY&newClientStrategyId=x-xcKtGhcu27f109953d6e4dc0974006&strategyType=STOP&quantity=0.01&price=35000&stopPrice=45000&recvWindow=10000&signature=2cacd678b9199f4f75ed8f7eb48cc35afc648346fc436c3808983baf983323ab"}, {"description": "Inverse swap portfolio margin conditional limit buy order", "method": "createOrder", "url": "https://papi.binance.com/papi/v1/cm/conditional/order", "input": ["ETH/USD:ETH", "limit", "buy", 1, 2000, {"portfolioMargin": true, "triggerPrice": 3000}], "output": "timestamp=1707113097689&symbol=ETHUSD_PERP&newOrderRespType=RESULT&side=BUY&newClientStrategyId=x-xcKtGhcuc6b86f054bb34933850739&strategyType=STOP&quantity=1&price=2000&stopPrice=3000&recvWindow=10000&signature=aa5ec5fa5eb51372fa68597242bc2ddc31a32e9be5c084f00c2f6999b31cad2b"}, {"description": "Spot margin portfolio margin limit buy order", "method": "createOrder", "url": "https://papi.binance.com/papi/v1/margin/order", "input": ["BTC/USDT", "limit", "buy", 0.001, 35000, {"portfolioMargin": true, "marginMode": "cross"}], "output": "timestamp=1707113537828&symbol=BTCUSDT&side=BUY&newOrderRespType=RESULT&newClientOrderId=x-R4BD3S82e9ef29d8346440f0b28b86&type=LIMIT&quantity=0.001&price=35000&timeInForce=GTC&recvWindow=10000&signature=08d71bf9a60d5184cc256964f1175179c813d11e038718c0d64d5cc5b982f2f5"}, {"description": "Spot margin portfolio margin limit buy order with only portfolioMargin set to true", "method": "createOrder", "url": "https://papi.binance.com/papi/v1/margin/order", "input": ["BTC/USDT", "limit", "buy", 0.0001, 50000, {"portfolioMargin": true}], "output": "timestamp=1712974976336&symbol=BTCUSDT&side=BUY&newOrderRespType=RESULT&newClientOrderId=x-R4BD3S828b7395b12d0846afa4643c&type=LIMIT&quantity=0.0001&price=50000&timeInForce=GTC&recvWindow=10000&signature=bf3748a7adae072d411444d031d45b17ba8c8febf47bb0b5f4236e33b963b49f"}, {"description": "swap order with priceMatch", "method": "createOrder", "url": "https://testnet.binancefuture.com/fapi/v1/order", "input": ["LTC/USDT:USDT", "limit", "buy", 0.2, null, {"priceMatch": "OPPONENT"}], "output": "timestamp=1708360882808&symbol=LTCUSDT&side=BUY&newOrderRespType=RESULT&newClientOrderId=x-xcKtGhcu6f60e2c1e1ed4166838709&type=LIMIT&quantity=0.2&timeInForce=GTC&priceMatch=OPPONENT&recvWindow=10000&signature=b73ae983cc2d4995d48e98841152c802ba0b420ee79fc501bde0b6eeee4817ab"}, {"description": "spot create a trailing limit stop loss order", "method": "createOrder", "url": "https://testnet.binance.vision/api/v3/order", "input": ["BTC/USDT", "limit", "sell", 0.1, 55000, {"trailingPercent": 5, "stopLossOrTakeProfit": "stopLoss"}], "output": "timestamp=1715064034670&symbol=BTCUSDT&side=SELL&trailingDelta=500&newClientOrderId=x-R4BD3S827b685798989f41d5802a06&newOrderRespType=FULL&type=STOP_LOSS_LIMIT&quantity=0.1&price=55000&timeInForce=GTC&recvWindow=10000&signature=57d9051c3a5ef532f6124922f104bfd35e73b553b8e46d54598a3fdbb9463cb5"}, {"description": "spot create a trailing limit take profit order", "method": "createOrder", "url": "https://testnet.binance.vision/api/v3/order", "input": ["BTC/USDT", "limit", "sell", 0.1, 70000, {"trailingPercent": 5, "stopLossOrTakeProfit": "takeProfit"}], "output": "timestamp=1715064136268&symbol=BTCUSDT&side=SELL&trailingDelta=500&newClientOrderId=x-R4BD3S829874c25a35ce4766a6c7bc&newOrderRespType=FULL&type=TAKE_PROFIT_LIMIT&quantity=0.1&price=70000&timeInForce=GTC&recvWindow=10000&signature=221d429c7db5a26495d5c0d9287f6e6a08fb59f45b3f71c3c6bfe8e319e35341"}, {"description": "spot portfolioMargin postOnly order", "method": "createOrder", "url": "https://papi.binance.com/papi/v1/margin/order", "input": ["LTC/USDT", "limit", "buy", 0.25, 50.1, {"portfolioMargin": true, "postOnly": true}], "output": "timestamp=1716470235123&symbol=LTCUSDT&side=BUY&newClientOrderId=x-R4BD3S8270e7c9840bb6463fb08c23&newOrderRespType=RESULT&type=LIMIT_MAKER&quantity=0.25&price=50.1&recvWindow=10000&signature=444e202f5d5916737d7fb6386199d06e8dcf86fae0605b631f4cf170497d98e5"}, {"description": "swap portfolioMargin postOnly order", "method": "createOrder", "url": "https://papi.binance.com/papi/v1/um/order", "input": ["LTC/USDT:USDT", "limit", "buy", 0.41, 50, {"portfolioMargin": true, "postOnly": true, "positionSide": "LONG"}], "output": "timestamp=1716470360382&symbol=LTCUSDT&side=BUY&newClientOrderId=x-xcKtGhcu08549492ddff403d8a6466&timeInForce=GTX&newOrderRespType=RESULT&type=LIMIT&quantity=0.41&price=50&positionSide=LONG&recvWindow=10000&signature=e9499c199ddedd7dec114f6b0491f6e41789b0fe0e6130b539b5ad6f9f0a549b"}, {"description": "swap portfolioMargin postOnly order using options", "method": "createOrder", "url": "https://papi.binance.com/papi/v1/um/order", "options": {"portfolioMargin": true}, "input": ["LTC/USDT:USDT", "limit", "buy", 0.41, 50, {"postOnly": true, "positionSide": "LONG"}], "output": "timestamp=1716470360382&symbol=LTCUSDT&side=BUY&newClientOrderId=x-xcKtGhcu08549492ddff403d8a6466&timeInForce=GTX&newOrderRespType=RESULT&type=LIMIT&quantity=0.41&price=50&positionSide=LONG&recvWindow=10000&signature=e9499c199ddedd7dec114f6b0491f6e41789b0fe0e6130b539b5ad6f9f0a549b"}, {"description": "swap limit bbo order", "method": "createOrder", "url": "https://testnet.binancefuture.com/fapi/v1/order", "input": ["LTC/USDT:USDT", "limit", "buy", 0.1, null, {"priceMatch": "OPPONENT"}], "output": "timestamp=1717843341137&symbol=LTCUSDT&side=BUY&newClientOrderId=x-xcKtGhcud3bc1226a44e4652b326a8&newOrderRespType=RESULT&type=LIMIT&quantity=0.1&timeInForce=GTC&priceMatch=OPPONENT&recvWindow=10000&signature=0683954e1707e361a26c80395574bbf38829fb35c4a0fa634c7a56f7f2617843"}, {"description": "swap trigger bbo order", "method": "createOrder", "url": "https://testnet.binancefuture.com/fapi/v1/order", "input": ["LTC/USDT:USDT", "limit", "buy", 0.1, null, {"triggerPrice": 100, "priceMatch": "OPPONENT"}], "output": "timestamp=1717843608802&symbol=LTCUSDT&side=BUY&newClientOrderId=x-xcKtGhcu918489f4e5334421a68067&newOrderRespType=RESULT&type=STOP&quantity=0.1&stopPrice=100&priceMatch=OPPONENT&recvWindow=10000&signature=1c87a4c108577d3fee3b3081295424c2ffeaf0454ca3cbff46ed7b84a1d62671"}, {"description": "swap hedged reduceOnly market sell order", "method": "createOrder", "url": "https://testnet.binancefuture.com/fapi/v1/order", "input": ["BTC/USDT:USDT", "market", "sell", 0.01, null, {"hedged": true, "reduceOnly": true}], "output": "timestamp=1727850641135&symbol=BTCUSDT&side=SELL&newClientOrderId=x-xcKtGhcua9d0397424bb4452a90901&newOrderRespType=RESULT&type=MARKET&quantity=0.01&positionSide=LONG&recvWindow=10000&signature=1fc5d67df418e595a5041c8e200644689d14c4de3bae2e997cff65571dbd4fe2"}, {"description": "swap hedged reduceOnly market buy order", "method": "createOrder", "url": "https://testnet.binancefuture.com/fapi/v1/order", "input": ["BTC/USDT:USDT", "market", "buy", 0.01, null, {"hedged": true, "reduceOnly": true}], "output": "timestamp=1727850728450&symbol=BTCUSDT&side=BUY&newClientOrderId=x-xcKtGhcu7115017eb3284b84905830&newOrderRespType=RESULT&type=MARKET&quantity=0.01&positionSide=SHORT&recvWindow=10000&signature=5709d8de55d46bce7ef997580b9a223d3145772c1d5c22160b53e4539246b9b2"}], "createOrders": [{"description": "Swap createOrders", "method": "createOrders", "url": "https://testnet.binancefuture.com/fapi/v1/batchOrders", "input": [[{"symbol": "LTC/USDT:USDT", "amount": 0.1, "side": "buy", "type": "limit", "price": 60}, {"symbol": "LTC/USDT:USDT", "amount": 0.11, "side": "buy", "type": "limit", "price": 61}]], "output": "timestamp=1699382693405&batchOrders=[{\"symbol\":\"LTCUSDT\",\"side\":\"BUY\",\"newClientOrderId\":\"x-xcKtGhcub371e14dda9e4fda804421\",\"newOrderRespType\":\"RESULT\",\"type\":\"LIMIT\",\"quantity\":\"0.1\",\"price\":\"60\",\"timeInForce\":\"GTC\"},{\"symbol\":\"LTCUSDT\",\"side\":\"BUY\",\"newClientOrderId\":\"x-xcKtGhcu2b5cffec484a42138cdf8e\",\"newOrderRespType\":\"RESULT\",\"type\":\"LIMIT\",\"quantity\":\"0.11\",\"price\":\"61\",\"timeInForce\":\"GTC\"}]&recvWindow=10000&signature=ce06633e6e16101cf7b4c3fd0f6a1afa3901206050382fafae81deddc1867f92"}], "createMarketOrderWithCost": [{"description": "Spot create market order with cost", "method": "createMarketOrderWithCost", "url": "https://testnet.binance.vision/api/v3/order", "input": ["BTC/USDT", "buy", 10], "output": "timestamp=1702677177092&symbol=BTCUSDT&side=BUY&newClientOrderId=x-R4BD3S82bbdbdf90c28b4c2a9dd885&newOrderRespType=FULL&type=MARKET&quoteOrderQty=10&recvWindow=10000&signature=e36681dea6e4baa6049a95b440fb76eb82c7b7a1906a8595ef8a6844273e0ef2"}], "createMarketBuyOrderWithCost": [{"description": "Spot create market buy order with cost", "method": "createMarketBuyOrderWithCost", "url": "https://testnet.binance.vision/api/v3/order", "input": ["BTC/USDT", 15], "output": "timestamp=1702677315563&symbol=BTCUSDT&side=BUY&newClientOrderId=x-R4BD3S82166697aa19d7484fb68378&newOrderRespType=FULL&type=MARKET&quoteOrderQty=15&recvWindow=10000&signature=64f817d988900608171a22a906a68da7ef58b401b8ec6514c0e141f2c262c8fa"}, {"description": "market buy order with weird cost", "method": "createMarketBuyOrderWithCost", "url": "https://api.binance.com/api/v3/order", "input": ["LTC/USDT", 5.3243423423423], "output": "timestamp=1718723820863&symbol=LTCUSDT&side=BUY&newClientOrderId=x-R4BD3S82844b9678d33a487c9d1a81&newOrderRespType=FULL&type=MARKET&quoteOrderQty=5.32&recvWindow=10000&signature=bae9de654740b296d76fcea43e907a64c3339a76b924f797dbc4ce54b21e832f"}], "createMarketSellOrderWithCost": [{"description": "Spot create market sell order with cost", "method": "createMarketSellOrderWithCost", "url": "https://testnet.binance.vision/api/v3/order", "input": ["BTC/USDT", 15], "output": "timestamp=1702677379657&symbol=BTCUSDT&side=SELL&newClientOrderId=x-R4BD3S82e9114f80b9124f96817cc1&newOrderRespType=FULL&type=MARKET&quoteOrderQty=15&recvWindow=10000&signature=c71dc764642f5e6b7a96d04b7d820058ec204557ec43ce64ff102d25df4423b8"}, {"description": "market sell with weird cost", "method": "createMarketSellOrderWithCost", "url": "https://api.binance.com/api/v3/order", "input": ["LTC/USDT", 5.3243423423423], "output": "timestamp=1718723859404&symbol=LTCUSDT&side=SELL&newClientOrderId=x-R4BD3S8211e624263fc64745ba2215&newOrderRespType=FULL&type=MARKET&quoteOrderQty=5.32&recvWindow=10000&signature=370d368512813304333bd7510e6ae62d89ba877ed6a829ac5595a9c26b0ba265"}], "editOrder": [{"description": "edit spot order", "method": "editOrder", "url": "https://testnet.binance.vision/api/v3/order/cancelReplace", "input": [2163811, "LTC/USDT", "limit", "buy", 0.25, 51], "output": "timestamp=1706094359481&symbol=LTCUSDT&side=BUY&type=LIMIT&newClientOrderId=x-R4BD3S82230eac64ed3d47c284a92b&newOrderRespType=FULL&quantity=0.25&price=51&timeInForce=GTC&cancelReplaceMode=STOP_ON_FAILURE&cancelOrderId=2163811&recvWindow=10000&signature=787c8bd2fa00acfcabaf5edd9e341a652db4a1549791c6114555838511217b94"}, {"description": "edit swap order", "method": "editOrder", "url": "https://testnet.binancefuture.com/fapi/v1/order", "input": [661753725, "LTC/USDT:USDT", "limit", "buy", 0.25, 51], "output": "timestamp=1706094442270&symbol=LTCUSDT&side=BUY&orderId=661753725&quantity=0.25&price=51&recvWindow=10000&signature=8a0439270bdbfc88a86853ea64972b5e54e90d506645a5b5efef8121b101f302"}], "editOrders": [{"description": "Swap editOrders", "method": "editOrders", "url": "https://testnet.binancefuture.com/fapi/v1/batchOrders", "input": [[{"id": "556886677", "symbol": "LTC/USDT:USDT", "amount": 0.1, "side": "buy", "type": "limit", "price": 60}, {"id": "556886678", "symbol": "LTC/USDT:USDT", "amount": 0.11, "side": "buy", "type": "limit", "price": 61}]], "output": "timestamp=1699382693405&batchOrders=[{\"orderId\":\"556886677\",\"symbol\":\"LTCUSDT\",\"side\":\"BUY\",\"quantity\":\"0.1\",\"price\":\"60\"},{\"orderId\":\"556886678\",\"symbol\":\"LTCUSDT\",\"side\":\"BUY\",\"quantity\":\"0.11\",\"price\":\"61\"}]&recvWindow=10000&signature=ce06633e6e16101cf7b4c3fd0f6a1afa3901206050382fafae81deddc1867f92"}], "cancelOrder": [{"description": "Swap cancelOrder", "method": "cancelOrder", "url": "https://testnet.binancefuture.com/fapi/v1/order?timestamp=1699382897679&symbol=LTCUSDT&orderId=652509009&recvWindow=10000&signature=02d0287d26b9b0603902e299840941f6738f9fbb3f497390b29d7077c42c318b", "input": [652509009, "LTC/USDT:USDT"]}, {"description": "Spot cancelOrder", "method": "cancelOrder", "url": "https://testnet.binance.vision/api/v3/order?timestamp=1699384031574&symbol=LTCUSDT&orderId=464950&recvWindow=10000&signature=548084e744d32adc2172c498ead0f621cd1920ac788ff7b4038fffa5dd139d42", "input": [464950, "LTC/USDT"]}, {"description": "Swap inverse cancelOrder", "method": "cancelOrder", "url": "https://dapi.binance.com/dapi/v1/order?timestamp=1699382897679&symbol=BTCUSD_PERP&orderId=652509009&recvWindow=10000&signature=02d0287d26b9b0603902e299840941f6738f9fbb3f497390b29d7077c42c318b", "input": [652509009, "BTC/USD:BTC"]}, {"description": "Option cancelOrder", "method": "cancelOrder", "url": "https://eapi.binance.com/eapi/v1/order?timestamp=1699382897679&symbol=ETH-231229-800-C&orderId=652509009&recvWindow=10000&signature=02d0287d26b9b0603902e299840941f6738f9fbb3f497390b29d7077c42c318b", "input": [652509009, "ETH/USDT:USDT-231229-800-C"]}, {"description": "cancel swap order with clientOrderId", "method": "cancelOrder", "url": "https://testnet.binancefuture.com/fapi/v1/order?timestamp=1703181445987&symbol=BTCUSDT&origClientOrderId=myswap&recvWindow=10000&signature=169048840e9612fc90a33b39894e882fc3f73950a02374b544ddecb344467929", "input": ["", "BTC/USDT:USDT", {"clientOrderId": "myswap"}]}, {"description": "Spot margin portfolio margin cancel order", "method": "cancelOrder", "url": "https://papi.binance.com/papi/v1/margin/order?timestamp=1707259954072&symbol=BTCUSDT&orderId=24711539682&recvWindow=10000&signature=a4d929126b279d07a6bf41075e0ee173da06b56414bd46826e146baeff2b1f95", "input": [24711539682, "BTC/USDT", {"portfolioMargin": true, "marginMode": "cross"}]}, {"description": "Spot margin portfolio margin cancel order with only portfolioMargin set to true", "method": "cancelOrder", "url": "https://papi.binance.com/papi/v1/margin/order?timestamp=1712975069995&symbol=BTCUSDT&orderId=26447758109&recvWindow=10000&signature=5dc7c88abd58e4adbdc8153a19e33dc189e402184f42694de7a5ce3febfac2e8", "input": ["26447758109", "BTC/USDT", {"portfolioMargin": true}]}, {"description": "Linear swap portfolio margin cancel order", "method": "cancelOrder", "url": "https://papi.binance.com/papi/v1/um/order?timestamp=1707269570829&symbol=BTCUSDT&orderId=259769934117&recvWindow=10000&signature=48600abd8db8aa5c9ab39e3a79879915f5a512d146ef389ae6c6d85ea351adba", "input": [259769934117, "BTC/USDT:USDT", {"portfolioMargin": true}]}, {"description": "Inverse swap portfolio margin cancel order", "method": "cancelOrder", "url": "https://papi.binance.com/papi/v1/cm/order?timestamp=1707269743580&symbol=ETHUSD_PERP&orderId=71378300965&recvWindow=10000&signature=69742329ab2104f69c56c0a1f9885512a3c8f54325511130e68ff92326bd61b3", "input": [71378300965, "ETH/USD:ETH", {"portfolioMargin": true}]}, {"description": "Linear swap portfolio margin conditional cancel order", "method": "cancelOrder", "url": "https://papi.binance.com/papi/v1/um/conditional/order?timestamp=1707269945072&symbol=BTCUSDT&strategyId=3733207&recvWindow=10000&signature=b5ec95255493898e91209894976c9883d22a3de188b62ea177bf1c23efcd0db5", "input": [3733207, "BTC/USDT:USDT", {"portfolioMargin": true, "stop": true}]}, {"description": "Linear swap portfolio margin trigger cancel order", "method": "cancelOrder", "url": "https://papi.binance.com/papi/v1/um/conditional/order?timestamp=1707269945072&symbol=BTCUSDT&strategyId=3733207&recvWindow=10000&signature=b5ec95255493898e91209894976c9883d22a3de188b62ea177bf1c23efcd0db5", "input": [3733207, "BTC/USDT:USDT", {"portfolioMargin": true, "trigger": true}]}, {"description": "Inverse swap portfolio margin conditional cancel order", "method": "cancelOrder", "url": "https://papi.binance.com/papi/v1/cm/conditional/order?timestamp=1707270382591&symbol=ETHUSD_PERP&strategyId=1423466&recvWindow=10000&signature=4c5d4d9522fef4c07eab511eb42359bbfd9b929b312c247b3d64fdd1b0684a3f", "input": [1423466, "ETH/USD:ETH", {"portfolioMargin": true, "stop": true}]}, {"description": "Inverse swap portfolio margin trigger cancel order", "method": "cancelOrder", "url": "https://papi.binance.com/papi/v1/cm/conditional/order?timestamp=1707270382591&symbol=ETHUSD_PERP&strategyId=1423466&recvWindow=10000&signature=4c5d4d9522fef4c07eab511eb42359bbfd9b929b312c247b3d64fdd1b0684a3f", "input": [1423466, "ETH/USD:ETH", {"portfolioMargin": true, "trigger": true}]}], "cancelOrders": [{"description": "Swap cancelOrders", "method": "cancelOrders", "url": "https://testnet.binancefuture.com/fapi/v1/batchOrders?timestamp=1699384523218&symbol=LTCUSDT&recvWindow=10000&orderidlist=%5B652511506%5D&signature=ced41362438af4b529b81484e1713dc0108b2715453e403d3e46fa93ba8a0a83", "input": [["652511506"], "LTC/USDT:USDT"]}], "cancelAllOrders": [{"description": "Spot cancelAllOrders", "method": "cancelAllOrders", "url": "https://testnet.binance.vision/api/v3/openOrders?timestamp=1699384119652&symbol=LTCUSDT&recvWindow=10000&signature=0dbf34752013b284d4fb297b83583be92da868fe532282f98f8f90095b1fdc89", "input": ["LTC/USDT"]}, {"description": "Swap cancelAllOrders", "method": "cancelAllOrders", "url": "https://testnet.binancefuture.com/fapi/v1/allOpenOrders?timestamp=1699384154326&symbol=LTCUSDT&recvWindow=10000&signature=6be9641d53bd8e041581dbbe9f9a10576253d6d661a755ccbced131b8dc3e56d", "input": ["LTC/USDT:USDT"]}, {"description": "Swap inverse cancelAllOrders", "method": "cancelAllOrders", "url": "https://dapi.binance.com/dapi/v1/allOpenOrders?timestamp=1699384154326&symbol=BTCUSD_PERP&recvWindow=10000&signature=6be9641d53bd8e041581dbbe9f9a10576253d6d661a755ccbced131b8dc3e56d", "input": ["BTC/USD:BTC"]}, {"description": "Option cancelAllOrders", "method": "cancelAllOrders", "url": "https://eapi.binance.com/eapi/v1/allOpenOrders?timestamp=1699384154326&symbol=ETH-231229-800-C&recvWindow=10000&signature=6be9641d53bd8e041581dbbe9f9a10576253d6d661a755ccbced131b8dc3e56d", "input": ["ETH/USDT:USDT-231229-800-C"]}, {"description": "Spot margin portfolio margin cancel all orders", "method": "cancelAllOrders", "url": "https://papi.binance.com/papi/v1/margin/allOpenOrders?timestamp=1707200410926&symbol=BTCUSDT&recvWindow=10000&signature=df707ee6f73851d1e718c1dac653ef25728c1cd886a430f50f1f50aa29521923", "input": ["BTC/USDT", {"portfolioMargin": true, "marginMode": "cross"}]}, {"description": "Linear swap portfolio margin cancel all orders", "method": "cancelAllOrders", "url": "https://papi.binance.com/papi/v1/um/allOpenOrders?timestamp=1707200607284&symbol=BTCUSDT&recvWindow=10000&signature=288414f1b0973e263c547484ce798dcf5940c8fbf65491610cb196d2f42900fc", "input": ["BTC/USDT:USDT", {"portfolioMargin": true}]}, {"description": "Inverse swap portfolio margin cancel all orders", "method": "cancelAllOrders", "url": "https://papi.binance.com/papi/v1/cm/allOpenOrders?timestamp=1707200728688&symbol=ETHUSD_PERP&recvWindow=10000&signature=032e5ca4f8726847411668a8d3790721a6c284989259aad341eadf45c07c3a8f", "input": ["ETH/USD:ETH", {"portfolioMargin": true}]}, {"description": "Linear swap conditional portfolio margin cancel all orders", "method": "cancelAllOrders", "url": "https://papi.binance.com/papi/v1/um/conditional/allOpenOrders?timestamp=1707200837231&symbol=BTCUSDT&recvWindow=10000&signature=a2d5b52b27de004012e2505eabd7008521b2755832a94e9c20e6c8b37bd8f44f", "input": ["BTC/USDT:USDT", {"portfolioMargin": true, "stop": true}]}, {"description": "Inverse swap conditional portfolio margin cancel all orders", "method": "cancelAllOrders", "url": "https://papi.binance.com/papi/v1/cm/conditional/allOpenOrders?timestamp=*************&symbol=ETHUSD_PERP&recvWindow=10000&signature=a2cd0a7bcdbf763ed40e90f84660de730e43d0e278755090fc96874911e0fb9f", "input": ["ETH/USD:ETH", {"portfolioMargin": true, "stop": true}]}], "fetchBalance": [{"description": "Spot fetch balance", "method": "fetchBalance", "url": "https://api.binance.com/api/v3/account?timestamp=*************&recvWindow=10000&signature=63f0c698cb014312107449ed70b52c6e684ef8d18cba7b62fb378716f043534d", "input": []}, {"description": "Swap fetchBalance", "method": "fetchBalance", "url": "https://testnet.binancefuture.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=f8a952f41e186866660fa44ef11e22e69fd54145638964c6e79034e2fa83eb58", "input": [{"type": "swap"}]}, {"description": "Swap inverse fetchBalance", "method": "fetchBalance", "url": "https://testnet.binancefuture.com/dapi/v1/account?timestamp=*************&recvWindow=10000&signature=f8a952f41e186866660fa44ef11e22e69fd54145638964c6e79034e2fa83eb58", "input": [{"type": "swap", "subType": "inverse"}]}, {"description": "<PERSON><PERSON>", "method": "fetchBalance", "url": "https://testnet.binancefuture.com/sapi/v1/margin/account?timestamp=*************&recvWindow=10000&signature=f8a952f41e186866660fa44ef11e22e69fd54145638964c6e79034e2fa83eb58", "input": [{"type": "margin"}]}, {"description": "Savings fetchBalance", "method": "fetchBalance", "url": "https://testnet.binancefuture.com/sapi/v1/lending/union/account?timestamp=*************&recvWindow=10000&signature=f8a952f41e186866660fa44ef11e22e69fd54145638964c6e79034e2fa83eb58", "input": [{"type": "savings"}]}, {"description": "Funding fetchBalance", "method": "fetchBalance", "url": "https://testnet.binancefuture.com/sapi/v1/asset/get-funding-asset", "input": [{"type": "funding"}], "output": "timestamp=*************&recvWindow=10000&signature=9dbc6d2f649bd8522187b3ac2643e2eec3c50e48d5d44e925eb225e108c133ee"}, {"description": "fetch isolated balance", "method": "fetchBalance", "url": "https://api.binance.com/sapi/v1/margin/isolated/account?timestamp=*************&recvWindow=10000&signature=96c89d509bf6e5a630b1e7dd84dce9f011cb07caf25cf6eaa90a8a4400793826", "input": [{"marginMode": "isolated"}]}, {"description": "Fetch portfolio margin balance", "method": "fetchBalance", "url": "https://papi.binance.com/papi/v1/balance?timestamp=*************&recvWindow=10000&signature=20a3d68b3de7861311b3374d162428361526523cb8bd7d79ceada4c2b02b0f4b", "input": [{"portfolioMargin": true}]}], "fetchMyTrades": [{"description": "Spot fetchMyTrades", "method": "fetchMyTrades", "url": "https://api.binance.com/api/v3/myTrades?timestamp=*************&symbol=LTCUSDT&recvWindow=10000&signature=e0364430919fe9187025c68199752304a723f4fe62de000440b8fa4db8f6dd66", "input": ["LTC/USDT"]}, {"description": "Swap fetchMyTrades", "method": "fetchMyTrades", "url": "https://testnet.binancefuture.com/fapi/v1/userTrades?timestamp=*************&symbol=LTCUSDT&recvWindow=10000&signature=df9868b4dbe11d15402e6cbea7910cc8cea2f5f5f87cddd91e40d8aa3b869d82", "input": ["LTC/USDT:USDT"]}, {"description": "Swap inverse fetchMyTrades", "method": "fetchMyTrades", "url": "https://dapi.binance.com/dapi/v1/userTrades?timestamp=*************&symbol=BTCUSD_PERP&recvWindow=10000&signature=df9868b4dbe11d15402e6cbea7910cc8cea2f5f5f87cddd91e40d8aa3b869d82", "input": ["BTC/USD:BTC"]}, {"description": "Option fetchMyTrades", "method": "fetchMyTrades", "url": "https://eapi.binance.com/eapi/v1/userTrades?timestamp=*************&symbol=ETH-231229-800-C&recvWindow=10000&signature=df9868b4dbe11d15402e6cbea7910cc8cea2f5f5f87cddd91e40d8aa3b869d82", "input": ["ETH/USDT:USDT-231229-800-C"]}, {"description": "Spot margin cross", "method": "fetchMyTrades", "url": "https://api.binance.com/sapi/v1/margin/myTrades?timestamp=1703241051088&symbol=LTCUSDT&limit=1&recvWindow=10000&signature=141057bfeccdf9ba9d99838058dc0fb77b9331ed06ef43216867952c6e1e874a", "input": ["LTC/USDT", null, 1, {"marginMode": "cross"}]}, {"description": "Spot margin isolated", "method": "fetchMyTrades", "url": "https://api.binance.com/sapi/v1/margin/myTrades?timestamp=1703241081711&symbol=LTCUSDT&limit=1&isIsolated=true&recvWindow=10000&signature=46f94e4f43408cb782d20c97be4bbdd2837cfb552091312206d49822d1065613", "input": ["LTC/USDT", null, 1, {"marginMode": "isolated"}]}, {"description": "Linear swap portfolio margin fetch my trades", "method": "fetchMyTrades", "url": "https://papi.binance.com/papi/v1/um/userTrades?timestamp=1707530080933&symbol=BTCUSDT&recvWindow=10000&signature=60b731019a84caf5034fb81e6ace6c4ccf66f02dc2b75713f1965a659d28c856", "input": ["BTC/USDT:USDT", null, null, {"portfolioMargin": true}]}, {"description": "Inverse swap portfolio margin fetch my trades", "method": "fetchMyTrades", "url": "https://papi.binance.com/papi/v1/cm/userTrades?timestamp=1707530344032&symbol=ETHUSD_PERP&recvWindow=10000&signature=d689f5f2a52c7fef837beb49c2493ccc15de55c0706d9878a6890a972a943fc8", "input": ["ETH/USD:ETH", null, null, {"portfolioMargin": true}]}, {"description": "Spot margin portfolio margin fetch my trades", "method": "fetchMyTrades", "url": "https://papi.binance.com/papi/v1/margin/myTrades?timestamp=1707549805183&symbol=ADAUSDT&recvWindow=10000&signature=5592c16d537fb2fd7c109ad2a7d779f28f24cb4a11175b33a1f458df35f611c0", "input": ["ADA/USDT", null, null, {"portfolioMargin": true}]}], "fetchOrders": [{"description": "Spot fetchOrders", "method": "fetchOrders", "url": "https://api.binance.com/api/v3/allOrders?timestamp=1699381044319&symbol=LTCUSDT&recvWindow=10000&signature=858226f6bc5879207bf14a06a71c380e6392601e703b3b0ac229fe1f1f3d092a", "input": ["LTC/USDT"]}, {"description": "Swap fetchOrders", "method": "fetchOrders", "url": "https://testnet.binancefuture.com/fapi/v1/allOrders?timestamp=1699381079525&symbol=LTCUSDT&recvWindow=10000&signature=e75dee5138d27e195e8bd66ba3e754dba7c7fd2911252f7358f5506566b7517e", "input": ["LTC/USDT:USDT"]}, {"description": "Inverse fetchOrders", "method": "fetchOrders", "url": "https://dapi.binance.com/dapi/v1/allOrders?timestamp=1699381120125&symbol=BTCUSD_PERP&recvWindow=10000&signature=4c191cf2693c1fed0a75a6231035bd3c57be27bc326d4e60c44735c4ddeac5dd", "input": ["BTC/USD:BTC"]}, {"description": "Option fetchOrders", "method": "fetchOrders", "url": "https://eapi.binance.com/eapi/v1/historyOrders?timestamp=1699381120125&symbol=ETH-231229-800-C&recvWindow=10000&signature=4c191cf2693c1fed0a75a6231035bd3c57be27bc326d4e60c44735c4ddeac5dd", "input": ["ETH/USDT:USDT-231229-800-C"]}, {"description": "Spot margin cross fetchOrders", "method": "fetchOrders", "url": "https://api.binance.com/sapi/v1/margin/allOrders?timestamp=1699381288329&symbol=LTCUSDT&startTime=1699381234000&limit=20&recvWindow=10000&signature=1cb6d6b9fcfc84a22d685d46827c33eb67fe8ff0980784712bce5002822c4515", "input": ["LTC/USDT", 1699381234000, 20, {"marginMode": "cross"}]}, {"description": "Spot margin isolated fetchOrders", "method": "fetchOrders", "url": "https://api.binance.com/sapi/v1/margin/allOrders?timestamp=1699381316098&symbol=LTCUSDT&isIsolated=true&startTime=1699381234000&limit=20&recvWindow=10000&signature=f589333c4be021c50cb3bf8b75d04d2027c545acf22fec45ae19c57be8ed5b3c", "input": ["LTC/USDT", 1699381234000, 20, {"marginMode": "isolated"}]}, {"description": "Linear swap portfolio margin fetch orders", "method": "fetchOrders", "url": "https://papi.binance.com/papi/v1/um/allOrders?timestamp=1707792652974&symbol=BTCUSDT&recvWindow=10000&signature=73fea37c53ceb88f88dd00ff5b7963bd76a05c3ee51ff978820b5db43d85e6e0", "input": ["BTC/USDT:USDT", null, null, {"portfolioMargin": true}]}, {"description": "Inverse swap portfolio margin fetch orders", "method": "fetchOrders", "url": "https://papi.binance.com/papi/v1/cm/allOrders?timestamp=1707792759881&symbol=ETHUSD_PERP&recvWindow=10000&signature=d55ede981e4d2dcfc2436a15dbe088eb41440285d8fb2e7dd20bf96eaaff8776", "input": ["ETH/USD:ETH", null, null, {"portfolioMargin": true}]}, {"description": "Linear swap conditional portfolio margin fetch orders", "method": "fetchOrders", "url": "https://papi.binance.com/papi/v1/um/conditional/allOrders?timestamp=1707792805020&symbol=BTCUSDT&recvWindow=10000&signature=fa5930b499e5a683591142f732b96939815fb42fb1d4c9c1caec2e87368bf5a7", "input": ["BTC/USDT:USDT", null, null, {"portfolioMargin": true, "stop": true}]}, {"description": "Inverse swap conditional portfolio margin fetch orders", "method": "fetchOrders", "url": "https://papi.binance.com/papi/v1/cm/conditional/allOrders?timestamp=1707792703391&symbol=ETHUSD_PERP&recvWindow=10000&signature=b8a90970cff0ef5402e2eddb88efef01316b83d1827a7015bdc6e3735ccb711e", "input": ["ETH/USD:ETH", null, null, {"portfolioMargin": true, "stop": true}]}, {"description": "Spot margin portfolio margin fetch orders", "method": "fetchOrders", "url": "https://papi.binance.com/papi/v1/margin/allOrders?timestamp=1707792860318&symbol=BTCUSDT&recvWindow=10000&signature=b8c3ab4decfe00027b3811b0d64cc7dfc5c6c73648ce095078ff805d1b78a1b0", "input": ["BTC/USDT", null, null, {"portfolioMargin": true}]}], "fetchOrder": [{"description": "Spot fetchOrder", "method": "fetchOrder", "url": "https://testnet.binance.vision/api/v3/order?timestamp=1699384269930&symbol=LTCUSDT&orderId=465138&recvWindow=10000&signature=3e56f68f7464d282deb93466f4a382886f216739cf0225e5ae32b13529bd7b84", "input": [465138, "LTC/USDT"]}, {"description": "Swap fetchOrder", "method": "fetchOrder", "url": "https://testnet.binancefuture.com/fapi/v1/order?timestamp=1699384317029&symbol=LTCUSDT&orderId=652124757&recvWindow=10000&signature=9f2120e58e884131b91f4193a94ea17a18ef4f3195b3e6f388d5f5e14e797414", "input": [652124757, "LTC/USDT:USDT"]}, {"description": "Swap inverse fetchOrder", "method": "fetchOrder", "url": "https://dapi.binance.com/dapi/v1/order?timestamp=1699384317029&symbol=BTCUSD_PERP&orderId=652124757&recvWindow=10000&signature=9f2120e58e884131b91f4193a94ea17a18ef4f3195b3e6f388d5f5e14e797414", "input": [652124757, "BTC/USD:BTC"]}, {"description": "Option fetchOrder", "method": "fetchOrder", "url": "https://eapi.binance.com/eapi/v1/order?timestamp=1699384317029&symbol=ETH-231229-800-C&orderId=652124757&recvWindow=10000&signature=9f2120e58e884131b91f4193a94ea17a18ef4f3195b3e6f388d5f5e14e797414", "input": [652124757, "ETH/USDT:USDT-231229-800-C"]}, {"description": "Spot cross order", "method": "fetchOrder", "url": "https://api.binance.com/sapi/v1/margin/order?timestamp=1703159386293&symbol=LTCUSDT&orderId=3103603561&recvWindow=10000&signature=dc62a3b4d2125eb456629bdbb70eee5cddfc1c2836b050e072dc5bec635bf5e8", "input": [3103603561, "LTC/USDT", {"marginMode": "cross"}]}, {"description": "Fetch isolated order", "method": "fetchOrder", "url": "https://api.binance.com/sapi/v1/margin/order?timestamp=1703159655585&symbol=LTCUSDT&isIsolated=true&orderId=3807830610&recvWindow=10000&signature=67f6cbaff42d879c7af414d5669b542c8ff536689d52ade8ca17a3406f665175", "input": [3807830610, "LTC/USDT", {"marginMode": "isolated"}]}, {"description": "Linear swap portfolio margin fetch order", "method": "fetchOrder", "url": "https://papi.binance.com/papi/v1/um/order?timestamp=1707257405537&symbol=BTCUSDT&orderId=259696755816&recvWindow=10000&signature=d916df835ea5208731b4da01ebb6eaef2eca85205777d49ef71e1d791505c522", "input": [259696755816, "BTC/USDT:USDT", {"portfolioMargin": true}]}, {"description": "Inverse swap portfolio margin fetch order", "method": "fetchOrder", "url": "https://papi.binance.com/papi/v1/cm/order?timestamp=1707257668234&symbol=ETHUSD_PERP&orderId=71371262156&recvWindow=10000&signature=8fb58c60d328534f21486e533ffe0e7371f96050d815609a05662d640b91eba8", "input": [71371262156, "ETH/USD:ETH", {"portfolioMargin": true}]}, {"description": "Spot margin portfolio margin fetch order", "method": "fetchOrder", "url": "https://papi.binance.com/papi/v1/margin/order?timestamp=1707257917946&symbol=BTCUSDT&orderId=24711539682&recvWindow=10000&signature=ecc58b10a3bb4c26814fb521facda9151b0e7caafacc65836557b89eca386e17", "input": [24711539682, "BTC/USDT", {"portfolioMargin": true, "marginMode": "cross"}]}], "fetchOpenOrders": [{"description": "Swap open orders", "method": "fetchOpenOrders", "url": "https://testnet.binancefuture.com/fapi/v1/openOrders?timestamp=1699381574417&symbol=LTCUSDT&recvWindow=10000&signature=6a256c608fa51704c38209d2dc0338832a2b4193b5c53360780ff3012b4777f6", "input": ["LTC/USDT:USDT"]}, {"description": "Spot one orders", "method": "fetchOpenOrders", "url": "https://api.binance.com/api/v3/openOrders?timestamp=1699381643483&symbol=LTCUSDT&recvWindow=10000&signature=5e065a2e4308050c13f57fbf2280c0148108537d487377153d4a35ceae451285", "input": ["LTC/USDT"]}, {"description": "Inverse open orders", "method": "fetchOpenOrders", "url": "https://dapi.binance.com/dapi/v1/openOrders?timestamp=1699381672955&symbol=BTCUSD_PERP&recvWindow=10000&signature=660a51120973fd1a69fb1b08fa0c0deb92be1a890e978688e659f8bc02acac44", "input": ["BTC/USD:BTC"]}, {"description": "Option open orders", "method": "fetchOpenOrders", "url": "https://eapi.binance.com/eapi/v1/openOrders?timestamp=1699381761363&symbol=ETH-231229-800-C&recvWindow=10000&signature=6614edfda35dd14772f8dd9b7236b9db9e36323bfe8682a829987033db85be24", "input": ["ETH/USDT:USDT-231229-800-C"]}, {"description": "Spot-margin cross open orders", "method": "fetchOpenOrders", "url": "https://api.binance.com/sapi/v1/margin/openOrders?timestamp=1699381707792&symbol=BTCUSDT&recvWindow=10000&signature=3f1f41573b1b178a7b320281ce6152a6e616c55f5f0ea3092e4cf94de0ac59cb", "input": ["BTC/USDT", null, null, {"marginMode": "cross"}]}, {"description": "Spot margin isolated open orders", "method": "fetchOpenOrders", "url": "https://api.binance.com/sapi/v1/margin/openOrders?timestamp=1699381761363&symbol=LTCUSDT&isIsolated=true&recvWindow=10000&signature=6614edfda35dd14772f8dd9b7236b9db9e36323bfe8682a829987033db85be24", "input": ["LTC/USDT", null, null, {"marginMode": "isolated"}]}, {"description": "Linear swap portfolio margin open orders", "method": "fetchOpenOrders", "url": "https://papi.binance.com/papi/v1/um/openOrders?timestamp=1707197626953&symbol=BTCUSDT&recvWindow=10000&signature=4df8c7c7f3715ffd41cf6dcffafff6f58f56e37e3f924feab36ee77595b63f72", "input": ["BTC/USDT:USDT", null, null, {"portfolioMargin": true}]}, {"description": "Inverse swap portfolio margin open orders", "method": "fetchOpenOrders", "url": "https://papi.binance.com/papi/v1/cm/openOrders?timestamp=1707197862310&symbol=ETHUSD_PERP&recvWindow=10000&signature=e2f92aad2f3fb7fe80b0c081494ca36a4bb32fa79194b2072bd8781731ce25c0", "input": ["ETH/USD:ETH", null, null, {"portfolioMargin": true}]}, {"description": "Linear swap conditional portfolio margin open orders", "method": "fetchOpenOrders", "url": "https://papi.binance.com/papi/v1/um/conditional/openOrders?timestamp=1707198871950&symbol=BTCUSDT&recvWindow=10000&signature=df05759f3ce7eedef0fc3fca72635deb0379a289365808755f1086c5dd59c980", "input": ["BTC/USDT:USDT", null, null, {"portfolioMargin": true, "stop": true}]}, {"description": "Inverse swap conditional portfolio margin open orders", "method": "fetchOpenOrders", "url": "https://papi.binance.com/papi/v1/cm/conditional/openOrders?timestamp=1707199082780&symbol=ETHUSD_PERP&recvWindow=10000&signature=b8b647ada017a613b32424302351b6a9794b8a7a81a34b5a91b39f3e5ea5ec08", "input": ["ETH/USD:ETH", null, null, {"portfolioMargin": true, "stop": true}]}, {"description": "Spot margin portfolio margin open orders", "method": "fetchOpenOrders", "url": "https://papi.binance.com/papi/v1/margin/openOrders?timestamp=1707199469255&symbol=BTCUSDT&recvWindow=10000&signature=5602662ad2b21486608f4e061cdf4812203b345743b752f0f4c6e5fa090ff70c", "input": ["BTC/USDT", null, null, {"portfolioMargin": true, "marginMode": "cross"}]}], "fetchCanceledOrders": [{"description": "Spot canceled orders", "method": "fetchCanceledOrders", "url": "https://testnet.binance.vision/api/v3/allOrders?timestamp=1699384225531&symbol=LTCUSDT&recvWindow=10000&signature=542ec2f6763831d5a92d891174d744b77b3deb931c5787463fc99744f2bb34df", "input": ["LTC/USDT", null, 1]}, {"description": "Spot margin portfolio margin fetch canceled orders", "method": "fetchCanceledOrders", "url": "https://papi.binance.com/papi/v1/margin/allOrders?timestamp=1707794017912&symbol=BTCUSDT&recvWindow=10000&signature=2d3478743a749fe36639cac676194b6926ffd82132fce7dca901caf3b08abcd4", "input": ["BTC/USDT", null, null, {"portfolioMargin": true}]}, {"description": "Linear portfolio margin fetch canceled orders", "method": "fetchCanceledOrders", "url": "https://papi.binance.com/papi/v1/um/allOrders?timestamp=1707798719391&symbol=BTCUSDT&recvWindow=10000&signature=3ea34eedd573746fe9bad66ea1b8eef8863a273653d408c246b94cc6029fd7bb", "input": ["BTC/USDT:USDT", null, null, {"portfolioMargin": true}]}, {"description": "Inverse portfolio margin fetch canceled orders", "method": "fetchCanceledOrders", "url": "https://papi.binance.com/papi/v1/cm/allOrders?timestamp=1707798826314&symbol=ETHUSD_PERP&recvWindow=10000&signature=8fff8802c032c9b55965aa1c2d949cabb5fbb9a6b23316bb03a3631d79af6851", "input": ["ETH/USD:ETH", null, null, {"portfolioMargin": true}]}, {"description": "Linear conditional portfolio margin fetch canceled orders", "method": "fetchCanceledOrders", "url": "https://papi.binance.com/papi/v1/um/conditional/allOrders?timestamp=1707798862888&symbol=BTCUSDT&recvWindow=10000&signature=6492a3f7739ac309e269451258f34bbd16f7c6b247b4fc7ac074cccf1acedcdf", "input": ["BTC/USDT:USDT", null, null, {"portfolioMargin": true, "stop": true}]}, {"description": "Inverse conditional portfolio margin fetch canceled orders", "method": "fetchCanceledOrders", "url": "https://papi.binance.com/papi/v1/cm/conditional/allOrders?timestamp=1707798908668&symbol=ETHUSD_PERP&recvWindow=10000&signature=cab4eee248582eb3f6ea432c5b2891afae57c723f8aabeaf047126c95c4fd95e", "input": ["ETH/USD:ETH", null, null, {"portfolioMargin": true, "stop": true}]}], "fetchClosedOrders": [{"description": "closed orders, should not provide limit in the request", "method": "fetchClosedOrders", "url": "https://api.binance.com/api/v3/allOrders?timestamp=1707669100824&symbol=LTCUSDT&recvWindow=10000&signature=e84b020042f478f2d7857ed9544737864d9b2fd23e85f55009bb3b2c6ad62215", "input": ["LTC/USDT", null, 10]}, {"description": "Linear portfolio margin fetch closed orders", "method": "fetchClosedOrders", "url": "https://papi.binance.com/papi/v1/um/allOrders?timestamp=1707793443640&symbol=BTCUSDT&recvWindow=10000&signature=5a0b8c56d3b612d0ea09042e8c664fea9c8e94d3ea3d18e5369c4ca0a73f03e4", "input": ["BTC/USDT:USDT", null, null, {"portfolioMargin": true}]}, {"description": "Inverse portfolio margin fetch closed orders", "method": "fetchClosedOrders", "url": "https://papi.binance.com/papi/v1/cm/allOrders?timestamp=1707793564312&symbol=ETHUSD_PERP&recvWindow=10000&signature=93a4fc25b85e718978fe065176fb43098a33f994aa03c05278051ed9c392d10c", "input": ["ETH/USD:ETH", null, null, {"portfolioMargin": true}]}, {"description": "Linear conditional portfolio margin fetch closed orders", "method": "fetchClosedOrders", "url": "https://papi.binance.com/papi/v1/um/conditional/allOrders?timestamp=1707793503573&symbol=BTCUSDT&recvWindow=10000&signature=3a62b27a95127273f2841ff3800aedc91741b1e9a06913317ec7a09328cf2260", "input": ["BTC/USDT:USDT", null, null, {"portfolioMargin": true, "stop": true}]}, {"description": "Inverse conditional portfolio margin fetch closed orders", "method": "fetchClosedOrders", "url": "https://papi.binance.com/papi/v1/cm/conditional/allOrders?timestamp=1707793604304&symbol=ETHUSD_PERP&recvWindow=10000&signature=8a515858c3a0cacc9f6170829688a426044733c29a9fb8563d5c94bfa8b443b0", "input": ["ETH/USD:ETH", null, null, {"portfolioMargin": true, "stop": true}]}, {"description": "Spot margin portfolio margin fetch closed orders", "method": "fetchClosedOrders", "url": "https://papi.binance.com/papi/v1/margin/allOrders?timestamp=1707793643759&symbol=BTCUSDT&recvWindow=10000&signature=3a0df656e278682cc86f523c35eb04243e21cd1f2cfcf3fa6bc5cc5981821182", "input": ["BTC/USDT", null, null, {"portfolioMargin": true}]}], "fetchOrderTrades": [{"description": "spot order trades", "method": "fetchOrderTrades", "url": "https://api.binance.com/api/v3/myTrades?timestamp=1706095069482&symbol=LTCUSDT&orderId=3010478906&recvWindow=10000&signature=69dfdb9c6162ba56efe69b3ae4b95800994ee7fd58d37b1a240440d35c4ebde0", "input": [3010478906, "LTC/USDT"]}], "fetchPositions": [{"description": "fetch default positions", "method": "fetchPositions", "url": "https://testnet.binancefuture.com/fapi/v3/positionRisk?timestamp=*************&recvWindow=10000&signature=f654a95fde9ee5ee6004b1ded1adb3671333ac84eeab7f3b11c27b12848a28c2", "input": []}, {"description": "fetch default positions", "method": "fetchPositions", "url": "https://testnet.binancefuture.com/dapi/v1/positionRisk?timestamp=*************&recvWindow=10000&signature=ebbde9cbf31b9e450afb6aa6f5236b4f1a89b7afb9686493be317fbad1a3a5b2", "input": [null, {"subType": "inverse"}]}, {"description": "fetch account positions", "method": "fetchPositions", "url": "https://testnet.binancefuture.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=6c1048179b4cfc725c45991f61b13da9321ba96f5149ac359ca956a4e8dd33ea", "input": [null, {"method": "account"}]}, {"description": "fetchPositions using v2 endpoint", "method": "fetchPositions", "url": "https://testnet.binancefuture.com/fapi/v2/positionRisk?timestamp=*************&recvWindow=10000&signature=dff9cc68edcb97f55d93a978daa862eb3caf36ccf053943467995603ba453b7c", "input": [["LTC/USDT:USDT"], {"useV2": true}]}], "transfer": [{"description": "Transfer from cross to spot", "method": "transfer", "url": "https://api.binance.com/sapi/v1/asset/transfer", "input": ["USDT", 1, "cross", "spot"], "output": "timestamp=*************&asset=USDT&amount=1&type=MARGIN_MAIN&recvWindow=10000&signature=1d4479f2e7df920f0b14af126ef2c753738e6e6945a28c2fdbd69bc841009e64"}, {"description": "Transfer from spot to linear", "method": "transfer", "url": "https://api.binance.com/sapi/v1/asset/transfer", "input": ["USDT", 1, "spot", "linear"], "output": "timestamp=*************&asset=USDT&amount=1&type=MAIN_UMFUTURE&recvWindow=10000&signature=33c61a7570d7a2c9c99ddd57edb5b5c2574351b9e485512e9f6382d5b5f1804f"}, {"description": "Transfer from spot to funding", "method": "transfer", "url": "https://api.binance.com/sapi/v1/asset/transfer", "input": ["USDT", 1, "spot", "funding"], "output": "timestamp=1699384737453&asset=USDT&amount=1&type=MAIN_FUNDING&recvWindow=10000&signature=5418d03fb525c32e4a1c4e1a874abcda635bad6ba7763fc82c0b164eeebd7523"}, {"description": "Transfer from funding to spot", "method": "transfer", "url": "https://api.binance.com/sapi/v1/asset/transfer", "input": ["USDT", 1, "funding", "spot"], "output": "timestamp=*************&asset=USDT&amount=1&type=FUNDING_MAIN&recvWindow=10000&signature=4e43d89ac545361e50c2b7df44b91f6c168c99dab114acc6bad382a4c51aa34b"}, {"description": "Main to isolated using symbol as toAccount", "method": "transfer", "url": "https://api.binance.com/sapi/v1/asset/transfer", "input": ["USDT", 1, "main", "LTC/USDT"], "output": "timestamp=*************&asset=USDT&amount=1&type=MAIN_ISOLATED_MARGIN&toSymbol=LTCUSDT&recvWindow=10000&signature=6fca390794311a2b8595b1b434d01f4533cef142980b5d4fdbb2f7ef7353f0b4"}, {"description": "from main to isolated", "method": "transfer", "url": "https://api.binance.com/sapi/v1/asset/transfer", "input": ["USDT", 1, "main", "isolated", {"symbol": "LTC/USDT"}], "output": "timestamp=*************&asset=USDT&amount=1&type=MAIN_ISOLATED_MARGIN&toSymbol=LTCUSDT&recvWindow=10000&signature=b4343c34b6664e6a95f519ef6828defa6b4b86cc14a135dbca00b723cba4787b"}, {"description": "From isolated to main using symbol as fromAccount", "method": "transfer", "url": "https://api.binance.com/sapi/v1/asset/transfer", "input": ["USDT", 1, "LTC/USDT", "main"], "output": "timestamp=*************&asset=USDT&amount=1&type=ISOLATED_MARGIN_MAIN&fromSymbol=LTCUSDT&recvWindow=10000&signature=40eabe9bb3f2265d8d15656278c5b92f37b690d4bc3161efa9803739e54d6dbf"}, {"description": "From isolated to main", "method": "transfer", "url": "https://api.binance.com/sapi/v1/asset/transfer", "input": ["USDT", 1, "isolated", "main", {"symbol": "LTC/USDT"}], "output": "timestamp=*************&asset=USDT&amount=1&type=ISOLATED_MARGIN_MAIN&fromSymbol=LTCUSDT&recvWindow=10000&signature=9dd361ee76b7d1ea34d282c538bb9cfe44b32a470f1076a00cc1592b1eb51ef0"}, {"description": "From isolated to isolated", "method": "transfer", "url": "https://api.binance.com/sapi/v1/asset/transfer", "input": ["USDT", 1, "LTC/USDT", "ADA/USDT"], "output": "timestamp=*************&asset=USDT&amount=1&type=ISOLATEDMARGIN_ISOLATEDMARGIN&fromSymbol=LTCUSDT&toSymbol=ADAUSDT&recvWindow=10000&signature=190b3acf1286fe5ab2e41d2024d8fe1cc81eb9a30bccf77386275d37d8cc81c5"}, {"description": "transfer main funding with weird amount", "method": "transfer", "url": "https://api.binance.com/sapi/v1/asset/transfer", "input": ["USDT", 1.23434223, "main", "funding"], "output": "timestamp=1718723373536&asset=USDT&amount=1.23434223&type=MAIN_FUNDING&recvWindow=10000&signature=731b5bbe83853c1cb5b3715a569fd67554e39681ecf5098c1646264e9c98a555"}], "fetchDeposits": [{"description": "Default fetch deposits", "method": "fetchDeposits", "url": "https://api.binance.com/sapi/v1/capital/deposit/hisrec?timestamp=1699440724626&recvWindow=10000&signature=d36b11a06e7af3f8bc85c28e97ab788a58e20f39b201b6fc31132688d71f4968", "input": []}, {"description": "fetch EUR deposits", "method": "fetchDeposits", "url": "https://api.binance.com/sapi/v1/fiat/orders?timestamp=1706094902897&transactionType=0&recvWindow=10000&signature=b8a61acd8cd767e332c80ee2e14f5d827d96720023df8e8d7bbe231ab25f794f", "input": ["EUR"]}], "fetchLedger": [{"description": "Fetch swap ledger", "method": "fetchLedger", "url": "https://fapi.binance.com/fapi/v1/income?timestamp=1699440856890&recvWindow=10000&signature=e921974f0fc79c71a0bf5c210bded9841b627248e89ca2659dd26616d2870bd9", "input": ["USDT", null, null, {"type": "swap"}]}, {"description": "Fetch swap ledger", "method": "fetchLedger", "url": "https://fapi.binance.com/dapi/v1/income?timestamp=1699440856890&recvWindow=10000&signature=e921974f0fc79c71a0bf5c210bded9841b627248e89ca2659dd26616d2870bd9", "input": ["USDT", null, null, {"type": "swap", "subType": "inverse"}]}, {"description": "Linear swap portfolio margin fetch ledger", "method": "fetchLedger", "url": "https://papi.binance.com/papi/v1/um/income?timestamp=1707448893303&recvWindow=10000&signature=3aed27d69b1967eb5dd0a6183b32fb08bd85f14d0f9d02d951a45a6007b74843", "input": [null, null, null, {"portfolioMargin": true, "type": "swap", "subType": "linear"}]}, {"description": "Inverse swap portfolio margin fetch ledger", "method": "fetchLedger", "url": "https://papi.binance.com/papi/v1/cm/income?timestamp=1707448981443&recvWindow=10000&signature=19baa97358f2f82e612b69e6d13c59ced0de2f6379df0c32fc6621bd74ec2639", "input": [null, null, null, {"portfolioMargin": true, "type": "swap", "subType": "inverse"}]}], "setLeverage": [{"description": "Swap linear setLeverage", "method": "setLeverage", "url": "https://testnet.binancefuture.com/fapi/v1/leverage", "input": [5, "LTC/USDT:USDT"], "output": "timestamp=1699440965441&symbol=LTCUSDT&leverage=5&recvWindow=10000&signature=500407a2dd13c20e6cef1a884fd1982fe494f2436761cd8c73c6de1aeeaf2a70"}, {"description": "Swap inverse setLeverage", "method": "setLeverage", "url": "https://dapi.binance.com/dapi/v1/leverage", "input": [5, "BTC/USD:BTC"], "output": "timestamp=1699441011001&symbol=BTCUSD_PERP&leverage=5&recvWindow=10000&signature=f932a4e8dd21ce1dcea84d8ab2fd9451c31b77c710d1adb9c51f54adc520cd90"}, {"description": "Linear swap portfolio margin set leverage", "method": "setLeverage", "url": "https://papi.binance.com/papi/v1/um/leverage", "input": [100, "BTC/USDT:USDT", {"portfolioMargin": true}], "output": "timestamp=1707286038777&symbol=BTCUSDT&leverage=100&recvWindow=10000&signature=7dc3dd7f2a447bec4ef5ef2eed9629e8228b238e90a380df9a10e0bf21e9b42f"}, {"description": "Inverse swap portfolio margin set leverage", "method": "setLeverage", "url": "https://papi.binance.com/papi/v1/cm/leverage", "input": [100, "ETH/USD:ETH", {"portfolioMargin": true}], "output": "timestamp=1707285921923&symbol=ETHUSD_PERP&leverage=100&recvWindow=10000&signature=383f5a990cdfbe2c6f26212483b7967ab613039e11e6ea3be4ef3ab2cf5640c9"}], "setMarginMode": [{"description": "Set margin mode to cross", "method": "setMarginMode", "url": "https://testnet.binancefuture.com/fapi/v1/marginType", "input": ["CROSS", "BTC/USDT:USDT"], "output": "timestamp=1699441787982&symbol=BTCUSDT&marginType=CROSSED&recvWindow=10000&signature=16f612e91d2ff4494239137194470ea53d7a20eff2d1b02c0783327bd40da955"}, {"description": "Set margin mode to isolated with inverse market", "method": "setMarginMode", "url": "https://dapi.binance.com/dapi/v1/marginType", "input": ["isolated", "BTC/USD:BTC"], "output": "timestamp=1699441887107&symbol=BTCUSD_PERP&marginType=ISOLATED&recvWindow=10000&signature=23faa49ca6c2c26c2ed637bda09e850587c8cf9dd884d495b44bddf6744dff09"}], "fetchCrossBorrowRate": [{"description": "Fetch cross borrow rate", "method": "fetchCrossBorrowRate", "url": "https://api.binance.com/sapi/v1/margin/interestRateHistory?timestamp=1700202941111&asset=USDT&recvWindow=10000&signature=ffc9f69400ec59a68f0785334a9a619258411f43f6cfc0c7aa9585202d0f8eb5", "input": ["USDT"]}], "fetchMarkPrices": [{"description": "fetchMarkPrices", "method": "fetchMarkPrices", "url": "https://fapi.binance.com/fapi/v1/premiumIndex", "input": []}], "fetchMarkPrice": [{"description": "fetchMarkPrice", "method": "fetchMarkPrice", "url": "https://fapi.binance.com/fapi/v1/premiumIndex?symbol=ETHUSDT", "input": ["ETH/USDT:USDT"]}, {"description": "fetchMarkPrice", "method": "fetchMarkPrice", "url": "https://dapi.binance.com/dapi/v1/premiumIndex?symbol=ETHUSD_PERP", "input": ["ETH/USD:ETH"]}], "fetchTickers": [{"description": "spot fetch tickers with symbols", "method": "fetchTickers", "url": "https://testnet.binance.vision/api/v3/ticker/24hr?symbols=%5B%22BTCUSDT%22%2C%22LTCUSDT%22%5D", "input": [["BTC/USDT", "LTC/USDT"]]}, {"description": "Swap tickers", "method": "fetchTickers", "url": "https://testnet.binancefuture.com/fapi/v1/ticker/24hr", "input": [["BTC/USDT:USDT"]]}, {"description": "Inverse tickers", "method": "fetchTickers", "url": "https://testnet.binancefuture.com/dapi/v1/ticker/24hr", "input": [["BTC/USD:BTC"]]}, {"description": "spot tickers", "method": "fetchTickers", "url": "https://api.binance.com/api/v3/ticker/24hr?symbols=%5B%22BTCUSDT%22%2C%22ETHUSDT%22%5D", "input": [["BTC/USDT", "ETH/USDT"]]}, {"description": "swap tickers", "method": "fetchTickers", "url": "https://fapi.binance.com/fapi/v1/ticker/24hr", "input": [["BTC/USDT:USDT", "ETH/USDT:USDT"]]}], "fetchLastPrices": [{"description": "fetch last prices - no arguments", "method": "fetchLastPrices", "url": "https://api.binance.com/api/v3/ticker/price", "input": []}, {"description": "fetch last prices - spot defaultType", "method": "fetchLastPrices", "url": "https://api.binance.com/api/v3/ticker/price", "input": [null, {"defaultType": "spot"}]}, {"description": "fetch last prices - spot symbols", "method": "fetchLastPrices", "url": "https://api.binance.com/api/v3/ticker/price", "input": [["BTC/USDT", "ETH/USDT"]]}, {"description": "fetch last prices - linear swap", "method": "fetchLastPrices", "url": "https://fapi.binance.com/fapi/v2/ticker/price", "input": [null, {"defaultType": "swap", "subType": "linear"}]}, {"description": "fetch last prices - linear swap symbols", "method": "fetchLastPrices", "url": "https://fapi.binance.com/fapi/v2/ticker/price", "input": [["BTC/USDT:USDT", "ETH/USDT:USDT"]]}, {"description": "fetch last prices - linear future", "method": "fetchLastPrices", "url": "https://fapi.binance.com/fapi/v2/ticker/price", "input": [null, {"defaultType": "future", "subType": "linear"}]}, {"description": "fetch last prices - inverse swap", "method": "fetchLastPrices", "url": "https://dapi.binance.com/dapi/v1/ticker/price", "input": [null, {"defaultType": "swap", "subType": "inverse"}]}, {"description": "fetch last prices - inverse swap symbols", "method": "fetchLastPrices", "url": "https://dapi.binance.com/dapi/v1/ticker/price", "input": [["BTC/USD:BTC", "ETH/USD:ETH"]]}, {"description": "fetch last prices - inverse future", "method": "fetchLastPrices", "url": "https://dapi.binance.com/dapi/v1/ticker/price", "input": [null, {"defaultType": "future", "subType": "inverse"}]}], "fetchOHLCV": [{"description": "Fetch OHLCV - spot", "method": "fetchOHLCV", "url": "https://api.binance.com/api/v3/klines?interval=1m&limit=5&symbol=BTCUSDT&startTime=1699381234000", "input": ["BTC/USDT", "1m", 1699381234000, 5]}, {"description": "Fetch OHLCV - linear", "method": "fetchOHLCV", "url": "https://fapi.binance.com/fapi/v1/klines?interval=1m&limit=5&symbol=BTCUSDT&startTime=1699381234000", "input": ["BTC/USDT:USDT", "1m", 1699381234000, 5]}, {"description": "Fetch OHLCV - inverse", "method": "fetchOHLCV", "url": "https://dapi.binance.com/dapi/v1/klines?interval=1m&limit=5&symbol=BTCUSD_PERP&startTime=1699381234000&endTime=1699381533999", "input": ["BTC/USD:BTC", "1m", 1699381234000, 5]}, {"description": "Fetch OHLCV - option", "method": "fetchOHLCV", "url": "https://eapi.binance.com/eapi/v1/klines?interval=1m&limit=5&symbol=ETH-231229-800-C&startTime=1699381234000", "input": ["ETH/USDT:USDT-231229-800-C", "1m", 1699381234000, 5]}, {"description": "Fetch OHLCV - linear, price=mark", "method": "fetchOHLCV", "url": "https://fapi.binance.com/fapi/v1/markPriceKlines?interval=1m&limit=5&symbol=BTCUSDT&startTime=1699381234000", "input": ["BTC/USDT:USDT", "1m", 1699381234000, 5, {"price": "mark"}]}, {"description": "Fetch OHLCV - inverse, price=mark", "method": "fetchOHLCV", "url": "https://dapi.binance.com/dapi/v1/markPriceKlines?interval=1m&limit=5&symbol=BTCUSD_PERP&startTime=1699381234000&endTime=1699381533999", "input": ["BTC/USD:BTC", "1m", 1699381234000, 5, {"price": "mark"}]}, {"description": "Fetch OHLCV - linear, price=index", "method": "fetchOHLCV", "url": "https://fapi.binance.com/fapi/v1/indexPriceKlines?interval=1m&limit=5&pair=BTCUSDT&startTime=1699381234000", "input": ["BTC/USDT:USDT", "1m", 1699381234000, 5, {"price": "index"}]}, {"description": "Fetch OHLCV - inverse, price=index", "method": "fetchOHLCV", "url": "https://dapi.binance.com/dapi/v1/indexPriceKlines?interval=1m&limit=5&pair=BTCUSD&startTime=1699381234000&endTime=1699381533999", "input": ["BTC/USD:BTC", "1m", 1699381234000, 5, {"price": "index"}]}, {"description": "spot ohlcv", "method": "fetchOHLCV", "url": "https://api.binance.com/api/v3/klines?interval=1m&limit=500&symbol=BTCUSDT", "input": ["BTC/USDT"]}, {"description": "swap ohlcv", "method": "fetchOHLCV", "url": "https://fapi.binance.com/fapi/v1/klines?interval=1m&limit=500&symbol=BTCUSDT", "input": ["BTC/USDT:USDT"]}, {"description": "ohlcv with since and until should use maxLimit spot", "method": "fetchOHLCV", "url": "https://api.binance.com/api/v3/klines?interval=1h&limit=1500&symbol=BTCUSDT&startTime=*************&endTime=172**********", "input": ["BTC/USDT", "1h", *************, null, {"until": 172**********}]}, {"description": "ohlcv with since and until should use maxLimit swap", "method": "fetchOHLCV", "url": "https://fapi.binance.com/fapi/v1/klines?interval=1h&limit=1500&symbol=BTCUSDT&startTime=*************&endTime=172**********", "input": ["BTC/USDT:USDT", "1h", *************, null, {"until": 172**********}]}], "fetchTradingFees": [{"description": "Fetch Trading Fees - spot", "method": "fetchTradingFees", "url": "https://api.binance.com/sapi/v1/asset/tradeFee?timestamp=*************&recvWindow=10000&signature=a9795caccb458e83f728210d51abeb6c51d5e6b67ced6562a9f4839910e189fd", "input": [{"type": "spot"}]}, {"description": "Fetch Trading Fees - linear", "method": "fetchTradingFees", "url": "https://api.binance.com/fapi/v1/accountConfig?timestamp=*************&recvWindow=10000&signature=a9795caccb458e83f728210d51abeb6c51d5e6b67ced6562a9f4839910e189fd", "input": [{"type": "swap", "subType": "linear"}]}, {"description": "Fetch Trading Fees - inverse", "method": "fetchTradingFees", "url": "https://api.binance.com/dapi/v1/account?timestamp=*************&recvWindow=10000&signature=a9795caccb458e83f728210d51abeb6c51d5e6b67ced6562a9f4839910e189fd", "input": [{"type": "swap", "subType": "inverse"}]}], "fetchFundingRate": [{"description": "Fetch Funding Rate - linear", "method": "fetchFundingRate", "url": "https://api.binance.com/fapi/v1/premiumIndex?symbol=BTCUSDT", "input": ["BTC/USDT:USDT"]}, {"description": "Fetch Funding Rate - inverse", "method": "fetchFundingRate", "url": "https://api.binance.com/dapi/v1/premiumIndex?symbol=BTCUSD_PERP", "input": ["BTC/USD:BTC"]}, {"description": "fundingRate", "method": "fetchFundingRate", "url": "https://fapi.binance.com/fapi/v1/premiumIndex?symbol=BTCUSDT", "input": ["BTC/USDT:USDT"]}], "fetchLeverageTiers": [{"description": "Fetch Leverage Tiers - linear", "method": "fetchLeverageTiers", "url": "https://fapi.binance.com/fapi/v1/leverageBracket?timestamp=*************&recvWindow=10000&signature=a9795caccb458e83f728210d51abeb6c51d5e6b67ced6562a9f4839910e189fd", "input": [null, {"subType": "linear"}]}, {"description": "Fetch Leverage Tiers - inverse", "method": "fetchLeverageTiers", "url": "https://dapi.binance.com/dapi/v2/leverageBracket?timestamp=*************&recvWindow=10000&signature=a9795caccb458e83f728210d51abeb6c51d5e6b67ced6562a9f4839910e189fd", "input": [null, {"subType": "inverse"}]}, {"description": "Linear portfolio margin fetch leverage tiers", "method": "fetchLeverageTiers", "url": "https://papi.binance.com/papi/v1/um/leverageBracket?timestamp=1707376044709&recvWindow=10000&signature=248c0da5dc253c98bce350eb8c3d2c8f37781003baa5141838cbd2caa74a3636", "input": [null, {"portfolioMargin": true, "subType": "linear"}]}, {"description": "Inverse portfolio margin fetch leverage tiers", "method": "fetchLeverageTiers", "url": "https://papi.binance.com/papi/v1/cm/leverageBracket?timestamp=1707376139383&recvWindow=10000&signature=6e4a769ba3c1aceddccf243167e92a4a814366cfed3594edbf6aa91bce359ed4", "input": [null, {"portfolioMargin": true, "subType": "inverse"}]}], "fetchFundingRateHistory": [{"description": "Fetch Funding Rate History - linear", "method": "fetchFundingRateHistory", "url": "https://api.binance.com/fapi/v1/fundingRate?symbol=BTCUSDT", "input": ["BTC/USDT:USDT"]}, {"description": "Fetch Funding Rate History - inverse", "method": "fetchFundingRateHistory", "url": "https://api.binance.com/dapi/v1/fundingRate?symbol=BTCUSD_PERP", "input": ["BTC/USD:BTC"]}, {"description": "fundingRateHistory", "method": "fetchFundingRateHistory", "url": "https://fapi.binance.com/fapi/v1/fundingRate?symbol=BTCUSDT", "input": ["BTC/USDT:USDT"]}], "fetchFundingRates": [{"description": "Fetch Funding Rates - linear", "method": "fetchFundingRates", "url": "https://api.binance.com/fapi/v1/premiumIndex", "input": [null, {"subType": "linear"}]}, {"description": "Fetch Funding Rates - inverse", "method": "fetchFundingRates", "url": "https://api.binance.com/dapi/v1/premiumIndex", "input": [null, {"subType": "inverse"}]}], "fetchFundingHistory": [{"description": "Fetch Funding History - linear", "method": "fetchFundingHistory", "url": "https://api.binance.com/fapi/v1/income?timestamp=*************&incomeType=FUNDING_FEE&symbol=BTCUSDT&recvWindow=10000&signature=a9795caccb458e83f728210d51abeb6c51d5e6b67ced6562a9f4839910e189fd", "input": ["BTC/USDT:USDT"]}, {"description": "Fetch Funding History - inverse", "method": "fetchFundingHistory", "url": "https://api.binance.com/dapi/v1/income?timestamp=*************&incomeType=FUNDING_FEE&symbol=BTCUSD_PERP&recvWindow=10000&signature=a9795caccb458e83f728210d51abeb6c51d5e6b67ced6562a9f4839910e189fd", "input": ["BTC/USD:BTC"]}, {"description": "Linear swap portfolio margin fetch funding history", "method": "fetchFundingHistory", "url": "https://papi.binance.com/papi/v1/um/income?timestamp=1707450596925&incomeType=FUNDING_FEE&symbol=BTCUSDT&recvWindow=10000&signature=2b76b4aedcc0b567260c087b446b50146f6db538a00d4cb692b0aeb0f939b9d3", "input": ["BTC/USDT:USDT", null, null, {"portfolioMargin": true}]}, {"description": "Inverse swap portfolio margin fetch funding history", "method": "fetchFundingHistory", "url": "https://papi.binance.com/papi/v1/cm/income?timestamp=1707450694387&incomeType=FUNDING_FEE&symbol=ETHUSD_PERP&recvWindow=10000&signature=26f345820e4a993c6a6be8b6acc0b7fdcc98cdf2ed33ddf0c3f78cef382ea4e2", "input": ["ETH/USD:ETH", null, null, {"portfolioMargin": true}]}], "fetchTrades": [{"description": "Fetch Trades - spot", "method": "fetchTrades", "url": "https://api.binance.com/api/v3/aggTrades?limit=5&symbol=BTCUSDT&startTime=1699381234000&endTime=1699384834000", "input": ["BTC/USDT", 1699381234000, 5]}, {"description": "Fetch Trades - linear", "method": "fetchTrades", "url": "https://fapi.binance.com/fapi/v1/aggTrades?limit=5&symbol=BTCUSDT&startTime=1699381234000&endTime=1699384834000", "input": ["BTC/USDT:USDT", 1699381234000, 5]}, {"description": "Fetch Trades - inverse", "method": "fetchTrades", "url": "https://dapi.binance.com/dapi/v1/aggTrades?limit=5&symbol=BTCUSD_PERP&startTime=1699381234000&endTime=1699384834000", "input": ["BTC/USD:BTC", 1699381234000, 5]}, {"description": "Fetch Trades - option", "method": "fetchTrades", "url": "https://eapi.binance.com/eapi/v1/trades?limit=5&symbol=ETH-231229-800-C", "input": ["ETH/USDT:USDT-231229-800-C", 1699381234000, 5]}, {"description": "spot fetchTrades", "method": "fetchTrades", "url": "https://api.binance.com/api/v3/aggTrades?symbol=BTCUSDT", "input": ["BTC/USDT"]}, {"description": "swap fetchTrades", "method": "fetchTrades", "url": "https://fapi.binance.com/fapi/v1/aggTrades?symbol=BTCUSDT", "input": ["BTC/USDT:USDT"]}], "fetchPositionsRisk": [{"description": "Fetch Positions Risk - linear", "method": "fetchPositionsRisk", "url": "https://fapi.binance.com/fapi/v3/positionRisk?timestamp=*************&recvWindow=10000&signature=a9795caccb458e83f728210d51abeb6c51d5e6b67ced6562a9f4839910e189fd", "input": [null, {"subType": "linear"}]}, {"description": "Fetch Positions Risk - inverse", "method": "fetchPositionsRisk", "url": "https://dapi.binance.com/dapi/v1/positionRisk?timestamp=*************&recvWindow=10000&signature=a9795caccb458e83f728210d51abeb6c51d5e6b67ced6562a9f4839910e189fd", "input": [null, {"subType": "inverse"}]}, {"description": "Inverse portfolio margin fetch positions risk", "method": "fetchPositionsRisk", "url": "https://papi.binance.com/papi/v1/cm/positionRisk?timestamp=1707443702916&recvWindow=10000&signature=88c195bc372626397818eb714cae53388fdfb9baa32f997d0611075dde07bd4f", "input": [["ETH/USD:ETH"], {"portfolioMargin": true, "subType": "inverse"}]}, {"description": "Linear portfolio margin fetch positions risk", "method": "fetchPositionsRisk", "url": "https://papi.binance.com/papi/v1/um/positionRisk?timestamp=*************&recvWindow=10000&signature=c51d48deff54a226859b6fe4ce62a5982c1b3eec82255d211c6e67d6b3bd4f0f", "input": [["BTC/USDT:USDT"], {"portfolioMargin": true, "subType": "linear"}]}], "fetchAccountPositions": [{"description": "Fetch Account Positions - linear", "method": "fetchAccountPositions", "url": "https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=a9795caccb458e83f728210d51abeb6c51d5e6b67ced6562a9f4839910e189fd", "input": [null, {"subType": "linear"}]}, {"description": "Fetch Account Positions - linear", "method": "fetchAccountPositions", "url": "https://fapi.binance.com/fapi/v2/account?timestamp=*************&recvWindow=10000&signature=a9795caccb458e83f728210d51abeb6c51d5e6b67ced6562a9f4839910e189fd", "input": [null, {"subType": "linear", "useV2": true}]}, {"description": "<PERSON><PERSON> Account Positions - inverse", "method": "fetchAccountPositions", "url": "https://dapi.binance.com/dapi/v1/account?timestamp=*************&recvWindow=10000&signature=a9795caccb458e83f728210d51abeb6c51d5e6b67ced6562a9f4839910e189fd", "input": [null, {"subType": "inverse"}]}, {"description": "Linear portfolio margin fetch account positions", "method": "fetchAccountPositions", "url": "https://papi.binance.com/papi/v1/um/account?timestamp=*************&recvWindow=10000&signature=2d245f910f1fe4d4a5481bdfe529e9e1815cea7085e6dc4e1d7640e4e9d5bb12", "input": [null, {"portfolioMargin": true, "subType": "linear"}]}, {"description": "Inverse portfolio margin fetch account positions", "method": "fetchAccountPositions", "url": "https://papi.binance.com/papi/v1/cm/account?timestamp=*************&recvWindow=10000&signature=515b93a4c7fdcbcd02bae0a798484b0e064f871375dad2fff72e7e4c8a2da16c", "input": [null, {"portfolioMargin": true, "subType": "inverse"}]}, {"description": "account positions with filterClosed option", "method": "fetchAccountPositions", "url": "https://testnet.binancefuture.com/fapi/v3/account?timestamp=*************&filterClosed=true&recvWindow=10000&signature=d70e7146e12dff30106d51e77574cda0b75aa353c505746b1a78a5c086a7ac19", "input": [null, {"filterClosed": true}]}], "fetchOpenInterest": [{"description": "Fetch Open Interest - linear", "method": "fetchOpenInterest", "url": "https://fapi.binance.com/fapi/v1/openInterest?symbol=BTCUSDT", "input": ["BTC/USDT:USDT"]}, {"description": "Fetch Open Interest - inverse", "method": "fetchOpenInterest", "url": "https://dapi.binance.com/dapi/v1/openInterest?symbol=BTCUSD_PERP", "input": ["BTC/USD:BTC"]}], "fetchOpenInterestHistory": [{"description": "Fetch Open Interest History - linear", "method": "fetchOpenInterestHistory", "url": "https://fapi.binance.com/futures/data/openInterestHist?period=5m&symbol=BTCUSDT", "input": ["BTC/USDT:USDT"]}, {"description": "Fetch Open Interest History - inverse", "method": "fetchOpenInterestHistory", "url": "https://dapi.binance.com/futures/data/openInterestHist?period=5m&pair=BTCUSD_PERP&contractType=CURRENT_QUARTER", "input": ["BTC/USD:BTC"]}], "setPositionMode": [{"description": "Swap linear setPositionMode", "method": "setPositionMode", "url": "https://testnet.binancefuture.com/fapi/v1/positionSide/dual", "input": [true, null, {"subType": "linear"}], "output": "timestamp=1699440965441&dualSidePosition=true&recvWindow=10000&signature=500407a2dd13c20e6cef1a884fd1982fe494f2436761cd8c73c6de1aeeaf2a70"}, {"description": "Swap inverse setPositionMode", "method": "setPositionMode", "url": "https://fapi.binance.com/dapi/v1/positionSide/dual", "input": [true, null, {"subType": "inverse"}], "output": "timestamp=1699441011001&dualSidePosition=true&recvWindow=10000&signature=f932a4e8dd21ce1dcea84d8ab2fd9451c31b77c710d1adb9c51f54adc520cd90"}, {"description": "Swap linear portfolio margin set position mode", "method": "setPositionMode", "url": "https://papi.binance.com/papi/v1/um/positionSide/dual", "input": [true, null, {"portfolioMargin": true, "subType": "linear"}], "output": "timestamp=1707282494111&dualSidePosition=true&recvWindow=10000&signature=5895dd7f3c7bf056635cc297f9d6c92a4aa907e61d8f6e900b99d4b25407eab5"}, {"description": "Swap inverse portfolio margin set position mode", "method": "setPositionMode", "url": "https://papi.binance.com/papi/v1/cm/positionSide/dual", "input": [true, null, {"portfolioMargin": true, "subType": "inverse"}], "output": "timestamp=1707282567514&dualSidePosition=true&recvWindow=10000&signature=12b2fe3fb47a0ae0ef4ec810b754aba2223e7e20e1145c89ef7aa5c1dc6a4485"}], "fetchBidsAsks": [{"description": "Fetch Bids Asks spot with array of symbols", "method": "fetchBidsAsks", "url": "https://testnet.binance.vision/api/v3/ticker/bookTicker?symbols=%5B%22BTCUSDT%22%2C%22LTCUSDT%22%5D", "input": [["BTC/USDT", "LTC/USDT"]]}, {"description": "spot bidsasks", "method": "fetchBidsAsks", "url": "https://api.binance.com/api/v3/ticker/bookTicker?symbols=%5B%22BTCUSDT%22%2C%22ETHUSDT%22%5D", "input": [["BTC/USDT", "ETH/USDT"]]}], "borrowIsolatedMargin": [{"description": "Borrow isolated margin", "method": "borrowIsolatedMargin", "url": "https://api.binance.com/sapi/v1/margin/borrow-repay", "input": ["LTC/USDT", "USDT", 1], "output": "timestamp=1704884479247&asset=USDT&amount=1&symbol=LTCUSDT&isIsolated=TRUE&type=BORROW&recvWindow=10000&signature=f173dc9490b5b255ac77fbf3bd0a53586cc768d2364cd0c439d51084ea1c4f98"}], "repayIsolatedMargin": [{"description": "Repay isolated margin", "method": "repayIsolatedMargin", "url": "https://api.binance.com/sapi/v1/margin/borrow-repay", "input": ["LTC/USDT", "USDT", 1], "output": "timestamp=1704884544319&asset=USDT&amount=1&symbol=LTCUSDT&isIsolated=TRUE&type=REPAY&recvWindow=10000&signature=aa81a97855a7512780aebf078a1a149360dcbee2111ab8e416881ff0e8c933a8"}], "borrowCrossMargin": [{"description": "borrow cross margin", "method": "borrowCrossMargin", "url": "https://api.binance.com/sapi/v1/margin/borrow-repay", "input": ["USDT", 1], "output": "timestamp=*************&asset=USDT&amount=1&isIsolated=FALSE&type=BORROW&recvWindow=10000&signature=7decbb81d0e7d0b0d553496893c7a36438ddcba418c9437eaa384a541325843b"}, {"description": "Portfolio margin borrow cross margin", "method": "borrowCrossMargin", "url": "https://papi.binance.com/papi/v1/marginLoan", "input": ["USDT", 5, {"portfolioMargin": true}], "output": "timestamp=1707369605273&asset=USDT&amount=5&recvWindow=10000&signature=2c17935d2d5997eb83c7299eddd116373949583a9b06b6f431affeda5fab5f3b"}], "repayCrossMargin": [{"description": "repay cross margin", "method": "repayCrossMargin", "url": "https://api.binance.com/sapi/v1/margin/borrow-repay", "input": ["USDT", 1], "output": "timestamp=1704884654418&asset=USDT&amount=1&isIsolated=FALSE&type=REPAY&recvWindow=10000&signature=22d5b8db751dc8cc40771a7e6999e863c5630b9108c15dca22c7bebd6b07ca17"}, {"description": "Portfolio margin repay cross margin", "method": "repayCrossMargin", "url": "https://papi.binance.com/papi/v1/repayLoan", "input": ["USDT", 5, {"portfolioMargin": true}], "output": "timestamp=1707370050475&asset=USDT&amount=5&recvWindow=10000&signature=25ac0418c9c3e2830f2b231da826c7d5deb816f58d0b31f654fe0f80c63561b7"}, {"description": "Portfolio margin repay cross margin with an alternative endpoint", "method": "repayCrossMargin", "url": "https://papi.binance.com/papi/v1/margin/repay-debt", "input": ["USDC", 10, {"method": "papiPostMarginRepayDebt", "papi": true}], "output": "timestamp=1727170759618&asset=USDC&amount=10&recvWindow=10000&signature=b9f6e7773fb5639f4d7c33556841287091ca35278560528f3bbebd54e00d4c1a"}], "fetchMyDustTrades": [{"description": "Fetch My Dust Trades", "method": "fetchMyDustTrades", "url": "https://api.binance.com/sapi/v1/asset/dribblet?timestamp=*************&recvWindow=10000&signature=7decbb81d0e7d0b0d553496893c7a36438ddcba418c9437eaa384a541325843b", "input": [null, null, null]}, {"description": "Fetch My Dust Trades - margin", "method": "fetchMyDustTrades", "url": "https://api.binance.com/sapi/v1/asset/dribblet?timestamp=*************&accountType=MARGIN&recvWindow=10000&signature=7decbb81d0e7d0b0d553496893c7a36438ddcba418c9437eaa384a541325843b", "input": [null, null, null, {"type": "margin"}]}], "fetchTransfers": [{"description": "fetch transfers", "method": "fetchTransfers", "url": "https://api.binance.com/sapi/v1/asset/transfer?timestamp=*************&type=MAIN_UMFUTURE&recvWindow=10000&signature=a8edc2192088ef3aff0fd3687e8c222eb2f45ba83cd9a4c589f561202466be8f", "input": []}, {"description": "internal transfers", "method": "fetchTransfers", "url": "https://api.binance.com/sapi/v1/pay/transactions?timestamp=*************&recvWindow=10000&signature=b9fe81491ea542195571f617280e2bef60e5971926be6ce646e4bb25ac18d80e", "input": [null, null, null, {"internal": true}]}], "fetchMyLiquidations": [{"description": "fetch swap liquidations", "method": "fetchMyLiquidations", "url": "https://fapi.binance.com/fapi/v1/forceOrders?timestamp=*************&autoCloseType=LIQUIDATION&symbol=LTCUSDT&recvWindow=10000&signature=d31c57ea2e96d101a4370284c30007a0095490c6aa367406912843abf2ffb315", "input": ["LTC/USDT:USDT"]}, {"description": "Spot margin portfolio margin fetch my liquidations", "method": "fetchMyLiquidations", "url": "https://papi.binance.com/papi/v1/margin/forceOrders?timestamp=1707878056633&recvWindow=10000&signature=c1391185fe60a089f125a9e6b36015ee98a04d4e7e507ad3cd24143c479b6d5d", "input": [null, null, null, {"portfolioMargin": true}]}, {"description": "Linear swap portfolio margin fetch my liquidations", "method": "fetchMyLiquidations", "url": "https://papi.binance.com/papi/v1/um/forceOrders?timestamp=1707878995904&autoCloseType=LIQUIDATION&recvWindow=10000&signature=6ced6b47b0c81862f3713d5830784fc4897bdfac50bc357907d1e6a489cd83cd", "input": ["BTC/USDT:USDT", null, null, {"portfolioMargin": true}]}, {"description": "Inverse swap portfolio margin fetch my liquidations", "method": "fetchMyLiquidations", "url": "https://papi.binance.com/papi/v1/cm/forceOrders?timestamp=1707879051093&autoCloseType=LIQUIDATION&recvWindow=10000&signature=46ebc9bfc482820fb8246d4643a2b33c0733ab15c6c1efd7a852b5f2992dc638", "input": ["ETH/USD:ETH", null, null, {"portfolioMargin": true}]}], "fetchOrderBook": [{"description": "fetch spot orderbook", "method": "fetchOrderBook", "url": "https://api.binance.com/api/v3/depth?symbol=BTCUSDT&limit=5", "input": ["BTC/USDT", 5]}, {"description": "swap linear orderbook", "method": "fetchOrderBook", "url": "https://fapi.binance.com/fapi/v1/depth?symbol=BTCUSDT&limit=5", "input": ["BTC/USDT:USDT", 5]}, {"description": "inverse swap", "method": "fetchOrderBook", "url": "https://dapi.binance.com/dapi/v1/depth?symbol=BTCUSD_PERP&limit=5", "input": ["BTC/USD:BTC", 5]}, {"description": "spot orderbook", "method": "fetchOrderBook", "url": "https://api.binance.com/api/v3/depth?symbol=BTCUSDT", "input": ["BTC/USDT"]}, {"description": "swap orderbook", "method": "fetchOrderBook", "url": "https://fapi.binance.com/fapi/v1/depth?symbol=BTCUSDT", "input": ["BTC/USDT:USDT"]}], "fetchWithdrawals": [{"description": "fetch USDT withrawals", "method": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://api.binance.com/sapi/v1/capital/withdraw/history?timestamp=1706095147177&coin=USDT&startTime=1661357136827&endTime=1669133136827&limit=1&recvWindow=10000&signature=6bd39d7a3add1e878df038eec054d6f662328cc4d402a9a67b8207c57392acf6", "input": ["USDT", 1661357136827, 1]}], "fetchTradingFee": [{"description": "spot trading fee", "method": "fetchTradingFee", "url": "https://api.binance.com/sapi/v1/asset/tradeFee?timestamp=1706095306815&symbol=BTCUSDT&recvWindow=10000&signature=8ec3dcaea78dbbaac7afcf27309eb9c29161363afd7e17ee911d0b8ee6a957b2", "input": ["BTC/USDT"]}, {"description": "Linear swap fetch trading fee", "method": "fetchTradingFee", "url": "https://testnet.binancefuture.com/fapi/v1/commissionRate?timestamp=1707777580650&symbol=BTCUSDT&recvWindow=10000&signature=c54b25d565695b045323ba483f32e6f07829bc0c19e07c0cb78e54ff415978ee", "input": ["BTC/USDT:USDT"]}, {"description": "Inverse swap fetch trading fee", "method": "fetchTradingFee", "url": "https://testnet.binancefuture.com/dapi/v1/commissionRate?timestamp=1707777670336&symbol=ETHUSD_PERP&recvWindow=10000&signature=8c0727960b0de153cb7dbc166fe3e6dfe3d473508909a4a6bfeda06f97dcacbf", "input": ["ETH/USD:ETH"]}, {"description": "Linear portfolio margin fetch trading fee", "method": "fetchTradingFee", "url": "https://papi.binance.com/papi/v1/um/commissionRate?timestamp=1707777345377&symbol=BTCUSDT&recvWindow=10000&signature=73a49e69c333a1ae5435c90f54eb4a07321c4a33cb7088696dbb4745aeb64db8", "input": ["BTC/USDT:USDT", {"portfolioMargin": true}]}, {"description": "Inverse portfolio margin fetch trading fee", "method": "fetchTradingFee", "url": "https://papi.binance.com/papi/v1/cm/commissionRate?timestamp=1707777389698&symbol=ETHUSD_PERP&recvWindow=10000&signature=cb22b12993cd61b5ce6db703964ae74e375a100da50d21f81f8c8951370eb639", "input": ["ETH/USD:ETH", {"portfolioMargin": true}]}], "fetchBorrowInterest": [{"description": "Cross fetch borrow interest", "method": "fetchBorrowInterest", "url": "https://api.binance.com/sapi/v1/margin/interestHistory?timestamp=1707551014113&asset=USDT&recvWindow=10000&signature=dff73a2270259eeac6da202942c96aa6880a346b5c6c3d09280fe766de203fa0", "input": ["USDT"]}, {"description": "Isolated fetch borrow interest", "method": "fetchBorrowInterest", "url": "https://api.binance.com/sapi/v1/margin/interestHistory?timestamp=1707551070428&asset=USDT&isolatedSymbol=BTCUSDT&recvWindow=10000&signature=5cf6a6b08309f58688b75986e55266363093030d30632b4d3840ecdaf906dfc8", "input": ["USDT", "BTC/USDT"]}, {"description": "Portfolio margin fetch borrow interest", "method": "fetchBorrowInterest", "url": "https://papi.binance.com/papi/v1/margin/marginInterestHistory?timestamp=1707550913636&asset=USDT&recvWindow=10000&signature=67a8ccc93c868c13044e059a799ab7929d10000f40def3a9be7ec87719e22a25", "input": ["USDT", null, null, null, {"portfolioMargin": true}]}], "fetchOpenOrder": [{"description": "Linear swap fetch open order", "method": "fetchOpenOrder", "url": "https://testnet.binancefuture.com/fapi/v1/openOrder?timestamp=1707893171707&symbol=BTCUSDT&orderId=3697213934&recvWindow=10000&signature=d9063e928c20216bab7d6b72d810bb1772a5914ba223afb1d99223d2519c4495", "input": ["3697213934", "BTC/USDT:USDT"]}, {"description": "Inverse swap fetch open order", "method": "fetchOpenOrder", "url": "https://testnet.binancefuture.com/dapi/v1/openOrder?timestamp=1707893671838&symbol=BTCUSD_PERP&orderId=597368542&recvWindow=10000&signature=867e778a63cf33ae958df0344823f01ae4bfbe5f2d061e68d1047c829454dfa3", "input": ["597368542", "BTC/USD:BTC"]}, {"description": "Linear portfolio margin fetch open order", "method": "fetchOpenOrder", "url": "https://papi.binance.com/papi/v1/um/openOrder?timestamp=1707894060424&symbol=BTCUSDT&orderId=264895013409&recvWindow=10000&signature=4d0be95bef943410edf40dab1e4adcc3f9719967ea8f2a6bd49852b14afe4303", "input": ["264895013409", "BTC/USDT:USDT", {"portfolioMargin": true}]}, {"description": "Inverse portfolio margin fetch open order", "method": "fetchOpenOrder", "url": "https://papi.binance.com/papi/v1/cm/openOrder?timestamp=1707894369330&symbol=ETHUSD_PERP&orderId=71790316950&recvWindow=10000&signature=483eb1e2eed64593d415a9e3bdc1a5d473655e9092a469d06a53f613f01b4be0", "input": ["71790316950", "ETH/USD:ETH", {"portfolioMargin": true}]}, {"description": "Linear portfolio margin conditional fetch open order", "method": "fetchOpenOrder", "url": "https://papi.binance.com/papi/v1/um/conditional/openOrder?timestamp=1707894696072&symbol=BTCUSDT&strategyId=4084339&recvWindow=10000&signature=22ae21d4e6cca439461b2deae7750b973f8b307025e2124e779e93eb86bcb1cd", "input": ["4084339", "BTC/USDT:USDT", {"portfolioMargin": true, "stop": true}]}, {"description": "Linear portfolio margin trigger fetch open order", "method": "fetchOpenOrder", "url": "https://papi.binance.com/papi/v1/um/conditional/openOrder?timestamp=1707894696072&symbol=BTCUSDT&strategyId=4084339&recvWindow=10000&signature=22ae21d4e6cca439461b2deae7750b973f8b307025e2124e779e93eb86bcb1cd", "input": ["4084339", "BTC/USDT:USDT", {"portfolioMargin": true, "trigger": true}]}, {"description": "Inverse portfolio margin conditional fetch open order", "method": "fetchOpenOrder", "url": "https://papi.binance.com/papi/v1/cm/conditional/openOrder?timestamp=1707894959489&symbol=ETHUSD_PERP&strategyId=1423501&recvWindow=10000&signature=83acda806cf655ebbcecabaa76d6c024a30daf49ad9d6bceeb939807d22964a7", "input": ["1423501", "ETH/USD:ETH", {"portfolioMargin": true, "stop": true}]}, {"description": "Inverse portfolio margin trigger fetch open order", "method": "fetchOpenOrder", "url": "https://papi.binance.com/papi/v1/cm/conditional/openOrder?timestamp=1707894959489&symbol=ETHUSD_PERP&strategyId=1423501&recvWindow=10000&signature=83acda806cf655ebbcecabaa76d6c024a30daf49ad9d6bceeb939807d22964a7", "input": ["1423501", "ETH/USD:ETH", {"portfolioMargin": true, "trigger": true}]}], "fetchPositionMode": [{"description": "Fetch Position Mode for linear markets", "method": "fetchPositionMode", "url": "https://testnet.binancefuture.com/fapi/v1/positionSide/dual?timestamp=1708957008104&recvWindow=10000&signature=9c06e6b4e748672ca010b656010a1a5ab35be45b8c0484c2ff1f7a672d30a83b", "input": [null, {"subType": "linear"}]}, {"description": "Fetch Position Mode for inverse markets", "method": "fetchPositionMode", "url": "https://testnet.binancefuture.com/dapi/v1/positionSide/dual?timestamp=1708957069083&recvWindow=10000&signature=8fca8b68435e3fb8a1abc2ccf6bbf47acfee2df637c6fcd80ca466441f12a2f4", "input": [null, {"subType": "inverse"}]}, {"description": "test with a linear symbol", "method": "fetchPositionMode", "url": "https://testnet.binancefuture.com/fapi/v1/positionSide/dual?timestamp=1708957224845&recvWindow=10000&signature=55a79022aa888b93211125c8ef8682102a3b6009bdbf9bad8cd3966b441429d8", "input": ["BTC/USDT:USDT"]}], "fetchTradingLimits": [{"disabled": true, "reason": "fetchMarkets performs multiple requests and that's not supported by static-tests", "description": "Fetch Trading Limits with a symbol array argument", "method": "fetchTradingLimits", "url": "https://testnet.binancefuture.com/dapi/v1/exchangeInfo", "input": [["BTC/USDT", "ETH/USDT"]]}, {"disabled": true, "description": "Fetch Trading Limits with no input arguments", "method": "fetchTradingLimits", "url": "https://testnet.binancefuture.com/dapi/v1/exchangeInfo", "input": []}], "fetchCanceledAndClosedOrders": [{"description": "fetch canceled and closed orders", "method": "fetchCanceledAndClosedOrders", "url": "https://testnet.binance.vision/api/v3/allOrders?timestamp=1708947070267&symbol=XRPUSDT&recvWindow=10000&signature=e3492363f286490a957b9500d81d89ea16bd7d21460150051055c5642b2c2ae1", "input": ["XRP/USDT"]}], "fetchLeverage": [{"description": "fetch leverage", "method": "fetchLeverage", "url": "https://testnet.binancefuture.com/fapi/v1/symbolConfig?timestamp=1709136468103&recvWindow=10000&signature=b24aa2014e8f5e24af708947eac62196161221c58f303a67e6755f725a7ab650", "input": ["BTC/USDT:USDT"]}], "fetchLeverages": [{"description": "Linear swap fetch all set leverages", "method": "fetchLeverages", "url": "https://testnet.binancefuture.com/fapi/v1/symbolConfig?timestamp=1709365967532&recvWindow=10000&signature=0ba690bc39cc0f098fc8adfab9a10e991a239b02b59e92c7cc88281c74b7ec1e", "input": []}], "fetchMarginModes": [{"description": "Fetch Margin Modes with linear subType and symbol list", "method": "fetchMarginModes", "url": "https://testnet.binancefuture.com/fapi/v1/symbolConfig?timestamp=*************&recvWindow=10000&signature=e03e51e9eaf6ccadbe8171e52d588d13e389861804104ee7948d768609713aaf", "input": [["BTC/USDT:USDT"], {"subType": "linear"}]}, {"description": "Fetch Margin Modes with inverse subType", "method": "fetchMarginModes", "url": "https://testnet.binancefuture.com/dapi/v1/account?timestamp=*************&recvWindow=10000&signature=3e5f0e80eddbfc30770b5bc9e9981900696de5e3236b0f9c27fe59255769c489", "input": [null, {"subType": "inverse"}]}], "fetchMarginMode": [{"description": "linear swap fetch margin mode", "method": "fetchMarginMode", "url": "https://fapi.binance.com/fapi/v1/symbolConfig?timestamp=*************&symbol=BTCUSDT&recvWindow=10000&signature=1413a17133ec9b4fddda726366f68c69f6b7328eef9dc40879718e24540b78b1", "input": ["BTC/USDT:USDT"]}, {"description": "inverse swap fetch margin mode", "method": "fetchMarginMode", "url": "https://dapi.binance.com/dapi/v1/account?timestamp=*************&recvWindow=10000&signature=f4a9d536dff966ecab5d3499be85bab1422fb9526db45715b38aaabb6d415000", "input": ["BTC/USD:BTC"]}], "fetchTime": [{"description": "fetchTime", "method": "fetchTime", "url": "https://api.binance.com/api/v3/time", "input": []}], "fetchTicker": [{"description": "Swap ticker", "method": "fetchTicker", "url": "https://fapi.binance.com/fapi/v1/ticker/24hr?symbol=BTCUSDT", "input": ["BTC/USDT:USDT"]}, {"description": "spot ticker", "method": "fetchTicker", "url": "https://api.binance.com/api/v3/ticker/24hr?symbol=BTCUSDT", "input": ["BTC/USDT"]}], "fetchOption": [{"description": "Fetch an option contract", "method": "fetchOption", "url": "https://eapi.binance.com/eapi/v1/ticker?symbol=BTC-241227-80000-C", "input": ["BTC/USDT:USDT-241227-80000-C"]}], "fetchMarginAdjustmentHistory": [{"description": "Fetch margin adjustment history with since, limit, until defined", "method": "fetchMarginAdjustmentHistory", "url": "https://testnet.binancefuture.com/fapi/v1/positionMargin/history?timestamp=1711146115814&symbol=XRPUSDT&startTime=1710460800000&limit=3&endTime=1711146090000&recvWindow=10000&signature=92bf15fd223dd6acf7e2c1b2ac116fbaf1441f54128691283ddc981b8c993b8f", "input": ["XRP/USDT:USDT", null, 1710460800000, 3, {"until": "1711146090000"}]}, {"description": "Fetch margin adjustment history", "method": "fetchMarginAdjustmentHistory", "url": "https://testnet.binancefuture.com/fapi/v1/positionMargin/history?timestamp=1711146515429&symbol=XRPUSDT&recvWindow=10000&signature=2ef24563a8dd25319ed4d7269866c117e086c0104ce37d7822c5b4e05620efe0", "input": ["XRP/USDT:USDT"]}], "addMargin": [{"description": "add margin", "method": "add<PERSON><PERSON>gin", "url": "https://testnet.binancefuture.com/fapi/v1/positionMargin", "input": ["XRP/USDT:USDT", 5.243432], "output": "timestamp=1712047896974&type=1&symbol=XRPUSDT&amount=5.2&recvWindow=10000&signature=ab7e4525180db9134418a3b410029cc1a055a4542cb4ebe1d60cc6f0134a2fce"}, {"description": "add small amount of margin", "method": "add<PERSON><PERSON>gin", "url": "https://testnet.binancefuture.com/fapi/v1/positionMargin", "input": ["XRP/USDT:USDT", 0.2], "output": "timestamp=1712047853481&type=1&symbol=XRPUSDT&amount=0.2&recvWindow=10000&signature=09954975fbbf5de734a82d26144d77d6929802345e183c212e6fc49722e34696"}], "reduceMargin": [{"description": "reduce margin", "method": "reduce<PERSON><PERSON>gin", "url": "https://testnet.binancefuture.com/fapi/v1/positionMargin", "input": ["XRP/USDT:USDT", 5.243432], "output": "timestamp=1712047923923&type=2&symbol=XRPUSDT&amount=5.2&recvWindow=10000&signature=db336b746bf1266fbe2f3d8887e105ce714f1a8ebbab733610722ee22b9a6fe7"}, {"description": "reduce small amount of margin", "method": "reduce<PERSON><PERSON>gin", "url": "https://testnet.binancefuture.com/fapi/v1/positionMargin", "input": ["XRP/USDT:USDT", 0.2], "output": "timestamp=1712047946547&type=2&symbol=XRPUSDT&amount=0.2&recvWindow=10000&signature=e8c53af6d974c388ca6ff5ec820a4c2ac813fcbaec55b1a50c60779da56be00b"}], "fetchConvertQuote": [{"description": "fetch convert quote", "method": "fetchConvertQuote", "url": "https://api.binance.com/sapi/v1/convert/getQuote", "input": ["USDC", "USDT", 3], "output": "timestamp=1713535948592&fromAsset=USDC&toAsset=USDT&fromAmount=3&recvWindow=10000&signature=e47dafb115d42ceaf14772ee0a2eac2f0a1df50419c676171e21910f46115934"}], "fetchConvertTrade": [{"description": "Fetch a conversion trade by the id and currency code", "method": "fetchConvertTrade", "url": "https://api.binance.com/sapi/v1/asset/convert-transfer/queryByPage?timestamp=1713422758560&tranId=118263615991&startTime=1713336358514&endTime=1713422758514&asset=BUSD&recvWindow=10000&signature=d00a936e5d2e76c0bc4463429dc0023c09bd49d71c28962bf19737a56fc78323", "input": ["118263615991", "BUSD", {"startTime": 1713336358514, "endTime": 1713422758514}]}], "fetchConvertTradeHistory": [{"description": "Fetch the conversion trade history", "method": "fetchConvertTradeHistory", "url": "https://api.binance.com/sapi/v1/convert/tradeFlow?timestamp=1713569838825&startTime=1713336537740&endTime=1713422937740&recvWindow=10000&signature=09af452a10ecf4a9eb0499682df79d4c1a86a2397effda8339a3ff542265ceb2", "input": [null, 1713336537740, null, {"endTime": 1713422937740}]}, {"description": "Fetch the conversion trade history for BUSD conversions", "method": "fetchConvertTradeHistory", "url": "https://api.binance.com/sapi/v1/asset/convert-transfer/queryByPage?timestamp=1713569909204&startTime=1713336537740&endTime=1713422937740&asset=BUSD&recvWindow=10000&signature=ee6908b431a1da834ae717ec872ea3f3c6a557e8adeb8a0266fa470aecfbfe99", "input": ["BUSD", 1713336537740, null, {"endTime": 1713422937740}]}], "createConvertTrade": [{"description": "convert usdc to usdt", "method": "createConvertTrade", "url": "https://api.binance.com/sapi/v1/convert/acceptQuote", "input": ["844f131de9994643a0b6ce7805d08cfb", "USDC", "USDT", 4], "output": "timestamp=1713536455384&quoteId=844f131de9994643a0b6ce7805d08cfb&recvWindow=10000&signature=39e6351a5061356643aa48f6e335b6d3c903710206f2543bd9443af07885948b"}], "fetchIsolatedBorrowRate": [{"description": "fetchIsolatedBorrowRate", "method": "fetchIsolatedBorrowRate", "url": "https://api.binance.com/sapi/v1/margin/isolatedMarginData?timestamp=1714664548704&symbol=BTCUSDT&recvWindow=10000&signature=6bed3101b0139a52896c5ac2cba8c9c16371153e133ecdef782c7cd398bb470f", "input": ["BTC/USDT"]}], "fetchIsolatedBorrowRates": [{"description": "fetchIsolatedBorrowRates", "method": "fetchIsolatedBorrowRates", "url": "https://api.binance.com/sapi/v1/margin/isolatedMarginData?timestamp=1714664627834&recvWindow=10000&signature=9b4fa07568a385ee7a642e31375bb1c6e0640d1491508b2f5bbd4e36ce31f117", "input": []}], "fetchPremiumIndexOHLCV": [{"description": "fetchPremiumIndexOHLCV linear", "method": "fetchPremiumIndexOHLCV", "url": "https://fapi.binance.com/fapi/v1/premiumIndexKlines?interval=1m&limit=500&symbol=BTCUSDT", "input": ["BTC/USDT:USDT"]}, {"description": "fetchPremiumIndexOHLCV inverse", "method": "fetchPremiumIndexOHLCV", "url": "https://dapi.binance.com/dapi/v1/premiumIndexKlines?interval=1m&limit=500&symbol=BTCUSD_PERP", "input": ["BTC/USD:BTC"]}], "withdraw": [{"description": "withdraw using trc20 network", "method": "withdraw", "url": "https://api.binance.com/sapi/v1/capital/withdraw/apply", "input": ["USDT", 10.34332, "TKRxCKea88ZvHeGsXEY33CqAQHPDqcAw4N", null, {"network": "TRC20"}], "output": "timestamp=1718724487672&coin=USDT&address=TKRxCKea88ZvHeGsXEY33CqAQHPDqcAw4N&amount=10.34332&network=TRX&recvWindow=10000&signature=d9a2ecdfd7a207b2e7770b45a1971a3ea6bfcdb9c0796691b6528a7a7b77fe02"}], "fetchCurrencies": [{"disabled": true, "description": "fetchCurrencies, temnporary disabled because of multi promise", "method": "fetchCurrencies", "url": "https://api.binance.com/sapi/v1/capital/config/getall?timestamp=1721628239072&recvWindow=10000&signature=sig", "input": []}], "fetchStatus": [{"description": "fetchStatus", "method": "fetchStatus", "url": "https://api.binance.com/sapi/v1/system/status", "input": []}], "fetchDepositAddress": [{"description": "fetchDepositAddress", "method": "fetchDepositAddress", "url": "https://api.binance.com/sapi/v1/capital/deposit/address?timestamp=1721628239072&recvWindow=10000&signature=sig&coin=BTC", "input": ["BTC"]}], "fetchTransactionFees": [{"description": "fetchTransactionFees", "method": "fetchTransactionFees", "url": "https://api.binance.com/sapi/v1/capital/config/getall?timestamp=1721628239072&recvWindow=10000&signature=sig", "input": []}], "fetchDepositWithdrawFees": [{"description": "fetchDepositWithdrawFees", "method": "fetchDepositWithdrawFees", "url": "https://api.binance.com/sapi/v1/capital/config/getall?timestamp=1721628239072&recvWindow=10000&signature=sig", "input": []}], "fetchPosition": [{"description": "fetchPosition", "method": "fetchPosition", "url": "https://eapi.binance.com/eapi/v1/position?timestamp=1721628239072&recvWindow=10000&signature=sig&symbol=ETH-231229-800-C", "input": ["ETH/USDT:USDT-231229-800-C"]}], "fetchOptionPositions": [{"description": "fetchOptionPositions", "method": "fetchOptionPositions", "url": "https://eapi.binance.com/eapi/v1/position?timestamp=1721628239072&recvWindow=10000&signature=sig", "input": []}], "fetchSettlementHistory": [{"description": "fetchSettlementHistory", "method": "fetchSettlementHistory", "url": "https://eapi.binance.com/eapi/v1/exerciseHistory?underlying=ETHUSDT", "input": ["ETH/USDT:USDT-231229-800-C"]}], "fetchMySettlementHistory": [{"description": "fetchMySettlementHistory", "method": "fetchMySettlementHistory", "url": "https://eapi.binance.com/eapi/v1/exerciseRecord?timestamp=1721628239072&recvWindow=10000&signature=sig&symbol=ETH-231229-800-C", "input": ["ETH/USDT:USDT-231229-800-C"]}], "fetchBorrowRateHistory": [{"description": "fetchBorrowRateHistory", "method": "fetchBorrowRateHistory", "url": "https://eapi.binance.com/sapi/v1/margin/interestRateHistory?timestamp=1721628239072&recvWindow=10000&signature=sig&asset=BTC&limit=93", "input": ["BTC"]}], "fetchFundingIntervals": [{"description": "swap linear funding interval", "method": "fetchFundingIntervals", "url": "https://fapi.binance.com/fapi/v1/fundingInfo", "input": [["BTC/USDT:USDT"]]}, {"description": "swap inverse funding intervals", "method": "fetchFundingIntervals", "url": "https://dapi.binance.com/dapi/v1/fundingInfo", "input": [["BTC/USD:BTC"]]}], "fetchFundingInterval": [{"description": "swap linear fetch funding interval", "method": "fetchFundingInterval", "url": "https://fapi.binance.com/fapi/v1/fundingInfo", "input": ["BTC/USDT:USDT"]}, {"description": "swap inverse fetch funding interval", "method": "fetchFundingInterval", "url": "https://dapi.binance.com/dapi/v1/fundingInfo", "input": ["BTC/USD:BTC"]}], "fetchLongShortRatioHistory": [{"description": "inverse swap fetch long short ratio history", "method": "fetchLongShortRatioHistory", "url": "https://dapi.binance.com/futures/data/globalLongShortAccountRatio?period=1d&pair=BTCUSD", "input": ["BTC/USD:BTC"]}, {"description": "linear swap fetch long short ratio history", "method": "fetchLongShortRatioHistory", "url": "https://fapi.binance.com/futures/data/globalLongShortAccountRatio?period=1d&symbol=BTCUSDT", "input": ["BTC/USDT:USDT"]}], "fetchAllGreeks": [{"description": "fetchAllGreeks with a single symbol", "method": "fetchAllGreeks", "url": "https://eapi.binance.com/eapi/v1/mark?symbol=ETH-231229-800-C", "input": [["ETH/USDT:USDT-231229-800-C"]]}]}}