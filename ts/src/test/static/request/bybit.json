{"exchange": "bybit", "skipKeys": ["transferId", "orderLinkId", "timestamp"], "outputType": "json", "methods": {"fetchCurrencies": [{"description": "fetchCurrencies", "method": "fetchCurrencies", "url": "https://api.bybit.com/v5/asset/coin/query-info?", "input": [], "output": null}], "createOrder": [{"description": "spot stopLossPrice order", "method": "createOrder", "url": "https://api-testnet.bybit.com/v5/order/create", "input": ["LTC/USDT", "market", "sell", 0.2, null, {"reduceOnly": true, "stopLossPrice": 90}], "output": "{\"symbol\":\"LTCUSDT\",\"side\":\"Sell\",\"orderType\":\"Market\",\"orderFilter\":\"tpslOrder\",\"category\":\"spot\",\"marketUnit\":\"baseCoin\",\"qty\":\"0.2\",\"triggerDirection\":2,\"triggerPrice\":\"90\",\"reduceOnly\":true}"}, {"description": "spot market buy order", "method": "createOrder", "url": "https://api-testnet.bybit.com/v5/order/create", "input": ["XRP/USDT", "market", "buy", 10], "output": "{\"symbol\":\"XRPUSDT\",\"side\":\"Buy\",\"orderType\":\"Market\",\"category\":\"spot\",\"marketUnit\":\"baseCoin\",\"qty\":\"10\"}"}, {"description": "spot market order with stopPrice", "method": "createOrder", "url": "https://api-testnet.bybit.com/v5/order/create", "input": ["XRP/USDT", "market", "buy", 1, 10, {"stopPrice": 55}], "output": "{\"symbol\":\"XRPUSDT\",\"side\":\"Buy\",\"orderType\":\"Market\",\"category\":\"spot\",\"qty\":\"10\",\"triggerPrice\":\"55\",\"orderFilter\":\"StopOrder\"}"}, {"description": "spot limit buy with triggerPrice", "method": "createOrder", "url": "https://api.bybit.com/v5/order/create", "input": ["BTC/USDT", "limit", "buy", 0.1, 36000, {"triggerPrice": 36001}], "output": "{\"symbol\":\"BTCUSDT\",\"side\":\"Buy\",\"orderType\":\"Limit\",\"category\":\"spot\",\"qty\":\"0.1\",\"price\":\"36000\",\"triggerPrice\":\"36001\",\"orderFilter\":\"StopOrder\"}"}, {"description": "Swap limit order", "method": "createOrder", "url": "https://api-testnet.bybit.com/v5/order/create", "input": ["ADA/USDT:USDT", "limit", "buy", 50, 0.1], "output": "{\"symbol\":\"ADAUSDT\",\"side\":\"Buy\",\"orderType\":\"Limit\",\"category\":\"linear\",\"qty\":\"50\",\"price\":\"0.1\"}"}, {"description": "Spot limit order", "method": "createOrder", "url": "https://api-testnet.bybit.com/v5/order/create", "input": ["ADA/USDT", "limit", "buy", 50, 0.1], "output": "{\"symbol\":\"ADAUSDT\",\"side\":\"Buy\",\"orderType\":\"Limit\",\"category\":\"spot\",\"qty\":\"50\",\"price\":\"0.1\"}"}, {"description": "Spot market order", "method": "createOrder", "url": "https://api-testnet.bybit.com/v5/order/create", "input": ["ADA/USDT", "market", "buy", 1, 10], "output": "{\"symbol\":\"ADAUSDT\",\"side\":\"Buy\",\"orderType\":\"Market\",\"category\":\"spot\",\"qty\":\"10\"}"}, {"description": "swap limit buy with triggerPrice and triggerDirection above", "method": "createOrder", "url": "https://api.bybit.com/v5/order/create", "input": ["BTC/USDT:USDT", "limit", "buy", 0.001, 37000, {"triggerPrice": 37000, "triggerDirection": "above"}], "output": "{\"symbol\":\"BTCUSDT\",\"side\":\"Buy\",\"orderType\":\"Limit\",\"category\":\"linear\",\"qty\":\"0.001\",\"price\":\"37000\",\"triggerDirection\":1,\"triggerPrice\":\"37000\"}"}, {"description": "swap limit buy with triggerPrice and triggerDirection below", "method": "createOrder", "url": "https://api.bybit.com/v5/order/create", "input": ["BTC/USDT:USDT", "limit", "buy", 0.001, 35000, {"triggerPrice": 35000, "triggerDirection": "below"}], "output": "{\"symbol\":\"BTCUSDT\",\"side\":\"Buy\",\"orderType\":\"Limit\",\"category\":\"linear\",\"qty\":\"0.001\",\"price\":\"35000\",\"triggerDirection\":2,\"triggerPrice\":\"35000\"}"}, {"description": "Spot limit buy with postOnly", "method": "createOrder", "url": "https://api-testnet.bybit.com/v5/order/create", "input": ["LTC/USDT", "limit", "buy", 0.1, 60, {"postOnly": true}], "output": "{\"symbol\":\"LTCUSDT\",\"side\":\"Buy\",\"orderType\":\"Limit\",\"category\":\"spot\",\"qty\":\"0.1\",\"price\":\"60\",\"timeInForce\":\"PostOnly\"}"}, {"description": "Swap limit sell with reduceOnly and postOnly", "method": "createOrder", "url": "https://api-testnet.bybit.com/v5/order/create", "input": ["LTC/USDT:USDT", "limit", "sell", 0.1, 100, {"reduceOnly": true, "postOnly": true}], "output": "{\"symbol\":\"LTCUSDT\",\"side\":\"Sell\",\"orderType\":\"Limit\",\"category\":\"linear\",\"qty\":\"0.1\",\"price\":\"100\",\"timeInForce\":\"PostOnly\",\"reduceOnly\":true}"}, {"description": "Swap limit sell with takeProfitPrice(Type2) while setting the endpoint to privatePostV5OrderCreate", "method": "createOrder", "url": "https://api-testnet.bybit.com/v5/order/create", "input": ["LTC/USDT:USDT", "limit", "sell", 0.1, 100, {"takeProfitPrice": 110, "method": "privatePostV5OrderCreate"}], "output": "{\"symbol\":\"LTCUSDT\",\"side\":\"Sell\",\"orderType\":\"Limit\",\"category\":\"linear\",\"qty\":\"0.1\",\"price\":\"100\",\"triggerDirection\":1,\"triggerPrice\":\"110\",\"reduceOnly\":true}"}, {"description": "Swap limit sell with stopLossPrice(Type2) while setting the endpoint to privatePostV5OrderCreate", "method": "createOrder", "url": "https://api-testnet.bybit.com/v5/order/create", "input": ["LTC/USDT:USDT", "limit", "sell", 0.16, 51, {"stopLossPrice": 56, "method": "privatePostV5OrderCreate"}], "output": "{\"symbol\":\"LTCUSDT\",\"side\":\"Sell\",\"orderType\":\"Limit\",\"category\":\"linear\",\"qty\":\"0.1\",\"price\":\"51\",\"triggerDirection\":2,\"triggerPrice\":\"56\",\"reduceOnly\":true}"}, {"description": "Swap limit sell with stopLossPrice(Type2)", "method": "createOrder", "url": "https://api-testnet.bybit.com/v5/position/trading-stop", "input": ["BTC/USDT:USDT", "limit", "sell", 1, 81000, {"stopLossPrice": 82000}], "output": "{\"symbol\":\"BTCUSDT\",\"stopLoss\":\"82000\",\"tpslMode\":\"Partial\",\"slOrderType\":\"Limit\",\"slLimitPrice\":\"81000\",\"slSize\":\"1\",\"category\":\"linear\"}"}, {"description": "Swap limit sell with takeProfitPrice(Type2)", "method": "createOrder", "url": "https://api-testnet.bybit.com/v5/position/trading-stop", "input": ["BTC/USDT:USDT", "limit", "sell", 1, 99000, {"takeProfitPrice": 98000}], "output": "{\"symbol\":\"BTCUSDT\",\"takeProfit\":\"98000\",\"tpslMode\":\"Partial\",\"tpOrderType\":\"Limit\",\"tpLimitPrice\":\"99000\",\"tpSize\":\"1\",\"category\":\"linear\"}"}, {"description": "Opening position with sl + tp attached (type 3)", "method": "createOrder", "url": "https://api-testnet.bybit.com/v5/order/create", "input": ["TRX/USDT:USDT", "market", "buy", 1, null, {"takeProfit": {"stopPrice": 1}, "stopLoss": {"stopPrice": 0.04}}], "output": "{\"symbol\":\"TRXUSDT\",\"side\":\"Buy\",\"orderType\":\"Market\",\"category\":\"linear\",\"qty\":\"1\",\"stopLoss\":\"0.04\",\"takeProfit\":\"1\"}"}, {"description": "Create swap trigger order with stop<PERSON>oss attached", "method": "createOrder", "url": "https://api-testnet.bybit.com/v5/order/create", "input": ["BTC/USDT:USDT", "limit", "buy", 0.001, 41000, {"triggerPrice": "40000", "triggerDirection": "above", "stopLoss": "39500"}], "output": "{\"symbol\":\"BTCUSDT\",\"side\":\"Buy\",\"orderType\":\"Limit\",\"category\":\"linear\",\"qty\":\"0.001\",\"price\":\"41000\",\"triggerDirection\":1,\"triggerPrice\":\"40000\",\"stopLoss\":\"39500\"}"}, {"description": "Spot market buy order with createMarketBuyOrderRequiresPrice set to false on the testnet (classic only)", "method": "createOrder", "url": "https://api-testnet.bybit.com/v5/order/create", "options": {"enableUnifiedAccount": false}, "input": ["BTC/USDT", "market", "buy", 20, null, {"createMarketBuyOrderRequiresPrice": false}], "output": "{\"symbol\":\"BTCUSDT\",\"side\":\"Buy\",\"orderType\":\"Market\",\"category\":\"spot\",\"qty\":\"20\"}"}, {"description": "Swap trailingAmount order", "method": "createOrder", "url": "https://api-testnet.bybit.com/v5/position/trading-stop", "input": ["BTC/USDT:USDT", "market", "sell", 0.001, null, {"trailingAmount": "1000"}], "output": "{\"symbol\":\"BTCUSDT\",\"category\":\"linear\",\"trailingStop\":\"1000\"}"}, {"description": "Swap inverse market buy order", "method": "createOrder", "url": "https://api-testnet.bybit.com/v5/order/create", "input": ["BTC/USD:BTC", "market", "buy", 1], "output": "{\"symbol\":\"BTCUSD\",\"side\":\"Buy\",\"orderType\":\"Market\",\"category\":\"inverse\",\"qty\":\"1\"}"}, {"description": "Swap inverse market sell order", "method": "createOrder", "url": "https://api-testnet.bybit.com/v5/order/create", "input": ["BTC/USD:BTC", "market", "sell", 1, null, {"reduceOnly": true}], "output": "{\"symbol\":\"BTCUSD\",\"side\":\"Sell\",\"orderType\":\"Market\",\"category\":\"inverse\",\"qty\":\"1\",\"reduceOnly\":true}"}, {"description": "Create market buy with base amount (UTA only)", "method": "createOrder", "url": "https://api-testnet.bybit.com/v5/order/create", "input": ["LTC/USDT", "market", "buy", 0.2], "output": "{\"symbol\":\"LTCUSDT\",\"side\":\"Buy\",\"orderType\":\"Market\",\"category\":\"spot\",\"marketUnit\":\"baseCoin\",\"qty\":\"0.2\"}"}, {"description": "Create market sell with cost (UTA only)", "method": "createOrder", "url": "https://api-testnet.bybit.com/v5/order/create", "input": ["LTC/USDT", "market", "sell", 0.2, 10], "output": "{\"symbol\":\"LTCUSDT\",\"side\":\"Sell\",\"orderType\":\"Market\",\"category\":\"spot\",\"marketUnit\":\"quoteCoin\",\"qty\":\"2\"}"}, {"description": "market sell with cost in params (UTA only)", "method": "createOrder", "url": "https://api-testnet.bybit.com/v5/order/create", "input": ["LTC/USDT", "market", "sell", 0, null, {"cost": 3}], "output": "{\"symbol\":\"LTCUSDT\",\"side\":\"Sell\",\"orderType\":\"Market\",\"category\":\"spot\",\"marketUnit\":\"quoteCoin\",\"qty\":\"3\"}"}, {"description": "Create swap limit order with tp+sl limit orders attached", "method": "createOrder", "url": "https://api-testnet.bybit.com/v5/order/create", "input": ["LTC/USDT:USDT", "limit", "buy", 1, 50, {"takeProfit": {"stopPrice": 100, "price": 90}, "stopLoss": {"stopPrice": 50, "price": 49}}], "output": "{\"symbol\":\"LTCUSDT\",\"side\":\"Buy\",\"orderType\":\"Limit\",\"category\":\"linear\",\"qty\":\"1\",\"price\":\"50\",\"stopLoss\":\"50\",\"tpslMode\":\"Partial\",\"slOrderType\":\"Limit\",\"slLimitPrice\":\"49\",\"takeProfit\":\"100\",\"tpOrderType\":\"Limit\",\"tpLimitPrice\":\"90\"}"}, {"description": "spot order with weird price and amount", "method": "createOrder", "url": "https://api-testnet.bybit.com/v5/order/create", "input": ["LTC/USDT", "limit", "buy", 0.1444444234234234, 60.423423423], "output": "{\"symbol\":\"LTCUSDT\",\"side\":\"Buy\",\"orderType\":\"Limit\",\"price\":\"60.42\",\"category\":\"spot\",\"qty\":\"0.14444\"}"}, {"description": "swap order with weird price and amount", "method": "createOrder", "url": "https://api-testnet.bybit.com/v5/order/create", "input": ["ADA/USDT:USDT", "limit", "buy", 1.1444444234234235, 60.423423423], "output": "{\"symbol\":\"ADAUSDT\",\"side\":\"Buy\",\"orderType\":\"Limit\",\"price\":\"60.4234\",\"category\":\"linear\",\"qty\":\"1\"}"}, {"description": "market option order", "method": "createOrder", "url": "https://api-testnet.bybit.com/v5/order/create", "input": ["BTC-25OCT24-46000-C", "market", "buy", 1], "output": "{\"symbol\":\"BTC-25OCT24-46000-C\",\"side\":\"Buy\",\"orderType\":\"Market\",\"orderLinkId\":\"8b2c958a6470a4da\",\"category\":\"option\",\"qty\":\"1\"}"}, {"description": "limit option order", "method": "createOrder", "url": "https://api-testnet.bybit.com/v5/order/create", "input": ["BTC-25OCT24-46000-C", "limit", "buy", 1, 15000], "output": "{\"symbol\":\"BTC-25OCT24-46000-C\",\"side\":\"Buy\",\"orderType\":\"Limit\",\"orderLinkId\":\"1b152a3bc495b57c\",\"price\":\"15000\",\"category\":\"option\",\"qty\":\"1\"}"}, {"description": "swap market sell reduceOnly order", "method": "createOrder", "url": "https://api-testnet.bybit.com/v5/order/create", "input": ["ADA/USDT:USDT", "market", "sell", 81, null, {"reduceOnly": true}], "output": "{\"symbol\":\"ADAUSDT\",\"side\":\"Sell\",\"orderType\":\"Market\",\"category\":\"linear\",\"qty\":\"81\",\"reduceOnly\":true}"}, {"description": "swap market buy hedged order", "method": "createOrder", "url": "https://api-testnet.bybit.com/v5/order/create", "input": ["ADA/USDT:USDT", "market", "buy", 10, null, {"hedged": true}], "output": "{\"symbol\":\"ADAUSDT\",\"side\":\"Buy\",\"orderType\":\"Market\",\"category\":\"linear\",\"qty\":\"10\",\"positionIdx\":1}"}, {"description": "swap market sell hedged order", "method": "createOrder", "url": "https://api-testnet.bybit.com/v5/order/create", "input": ["ADA/USDT:USDT", "market", "sell", 10, null, {"hedged": true}], "output": "{\"symbol\":\"ADAUSDT\",\"side\":\"Sell\",\"orderType\":\"Market\",\"category\":\"linear\",\"qty\":\"10\",\"positionIdx\":2}"}, {"description": "swap hedged reduceOnly sell order", "method": "createOrder", "url": "https://api-testnet.bybit.com/v5/order/create", "input": ["ADA/USDT:USDT", "market", "sell", 10, null, {"hedged": true, "reduceOnly": true}], "output": "{\"symbol\":\"ADAUSDT\",\"side\":\"Sell\",\"orderType\":\"Market\",\"category\":\"linear\",\"qty\":\"10\",\"positionIdx\":1}"}, {"description": "swap hedged buy reduceOnly order", "method": "createOrder", "url": "https://api-testnet.bybit.com/v5/order/create", "input": ["ADA/USDT:USDT", "market", "buy", 10, null, {"hedged": true, "reduceOnly": true}], "output": "{\"symbol\":\"ADAUSDT\",\"side\":\"Buy\",\"orderType\":\"Market\",\"category\":\"linear\",\"qty\":\"10\",\"positionIdx\":2}"}], "createMarketBuyOrderWithCost": [{"description": "order with cost, using a decimal amount", "method": "createMarketBuyOrderWithCost", "url": "https://api.bybit.com/v5/order/create", "input": ["DOGE/USDT", 12.45], "output": "{\"symbol\":\"DOGEUSDT\",\"side\":\"Buy\",\"orderType\":\"Market\",\"category\":\"spot\",\"qty\":\"12.45\"}"}, {"description": "Spot market buy with createMarketBuyOrderWithCost", "method": "createMarketBuyOrderWithCost", "url": "https://api-testnet.bybit.com/v5/order/create", "input": ["LTC/USDT", 10], "output": "{\"symbol\":\"LTCUSDT\",\"side\":\"Buy\",\"orderType\":\"Market\",\"category\":\"spot\",\"qty\":\"10\"}"}], "createMarketSellOrderWithCost": [{"description": "market sell order with cost (UTA only)", "method": "createMarkeSellOrderWithCost", "url": "https://api-testnet.bybit.com/v5/order/create", "input": ["LTC/USDT", 5], "output": "{\"symbol\":\"LTCUSDT\",\"side\":\"Sell\",\"orderType\":\"Market\",\"category\":\"spot\",\"marketUnit\":\"quoteCoin\",\"qty\":\"5\"}"}], "createOrders": [{"description": "Spot create orders", "method": "createOrders", "url": "https://api-testnet.bybit.com/v5/order/create-batch", "input": [[{"symbol": "LTC/USDT", "amount": 0.1, "side": "buy", "type": "limit", "price": 50}, {"symbol": "LTC/USDT", "amount": 0.11, "side": "sell", "type": "limit", "price": 70}]], "output": "{\"category\":\"spot\",\"request\":[{\"symbol\":\"LTCUSDT\",\"side\":\"Buy\",\"orderType\":\"Limit\",\"qty\":\"0.1\",\"price\":\"50\"},{\"symbol\":\"LTCUSDT\",\"side\":\"Sell\",\"orderType\":\"Limit\",\"qty\":\"0.11\",\"price\":\"70\"}]}"}, {"description": "Swap create orders", "method": "createOrders", "url": "https://api-testnet.bybit.com/v5/order/create-batch", "input": [[{"symbol": "LTC/USDT:USDT", "amount": 0.1, "side": "buy", "type": "limit", "price": 60}, {"symbol": "LTC/USDT:USDT", "amount": 0.11, "side": "buy", "type": "limit", "price": 61}]], "output": "{\"category\":\"linear\",\"request\":[{\"symbol\":\"LTCUSDT\",\"side\":\"Buy\",\"orderType\":\"Limit\",\"qty\":\"0.1\",\"price\":\"60\"},{\"symbol\":\"LTCUSDT\",\"side\":\"Buy\",\"orderType\":\"Limit\",\"qty\":\"0.1\",\"price\":\"61\"}]}"}], "cancelOrder": [{"description": "Swap cancelOrder", "method": "cancelOrder", "url": "https://api-testnet.bybit.com/v5/order/cancel", "input": ["8caeea8c-cf80-4b06-83e8-bb7df45fc122", "LTC/USDT:USDT"], "output": "{\"symbol\":\"LTCUSDT\",\"orderId\":\"8caeea8c-cf80-4b06-83e8-bb7df45fc122\",\"category\":\"linear\"}"}, {"description": "Spot cancelOrder", "method": "cancelOrder", "url": "https://api-testnet.bybit.com/v5/order/cancel", "input": ["1513125485218108928", "LTC/USDT"], "output": "{\"symbol\":\"LTCUSDT\",\"orderFilter\":\"Order\",\"orderId\":\"1513125485218108928\",\"category\":\"spot\"}"}, {"description": "Spot cancel a stop order", "method": "cancelOrder", "url": "https://api-testnet.bybit.com/v5/order/cancel", "input": ["1599966662050972416", "BTC/USDT", {"stop": true}], "output": "{\"symbol\":\"BTCUSDT\",\"orderFilter\":\"StopOrder\",\"orderId\":\"1599966662050972416\",\"category\":\"spot\"}"}], "cancelOrders": [{"description": "Swap cancelOrders", "method": "cancelOrders", "url": "https://api-testnet.bybit.com/v5/order/cancel-batch", "input": [["8caeea8c-cf80-4b06-83e8-bb7df45fc122"], "LTC/USDT:USDT"], "output": "{\"category\":\"linear\",\"request\":[{\"symbol\":\"LTCUSDT\",\"orderId\":\"8caeea8c-cf80-4b06-83e8-bb7df45fc122\"}]}"}, {"description": "Spot cancelOrders", "method": "cancelOrders", "url": "https://api-testnet.bybit.com/v5/order/cancel-batch", "input": [["1513125485218108928"], "LTC/USDT"], "output": "{\"category\":\"spot\",\"request\":[{\"symbol\":\"LTCUSDT\",\"orderId\":\"1513125485218108928\"}]}"}, {"description": "cancelOrders by clientOrderId", "method": "cancelOrders", "url": "https://api-testnet.bybit.com/v5/order/cancel-batch", "input": [[], "LTC/USDT:USDT", {"clientOrderIds": ["test123"]}], "output": "{\"category\":\"linear\",\"request\":[{\"symbol\":\"LTCUSDT\",\"orderLinkId\":\"test123\"}]}"}], "cancelAllOrders": [{"description": "Swap cancel all open orders", "method": "cancelAllOrders", "url": "https://api-testnet.bybit.com/v5/order/cancel-all", "input": ["BTC/USDT:USDT"], "output": "{\"symbol\":\"BTCUSDT\",\"category\":\"linear\"}"}, {"description": "Swap cancel all open stop orders", "method": "cancelAllOrders", "url": "https://api-testnet.bybit.com/v5/order/cancel-all", "input": ["BTC/USDT:USDT", {"stop": true}], "output": "{\"symbol\":\"BTCUSDT\",\"category\":\"linear\",\"orderFilter\":\"StopOrder\"}"}, {"description": "Spot cancel all open stop orders", "method": "cancelAllOrders", "url": "https://api-testnet.bybit.com/v5/order/cancel-all", "input": ["BTC/USDT", {"stop": true}], "output": "{\"symbol\":\"BTCUSDT\",\"category\":\"spot\",\"orderFilter\":\"StopOrder\"}"}], "cancelOrdersForSymbols": [{"description": "cancelOrdersForSymbols", "method": "cancelOrdersForSymbols", "url": "https://api-testnet.bybit.com/v5/order/cancel-batch", "input": [[{"id": "da3d893d-8668-4482-a2ec-c3d1414695c3", "symbol": "LTC/USDT:USDT"}, {"id": "da03221b-f231-49c2-9b93-e1d10ed470c2", "symbol": "ADA/USDT:USDT"}]], "output": "{\"category\":\"linear\",\"request\":[{\"symbol\":\"LTCUSDT\",\"orderId\":\"da3d893d-8668-4482-a2ec-c3d1414695c3\"},{\"symbol\":\"ADAUSDT\",\"orderId\":\"da03221b-f231-49c2-9b93-e1d10ed470c2\"}]}"}], "editOrders": [{"description": "Spot edit orders", "method": "editOrders", "url": "https://api-testnet.bybit.com/v5/order/amend-batch", "input": [[{"id": "2e472521-031f-40f5-a8cb-bc3efb86ce0f", "symbol": "LTC/USDT", "amount": 0.1, "side": "buy", "type": "limit", "price": 50}, {"id": "3e472521-031f-40f5-a8cb-bc3efb86ce0f", "symbol": "LTC/USDT", "amount": 0.11, "side": "sell", "type": "limit", "price": 70}]], "output": "{\"category\":\"spot\",\"request\":[{\"orderId\":\"2e472521-031f-40f5-a8cb-bc3efb86ce0f\",\"symbol\":\"LTCUSDT\",\"qty\":\"0.1\",\"price\":\"50\"},{\"orderId\":\"3e472521-031f-40f5-a8cb-bc3efb86ce0f\",\"symbol\":\"LTCUSDT\",\"qty\":\"0.11\",\"price\":\"70\"}]}"}, {"description": "Swap edit orders", "method": "editOrders", "url": "https://api-testnet.bybit.com/v5/order/amend-batch", "input": [[{"id": "2e472521-031f-40f5-a8cb-bc3efb86ce0f", "symbol": "LTC/USDT:USDT", "amount": 0.1, "side": "buy", "type": "limit", "price": 60}, {"id": "3e472521-031f-40f5-a8cb-bc3efb86ce0f", "symbol": "LTC/USDT:USDT", "amount": 0.11, "side": "buy", "type": "limit", "price": 61}]], "output": "{\"category\":\"linear\",\"request\":[{\"orderId\":\"2e472521-031f-40f5-a8cb-bc3efb86ce0f\",\"symbol\":\"LTCUSDT\",\"qty\":\"0.1\",\"price\":\"60\"},{\"orderId\":\"3e472521-031f-40f5-a8cb-bc3efb86ce0f\",\"symbol\":\"LTCUSDT\",\"qty\":\"0.1\",\"price\":\"61\"}]}"}], "fetchOpenOrders": [{"description": "Spot fetchOpenOrders", "method": "fetchOpenOrders", "url": "https://api-testnet.bybit.com/v5/order/realtime?symbol=LTCUSDT&category=spot", "input": ["LTC/USDT", null, null]}, {"description": "Swap fetchOpenOrders", "method": "fetchOpenOrders", "url": "https://api-testnet.bybit.com/v5/order/realtime?symbol=LTCUSDT&category=linear", "input": ["LTC/USDT", null, null, {"type": "swap"}]}, {"description": "Spot fetch open trigger orders", "method": "fetchOpenOrders", "url": "https://api-testnet.bybit.com/v5/order/realtime?symbol=BTCUSDT&category=spot&orderFilter=StopOrder", "input": ["BTC/USDT", null, null, {"stop": true}]}], "fetchClosedOrders": [{"description": "Swap fetchClosedOrders", "method": "fetchClosedOrders", "url": "https://api-testnet.bybit.com/v5/order/history?category=linear&orderStatus=Filled", "input": []}], "fetchCanceledOrders": [{"description": "Spot fetchCanceledOrders", "method": "fetchCanceledOrders", "url": "https://api-testnet.bybit.com/v5/order/history?category=linear&orderStatus=Cancelled", "input": []}], "fetchMyTrades": [{"description": "Spot fetchMyTrades", "method": "fetchMyTrades", "url": "https://api-testnet.bybit.com/v5/execution/list?execType=Trade&symbol=LTCUSDT&category=spot", "input": ["LTC/USDT"]}, {"description": "Swap fetchMyTrades", "method": "fetchMyTrades", "url": "https://api-testnet.bybit.com/v5/execution/list?execType=Trade&symbol=LTCUSDT&category=linear", "input": ["LTC/USDT:USDT"]}], "fetchPositions": [{"description": "Swap fetchPositions", "method": "fetchPositions", "url": "https://api-testnet.bybit.com/v5/position/list?settleCoin=USDT&category=linear&limit=200", "input": []}], "fetchPosition": [{"description": "Swap fetch an inverse swap position", "method": "fetchPosition", "url": "https://api-testnet.bybit.com/v5/position/list?symbol=BTCUSD&category=inverse", "input": ["BTC/USD:BTC"]}], "fetchBalance": [{"description": "fetch default balance", "method": "fetchBalance", "url": "https://api-testnet.bybit.com/v5/account/wallet-balance?accountType=UNIFIED", "input": []}, {"description": "fetch funding balance", "method": "fetchBalance", "url": "https://api.bybit.com/v5/asset/transfer/query-account-coins-balance?accountType=FUND", "input": [{"type": "funding"}]}, {"description": "inverse balance should use unified when account is > UTA 1.0", "method": "fetchBalance", "options": {"unifiedMarginStatus": 5}, "url": "https://api-testnet.bybit.com/v5/account/wallet-balance?accountType=UNIFIED", "input": [{"subType": "inverse"}]}, {"description": "fetch funding balance using options", "method": "fetchBalance", "options": {"fetchBalance": {"defaultType": "fund"}}, "url": "https://api.bybit.com/v5/asset/transfer/query-account-coins-balance?accountType=FUND", "input": []}, {"description": "fetch funding balance using options: funding", "method": "fetchBalance", "options": {"fetchBalance": {"defaultType": "funding"}}, "url": "https://api.bybit.com/v5/asset/transfer/query-account-coins-balance?accountType=FUND", "input": []}, {"description": "fetch inverse balance", "method": "fetchBalance", "options": {"unifiedMarginStatus": 3}, "url": "https://api.bybit.com/v5/account/wallet-balance?accountType=CONTRACT", "input": [{"type": "swap", "subType": "inverse"}]}], "setLeverage": [{"description": "Set leverage swap linear", "method": "setLeverage", "url": "https://api-testnet.bybit.com/v5/position/set-leverage", "input": [6, "LTC/USDT:USDT"], "output": "{\"symbol\":\"LTCUSDT\",\"buyLeverage\":\"6\",\"sellLeverage\":\"6\",\"category\":\"linear\"}"}, {"description": "Set leverage inverse", "method": "setLeverage", "url": "https://api-testnet.bybit.com/v5/position/set-leverage", "input": [6, "BTC/USD:BTC"], "output": "{\"symbol\":\"BTCUSD\",\"buyLeverage\":\"6\",\"sellLeverage\":\"6\",\"category\":\"inverse\"}"}], "fetchDeposits": [{"description": "Default fetchDeposits", "method": "fetchDeposits", "url": "https://api-testnet.bybit.com/v5/asset/deposit/query-record?", "input": []}], "fetchLedger": [{"description": "fetchLedger using uta 1.0", "options": {"unifiedMarginStatus": 4}, "method": "fetchLedger", "url": "https://api-testnet.bybit.com/v5/account/contract-transaction-log?", "input": [null, null, null, {"subType": "inverse"}], "output": null}, {"description": "fetchLedger (inverse) using uta 2.0", "method": "fetchLedger", "url": "https://api-testnet.bybit.com/v5/account/transaction-log?", "input": [null, null, null, {"subType": "inverse"}], "output": null}, {"description": "<PERSON><PERSON><PERSON>edger", "method": "fetchLedger", "url": "https://api-testnet.bybit.com/v5/account/transaction-log?", "input": []}, {"description": "Ledger from inverse wallet", "options": {"unifiedMarginStatus": 3}, "method": "fetchLedger", "url": "https://api-testnet.bybit.com/v5/account/contract-transaction-log?", "input": [null, null, null, {"subType": "inverse"}]}], "fetchOrder": [{"description": "spot", "method": "fetchOrder", "url": "https://api.bybit.com/v5/order/realtime?symbol=LTCUSDT&orderId=1777987504537082112&category=spot", "input": ["1777987504537082112", "LTC/USDT", {"acknowledged": true}]}, {"description": "linear swap", "method": "fetchOrder", "url": "https://api.bybit.com/v5/order/realtime?symbol=LTCUSDT&orderId=b04e654c-22fe-4fad-b9f9-d116eabd68b3&category=linear", "input": ["b04e654c-22fe-4fad-b9f9-d116eabd68b3", "LTC/USDT:USDT", {"acknowledged": true}]}, {"description": "inverse swap", "method": "fetchOrder", "url": "https://api.bybit.com/v5/order/realtime?symbol=LTCUSD&orderId=117d831c-2e8c-4bcf-b362-3e9c95addc4e&category=inverse", "input": ["117d831c-2e8c-4bcf-b362-3e9c95addc4e", "LTC/USD:LTC", {"acknowledged": true}]}], "fetchTransfers": [{"description": "Fetch USDT transfers", "method": "fetchTransfers", "url": "https://api-testnet.bybit.com/v5/asset/transfer/query-inter-transfer-list?coin=USDT", "input": ["USDT"]}], "transfer": [{"description": "Unified to funding transfer", "method": "transfer", "url": "https://api-testnet.bybit.com/v5/asset/transfer/inter-transfer", "input": ["USDT", 1, "unified", "funding"], "output": "{\"transferId\":\"22942eaa-4231-47a4-8f05-0a6a6d8d221e\",\"fromAccountType\":\"UNIFIED\",\"toAccountType\":\"FUND\",\"coin\":\"USDT\",\"amount\":\"1\"}"}], "setPositionMode": [{"description": "Set position Mode to hedge mode", "method": "setPositionMode", "url": "https://api-testnet.bybit.com/v5/position/switch-mode", "input": [true, "BTC/USDT:USDT"], "output": "{\"mode\":3,\"symbol\":\"BTCUSDT\",\"category\":\"linear\"}"}, {"description": "Set position mode to one-way mode", "method": "setPositionMode", "url": "https://api-testnet.bybit.com/v5/position/switch-mode", "input": [false, "BTC/USDT:USDT"], "output": "{\"mode\":0,\"symbol\":\"BTCUSDT\",\"category\":\"linear\"}"}], "setMarginMode": [{"description": "set margin mode to isolated margin", "method": "setMarginMode", "url": "https://api-testnet.bybit.com/v5/account/set-margin-mode", "input": ["isolated", "BTC/USDT:USDT"], "output": "{\"setMarginMode\":\"ISOLATED_MARGIN\"}"}, {"description": "Set margin mode to cross margin", "method": "setMarginMode", "url": "https://api-testnet.bybit.com/v5/account/set-margin-mode", "input": ["cross", "BTC/USDT:USDT"], "output": "{\"setMarginMode\":\"REGULAR_MARGIN\"}"}], "fetchTickers": [{"description": "fetch options tickers", "method": "fetchTickers", "url": "https://api.bybit.com/v5/market/tickers?category=option&baseCoin=BTC", "input": [null, {"type": "option"}], "output": null}, {"description": "Fetch option tickers with the code parameter set to BTC", "method": "fetchTickers", "url": "https://api.bybit.com/v5/market/tickers?category=option&baseCoin=BTC&code=BTC", "input": [null, {"type": "option", "code": "BTC"}]}, {"description": "Fetch option tickers with the code parameter set to SOL", "method": "fetchTickers", "url": "https://api.bybit.com/v5/market/tickers?category=option&baseCoin=SOL&code=SOL", "input": [null, {"type": "option", "code": "SOL"}]}, {"description": "Fetch tickers for a BTC option", "method": "fetchTickers", "url": "https://api.bybit.com/v5/market/tickers?category=option&baseCoin=BTC", "input": [["BTC/USDT:USDT-250530-70000-C"]]}, {"description": "Fetch tickers for an ETH option", "method": "fetchTickers", "url": "https://api.bybit.com/v5/market/tickers?category=option&baseCoin=ETH", "input": [["ETH/USDT:USDT-250530-1700-P"]]}, {"description": "Fetch tickers for a SOL option", "method": "fetchTickers", "url": "https://api.bybit.com/v5/market/tickers?category=option&baseCoin=SOL", "input": [["SOL/USDT:USDT-250530-90-P"]]}, {"description": "Conflicting id with type in params", "method": "fetchTickers", "url": "https://api-testnet.bybit.com/v5/market/tickers?category=spot", "input": [["BTCUSDT"], {"type": "spot"}]}, {"description": "Default conflicting id to swap", "method": "fetchTickers", "url": "https://api-testnet.bybit.com/v5/market/tickers?category=linear", "input": [["BTCUSDT"]]}, {"description": "exchange default", "method": "fetchTickers", "url": "https://api.bybit.com/v5/market/tickers?category=linear", "input": []}, {"description": "spot with option.defaultType", "options": {"defaultType": "spot"}, "method": "fetchTickers", "url": "https://api.bybit.com/v5/market/tickers?category=spot", "input": []}, {"description": "subtype:linear", "method": "fetchTickers", "url": "https://api.bybit.com/v5/market/tickers?category=linear", "input": [null, {"subType": "linear"}]}, {"description": "subtype:inverse", "method": "fetchTickers", "url": "https://api.bybit.com/v5/market/tickers?category=inverse", "input": [null, {"subType": "inverse"}]}, {"description": "Fetch tickers of multiple spot symbols", "method": "fetchTickers", "url": "https://api.bybit.com/v5/market/tickers?category=spot", "input": [["BTC/USDT", "LTC/USDT"]]}, {"description": "Fetch tickers of multiple linear swap symbols", "method": "fetchTickers", "url": "https://api.bybit.com/v5/market/tickers?category=linear", "input": [["BTC/USDT:USDT", "LTC/USDT:USDT"]]}], "fetchOHLCV": [{"description": "basic call", "method": "fetchOHLCV", "url": "https://api.bybit.com/v5/market/kline?symbol=BTCUSDT&limit=200&interval=1&category=spot", "input": ["BTC/USDT"]}, {"description": "limit and interval set", "method": "fetchOHLCV", "url": "https://api.bybit.com/v5/market/kline?symbol=BTCUSDT&limit=5&interval=60&category=spot", "input": ["BTC/USDT", "1h", null, 5]}, {"description": "since and until set", "method": "fetchOHLCV", "url": "https://api.bybit.com/v5/market/kline?symbol=BTCUSDT&start=1704067200000&limit=200&end=1704067800000&interval=1&category=spot", "input": ["BTC/USDT", "1m", 1704067200000, null, {"until": 1704067800000}]}], "fetchOpenOrder": [{"description": "Spot fetch open order", "options": {"defaultType": "spot"}, "method": "fetchOpenOrder", "url": "https://api-testnet.bybit.com/v5/order/realtime?category=spot&orderId=1620279018815488768", "input": ["1620279018815488768"]}, {"description": "Swap fetch open order", "method": "fetchOpenOrder", "url": "https://api-testnet.bybit.com/v5/order/realtime?symbol=BTCUSDT&category=linear&orderId=90e3a7b9-20dc-4d04-9259-a565135dbf69", "input": ["90e3a7b9-20dc-4d04-9259-a565135dbf69", "BTC/USDT:USDT"]}, {"description": "spot open order with symbol", "method": "fetchOpenOrder", "url": "https://api-testnet.bybit.com/v5/order/realtime?symbol=LTCUSDT&category=spot&orderId=1620421817225451008", "input": ["1620421817225451008", "LTC/USDT"]}], "fetchClosedOrder": [{"description": "Spot fetch closed order", "options": {"defaultType": "spot"}, "method": "fetchClosedOrder", "url": "https://api-testnet.bybit.com/v5/order/history?category=spot&orderStatus=Filled&orderId=1620280083321455360", "input": ["1620280083321455360"]}, {"description": "Swap fetch closed order", "method": "fetchClosedOrder", "url": "https://api-testnet.bybit.com/v5/order/history?symbol=BTCUSDT&category=linear&orderStatus=Filled&orderId=1e471313-dd08-4335-a66a-398937d92a51", "input": ["1e471313-dd08-4335-a66a-398937d92a51", "BTC/USDT:USDT"]}, {"description": "fetch closed order spot with symbol", "method": "fetchClosedOrder", "url": "https://api-testnet.bybit.com/v5/order/history?symbol=LTCUSDT&category=spot&orderStatus=Filled&orderId=1620421267343808000", "input": ["1620421267343808000", "LTC/USDT"]}], "fetchMyLiquidations": [{"description": "Linear fetch my liquidations", "method": "fetchMyLiquidations", "url": "https://api-testnet.bybit.com/v5/execution/list?execType=BustTrade&category=linear", "input": [null, null, null, {"category": "linear"}]}, {"description": "Inverse fetch my liquidations", "method": "fetchMyLiquidations", "url": "https://api-testnet.bybit.com/v5/execution/list?execType=BustTrade&category=inverse", "input": [null, null, null, {"category": "inverse"}]}], "fetchLeverage": [{"description": "fetch leverage", "method": "fetchLeverage", "url": "https://api-testnet.bybit.com/v5/position/list?symbol=LTCUSDT&category=linear", "input": ["LTC/USDT:USDT"]}], "fetchTradingFee": [{"description": "linear trading fee", "method": "fetchTradingFee", "url": "https://api-testnet.bybit.com/v5/account/fee-rate?symbol=BTCUSDT&category=linear", "input": ["BTC/USDT:USDT"]}, {"description": "spot trading fee", "method": "fetchTradingFee", "url": "https://api-testnet.bybit.com/v5/account/fee-rate?symbol=BTCUSDT&category=spot", "input": ["BTC/USDT"]}], "fetchTime": [{"description": "fetchTime", "method": "fetchTime", "url": "https://api.bybit.com/v5/market/time", "input": []}], "fetchLeverageTiers": [{"disabled": true, "description": "linear leverage tiers", "method": "fetchLeverageTiers", "url": "https://api-testnet.bybit.com/v5/market/risk-limit?category=linear", "input": [["BTC/USDT:USDT"]]}], "fetchFundingHistory": [{"description": "funding history linear", "method": "fetchFundingHistory", "url": "https://api-testnet.bybit.com/v5/execution/list?execType=Funding&symbol=LTCUSDT&category=linear&size=100", "input": ["LTC/USDT:USDT"]}], "fetchOptionChain": [{"description": "Fetch an entire option chain for an underlying asset", "method": "fetchOptionChain", "url": "https://api-testnet.bybit.com/v5/market/tickers?category=option&baseCoin=BTC", "input": ["BTC"]}], "fetchOption": [{"description": "Fetch an option contract", "method": "fetchOption", "url": "https://api-testnet.bybit.com/v5/market/tickers?category=option&symbol=BTC-27DEC24-55000-P", "input": ["BTC/USDC:USDC-241227-55000-P"]}], "fetchPositionsHistory": [{"description": "positionHistory no symbol", "method": "fetchPositionsHistory", "url": "https://api-testnet.bybit.com/v5/position/closed-pnl?category=linear", "input": []}, {"description": "positionHistory with symbol", "method": "fetchPositionsHistory", "url": "https://api-testnet.bybit.com/v5/position/closed-pnl?category=linear&symbol=XRPUSDT&startTime=*************&limit=1&endTime=*************", "input": [["XRP/USDT:USDT"], *************, 1, {"until": *************}]}], "withdraw": [{"description": "withdraw usdt on tron", "method": "withdraw", "url": "https://api.bybit.com/v5/asset/withdraw/create", "input": ["USDT", 5, "TReSxAuV3YvKQPTZoUuCVSFXxJUC432LMz", null, {"network": "TRC20"}], "output": "{\"coin\":\"USDT\",\"amount\":\"5\",\"address\":\"TReSxAuV3YvKQPTZoUuCVSFXxJUC432LMz\",\"timestamp\":*************,\"accountType\":\"SPOT\",\"chain\":\"TRX\"}"}], "fetchConvertCurrencies": [{"description": "fetch conversion enabled currencies", "method": "fetchConvertCurrencies", "url": "https://api-testnet.bybit.com/v5/asset/exchange/query-coin-list?accountType=eb_convert_uta", "input": []}], "fetchConvertQuote": [{"description": "fetch a conversion quote", "method": "fetchConvertQuote", "url": "https://api-testnet.bybit.com/v5/asset/exchange/quote-apply", "input": ["USDT", "BTC", 10], "output": "{\"fromCoin\":\"USDT\",\"toCoin\":\"BTC\",\"requestAmount\":\"10\",\"requestCoin\":\"USDT\",\"accountType\":\"eb_convert_uta\"}"}], "createConvertTrade": [{"description": "create a conversion trade", "method": "createConvertTrade", "url": "https://api-testnet.bybit.com/v5/asset/exchange/convert-execute", "input": ["1010210067439483213403283456", "USDT", "BTC", 10], "output": "{\"quoteTxId\":\"1010210067439483213403283456\"}"}], "fetchConvertTrade": [{"description": "fetch a conversion trade", "method": "fetchConvertTrade", "url": "https://api-testnet.bybit.com/v5/asset/exchange/convert-result-query?quoteTxId=1010020692439483803499737088&accountType=eb_convert_uta", "input": ["1010020692439483803499737088"]}], "fetchConvertTradeHistory": [{"description": "fetch conversion trade history with code and limit arguments", "method": "fetchConvertTradeHistory", "url": "https://api-testnet.bybit.com/v5/asset/exchange/query-convert-history?limit=3", "input": ["USDT", null, 3]}], "fetchLongShortRatioHistory": [{"description": "fetch the long short ratio history for a linear swap market", "method": "fetchLongShortRatioHistory", "url": "https://api-testnet.bybit.com/v5/market/account-ratio?symbol=BTCUSDT&period=1d&category=linear", "input": ["BTC/USDT:USDT"]}], "fetchBorrowRateHistory": [{"description": "fetchBorrowRateHistory", "method": "fetchBorrowRateHistory", "url": "https://api-testnet.bybit.com/v5/spot-margin-trade/interest-rate-history?currency=USDT&startTime=*************&endTime=*************", "input": ["USDT", *************]}], "fetchOpenInterest": [{"description": "linear swap fetchOpenInterest", "method": "fetchOpenInterest", "url": "https://api-testnet.bybit.com/v5/market/open-interest?symbol=BTCUSDT&intervalTime=1h&category=linear", "input": ["BTC/USDT:USDT"]}, {"description": "inverse swap fetchOpenInterest", "method": "fetchOpenInterest", "url": "https://api-testnet.bybit.com/v5/market/open-interest?symbol=BTCUSD&intervalTime=1h&category=inverse", "input": ["BTC/USD:BTC"]}], "fetchAllGreeks": [{"description": "fetch all greeks with a single symbol argument and baseCoin parameter", "method": "fetchAllGreeks", "url": "https://api-testnet.bybit.com/v5/market/tickers?category=option&baseCoin=BTC&symbol=BTC-30MAY25-70000-C-USDT", "input": [["BTC/USDT:USDT-250530-70000-C"], {"baseCoin": "BTC"}]}]}, "disabled": []}