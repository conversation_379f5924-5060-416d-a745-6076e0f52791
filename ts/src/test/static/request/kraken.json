{"exchange": "kraken", "skipKeys": ["nonce"], "outputType": "u<PERSON><PERSON><PERSON>", "methods": {"fetchCurrencies": [{"description": "fetchCurrencies", "method": "fetchCurrencies", "url": "https://api.kraken.com/0/public/Assets", "input": [], "output": null}], "createOrder": [{"description": "createOrder long precisions2", "method": "createOrder", "url": "https://api.kraken.com/0/private/AddOrder", "input": ["ALPHA/USD", "limit", "buy", 50.12345678, 0.0123456789], "output": "nonce=1749617742768&pair=ALPHAUSD&type=buy&ordertype=limit&volume=50.12345&price=0.01235"}, {"description": "createOrder long precisions", "method": "createOrder", "url": "https://api.kraken.com/0/private/AddOrder", "input": ["BTC/USDT", "limit", "buy", 0.000123412345, 12345.123456789], "output": "nonce=1749616648149&pair=XBTUSDT&type=buy&ordertype=limit&volume=0.00012341&price=12345.1"}, {"description": "Spot market buy", "method": "createOrder", "url": "https://api.kraken.com/0/private/AddOrder", "input": ["LTC/USDT", "market", "buy", 0.1], "output": "nonce=1698834065549&pair=LTCUSDT&type=buy&ordertype=market&volume=0.1"}, {"description": "Spot market sell", "method": "createOrder", "url": "https://api.kraken.com/0/private/AddOrder", "input": ["LTC/USDT", "market", "sell", 0.1], "output": "nonce=1698834105321&pair=LTCUSDT&type=sell&ordertype=market&volume=0.1"}, {"description": "Margin stopLossPrice order (type 2)", "method": "createOrder", "url": "https://api.kraken.com/0/private/AddOrder", "input": ["BTC/USD", "limit", "sell", 0.00012, 34000, {"stopLossPrice": 35000, "leverage": 5, "reduceOnly": true}], "output": "nonce=1702704715667&pair=XXBTZUSD&type=sell&ordertype=stop-loss-limit&volume=0.00012&price=35000&price2=34000&reduce_only=true&leverage=5"}, {"description": "Margin takeProfitPrice order (type 2)", "method": "createOrder", "url": "https://api.kraken.com/0/private/AddOrder", "input": ["BTC/USD", "limit", "sell", 0.0001, 46000, {"takeProfitPrice": 45000, "leverage": 5, "reduceOnly": true}], "output": "nonce=1703055164304&pair=XXBTZUSD&type=sell&ordertype=take-profit-limit&volume=0.0001&price=45000&price2=46000&reduce_only=true&leverage=5"}, {"description": "Margin trailing order", "method": "createOrder", "url": "https://api.kraken.com/0/private/AddOrder", "input": ["BTC/USD", "market", "sell", 0.0002, null, {"trailingAmount": 100, "leverage": 5, "reduceOnly": true}], "output": "nonce=1703236727221&pair=XXBTZUSD&type=sell&ordertype=trailing-stop&volume=0.0002&trigger=last&price=%2B100&reduce_only=true&leverage=5"}, {"description": "Margin trailing limit order", "method": "createOrder", "url": "https://api.kraken.com/0/private/AddOrder", "input": ["BTC/USD", "limit", "sell", 0.0002, null, {"trailingAmount": 100, "trailingLimitAmount": 100, "leverage": 5, "reduceOnly": true}], "output": "nonce=1703236604802&pair=XXBTZUSD&type=sell&ordertype=trailing-stop-limit&volume=0.0002&trigger=last&price=%2B100&price2=-100&reduce_only=true&leverage=5"}, {"description": "Stop loss market order (type 2)", "method": "createOrder", "url": "https://api.kraken.com/0/private/AddOrder", "input": ["LTC/USDT", "market", "sell", 0.1, null, {"stopLossPrice": 55}], "output": "nonce=1705242635875&pair=LTCUSDT&type=sell&ordertype=stop-loss&volume=0.1&price=55"}, {"description": "Stop loss limit order (type 2)", "method": "createOrder", "url": "https://api.kraken.com/0/private/AddOrder", "input": ["LTC/USDT", "limit", "sell", 0.1, 54, {"stopLossPrice": 55}], "output": "nonce=1705242679268&pair=LTCUSDT&type=sell&ordertype=stop-loss-limit&volume=0.1&price=55&price2=54"}, {"description": "take profit limit order", "method": "createOrder", "url": "https://api.kraken.com/0/private/AddOrder", "input": ["LTC/USDT", "limit", "sell", 0.1, 110, {"takeProfitPrice": 120}], "output": "nonce=1705242729895&pair=LTCUSDT&type=sell&ordertype=take-profit-limit&volume=0.1&price=120&price2=110"}, {"description": "take profit market order (type 2)", "method": "createOrder", "url": "https://api.kraken.com/0/private/AddOrder", "input": ["LTC/USDT", "market", "sell", 0.1, null, {"takeProfitPrice": 120}], "output": "nonce=1705242796801&pair=LTCUSDT&type=sell&ordertype=take-profit&volume=0.1&price=120"}, {"description": "order with multiple oflags combined", "method": "createOrder", "url": "https://api.kraken.com/0/private/AddOrder", "input": ["LTC/USDT", "limit", "buy", 0.1, 50, {"postOnly": true, "oflags": "fcib"}], "output": "nonce=1715945544767&pair=LTCUSDT&type=buy&ordertype=limit&volume=0.1&price=50&oflags=fcib%2Cpost"}, {"description": "order with extra oflags", "method": "createOrder", "url": "https://api.kraken.com/0/private/AddOrder", "input": ["LTC/USDT", "limit", "buy", 0.5, 20, {"oflags": "fcib"}], "output": "nonce=1723192431526&pair=LTCUSDT&type=buy&ordertype=limit&volume=0.5&price=20&oflags=fcib"}, {"description": "trailing percent order", "method": "createOrder", "url": "https://api.kraken.com/0/private/AddOrder", "input": ["BTC/USD", "market", "buy", 0.0001, null, {"trailingPercent": 5}], "output": "{\"nonce\":\"1727779183078\",\"pair\":\"XXBTZUSD\",\"type\":\"buy\",\"ordertype\":\"trailing-stop\",\"volume\":\"0.0001\",\"trigger\":\"last\",\"price\":\"+5%\"}"}, {"description": "trailing limit percent order", "method": "createOrder", "url": "https://api.kraken.com/0/private/AddOrder", "input": ["BTC/USD", "limit", "buy", 0.0001, null, {"trailingPercent": 5, "trailingLimitPercent": 4}], "output": "{\"nonce\":\"1727849333383\",\"pair\":\"XXBTZUSD\",\"type\":\"buy\",\"ordertype\":\"trailing-stop-limit\",\"volume\":\"0.0001\",\"trigger\":\"last\",\"price\":\"+5%\",\"price2\":\"-4%\"}"}], "createMarketBuyOrderWithCost": [{"description": "market buy order", "method": "createMarketBuyOrderWithCost", "url": "https://api.kraken.com/0/private/AddOrder", "input": ["LTC/USD", 5], "output": "nonce=1715881618883&pair=XLTCZUSD&type=buy&ordertype=market&volume=5&oflags=viqc"}, {"description": "usdt order", "method": "createMarketBuyOrderWithCost", "url": "https://api.kraken.com/0/private/AddOrder", "input": ["LTC/USDT", 10], "output": "nonce=1715951571837&pair=LTCUSDT&type=buy&ordertype=market&volume=10&oflags=viqc"}], "fetchMyTrades": [{"description": "Spot private trades", "method": "fetchMyTrades", "url": "https://api.kraken.com/0/private/TradesHistory", "input": ["LTC/USDT", 1699457638000, 5], "output": "nonce=1699458293821&start=1699457638"}, {"description": "fetch my trades with since and until parameters", "method": "fetchMyTrades", "url": "https://api.kraken.com/0/private/TradesHistory", "input": ["BTC/USDT", 1703148143996, null, {"until": 1703148662559}], "output": "nonce=1723943525031&start=1703148143&end=1703148663"}], "fetchOpenOrders": [{"description": "Spot open orders", "method": "fetchOpenOrders", "url": "https://api.kraken.com/0/private/OpenOrders", "input": ["LTC/USDT"], "output": "nonce=1699458294470"}, {"description": "fetch spot open orders by client order id", "method": "fetchOpenOrders", "url": "https://api.kraken.com/0/private/OpenOrders", "input": ["BTC/USD", null, null, {"clientOrderId": "1234"}], "output": "nonce=1733815452225&cl_ord_id=1234"}], "fetchClosedOrders": [{"description": "Spot closed orders", "method": "fetchClosedOrders", "url": "https://api.kraken.com/0/private/ClosedOrders", "input": ["LTC/USDT"], "output": "nonce=1699458294863"}, {"description": "fetch spot closed orders with a limit argument and client order id param", "method": "fetchClosedOrders", "url": "https://api.kraken.com/0/private/ClosedOrders", "input": ["BTC/USD", null, 1, {"clientOrderId": "1234"}], "output": "nonce=1733816591092&cl_ord_id=1234"}], "cancelAllOrders": [{"description": "Cancel swap orders", "method": "cancelAllOrders", "url": "https://api.kraken.com/0/private/CancelAll", "input": ["LTC/USDT:USDT"], "output": "nonce=1699458295619"}, {"description": "Cancel spot orders", "method": "cancelAllOrders", "url": "https://api.kraken.com/0/private/CancelAll", "input": ["LTC/USDT"], "output": "nonce=1699458295807"}], "cancelAllOrdersAfter": [{"description": "Cancel spot orders after", "method": "cancelAllOrdersAfter", "url": "https://api.kraken.com/0/private/CancelAllOrdersAfter", "input": [10000], "output": "timeout=10&nonce=1699458295619"}, {"description": "Cancel spot orders after", "method": "cancelAllOrdersAfter", "url": "https://api.kraken.com/0/private/CancelAllOrdersAfter", "input": [0], "output": "timeout=0&nonce=1699458295619"}], "cancelOrder": [{"description": "cancel open trailing percent order", "method": "cancelOrder", "url": "https://api.kraken.com/0/private/CancelOrder", "input": ["OF7S76-H2IMS-N2ML4U"], "output": "nonce=1706895560930&txid=OF7S76-H2IMS-N2ML4U"}, {"description": "spot cancel order by client order id", "method": "cancelOrder", "url": "https://api.kraken.com/0/private/CancelOrder", "input": ["O45M52-BFD55-YXKQOU", "BTC/USD", {"clientOrderId": "1234"}], "output": "nonce=1733816320725&cl_ord_id=1234"}], "fetchBalance": [{"description": "Fetch spot Balance", "method": "fetchBalance", "url": "https://api.kraken.com/0/private/BalanceEx", "input": [{"type": "spot"}], "output": "nonce=1699458295995&type=spot"}, {"description": "Fetch swap Balance", "method": "fetchBalance", "url": "https://api.kraken.com/0/private/BalanceEx", "input": [{"type": "swap"}], "output": "nonce=1699458296188&type=swap"}], "fetchPositions": [{"description": "fetch positions", "method": "fetchPositions", "url": "https://api.kraken.com/0/private/OpenPositions", "input": [], "output": "nonce=1712572467788&docalcs=true&consolidation=market"}], "fetchLedger": [{"description": "fetch USDT ledger", "method": "fetchLedger", "url": "https://api.kraken.com/0/private/Ledgers", "input": ["USDT"], "output": "nonce=1699460637341&asset=USDT"}, {"description": "fetch BTC ledger with since and until parameters", "method": "fetchLedger", "url": "https://api.kraken.com/0/private/Ledgers", "input": ["BTC", 1653094900345, null, {"until": 1703054504934}], "output": "nonce=1723943578678&asset=XXBT&start=1653094900&end=1703054505"}], "fetchDeposits": [{"description": "fetch deposits without a code", "method": "fetchDeposits", "url": "https://api.kraken.com/0/private/DepositStatus", "input": [], "output": "nonce=1723801422087"}, {"description": "Fetch deposit", "method": "fetchDeposits", "url": "https://api.kraken.com/0/private/DepositStatus", "input": ["USDT"], "output": "nonce=1699458295995&asset=USDT"}, {"description": "fetch deposits with a since argument", "method": "fetchDeposits", "url": "https://api.kraken.com/0/private/DepositStatus", "input": ["USDT", 1671064645000], "output": "nonce=1723941524303&asset=USDT&start=1671064645"}, {"description": "fetch deposits with an until parameter", "method": "fetchDeposits", "url": "https://api.kraken.com/0/private/DepositStatus", "input": ["USDT", null, null, {"until": 1653094102000}], "output": "nonce=1723941539991&asset=USDT&end=1653094103"}], "fetchWithdrawals": [{"description": "fetch withdrawals with code and since arguments", "method": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://api.kraken.com/0/private/WithdrawStatus", "input": ["USDT", 1671133778000], "output": "nonce=1723941760936&asset=USDT&start=1671133778"}, {"description": "fetch withdrawals with an until parameter", "method": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://api.kraken.com/0/private/WithdrawStatus", "input": ["USDT", null, null, {"until": 1671133778000}], "output": "nonce=1723941781388&asset=USDT&end=1671133779"}], "fetchDepositAddress": [{"description": "coin with space in id", "method": "fetchDepositAddress", "url": "https://api.kraken.com/0/private/DepositAddresses", "input": ["MATIC"], "output": "nonce=1724594503685&asset=MATIC&method=Polygon%20%28MATIC%29"}], "fetchOHLCV": [{"description": "spot with since", "method": "fetchOHLCV", "url": "https://api.kraken.com/0/public/OHLC?pair=ETHUSDT&interval=60&since=1716156000", "input": ["ETH/USDT", "1h", 1716159600000]}, {"description": "spot ohlcv", "method": "fetchOHLCV", "url": "https://api.kraken.com/0/public/OHLC?pair=XBTUSDT&interval=1", "input": ["BTC/USDT"]}], "fetchTime": [{"description": "fetchTime", "method": "fetchTime", "url": "https://api.kraken.com/0/public/Time", "input": []}], "fetchTrades": [{"description": "spot fetchTrades", "method": "fetchTrades", "url": "https://api.kraken.com/0/public/Trades?pair=XBTUSDT", "input": ["BTC/USDT"]}, {"description": "trades with since", "method": "fetchTrades", "url": "https://api.kraken.com/0/public/Trades?pair=XBTUSDT&since=1716550666", "input": ["BTC/USDT", 1716550666738]}], "fetchOrderBook": [{"description": "spot orderbook", "method": "fetchOrderBook", "url": "https://api.kraken.com/0/public/Depth?pair=XBTUSDT", "input": ["BTC/USDT"]}], "fetchTicker": [{"description": "spot ticker", "method": "fetchTicker", "url": "https://api.kraken.com/0/public/Ticker?pair=XBTUSDT", "input": ["BTC/USDT"]}], "fetchTickers": [{"description": "spot tickers", "method": "fetchTickers", "url": "https://api.kraken.com/0/public/Ticker?pair=XBTUSDT%2CETHUSDT", "input": [["BTC/USDT", "ETH/USDT"]]}], "editOrder": [{"description": "edit a limit orders amount and price", "method": "editOrder", "url": "https://api.kraken.com/0/private/AmendOrder", "input": ["OP3KR4-WARBU-MOLAZO", "BTC/USD", "limit", "buy", 0.0001, 68000], "output": "nonce=1732915315155&txid=OP3KR4-WARBU-MOLAZO&order_qty=0.0001&limit_price=68000"}, {"description": "edit an orders post only parameter", "method": "editOrder", "url": "https://api.kraken.com/0/private/AmendOrder", "input": ["OC2Z3W-HREW5-IN6YFD", "BTC/USD", "limit", "buy", 0.0001, 71000, {"postOnly": true}], "output": "nonce=1732918147648&txid=OC2Z3W-HREW5-IN6YFD&post_only=true&order_qty=0.0001&limit_price=71000"}, {"description": "edit an order using the clientOrderId parameter", "method": "editOrder", "url": "https://api.kraken.com/0/private/AmendOrder", "input": ["05AMIQ-75QVP-DIFYBG", "BTC/USD", "limit", "buy", 0.0001, 71000, {"clientOrderId": "1234"}], "output": "nonce=1732919391972&cl_ord_id=1234&order_qty=0.0001&limit_price=71000"}, {"description": "edit a stopLossPrice order", "method": "editOrder", "url": "https://api.kraken.com/0/private/AmendOrder", "input": ["OV4PNR-TU2HK-YRMVWR", "BTC/USD", "market", "sell", 0.0001, null, {"stopLossPrice": "74000"}], "output": "nonce=1732937292347&txid=OV4PNR-TU2HK-YRMVWR&order_qty=0.0001&trigger_price=74000"}]}}