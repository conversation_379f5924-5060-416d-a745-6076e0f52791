{"exchange": "bigone", "skipKeys": ["guid"], "outputType": "json", "methods": {"createOrder": [{"description": "Spot limit buy", "method": "createOrder", "url": "https://big.one/api/v3/viewer/orders", "input": ["BTC/USDT", "limit", "buy", 0.0005, 25000], "output": "{\"asset_pair_name\":\"BTC-USDT\",\"side\":\"BID\",\"amount\":\"0.0005\",\"price\":\"25000\",\"type\":\"LIMIT\"}"}, {"description": "Spot limit sell", "method": "createOrder", "url": "https://big.one/api/v3/viewer/orders", "input": ["BTC/USDT", "limit", "sell", 0.000262, 39000], "output": "{\"asset_pair_name\":\"BTC-USDT\",\"side\":\"ASK\",\"amount\":\"0.000262\",\"price\":\"39000\",\"type\":\"LIMIT\"}"}, {"description": "Spot market sell", "method": "createOrder", "url": "https://big.one/api/v3/viewer/orders", "input": ["BTC/USDT", "market", "sell", 0.000262, null], "output": "{\"asset_pair_name\":\"BTC-USDT\",\"side\":\"ASK\",\"amount\":\"0.000262\",\"type\":\"MARKET\"}"}, {"description": "Spot market buy order with createMarketBuyOrderRequiresPrice set to false", "method": "createOrder", "url": "https://big.one/api/v3/viewer/orders", "input": ["BTC/USDT", "market", "buy", 10, null, {"createMarketBuyOrderRequiresPrice": false}], "output": "{\"asset_pair_name\":\"BTC-USDT\",\"side\":\"BID\",\"amount\":\"10\",\"type\":\"MARKET\"}"}, {"description": "create order with clientOrderId", "method": "createOrder", "url": "https://big.one/api/v3/viewer/orders", "input": ["DOT/USDT", "market", "sell", 0.9, null, {"client_order_id": "1253"}], "output": "{\"asset_pair_name\":\"DOT-USDT\",\"side\":\"ASK\",\"amount\":\"0.9\",\"type\":\"MARKET\",\"client_order_id\":\"1253\"}"}], "createMarketBuyOrderWithCost": [{"description": "Spot market buy order with cost", "method": "createMarketBuyOrderWithCost", "url": "https://big.one/api/v3/viewer/orders", "input": ["LTC/USDT", 11], "output": "{\"asset_pair_name\":\"LTC-USDT\",\"side\":\"BID\",\"amount\":\"11\",\"type\":\"MARKET\"}"}], "fetchOrders": [{"description": "Spot orders", "method": "fetchOrders", "url": "https://big.one/api/v3/viewer/orders?asset_pair_name=LTC-USDT", "input": ["LTC/USDT"]}], "fetchMyTrades": [{"description": "Spot private trades", "method": "fetchMyTrades", "url": "https://big.one/api/v3/viewer/trades?asset_pair_name=LTC-USDT&limit=5", "input": ["LTC/USDT", 1699457638000, 5]}], "fetchOpenOrders": [{"description": "Spot open orders", "method": "fetchOpenOrders", "url": "https://big.one/api/v3/viewer/orders?asset_pair_name=LTC-USDT&state=PENDING", "input": ["LTC/USDT"]}], "fetchClosedOrders": [{"description": "Spot closed orders", "method": "fetchClosedOrders", "url": "https://big.one/api/v3/viewer/orders?asset_pair_name=LTC-USDT&state=FILLED", "input": ["LTC/USDT"]}], "cancelAllOrders": [{"description": "Cancel spot orders", "method": "cancelAllOrders", "url": "https://big.one/api/v3/viewer/orders/cancel", "input": ["LTC/USDT"], "output": "{\"asset_pair_name\":\"LTC-USDT\"}"}], "fetchBalance": [{"description": "Fetch spot Balance", "method": "fetchBalance", "url": "https://big.one/api/v3/viewer/accounts", "input": [{"type": "spot"}]}, {"description": "Fetch swap Balance", "method": "fetchBalance", "url": "https://big.one/api/v3/viewer/accounts", "input": [{"type": "swap"}]}], "fetchDeposits": [{"description": "Fetch deposits", "method": "fetchDeposits", "url": "https://big.one/api/v3/viewer/deposits", "input": []}], "fetchWithdrawals": [{"description": "Fetch withdrawals", "method": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://big.one/api/v3/viewer/withdrawals", "input": []}], "transfer": [{"description": "transfer from spot to swap", "method": "transfer", "url": "https://big.one/api/v3/viewer/transfer", "input": ["USDT", 1, "spot", "swap"], "output": "{\"symbol\":\"USDT\",\"amount\":\"1\",\"from\":\"SPOT\",\"to\":\"CONTRACT\",\"guid\":\"e4514628-c33b-4a91-bac7-75ace392c469\"}"}], "fetchDepositAddress": [{"description": "fetch USDT deposit address", "method": "fetchDepositAddress", "url": "https://big.one/api/v3/viewer/assets/USDT/address", "input": ["USDT"]}], "fetchTickers": [{"description": "spot tickers", "method": "fetchTickers", "url": "https://big.one/api/v3/asset_pairs/tickers?pair_names=BTC-USDT", "input": [["BTC/USDT"]]}, {"description": "swap tickers", "method": "fetchTickers", "url": "https://big.one/api/contract/v2/instruments", "input": [["BTC/USDT:USDT", "XRP/USDT:USDT"]]}, {"description": "spot tickers", "method": "fetchTickers", "url": "https://big.one/api/v3/asset_pairs/tickers?pair_names=BTC-USDT", "input": [["BTC/USDT"]]}, {"description": "swap tickers", "method": "fetchTickers", "url": "https://big.one/api/contract/v2/instruments", "input": [["BTC/USDT:USDT"]]}], "fetchTime": [{"description": "fetchTime", "method": "fetchTime", "url": "https://big.one/api/v3/ping", "input": []}], "fetchTrades": [{"description": "spot fetchTrades", "method": "fetchTrades", "url": "https://big.one/api/v3/asset_pairs/BTC-USDT/trades", "input": ["BTC/USDT"]}], "fetchOrderBook": [{"description": "spot orderbook", "method": "fetchOrderBook", "url": "https://big.one/api/v3/asset_pairs/BTC-USDT/depth", "input": ["BTC/USDT"]}, {"description": "swap orderbook", "method": "fetchOrderBook", "url": "https://big.one/api/contract/v2/depth@BTCUSDT/snapshot", "input": ["BTC/USDT:USDT"]}], "fetchTicker": [{"description": "Swap ticker", "method": "fetchTicker", "url": "https://big.one/api/contract/v2/instruments", "input": ["BTC/USDT:USDT"]}, {"description": "spot ticker", "method": "fetchTicker", "url": "https://big.one/api/v3/asset_pairs/BTC-USDT/ticker", "input": ["BTC/USDT"]}], "fetchOHLCV": [{"description": "spot ohlcv", "method": "fetchOHLCV", "url": "https://big.one/api/v3/asset_pairs/BTC-USDT/candles?period=min1&limit=100", "input": ["BTC/USDT"]}, {"description": "fetchOHLCV with since", "method": "fetchOHLCV", "url": "https://big.one/api/v3/asset_pairs/BTC-USDT/candles?period=hour1&limit=100&time=2025-01-07T04%3A00%3A00.000Z", "input": ["BTC/USDT", "1h", 1735862400000]}, {"description": "fetchOHLCV with limit", "method": "fetchOHLCV", "url": "https://big.one/api/v3/asset_pairs/BTC-USDT/candles?period=hour1&limit=4", "input": ["BTC/USDT", "1h", null, 4]}, {"description": "fetchOHLCV with until", "method": "fetchOHLCV", "url": "https://big.one/api/v3/asset_pairs/BTC-USDT/candles?period=hour1&limit=100&time=2025-01-04T00%3A00%3A00.001Z", "input": ["BTC/USDT", "1h", null, null, {"until": 1735948800000}]}, {"description": "fetchOHLCV with since, and limit", "method": "fetchOHLCV", "url": "https://big.one/api/v3/asset_pairs/BTC-USDT/candles?period=hour1&limit=4&time=2025-01-03T03%3A59%3A59.999Z", "input": ["BTC/USDT", "1h", 1735862399999, 4]}, {"description": "fetchOHLCV with since, and until", "method": "fetchOHLCV", "url": "https://big.one/api/v3/asset_pairs/BTC-USDT/candles?period=hour1&limit=500&time=2025-01-04T00%3A00%3A00.001Z", "input": ["BTC/USDT", "1h", 1735862400000, null, {"until": 1735948800000}]}, {"description": "fetchOHLCV with limit and until", "method": "fetchOHLCV", "url": "https://big.one/api/v3/asset_pairs/BTC-USDT/candles?period=hour1&limit=4&time=2025-01-04T00%3A00%3A00.001Z", "input": ["BTC/USDT", "1h", null, 4, {"until": 1735948800000}]}, {"description": "fetchOHLCV with since, limit and until", "method": "fetchOHLCV", "url": "https://big.one/api/v3/asset_pairs/BTC-USDT/candles?period=hour1&limit=4&time=2025-01-03T04%3A00%3A00.000Z", "input": ["BTC/USDT", "1h", 1735862400000, 4, {"until": 1735948800000}]}], "cancelOrder": [{"description": "cancelOrder", "method": "cancelOrder", "url": "https://big.one/api/v3/viewer/orders/10/cancel", "input": ["10", "BTC/USDT"], "output": {}}], "fetchOrder": [{"description": "fetchOrder", "method": "fetchOrder", "url": "https://big.one/api/v3/viewer/orders/10", "input": ["10", "BTC/USDT"]}], "withdraw": [{"description": "withdraw", "method": "withdraw", "url": "https://big.one/api/v3/viewer/withdrawals", "input": ["BTC", 0.03, "**********************************"], "output": "{\"symbol\":\"BTC\",\"target_address\":\"**********************************\",\"amount\":\"0.03\"}"}]}}