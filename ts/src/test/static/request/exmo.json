{"exchange": "exmo", "skipKeys": ["nonce", "from", "to"], "outputType": "u<PERSON><PERSON><PERSON>", "methods": {"fetchDeposits": [{"description": "Fetch deposits", "method": "fetchDeposits", "url": "https://api.exmo.com/v1.1/wallet_operations", "input": [], "output": "nonce=1699458294472&type=deposit"}], "fetchWithdrawals": [{"description": "Fetch withdrawals", "method": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://api.exmo.com/v1.1/wallet_operations", "input": [], "output": "nonce=1699460471005&type=withdraw"}], "fetchBalance": [{"description": "Fetch balance", "method": "fetchBalance", "url": "https://api.exmo.com/v1.1/user_info", "input": [], "output": "nonce=1700154924505"}], "fetchCanceledOrders": [{"description": "Fetch cancelled orders", "method": "fetchCanceledOrders", "url": "https://api.exmo.com/v1.1/user_cancelled_orders", "input": [], "output": "nonce=1700155071992&limit=100&offset=0"}], "fetchDeposit": [{"description": "Fetch deposit", "method": "fetchDeposit", "url": "https://api.exmo.com/v1.1/wallet_operations", "input": [65221580], "output": "nonce=1700155146392&order_id=65221580&type=deposit"}], "fetchDepositAddress": [{"description": "Fetch deposit address of Tron", "method": "fetchDepositAddress", "url": "https://api.exmo.com/v1.1/deposit_address", "input": ["BTC"], "output": "nonce=1700155247019"}], "fetchDepositsWithdrawals": [{"description": "Fetch history of deposits and withdrawals of Tron", "method": "fetchDepositsWithdrawals", "url": "https://api.exmo.com/v1.1/wallet_history", "input": ["BTC"], "output": "nonce=1700155466968"}], "fetchWithdrawal": [{"description": "Fetch data on a currency withdrawal via the withdrawal id", "method": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://api.exmo.com/v1.1/wallet_operations", "input": [187435], "output": "nonce=1700155560242&order_id=187435&type=withdraw"}], "fetchOpenOrders": [{"description": "Fetch open orders", "method": "fetchOpenOrders", "url": "https://api.exmo.com/v1.1/user_open_orders", "input": [], "output": "nonce=1700155623591"}], "fetchMyTrades": [{"description": "Fetch my BTC/USDT trades", "method": "fetchMyTrades", "url": "https://api.exmo.com/v1.1/user_trades", "input": ["BTC/USDT"], "output": "nonce=1700155664178&pair=BTC_USDT&limit=100&offset=0"}], "fetchTradingFees": [{"description": "Fetch trading fees - private", "method": "fetchTradingFees", "url": "https://api.exmo.com/v1.1/margin/pair/list", "input": [], "output": "nonce=1700155664178"}, {"description": "Fetch trading fees - public", "method": "fetchTradingFees", "url": "https://api.exmo.com/v1.1/pair_settings", "input": [{"method": "fetchPublicTradingFees"}]}], "addMargin": [{"description": "<PERSON><PERSON>", "method": "add<PERSON><PERSON>gin", "url": "https://api.exmo.com/v1.1/margin/user/position/margin_add", "input": ["BTC/USDT", 1], "output": "nonce=1700155664178&position_id=BTC_USDT&quantity=1"}], "reduceMargin": [{"description": "Reduce <PERSON>", "method": "reduce<PERSON><PERSON>gin", "url": "https://api.exmo.com/v1.1/margin/user/position/margin_remove", "input": ["BTC/USDT", 1], "output": "nonce=1700155664178&position_id=BTC_USDT&quantity=1"}], "createOrder": [{"description": "create market sell order with cost", "method": "createOrder", "url": "https://api.exmo.com/v1.1/order_create", "input": ["LTC/USD", "market", "sell", 0, null, {"cost": 5}], "output": "nonce=1734346972185&pair=LTC_USD&quantity=5&price=0&type=market_sell_total"}, {"description": "create market order with cost", "method": "createOrder", "url": "https://api.exmo.com/v1.1/order_create", "input": ["LTC/USD", "market", "buy", 0, null, {"cost": 5}], "output": "nonce=1734346942237&pair=LTC_USD&quantity=5&price=0&type=market_buy_total"}, {"description": "Create Order - spot", "method": "createOrder", "url": "https://api.exmo.com/v1.1/order_create", "input": ["BTC/USDT", "market", "buy", 1, null], "output": "nonce=1700155664178&pair=BTC_USDT&quantity=1&price=0&type=market_buy"}, {"description": "Create Order - spot & stop order", "method": "createOrder", "url": "https://api.exmo.com/v1.1/stop_market_order_create", "input": ["BTC/USDT", "market", "buy", 1, null, {"triggerPrice": 1000}], "output": "nonce=1700155664178&pair=BTC_USDT&quantity=1&type=buy&trigger_price=1000"}, {"description": "Spot limit buy", "method": "createOrder", "url": "https://api.exmo.com/v1.1/order_create", "input": ["LTC/USDT", "limit", "buy", 0.1, 50], "output": "nonce=1701427482963&pair=LTC_USDT&quantity=0.1&price=50&type=buy"}, {"description": "Spot market sell", "method": "createOrder", "url": "https://api.exmo.com/v1.1/order_create", "input": ["LTC/USDT", "market", "sell", 0.1], "output": "nonce=1701427519555&pair=LTC_USDT&quantity=0.1&price=0&type=market_sell"}], "fetchTrades": [{"description": "spot fetchTrades", "method": "fetchTrades", "url": "https://api.exmo.com/v1.1/trades?pair=BTC_USDT", "input": ["BTC/USDT"]}], "fetchOrderBook": [{"description": "spot orderbook", "method": "fetchOrderBook", "url": "https://api.exmo.com/v1.1/order_book?pair=BTC_USDT", "input": ["BTC/USDT"]}], "fetchTicker": [{"description": "spot ticker", "method": "fetchTicker", "url": "https://api.exmo.com/v1.1/ticker", "input": ["BTC/USDT"]}], "fetchTickers": [{"description": "spot tickers", "method": "fetchTickers", "url": "https://api.exmo.com/v1.1/ticker", "input": [["BTC/USDT", "ETH/USDT"]]}], "fetchOHLCV": [{"description": "spot ohlcv", "method": "fetchOHLCV", "url": "https://api.exmo.com/v1.1/candles_history?symbol=BTC_USDT&resolution=1&from=1709932983&to=1709992984", "input": ["BTC/USDT"]}, {"description": "Fetch OHLCV with until", "method": "fetchOHLCV", "url": "https://api.exmo.com/v1.1/candles_history?symbol=BTC_USDT&resolution=60&from=1731916799&to=1735516800", "input": ["BTC/USDT", "1h", null, null, {"until": 1735516800000}]}, {"description": "fetchOHLCV with since, and until", "method": "fetchOHLCV", "url": "https://api.exmo.com/v1.1/candles_history?symbol=BTC_USDT&resolution=60&from=1735430399&to=1735516800", "input": ["BTC/USDT", "1h", 1735430400000, null, {"until": 1735516800000}]}, {"description": "fetchOHLCV with limit and until", "method": "fetchOHLCV", "url": "https://api.exmo.com/v1.1/candles_history?symbol=BTC_USDT&resolution=60&from=1735505999&to=1735516800", "input": ["BTC/USDT", "1h", null, 3, {"until": 1735516800000}]}, {"description": "fetchOHLCV with since, limit and until", "method": "fetchOHLCV", "url": "https://api.exmo.com/v1.1/candles_history?symbol=BTC_USDT&resolution=60&from=1735430399&to=1735516800", "input": ["BTC/USDT", "1h", 1735430400000, 3, {"until": 1735516800000}]}, {"description": "fetchOHLCV with limit", "method": "fetchOHLCV", "url": "https://api.exmo.com/v1.1/candles_history?symbol=BTC_USDT&resolution=60&from=1735666548&to=1735677349", "input": ["BTC/USDT", "1h", null, 3]}, {"description": "fetchOHLCV with since", "method": "fetchOHLCV", "url": "https://api.exmo.com/v1.1/candles_history?symbol=BTC_USDT&resolution=60&from=1735430399&to=1735677380", "input": ["BTC/USDT", "1h", 1735430400000]}]}}