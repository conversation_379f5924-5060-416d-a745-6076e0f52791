{"exchange": "woo", "skipKeys": [], "outputType": "u<PERSON><PERSON><PERSON>", "methods": {"withdraw": [{"description": "withdraw USDT on ETH", "method": "withdraw", "url": "https://api.woox.io/v1/asset/withdraw", "input": ["USDT", 10, "******************************************", null, {"network": "ETH"}], "output": "address=******************************************&amount=10&token=ETH_USDT"}, {"description": "withdraw USDT on ERC20", "method": "withdraw", "url": "https://api.woox.io/v1/asset/withdraw", "input": ["USDT", 10, "******************************************", null, {"network": "ERC20"}], "output": "address=******************************************&amount=10&token=ETH_USDT"}], "createOrder": [{"description": "spot limit buy", "method": "createOrder", "url": "https://api.woox.io/v3/trade/order", "input": ["LTC/USDT", "limit", "buy", 0.1, 50], "output": "{\"price\":\"50\",\"quantity\":\"0.1\",\"side\":\"BUY\",\"symbol\":\"SPOT_LTC_USDT\",\"type\":\"LIMIT\"}"}, {"description": "spot market buy", "method": "createOrder", "url": "https://api.woox.io/v3/trade/order", "input": ["LTC/USDT", "market", "buy", 10, 1], "output": "{\"amount\":\"10\",\"side\":\"BUY\",\"symbol\":\"SPOT_LTC_USDT\",\"type\":\"MARKET\"}"}, {"description": "spot market buy using base amount", "method": "createOrder", "url": "https://api.woox.io/v3/trade/order", "input": ["LTC/USDT", "market", "buy", 0.1], "output": "{\"quantity\":\"0.1\",\"side\":\"BUY\",\"symbol\":\"SPOT_LTC_USDT\",\"type\":\"MARKET\"}"}, {"description": "spot market sell", "method": "createOrder", "url": "https://api.woox.io/v3/trade/order", "input": ["LTC/USDT", "market", "sell", 0.1], "output": "{\"quantity\":\"0.1\",\"side\":\"SELL\",\"symbol\":\"SPOT_LTC_USDT\",\"type\":\"MARKET\"}"}, {"description": "swap market buy", "method": "createOrder", "url": "https://api.woox.io/v3/trade/order", "input": ["LTC/USDT:USDT", "market", "buy", 0.1], "output": "{\"quantity\":\"0.1\",\"side\":\"BUY\",\"symbol\":\"PERP_LTC_USDT\",\"type\":\"MARKET\"}"}, {"description": "Swap market sell with reduceOnly", "method": "createOrder", "url": "https://api.woox.io/v3/trade/order", "input": ["LTC/USDT:USDT", "market", "sell", 0.1, null, {"reduceOnly": true}], "output": "{\"quantity\":\"0.1\",\"reduceOnly\":true,\"side\":\"SELL\",\"symbol\":\"PERP_LTC_USDT\",\"type\":\"MARKET\"}"}, {"description": "Swap trailingAmount reduceOnly order", "method": "createOrder", "url": "https://api.woox.io/v3/trade/algoOrder", "input": ["BTC/USDT:USDT", "market", "sell", 0.0001, null, {"trailingAmount": "1000", "trailingTriggerPrice": "50000", "reduceOnly": true}], "output": "{\"activatedPrice\":\"50000\",\"algoType\":\"TRAILING_STOP\",\"callbackValue\":\"1000\",\"quantity\":\"0.0001\",\"reduceOnly\":true,\"side\":\"SELL\",\"symbol\":\"PERP_BTC_USDT\",\"type\":\"MARKET\"}"}, {"description": "Swap trailingPercent reduceOnly order", "method": "createOrder", "url": "https://api.woox.io/v3/trade/algoOrder", "input": ["BTC/USDT:USDT", "market", "sell", 0.0001, null, {"trailingPercent": "5", "trailingTriggerPrice": "50000", "reduceOnly": true}], "output": "{\"activatedPrice\":\"50000\",\"algoType\":\"TRAILING_STOP\",\"callbackRate\":\"0.05\",\"quantity\":\"0.0001\",\"reduceOnly\":true,\"side\":\"SELL\",\"symbol\":\"PERP_BTC_USDT\",\"type\":\"MARKET\"}"}, {"description": "Swap margine mode isolated order", "method": "createOrder", "url": "https://api.woox.io/v3/trade/order", "input": ["LTC/USDT:USDT", "limit", "buy", 0.2, 50, {"marginMode": "isolated"}], "output": "{\"marginMode\":\"ISOLATED\",\"price\":\"50\",\"quantity\":\"0.2\",\"side\":\"BUY\",\"symbol\":\"PERP_LTC_USDT\",\"type\":\"LIMIT\"}"}, {"description": "Swap margine mode cross order", "method": "createOrder", "url": "https://api.woox.io/v3/trade/order", "input": ["LTC/USDT:USDT", "limit", "buy", 0.2, 50, {"marginMode": "cross"}], "output": "{\"marginMode\":\"CROSS\",\"price\":\"50\",\"quantity\":\"0.2\",\"side\":\"BUY\",\"symbol\":\"PERP_LTC_USDT\",\"type\":\"LIMIT\"}"}], "createMarketBuyOrderWithCost": [{"description": "spot market buy with cost", "method": "createMarketBuyOrderWithCost", "url": "https://api.woox.io/v3/trade/order", "input": ["LTC/USDT", 5], "output": "{\"amount\":\"5\",\"side\":\"BUY\",\"symbol\":\"SPOT_LTC_USDT\",\"type\":\"MARKET\"}"}], "createMarketSellOrderWithCost": [{"description": "spot market sell with cost", "method": "createMarketSellOrderWithCost", "url": "https://api.woox.io/v3/trade/order", "input": ["LTC/USDT", 5], "output": "{\"amount\":\"5\",\"side\":\"SELL\",\"symbol\":\"SPOT_LTC_USDT\",\"type\":\"MARKET\"}"}], "fetchOrders": [{"description": "fetchOrders", "method": "fetchOrders", "url": "https://api.woox.io/v3/trade/orders?symbol=SPOT_LTC_USDT&size=1&startTime=1752049747730&endTime=1752149747730", "input": ["LTC/USDT", 1752049747730, 1, {"until": 1752149747730}]}, {"description": "fetchOrders - algo", "method": "fetchOrders", "url": "https://api.woox.io/v3/trade/algoOrders?symbol=SPOT_LTC_USDT&size=1&startTime=1752049747730", "input": ["LTC/USDT", 1752049747730, 1, {"stop": true}]}], "fetchMyTrades": [{"description": "fetchMyTrades", "method": "fetchMyTrades", "url": "https://api.woox.io/v3/trade/transactionHistory?symbol=SPOT_LTC_USDT&startTime=1699457638000&limit=1", "input": ["LTC/USDT", 1699457638000, 1]}], "cancelAllOrders": [{"description": "cancelAllOrders", "method": "cancelAllOrders", "url": "https://api.woox.io/v3/trade/orders?symbol=SPOT_LTC_USDT", "input": ["LTC/USDT"]}, {"description": "cancelAllOrders algo", "method": "cancelAllOrders", "url": "https://api.woox.io/v3/trade/algoOrders", "input": ["LTC/USDT", {"stop": true}]}], "cancelAllOrdersAfter": [{"description": "Cancel all orders after", "method": "cancelAllOrdersAfter", "url": "https://api.woox.io/v3/trade/cancelAllAfter", "input": [10000], "output": "{\"triggerAfter\":10000}"}, {"description": "Close cancel all orders after", "method": "cancelAllOrdersAfter", "url": "https://api.woox.io/v3/trade/cancelAllAfter", "input": [0], "output": "{\"triggerAfter\":0}"}], "fetchBalance": [{"description": "Fetch spot Balance", "method": "fetchBalance", "url": "https://api.woox.io/v3/balances?type=spot", "input": [{"type": "spot"}]}, {"description": "Fetch swap Balance", "method": "fetchBalance", "url": "https://api.woox.io/v3/balances?type=swap", "input": [{"type": "swap"}]}], "fetchPosition": [{"description": "Fetch linear position", "method": "fetchPosition", "url": "https://api.woox.io/v3/futures/positions?symbol=PERP_LTC_USDT", "input": ["LTC/USDT:USDT"]}], "fetchPositions": [{"description": "Fetch linear position", "method": "fetchPositions", "url": "https://api.woox.io/v3/futures/positions", "input": [["LTC/USDT:USDT"]]}], "setLeverage": [{"description": "setLeverage", "method": "setLeverage", "url": "https://api.woox.io/v3/spotMargin/leverage", "input": [5], "output": "{\"leverage\":5}"}, {"description": "setLeverage - swap", "method": "setLeverage", "url": "https://api.woox.io/v3/futures/leverage", "input": [5, "LTC/USDT:USDT"], "output": "{\"leverage\":5,\"marginMode\":\"CROSS\",\"symbol\":\"PERP_LTC_USDT\"}"}], "fetchDeposits": [{"description": "Fetch deposits", "method": "fetchDeposits", "url": "https://api.woox.io/v3/asset/wallet/history?tokenSide=DEPOSIT&type=BALANCE", "input": []}], "fetchWithdrawals": [{"description": "Fetch withdrawals", "method": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://api.woox.io/v3/asset/wallet/history?tokenSide=WITHDRAW&type=BALANCE", "input": []}], "fetchTransfers": [{"description": "fetch USDT transfers", "method": "fetchTransfers", "url": "https://api.woox.io/v3/asset/transfer/history", "input": []}], "fetchLedger": [{"description": "fetch USDT ledger", "method": "fetchLedger", "url": "https://api.woox.io/v3/asset/wallet/history?token=USDT", "input": ["USDT"]}], "editOrder": [{"description": "Swap edit trailingPercent order", "method": "editOrder", "url": "https://api.staging.woox.io/v3/algo/order/1111779", "input": ["1111779", "BTC/USDT:USDT", "market", "sell", 0.0001, null, {"trailingPercent": "4"}], "output": "{\"callbackRate\":\"0.04\",\"quantity\":\"0.0001\"}"}, {"description": "Swap edit trailingAmount order", "method": "editOrder", "url": "https://api.staging.woox.io/v3/algo/order/1111778", "input": ["1111778", "BTC/USDT:USDT", "market", "sell", 0.0001, null, {"trailingAmount": "1000", "trailingTriggerPrice": "51000"}], "output": "{\"activatedPrice\":\"51000\",\"callbackValue\":\"1000\",\"quantity\":\"0.0001\"}"}], "fetchDepositAddress": [{"description": "fetchDepositAddress USDT on ERC20", "method": "fetchDepositAddress", "url": "https://api.woox.io/v3/asset/wallet/deposit?token=USDT&network=ETH", "input": ["USDT", {"network": "ERC20"}], "output": null}, {"description": "fetchDepositAddress ETH on ETH", "method": "fetchDepositAddress", "url": "https://api.woox.io/v3/asset/wallet/deposit?token=ETH&network=ETH", "input": ["ETH", {"network": "ETH"}], "output": null}, {"description": "fetchDepositAddress ETH on ERC20", "method": "fetchDepositAddress", "url": "https://api.woox.io/v3/asset/wallet/deposit?token=ETH&network=ETH", "input": ["ETH", {"network": "ERC20"}], "output": null}, {"description": "fetchDepositAddress BTC on BTC", "method": "fetchDepositAddress", "url": "https://api.woox.io/v3/asset/wallet/deposit?token=BTC&network=BTC", "input": ["BTC", {"network": "BTC"}], "output": null}, {"description": "fetchDepositAddress USDT on ETH", "method": "fetchDepositAddress", "url": "https://api.woox.io/v3/asset/wallet/deposit?token=USDT&network=ETH", "input": ["USDT", {"network": "ETH"}], "output": null}], "fetchOHLCV": [{"description": "fetchOHLCV with since and limit", "method": "fetchOHLCV", "url": "https://api.woox.io/v3/public/klineHistory?symbol=SPOT_BTC_USDT&type=1m&limit=200", "input": ["BTC/USDT", "1m", null, 200]}, {"description": "fetchOHLCV with since & until", "method": "fetchOHLCV", "url": "https://api.woox.io/v3/public/klineHistory?after=1704067200000&before=1704067300000&symbol=SPOT_BTC_USDT&type=1m", "input": ["BTC/USDT", "1m", 1704067200000, null, {"until": 1704067300000}]}, {"description": "fetchOHLCV with limit and without since", "method": "fetchOHLCV", "url": "https://api.woox.io/v3/public/klineHistory?limit=200&symbol=SPOT_BTC_USDT&type=1m", "input": ["BTC/USDT", "1m", null, 200]}, {"description": "spot ohlcv", "method": "fetchOHLCV", "url": "https://api.woox.io/v3/public/klineHistory?symbol=SPOT_BTC_USDT&type=1m", "input": ["BTC/USDT"]}, {"description": "swap ohlcv", "method": "fetchOHLCV", "url": "https://api.woox.io/v3/public/klineHistory?symbol=PERP_BTC_USDT&type=1m", "input": ["BTC/USDT:USDT"]}], "setPositionMode": [{"description": "set position mode to hedge mode", "method": "setPositionMode", "url": "https://api.woox.io/v3/futures/positionMode", "input": [true], "output": "{\"positionMode\":\"HEDGE_MODE\"}"}, {"description": "set position mode to one way mode", "method": "setPositionMode", "url": "https://api.woox.io/v3/futures/positionMode", "input": [false], "output": "{\"positionMode\":\"ONE_WAY\"}"}], "fetchTrades": [{"description": "spot fetchTrades", "method": "fetchTrades", "url": "https://api.woox.io/v3/public/marketTrades?symbol=SPOT_BTC_USDT", "input": ["BTC/USDT"]}, {"description": "swap fetchTrades", "method": "fetchTrades", "url": "https://api.woox.io/v3/public/marketTrades?symbol=PERP_BTC_USDT", "input": ["BTC/USDT:USDT"]}], "cancelOrder": [{"description": "cancelOrder", "method": "cancelOrder", "url": "https://api.woox.io/v3/trade/order?orderId=1111779&symbol=PERP_LTC_USDT", "input": ["1111779", "LTC/USDT:USDT"]}, {"description": "cancelOrder - algo", "method": "cancelOrder", "url": "https://api.woox.io/v3/trade/algoOrder?algoOrderId=1111779", "input": ["1111779", "LTC/USDT:USDT", {"stop": true}]}], "fetchTime": [{"description": "fetchTime", "method": "fetchTime", "url": "https://api.woox.io/v3/public/systemInfo", "input": []}], "fetchStatus": [{"description": "fetchStatus", "method": "fetchStatus", "url": "https://api.woox.io/v3/public/systemInfo", "input": []}], "fetchMarkets": [{"description": "fetchMarkets", "method": "fetchMarkets", "url": "https://api.woox.io/v3/public/instruments", "input": []}], "fetchTradingFee": [{"description": "fetchTradingFee", "method": "fetchTradingFee", "url": "https://api.woox.io/v3/trade/tradingFee?symbol=PERP_BTC_USDT", "input": ["BTC/USDT:USDT"]}], "fetchTradingFees": [{"description": "fetchTradingFees", "method": "fetchTradingFees", "url": "https://api.woox.io/v3/account/info", "input": []}], "fetchCurrencies": [{"disabled": true, "disabledReason": "multiple urls called", "description": "fetchCurrencies", "method": "fetchCurrencies", "url": "https://api.woox.io/v1/public/token", "input": []}], "fetchOrderBook": [{"description": "fetchOrderBook", "method": "fetchOrderBook", "url": "https://api.woox.io/v3/public/orderbook?symbol=SPOT_LTC_USDT&maxLevel=5", "input": ["LTC/USDT", 5]}, {"description": "spot orderbook", "method": "fetchOrderBook", "url": "https://api.woox.io/v3/public/orderbook?symbol=SPOT_BTC_USDT", "input": ["BTC/USDT"]}, {"description": "swap orderbook", "method": "fetchOrderBook", "url": "https://api.woox.io/v3/public/orderbook?symbol=PERP_BTC_USDT", "input": ["BTC/USDT:USDT"]}], "fetchOrderTrades": [{"description": "fetchOrderTrades", "method": "fetchOrderTrades", "url": "https://api.woox.io/v1/order/**********/trades", "input": ["**********", "LTC/USDT"]}], "transfer": [{"description": "transfer", "method": "transfer", "url": "https://api.woox.io/v1/asset/main_sub_transfer", "input": ["USDT", 1000, "0f1bd3cd-dba2-4563-b8bb-0adb1bfb83a3", "c01e6940-a735-4022-9b6c-9d3971cdfdfa"], "output": "amount=1000&from_application_id=0f1bd3cd-dba2-4563-b8bb-0adb1bfb83a3&to_application_id=c01e6940-a735-4022-9b6c-9d3971cdfdfa&token=USDT"}], "fetchFundingRateHistory": [{"description": "fetchFundingRateHistory", "method": "fetchFundingRateHistory", "url": "https://api.woox.io/v3/public/fundingRateHistory?symbol=PERP_BTC_USDT", "input": ["BTC/USDT:USDT"]}], "fetchFundingHistory": [{"description": "fetchFundingHistory", "method": "fetchFundingHistory", "url": "https://api.woox.io/v3/futures/fundingFee/history?size=1&symbol=PERP_LTC_USDT", "input": ["LTC/USDT:USDT", null, 1]}], "fetchFundingRate": [{"description": "fetchFundingRate", "method": "fetchFundingRate", "url": "https://api.woox.io/v3/public/fundingRate?symbol=PERP_BTC_USDT", "input": ["BTC/USDT:USDT"]}], "fetchFundingRates": [{"description": "fetchFundingRates", "method": "fetchFundingRates", "url": "https://api.woox.io/v3/public/fundingRate", "input": [["BTC/USDT:USDT"]]}], "fetchOpenOrders": [{"description": "fetchOpenOrders", "method": "fetchOpenOrders", "url": "https://api.woox.io/v3/trade/orders?status=INCOMPLETE", "input": []}, {"description": "fetchOpenOrders - algo", "method": "fetchOpenOrders", "url": "https://api.woox.io/v3/trade/algoOrders?status=INCOMPLETE", "input": [null, null, null, {"stop": true}]}], "fetchClosedOrders": [{"description": "fetchClosedOrders", "method": "fetchClosedOrders", "url": "https://api.woox.io/v3/trade/orders?status=COMPLETED", "input": []}, {"description": "fetchClosedOrders - algo", "method": "fetchClosedOrders", "url": "https://api.woox.io/v3/trade/algoOrders?status=COMPLETED", "input": [null, null, null, {"stop": true}]}], "fetchConvertTrade": [{"description": "Fetch a conversion trade", "method": "fetchConvertTrade", "url": "https://api.staging.woox.io/v3/convert/trade?quoteId=12", "input": ["12"]}], "addMargin": [{"description": "Add margin with position side long", "method": "add<PERSON><PERSON>gin", "url": "https://api.staging.woox.io/v1/client/isolated_margin", "input": ["XRP/USDT:USDT", 0.2, {"position_side": "LONG"}], "output": "action=ADD&adjust_amount=0.2&adjust_token=USDT&position_side=LONG&symbol=PERP_XRP_USDT"}, {"description": "Add margin with position side short", "method": "add<PERSON><PERSON>gin", "url": "https://api.staging.woox.io/v1/client/isolated_margin", "input": ["XRP/USDT:USDT", 0.2, {"position_side": "SHORT"}], "output": "action=ADD&adjust_amount=0.2&adjust_token=USDT&position_side=SHORT&symbol=PERP_XRP_USDT"}], "reduceMargin": [{"description": "Reduce margin with position side long", "method": "reduce<PERSON><PERSON>gin", "url": "https://api.staging.woox.io/v1/client/isolated_margin", "input": ["XRP/USDT:USDT", 0.2, {"position_side": "LONG"}], "output": "action=REDUCE&adjust_amount=0.2&adjust_token=USDT&position_side=LONG&symbol=PERP_XRP_USDT"}, {"description": "Reduce margin with position side short", "method": "reduce<PERSON><PERSON>gin", "url": "https://api.staging.woox.io/v1/client/isolated_margin", "input": ["XRP/USDT:USDT", 0.2, {"position_side": "SHORT"}], "output": "action=REDUCE&adjust_amount=0.2&adjust_token=USDT&position_side=SHORT&symbol=PERP_XRP_USDT"}], "fetchFundingInterval": [{"description": "linear swap fetch the funding interval", "method": "fetchFundingInterval", "url": "https://api.staging.woox.io/v3/public/fundingRate?symbol=PERP_BTC_USDT", "input": ["BTC/USDT:USDT"]}], "fetchOrder": [{"description": "fetchOrder", "method": "fetchOrder", "url": "https://api.woox.io/v3/trade/order?orderId=***********", "input": ["***********"]}, {"description": "fetchOrder - algo", "method": "fetchOrder", "url": "https://api.woox.io/v3/trade/algoOrder?algoOrderId=********", "input": ["********", null, {"stop": true}]}], "fetchLeverage": [{"description": "fetchLeverage", "method": "fetchLeverage", "url": "https://api.woox.io/v3/account/info", "input": ["LTC/USDT"]}, {"description": "fetchLeverage - swap", "method": "fetchLeverage", "url": "https://api.woox.io/v3/futures/leverage?marginMode=CROSS&positionMode=ONE_WAY&symbol=PERP_LTC_USDT", "input": ["LTC/USDT:USDT", {"positionMode": "ONE_WAY"}]}]}}