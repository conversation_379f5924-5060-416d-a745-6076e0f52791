{"exchange": "hyperliquid", "options": {"approvedBuilderFee": true}, "skipKeys": ["startTime", "endTime", "r", "s", "v", "nonce", "time"], "outputType": "json", "methods": {"cancelAllOrdersAfter": [{"description": "request with vaultAddress in options", "options": {"vaultAddress": "******************************************"}, "method": "cancelAllOrdersAfter", "url": "https://api.hyperliquid.xyz/exchange", "input": [1], "output": "{\"nonce\":*************,\"action\":{\"type\":\"scheduleCancel\",\"time\":*************},\"signature\":{\"r\":\"0x2097f6619664dc6725d38970768c44094fc48510311b3cdbc2d6ac6396af16a9\",\"s\":\"0x1f0e8d2fee5d26d5c4022a84f3b7ece04e329f7fd8eada3a66d7fb8ab5d06605\",\"v\":28},\"vaultAddress\":\"cb4d9044aab9e5aa3965b470ebff554cc8820c7b\"}"}, {"description": "request with subAccount in options", "options": {"subAccountAddress": "******************************************"}, "method": "cancelAllOrdersAfter", "url": "https://api.hyperliquid.xyz/exchange", "input": [1], "output": "{\"nonce\":*************,\"action\":{\"type\":\"scheduleCancel\",\"time\":*************},\"signature\":{\"r\":\"0x2097f6619664dc6725d38970768c44094fc48510311b3cdbc2d6ac6396af16a9\",\"s\":\"0x1f0e8d2fee5d26d5c4022a84f3b7ece04e329f7fd8eada3a66d7fb8ab5d06605\",\"v\":28},\"vaultAddress\":\"cb4d9044aab9e5aa3965b470ebff554cc8820c7b\"}"}], "fetchCurrencies": [{"description": "fetchCurrencies", "method": "fetchCurrencies", "url": "https://api.hyperliquid.xyz/info", "input": [], "output": "{\"type\":\"meta\"}"}], "fetchTickers": [{"description": "fetch spot tickers only", "method": "fetchTickers", "url": "https://api.hyperliquid.xyz/info", "input": [null, {"type": "spot"}], "output": "{\"type\":\"spotMetaAndAssetCtxs\"}"}, {"description": "fetch swap tickers only", "method": "fetchTickers", "url": "https://api.hyperliquid.xyz/info", "input": [null, {"type": "swap"}], "output": "{\"type\":\"metaAndAssetCtxs\"}"}], "createOrder": [{"description": "limit order with more than 5 sig digs", "method": "createOrder", "url": "https://api.hyperliquid-testnet.xyz/exchange", "input": ["BTC/USDC:USDC", "limit", "buy", 0.0014, 100000.42343243], "output": "{\"action\":{\"type\":\"order\",\"orders\":[{\"a\":0,\"b\":true,\"p\":\"100000\",\"s\":\"0.0014\",\"r\":false,\"t\":{\"limit\":{\"tif\":\"Gtc\"}}}],\"grouping\":\"na\",\"builder\":{\"b\":\"******************************************\",\"f\":10}},\"nonce\":1732702700305,\"signature\":{\"r\":\"0xf9ca85459036d333814b93d3516a238e330be7d4dd0780269236bf9154056cba\",\"s\":\"0x2bbe382c0dfa04d21bf65b701c128aad31886f932b319d4c3ef37e930b30bb5b\",\"v\":28}}"}, {"description": "swap limit order", "method": "createOrder", "url": "https://api.hyperliquid-testnet.xyz/exchange", "input": ["SOL/USDC:USDC", "limit", "buy", 0.1, 100.9], "output": "{\"action\":{\"type\":\"order\",\"orders\":[{\"a\":5,\"b\":true,\"p\":\"100.9\",\"s\":\"0.1\",\"r\":false,\"t\":{\"limit\":{\"tif\":\"Gtc\"}}}],\"grouping\":\"na\",\"builder\":{\"b\":\"******************************************\",\"f\":10}},\"nonce\":1709144041776,\"signature\":{\"r\":\"0x98e752f90b99d6b52254d471e2096bf1ebcd9af53e41124ad838375eb86268b5\",\"s\":\"0x5ffbec62ad6ad94a14d7db87b6bf8d72c146f9bf49756dfd6359f5ee7580f14d\",\"v\":27}}"}, {"description": "swap market buy", "method": "createOrder", "url": "https://api.hyperliquid-testnet.xyz/exchange", "input": ["SOL/USDC:USDC", "market", "buy", 1, 110], "output": "{\"action\":{\"type\":\"order\",\"orders\":[{\"a\":5,\"b\":true,\"p\":\"115.5\",\"s\":\"1\",\"r\":false,\"t\":{\"limit\":{\"tif\":\"Ioc\"}}}],\"grouping\":\"na\",\"builder\":{\"b\":\"******************************************\",\"f\":10}},\"nonce\":1709144079538,\"signature\":{\"r\":\"0xc51efbead65cfc7fa4b9484faf796e711296709ab361e76632901dc13d6eb9e4\",\"s\":\"0x11b1526c06b7b301ec2129b4aca7fa8570ad435302a9d6245d1613c0abae5176\",\"v\":27}}"}, {"description": "trigger limit order", "method": "createOrder", "url": "https://api.hyperliquid-testnet.xyz/exchange", "input": ["SOL/USDC:USDC", "limit", "buy", 0.5, 50, {"triggerPrice": 55}], "output": "{\"action\":{\"type\":\"order\",\"orders\":[{\"a\":5,\"b\":true,\"p\":\"50\",\"s\":\"0.5\",\"r\":false,\"t\":{\"trigger\":{\"isMarket\":false,\"triggerPx\":\"55\",\"tpsl\":\"sl\"}}}],\"grouping\":\"na\",\"builder\":{\"b\":\"******************************************\",\"f\":10}},\"nonce\":1709642462922,\"signature\":{\"r\":\"0x5103646526d5556c306be0250f0acc63eba58639e47bc44f3c4ddc3cec33c568\",\"s\":\"0x667fc5139457aff57e323025355bae03f5f136ed8e4c0379595a858651efc9e9\",\"v\":28}}"}, {"description": "trigger market order", "method": "createOrder", "url": "https://api.hyperliquid-testnet.xyz/exchange", "input": ["SOL/USDC:USDC", "market", "buy", 0.5, 55, {"triggerPrice": 55}], "output": "{\"action\":{\"type\":\"order\",\"orders\":[{\"a\":5,\"b\":true,\"p\":\"57.75\",\"s\":\"0.5\",\"r\":false,\"t\":{\"trigger\":{\"isMarket\":true,\"triggerPx\":\"55\",\"tpsl\":\"sl\"}}}],\"grouping\":\"na\",\"builder\":{\"b\":\"******************************************\",\"f\":10}},\"nonce\":1709642530122,\"signature\":{\"r\":\"0x86f34e7f2c71b6a1ec0a67e6c44676c3be94c2146d395c8f51ab33478158bef9\",\"s\":\"0x48a694df72c8cf57e29ec59aaffbaee2674c041151cb14d5e4167efcfb1f42b5\",\"v\":28}}"}, {"description": "order with clientOrderId", "method": "createOrder", "url": "https://api.hyperliquid-testnet.xyz/exchange", "input": ["SOL/USDC:USDC", "market", "buy", 0.5, 130, {"clientOrderId": "0x1234567890abcdef1234567890abcdef"}], "output": "{\"action\":{\"type\":\"order\",\"orders\":[{\"a\":5,\"b\":true,\"p\":\"136.5\",\"s\":\"0.5\",\"r\":false,\"t\":{\"limit\":{\"tif\":\"Ioc\"}},\"c\":\"0x1234567890abcdef1234567890abcdef\"}],\"grouping\":\"na\",\"builder\":{\"b\":\"******************************************\",\"f\":10}},\"nonce\":1709643341698,\"signature\":{\"r\":\"0xb873d98d5530fdb1e188eed56e80f74c22b3e13fcf40001a79c2b55738bde25c\",\"s\":\"0x4658527dcd831b2aad7eaf6b534f783cfea54cb4c1c93d8c2924f7bf052ab4e\",\"v\":27}}"}, {"description": "BTC order with unformatted price and amount", "method": "createOrder", "url": "https://api.hyperliquid-testnet.xyz/exchange", "input": ["BTC/USDC:USDC", "limit", "buy", 0.001409324, 70956.12312321], "output": "{\"action\":{\"type\":\"order\",\"orders\":[{\"a\":0,\"b\":true,\"p\":\"70956\",\"s\":\"0.00141\",\"r\":false,\"t\":{\"limit\":{\"tif\":\"Gtc\"}}}],\"grouping\":\"na\",\"builder\":{\"b\":\"******************************************\",\"f\":10}},\"nonce\":1710503852928,\"signature\":{\"r\":\"0xc3b83528452492118d90f6f9ed0cae8bd975d7eba4f2d64e61abb47590ee2889\",\"s\":\"0x2f85d2ce05b88eef6d2546cdba280785ec2ef3e7ff0b389c7dcb7d4ed8adc3d3\",\"v\":27}}"}, {"description": "ETH order with unformatted price and amount", "method": "createOrder", "url": "https://api.hyperliquid-testnet.xyz/exchange", "input": ["ETH/USDC:USDC", "limit", "buy", 0.01349234, 3500.12345], "output": "{\"action\":{\"type\":\"order\",\"orders\":[{\"a\":1,\"b\":true,\"p\":\"3500.1\",\"s\":\"0.0135\",\"r\":false,\"t\":{\"limit\":{\"tif\":\"Gtc\"}}}],\"grouping\":\"na\",\"builder\":{\"b\":\"******************************************\",\"f\":10}},\"nonce\":1710503941098,\"signature\":{\"r\":\"0x9f9ed0bac32450e14214db21ab8fca2e0bc88e42abfddd6988199a55bd6d2b1b\",\"s\":\"0x6e5739314381a73080436bae1ca2269e59847366bb0f7b3ba912aa12e0f90036\",\"v\":27}}"}, {"description": "MEME order with unformatted price and amount", "method": "createOrder", "url": "https://api.hyperliquid-testnet.xyz/exchange", "input": ["MEME/USDC:USDC", "limit", "buy", 1000.543543, 0.043423423434], "output": "{\"action\":{\"type\":\"order\",\"orders\":[{\"a\":75,\"b\":true,\"p\":\"0.043423\",\"s\":\"1001\",\"r\":false,\"t\":{\"limit\":{\"tif\":\"Gtc\"}}}],\"grouping\":\"na\",\"builder\":{\"b\":\"******************************************\",\"f\":10}},\"nonce\":1710504463822,\"signature\":{\"r\":\"0x5b667d58d29e6d20e4bb69a6c8f876e8c588813caadb3177dc6a70cb8583ccd6\",\"s\":\"0x4187e4f7919bf8b564499f047796894bc4cf59fa5bf10f4c7894439e5adf2b48\",\"v\":28}}"}, {"description": "btc order with long amount", "method": "createOrder", "url": "https://api.hyperliquid-testnet.xyz/exchange", "input": ["BTC/USDC:USDC", "limit", "buy", 0.0014646006034154486, 70000], "output": "{\"action\":{\"type\":\"order\",\"orders\":[{\"a\":0,\"b\":true,\"p\":\"70000\",\"s\":\"0.00146\",\"r\":false,\"t\":{\"limit\":{\"tif\":\"Gtc\"}}}],\"grouping\":\"na\",\"builder\":{\"b\":\"******************************************\",\"f\":10}},\"nonce\":1710520063328,\"signature\":{\"r\":\"0xe2bb3f1a26776cab9c49b7e874e5fc07a4ab2cf80b24beed2958be41c2189e3a\",\"s\":\"0x582b3d4ba87cec02d16aa74c16e7b19b465b7d4e454f7e0b543e19a4e8c87ef7\",\"v\":27}}"}, {"description": "market order with unformatted price and amount", "method": "createOrder", "url": "https://api.hyperliquid-testnet.xyz/exchange", "input": ["BTC/USDC:USDC", "market", "buy", 0.0014646006034154486, 70000.234234324], "output": "{\"action\":{\"type\":\"order\",\"orders\":[{\"a\":0,\"b\":true,\"p\":\"73500\",\"s\":\"0.00146\",\"r\":false,\"t\":{\"limit\":{\"tif\":\"Ioc\"}}}],\"grouping\":\"na\",\"builder\":{\"b\":\"******************************************\",\"f\":10}},\"nonce\":1710527621347,\"signature\":{\"r\":\"0xba8575938a44e3e291085efb8e9682af3e9da54d5fbcbf49316c89b4ae84dba4\",\"s\":\"0x1d20dc5973f66161f4cd856d50611543d11f65f59a1bc53e33b01e1a4874b555\",\"v\":28}}"}, {"description": "Order with timeInForce", "method": "createOrder", "url": "https://api.hyperliquid-testnet.xyz/exchange", "input": ["BTC/USDC:USDC", "limit", "buy", 0.0014646006034154486, 60000.234234324, {"timeInForce": "GTC"}], "output": "{\"action\":{\"type\":\"order\",\"orders\":[{\"a\":0,\"b\":true,\"p\":\"60000\",\"s\":\"0.00146\",\"r\":false,\"t\":{\"limit\":{\"tif\":\"Gtc\"}}}],\"grouping\":\"na\",\"builder\":{\"b\":\"******************************************\",\"f\":10}},\"nonce\":1710929482709,\"signature\":{\"r\":\"0xdae9324db6a93f1c2062d24107f12e1549ec03f3313a6c4d7547e2f11c72b623\",\"s\":\"0x1e42a0c6edd18fe34c41d5c5e891e163449e66df27f537df0a79e5495cd273b1\",\"v\":28}}"}, {"description": "stopLossprice order on longPosition", "method": "createOrder", "url": "https://api.hyperliquid-testnet.xyz/exchange", "input": ["SOL/USDC:USDC", "market", "sell", 3.34, 100, {"stopLossPrice": 100}], "output": "{\"action\":{\"type\":\"order\",\"orders\":[{\"a\":5,\"b\":false,\"p\":\"95\",\"s\":\"3.34\",\"r\":false,\"t\":{\"trigger\":{\"isMarket\":true,\"triggerPx\":\"100\",\"tpsl\":\"sl\"}}}],\"grouping\":\"na\",\"builder\":{\"b\":\"******************************************\",\"f\":10}},\"nonce\":1711102975419,\"signature\":{\"r\":\"0x5e780e469a7ee4ffdd5bc5c3d01499d1bdffb7b903e3059fb3f92e8990a889ab\",\"s\":\"0xe8597e10a0bc540a025a946fb67abe82dca4a29620677b51e9441752829760c\",\"v\":27}}"}, {"description": "takeProfit order on long position", "method": "createOrder", "url": "https://api.hyperliquid-testnet.xyz/exchange", "input": ["SOL/USDC:USDC", "market", "buy", 3.34, 500, {"takeProfitPrice": 500}], "output": "{\"action\":{\"type\":\"order\",\"orders\":[{\"a\":5,\"b\":true,\"p\":\"525\",\"s\":\"3.34\",\"r\":false,\"t\":{\"trigger\":{\"isMarket\":true,\"triggerPx\":\"500\",\"tpsl\":\"tp\"}}}],\"grouping\":\"na\",\"builder\":{\"b\":\"******************************************\",\"f\":10}},\"nonce\":1711103051433,\"signature\":{\"r\":\"0x75cdfd6d0db428983f9b474ab19902c276423864de1889f5615e4e6208931be9\",\"s\":\"0x1be579fb6c6730c4da2095156c7c7d4ed77367cbe999eae9281320fcd04b187c\",\"v\":28}}"}, {"description": "open short position", "method": "createOrder", "url": "https://api.hyperliquid-testnet.xyz/exchange", "input": ["SOL/USDC:USDC", "market", "sell", 2, 175], "output": "{\"action\":{\"type\":\"order\",\"orders\":[{\"a\":5,\"b\":false,\"p\":\"166.25\",\"s\":\"2\",\"r\":false,\"t\":{\"limit\":{\"tif\":\"Ioc\"}}}],\"grouping\":\"na\",\"builder\":{\"b\":\"******************************************\",\"f\":10}},\"nonce\":1711103381494,\"signature\":{\"r\":\"0xf5ae090a22d3045ebbbe5b077e05dd41d98f2c4927f43cb352e49fcf63c3289e\",\"s\":\"0x380b7591b5ad4a8851c86d9818aed79fde4abbb197fe78aed17b228bde8dc80c\",\"v\":27}}"}, {"description": "takeProfitPrice on a short position", "method": "createOrder", "url": "https://api.hyperliquid-testnet.xyz/exchange", "input": ["SOL/USDC:USDC", "market", "buy", 3.34, 100, {"takeProfitPrice": 100}], "output": "{\"action\":{\"type\":\"order\",\"orders\":[{\"a\":5,\"b\":true,\"p\":\"105\",\"s\":\"3.34\",\"r\":false,\"t\":{\"trigger\":{\"isMarket\":true,\"triggerPx\":\"100\",\"tpsl\":\"tp\"}}}],\"grouping\":\"na\",\"builder\":{\"b\":\"******************************************\",\"f\":10}},\"nonce\":1711103408170,\"signature\":{\"r\":\"0x188d6cc5cc5b3f069b1be089a514df4dbdd2365839ff17cb13e66b37da213133\",\"s\":\"0x22843e7032ff62a8bff6cd934013f44261c205114be8185fe0848deae0820a99\",\"v\":28}}"}, {"description": "stopLossPrice on short position", "method": "createOrder", "url": "https://api.hyperliquid-testnet.xyz/exchange", "input": ["SOL/USDC:USDC", "market", "buy", 3.34, 300, {"stopLossPrice": 300}], "output": "{\"action\":{\"type\":\"order\",\"orders\":[{\"a\":5,\"b\":true,\"p\":\"315\",\"s\":\"3.34\",\"r\":false,\"t\":{\"trigger\":{\"isMarket\":true,\"triggerPx\":\"300\",\"tpsl\":\"sl\"}}}],\"grouping\":\"na\",\"builder\":{\"b\":\"******************************************\",\"f\":10}},\"nonce\":1711103459750,\"signature\":{\"r\":\"0x896488134a2e05705029dad89e7918b6969ef6bb7ca9e85216ff4fe7e2625e75\",\"s\":\"0x5ae03833be91f209c2aa0b08dc061fef155affbf64a968e2f444feec87a09178\",\"v\":28}}"}, {"description": "order in a vault", "method": "createOrder", "url": "https://api.hyperliquid-testnet.xyz/exchange", "input": ["SOL/USDC:USDC", "market", "buy", 0.1, 175, {"vaultAddress": "eba57e1d6bd242ba5f41b61ff1d30b481a535b58"}], "output": "{\"action\":{\"type\":\"order\",\"orders\":[{\"a\":5,\"b\":true,\"p\":\"183.75\",\"s\":\"0.1\",\"r\":false,\"t\":{\"limit\":{\"tif\":\"Ioc\"}}}],\"grouping\":\"na\",\"builder\":{\"b\":\"******************************************\",\"f\":10}},\"nonce\":*************,\"signature\":{\"r\":\"0xf389ba0e2bd5f1384e80e4fba5e337273bd02d40b19127206dea14ffe6c7e0af\",\"s\":\"0xc4089eac6cc70d6834f595acce82d32252f11c9459e4a3868548b6fc3087043\",\"v\":27},\"vaultAddress\":\"eba57e1d6bd242ba5f41b61ff1d30b481a535b58\"}"}, {"description": "order in vaultadress with 0x", "method": "createOrder", "url": "https://api.hyperliquid-testnet.xyz/exchange", "input": ["SOL/USDC:USDC", "market", "buy", 0.1, 175, {"vaultAddress": "******************************************"}], "output": "{\"action\":{\"type\":\"order\",\"orders\":[{\"a\":5,\"b\":true,\"p\":\"183.75\",\"s\":\"0.1\",\"r\":false,\"t\":{\"limit\":{\"tif\":\"Ioc\"}}}],\"grouping\":\"na\",\"builder\":{\"b\":\"******************************************\",\"f\":10}},\"nonce\":1711105150669,\"signature\":{\"r\":\"0xc10726b93896904251f8b6ec474ed428c448e5029a2d549d0e00a9f6580ac493\",\"s\":\"0x759e02cc811372410582ee28dc5b8c965bbdd87606570e8d2a8f31663f268ed0\",\"v\":27},\"vaultAddress\":\"eba57e1d6bd242ba5f41b61ff1d30b481a535b58\"}"}, {"description": "order with custom params", "method": "createOrder", "url": "https://api.hyperliquid-testnet.xyz/exchange", "input": ["SOL/USDC:USDC", "market", "buy", 0.1, 175, {"r": true}], "output": "{\"action\":{\"type\":\"order\",\"orders\":[{\"a\":5,\"b\":true,\"p\":\"183.75\",\"s\":\"0.1\",\"r\":true,\"t\":{\"limit\":{\"tif\":\"Ioc\"}}}],\"grouping\":\"na\",\"builder\":{\"b\":\"******************************************\",\"f\":10}},\"nonce\":1711107892157,\"signature\":{\"r\":\"0x1d1c230fb2d472d613b875c24f83527ed788180b90468be9bfb22d36e8259c71\",\"s\":\"0x3682f2286381b7f20ee1e703f48addb42554348cda438bab9da0d51eb5081938\",\"v\":27}}"}, {"description": "order with reduceOnly", "method": "createOrder", "url": "https://api.hyperliquid-testnet.xyz/exchange", "input": ["SOL/USDC:USDC", "market", "buy", 0.1, 175, {"reduceOnly": true}], "output": "{\"action\":{\"type\":\"order\",\"orders\":[{\"a\":5,\"b\":true,\"p\":\"183.75\",\"s\":\"0.1\",\"r\":true,\"t\":{\"limit\":{\"tif\":\"Ioc\"}}}],\"grouping\":\"na\",\"builder\":{\"b\":\"******************************************\",\"f\":10}},\"nonce\":1711269186132,\"signature\":{\"r\":\"0x3bd6ec400ce752dc85c4d3a72559f46960ba692b6dd7b750ef45cf6ed6dbd527\",\"s\":\"0x298f5ea4cfb6b7b756b137e64e20ac84cd4a18e6d0def82929f21145a8cd58ec\",\"v\":27}}"}, {"description": "post only order", "method": "createOrder", "url": "https://api.hyperliquid-testnet.xyz/exchange", "input": ["SOL/USDC:USDC", "limit", "buy", 0.1, 170, {"postOnly": true}], "output": "{\"action\":{\"type\":\"order\",\"orders\":[{\"a\":5,\"b\":true,\"p\":\"170\",\"s\":\"0.1\",\"r\":false,\"t\":{\"limit\":{\"tif\":\"Alo\"}}}],\"grouping\":\"na\",\"builder\":{\"b\":\"******************************************\",\"f\":10}},\"nonce\":1711269437173,\"signature\":{\"r\":\"0x7ad22203bcece7b94016458e6080be8bee0724807186bcb7a64f1a9f9a83c1b9\",\"s\":\"0x1ae3889c08d3a9ce6bf50260b9e4b2947e55a10fe8c6640e6254a0ab829febb9\",\"v\":28}}"}, {"description": "spot market order", "method": "createOrder", "url": "https://api.hyperliquid-testnet.xyz/exchange", "input": ["PURR/USDC", "market", "buy", 1000, 0.019], "output": "{\"action\":{\"type\":\"order\",\"orders\":[{\"a\":10000,\"b\":true,\"p\":\"0.01995\",\"s\":\"1000\",\"r\":false,\"t\":{\"limit\":{\"tif\":\"Ioc\"}}}],\"grouping\":\"na\",\"builder\":{\"b\":\"******************************************\",\"f\":10}},\"nonce\":1711797846550,\"signature\":{\"r\":\"0x8a9f3b96eef951af6235ed41f4adc9bae2781cb617f98a48cc7e2159775ba1d0\",\"s\":\"0x38d23e9c00707dc20b6d8ba15bf0a37b72602e72be861bcc25494c1df3c0aaed\",\"v\":27}}"}, {"description": "spot limit order", "method": "createOrder", "url": "https://api.hyperliquid-testnet.xyz/exchange", "input": ["PURR/USDC", "limit", "buy", 700, 0.015], "output": "{\"action\":{\"type\":\"order\",\"orders\":[{\"a\":10000,\"b\":true,\"p\":\"0.015\",\"s\":\"700\",\"r\":false,\"t\":{\"limit\":{\"tif\":\"Gtc\"}}}],\"grouping\":\"na\",\"builder\":{\"b\":\"******************************************\",\"f\":10}},\"nonce\":1711798239311,\"signature\":{\"r\":\"0x6b1d91dcedb64e423ef45336abc3b287ce8d3619ef87f8e28152160ecd026c65\",\"s\":\"0xbae493bf733566e9a734f8665879b3435bb0fd1b0f879e3717259a313437562\",\"v\":28}}"}, {"description": "SOL swap order price should have 5 significan digits and 3 decimals", "method": "createOrder", "url": "https://api.hyperliquid-testnet.xyz/exchange", "input": ["SOL/USDC:USDC", "limit", "buy", 0.5, 55.423434234], "output": "{\"action\":{\"type\":\"order\",\"orders\":[{\"a\":5,\"b\":true,\"p\":\"55.423\",\"s\":\"0.5\",\"r\":false,\"t\":{\"limit\":{\"tif\":\"Gtc\"}}}],\"grouping\":\"na\",\"builder\":{\"b\":\"******************************************\",\"f\":10}},\"nonce\":1726519973492,\"signature\":{\"r\":\"0xf308ca59b87e83d9bf559e0977b3842a5c60f6c0d84ce6278270d86a01c945ff\",\"s\":\"0x7ac1e8412972624409312685422fbfc325fbf7f9cbb133cca5a6980650d66e47\",\"v\":28}}"}, {"description": "SOL swap order price should have 5 significan digits and 2 decimals", "method": "createOrder", "url": "https://api.hyperliquid-testnet.xyz/exchange", "input": ["SOL/USDC:USDC", "limit", "sell", 0.5, 100.3424324], "output": "{\"action\":{\"type\":\"order\",\"orders\":[{\"a\":5,\"b\":false,\"p\":\"100.34\",\"s\":\"0.5\",\"r\":false,\"t\":{\"limit\":{\"tif\":\"Gtc\"}}}],\"grouping\":\"na\",\"builder\":{\"b\":\"******************************************\",\"f\":10}},\"nonce\":1726520138074,\"signature\":{\"r\":\"0xa144d396caa8b1fdb10594f998b57ec6ac387331647b781a555a3d3a829bd6f4\",\"s\":\"0x58ab6363a56b7c0f1380b1a233f6b53245ce9d4fc815f55ac3d3dd3a1ce56181\",\"v\":27}}"}, {"description": "open short position with market stoploss and takeprofit", "method": "createOrder", "url": "https://api.hyperliquid-testnet.xyz/exchange", "input": ["SOL/USDC:USDC", "market", "sell", 2, 150, {"stopLoss": {"type": "market", "triggerPrice": 200}, "takeProfit": {"type": "market", "triggerPrice": 50}}], "output": "{\"action\":{\"type\":\"order\",\"orders\":[{\"a\":5,\"b\":false,\"p\":\"142.5\",\"s\":\"2\",\"r\":false,\"t\":{\"limit\":{\"tif\":\"Ioc\"}}},{\"a\":5,\"b\":true,\"p\":\"52.5\",\"s\":\"2\",\"r\":true,\"t\":{\"trigger\":{\"isMarket\":true,\"triggerPx\":\"50\",\"tpsl\":\"tp\"}}},{\"a\":5,\"b\":true,\"p\":\"210\",\"s\":\"2\",\"r\":true,\"t\":{\"trigger\":{\"isMarket\":true,\"triggerPx\":\"200\",\"tpsl\":\"sl\"}}}],\"grouping\":\"normalTpsl\",\"builder\":{\"b\":\"******************************************\",\"f\":10}},\"nonce\":1750773607539,\"signature\":{\"r\":\"\",\"s\":\"\",\"v\":27}}"}, {"description": "open short position with limit stoploss and takeprofit orders", "method": "createOrder", "url": "https://api.hyperliquid-testnet.xyz/exchange", "input": ["SOL/USDC:USDC", "market", "sell", 2, 150, {"stopLoss": {"type": "limit", "triggerPrice": 200, "stopLossPrice": 190}, "takeProfit": {"type": "limit", "triggerPrice": 50, "takeProfitPrice": 40}}], "output": "{\"action\":{\"type\":\"order\",\"orders\":[{\"a\":5,\"b\":false,\"p\":\"142.5\",\"s\":\"2\",\"r\":false,\"t\":{\"limit\":{\"tif\":\"Ioc\"}}},{\"a\":5,\"b\":true,\"p\":\"40\",\"s\":\"2\",\"r\":true,\"t\":{\"trigger\":{\"isMarket\":false,\"triggerPx\":\"50\",\"tpsl\":\"tp\"}}},{\"a\":5,\"b\":true,\"p\":\"190\",\"s\":\"2\",\"r\":true,\"t\":{\"trigger\":{\"isMarket\":false,\"triggerPx\":\"200\",\"tpsl\":\"sl\"}}}],\"grouping\":\"normalTpsl\", \"builder\":{\"b\":\"******************************************\",\"f\":10}},\"nonce\":*************,\"signature\":{\"r\":\"\",\"s\":\"\",\"v\":27}}"}, {"description": "order with a subaccount", "method": "createOrder", "url": "https://api.hyperliquid-testnet.xyz/exchange", "input": ["SOL/USDC:USDC", "market", "buy", 0.1, 175, {"subAccountAddress": "eba57e1d6bd242ba5f41b61ff1d30b481a535b58"}], "output": "{\"action\":{\"type\":\"order\",\"orders\":[{\"a\":5,\"b\":true,\"p\":\"183.75\",\"s\":\"0.1\",\"r\":false,\"t\":{\"limit\":{\"tif\":\"Ioc\"}}}],\"grouping\":\"na\",\"builder\":{\"b\":\"******************************************\",\"f\":10}},\"nonce\":*************,\"signature\":{\"r\":\"0xf389ba0e2bd5f1384e80e4fba5e337273bd02d40b19127206dea14ffe6c7e0af\",\"s\":\"0xc4089eac6cc70d6834f595acce82d32252f11c9459e4a3868548b6fc3087043\",\"v\":27},\"vaultAddress\":\"eba57e1d6bd242ba5f41b61ff1d30b481a535b58\"}"}], "createOrders": [{"description": "create limit orders", "method": "createOrders", "url": "https://api.hyperliquid-testnet.xyz/exchange", "input": [[{"symbol": "SOL/USDC:USDC", "amount": 0.1, "side": "buy", "type": "limit", "price": 60}, {"symbol": "SOL/USDC:USDC", "amount": 0.11, "side": "buy", "type": "limit", "price": 61}]], "output": "{\"action\":{\"type\":\"order\",\"orders\":[{\"a\":5,\"b\":true,\"p\":\"60\",\"s\":\"0.1\",\"r\":false,\"t\":{\"limit\":{\"tif\":\"Gtc\"}}},{\"a\":5,\"b\":true,\"p\":\"61\",\"s\":\"0.11\",\"r\":false,\"t\":{\"limit\":{\"tif\":\"Gtc\"}}}],\"grouping\":\"na\",\"builder\":{\"b\":\"******************************************\",\"f\":10}},\"nonce\":1709643776143,\"signature\":{\"r\":\"0xe94bca75465d32e21e86e86f32d443331ebe1195888eb05e5eebf5a4db2a1716\",\"s\":\"0x2c9b208ec7e339ed005b2263030f303c9d0b3a1f4bb41e9aa38b40a62b9c34a4\",\"v\":28}}"}], "editOrder": [{"description": "edit order", "method": "editOrder", "url": "https://api.hyperliquid-testnet.xyz/exchange", "input": ["6530857127", "SOL/USDC:USDC", "limit", "buy", 0.7, 100], "output": "{\"action\":{\"type\":\"batchModify\",\"modifies\":[{\"oid\":6530857127,\"order\":{\"a\":5,\"b\":true,\"p\":\"100\",\"s\":\"0.7\",\"r\":false,\"t\":{\"limit\":{\"tif\":\"Gtc\"}}}}]},\"nonce\":1709643553938,\"signature\":{\"r\":\"0x5455e138164fb02c23fae78d26bcfff278ebbd7d0ad4bfdd450685221135bcbb\",\"s\":\"0x2eb7ab67b84d7614c195fe42f50ebfa83822237abd1b68c44ab547a2f8acdd1a\",\"v\":27}}"}, {"description": "edit order on a vault", "method": "editOrder", "url": "https://api.hyperliquid-testnet.xyz/exchange", "input": ["**********", "SOL/USDC:USDC", "limit", "buy", 0.2, 151, {"vaultAddress": "******************************************"}], "output": "{\"action\":{\"type\":\"batchModify\",\"modifies\":[{\"oid\":**********,\"order\":{\"a\":5,\"b\":true,\"p\":\"151\",\"s\":\"0.2\",\"r\":false,\"t\":{\"limit\":{\"tif\":\"Gtc\"}}}}]},\"nonce\":*************,\"signature\":{\"r\":\"0x1d07b1ae45cc8551b3d6d610780cc32d12a263cdabbe4013f4f927122215dfa8\",\"s\":\"0x33df2aea44f821de5e3b80e290ce2da30d8bd35f0358e04fec4c0daf60811f03\",\"v\":27},\"vaultAddress\":\"eba57e1d6bd242ba5f41b61ff1d30b481a535b58\"}"}, {"description": "edit order with a subaccount", "method": "editOrder", "url": "https://api.hyperliquid-testnet.xyz/exchange", "input": ["**********", "SOL/USDC:USDC", "limit", "buy", 0.2, 151, {"subAccountAddress": "******************************************"}], "output": "{\"action\":{\"type\":\"batchModify\",\"modifies\":[{\"oid\":**********,\"order\":{\"a\":5,\"b\":true,\"p\":\"151\",\"s\":\"0.2\",\"r\":false,\"t\":{\"limit\":{\"tif\":\"Gtc\"}}}}]},\"nonce\":*************,\"signature\":{\"r\":\"0x1d07b1ae45cc8551b3d6d610780cc32d12a263cdabbe4013f4f927122215dfa8\",\"s\":\"0x33df2aea44f821de5e3b80e290ce2da30d8bd35f0358e04fec4c0daf60811f03\",\"v\":27},\"vaultAddress\":\"eba57e1d6bd242ba5f41b61ff1d30b481a535b58\"}"}], "cancelOrder": [{"description": "cancel order", "method": "cancelOrder", "url": "https://api.hyperliquid-testnet.xyz/exchange", "input": [**********, "SOL/USDC:USDC"], "output": "{\"nonce\":*************,\"action\":{\"type\":\"cancel\",\"cancels\":[{\"a\":5,\"o\":**********}]},\"signature\":{\"r\":\"0xba035847994c83ffeaadf906639c0f82548cbcae8195c527f74e3cb50148cd6b\",\"s\":\"0x64098742b1d2fdac9a11973c991de69b8adbcee4e461acb7dd06f41decf4995d\",\"v\":27}}"}, {"description": "cancel spot order", "method": "cancelOrder", "url": "https://api.hyperliquid-testnet.xyz/exchange", "input": ["9078231563", "PURR/USDC"], "output": "{\"nonce\":1711798403045,\"action\":{\"type\":\"cancel\",\"cancels\":[{\"a\":10000,\"o\":9078231563}]},\"signature\":{\"r\":\"0xfe4aeec522ca718c78cf5e3b1775f2256edbcf1f92ad2b17bf40a5dd0872022d\",\"s\":\"0x67bc84c7445ab5abb1f06523775845755a35b4d9d6f728c0d7de9337d55eb51a\",\"v\":28}}"}], "cancelOrders": [{"description": "Fill this with a description of the method call", "method": "cancelOrders", "url": "https://api.hyperliquid-testnet.xyz/exchange", "input": [["6927265353", "6927351467"], "SOL/USDC:USDC"], "output": "{\"nonce\":1709652626969,\"action\":{\"type\":\"cancel\",\"cancels\":[{\"a\":5,\"o\":6927265353},{\"a\":5,\"o\":6927351467}]},\"signature\":{\"r\":\"0x546bc0dcf536756b5cce7206528aadf03a44d1b2a9b0a48c2e85fd21b1692b6f\",\"s\":\"0x5088740b9decdc966025ba75f127eb6cbb097eaab1835238dab8d95cfe101040\",\"v\":27}}"}], "fetchBalance": [{"description": "fetchBalance", "method": "fetchBalance", "url": "https://api.hyperliquid.xyz/info", "input": [{"user": "******************************************"}], "output": "{\"type\":\"clearinghouseState\",\"user\":\"******************************************\"}"}, {"description": "fetch spot balance", "method": "fetchBalance", "url": "https://api.hyperliquid.xyz/info", "input": [{"type": "spot", "user": "******************************************"}], "output": "{\"type\":\"spotClearinghouseState\",\"user\":\"******************************************\"}"}, {"description": "fetch balance for a subaccount", "method": "fetchBalance", "url": "https://api.hyperliquid.xyz/info", "input": [{"type": "spot", "subAccountAddress": "******************************************"}], "output": "{\"type\":\"spotClearinghouseState\",\"user\":\"******************************************\"}"}], "fetchOrderBook": [{"description": "fetch orderbook with kilo pair", "method": "fetchOrderBook", "url": "https://api.hyperliquid.xyz/info", "input": ["KPEPE/USDC:USDC"], "output": "{\"type\":\"l2Book\",\"coin\":\"kPEPE\"}"}, {"description": "fetchOrderBook", "method": "fetchOrderBook", "url": "https://api.hyperliquid.xyz/info", "input": ["ETH/USDC:USDC"], "output": "{\"type\":\"l2Book\",\"coin\":\"ETH\"}"}, {"description": "spot orderbook", "method": "fetchOrderBook", "url": "https://api.hyperliquid.xyz/info", "input": ["PURR/USDC"], "output": "{\"type\":\"l2Book\",\"coin\":\"PURR/USDC\"}"}], "fetchOHLCV": [{"description": "fetch ohlcv  with kilo pair", "method": "fetchOHLCV", "url": "https://api.hyperliquid.xyz/info", "input": ["KPEPE/USDC:USDC"], "output": "{\"type\":\"candleSnapshot\",\"req\":{\"coin\":\"kPEPE\",\"interval\":\"1m\",\"startTime\":0,\"endTime\":1752145874428}}"}, {"description": "fetchOHLCV", "method": "fetchOHLCV", "url": "https://api.hyperliquid.xyz/info", "input": ["ETH/USDC:USDC"], "output": "{\"type\":\"candleSnapshot\",\"req\":{\"coin\":\"ETH\",\"interval\":\"1m\",\"startTime\":0,\"endTime\":1704683640997}}"}, {"description": "spot ohlcv", "method": "fetchOHLCV", "url": "https://api.hyperliquid.xyz/info", "input": ["PURR/USDC"], "output": "{\"type\":\"candleSnapshot\",\"req\":{\"coin\":\"PURR/USDC\",\"interval\":\"1m\",\"startTime\":0,\"endTime\":1713610566832}}"}, {"description": "fetchOHLCV", "method": "fetchOHLCV", "url": "https://api.hyperliquid.xyz/info", "input": ["ETH/USDC:USDC", "1m", null, 10, {"until": "1730378220000"}], "output": "{\"type\":\"candleSnapshot\",\"req\":{\"coin\":\"ETH\",\"interval\":\"1m\",\"startTime\":1730342220000,\"endTime\":1730378220000}}"}], "fetchFundingRateHistory": [{"description": "funding rate history with kilo pair", "method": "fetchFundingRateHistory", "url": "https://api.hyperliquid.xyz/info", "input": ["KPEPE/USDC:USDC"], "output": "{\"type\":\"fundingHistory\",\"coin\":\"kPEPE\",\"startTime\":1750345830702}"}, {"description": "fetchFundingRateHistory", "method": "fetchFundingRateHistory", "url": "https://api.hyperliquid.xyz/info", "input": ["ETH/USDC:USDC", null, null, {"user": "******************************************"}], "output": "{\"type\":\"fundingHistory\",\"coin\":\"ETH\",\"startTime\":*************,\"user\":\"******************************************\"}"}], "fetchOpenOrders": [{"description": "fetchOpenOrders", "method": "fetchOpenOrders", "url": "https://api.hyperliquid.xyz/info", "input": ["ETH/USDC:USDC", null, null, {"user": "******************************************"}], "output": "{\"type\":\"frontendOpenOrders\",\"user\":\"******************************************\"}"}, {"description": "fetchOpenOrders for a subaccount", "method": "fetchOpenOrders", "url": "https://api.hyperliquid.xyz/info", "input": ["ETH/USDC:USDC", null, null, {"subAccountAddress": "******************************************"}], "output": "{\"type\":\"frontendOpenOrders\",\"user\":\"******************************************\"}"}], "fetchOrder": [{"description": "fetchOrder", "method": "fetchOrder", "url": "https://api.hyperliquid.xyz/info", "input": ["**********", "ETH/USDC:USDC", {"user": "******************************************"}], "output": "{\"type\":\"orderStatus\",\"oid\":**********,\"user\":\"******************************************\"}"}, {"description": "fetchOrder with clientOrderId", "method": "fetchOrder", "url": "https://api.hyperliquid-testnet.xyz/info", "input": ["0x1234567890abcdef1234567890abcded", "SOL/USDC:USDC", {"user": "******************************************"}], "output": "{\"type\":\"orderStatus\",\"oid\":\"0x1234567890abcdef1234567890abcded\",\"user\":\"******************************************\"}"}, {"description": "fetchOrder for a subaccount", "method": "fetchOrder", "url": "https://api.hyperliquid.xyz/info", "input": ["**********", "ETH/USDC:USDC", {"subAccountAddress": "******************************************"}], "output": "{\"type\":\"orderStatus\",\"oid\":**********,\"user\":\"******************************************\"}"}], "fetchTrades": [{"description": "fetchTrades", "method": "fetchTradesxp", "url": "https://api.hyperliquid.xyz/info", "input": ["ETH/USDC:USDC", null, null, {"user": "******************************************"}], "output": "{\"type\":\"userFills\",\"user\":\"******************************************\"}"}, {"description": "fetchTrades", "method": "fetchTrades", "url": "https://api.hyperliquid.xyz/info", "input": ["ETH/USDC:USDC", null, null, {"address": "******************************************"}], "output": "{\"type\":\"userFills\",\"user\":\"******************************************\"}"}, {"description": "fetchTrades for a subaccount", "method": "fetchTradesxp", "url": "https://api.hyperliquid.xyz/info", "input": ["ETH/USDC:USDC", null, null, {"subAccountAddress": "******************************************"}], "output": "{\"type\":\"userFills\",\"user\":\"******************************************\"}"}], "fetchMyTrades": [{"description": "fetchMyTrades", "method": "fetchMyTrades", "url": "https://api.hyperliquid.xyz/info", "input": ["ETH/USDC:USDC", null, null, {"user": "******************************************"}], "output": "{\"type\":\"userFills\",\"user\":\"******************************************\"}"}, {"description": "fetchMyTrades for a subaccount", "method": "fetchMyTrades", "url": "https://api.hyperliquid.xyz/info", "input": ["ETH/USDC:USDC", null, null, {"subAccountAddress": "******************************************"}], "output": "{\"type\":\"userFills\",\"user\":\"******************************************\"}"}], "fetchPosition": [{"description": "fetchPosition", "method": "fetchPosition", "url": "https://api.hyperliquid.xyz/info", "input": ["ETH/USDC:USDC", {"user": "******************************************"}], "output": "{\"type\":\"clearinghouseState\",\"user\":\"******************************************\"}"}, {"description": "fetchPosition for a subaccount", "method": "fetchPosition", "url": "https://api.hyperliquid.xyz/info", "input": ["ETH/USDC:USDC", {"subAccountAddress": "******************************************"}], "output": "{\"type\":\"clearinghouseState\",\"user\":\"******************************************\"}"}], "fetchPositions": [{"description": "fetchPositions", "method": "fetchPositions", "url": "https://api.hyperliquid.xyz/info", "input": [["ETH/USDC:USDC"], {"user": "******************************************"}], "output": "{\"type\":\"clearinghouseState\",\"user\":\"******************************************\"}"}, {"description": "fetchPositions for a subaccount", "method": "fetchPositions", "url": "https://api.hyperliquid.xyz/info", "input": [["ETH/USDC:USDC"], {"subAccountAddress": "******************************************"}], "output": "{\"type\":\"clearinghouseState\",\"user\":\"******************************************\"}"}], "setLeverage": [{"description": "set leverage", "method": "setLeverage", "url": "https://api.hyperliquid-testnet.xyz/exchange", "input": [5, "BTC/USDC:USDC"], "output": "{\"action\":{\"type\":\"updateLeverage\",\"asset\":0,\"isCross\":true,\"leverage\":5},\"nonce\":*************,\"signature\":{\"r\":\"0x5a9023d51d2c2d0d6eb243a423473a270b8ad80bb7284bc6d9d7a9007201026\",\"s\":\"0x3bd6c3257be38a5aac22bf5ecefe63ba0c5e4c39c9ac78d5d059e1dfe69dedd\",\"v\":27}}"}, {"description": "set leverage on a vault", "method": "setLeverage", "url": "https://api.hyperliquid-testnet.xyz/exchange", "input": [5, "SOL/USDC:USDC", {"vaultAddress": "******************************************"}], "output": "{\"action\":{\"type\":\"updateLeverage\",\"asset\":5,\"isCross\":true,\"leverage\":5},\"nonce\":*************,\"signature\":{\"r\":\"0xafbc0ef20e0b277cf4722f4ceb4fcf2f085c55a1ed41fa1a21fb9429772c5aa5\",\"s\":\"0x1975ac7cc0a533162ac24bb1457c4785d531f46e59eef33bf4a4131f1ea4b249\",\"v\":28},\"vaultAddress\":\"eba57e1d6bd242ba5f41b61ff1d30b481a535b58\"}"}, {"description": "set leverage with a subaccount", "method": "setLeverage", "url": "https://api.hyperliquid-testnet.xyz/exchange", "input": [5, "SOL/USDC:USDC", {"subAccountAddress": "******************************************"}], "output": "{\"action\":{\"type\":\"updateLeverage\",\"asset\":5,\"isCross\":true,\"leverage\":5},\"nonce\":*************,\"signature\":{\"r\":\"0xafbc0ef20e0b277cf4722f4ceb4fcf2f085c55a1ed41fa1a21fb9429772c5aa5\",\"s\":\"0x1975ac7cc0a533162ac24bb1457c4785d531f46e59eef33bf4a4131f1ea4b249\",\"v\":28},\"vaultAddress\":\"eba57e1d6bd242ba5f41b61ff1d30b481a535b58\"}"}], "setMarginMode": [{"description": "set cross margin mode", "method": "setMarginMode", "url": "https://api.hyperliquid-testnet.xyz/exchange", "input": ["cross", "SOL/USDC:USDC", {"leverage": 5}], "output": "{\"action\":{\"type\":\"updateLeverage\",\"asset\":5,\"isCross\":true,\"leverage\":5},\"nonce\":*************,\"signature\":{\"r\":\"0x4ecdba9896ecf5c2e3a8b00502039aff269d0878371953f505a4d70795893fa6\",\"s\":\"0x10a2b8fc5448ab711bcacadd24929c1d31d20fc8f45d3fd45e6f86e4c9e724ac\",\"v\":28}}"}, {"description": "set isolated margin mode", "method": "setMarginMode", "url": "https://api.hyperliquid-testnet.xyz/exchange", "input": ["isolated", "SOL/USDC:USDC", {"leverage": 5}], "output": "{\"action\":{\"type\":\"updateLeverage\",\"asset\":5,\"isCross\":false,\"leverage\":5},\"nonce\":1711360592621,\"signature\":{\"r\":\"0x233801ff1dc0ce66c1ee523317721e282f52ee3542a9195d3467430d1c70cad1\",\"s\":\"0x2c01401e469679d99893ceaaf5603c2e28279fd83c7802f43603ec6b3b4019b9\",\"v\":28}}"}], "transfer": [{"description": "transfer from spot to swap", "method": "transfer", "url": "https://api.hyperliquid-testnet.xyz/exchange", "input": ["USDC", 100, "spot", "swap"], "output": "{\"action\":{\"hyperliquidChain\":\"Mainnet\",\"signatureChainId\":\"0x66eee\",\"type\":\"usdClassTransfer\",\"amount\":\"100\",\"toPerp\":true,\"nonce\":1733369421517},\"nonce\":1733369421517,\"signature\":{\"r\":\"0x40e206a842100898c9496acbafaecb58f0d15cff709cff1f0ddd03eb024d758e\",\"s\":\"0x763bd3e89fc5bd4cceeb82e55b9ebf1445ffcdf9a1cb162a69e453ffb990a99b\",\"v\":27}}"}, {"description": "transfer from swap to spot", "method": "transfer", "url": "https://api.hyperliquid-testnet.xyz/exchange", "input": ["USDC", 100, "swap", "spot"], "output": "{\"action\":{\"hyperliquidChain\":\"Mainnet\",\"signatureChainId\":\"0x66eee\",\"type\":\"usdClassTransfer\",\"amount\":\"100\",\"toPerp\":false,\"nonce\":*************},\"nonce\":*************,\"signature\":{\"r\":\"0x6dea451134dc4ec8f5b83aff45d776b41874aaa0db42002b954d927d95872fa0\",\"s\":\"0x24f03636d21aa06a1029a3f7e67ba84454ed41af2d6e48033f186ec3bc4b4399\",\"v\":27}}"}, {"description": "transfer from main to subaccount", "method": "transfer", "url": "https://api.hyperliquid-testnet.xyz/exchange", "input": ["USDC", 100, "main", "******************************************"], "output": "{\"action\":{\"type\":\"subAccountTransfer\",\"subAccountUser\":\"******************************************\",\"isDeposit\":true,\"usd\":*********},\"nonce\":*************,\"signature\":{\"r\":\"0x575f802d4117366cdfc2df33185bf21af6d2cebf1549689cc23082ffcf232a5d\",\"s\":\"0x661b7e839f0a66de5c63f9d3ff437f0455f74f7e3218332e26f6f2616b0e50eb\",\"v\":27}}"}, {"description": "transfer from subaccount to main", "method": "transfer", "url": "https://api.hyperliquid-testnet.xyz/exchange", "input": ["USDC", 100, "******************************************", "main"], "output": "{\"action\":{\"type\":\"subAccountTransfer\",\"subAccountUser\":\"******************************************\",\"isDeposit\":false,\"usd\":*********},\"nonce\":*************,\"signature\":{\"r\":\"0x575f802d4117366cdfc2df33185bf21af6d2cebf1549689cc23082ffcf232a5d\",\"s\":\"0x661b7e839f0a66de5c63f9d3ff437f0455f74f7e3218332e26f6f2616b0e50eb\",\"v\":27}}"}], "withdraw": [{"description": "withdraw on chain", "method": "withdraw", "url": "https://api.hyperliquid-testnet.xyz/exchange", "input": ["USDC", 100, "******************************************"], "output": "{\"action\":{\"hyperliquidChain\":\"Mainnet\",\"signatureChainId\":\"0x66eee\",\"destination\":\"******************************************\",\"amount\":\"100\",\"time\":*************,\"type\":\"withdraw3\"},\"nonce\":*************,\"signature\":{\"r\":\"0x10bb60d01ebab494400e5509b50881f6967389adbe04579419ae5fdb3555fae1\",\"s\":\"0x232044e07569968bfe6f2a52ecb0d9e85b9f5215174e39c8a82241d244860b9\",\"v\":27}}"}, {"description": "withdraw mainnet", "method": "withdraw", "url": "https://api.hyperliquid.xyz/exchange", "input": ["USDC", 10, "******************************************"], "output": "{\"action\":{\"hyperliquidChain\":\"Mainnet\",\"signatureChainId\":\"0x66eee\",\"destination\":\"******************************************\",\"amount\":\"10\",\"time\":1718451417480,\"type\":\"withdraw3\"},\"nonce\":1718451417480,\"signature\":{\"r\":\"0x307d794d5235c9d7d6b931741a487b6b0ec2a756177b7b473c6746c96f31d23c\",\"s\":\"0x27734ee21a070c0f97ffc1f5de99b70120f8c5c6b352e4e5f4e88d48cafd02ce\",\"v\":27}}"}, {"description": "withdraw from vault", "method": "withdraw", "url": "https://api.hyperliquid-testnet.xyz/exchange", "input": ["USDC", 100, "******************************************", "", {"vaultAddress": "******************************************"}], "output": "{\"action\":{\"vaultAddress\":\"******************************************\",\"isDeposit\":false,\"usd\":100,\"type\":\"vaultTransfer\"},\"nonce\":*************,\"signature\":{\"r\":\"0x10bb60d01ebab494400e5509b50881f6967389adbe04579419ae5fdb3555fae1\",\"s\":\"0x232044e07569968bfe6f2a52ecb0d9e85b9f5215174e39c8a82241d244860b9\",\"v\":27}}"}], "cancelOrdersForSymbols": [{"description": "cancel swap orders with different symbols", "method": "cancelOrdersForSymbols", "url": "https://api.hyperliquid-testnet.xyz/exchange", "input": [[{"id": "7556739260", "symbol": "SOL/USDC:USDC"}, {"id": "7556435631", "symbol": "ADA/USDC:USDC"}]], "output": "{\"nonce\":1713463292519,\"action\":{\"type\":\"cancel\",\"cancels\":[{\"a\":5,\"o\":7556739260},{\"a\":65,\"o\":7556435631}]},\"signature\":{\"r\":\"0x82614f598ccba0a59c0468990a7041c7ff72c0daf33d3398873c60e31234fb94\",\"s\":\"0x3a6084b754bc2d9967511f8c6286c0f86e3625c8e4c43ee9d6932ec011ae159a\",\"v\":28}}"}, {"description": "cancelOrders by clientOrderId", "method": "cancelOrdersForSymbols", "url": "https://api.hyperliquid-testnet.xyz/exchange", "input": [[{"clientOrderId": "0x1234567890abcdef1234567890abcded", "symbol": "SOL/USDC:USDC"}]], "output": "{\"nonce\":1713523049140,\"action\":{\"type\":\"cancelByCloid\",\"cancels\":[{\"asset\":5,\"cloid\":\"0x1234567890abcdef1234567890abcded\"}]},\"signature\":{\"r\":\"0xa271d3d3d5dc27f6bfa2b63770c3bcb0f8610e832d0c7b2a4b7cff7f94f5301d\",\"s\":\"0x7986c12c100b8ac2ab8f63ac4a04d36f17e2c97b9341ad7519713ac5d5cf27f\",\"v\":27}}"}], "fetchTradingFee": [{"description": "fetchTradingFee", "method": "fetchTradingFee", "url": "https://api.hyperliquid-testnet.xyz/info", "input": ["SOL/USDC:USDC", {"user": "******************************************"}], "output": "{\"type\":\"userFees\",\"user\":\"******************************************\"}"}, {"description": "fetchTradingFee for a subaccount", "method": "fetchTradingFee", "url": "https://api.hyperliquid-testnet.xyz/info", "input": ["SOL/USDC:USDC", {"subAccountAddress": "******************************************"}], "output": "{\"type\":\"userFees\",\"user\":\"******************************************\"}"}], "fetchLedger": [{"description": "fetchLedger", "method": "fetchLedger", "url": "https://api.hyperliquid.xyz/info", "input": ["USDC", *************, 10, {"user": "******************************************"}], "output": "{\"type\":\"userNonFundingLedgerUpdates\",\"user\":\"******************************************\",\"startTime\":*************}"}, {"description": "fetchLedger for a subaccount", "method": "fetchLedger", "url": "https://api.hyperliquid.xyz/info", "input": ["USDC", *************, 10, {"subAccountAddress": "******************************************"}], "output": "{\"type\":\"userNonFundingLedgerUpdates\",\"user\":\"******************************************\",\"startTime\":*************}"}], "fetchDeposits": [{"description": "fetchDeposits", "method": "fetchDeposits", "url": "https://api.hyperliquid.xyz/info", "input": ["USDC", *************, 10, {"user": "******************************************"}], "output": "{\"type\":\"userNonFundingLedgerUpdates\",\"user\":\"******************************************\",\"startTime\":*************}"}, {"description": "fetchDeposits for a subaccount", "method": "fetchDeposits", "url": "https://api.hyperliquid.xyz/info", "input": ["USDC", *************, 10, {"subAccountAddress": "******************************************"}], "output": "{\"type\":\"userNonFundingLedgerUpdates\",\"user\":\"******************************************\",\"startTime\":*************}"}], "fetchWithdrawals": [{"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "method": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://api.hyperliquid.xyz/info", "input": ["USDC", *************, 10, {"user": "******************************************"}], "output": "{\"type\":\"userNonFundingLedgerUpdates\",\"user\":\"******************************************\",\"startTime\":*************}"}, {"description": "fetchWithdrawals for a subaccount", "method": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://api.hyperliquid.xyz/info", "input": ["USDC", *************, 10, {"subAccountAddress": "******************************************"}], "output": "{\"type\":\"userNonFundingLedgerUpdates\",\"user\":\"******************************************\",\"startTime\":*************}"}]}}