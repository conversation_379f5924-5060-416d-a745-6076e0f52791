{"exchange": "ascendex", "skipKeys": ["time", "account-group", "requestTime"], "outputType": "json", "methods": {"createOrder": [{"description": "Spot limit order", "method": "createOrder", "url": "https://ascendex.com/myAccount/api/pro/v1/cash/order", "input": ["BTC/USDT", "limit", "buy", 0.0002, 25000], "output": "{\"symbol\":\"BTC/USDT\",\"time\":*************,\"orderQty\":\"0.0002\",\"orderType\":\"limit\",\"side\":\"buy\",\"orderPrice\":\"25000\",\"category\":\"cash\"}"}, {"description": "Spot market order", "method": "createOrder", "url": "https://ascendex.com/myAccount/api/pro/v1/cash/order", "input": ["BTC/USDT", "market", "buy", 0.0002], "output": "{\"symbol\":\"BTC/USDT\",\"time\":*************,\"orderQty\":\"0.0002\",\"orderType\":\"market\",\"side\":\"buy\",\"category\":\"cash\"}"}, {"description": "Spot margin limit order", "method": "createOrder", "url": "https://ascendex.com/myAccount/api/pro/v1/margin/order", "input": ["BTC/USDT", "limit", "buy", 0.0002, 25000, {"marginMode": "cross"}], "output": "{\"symbol\":\"BTC/USDT\",\"time\":*************,\"orderQty\":\"0.0002\",\"orderType\":\"limit\",\"side\":\"buy\",\"orderPrice\":\"25000\",\"category\":\"margin\"}"}, {"description": "Spot margin market order", "method": "createOrder", "url": "https://ascendex.com/myAccount/api/pro/v1/margin/order", "input": ["BTC/USDT", "market", "buy", 0.0002, null, {"marginMode": "cross"}], "output": "{\"symbol\":\"BTC/USDT\",\"time\":*************,\"orderQty\":\"0.0002\",\"orderType\":\"market\",\"side\":\"buy\",\"category\":\"margin\"}"}], "createOrders": [{"description": "Spot create multiple limit orders at once", "method": "createOrders", "url": "https://ascendex.com/myAccount/api/pro/v1/cash/order/batch", "input": [[{"symbol": "BTC/USDT", "type": "limit", "side": "buy", "amount": "0.0002", "price": "25000"}, {"symbol": "BTC/USDT", "type": "limit", "side": "buy", "amount": "0.0002", "price": "27000"}]], "output": "{\"orders\":[{\"account-group\":\"6\",\"account-category\":\"cash\",\"symbol\":\"BTC/USDT\",\"time\":*************,\"orderQty\":\"0.0002\",\"orderType\":\"limit\",\"side\":\"buy\",\"orderPrice\":\"25000\",\"category\":\"cash\"},{\"account-group\":\"6\",\"account-category\":\"cash\",\"symbol\":\"BTC/USDT\",\"time\":*************,\"orderQty\":\"0.0002\",\"orderType\":\"limit\",\"side\":\"buy\",\"orderPrice\":\"27000\",\"category\":\"cash\"}]}"}, {"description": "Spot margin create multiple limit orders at once", "method": "createOrders", "url": "https://ascendex.com/myAccount/api/pro/v1/margin/order/batch", "input": [[{"symbol": "BTC/USDT", "type": "limit", "side": "buy", "amount": 0.0002, "price": 25000, "params": {"marginMode": "cross"}}, {"symbol": "BTC/USDT", "type": "limit", "side": "buy", "amount": 0.0002, "price": 27000, "params": {"marginMode": "cross"}}]], "output": "{\"orders\":[{\"account-group\":\"6\",\"account-category\":\"margin\",\"symbol\":\"BTC/USDT\",\"time\":*************,\"orderQty\":\"0.0002\",\"orderType\":\"limit\",\"side\":\"buy\",\"orderPrice\":\"25000\",\"category\":\"margin\"},{\"account-group\":\"6\",\"account-category\":\"margin\",\"symbol\":\"BTC/USDT\",\"time\":*************,\"orderQty\":\"0.0002\",\"orderType\":\"limit\",\"side\":\"buy\",\"orderPrice\":\"27000\",\"category\":\"margin\"}]}"}], "fetchDeposits": [{"description": "Fetch deposits", "method": "fetchDeposits", "url": "https://ascendex.com/api/pro/v1/wallet/transactions?txType=deposit", "input": []}], "fetchBalance": [{"description": "Fetch spot balance", "method": "fetchBalance", "url": "https://ascendex.com/myAccount/api/pro/v1/cash/balance", "input": [{"type": "spot"}]}, {"description": "Fetch margin balance", "method": "fetchBalance", "url": "https://ascendex.com/myAccount/api/pro/v1/margin/balance", "input": [{"margin": true}]}, {"description": "Fetch swap balance", "method": "fetchBalance", "url": "https://ascendex.com/myAccount/api/pro/v2/futures/position", "input": [{"type": "swap"}]}], "fetchOrder": [{"description": "Fetch spot order", "method": "fetchOrder", "url": "https://ascendex.com/myAccount/api/pro/v1/cash/order/status?orderId=1234", "input": ["1234", "BTC/USDT"]}, {"description": "Fetch margin order", "method": "fetchOrder", "url": "https://ascendex.com/myAccount/api/pro/v1/margin/order/status?orderId=1234", "input": ["1234", null, {"type": "margin"}]}, {"description": "Fetch swap order", "method": "fetchOrder", "url": "https://ascendex.com/myAccount/api/pro/v2/futures/order/status?orderId=1234", "input": ["1234", "BTC/USDT:USDT"]}], "fetchOpenOrders": [{"description": "Fetch spot open order", "method": "fetchOpenOrders", "url": "https://ascendex.com/myAccount/api/pro/v1/cash/order/open", "input": ["BTC/USDT"]}, {"description": "Fetch margin open order", "method": "fetchOpenOrders", "url": "https://ascendex.com/myAccount/api/pro/v1/margin/order/open", "input": [null, 1, 1, {"type": "margin"}]}, {"description": "Fetch swap open order", "method": "fetchOpenOrders", "url": "https://ascendex.com/myAccount/api/pro/v2/futures/order/open", "input": ["BTC/USDT:USDT"]}], "fetchClosedOrders": [{"description": "Fetch spot closed order", "method": "fetchClosedOrders", "url": "https://ascendex.com/api/pro/data/v2/order/hist?symbol=BTC%2FUSDT&account=cash", "input": ["BTC/USDT"]}, {"description": "Fetch margin closed order", "method": "fetchClosedOrders", "url": "https://ascendex.com/api/pro/data/v2/order/hist?symbol=BTC%2FUSDT&startTime=1&limit=1&account=margin", "input": ["BTC/USDT", 1, 1, {"type": "margin"}]}, {"description": "Fetch swap closed order", "method": "fetchClosedOrders", "url": "https://ascendex.com/myAccount/api/pro/v2/futures/order/hist/current?symbol=BTC-PERP", "input": ["BTC/USDT:USDT"]}], "cancelOrder": [{"description": "Cancel spot order", "method": "cancelOrder", "url": "https://ascendex.com/myAccount/api/pro/v1/cash/order", "input": ["1234", "BTC/USDT"], "output": "{\"symbol\":\"BTC/USDT\",\"time\":*************,\"id\":\"foobar\",\"orderId\":\"1234\"}"}, {"description": "Cancel margin order", "method": "cancelOrder", "url": "https://ascendex.com/myAccount/api/pro/v1/margin/order", "input": ["1234", "BTC/USDT", {"type": "margin"}], "output": "{\"symbol\":\"BTC/USDT\",\"time\":*************,\"id\":\"foobar\",\"orderId\":\"1234\"}"}, {"description": "Cancel swap order", "method": "cancelOrder", "url": "https://ascendex.com/myAccount/api/pro/v2/futures/order", "input": ["1234", "BTC/USDT:USDT"], "output": "{\"symbol\":\"BTC-PERP\",\"time\":*************,\"id\":\"foobar\",\"orderId\":\"1234\"}"}], "cancelAllOrders": [{"description": "Cancel all spot orders", "method": "cancelOrder", "url": "https://ascendex.com/myAccount/api/pro/v1/cash/order/all", "input": ["BTC/USDT"], "output": "{\"symbol\":\"BTC/USDT\",\"time\":*************}"}, {"description": "Cancel all margin orders", "method": "cancelOrder", "url": "https://ascendex.com/myAccount/api/pro/v1/margin/order/all", "input": ["BTC/USDT", {"type": "margin"}], "output": "{\"symbol\":\"BTC/USDT\",\"time\":*************}"}, {"description": "Cancel all swap orders", "method": "cancelOrder", "url": "https://ascendex.com/myAccount/api/pro/v2/futures/order/all", "input": ["BTC/USDT:USDT"], "output": "{\"symbol\":\"BTC-PERP\",\"time\":*************}"}], "fetchDepositAddress": [{"description": "fetch deposit address", "method": "fetchDepositAddress", "url": "https://ascendex.com/api/pro/v1/wallet/deposit/address?asset=USDT&blockchain=TRC20&network=TRC20", "input": ["USDT", {"network": "TRC20"}]}], "transfer": [{"description": "transfer from spot to futures", "method": "transfer", "disabled": true, "url": "https://ascendex.com/3/api/pro/v1/transfer", "input": ["USDT", 1, "cash", "futures"], "output": "{\"amount\":\"1\",\"asset\":\"USDT\",\"fromAccount\":\"cash\",\"toAccount\":\"futures\"}"}], "fetchTime": [{"description": "fetchTime", "method": "fetchTime", "url": "https://ascendex.com/api/pro/v1/exchange-info?requestTime=*************", "input": []}], "fetchTrades": [{"description": "spot fetchTrades", "method": "fetchTrades", "url": "https://ascendex.com/api/pro/v1/trades?symbol=BTC%2FUSDT", "input": ["BTC/USDT"]}, {"description": "swap fetchTrades", "method": "fetchTrades", "url": "https://ascendex.com/api/pro/v1/trades?symbol=BTC-PERP", "input": ["BTC/USDT:USDT"]}], "fetchOrderBook": [{"description": "spot orderbook", "method": "fetchOrderBook", "url": "https://ascendex.com/api/pro/v1/depth?symbol=BTC%2FUSDT", "input": ["BTC/USDT"]}, {"description": "swap orderbook", "method": "fetchOrderBook", "url": "https://ascendex.com/api/pro/v1/depth?symbol=BTC-PERP", "input": ["BTC/USDT:USDT"]}], "fetchTicker": [{"description": "Swap ticker", "method": "fetchTicker", "url": "https://ascendex.com/api/pro/v1/ticker?symbol=BTC-PERP", "input": ["BTC/USDT:USDT"]}, {"description": "spot ticker", "method": "fetchTicker", "url": "https://ascendex.com/api/pro/v1/ticker?symbol=BTC%2FUSDT", "input": ["BTC/USDT"]}], "fetchTickers": [{"description": "spot tickers", "method": "fetchTickers", "url": "https://ascendex.com/api/pro/v1/ticker?symbol=BTC%2FUSDT%2CETH%2FUSDT", "input": [["BTC/USDT", "ETH/USDT"]]}, {"description": "swap tickers", "method": "fetchTickers", "url": "https://ascendex.com/api/pro/v2/futures/ticker?symbol=BTC-PERP%2CETH-PERP", "input": [["BTC/USDT:USDT", "ETH/USDT:USDT"]]}], "fetchOHLCV": [{"description": "spot ohlcv", "method": "fetchOHLCV", "url": "https://ascendex.com/api/pro/v1/barhist?symbol=BTC%2FUSDT&interval=1", "input": ["BTC/USDT"]}, {"description": "fetchOHLCV with since and limit", "method": "fetchOHLCV", "url": "https://ascendex.com/api/pro/v1/barhist?symbol=BTC%2FUSD&interval=60&from=1735776000000&to=1735786800001", "input": ["BTC/USD", "1h", 1735776000000, 3]}, {"description": "fetchOHLCV with since", "method": "fetchOHLCV", "url": "https://ascendex.com/api/pro/v1/barhist?symbol=BTC%2FUSD&interval=60&from=1735776000000&to=1737576000001", "input": ["BTC/USD", "1h", 1735776000000]}, {"description": "fetchOHLVC with limit and until", "method": "fetchOHLCV", "url": "https://ascendex.com/api/pro/v1/barhist?symbol=BTC-PERP&interval=60&to=*************&from=1735678800000", "input": ["BTC/USDT:USDT", "1h", null, 3, {"until": *************}]}, {"description": "fetchOHLCV with until", "method": "fetchOHLCV", "url": "https://ascendex.com/api/pro/v1/barhist?symbol=BTC-PERP&interval=60&to=*************&from=1733889600000", "input": ["BTC/USDT:USDT", "1h", null, null, {"until": *************}]}, {"description": "fetchOHLCV with limit", "method": "fetchOHLCV", "url": "https://ascendex.com/api/pro/v1/barhist?symbol=BTC-PERP&interval=60&n=3", "input": ["BTC/USDT:USDT", "1h", null, 3]}, {"description": "fetchOHLCV with since and until", "method": "fetchOHLCV", "url": "https://ascendex.com/api/pro/v1/barhist?symbol=BTC-PERP&interval=60&from=*************&to=*************", "input": ["BTC/USDT:USDT", "1h", *************, null, {"until": *************}]}, {"description": "fetchOHLCV with since limit and until", "method": "fetchOHLCV", "url": "https://ascendex.com/api/pro/v1/barhist?symbol=BTC-PERP&interval=60&from=*************&to=*************", "input": ["BTC/USDT:USDT", "1h", *************, 2, {"until": *************}]}, {"description": "swap ohlcv", "method": "fetchOHLCV", "url": "https://ascendex.com/api/pro/v1/barhist?symbol=BTC-PERP&interval=1", "input": ["BTC/USDT:USDT"]}], "fetchMarginModes": [{"description": "Fetch all of the set margin modes", "method": "fetchMarginModes", "url": "https://ascendex.com/myAccount/api/pro/v2/futures/position", "input": []}], "fetchMarginMode": [{"description": "Fetch the set margin mode of a specified market", "method": "fetchMarginMode", "url": "https://ascendex.com/myAccount/api/pro/v2/futures/position", "input": ["BTC/USDT:USDT"]}], "fetchFundingRate": [{"description": "fundingRate", "method": "fetchFundingRate", "url": "https://ascendex.com/api/pro/v2/futures/pricing-data", "input": ["BTC/USDT:USDT"]}], "fetchLeverages": [{"description": "Fetch all set leverages", "method": "fetchLeverages", "url": "https://ascendex.com/myAccount/api/pro/v2/futures/position", "input": []}], "fetchLeverage": [{"description": "Fetch the set leverage of a specified market", "method": "fetchLeverage", "url": "https://ascendex.com/myAccount/api/pro/v2/futures/position", "input": ["BTC/USDT:USDT"]}], "fetchCurrencies": [{"disabled": "until multi-requests methods are supported", "description": "fetchCurrencies", "method": "fetchCurrencies", "url": "https://ascendex.com/api/pro/v1/assets", "input": []}], "fetchMarkets": [{"disabled": "until multi-requests methods are supported", "description": "fetchMarkets", "method": "fetchMarkets", "url": "https://ascendex.com/api/pro/v1/products", "input": []}], "fetchAccounts": [{"description": "fetchAccounts", "method": "fetchAccounts", "url": "https://ascendex.com/api/pro/v1/info", "input": []}], "fetchTradingFees": [{"description": "fetchTradingFees", "method": "fetchTradingFees", "url": "https://ascendex.com/myAccount/api/pro/v1/spot/fee", "input": []}], "fetchWithdrawals": [{"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "method": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://ascendex.com/api/pro/v1/wallet/transactions?txType=withdrawal", "input": []}], "fetchDepositsWithdrawals": [{"description": "fetchDepositsWithdrawals", "method": "fetchDepositsWithdrawals", "url": "https://ascendex.com/api/pro/v1/wallet/transactions", "input": []}], "fetchPositions": [{"description": "fetchPositions", "method": "fetchPositions", "url": "https://ascendex.com/myAccount/api/pro/v2/futures/position", "input": [["BTC/USDT:USDT"]]}], "fetchFundingRates": [{"description": "fetchFundingRates", "method": "fetchFundingRates", "url": "https://ascendex.com/api/pro/v2/futures/pricing-data", "input": [["BTC/USDT:USDT"]]}], "reduceMargin": [{"description": "reduce<PERSON><PERSON>gin", "method": "reduce<PERSON><PERSON>gin", "url": "https://ascendex.com/myAccount/api/pro/v2/futures/isolated-position-margin", "input": ["BTC/USDT:USDT", 1], "output": "{\"symbol\":\"BTC-PERP\",\"amount\":\"-1\"}"}], "addMargin": [{"description": "add<PERSON><PERSON>gin", "method": "add<PERSON><PERSON>gin", "url": "https://ascendex.com/myAccount/api/pro/v2/futures/isolated-position-margin", "input": ["BTC/USDT:USDT", 1], "output": "{\"symbol\":\"BTC-PERP\",\"amount\":\"1\"}"}], "setLeverage": [{"description": "setLeverage", "method": "setLeverage", "url": "https://ascendex.com/myAccount/api/pro/v2/futures/leverage", "input": [5, "BTC/USDT:USDT"], "output": "{\"symbol\":\"BTC-PERP\",\"leverage\":5}"}], "setMarginMode": [{"description": "setMarginMode", "method": "setMarginMode", "url": "https://ascendex.com/myAccount/api/pro/v2/futures/margin-type", "input": ["cross", "BTC/USDT:USDT"], "output": "{\"symbol\":\"BTC-PERP\",\"marginType\":\"crossed\"}"}], "fetchLeverageTiers": [{"description": "fetchLeverageTiers", "method": "fetchLeverageTiers", "url": "https://ascendex.com/api/pro/v2/futures/contract", "input": []}], "fetchDepositWithdrawFees": [{"description": "fetchDepositWithdrawFees", "method": "fetchDepositWithdrawFees", "url": "https://ascendex.com/api/pro/v2/assets", "input": []}], "fetchFundingHistory": [{"description": "fetchFundingHistory", "method": "fetchFundingHistory", "url": "https://ascendex.com/myAccount/api/pro/v2/futures/funding-payments?symbol=BTC-PERP", "input": ["BTC/USDT:USDT"]}]}}