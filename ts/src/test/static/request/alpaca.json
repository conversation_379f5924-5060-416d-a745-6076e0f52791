{"exchange": "alpaca", "skipKeys": ["client_order_id"], "outputType": "json", "methods": {"createMarketBuyOrderWithCost": [{"description": "order with cost", "method": "createMarketBuyOrderWithCost", "url": "https://paper-api.alpaca.markets/v2/orders", "input": ["LTC/USD", 15], "output": "{\"symbol\":\"LTC/USD\",\"side\":\"buy\",\"type\":\"market\",\"notional\":\"15\",\"time_in_force\":\"gtc\",\"client_order_id\":\"ccxt_ae433584bd694fcb96d0f3e1333ad236\"}"}], "fetchTime": [{"description": "fetchTime", "method": "fetchTime", "url": "https://paper-api.alpaca.markets/v2/clock", "input": []}], "fetchMarkets": [{"description": "fetchMarkets", "method": "fetchMarkets", "url": "https://paper-api.alpaca.markets/v2/assets?asset_class=crypto&status=active", "input": []}], "fetchTrades": [{"description": "Fetch Trades", "method": "fetchTrades", "url": "https://data.alpaca.markets/v1beta3/crypto/us/trades?symbols=BTC%2FUSD", "input": ["BTC/USD"]}, {"description": "Fetch trades with method: marketPublicGetV1beta3CryptoLocLatestTrades", "method": "fetchTrades", "url": "https://data.alpaca.markets/v1beta3/crypto/us/latest/trades?symbols=BTC%2FUSDT", "input": ["BTC/USDT", null, null, {"method": "marketPublicGetV1beta3CryptoLocLatestTrades"}]}, {"description": "Fetch trades with method: marketPublicGetV1beta3CryptoLocTrades", "method": "fetchTrades", "url": "https://data.alpaca.markets/v1beta3/crypto/us/trades?symbols=BTC%2FUSD", "input": ["BTC/USD", null, null, {"method": "marketPublicGetV1beta3CryptoLocTrades"}]}], "fetchOrderBook": [{"description": "fetchOrderBook", "method": "fetchOrderBook", "url": "https://paper-api.alpaca.markets/v1beta3/crypto/us/latest/orderbooks?symbols=BTC%2FUSD", "input": ["BTC/USD"]}, {"description": "fetch order book with limit", "method": "fetchOrderBook", "url": "https://data.alpaca.markets/v1beta3/crypto/us/latest/orderbooks?symbols=BTC%2FUSDT", "input": ["BTC/USDT", 1]}], "fetchOHLCV": [{"description": "fetchOHLCV", "method": "fetchOHLCV", "url": "https://paper-api.alpaca.markets/v1beta3/crypto/us/bars?symbols=BTC%2FUSD&timeframe=1min", "input": ["BTC/USD", "1m"]}], "fetchTicker": [{"description": "spot fetch ticker", "method": "fetchTicker", "url": "https://data.alpaca.markets/v1beta3/crypto/us/snapshots?symbols=BTC%2FUSD", "input": ["BTC/USD"]}], "fetchTickers": [{"description": "spot fetch multiple tickers at once", "method": "fetchTickers", "url": "https://data.alpaca.markets/v1beta3/crypto/us/snapshots?symbols=BTC%2FUSD%2CLTC%2FUSD", "input": [["BTC/USD", "LTC/USD"]]}], "createOrder": [{"description": "Spot market buy", "method": "createOrder", "url": "https://paper-api.alpaca.markets/v2/orders", "input": ["BTC/USD", "market", "buy", "1", "5"], "output": "{\"symbol\":\"BTC/USD\",\"qty\":\"1\",\"side\":\"buy\",\"type\":\"market\",\"time_in_force\":\"gtc\",\"client_order_id\":\"ccxt_b17b85b6309a4e9e91eb1f3ad5591efe\"}"}, {"description": "Spot market sell", "method": "createOrder", "url": "https://paper-api.alpaca.markets/v2/orders", "input": ["BTC/USD", "market", "sell", 0.001], "output": "{\"symbol\":\"BTC/USD\",\"qty\":\"0.001\",\"side\":\"sell\",\"type\":\"market\",\"time_in_force\":\"gtc\",\"client_order_id\":\"ccxt_f7e2354ca93c4f1d9cb351b584863faa\"}"}, {"description": "Spot limit buy", "method": "createOrder", "url": "https://paper-api.alpaca.markets/v2/orders", "input": ["BTC/USD", "limit", "buy", 0.001, "20000"], "output": "{\"symbol\":\"BTC/USD\",\"qty\":\"0.001\",\"side\":\"buy\",\"type\":\"limit\",\"limit_price\":\"20000\",\"time_in_force\":\"gtc\",\"client_order_id\":\"ccxt_0b7a535d63a745659e6cc4ffe97e0b7b\"}"}], "editOrder": [{"description": "edit order", "method": "editOrder", "url": "https://paper-api.alpaca.markets/v2/orders/5951bf3a-1b42-42da-a8d0-a86bad815f33", "input": ["5951bf3a-1b42-42da-a8d0-a86bad815f33", "LTC/USD", "limit", "buy", 0.2, 56], "output": "{\"qty\":\"0.2\",\"limit_price\":\"56\",\"time_in_force\":\"gtc\",\"client_order_id\":\"ccxt_ced70043a38849718597ebad4846d007\"}"}], "cancelOrder": [{"description": "Cancel order", "method": "cancelOrder", "url": "https://paper-api.alpaca.markets/v2/orders/eb625a98-5137-4c3d-b88c-699151ed21a8", "input": ["eb625a98-5137-4c3d-b88c-699151ed21a8"]}], "cancelAllOrders": [{"description": "cancelAllOrders", "method": "cancelAllOrders", "url": "https://paper-api.alpaca.markets/v2/orders", "input": ["BTC/USD"]}], "fetchOrder": [{"description": "Fetch order", "method": "fetchOrder", "url": "https://paper-api.alpaca.markets/v2/orders/eb625a98-5137-4c3d-b88c-699151ed21a8", "input": ["eb625a98-5137-4c3d-b88c-699151ed21a8"]}], "fetchOrders": [{"description": "trades with since and until", "method": "fetchOrders", "url": "https://paper-api.alpaca.markets/v2/orders?status=all&symbols=BTC%2FUSDT&endTime=2025-02-14T15%3A54%3A04.000Z&after=2025-02-10T15%3A54%3A04.000Z&limit=100", "input": ["BTC/USDT", *************, 100, {"until": *************}], "output": null}, {"description": "Spot open orders", "method": "fetchOrders", "url": "https://api.alpaca.markets/v2/orders?status=all&symbols=LTC%2FUSDT", "input": ["LTC/USDT"]}], "fetchOpenOrders": [{"description": "Spot open orders", "method": "fetchOpenOrders", "url": "https://api.alpaca.markets/v2/orders?status=open&symbols=LTC%2FUSDT", "input": ["LTC/USDT"]}], "fetchClosedOrders": [{"description": "Spot closed orders", "method": "fetchClosedOrders", "url": "https://api.alpaca.markets/v2/orders?status=closed&symbols=LTC%2FUSDT", "input": ["LTC/USDT"]}], "fetchMyTrades": [{"description": "trades with since and until", "method": "fetchMyTrades", "url": "https://paper-api.alpaca.markets/v2/account/activities/FILL?until=2025-02-14T15%3A54%3A04.000Z&after=2025-02-10T15%3A54%3A04.000Z&page_size=100", "input": ["BTC/USDT", *************, 100, {"until": *************}], "output": null}, {"description": "spot fetch my trades with a limit argument", "method": "fetchMyTrades", "url": "https://api.alpaca.markets/v2/account/activities/FILL?page_size=3", "input": ["LTC/USD", null, 3]}], "fetchDepositAddress": [{"description": "fetch the BTC deposit address", "method": "fetchDepositAddress", "url": "https://api.alpaca.markets/v2/wallets?asset=BTC", "input": ["BTC"]}], "fetchDeposits": [{"description": "fetch USDT deposits", "method": "fetchDeposits", "url": "https://api.alpaca.markets/v2/wallets/transfers", "input": ["USDT"]}], "fetchWithdrawals": [{"description": "fetch USDT withdrawals", "method": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://api.alpaca.markets/v2/wallets/transfers", "input": ["USDT"]}], "fetchDepositsWithdrawals": [{"description": "fetch USDT deposits and withdrawals", "method": "fetchDepositsWithdrawals", "url": "https://api.alpaca.markets/v2/wallets/transfers", "input": ["USDT"]}], "withdraw": [{"description": "withdraw 20 USDT to a whitelisted address", "method": "withdraw", "url": "https://api.alpaca.markets/v2/wallets/transfers", "input": ["USDT", 20, "******************************************"], "output": "{\"asset\":\"USDT\",\"address\":\"******************************************\",\"amount\":\"20\"}"}], "fetchBalance": [{"description": "fetch balance", "method": "fetchBalance", "url": "https://api.alpaca.markets/v2/account", "input": []}]}}