{"exchange": "poloniex", "skipKeys": ["start", "end"], "outputType": "json", "methods": {"withdraw": [{"description": "withdraw USDT on ETH", "method": "withdraw", "url": "https://api.poloniex.com/wallets/withdraw", "input": ["USDT", 5, "******************************************", null, {"network": "ETH"}], "output": "{\"currency\":\"USDTETH\",\"amount\":\"5\",\"address\":\"******************************************\"}"}], "fetchDepositAddress": [{"description": "fetchDepositAddress ETH on ETH", "method": "fetchDepositAddress", "url": "https://api.poloniex.com/wallets/addresses?currency=ETH", "input": ["ETH", {"network": "ETH"}], "output": null}, {"description": "fetchDepositAddress USDT on TRC20", "method": "fetchDepositAddress", "url": "https://api.poloniex.com/wallets/addresses?currency=USDTTRON", "input": ["USDT", {"network": "TRC20"}], "output": null}, {"description": "fetchDepositAddress USDT on ERC20", "method": "fetchDepositAddress", "url": "https://api.poloniex.com/wallets/addresses?currency=USDTETH", "input": ["USDT", {"network": "ERC20"}], "output": null}, {"description": "fetchDepositAddress USDT on ETH", "method": "fetchDepositAddress", "url": "https://api.poloniex.com/wallets/addresses?currency=USDTETH", "input": ["USDT", {"network": "ETH"}], "output": null}, {"description": "fetchDepositAddress BTC on BTC", "method": "fetchDepositAddress", "url": "https://api.poloniex.com/wallets/addresses?currency=BTC", "input": ["BTC", {"network": "BTC"}], "output": null}], "createDepositAddress": [{"description": "create ETH on ETH", "method": "createDepositAddress", "url": "https://api.poloniex.com/wallets/address", "input": ["ETH", {"network": "ETH"}], "output": "{\"currency\":\"ETH\"}"}, {"description": "create BTC", "method": "createDepositAddress", "url": "https://api.poloniex.com/wallets/address", "input": ["BTC", {"network": "BTC"}], "output": "{\"currency\":\"BTC\"}"}, {"description": "create ETH", "method": "createDepositAddress", "url": "https://api.poloniex.com/wallets/address", "input": ["USDT", {"network": "ETH"}], "output": "{\"currency\":\"USDTETH\"}"}, {"description": "create ERC20", "method": "createDepositAddress", "url": "https://api.poloniex.com/wallets/address", "input": ["USDT", {"network": "ERC20"}], "output": "{\"currency\":\"USDTETH\"}"}, {"description": "create TRC20", "method": "createDepositAddress", "url": "https://api.poloniex.com/wallets/address", "input": ["USDT", {"network": "TRC20"}], "output": "{\"currency\":\"USDTTRON\"}"}], "fetchCurrencies": [{"description": "fetchCurrencies", "method": "fetchCurrencies", "url": "https://api.poloniex.com/currencies?includeMultiChainCurrencies=true", "input": [], "output": null}], "fetchPositions": [{"description": "linear", "method": "fetchPositions", "url": "https://api.poloniex.com/v3/trade/position/opens", "input": [["BTC/USDT:USDT"]], "output": null}, {"description": "linear", "method": "fetchPositions", "url": "https://api.poloniex.com/v3/trade/position/opens", "input": [["BTC/USDT:USDT"]], "output": null}], "setPositionMode": [{"description": "linear", "method": "setPositionMode", "url": "https://api.poloniex.com/v3/position/mode", "input": [true, "BTC/USDT:USDT"], "output": "{\"posMode\":\"HEDGE\"}"}], "fetchPositionMode": [{"description": "linear", "method": "fetchPositionMode", "url": "https://api.poloniex.com/v3/position/mode", "input": ["BTC/USDT:USDT"], "output": null}], "fetchLeverage": [{"description": "linear", "method": "fetchLeverage", "url": "https://api.poloniex.com/v3/position/leverages?symbol=BTC_USDT_PERP&mgnMode=CROSS", "input": ["BTC/USDT:USDT", {"marginMode": "cross"}], "output": null}], "setLeverage": [{"description": "linear", "method": "setLeverage", "url": "https://api.poloniex.com/v3/position/leverage", "input": [20, "BTC/USDT:USDT", {"marginMode": "cross"}], "output": "{\"lever\":20,\"mgnMode\":\"CROSS\",\"symbol\":\"BTC_USDT_PERP\"}"}], "fetchOrderTrades": [{"description": "linear", "method": "fetchOrderTrades", "url": "https://api.poloniex.com/orders/420069629655085056/trades", "input": ["420069629655085056", "BTC/USDT:USDT"], "output": null}], "fetchMyTrades": [{"description": "linear", "method": "fetchMyTrades", "url": "https://api.poloniex.com/v3/trade/order/trades?sTime=1730000000000&limit=3&symbol=BTC_USDT_PERP", "input": ["BTC/USDT:USDT", 1730000000000, 3], "output": null}, {"description": "Spot private trades", "method": "fetchMyTrades", "url": "https://api.poloniex.com/trades?startTime=1699457638000&limit=5", "input": ["LTC/USDT", 1699457638000, 5]}], "fetchOpenOrders": [{"description": "swap open orders no symbol", "method": "fetchOpenOrders", "url": "https://api.poloniex.com/v3/trade/order/opens", "input": [null, null, null, {"type": "swap"}], "output": null}, {"description": "spot open orders no symbol", "method": "fetchOpenOrders", "url": "https://api.poloniex.com/orders", "input": [], "output": null}, {"description": "Spot open orders", "method": "fetchOpenOrders", "url": "https://api.poloniex.com/orders?symbol=LTC_USDT", "input": ["LTC/USDT"]}], "cancelOrder": [{"description": "linear", "method": "cancelOrder", "url": "https://api.poloniex.com/v3/trade/order", "input": ["420053842861056000", "BTC/USDT:USDT"], "output": "{\"symbol\":\"BTC_USDT_PERP\",\"ordId\":\"420053842861056000\"}"}, {"description": "cancel spot order", "method": "cancelOrder", "url": "https://api.poloniex.com/orders/2586600099********", "input": ["2586600099********", "LTC/USDT"]}], "cancelAllOrders": [{"description": "linear", "method": "cancelAllOrders", "url": "https://api.poloniex.com/v3/trade/allOrders", "input": ["BTC/USDT:USDT"], "output": "{\"symbols\":[\"BTC_USDT_PERP\"]}"}, {"description": "Cancel spot orders", "method": "cancelAllOrders", "url": "https://api.poloniex.com/orders", "input": ["LTC/USDT"], "output": "{\"symbols\":[\"LTC_USDT\"]}"}], "fetchBalance": [{"description": "linear", "method": "fetchBalance", "url": "https://api.poloniex.com/v3/account/balance", "input": [{"type": "swap"}], "output": null}, {"description": "Fetch spot Balance", "method": "fetchBalance", "url": "https://api.poloniex.com/accounts/balances?accountType=SPOT", "input": [{"type": "spot"}]}], "fetchDeposits": [{"description": "Fetch deposits", "method": "fetchDeposits", "url": "https://api.poloniex.com/wallets/activity?start=**********&end=**********", "input": []}], "fetchWithdrawals": [{"description": "Fetch withdrawals", "method": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://api.poloniex.com/wallets/activity?start=**********&end=**********", "input": []}], "createOrder": [{"description": "linear, limi buy", "method": "createOrder", "url": "https://api.poloniex.com/v3/trade/order", "input": ["BTC/USDT:USDT", "limit", "buy", 1, 84000], "output": "{\"symbol\":\"BTC_USDT_PERP\",\"side\":\"BUY\",\"type\":\"LIMIT\",\"sz\":\"1\",\"px\":\"84000\"}"}, {"description": "Spot limit buy", "method": "createOrder", "url": "https://api.poloniex.com/orders", "input": ["LTC/USDT", "limit", "buy", 0.1, 50], "output": "{\"symbol\":\"LTC_USDT\",\"side\":\"BUY\",\"type\":\"LIMIT\",\"quantity\":\"0.1\",\"price\":\"50\"}"}, {"description": "spot market sell", "method": "createOrder", "url": "https://api.poloniex.com/orders", "input": ["LTC/USDT", "market", "sell", 0.1], "output": "{\"symbol\":\"LTC_USDT\",\"side\":\"SELL\",\"type\":\"MARKET\",\"quantity\":\"0.1\"}"}, {"description": "market buy with createMarketBuyOrderRequiresPrice set to false", "method": "createOrder", "url": "https://api.poloniex.com/orders", "input": ["LTC/USDT", "market", "buy", 10, null, {"createMarketBuyOrderRequiresPrice": false}], "output": "{\"symbol\":\"LTC_USDT\",\"side\":\"BUY\",\"type\":\"MARKET\",\"amount\":\"10\"}"}, {"description": "Spot limit sell", "method": "createOrder", "url": "https://api.poloniex.com/orders", "input": ["LTC/USDT", "limit", "sell", 0.1, 100], "output": "{\"symbol\":\"LTC_USDT\",\"side\":\"SELL\",\"type\":\"LIMIT\",\"quantity\":\"0.1\",\"price\":\"100\"}"}], "createMarketBuyOrderWithCost": [{"description": "spot market buy with cost", "method": "createMarketBuyOrderWithCost", "url": "https://api.poloniex.com/orders", "input": ["LTC/USDT", 10], "output": "{\"symbol\":\"LTC_USDT\",\"side\":\"BUY\",\"type\":\"MARKET\",\"amount\":\"10\"}"}], "fetchTime": [{"description": "fetchTime", "method": "fetchTime", "url": "https://api.poloniex.com/timestamp", "input": []}], "fetchTrades": [{"description": "linear", "method": "fetchTrades", "url": "https://api.poloniex.com/v3/market/trades?symbol=BTC_USDT_PERP&limit=3", "input": ["BTC/USDT:USDT", 1730000000000, 3], "output": null}, {"description": "spot fetchTrades", "method": "fetchTrades", "url": "https://api.poloniex.com/markets/BTC_USDT/trades", "input": ["BTC/USDT"]}], "fetchOrderBook": [{"description": "linear", "method": "fetchOrderBook", "url": "https://api.poloniex.com/v3/market/orderBook?symbol=BTC_USDT_PERP", "input": ["BTC/USDT:USDT"], "output": null}, {"description": "spot orderbook", "method": "fetchOrderBook", "url": "https://api.poloniex.com/markets/BTC_USDT/orderBook", "input": ["BTC/USDT"]}], "fetchTicker": [{"description": "linear", "method": "fetchTicker", "url": "https://api.poloniex.com/v3/market/tickers?symbol=BTC_USDT_PERP", "input": ["BTC/USDT:USDT"], "output": null}, {"description": "spot ticker", "method": "fetchTicker", "url": "https://api.poloniex.com/markets/BTC_USDT/ticker24h", "input": ["BTC/USDT"]}], "fetchTickers": [{"description": "linear", "method": "fetchTickers", "url": "https://api.poloniex.com/markets/ticker24h", "input": [], "output": null}, {"description": "spot tickers", "method": "fetchTickers", "url": "https://api.poloniex.com/markets/ticker24h", "input": [["BTC/USDT", "ETH/USDT"]]}], "fetchOHLCV": [{"description": "linear", "method": "fetchOHLCV", "url": "https://api.poloniex.com/v3/market/candles?symbol=BTC_USDT_PERP&interval=MINUTE_1", "input": ["BTC/USDT:USDT", "1m"], "output": null}, {"description": "spot ohlcv", "method": "fetchOHLCV", "url": "https://api.poloniex.com/markets/BTC_USDT/candles?interval=MINUTE_1", "input": ["BTC/USDT"]}]}}