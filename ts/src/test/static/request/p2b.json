{"exchange": "p2b", "skipKeys": ["nonce", "startTime", "endTime"], "outputType": "json", "methods": {"createOrder": [{"description": "Spot limit buy", "method": "createOrder", "url": "https://api.p2pb2b.com/api/v2/order/new", "input": ["LTC/USDT", "limit", "buy", 0.1, 55], "output": "{\"market\":\"LTC_USDT\",\"side\":\"buy\",\"amount\":\"0.1\",\"price\":\"55\",\"request\":\"/api/v2/order/new\",\"nonce\":\"1700046291\"}"}, {"description": "Limit sell order", "method": "createOrder", "url": "https://api.p2pb2b.com/api/v2/order/new", "input": ["LTC/USDT", "limit", "sell", 0.1, 100], "output": "{\"market\":\"LTC_USDT\",\"side\":\"sell\",\"amount\":\"0.1\",\"price\":\"100\",\"request\":\"/api/v2/order/new\",\"nonce\":\"**********\"}"}], "cancelOrder": [{"description": "Cancel spot order", "method": "cancelOrder", "url": "https://api.p2pb2b.com/api/v2/order/cancel", "input": ["************", "LTC/USDT"], "output": "{\"market\":\"LTC_USDT\",\"orderId\":\"************\",\"request\":\"/api/v2/order/cancel\",\"nonce\":\"**********\"}"}], "fetchMyTrades": [{"description": "Fetch spot trades", "method": "fetchMyTrades", "url": "https://api.p2pb2b.com/api/v2/account/market_deal_history", "input": ["LTC/USDT"], "output": "{\"market\":\"LTC_USDT\",\"startTime\":**********,\"endTime\":**********,\"request\":\"/api/v2/account/market_deal_history\",\"nonce\":\"**********\"}"}], "fetchBalance": [{"description": "Fetch balance", "method": "fetchBalance", "url": "https://api.p2pb2b.com/api/v2/account/balances", "input": [], "output": "{\"request\":\"/api/v2/account/balances\",\"nonce\":\"**********\"}"}], "fetchOrderBook": [{"description": "spot orderbook", "method": "fetchOrderBook", "url": "https://api.p2pb2b.com/api/v2/public/depth/result?market=BTC_USDT", "input": ["BTC/USDT"]}], "fetchTicker": [{"description": "spot ticker", "method": "fetchTicker", "url": "https://api.p2pb2b.com/api/v2/public/ticker?market=BTC_USDT", "input": ["BTC/USDT"]}], "fetchTickers": [{"description": "spot tickers", "method": "fetchTickers", "url": "https://api.p2pb2b.com/api/v2/public/tickers", "input": [["BTC/USDT", "ETH/USDT"]]}], "fetchOHLCV": [{"description": "spot ohlcv", "method": "fetchOHLCV", "url": "https://api.p2pb2b.com/api/v2/public/market/kline?market=BTC_USDT&interval=1m", "input": ["BTC/USDT"]}]}}