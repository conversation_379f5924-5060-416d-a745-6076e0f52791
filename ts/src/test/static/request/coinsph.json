{"exchange": "coinsph", "skipKeys": ["timestamp", "signature"], "outputType": "json", "methods": {"fetchCurrencies": [{"description": "fetchCurrencies", "method": "fetchCurrencies", "url": "https://api.pro.coins.ph/openapi/wallet/v1/config/getall?timestamp=*************&&signature=bbc4f04b90bd804248a16aa6dee15398718438eb88b73212f9e6c0e48a81b3c5", "input": [], "output": null}], "fetchBalance": [{"description": "Fetch spot Balance", "method": "fetchBalance", "url": "https://api.pro.coins.ph/openapi/v1/account?type=spot&timestamp=*************&&signature=de696e042ad7db004df6c16c0cf65370ae966672edae2b409ea8bf282675bf26", "input": [{"type": "spot"}]}, {"description": "Fetch swap Balance", "method": "fetchBalance", "url": "https://api.pro.coins.ph/openapi/v1/account?type=swap&timestamp=*************&&signature=eacaf1d49e097dc24687fd971751e367722a6cd50044fc07ef433142aa7ce833", "input": [{"type": "swap"}]}], "fetchDeposits": [{"description": "Fetch deposits", "method": "fetchDeposits", "url": "https://api.pro.coins.ph/openapi/wallet/v1/deposit/history?timestamp=*************&&signature=5aa81438c1e8ac9dd35fc121e6973898ad047781330eb27524c24838cb5b6730", "input": []}], "fetchWithdrawals": [{"description": "Fetch withdrawals", "method": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://api.pro.coins.ph/openapi/wallet/v1/withdraw/history?timestamp=*************&&signature=992cefbe195e98dc0a0d18777d4cc764375f8e21800cfc2ab6ce9a1f3ad6218b", "input": []}], "createOrder": [{"description": "Spot market buy with createMarketBuyOrderRequiresPrice set to false", "method": "createOrder", "url": "https://api.pro.coins.ph/openapi/v1/order?symbol=BTCUSDT&type=MARKET&side=BUY&quoteOrderQty=2&newOrderRespType=FULL&timestamp=*************&&signature=d1031c5fb2f8afe32eff6af331904522bc703e6de4f785cf18b50603b906fd62", "input": ["BTC/USDT", "market", "buy", 2, null, {"createMarketBuyOrderRequiresPrice": false}]}, {"description": "Spot market buy with createMarketBuyOrderRequiresPrice set to true", "method": "createOrder", "url": "https://api.pro.coins.ph/openapi/v1/order?symbol=BTCUSDT&type=MARKET&side=BUY&quoteOrderQty=2.19&newOrderRespType=FULL&timestamp=1702189547198&&signature=3546694e8823c70c439a82945977e389089ad68015d19a824cabccce52684263", "input": ["BTC/USDT", "market", "buy", 5e-05, 43964.92, {"createMarketBuyOrderRequiresPrice": true}]}, {"description": "Spot market buy using the cost param", "method": "createOrder", "url": "https://api.pro.coins.ph/openapi/v1/order?symbol=BTCUSDT&type=MARKET&side=BUY&quoteOrderQty=2&newOrderRespType=FULL&timestamp=1702189330305&&signature=6ef187d65548da4edffea284e4a250702e5ae378ed0d376906a75ea15d7b1c22", "input": ["BTC/USDT", "market", "buy", 0, null, {"cost": 2}]}, {"description": "Spot limit buy", "method": "createOrder", "url": "https://api.pro.coins.ph/openapi/v1/order?symbol=BTCUSDT&type=LIMIT&side=BUY&price=40000&quantity=0.00005&timeInForce=GTC&newOrderRespType=FULL&timestamp=1702189604069&&signature=5d1c98c77e37c1279a2e1fe0d7dc650d05936056ba59415b6007bf4cacbbeba3", "input": ["BTC/USDT", "limit", "buy", 5e-05, 40000]}, {"description": "Spot limit sell", "method": "createOrder", "url": "https://api.pro.coins.ph/openapi/v1/order?symbol=BTCUSDT&type=LIMIT&side=SELL&price=50000&quantity=0.00005&timeInForce=GTC&newOrderRespType=FULL&timestamp=1702189639303&&signature=ed9e0f27e821dd3fd92cbee96a79411838cafe76f5590e4b0ce7081ddc83e8bc", "input": ["BTC/USDT", "limit", "sell", 5e-05, 50000]}, {"description": "Spot market sell", "method": "createOrder", "url": "https://api.pro.coins.ph/openapi/v1/order?symbol=BTCUSDT&type=MARKET&side=SELL&quantity=0.0001854&newOrderRespType=FULL&timestamp=1702189732038&&signature=b19f78019ac7d7c0fe197963d5d15b812f667bc7279c9d4bc60e32df07e856e0", "input": ["BTC/USDT", "market", "sell", 0.00018544, null]}, {"description": "test create order", "method": "createOrder", "url": "https://api.pro.coins.ph/openapi/v1/order/test?symbol=BTCUSDT&type=MARKET&side=SELL&quantity=0.0001854&newOrderRespType=FULL&timestamp=1702189732038&&signature=b19f78019ac7d7c0fe197963d5d15b812f667bc7279c9d4bc60e32df07e856e0", "input": ["BTC/USDT", "market", "sell", 0.00018544, null, {"test": true}]}], "createMarketBuyOrderWithCost": [{"description": "Spot market buy order with cost", "method": "createMarketBuyOrderWithCost", "url": "https://api.pro.coins.ph/openapi/v1/order?symbol=BTCUSDT&type=MARKET&side=BUY&quoteOrderQty=2&newOrderRespType=FULL&timestamp=1702189422251&&signature=5cd28d7b59d7fe0cf6f1bb747d312759cd5c77d7878cdb24aef1840fc1f4ca5c", "input": ["BTC/USDT", 2]}], "fetchTickers": [{"description": "Fetch tickers", "method": "fetchTickers", "url": "https://api.pro.coins.ph/openapi/quote/v1/ticker/24hr", "input": []}, {"description": "spot tickers", "method": "fetchTickers", "url": "https://api.pro.coins.ph/openapi/quote/v1/ticker/24hr?symbols=%5B\"BTCUSDT\",\"ETHUSDT\"%5D", "input": [["BTC/USDT", "ETH/USDT"]]}], "fetchTicker": [{"description": "Fetch ticker", "method": "fetchTicker", "url": "https://api.pro.coins.ph/openapi/quote/v1/ticker/24hr?symbol=BTCUSDT", "input": ["BTC/USDT"]}, {"description": "spot ticker", "method": "fetchTicker", "url": "https://api.pro.coins.ph/openapi/quote/v1/ticker/24hr?symbol=BTCUSDT&", "input": ["BTC/USDT"]}], "fetchTime": [{"description": "fetchTime", "method": "fetchTime", "url": "https://api.pro.coins.ph/openapi/v1/time", "input": []}], "fetchTrades": [{"description": "spot fetchTrades", "method": "fetchTrades", "url": "https://api.pro.coins.ph/openapi/quote/v1/trades?symbol=BTCUSDT&", "input": ["BTC/USDT"]}], "fetchOrderBook": [{"description": "spot orderbook", "method": "fetchOrderBook", "url": "https://api.pro.coins.ph/openapi/quote/v1/depth?symbol=BTCUSDT&", "input": ["BTC/USDT"]}], "fetchOHLCV": [{"description": "fetchOHLCV", "method": "fetchOHLCV", "url": "https://api.pro.coins.ph/openapi/quote/v1/klines?symbol=BTCUSDT&interval=1m&limit=1000&", "input": ["BTC/USDT"]}, {"description": "fetchOHLCV with limit", "method": "fetchOHLCV", "url": "https://api.pro.coins.ph/openapi/quote/v1/klines?symbol=BTCUSDT&interval=1h&limit=4&", "input": ["BTC/USDT", "1h", null, 4]}, {"description": "fetchOHLCV with until", "method": "fetchOHLCV", "url": "https://api.pro.coins.ph/openapi/quote/v1/klines?symbol=BTCUSDT&interval=1h&endTime=1735948800000&startTime=1732352400000&limit=1000&", "input": ["BTC/USDT", "1h", null, null, {"until": 1735948800000}]}, {"description": "fetchOHLCV with since, and limit", "method": "fetchOHLCV", "url": "https://api.pro.coins.ph/openapi/quote/v1/klines?symbol=BTCUSDT&interval=1h&startTime=1735862399999&endTime=1735873199999&limit=4&", "input": ["BTC/USDT", "1h", 1735862399999, 4]}, {"description": "fetchOHLCV with since, and until", "method": "fetchOHLCV", "url": "https://api.pro.coins.ph/openapi/quote/v1/klines?symbol=BTCUSDT&interval=1h&startTime=1735862400000&endTime=1735948800000&limit=1000&", "input": ["BTC/USDT", "1h", 1735862400000, null, {"until": 1735948800000}]}, {"description": "fetchOHLCV with limit and until", "method": "fetchOHLCV", "url": "https://api.pro.coins.ph/openapi/quote/v1/klines?symbol=BTCUSDT&interval=1h&endTime=1735948800000&startTime=1735938000000&limit=4&", "input": ["BTC/USDT", "1h", null, 4, {"until": 1735948800000}]}, {"description": "fetchOHLCV with since, limit and until", "method": "fetchOHLCV", "url": "https://api.pro.coins.ph/openapi/quote/v1/klines?symbol=BTCUSDT&interval=1h&startTime=1735862400000&endTime=1735948800000&limit=4&", "input": ["BTC/USDT", "1h", 1735862400000, 4, {"until": 1735948800000}]}]}}