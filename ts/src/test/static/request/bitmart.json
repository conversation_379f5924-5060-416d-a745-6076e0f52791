{"exchange": "bitmart", "skipKeys": ["start_time", "end_time"], "outputType": "json", "methods": {"fetchPositionMode": [{"description": "fetchPositionMode", "method": "fetchPositionMode", "url": "https://api-cloud-v2.bitmart.com/contract/private/get-position-mode", "input": [], "output": null}], "setPositionMode": [{"description": "set position mode hedged", "method": "setPositionMode", "url": "https://api-cloud-v2.bitmart.com/contract/private/set-position-mode", "input": [true], "output": "{\"position_mode\":\"hedge_mode\"}"}], "fetchTransactionFee": [{"description": "trx tx fee", "method": "fetchTransactionFee", "url": "https://api-cloud.bitmart.com/account/v1/withdraw/charge?currency=TRX", "input": ["TRX"], "output": null}, {"description": "usdt erc20 tx fee", "method": "fetchTransactionFee", "url": "https://api-cloud.bitmart.com/account/v1/withdraw/charge?currency=USDT-ERC20", "input": ["USDT", {"network": "ERC20"}], "output": null}, {"description": "eth tx fee", "method": "fetchTransactionFee", "url": "https://api-cloud.bitmart.com/account/v1/withdraw/charge?currency=USDT-TRC20", "input": ["USDT"], "output": null}, {"description": "eth tx fee", "method": "fetchTransactionFee", "url": "https://api-cloud.bitmart.com/account/v1/withdraw/charge?currency=ETH", "input": ["ETH"], "output": null}, {"description": "btc tx fee", "method": "fetchTransactionFee", "url": "https://api-cloud.bitmart.com/account/v1/withdraw/charge?currency=BTC", "input": ["BTC"], "output": null}], "fetchTicker": [{"description": "Fetch spot ticker", "method": "fetchTicker", "url": "https://api-cloud.bitmart.com/spot/quotation/v3/ticker?symbol=BTC_USDT", "input": ["BTC/USDT"]}, {"description": "swap ticker", "method": "fetchTicker", "url": "https://api-cloud-v2.bitmart.com/contract/public/details?symbol=BTCUSDT", "input": ["BTC/USDT:USDT"]}], "fetchTickers": [{"description": "Fetch spot tickers", "method": "fetchTickers", "url": "https://api-cloud.bitmart.com/spot/quotation/v3/tickers", "input": [["BTC/USDT"]]}, {"description": "swap tickers", "method": "fetchTickers", "url": "https://api-cloud.bitmart.com/spot/quotation/v3/tickers", "input": []}, {"description": "spot tickers", "method": "fetchTickers", "url": "https://api-cloud.bitmart.com/spot/quotation/v3/tickers", "input": [["BTC/USDT", "ETH/USDT"]]}], "createOrder": [{"description": "Spot limit buy", "method": "createOrder", "url": "https://api-cloud.bitmart.com/spot/v2/submit_order", "input": ["LTC/USDT", "limit", "buy", 0.1, 50], "output": "{\"symbol\":\"LTC_USDT\",\"side\":\"buy\",\"type\":\"limit\",\"size\":\"0.1\",\"price\":\"50\"}"}, {"description": "Spot market buy", "method": "createOrder", "url": "https://api-cloud.bitmart.com/spot/v2/submit_order", "input": ["LTC/USDT", "market", "buy", 1, 5], "output": "{\"symbol\":\"LTC_USDT\",\"side\":\"buy\",\"type\":\"market\",\"notional\":\"5\"}"}, {"description": "Spot market sell", "method": "createOrder", "url": "https://api-cloud.bitmart.com/spot/v2/submit_order", "input": ["LTC/USDT", "market", "sell", 0.1], "output": "{\"symbol\":\"LTC_USDT\",\"side\":\"sell\",\"type\":\"market\",\"size\":\"0.1\"}"}, {"description": "Spot limit buy with margin", "method": "createOrder", "url": "https://api-cloud.bitmart.com/spot/v1/margin/submit_order", "input": ["LTC/USDT", "limit", "buy", 0.1, 50, {"marginMode": "isolated"}], "output": "{\"symbol\":\"LTC_USDT\",\"side\":\"buy\",\"type\":\"limit\",\"size\":\"0.1\",\"price\":\"50\",\"marginMode\":\"isolated\"}"}, {"description": "spot order with clientOrderId", "method": "createOrder", "url": "https://api-cloud.bitmart.com/spot/v2/submit_order", "input": ["ADA/USDT", "market", "sell", 20, null, {"clientOrderId": "myorder"}], "output": "{\"symbol\":\"ADA_USDT\",\"side\":\"sell\",\"type\":\"market\",\"size\":\"20\",\"client_order_id\":\"myorder\"}"}, {"description": "Swap market buy", "method": "createOrder", "url": "https://api-cloud-v2.bitmart.com/contract/private/submit-order", "input": ["LTC/USDT:USDT", "market", "buy", 1], "output": "{\"symbol\":\"LTCUSDT\",\"type\":\"market\",\"size\":1,\"side\":1,\"open_type\":\"cross\"}"}, {"description": "Swap limit sell order", "method": "createOrder", "url": "https://api-cloud-v2.bitmart.com/contract/private/submit-order", "input": ["LTC/USDT:USDT", "limit", "sell", 1, 100], "output": "{\"symbol\":\"LTCUSDT\",\"type\":\"limit\",\"size\":1,\"price\":\"100\",\"side\":4,\"open_type\":\"cross\"}"}, {"description": "Swap market sell with reduceOnly (reducing long)", "method": "createOrder", "url": "https://api-cloud-v2.bitmart.com/contract/private/submit-order", "input": ["LTC/USDT:USDT", "market", "sell", 1, null, {"reduceOnly": true}], "output": "{\"symbol\":\"LTCUSDT\",\"type\":\"market\",\"size\":1,\"side\":3,\"open_type\":\"cross\"}"}, {"description": "Swap limit buy with postOnly and clientOrderId", "method": "createOrder", "url": "https://api-cloud-v2.bitmart.com/contract/private/submit-order", "input": ["LTC/USDT:USDT", "limit", "buy", 1, 55, {"postOnly": true, "clientOrderId": "myClientOrderId"}], "output": "{\"symbol\":\"LTCUSDT\",\"type\":\"limit\",\"size\":1,\"mode\":4,\"price\":\"55\",\"side\":1,\"open_type\":\"cross\",\"client_order_id\":\"myClientOrderId\"}"}, {"description": "Spot market buy order with createMarketBuyOrderRequiresPrice set to false", "method": "createOrder", "url": "https://api-cloud.bitmart.com/spot/v2/submit_order", "input": ["BTC/USDT", "market", "buy", 5, null, {"createMarketBuyOrderRequiresPrice": false}], "output": "{\"symbol\":\"BTC_USDT\",\"side\":\"buy\",\"type\":\"market\",\"notional\":\"5\"}"}, {"description": "Swap trailing order", "method": "createOrder", "url": "https://api-cloud-v2.bitmart.com/contract/private/submit-trail-order", "input": ["ETH/USDT:USDT", "market", "sell", 1, null, {"leverage": 2, "marginMode": "isolated", "trailingTriggerPrice": 2700, "trailingPercent": 3, "reduceOnly": true}], "output": "{\"symbol\":\"ETHUSDT\",\"size\":1,\"callback_rate\":\"3\",\"activation_price\":\"2700\",\"activation_price_type\":1,\"open_type\":\"isolated\",\"side\":3,\"leverage\":\"2\"}"}, {"description": "Swap stop limit buy order using the triggerPrice param (type 1)", "method": "createOrder", "url": "https://api-cloud-v2.bitmart.com/contract/private/submit-plan-order", "input": ["BTC/USDT:USDT", "limit", "buy", 1, 30000, {"triggerPrice": "31000"}], "output": "{\"symbol\":\"BTCUSDT\",\"type\":\"limit\",\"size\":1,\"price\":\"30000\",\"executive_price\":\"30000\",\"trigger_price\":\"31000\",\"price_type\":1,\"price_way\":1,\"side\":1,\"open_type\":\"cross\",\"leverage\":\"1\"}"}, {"description": "swap limit trigger order", "method": "createOrder", "url": "https://api-cloud-v2.bitmart.com/contract/private/submit-plan-order", "input": ["LTC/USDT:USDT", "limit", "buy", 1, 50, {"triggerPrice": 55}], "output": "{\"symbol\":\"LTCUSDT\",\"type\":\"limit\",\"size\":1,\"price\":\"50\",\"executive_price\":\"50\",\"trigger_price\":\"55\",\"price_type\":1,\"price_way\":1,\"side\":1,\"open_type\":\"cross\",\"leverage\":\"1\"}"}, {"description": "swap market trigger order", "method": "createOrder", "url": "https://api-cloud-v2.bitmart.com/contract/private/submit-plan-order", "input": ["LTC/USDT:USDT", "market", "buy", 1, null, {"triggerPrice": 55}], "output": "{\"symbol\":\"LTCUSDT\",\"type\":\"market\",\"size\":1,\"trigger_price\":\"55\",\"price_type\":1,\"price_way\":1,\"side\":1,\"open_type\":\"cross\",\"leverage\":\"1\"}"}, {"description": "swap stop loss order", "method": "createOrder", "url": "https://api-cloud-v2.bitmart.com/contract/private/submit-tp-sl-order", "input": ["BTC/USDT:USDT", "limit", "sell", 1, 50000, {"stopLossPrice": "51000"}], "output": "{\"symbol\":\"BTCUSDT\",\"type\":\"stop_loss\",\"size\":1,\"price_type\":1,\"executive_price\":\"50000\",\"trigger_price\":\"51000\",\"side\":3}"}, {"description": "swap take profit order", "method": "createOrder", "url": "https://api-cloud-v2.bitmart.com/contract/private/submit-tp-sl-order", "input": ["BTC/USDT:USDT", "limit", "sell", 1, 70000, {"takeProfitPrice": "71000"}], "output": "{\"symbol\":\"BTCUSDT\",\"type\":\"take_profit\",\"size\":1,\"price_type\":1,\"executive_price\":\"70000\",\"trigger_price\":\"71000\",\"side\":3}"}], "createOrders": [{"description": "spot orders", "method": "createOrders", "url": "https://api-cloud.bitmart.com/spot/v4/batch_orders", "input": [[{"symbol": "LTC/USDT", "amount": 0.1, "side": "buy", "type": "limit", "price": 60}, {"symbol": "LTC/USDT", "amount": 0.11, "side": "buy", "type": "limit", "price": 61}]], "output": "{\"symbol\":\"LTC_USDT\",\"orderParams\":[{\"side\":\"buy\",\"type\":\"limit\",\"size\":\"0.1\",\"price\":\"60\"},{\"side\":\"buy\",\"type\":\"limit\",\"size\":\"0.11\",\"price\":\"61\"}]}"}], "createMarketBuyOrderWithCost": [{"description": "Spot create market buy order with the quote amount as the cost", "method": "createMarketBuyOrderWithCost", "url": "https://api-cloud.bitmart.com/spot/v2/submit_order", "input": ["BTC/USDT", 5], "output": "{\"symbol\":\"BTC_USDT\",\"side\":\"buy\",\"type\":\"market\",\"notional\":\"5\"}"}], "fetchMyTrades": [{"description": "Spot private trades", "method": "fetchMyTrades", "url": "https://api-cloud.bitmart.com/spot/v4/query/trades", "input": ["LTC/USDT", 1699457638000, 5], "output": "{\"symbol\":\"LTC_USDT\",\"limit\":5,\"startTime\":1699457638000}"}, {"description": "Swap private trades", "method": "fetchMyTrades", "url": "https://api-cloud-v2.bitmart.com/contract/private/trades?symbol=LTCUSDT&start_time=1699457638000", "input": ["LTC/USDT:USDT", 1699457638000, 5]}], "fetchOpenOrders": [{"description": "Spot open orders", "method": "fetchOpenOrders", "url": "https://api-cloud.bitmart.com/spot/v4/query/open-orders", "input": ["LTC/USDT"], "output": "{\"symbol\":\"LTC_USDT\"}"}, {"description": "Swap open orders", "method": "fetchOpenOrders", "url": "https://api-cloud-v2.bitmart.com/contract/private/get-open-orders?symbol=LTCUSDT", "input": ["LTC/USDT:USDT"]}, {"description": "Swap open trailing orders", "method": "fetchOpenOrders", "url": "https://api-cloud-v2.bitmart.com/contract/private/get-open-orders?symbol=BTCUSDT&type=trailing", "input": ["BTC/USDT:USDT", null, null, {"orderType": "trailing"}]}, {"description": "open trigger orders", "method": "fetchOpenOrders", "url": "https://api-cloud-v2.bitmart.com/contract/private/current-plan-order?symbol=LTCUSDT", "input": ["LTC/USDT:USDT", null, null, {"trigger": true}]}], "fetchOrder": [{"description": "fetch swap order", "method": "fetchOrder", "url": "https://api-cloud-v2.bitmart.com/contract/private/order?symbol=LTCUSDT&order_id=3000013086011694", "input": ["3000013086011694", "LTC/USDT:USDT"]}, {"description": "Swap trailing order by id", "method": "fetchOrder", "url": "https://api-cloud-v2.bitmart.com/contract/private/order?type=trailing&symbol=BTCUSDT&order_id=2312139250106560", "input": ["2312139250106560", "BTC/USDT:USDT", {"orderType": "trailing"}]}], "fetchClosedOrders": [{"description": "Spot closed orders", "method": "fetchClosedOrders", "url": "https://api-cloud.bitmart.com/spot/v4/query/history-orders", "input": ["LTC/USDT"], "output": "{\"symbol\":\"LTC_USDT\"}"}, {"description": "Fetch swap closed orders", "method": "fetchClosedOrders", "url": "https://api-cloud-v2.bitmart.com/contract/private/order-history?symbol=LTCUSDT", "input": ["LTC/USDT:USDT", null, 1]}], "cancelOrder": [{"description": "Cancel swap order", "method": "cancelOrder", "url": "https://api-cloud-v2.bitmart.com/contract/private/cancel-order", "input": ["231116362740328", "LTC/USDT:USDT"], "output": "{\"symbol\":\"LTCUSDT\",\"order_id\":\"231116362740328\"}"}, {"description": "Cancel spot order", "method": "cancelOrder", "url": "https://api-cloud.bitmart.com/spot/v3/cancel_order", "input": ["200683008340521505", "LTC/USDT"], "output": "{\"symbol\":\"LTC_USDT\",\"order_id\":\"200683008340521505\"}"}, {"description": "Swap cancel plan order", "method": "cancelOrder", "url": "https://api-cloud-v2.bitmart.com/contract/private/cancel-plan-order", "input": ["2312213461000000", "BTC/USDT:USDT", {"stop": true}], "output": "{\"symbol\":\"BTCUSDT\",\"order_id\":\"2312213461000000\"}"}, {"description": "<PERSON><PERSON>p cancel trailing order", "method": "cancelOrder", "url": "https://api-cloud-v2.bitmart.com/contract/private/cancel-trail-order", "input": [3000080071007939, "ETH/USDT:USDT", {"trailing": true}], "output": "{\"symbol\":\"ETHUSDT\",\"order_id\":\"3000080071007939\",\"trailing\":true}"}], "cancelOrders": [{"description": "spot orders", "method": "cancelOrders", "url": "https://api-cloud.bitmart.com/spot/v4/cancel_orders", "input": [["552971433421492224", "552972168286863360"], "LTC/USDT"], "output": "{\"symbol\":\"LTC_USDT\",\"orderIds\":[\"552971433421492224\",\"552972168286863360\"]}"}], "cancelAllOrders": [{"description": "Cancel swap orders", "method": "cancelAllOrders", "url": "https://api-cloud-v2.bitmart.com/contract/private/cancel-orders", "input": ["LTC/USDT:USDT"], "output": "{\"symbol\":\"LTCUSDT\"}"}, {"description": "cancelAll spot orders", "method": "cancelAllOrders", "url": "https://api-cloud.bitmart.com/spot/v4/cancel_all", "input": [], "output": "{}"}], "fetchBalance": [{"description": "Fetch spot Balance", "method": "fetchBalance", "url": "https://api-cloud.bitmart.com/spot/v1/wallet", "input": [{"type": "spot"}]}, {"description": "Fetch swap Balance", "method": "fetchBalance", "url": "https://api-cloud-v2.bitmart.com/contract/private/assets-detail", "input": [{"type": "swap"}]}, {"description": "Fetch accoun Balance", "method": "fetchBalance", "url": "https://api-cloud-v2.bitmart.com/account/v1/wallet", "input": [{"type": "account"}]}, {"description": "Fetch margin Balance", "method": "fetchBalance", "url": "https://api-cloud.bitmart.com/spot/v1/margin/isolated/account", "input": [{"type": "margin"}]}], "fetchPositions": [{"description": "Fetch linear position", "method": "fetchPositions", "url": "https://api-cloud-v2.bitmart.com/contract/private/position-v2?symbol=LTCUSDT", "input": [["LTC/USDT:USDT"]]}], "fetchDeposits": [{"description": "Fetch deposits", "method": "fetchDeposits", "url": "https://api-cloud-v2.bitmart.com/account/v2/deposit-withdraw/history?operation_type=deposit&N=1000", "input": []}, {"description": "Fetch deposits for USDT", "method": "fetchDeposits", "url": "https://api-cloud.bitmart.com/account/v2/deposit-withdraw/history?operation_type=deposit&N=1000&currency=USDT", "input": ["USDT"]}, {"description": "Fetch deposits for USDT with since and until", "method": "fetchDeposits", "url": "https://api-cloud.bitmart.com/account/v2/deposit-withdraw/history?operation_type=deposit&N=1000&currency=USDT&startTime=*************&endTime=*************", "input": ["USDT", *************, null, {"until": *************}]}], "fetchWithdrawals": [{"description": "Fetch withdrawals", "method": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://api-cloud-v2.bitmart.com/account/v2/deposit-withdraw/history?operation_type=withdraw&N=1000", "input": []}], "transfer": [{"description": "transfer from spot to swap", "method": "transfer", "url": "https://api-cloud-v2.bitmart.com/account/v1/transfer-contract", "input": ["USDT", 1, "spot", "swap"], "output": "{\"amount\":\"1\",\"currency\":\"USDT\",\"type\":\"spot_to_contract\"}"}], "fetchTransfers": [{"description": "fetch USDT transfers", "method": "fetchTransfers", "url": "https://api-cloud-v2.bitmart.com/account/v1/transfer-contract-list", "input": ["USDT"], "output": "{\"page\":1,\"limit\":10,\"currency\":\"USDT\"}"}], "fetchDepositAddress": [{"description": "fetch TRUMP deposit address", "method": "fetchDepositAddress", "url": "https://api-cloud.bitmart.com/account/v1/deposit/address?currency=TRUMP-SOL", "input": ["TRUMP"]}, {"description": "fetch USDT deposit address", "method": "fetchDepositAddress", "url": "https://api-cloud.bitmart.com/account/v1/deposit/address?currency=USDT-TRC20", "input": ["USDT"]}, {"description": "fetch USDT deposit address with ERC20 network", "method": "fetchDepositAddress", "url": "https://api-cloud.bitmart.com/account/v1/deposit/address?currency=USDT-ERC20", "input": ["USDT", {"network": "ERC20"}]}], "fetchTime": [{"description": "fetchTime", "method": "fetchTime", "url": "https://api-cloud-v2.bitmart.com/system/time", "input": []}], "fetchTrades": [{"description": "spot fetchTrades with a symbol and limit argument", "method": "fetchTrades", "url": "https://api-cloud.bitmart.com/spot/quotation/v3/trades?symbol=BTC_USDT&limit=3", "input": ["BTC/USDT", null, 3]}], "fetchOrderBook": [{"description": "spot orderbook", "method": "fetchOrderBook", "url": "https://api-cloud.bitmart.com/spot/quotation/v3/books?symbol=BTC_USDT", "input": ["BTC/USDT"]}, {"description": "swap orderbook", "method": "fetchOrderBook", "url": "https://api-cloud-v2.bitmart.com/contract/public/depth?symbol=BTCUSDT", "input": ["BTC/USDT:USDT"]}], "fetchOHLCV": [{"description": "spot ohlcv", "method": "fetchOHLCV", "url": "https://api-cloud.bitmart.com/spot/quotation/v3/klines?symbol=BTC_USDT&step=1", "input": ["BTC/USDT"]}, {"description": "swap ohlcv", "method": "fetchOHLCV", "url": "https://api-cloud-v2.bitmart.com/contract/public/kline?symbol=BTCUSDT&step=1&start_time=1709920990&end_time=1709992990", "input": ["BTC/USDT:USDT"]}], "fetchMarkOHLCV": [{"description": "fetch mark ohlcv", "method": "fetchMarkOHLCV", "url": "https://api-cloud-v2.bitmart.com/contract/public/markprice-kline?symbol=ETHUSDT&step=1&start_time=1740288630&end_time=1740318630", "input": ["ETH/USDT:USDT"]}], "fetchFundingRate": [{"description": "fundingRate", "method": "fetchFundingRate", "url": "https://api-cloud-v2.bitmart.com/contract/public/funding-rate?symbol=BTCUSDT", "input": ["BTC/USDT:USDT"]}], "editOrder": [{"description": "edit swap limit order", "method": "editOrder", "url": "https://api-cloud-v2.bitmart.com/contract/private/modify-limit-order", "input": ["3000013590000612", "LTC/USDT:USDT", "limit", "buy", null, 56], "output": "{\"symbol\":\"LTCUSDT\",\"order_id\":3000013590000612,\"price\":\"56\"}"}, {"description": "edit a stopLossPrice order", "method": "editOrder", "url": "https://api-cloud-v2.bitmart.com/contract/private/modify-tp-sl-order", "input": ["3000023150003480", "BTC/USDT:USDT", "limit", "sell", 1, 50000, {"stopLossPrice": "52000"}], "output": "{\"symbol\":\"BTCUSDT\",\"order_id\":\"3000023150003480\",\"price_type\":1,\"executive_price\":\"50000\",\"category\":\"limit\",\"trigger_price\":\"52000\"}"}, {"description": "edit a stopLoss order", "method": "editOrder", "url": "https://api-cloud-v2.bitmart.com/contract/private/modify-preset-plan-order", "input": ["3000023150003496", "BTC/USDT:USDT", "limit", "sell", 1, 50000, {"stopLoss": {"triggerPrice": "52000"}}], "output": "{\"symbol\":\"BTCUSDT\",\"order_id\":\"3000023150003496\",\"preset_stop_loss_price_type\":1,\"preset_stop_loss_price\":\"52000\"}"}, {"description": "edit a triggerPrice order", "method": "editOrder", "url": "https://api-cloud-v2.bitmart.com/contract/private/modify-plan-order", "input": ["3000023150003503", "BTC/USDT:USDT", "limit", "buy", 1, 71000, {"triggerPrice": "70500"}], "output": "{\"symbol\":\"BTCUSDT\",\"order_id\":\"3000023150003503\",\"price_type\":1,\"executive_price\":\"71000\",\"type\":\"limit\",\"trigger_price\":\"70500\"}"}], "fetchBorrowInterest": [{"description": "isolated fetch the borrow interest", "method": "fetchBorrowInterest", "url": "https://api-cloud.bitmart.com/spot/v1/margin/isolated/borrow_record?symbol=BTC_USDT", "input": ["USDT", "BTC/USDT"]}], "fetchFundingRateHistory": [{"description": "fetch funding rate history with a symbol and limit argument", "method": "fetchFundingRateHistory", "url": "https://api-cloud-v2.bitmart.com/contract/public/funding-rate-history?symbol=BTCUSDT&limit=2", "input": ["BTC/USDT:USDT", null, 2]}], "fetchLedger": [{"description": "fetch ledger with code and limit arguments", "method": "fetchLedger", "url": "https://api-cloud-v2.bitmart.com/contract/private/transaction-history?flow_type=0&page_size=1", "input": ["USDT", null, 1]}], "fetchFundingHistory": [{"description": "fetch funding history with symbol and limit arguments", "method": "fetchFundingHistory", "url": "https://api-cloud-v2.bitmart.com/contract/private/transaction-history?flow_type=3&symbol=LTCUSDT&page_size=1", "input": ["LTC/USDT:USDT", null, 1]}]}}