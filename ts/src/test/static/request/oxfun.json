{"exchange": "oxfun", "skipKeys": ["timestamp"], "outputType": "json", "methods": {"fetchFundingRate": [{"description": "singular funding rate", "method": "fetchFundingRate", "url": "https://api.ox.fun/v3/funding/estimates?marketCode=BTC-USD-SWAP-LIN", "input": ["BTC/USD:OX"], "output": null}], "createOrder": [{"description": "spot limit buy", "method": "createOrder", "url": "https://api.ox.fun/v3/orders/place", "input": ["ETH/USDT", "limit", "buy", 0.01, 10], "output": "{\"responseType\":\"FULL\",\"timestamp\":1717083207271,\"orders\":[{\"source\":1000, \"marketCode\":\"ETH-USDT\",\"side\":\"BUY\",\"quantity\":0.01,\"orderType\":\"LIMIT\",\"price\":10}]}"}, {"description": "swap limit buy", "method": "createOrder", "url": "https://api.ox.fun/v3/orders/place", "input": ["ETH/USD:OX", "limit", "buy", 0.01, 10], "output": "{\"responseType\":\"FULL\",\"timestamp\":1717083306700,\"orders\":[{\"source\":1000,\"marketCode\":\"ETH-USD-SWAP-LIN\",\"side\":\"BUY\",\"quantity\":0.01,\"orderType\":\"LIMIT\",\"price\":10}]}"}], "fetchTrades": [{"description": "Spot fetch public trades", "method": "fetchTrades", "url": "https://api.ox.fun/v3/exchange-trades?marketCode=ETH-USDT", "input": ["ETH/USDT"]}, {"description": "Swap fetch public trades", "method": "fetchTrades", "url": "https://api.ox.fun/v3/exchange-trades?marketCode=ETH-USD-SWAP-LIN", "input": ["ETH/USD:OX"]}, {"description": "Fetch trades with a since timestamp argument", "method": "fetchTrades", "url": "https://api.ox.fun/v3/exchange-trades?marketCode=ETH-USD-SWAP-LIN&startTime=1715686562085&limit=2&endTime=1715687562085", "input": ["ETH/USD:OX", 1715686562085, 2, {"until": 1715687562085}]}], "fetchOrderBook": [{"description": "Spot fetch order book with a limit argument", "method": "fetchOrderBook", "url": "https://api.ox.fun/v3/depth?marketCode=ETH-USDT&level=3", "input": ["ETH/USDT", 3]}, {"description": "Swap fetch order book with a limit argument", "method": "fetchOrderBook", "url": "https://api.ox.fun/v3/depth?marketCode=ETH-USD-SWAP-LIN&level=3", "input": ["ETH/USD:OX", 3]}], "fetchOrder": [{"description": "Spot fetch an order", "method": "fetchOrder", "url": "https://api.ox.fun/v3/orders/status?orderId=1000117918630", "input": [1000117918630, "ETH/USDT"]}, {"description": "<PERSON>wap fetch an order", "method": "fetchOrder", "url": "https://api.ox.fun/v3/orders/status?orderId=1000117922976", "input": [1000117922976, "ETH/USD:OX"]}], "cancelAllOrders": [{"description": "Fill this with a description of the method call", "method": "cancelAllOrders", "url": "https://api.ox.fun/v3/orders/cancel-all", "input": [], "output": "{}"}], "fetchMyTrades": [{"description": "Swap fetch my trades", "method": "fetchMyTrades", "url": "https://api.ox.fun/v3/trades?marketCode=ETH-USD-SWAP-LIN&startTime=1716966000000&endTime=1717570800000", "input": ["ETH/USD:OX", 1716966000000]}, {"description": "Fetch my trades for swap with a limit and until arguments", "method": "fetchMyTrades", "url": "https://api.ox.fun/v3/trades?marketCode=ETH-USD-SWAP-LIN&startTime=1716966000000&limit=2&endTime=1717077000000", "input": ["ETH/USD:OX", 1716966000000, 2, {"until": 1717077000000}]}], "fetchTickers": [{"description": "Fetch all tickers", "method": "fetchTickers", "url": "https://api.ox.fun/v3/tickers", "input": []}], "fetchTicker": [{"description": "Spot fetch ticker", "method": "fetchTicker", "url": "https://api.ox.fun/v3/tickers?marketCode=ETH-USDT", "input": ["ETH/USDT"]}, {"description": "Swap fetch ticker", "method": "fetchTicker", "url": "https://api.ox.fun/v3/tickers?marketCode=ETH-USD-SWAP-LIN", "input": ["ETH/USD:OX"]}], "fetchOHLCV": [{"description": "Spot fetch candles with a since timestamp argument", "method": "fetchOHLCV", "url": "https://api.ox.fun/v3/candles?marketCode=ETH-USDT&timeframe=60s&startTime=1717085760000&endTime=1717690560000", "input": ["ETH/USDT", "1m", 1717085760000]}, {"description": "Swap fetch candles with a since timestamp argument", "method": "fetchOHLCV", "url": "https://api.ox.fun/v3/candles?marketCode=ETH-USD-SWAP-LIN&timeframe=60s&startTime=1717085880000&endTime=1717690680000", "input": ["ETH/USD:OX", "1m", 1717085880000]}, {"description": "Fetch candles with a limit and until arguments", "method": "fetchOHLCV", "url": "https://api.ox.fun/v3/candles?marketCode=ETH-USDT&timeframe=300s&startTime=1717085760000&limit=3&endTime=1717385870000", "input": ["ETH/USDT", "5m", 1717085760000, 3, {"until": 1717385870000}]}], "fetchCurrencies": [{"description": "fetchCurrencies", "method": "fetchCurrencies", "url": "https://api.ox.fun/v3/assets", "input": [], "output": null}, {"description": "Fetch all currencies", "method": "fetchCurrencies", "url": "https://api.ox.fun/v3/assets", "input": []}], "fetchFundingRates": [{"description": "Fetch all funding rates", "method": "fetchFundingRates", "url": "https://api.ox.fun/v3/funding/estimates", "input": []}], "fetchFundingRateHistory": [{"description": "Fetch funding rate history for a specific market", "method": "fetchFundingRateHistory", "url": "https://api.ox.fun/v3/funding/rates?marketCode=ETH-USD-SWAP-LIN&startTime=1717581601006", "input": ["ETH/USD:OX", 1717581601006]}, {"description": "Fetch funding rate history with a limit and until arguments", "method": "fetchFundingRateHistory", "url": "https://api.ox.fun/v3/funding/rates?marketCode=ETH-USD-SWAP-LIN&startTime=1717581601006&limit=3&endTime=1717681601006", "input": ["ETH/USD:OX", 1717581601006, 3, {"until": 1717681601006}]}], "fetchFundingHistory": [{"description": "Fetch funding history", "method": "fetchFundingHistory", "url": "https://api.ox.fun/v3/funding?marketCode=ETH-USD-SWAP-LIN&startTime=*************&limit=2&endTime=*************", "input": ["ETH/USD:OX", *************, 2, {"until": *************}]}], "fetchLeverageTiers": [{"description": "Fetch leverage tiers", "method": "fetchLeverageTiers", "url": "https://api.ox.fun/v3/leverage/tiers", "input": []}], "fetchBalance": [{"description": "Fetch balance", "method": "fetchBalance", "url": "https://api.ox.fun/v3/balances", "input": []}, {"description": "Fetch balance for a specific asset and sub account", "method": "fetchBalance", "url": "https://api.ox.fun/v3/balances?asset=OX&subAcc=Ray", "input": [{"asset": "OX", "subAcc": "<PERSON>"}]}], "fetchAccounts": [{"description": "Fetch accounts", "method": "fetchAccounts", "url": "https://api.ox.fun/v3/account/names", "input": []}], "transfer": [{"description": "Transfer from main account to sub account", "method": "transfer", "url": "https://api.ox.fun/v3/transfer", "input": ["OX", 10, "106464", "106570"], "output": "{\"asset\":\"OX\",\"quantity\":\"10\",\"fromAccount\":\"106464\",\"toAccount\":\"106570\"}"}], "fetchTransfers": [{"description": "Fetch transfers", "method": "fetchTransfers", "url": "https://api.ox.fun/v3/transfer", "input": []}, {"description": "Fetch transfers with a limit and until arguments", "method": "fetchTransfers", "url": "https://api.ox.fun/v3/transfer?asset=OX&startTime=*************&limit=1&endTime=*************", "input": ["OX", *************, 1, {"until": *************}]}], "fetchDepositAddress": [{"description": "Fetch deposit address for a specific network", "method": "fetchDepositAddress", "url": "https://api.ox.fun/v3/deposit-addresses?asset=ETH&network=Ethereum", "input": ["ETH", {"network": "ERC20"}]}], "fetchDeposits": [{"description": "Fetch deposits", "method": "fetchDeposits", "url": "https://api.ox.fun/v3/deposit?asset=USDC&startTime=*************&limit=1&endTime=*************", "input": ["USDC", *************, 1, {"until": *************}]}], "fetchWithdrawals": [{"description": "Fetch withdrawals", "method": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://api.ox.fun/v3/withdrawal?asset=OX&startTime=1715530365450&limit=2&endTime=1715530527000", "input": ["OX", 1715530365450, 2, {"until": 1715530527000}]}]}}