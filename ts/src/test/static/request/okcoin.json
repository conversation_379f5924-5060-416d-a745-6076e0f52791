{"exchange": "okcoin", "skipKeys": [], "outputType": "json", "methods": {"fetchBalance": [{"description": "Fetch spot Balance", "method": "fetchBalance", "url": "https://www.okcoin.com/api/v5/account/balance", "input": [{"type": "spot"}]}, {"description": "Fetch swap Balance", "method": "fetchBalance", "url": "https://www.okcoin.com/api/v5/account/balance", "input": [{"type": "swap"}]}], "fetchDeposits": [{"description": "Fetch deposits", "method": "fetchDeposits", "url": "https://www.okcoin.com/api/v5/asset/deposit-history", "input": []}], "fetchWithdrawals": [{"description": "Fetch withdrawals", "method": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://www.okcoin.com/api/v5/asset/withdrawal-history", "input": []}], "createOrder": [{"description": "Spot market buy order", "method": "createOrder", "url": "https://www.okcoin.com/api/v5/trade/batch-orders", "input": ["BTC/USD", "market", "buy", 1], "output": "[{\"instId\":\"BTC-USD\",\"side\":\"buy\",\"ordType\":\"market\",\"sz\":\"1\",\"tdMode\":\"cash\",\"tgtCcy\":\"base_ccy\"}]"}, {"description": "Spot market buy order with triggerPrice", "method": "createOrder", "url": "https://www.okcoin.com/api/v5/trade/order-algo", "input": ["BTC/USD", "market", "buy", 1, null, {"triggerPrice": 40000}], "output": "{\"instId\":\"BTC-USD\",\"side\":\"buy\",\"ordType\":\"trigger\",\"sz\":\"1\",\"tdMode\":\"cash\",\"tgtCcy\":\"base_ccy\",\"triggerPx\":\"40000\",\"orderPx\":\"-1\"}"}, {"description": "Spot market sell order", "method": "createOrder", "url": "https://www.okcoin.com/api/v5/trade/batch-orders", "input": ["USDT/USD", "market", "sell", 11, null], "output": "[{\"instId\":\"USDT-USD\",\"side\":\"sell\",\"ordType\":\"market\",\"tdMode\":\"cash\",\"tgtCcy\":\"base_ccy\",\"sz\":\"11\"}]"}, {"description": "Spot market buy order with createMarketBuyOrderRequiresPrice set to false and tgtCcy set to quote", "method": "createOrder", "url": "https://www.okcoin.com/api/v5/trade/batch-orders", "input": ["BTC/USD", "market", "buy", 5, null, {"createMarketBuyOrderRequiresPrice": false, "tgtCcy": "quote_ccy"}], "output": "[{\"instId\":\"BTC-USD\",\"side\":\"buy\",\"ordType\":\"market\",\"tdMode\":\"cash\",\"tgtCcy\":\"quote_ccy\",\"sz\":\"5\"}]"}, {"description": "Spot market buy order using the cost param and tgtCcy set to quote", "method": "createOrder", "url": "https://www.okcoin.com/api/v5/trade/batch-orders", "input": ["BTC/USD", "market", "buy", 0, null, {"cost": 5, "tgtCcy": "quote_ccy"}], "output": "[{\"instId\":\"BTC-USD\",\"side\":\"buy\",\"ordType\":\"market\",\"tdMode\":\"cash\",\"tgtCcy\":\"quote_ccy\",\"sz\":\"5\"}]"}], "createMarketBuyOrderWithCost": [{"description": "spot market buy", "method": "createMarketBuyOrderWithCost", "url": "https://www.okcoin.com/api/v5/trade/batch-orders", "input": ["USDT/USD", 3], "output": "[{\"instId\":\"USDT-USD\",\"side\":\"buy\",\"ordType\":\"market\",\"tdMode\":\"cash\",\"tgtCcy\":\"quote_ccy\",\"sz\":\"3\"}]"}], "fetchLedger": [{"description": "fetch Ledger", "method": "fetchLedger", "url": "https://www.okcoin.com/api/v5/account/bills?ccy=USDT", "input": ["USDT"]}], "fetchTime": [{"description": "fetchTime", "method": "fetchTime", "url": "https://www.okcoin.com/api/v5/public/time", "input": []}], "fetchOHLCV": [{"description": "without args", "method": "fetchOHLCV", "url": "https://www.okcoin.com/api/v5/market/candles?instId=BTC-USD&bar=1m", "input": ["BTC/USD"]}]}}