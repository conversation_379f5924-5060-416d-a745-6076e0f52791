{"exchange": "bitget", "skipKeys": ["endTime", "newClientOid", "startTime"], "outputType": "json", "methods": {"withdraw": [{"description": "with_precision", "method": "withdraw", "url": "https://api.bitget.com/api/v2/spot/wallet/withdrawal", "input": ["USDT", 11.123456789, "EBz7xtobD2Rm8aAXH2VHmE2rnePATevbHcWQ3RRo4nqA", null, {"network": "SOL"}], "output": "{\"coin\":\"USDT\",\"address\":\"EBz7xtobD2Rm8aAXH2VHmE2rnePATevbHcWQ3RRo4nqA\",\"chain\":\"SOL\",\"size\":\"11.123457\",\"transferType\":\"on_chain\"}"}], "fetchCurrencies": [{"description": "fetchCurrencies", "method": "fetchCurrencies", "url": "https://api.bitget.com/api/v2/spot/public/coins", "input": [], "output": null}], "createOrder": [{"description": "Spot limit buy", "method": "createOrder", "url": "https://api.bitget.com/api/v2/spot/trade/place-order", "input": ["BTC/USDT", "limit", "buy", 0.0002, "25000"], "output": "{\"symbol\":\"BTCUSDT\",\"orderType\":\"limit\",\"price\":\"25000\",\"force\":\"GTC\",\"side\":\"buy\",\"size\":\"0.0002\"}"}, {"description": "Swap cross limit buy", "method": "createOrder", "url": "https://api.bitget.com/api/v2/mix/order/place-order", "input": ["BTC/USDT:USDT", "limit", "buy", 0.001, "25000", {"marginMode": "cross"}], "output": "{\"symbol\":\"BTCUSDT\",\"orderType\":\"limit\",\"price\":\"25000\",\"force\":\"GTC\",\"marginCoin\":\"USDT\",\"size\":\"0.001\",\"productType\":\"USDT-FUTURES\",\"marginMode\":\"crossed\",\"side\":\"buy\"}"}, {"description": "Swap cross limit buy with trigger price", "method": "createOrder", "url": "https://api.bitget.com/api/v2/mix/order/place-plan-order", "input": ["BTC/USDT:USDT", "limit", "buy", 0.001, "25000", {"triggerPrice": 26000, "marginMode": "cross"}], "output": "{\"symbol\":\"BTCUSDT\",\"orderType\":\"limit\",\"price\":\"25000\",\"force\":\"GTC\",\"marginCoin\":\"USDT\",\"size\":\"0.001\",\"productType\":\"USDT-FUTURES\",\"triggerType\":\"mark_price\",\"marginMode\":\"crossed\",\"side\":\"buy\",\"planType\":\"normal_plan\",\"triggerPrice\":26000,\"executePrice\":\"25000\"}"}, {"description": "Spot market buy", "method": "createOrder", "url": "https://api.bitget.com/api/v2/spot/trade/place-order", "input": ["BTC/USDT", "market", "buy", 0.0003, "37811"], "output": "{\"symbol\":\"BTCUSDT\",\"orderType\":\"market\",\"force\":\"GTC\",\"side\":\"buy\",\"size\":\"11.34\"}"}, {"description": "Spot market sell", "method": "createOrder", "url": "https://api.bitget.com/api/v2/spot/trade/place-order", "input": ["BTC/USDT", "market", "sell", 0.0002], "output": "{\"symbol\":\"BTCUSDT\",\"orderType\":\"market\",\"force\":\"GTC\",\"side\":\"sell\",\"size\":\"0.0002\"}"}, {"description": "spot margin market buy with createMarketBuyOrderRequiresPrice = false", "method": "createOrder", "url": "https://api.bitget.com/api/v2/margin/crossed/place-order", "input": ["LTC/USDT", "market", "buy", 10, null, {"createMarketBuyOrderRequiresPrice": false, "marginMode": "cross"}], "output": "{\"symbol\":\"LTCUSDT\",\"orderType\":\"market\",\"force\":\"GTC\",\"side\":\"buy\",\"loanType\":\"normal\",\"quoteSize\":\"10\"}"}, {"description": "spot margin market buy", "method": "createOrder", "url": "https://api.bitget.com/api/v2/margin/crossed/place-order", "input": ["LTC/USDT", "market", "buy", 5.1, 1, {"marginMode": "cross"}], "output": "{\"symbol\":\"LTCUSDT\",\"orderType\":\"market\",\"force\":\"GTC\",\"side\":\"buy\",\"loanType\":\"normal\",\"quoteSize\":\"5.1\"}"}, {"description": "Spot market buy with trigger price", "method": "createOrder", "url": "https://api.bitget.com/api/v2/spot/trade/place-plan-order", "input": ["BTC/USDT", "market", "buy", 0.0003, "25000", {"triggerPrice": 26000}], "output": "{\"symbol\":\"BTCUSDT\",\"orderType\":\"market\",\"force\":\"GTC\",\"side\":\"buy\",\"size\":\"7.5\",\"planType\":\"total\",\"triggerType\":\"mark_price\",\"triggerPrice\":26000,\"executePrice\":\"25000\"}"}, {"description": "Spot limit buy with post only", "method": "createOrder", "url": "https://api.bitget.com/api/v2/spot/trade/place-order", "input": ["BTC/USDT", "limit", "buy", 0.0002, "25000", {"postOnly": true}], "output": "{\"symbol\":\"BTCUSDT\",\"orderType\":\"limit\",\"price\":\"25000\",\"force\":\"post_only\",\"side\":\"buy\",\"size\":\"0.0002\"}"}, {"description": "Swap limit sell order with postOnly and reduceOnly", "method": "createOrder", "url": "https://api.bitget.com/api/v2/mix/order/place-order", "input": ["BTC/USDT:USDT", "limit", "sell", 0.001, "38000", {"postOnly": true, "reduceOnly": true, "marginMode": "cross"}], "output": "{\"symbol\":\"BTCUSDT\",\"orderType\":\"limit\",\"price\":\"38000\",\"force\":\"post_only\",\"marginCoin\":\"USDT\",\"size\":\"0.001\",\"productType\":\"USDT-FUTURES\",\"marginMode\":\"crossed\",\"reduceOnly\":\"YES\",\"side\":\"sell\"}"}, {"description": "Cross margin limit buy order", "method": "createOrder", "url": "https://api.bitget.com/api/v2/margin/crossed/place-order", "input": ["BTC/USDT", "limit", "buy", 0.0002, "25000", {"marginMode": "cross"}], "output": "{\"symbol\":\"BTCUSDT\",\"orderType\":\"limit\",\"price\":\"25000\",\"force\":\"GTC\",\"side\":\"buy\",\"loanType\":\"normal\",\"baseSize\":\"0.0002\"}"}, {"description": "Spot create market buy order with createMarketBuyOrderRequiresPrice set to false", "method": "createOrder", "url": "https://api.bitget.com/api/v2/spot/trade/place-order", "input": ["BTC/USDT", "market", "buy", 8, null, {"createMarketBuyOrderRequiresPrice": false}], "output": "{\"symbol\":\"BTCUSDT\",\"orderType\":\"market\",\"force\":\"GTC\",\"side\":\"buy\",\"size\":\"8\"}"}, {"description": "Spot create market buy order using the cost param", "method": "createOrder", "url": "https://api.bitget.com/api/v2/spot/trade/place-order", "input": ["BTC/USDT", "market", "buy", 0, null, {"cost": 8}], "output": "{\"symbol\":\"BTCUSDT\",\"orderType\":\"market\",\"force\":\"GTC\",\"side\":\"buy\",\"size\":\"8\"}"}, {"description": "Swap trailing order using trailingPercent and setting reduceOnly to true", "method": "createOrder", "url": "https://api.bitget.com/api/v2/mix/order/place-plan-order", "input": ["BTC/USDT:USDT", "market", "sell", 0.002, null, {"trailingPercent": "10", "trailingTriggerPrice": "45000", "reduceOnly": true}], "output": "{\"symbol\":\"BTCUSDT\",\"orderType\":\"market\",\"force\":\"GTC\",\"marginCoin\":\"USDT\",\"size\":\"0.002\",\"productType\":\"USDT-FUTURES\",\"triggerType\":\"mark_price\",\"planType\":\"track_plan\",\"triggerPrice\":\"45000\",\"callbackRatio\":\"10\",\"marginMode\":\"crossed\",\"reduceOnly\":\"YES\",\"side\":\"sell\"}"}, {"description": "Swap oneWayMode market sell order with reduceOnly", "method": "createOrder", "url": "https://api.bitget.com/api/v2/mix/order/place-order", "input": ["BTC/USDT:USDT", "market", "sell", 0.001, null, {"marginMode": "isolated", "reduceOnly": true, "oneWayMode": true}], "output": "{\"symbol\":\"BTCUSDT\",\"orderType\":\"market\",\"force\":\"GTC\",\"marginCoin\":\"USDT\",\"size\":\"0.001\",\"productType\":\"USDT-FUTURES\",\"marginMode\":\"isolated\",\"reduceOnly\":\"YES\",\"side\":\"sell\"}"}, {"description": "Swap oneWayMode limit buy order", "method": "createOrder", "url": "https://api.bitget.com/api/v2/mix/order/place-order", "input": ["BTC/USDT:USDT", "limit", "buy", 0.001, 40000, {"marginMode": "isolated", "oneWayMode": true}], "output": "{\"symbol\":\"BTCUSDT\",\"orderType\":\"limit\",\"price\":\"40000\",\"force\":\"GTC\",\"marginCoin\":\"USDT\",\"size\":\"0.001\",\"productType\":\"USDT-FUTURES\",\"marginMode\":\"isolated\",\"side\":\"buy\"}"}, {"description": "swap market buy +hedged", "method": "createOrder", "url": "https://api.bitget.com/api/v2/mix/order/place-order", "input": ["BTC/USDT:USDT", "market", "buy", 0.001, null, {"hedged": true}], "output": "{\"symbol\":\"BTCUSDT\",\"orderType\":\"market\",\"force\":\"GTC\",\"marginCoin\":\"USDT\",\"size\":\"0.001\",\"productType\":\"USDT-FUTURES\",\"marginMode\":\"crossed\",\"tradeSide\":\"Open\",\"side\":\"buy\"}"}, {"description": "swap market sell +hedged", "method": "createOrder", "url": "https://api.bitget.com/api/v2/mix/order/place-order", "input": ["BTC/USDT:USDT", "market", "sell", 0.001, null, {"hedged": true}], "output": "{\"symbol\":\"BTCUSDT\",\"orderType\":\"market\",\"force\":\"GTC\",\"marginCoin\":\"USDT\",\"size\":\"0.001\",\"productType\":\"USDT-FUTURES\",\"marginMode\":\"crossed\",\"tradeSide\":\"Open\",\"side\":\"sell\"}"}, {"description": "swap market buy +hedged +reduceOnly", "method": "createOrder", "url": "https://api.bitget.com/api/v2/mix/order/place-order", "input": ["BTC/USDT:USDT", "market", "buy", 0.001, null, {"hedged": true, "reduceOnly": true}], "output": "{\"symbol\":\"BTCUSDT\",\"orderType\":\"market\",\"force\":\"GTC\",\"marginCoin\":\"USDT\",\"size\":\"0.001\",\"productType\":\"USDT-FUTURES\",\"marginMode\":\"crossed\",\"tradeSide\":\"Close\",\"side\":\"sell\"}"}, {"description": "swap market sell +hedged +reduceOnly", "method": "createOrder", "url": "https://api.bitget.com/api/v2/mix/order/place-order", "input": ["BTC/USDT:USDT", "market", "sell", 0.001, null, {"hedged": true, "reduceOnly": true}], "output": "{\"symbol\":\"BTCUSDT\",\"orderType\":\"market\",\"force\":\"GTC\",\"marginCoin\":\"USDT\",\"size\":\"0.001\",\"productType\":\"USDT-FUTURES\",\"marginMode\":\"crossed\",\"tradeSide\":\"Close\",\"side\":\"buy\"}"}], "createMarketBuyOrderWithCost": [{"description": "Spot create market buy order with cost", "method": "createMarketBuyOrderWithCost", "url": "https://api.bitget.com/api/v2/spot/trade/place-order", "input": ["BTC/USDT", 8], "output": "{\"symbol\":\"BTCUSDT\",\"orderType\":\"market\",\"force\":\"GTC\",\"side\":\"buy\",\"size\":\"8\"}"}], "createOrders": [{"description": "Spot create multiple limit orders at once", "method": "createOrders", "url": "https://api.bitget.com/api/v2/spot/trade/batch-orders", "input": [[{"symbol": "BTC/USDT", "type": "limit", "side": "buy", "amount": 0.0002, "price": 25000}, {"symbol": "BTC/USDT", "type": "limit", "side": "buy", "amount": 0.0002, "price": 27000}]], "output": "{\"symbol\":\"BTCUSDT\",\"orderList\":[{\"symbol\":\"BTCUSDT\",\"orderType\":\"limit\",\"price\":\"25000\",\"force\":\"GTC\",\"side\":\"buy\",\"size\":\"0.0002\"},{\"symbol\":\"BTCUSDT\",\"orderType\":\"limit\",\"price\":\"27000\",\"force\":\"GTC\",\"side\":\"buy\",\"size\":\"0.0002\"}]}"}, {"description": "Spot isolated margin create multiple orders at once", "method": "createOrders", "url": "https://api.bitget.com/api/v2/margin/isolated/batch-place-order", "input": [[{"symbol": "BTC/USDT", "type": "limit", "side": "buy", "amount": 0.0002, "price": 25000, "params": {"marginMode": "isolated"}}, {"symbol": "BTC/USDT", "type": "limit", "side": "buy", "amount": 0.0002, "price": 27000, "params": {"marginMode": "isolated"}}]], "output": "{\"symbol\":\"BTCUSDT\",\"orderList\":[{\"symbol\":\"BTCUSDT\",\"orderType\":\"limit\",\"price\":\"25000\",\"force\":\"GTC\",\"side\":\"buy\",\"loanType\":\"normal\",\"baseSize\":\"0.0002\"},{\"symbol\":\"BTCUSDT\",\"orderType\":\"limit\",\"price\":\"27000\",\"force\":\"GTC\",\"side\":\"buy\",\"loanType\":\"normal\",\"baseSize\":\"0.0002\"}]}"}, {"description": "Spot cross margin create multiple orders at once", "method": "createOrders", "url": "https://api.bitget.com/api/v2/margin/crossed/batch-place-order", "input": [[{"symbol": "BTC/USDT", "type": "limit", "side": "buy", "amount": 0.0002, "price": 25000, "params": {"marginMode": "cross"}}, {"symbol": "BTC/USDT", "type": "limit", "side": "buy", "amount": 0.0002, "price": 27000, "params": {"marginMode": "cross"}}]], "output": "{\"symbol\":\"BTCUSDT\",\"orderList\":[{\"symbol\":\"BTCUSDT\",\"orderType\":\"limit\",\"price\":\"25000\",\"force\":\"GTC\",\"side\":\"buy\",\"loanType\":\"normal\",\"baseSize\":\"0.0002\"},{\"symbol\":\"BTCUSDT\",\"orderType\":\"limit\",\"price\":\"27000\",\"force\":\"GTC\",\"side\":\"buy\",\"loanType\":\"normal\",\"baseSize\":\"0.0002\"}]}"}, {"description": "Swap create multiple limit orders at once", "method": "createOrders", "url": "https://api.bitget.com/api/v2/mix/order/batch-place-order", "input": [[{"symbol": "BTC/USDT:USDT", "type": "limit", "side": "buy", "amount": 0.001, "price": 25000, "params": {"marginMode": "cross"}}, {"symbol": "BTC/USDT:USDT", "type": "limit", "side": "buy", "amount": 0.001, "price": 27000, "params": {"marginMode": "cross"}}]], "output": "{\"symbol\":\"BTCUSDT\",\"orderList\":[{\"symbol\":\"BTCUSDT\",\"orderType\":\"limit\",\"price\":\"25000\",\"force\":\"GTC\",\"marginCoin\":\"USDT\",\"size\":\"0.001\",\"productType\":\"USDT-FUTURES\",\"marginMode\":\"crossed\",\"side\":\"buy\"},{\"symbol\":\"BTCUSDT\",\"orderType\":\"limit\",\"price\":\"27000\",\"force\":\"GTC\",\"marginCoin\":\"USDT\",\"size\":\"0.001\",\"productType\":\"USDT-FUTURES\",\"marginMode\":\"crossed\",\"side\":\"buy\"}],\"marginMode\":\"crossed\",\"marginCoin\":\"USDT\",\"productType\":\"USDT-FUTURES\"}"}], "editOrder": [{"description": "Spot edit a trigger order", "method": "editOrder", "url": "https://api.bitget.com/api/v2/spot/trade/modify-plan-order", "input": ["1113281809859813380", "BTC/USDT", "limit", "buy", 0.0002, "27000", {"triggerPrice": 28000}], "output": "{\"orderId\":\"1113281809859813380\",\"size\":\"0.0002\",\"orderType\":\"limit\",\"triggerPrice\":28000,\"executePrice\":\"27000\"}"}, {"description": "Swap edit order", "method": "editOrder", "url": "https://api.bitget.com/api/v2/mix/order/modify-order", "input": ["1113283350484385804", "BTC/USDT:USDT", "limit", "buy", 0.002, "27000"], "output": "{\"orderId\":\"1113283350484385804\",\"symbol\":\"BTCUSDT\",\"productType\":\"USDT-FUTURES\",\"newSize\":\"0.002\",\"newPrice\":\"27000\",\"newClientOid\":\"f0c222d0-65c0-4502-b859-39d78e41450e\"}"}, {"description": "Swap edit a trigger order", "method": "editOrder", "url": "https://api.bitget.com/api/v2/mix/order/modify-plan-order", "input": ["1113283776619290625", "BTC/USDT:USDT", "limit", "buy", 0.001, "25000", {"triggerPrice": 27000}], "output": "{\"orderId\":\"1113283776619290625\",\"symbol\":\"BTCUSDT\",\"productType\":\"USDT-FUTURES\",\"newSize\":\"0.001\",\"newPrice\":\"25000\",\"newTriggerPrice\":\"27000\",\"triggerPrice\":27000}"}, {"description": "Swap edit a stopLossPrice order", "method": "editOrder", "url": "https://api.bitget.com/api/v2/mix/order/modify-tpsl-order", "input": ["1113286462953558017", "BTC/USDT:USDT", "market", "buy", 0.001, "25000", {"stopLossPrice": 27000}], "output": "{\"orderId\":\"1113286462953558017\",\"symbol\":\"BTCUSDT\",\"productType\":\"USDT-FUTURES\",\"marginCoin\":\"USDT\",\"size\":\"0.001\",\"executePrice\":\"25000\",\"triggerPrice\":\"27000\"}"}, {"description": "<PERSON><PERSON><PERSON> edit a trailing order", "method": "editOrder", "url": "https://api.bitget.com/api/v2/mix/order/modify-plan-order", "input": ["1121635717619453955", "BTC/USDT:USDT", "market", "sell", 0.002, null, {"trailingPercent": "5", "trailingTriggerPrice": "46000"}], "output": "{\"orderId\":\"1121635717619453955\",\"symbol\":\"BTCUSDT\",\"productType\":\"USDT-FUTURES\",\"newSize\":\"0.002\",\"newTriggerPrice\":\"46000\",\"newCallbackRatio\":\"5\"}"}], "fetchMyTrades": [{"description": "Spot fetch private trades", "method": "fetchMyTrades", "url": "https://api.bitget.com/api/v2/spot/trade/fills?limit=3&symbol=BTCUSDT", "input": ["BTC/USDT", null, "3"]}, {"description": "Swap fetch private trades", "method": "fetchMyTrades", "url": "https://api.bitget.com/api/v2/mix/order/fills?limit=3&productType=USDT-FUTURES&startTime=1699457638000&symbol=BTCUSDT", "input": ["BTC/USDT:USDT", "1699457638000", "3"]}, {"description": "Cross margin fetch private trades", "method": "fetchMyTrades", "url": "https://api.bitget.com/api/v2/margin/crossed/fills?limit=3&startTime=1693121196677&symbol=BTCUSDT", "input": ["BTC/USDT", null, "3", {"marginMode": "cross"}]}, {"description": "Isolated margin fetch private trades", "method": "fetchMyTrades", "url": "https://api.bitget.com/api/v2/margin/isolated/fills?limit=3&startTime=1693121211352&symbol=BTCUSDT", "input": ["BTC/USDT", null, "3", {"marginMode": "isolated"}]}], "fetchOrder": [{"description": "Spot fetch order", "method": "fetchOrder", "url": "https://api.bitget.com/api/v2/spot/trade/orderInfo?orderId=1112193234476896261", "input": ["1112193234476896261", "BTC/USDT"]}, {"description": "Fetch swap order", "method": "fetchOrder", "url": "https://api.bitget.com/api/v2/mix/order/detail?orderId=1112202424673271809&productType=USDT-FUTURES&symbol=BTCUSDT", "input": ["1112202424673271809", "BTC/USDT:USDT"]}], "fetchOpenOrders": [{"description": "Spot open orders", "method": "fetchOpenOrders", "url": "https://api.bitget.com/api/v2/spot/trade/unfilled-orders?symbol=BTCUSDT", "input": ["BTC/USDT"]}, {"description": "Spot open orders without symbol", "method": "fetchOpenOrders", "url": "https://api.bitget.com/api/v2/spot/trade/unfilled-orders", "input": []}, {"description": "Swap open orders", "method": "fetchOpenOrders", "url": "https://api.bitget.com/api/v2/mix/order/orders-pending?productType=USDT-FUTURES&symbol=BTCUSDT", "input": ["BTC/USDT:USDT"]}, {"description": "Swap open orders without symbol", "method": "fetchOpenOrders", "url": "https://api.bitget.com/api/v2/mix/order/orders-pending?productType=USDT-FUTURES&startTime=0&limit=0", "input": [null, 0, 0, {"type": "swap"}]}, {"description": "Swap open trailing orders", "method": "fetchOpenOrders", "url": "https://api.bitget.com/api/v2/mix/order/orders-plan-pending?planType=track_plan&productType=USDT-FUTURES&symbol=BTCUSDT", "input": ["BTC/USDT:USDT", null, null, {"trailing": true}]}, {"description": "fetch open tp/sl orders", "method": "fetchOpenOrders", "url": "https://api.bitget.com/api/v2/mix/order/orders-plan-pending?planType=profit_loss&productType=USDT-FUTURES&symbol=ADAUSDT", "input": ["ADA/USDT:USDT", null, null, {"planType": "profit_loss"}]}], "fetchClosedOrders": [{"description": "spot closed orders without symbol", "method": "fetchClosedOrders", "url": "https://api.bitget.com/api/v2/spot/trade/history-orders", "input": []}, {"description": "swap closed orders without symbol", "method": "fetchClosedOrders", "url": "https://api.bitget.com/api/v2/mix/order/orders-history?productType=USDT-FUTURES", "input": [null, null, null, {"type": "swap"}]}, {"description": "Spot closed orders", "method": "fetchClosedOrders", "url": "https://api.bitget.com/api/v2/spot/trade/history-orders?symbol=BTCUSDT", "input": ["BTC/USDT"]}, {"description": "Swap closed orders", "method": "fetchClosedOrders", "url": "https://api.bitget.com/api/v2/mix/order/orders-history?productType=USDT-FUTURES&symbol=BTCUSDT", "input": ["BTC/USDT:USDT"]}, {"description": "Swap closed trailing orders", "method": "fetchClosedOrders", "url": "https://api.bitget.com/api/v2/mix/order/orders-plan-history?planType=track_plan&productType=USDT-FUTURES&symbol=BTCUSDT", "input": ["BTC/USDT:USDT", null, null, {"trailing": true}]}], "fetchCanceledOrders": [{"description": "spot canceled orders", "method": "fetchCanceledOrders", "url": "https://api.bitget.com/api/v2/spot/trade/history-orders", "input": []}, {"description": "Swap canceled orders without symbol", "method": "fetchCanceledOrders", "url": "https://api.bitget.com/api/v2/mix/order/orders-history?productType=USDT-FUTURES", "input": [null, null, null, {"type": "swap"}]}, {"description": "spot canceled orders with symbol", "method": "fetchCanceledOrders", "url": "https://api.bitget.com/api/v2/spot/trade/history-orders?symbol=LTCUSDT", "input": ["LTC/USDT"]}, {"description": "swap canceled orders with symbol", "method": "fetchCanceledOrders", "url": "https://api.bitget.com/api/v2/mix/order/orders-history?productType=USDT-FUTURES&symbol=LTCUSDT", "input": ["LTC/USDT:USDT"]}, {"description": "swap canceled trailing orders", "method": "fetchCanceledOrders", "url": "https://api.bitget.com/api/v2/mix/order/orders-plan-history?planType=track_plan&productType=USDT-FUTURES&symbol=BTCUSDT", "input": ["BTC/USDT:USDT", null, null, {"trailing": true}]}], "fetchBalance": [{"description": "Fetch spot balance", "method": "fetchBalance", "url": "https://api.bitget.com/api/v2/spot/account/assets", "input": [{"type": "spot"}]}, {"description": "Fetch linear swap balance", "method": "fetchBalance", "url": "https://api.bitget.com/api/v2/mix/account/accounts?productType=USDT-FUTURES", "input": [{"type": "swap", "productType": "USDT-FUTURES"}]}, {"description": "Fetch cross margin balance", "method": "fetchBalance", "url": "https://api.bitget.com/api/margin/v1/cross/account/assets", "input": [{"marginMode": "cross"}]}, {"description": "Fetch isolated margin balance", "method": "fetchBalance", "url": "https://api.bitget.com/api/margin/v1/isolated/account/assets", "input": [{"marginMode": "isolated"}]}], "fetchPosition": [{"description": "Fetch a linear position", "method": "fetchPosition", "url": "https://api.bitget.com/api/v2/mix/position/single-position?marginCoin=USDT&productType=USDT-FUTURES&symbol=BTCUSDT", "input": ["BTC/USDT:USDT"]}], "fetchPositions": [{"description": "Fetch linear positions", "method": "fetchPositions", "url": "https://api.bitget.com/api/v2/mix/position/all-position?marginCoin=USDT&productType=USDT-FUTURES", "input": [["BTC/USDT:USDT"]]}, {"description": "Fetch linear positions without symbol", "method": "fetchPositions", "url": "https://api.bitget.com/api/v2/mix/position/all-position?marginCoin=USDT&productType=USDT-FUTURES", "input": []}, {"description": "fetchPositions using the history endpoint", "method": "fetchPositions", "url": "https://api.bitget.com/api/v2/mix/position/history-position?productType=USDT-FUTURES&useHistoryEndpoint=true", "input": [null, {"useHistoryEndpoint": true}]}], "fetchPositionsHistory": [{"description": "Fetch Positions History", "method": "fetchPositionsHistory", "url": "https://api.bitget.com/api/v2/mix/position/history-position", "input": []}, {"description": "Fill this with a description of the method call", "method": "fetchPositionsHistory", "url": "https://api.bitget.com/api/v2/mix/position/history-position?endTime=*************&limit=1&startTime=*************&symbol=XRPUSDT", "input": [["XRP/USDT:USDT"], *************, 1, {"until": *************}]}], "setLeverage": [{"description": "Set linear leverage", "method": "setLeverage", "url": "https://api.bitget.com/api/v2/mix/account/set-leverage", "input": [15, "ADA/USDT:USDT"], "output": "{\"symbol\":\"ADAUSDT\",\"marginCoin\":\"USDT\",\"leverage\":\"15\",\"productType\":\"USDT-FUTURES\"}"}, {"description": "Set inverse isolated leverage", "method": "setLeverage", "url": "https://api.bitget.com/api/v2/mix/account/set-leverage", "input": ["20", "BTC/USD:BTC", {"holdSide": "long"}], "output": "{\"symbol\":\"BTCUSD\",\"marginCoin\":\"BTC\",\"leverage\":\"20\",\"productType\":\"COIN-FUTURES\",\"holdSide\":\"long\"}"}], "setPositionMode": [{"description": "Set linear position mode to dual side", "method": "setPositionMode", "url": "https://api.bitget.com/api/v2/mix/account/set-position-mode", "input": [true, "LTC/USDT:USDT"], "output": "{\"posMode\":\"hedge_mode\",\"productType\":\"USDT-FUTURES\"}"}], "setMarginMode": [{"description": "Set margin mode to isolated", "method": "setMarginMode", "url": "https://api.bitget.com/api/v2/mix/account/set-margin-mode", "input": ["isolated", "LTC/USDT:USDT"], "output": "{\"symbol\":\"LTCUSDT\",\"marginCoin\":\"USDT\",\"marginMode\":\"isolated\",\"productType\":\"USDT-FUTURES\"}"}], "cancelOrder": [{"description": "Spot cancel order", "method": "cancelOrder", "url": "https://api.bitget.com/api/v2/spot/trade/cancel-order", "input": ["1112183589322526722", "BTC/USDT"], "output": "{\"symbol\":\"BTCUSDT\",\"orderId\":\"1112183589322526722\"}"}, {"description": "Spot cancel trigger order", "method": "cancelOrder", "url": "https://api.bitget.com/api/v2/spot/trade/cancel-plan-order", "input": ["1112185242788634625", "BTC/USDT", {"stop": true}], "output": "{\"orderId\":\"1112185242788634625\"}"}, {"description": "Swap cancel order", "method": "cancelOrder", "url": "https://api.bitget.com/api/v2/mix/order/cancel-order", "input": ["1112196299005526018", "BTC/USDT:USDT"], "output": "{\"symbol\":\"BTCUSDT\",\"orderId\":\"1112196299005526018\",\"productType\":\"USDT-FUTURES\"}"}, {"description": "Swap cancel trigger order", "method": "cancelOrder", "url": "https://api.bitget.com/api/v2/mix/order/cancel-plan-order", "input": ["1112196961443323905", "BTC/USDT:USDT", {"stop": true}], "output": "{\"symbol\":\"BTCUSDT\",\"productType\":\"USDT-FUTURES\",\"orderIdList\":[{\"orderId\":\"1112196961443323905\"}]}"}, {"description": "Cross margin cancel order", "method": "cancelOrder", "url": "https://api.bitget.com/api/v2/margin/crossed/cancel-order", "input": ["1112204653770715137", "BTC/USDT", {"marginMode": "cross"}], "output": "{\"symbol\":\"BTCUSDT\",\"orderId\":\"1112204653770715137\"}"}, {"description": "<PERSON><PERSON>p cancel trailing order", "method": "cancelOrder", "url": "https://api.bitget.com/api/v2/mix/order/cancel-plan-order", "input": ["1121635717619453955", "BTC/USDT:USDT", {"trailing": true}], "output": "{\"symbol\":\"BTCUSDT\",\"orderId\":\"1121635717619453955\",\"productType\":\"USDT-FUTURES\",\"orderIdList\":[{\"orderId\":\"1121635717619453955\"}],\"planType\":\"track_plan\"}"}], "cancelOrders": [{"description": "Spot cancel multiple orders at once", "method": "cancelOrders", "url": "https://api.bitget.com/api/v2/spot/trade/batch-cancel-order", "input": [["1112194858800812032", "1112194858805006336"], "BTC/USDT"], "output": "{\"symbol\":\"BTCUSDT\",\"orderList\":[{\"orderId\":\"1112194858800812032\"},{\"orderId\":\"1112194858805006336\"}]}"}, {"description": "Swap cancel multiple orders at once", "method": "cancelOrders", "url": "https://api.bitget.com/api/v2/mix/order/batch-cancel-orders", "input": [["1112203337114746893", "1112203337152495632"], "BTC/USDT:USDT"], "output": "{\"symbol\":\"BTCUSDT\",\"orderIdList\":[{\"orderId\":\"1112203337114746893\"},{\"orderId\":\"1112203337152495632\"}],\"productType\":\"USDT-FUTURES\"}"}, {"description": "Cross margin cancel multiple orders at once", "method": "cancelOrders", "url": "https://api.bitget.com/api/v2/margin/crossed/batch-cancel-order", "input": [["1112205645295792129", "1112205645442592772"], "BTC/USDT", {"marginMode": "cross"}], "output": "{\"symbol\":\"BTCUSDT\",\"orderIdList\":[{\"orderId\":\"1112205645295792129\"},{\"orderId\":\"1112205645442592772\"}]}"}, {"description": "Isolated margin cancel multiple orders at once", "method": "cancelOrders", "url": "https://api.bitget.com/api/v2/margin/isolated/batch-cancel-order", "input": [["1112207614827704321", "1112207614982893571"], "BTC/USDT", {"marginMode": "isolated"}], "output": "{\"symbol\":\"BTCUSDT\",\"orderIdList\":[{\"orderId\":\"1112207614827704321\"},{\"orderId\":\"1112207614982893571\"}]}"}], "cancelAllOrders": [{"description": "Spot stop cancel all orders", "method": "cancelAllOrders", "url": "https://api.bitget.com/api/v2/spot/trade/batch-cancel-plan-order", "input": ["BTC/USDT", {"stop": true}], "output": "{\"symbolList\":[\"BTCUSDT\"]}"}, {"description": "Spot cancel all plan orders", "method": "cancelAllOrders", "url": "https://api.bitget.com/api/v2/spot/trade/cancel-symbol-order", "input": ["BTC/USDT"], "output": "{\"symbol\":\"BTCUSDT\"}"}, {"description": "Swap cancel all orders", "method": "cancelAllOrders", "url": "https://api.bitget.com/api/v2/mix/order/batch-cancel-orders", "input": ["BTC/USDT:USDT"], "output": "{\"symbol\":\"BTCUSDT\",\"productType\":\"USDT-FUTURES\"}"}, {"description": "Cross margin cancel all orders", "method": "cancelAllOrders", "url": "https://api.bitget.com/api/margin/v1/cross/order/batchCancelOrder", "input": ["BTC/USDT", {"marginMode": "cross"}], "output": "{\"symbol\":\"BTCUSDT\"}"}, {"description": "Isolated margin cancel all orders", "method": "cancelAllOrders", "url": "https://api.bitget.com/api/margin/v1/isolated/order/batchCancelOrder", "input": ["BTC/USDT", {"marginMode": "isolated"}], "output": "{\"symbol\":\"BTCUSDT\"}"}], "fetchCrossBorrowRate": [{"description": "Fetch the cross borrow rate", "method": "fetchCrossBorrowRate", "url": "https://api.bitget.com/api/v2/margin/crossed/interest-rate-and-limit?coin=USDT", "input": ["USDT"]}], "fetchIsolatedBorrowRate": [{"description": "Fetch the isolated borrow rate", "method": "fetchIsolatedBorrowRate", "url": "https://api.bitget.com/api/v2/margin/isolated/interest-rate-and-limit?symbol=BTCUSDT", "input": ["BTC/USDT"]}], "closePosition": [{"description": "Swap close linear position", "method": "closePosition", "url": "https://api.bitget.com/api/v2/mix/order/close-positions", "input": ["BTC/USDT:USDT", null], "output": "{\"symbol\":\"BTCUSDT\",\"productType\":\"USDT-FUTURES\"}"}], "closeAllPositions": [{"description": "<PERSON><PERSON><PERSON> close all USDT margined positions", "method": "closeAllPositions", "url": "https://api.bitget.com/api/v2/mix/order/close-positions", "input": [{"productType": "USDT-FUTURES"}], "output": "{\"productType\":\"USDT-FUTURES\"}"}], "fetchOHLCV": [{"description": "+1d +since +limit +until (abnormal, limit more than permitted)", "method": "fetchOHLCV", "url": "https://api.bitget.com/api/v2/spot/market/history-candles?symbol=BTCUSDT&granularity=1Dutc&startTime=1672720000000&endTime=1690000000000&limit=200", "input": ["BTC/USDT", "1d", 1420000000000, 3000, {"until": 1690000000000}], "output": null}, {"description": "+1d +since +limit +until (abnormal, limit more than permitted)", "method": "fetchOHLCV", "url": "https://api.bitget.com/api/v2/mix/market/history-candles?symbol=BTCUSDT&granularity=1Dutc&startTime=1682224000000&endTime=1690000000000&limit=90&productType=USDT-FUTURES", "input": ["BTC/USDT:USDT", "1d", 1420000000000, 3000, {"until": 1690000000000}], "output": null}, {"description": "+1d +since +limit +until (abnormal, limit more than permitted)", "method": "fetchOHLCV", "url": "https://api.bitget.com/api/v2/spot/market/history-candles?symbol=BTCUSDT&granularity=1Dutc&startTime=1403720000000&endTime=1421000000000&limit=200", "input": ["BTC/USDT", "1d", 1420000000000, 3000, {"until": 1421000000000}], "output": null}, {"description": "+1d +since +limit +until (abnormal, limit more than permitted)", "method": "fetchOHLCV", "url": "https://api.bitget.com/api/v2/mix/market/history-candles?symbol=BTCUSDT&granularity=1Dutc&startTime=1413224000000&endTime=1421000000000&limit=90&productType=USDT-FUTURES", "input": ["BTC/USDT:USDT", "1d", 1420000000000, 3000, {"until": 1421000000000}], "output": null}, {"description": "+1d +since +limit +until (normal, but limit less than until)", "method": "fetchOHLCV", "url": "https://api.bitget.com/api/v2/spot/market/history-candles?symbol=BTCUSDT&granularity=1Dutc&startTime=1420913600000&endTime=1421000000000&limit=1", "input": ["BTC/USDT", "1d", 1420000000000, 1, {"until": 1421000000000}], "output": null}, {"description": "+1d +since +limit +until (normal, but limit less than until)", "method": "fetchOHLCV", "url": "https://api.bitget.com/api/v2/mix/market/history-candles?symbol=BTCUSDT&granularity=1Dutc&startTime=1420913600000&endTime=1421000000000&limit=1&productType=USDT-FUTURES", "input": ["BTC/USDT:USDT", "1d", 1420000000000, 1, {"until": 1421000000000}], "output": null}, {"description": "+1d +since +until", "method": "fetchOHLCV", "url": "https://api.bitget.com/api/v2/spot/market/history-candles?symbol=BTCUSDT&granularity=1Dutc&startTime=1412360000000&endTime=1421000000000&limit=100", "input": ["BTC/USDT", "1d", 1420000000000, null, {"until": 1421000000000}], "output": null}, {"description": "+1d +since +until", "method": "fetchOHLCV", "url": "https://api.bitget.com/api/v2/mix/market/history-candles?symbol=BTCUSDT&granularity=1Dutc&startTime=1412360000000&endTime=1420136000000&limit=100&productType=USDT-FUTURES", "input": ["BTC/USDT:USDT", "1d", 1420000000000, null, {"until": 1421000000000}], "output": null}, {"description": "+1d +until", "method": "fetchOHLCV", "url": "https://api.bitget.com/api/v2/spot/market/history-candles?symbol=BTCUSDT&granularity=1Dutc&endTime=1421000000000&startTime=1412360000000&limit=100", "input": ["BTC/USDT", "1d", null, null, {"until": 1421000000000}], "output": null}, {"description": "+1d +until", "method": "fetchOHLCV", "url": "https://api.bitget.com/api/v2/mix/market/history-candles?symbol=BTCUSDT&granularity=1Dutc&endTime=1420136000000&startTime=1412360000000&limit=100&productType=USDT-FUTURES", "input": ["BTC/USDT:USDT", "1d", null, null, {"until": 1421000000000}], "output": null}, {"description": "+1d +since +limit", "method": "fetchOHLCV", "url": "https://api.bitget.com/api/v2/spot/market/history-candles?symbol=BTCUSDT&granularity=1Dutc&startTime=1428640000000&endTime=1445920000000&limit=200", "input": ["BTC/USDT", "1d", 1420000000000, 3000], "output": null}, {"description": "+1d +since +limit", "method": "fetchOHLCV", "url": "https://api.bitget.com/api/v2/mix/market/history-candles?symbol=BTCUSDT&granularity=1Dutc&startTime=1420000000000&endTime=1427776000000&limit=90&productType=USDT-FUTURES", "input": ["BTC/USDT:USDT", "1d", 1420000000000, 3000], "output": null}, {"description": "+1d +since", "method": "fetchOHLCV", "url": "https://api.bitget.com/api/v2/spot/market/history-candles?symbol=BTCUSDT&granularity=1Dutc&startTime=1420000000000&endTime=1428640000000&limit=100", "input": ["BTC/USDT", "1d", 1420000000000], "output": null}, {"description": "+1d +since", "method": "fetchOHLCV", "url": "https://api.bitget.com/api/v2/mix/market/history-candles?symbol=BTCUSDT&granularity=1Dutc&startTime=1420000000000&endTime=1427776000000&limit=100&productType=USDT-FUTURES", "input": ["BTC/USDT:USDT", "1d", 1420000000000], "output": null}, {"description": "+1m +since +limit +until (abnormal; limit & until more than permitted)", "method": "fetchOHLCV", "url": "https://api.bitget.com/api/v2/spot/market/history-candles?symbol=BTCUSDT&granularity=1min&startTime=1420988000000&endTime=1421000000000&limit=200", "input": ["BTC/USDT", "1m", 1420000000000, 3000, {"until": 1421000000000}], "output": null}, {"description": "+1m +since +limit +until (abnormal; limit & until more than permitted)", "method": "fetchOHLCV", "url": "https://api.bitget.com/api/v2/mix/market/history-candles?symbol=BTCUSDT&granularity=1m&startTime=1420988000000&endTime=1421000000000&limit=200&productType=USDT-FUTURES", "input": ["BTC/USDT:USDT", "1m", 1420000000000, 3000, {"until": 1421000000000}], "output": null}], "fetchCanceledAndClosedOrders": [{"description": "spot canceled and closed orders", "method": "fetchCanceledAndClosedOrders", "url": "https://api.bitget.com/api/v2/spot/trade/history-orders?symbol=LTCUSDT", "input": ["LTC/USDT"]}, {"description": "swap canceled and closed orders", "method": "fetchCanceledAndClosedOrders", "url": "https://api.bitget.com/api/v2/mix/order/orders-history?productType=USDT-FUTURES&symbol=LTCUSDT", "input": ["LTC/USDT:USDT"]}], "fetchOrderBook": [{"description": "spot orderbook", "method": "fetchOrderBook", "url": "https://api.bitget.com/api/v2/spot/market/orderbook?symbol=BTCUSDT&limit=5", "input": ["BTC/USDT", 5]}, {"description": "swap orderbook", "method": "fetchOrderBook", "url": "https://api.bitget.com/api/v2/mix/market/merge-depth?symbol=BTCUSDT&limit=5&productType=USDT-FUTURES", "input": ["BTC/USDT:USDT", 5]}, {"description": "spot orderbook", "method": "fetchOrderBook", "url": "https://api.bitget.com/api/v2/spot/market/orderbook?symbol=BTCUSDT", "input": ["BTC/USDT"]}, {"description": "swap orderbook", "method": "fetchOrderBook", "url": "https://api.bitget.com/api/v2/mix/market/merge-depth?symbol=BTCUSDT&productType=USDT-FUTURES", "input": ["BTC/USDT:USDT"]}], "fetchTrades": [{"description": "fetch spot trades", "method": "fetchTrades", "url": "https://api.bitget.com/api/v2/spot/market/fills-history?symbol=BTCUSDT&limit=5", "input": ["BTC/USDT", null, 5]}, {"description": "fetch swap trades", "method": "fetchTrades", "url": "https://api.bitget.com/api/v2/mix/market/fills-history?symbol=BTCUSDT&limit=5&productType=USDT-FUTURES", "input": ["BTC/USDT:USDT", null, 5]}, {"description": "spot fetchTrades", "method": "fetchTrades", "url": "https://api.bitget.com/api/v2/spot/market/fills-history?symbol=BTCUSDT", "input": ["BTC/USDT"]}, {"description": "swap fetchTrades", "method": "fetchTrades", "url": "https://api.bitget.com/api/v2/mix/market/fills-history?symbol=BTCUSDT&productType=USDT-FUTURES", "input": ["BTC/USDT:USDT"]}], "fetchTradingFee": [{"description": "swap trading fee", "method": "fetchTradingFee", "url": "https://api.bitget.com/api/v2/common/trade-rate?businessType=mix&symbol=BTCUSDT", "input": ["BTC/USDT:USDT"], "output": null}, {"description": "fetch trading fee spot", "method": "fetchTradingFee", "url": "https://api.bitget.com/api/v2/common/trade-rate?businessType=spot&symbol=BTCUSDT", "input": ["BTC/USDT"]}], "fetchTradingFees": [{"description": "spot trading fees", "method": "fetchTradingFees", "url": "https://api.bitget.com/api/v2/spot/public/symbols", "input": []}], "fetchLedger": [{"description": "fetch USDT ledger", "method": "fetchLedger", "url": "https://api.bitget.com/api/v2/spot/account/bills?coin=USDT", "input": ["USDT"]}], "fetchFundingHistory": [{"description": "fetch funding history", "method": "fetchFundingHistory", "url": "https://api.bitget.com/api/v2/mix/account/bill?businessType=contract_settle_fee&marginCoin=USDT&productType=USDT-FUTURES&symbol=BTCUSDT", "input": ["BTC/USDT:USDT"]}], "fetchOpenInterest": [{"description": "fetch BTC/USDT:USDT OI", "method": "fetchOpenInterest", "url": "https://api.bitget.com/api/v2/mix/market/open-interest?symbol=BTCUSDT&productType=USDT-FUTURES", "input": ["BTC/USDT:USDT"]}], "fetchDepositAddress": [{"description": "fetch deposit address $AI", "method": "fetchDepositAddress", "url": "https://api.bitget.com/api/v2/spot/wallet/deposit-address?coin=$AI", "input": ["$AI"]}, {"description": "fetch USDT deposit address", "method": "fetchDepositAddress", "url": "https://api.bitget.com/api/v2/spot/wallet/deposit-address?coin=USDT", "input": ["USDT"]}, {"description": "fetchDepositAddress with network TRC20", "method": "fetchDepositAddress", "url": "https://api.bitget.com/api/v2/spot/wallet/deposit-address?chain=TRC20&coin=USDT", "input": ["USDT", {"network": "TRC20"}]}], "fetchLeverage": [{"description": "Swap fetch set leverage", "method": "fetchLeverage", "url": "https://api.bitget.com/api/v2/mix/account/account?marginCoin=USDT&productType=USDT-FUTURES&symbol=BTCUSDT", "input": ["BTC/USDT:USDT"]}], "fetchMarginMode": [{"description": "Swap fetch the set margin mode for a trading pair", "method": "fetchMarginMode", "url": "https://api.bitget.com/api/v2/mix/account/account?marginCoin=USDT&productType=USDT-FUTURES&symbol=BTCUSDT", "input": ["BTC/USDT:USDT"]}], "fetchTime": [{"description": "fetchTime", "method": "fetchTime", "url": "https://api.bitget.com/api/v2/public/time", "input": []}], "fetchMarkPrice": [{"description": "fetchMarkPrice", "method": "fetchMarkPrice", "url": "https://api.bitget.com/api/v2/mix/market/symbol-price?symbol=BTCUSDT&productType=USDT-FUTURES", "input": ["BTC/USDT:USDT"]}], "fetchTicker": [{"description": "Swap ticker", "method": "fetchTicker", "url": "https://api.bitget.com/api/v2/mix/market/ticker?symbol=BTCUSDT&productType=USDT-FUTURES", "input": ["BTC/USDT:USDT"]}, {"description": "spot ticker", "method": "fetchTicker", "url": "https://api.bitget.com/api/v2/spot/market/tickers?symbol=BTCUSDT", "input": ["BTC/USDT"]}], "fetchTickers": [{"description": "spot tickers", "method": "fetchTickers", "url": "https://api.bitget.com/api/v2/spot/market/tickers", "input": [["BTC/USDT", "ETH/USDT"]]}, {"description": "swap tickers", "method": "fetchTickers", "url": "https://api.bitget.com/api/v2/mix/market/tickers?productType=USDT-FUTURES", "input": [["BTC/USDT:USDT", "ETH/USDT:USDT"]]}, {"description": "subType linear", "method": "fetchTickers", "url": "https://api.bitget.com/api/v2/mix/market/tickers?productType=USDT-FUTURES", "input": [null, {"subType": "linear"}]}, {"description": "subType inverse", "method": "fetchTickers", "url": "https://api.bitget.com/api/v2/mix/market/tickers?productType=COIN-FUTURES", "input": [null, {"subType": "inverse"}]}, {"description": "productType usdc", "method": "fetchTickers", "url": "https://api.bitget.com/api/v2/mix/market/tickers?productType=USDC-FUTURES", "input": [null, {"defaultType": "swap", "productType": "USDC-FUTURES"}]}], "fetchFundingRateHistory": [{"description": "fundingRateHistory", "method": "fetchFundingRateHistory", "url": "https://api.bitget.com/api/v2/mix/market/history-fund-rate?symbol=BTCUSDT&productType=USDT-FUTURES", "input": ["BTC/USDT:USDT"]}], "fetchFundingRate": [{"description": "fundingRate", "method": "fetchFundingRate", "url": "https://api.bitget.com/api/v2/mix/market/current-fund-rate?symbol=BTCUSDT&productType=USDT-FUTURES", "input": ["BTC/USDT:USDT"]}, {"description": "fetch the funding rate details with an alternative method", "method": "fetchFundingRate", "url": "https://api.bitget.com/api/v2/mix/market/funding-time?symbol=BTCUSDT&productType=USDT-FUTURES", "input": ["BTC/USDT:USDT", {"method": "publicMixGetV2MixMarketFundingTime"}]}], "fetchDeposits": [{"description": "fetch deposits without currency", "method": "fetchDeposits", "url": "https://api.bitget.com/api/v2/spot/wallet/deposit-records?endTime=1741255153908&startTime=1733479153908", "input": [], "output": null}, {"description": "fetch usdt deposits", "method": "fetchDeposits", "url": "https://api.bitget.com/api/v2/spot/wallet/deposit-records?coin=USDT&endTime=1712048378795&startTime=1704272378795", "input": ["USDT"]}], "fetchWithdrawals": [{"description": "fetchWitdrawals without currency", "method": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://api.bitget.com/api/v2/spot/wallet/withdrawal-records?endTime=1743676539420&startTime=1735900539420", "input": [], "output": null}, {"description": "fetch withdrawals", "method": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://api.bitget.com/api/v2/spot/wallet/withdrawal-records?coin=USDT&endTime=1712048411669&startTime=1704272411669", "input": ["USDT"]}], "fetchConvertQuote": [{"description": "Fetch a quote for a conversion trade", "method": "fetchConvertQuote", "url": "https://api.bitget.com/api/v2/convert/quoted-price?fromCoin=USDC&fromCoinSize=3&toCoin=USDT", "input": ["USDC", "USDT", 3]}], "fetchConvertCurrencies": [{"description": "Fetch currencies that can be converted", "method": "fetchConvertCurrencies", "url": "https://api.bitget.com/api/v2/convert/currencies", "input": []}], "createConvertTrade": [{"description": "Convert from one currency to another", "method": "createConvertTrade", "url": "https://api.bitget.com/api/v2/convert/trade", "input": ["1164014202462613512", "USDC", "USDT", 5, {"toAmount": "4.9915035", "price": "0.9983007"}], "output": "{\"traceId\":\"1164014202462613512\",\"fromCoin\":\"USDC\",\"toCoin\":\"USDT\",\"fromCoinSize\":\"5\",\"toCoinSize\":\"4.9915035\",\"cnvtPrice\":\"0.9983007\"}"}], "fetchConvertTradeHistory": [{"description": "Fetch the conversion trade history", "method": "fetchConvertTradeHistory", "url": "https://api.bitget.com/api/v2/convert/convert-record?endTime=*************&startTime=*************", "input": []}], "fetchFundingInterval": [{"description": "linear swap fetch funding interval", "method": "fetchFundingInterval", "url": "https://api.bitget.com/api/v2/mix/market/funding-time?symbol=BTCUSDT&productType=USDT-FUTURES", "input": ["BTC/USDT:USDT"]}], "fetchLongShortRatioHistory": [{"description": "swap fetch the long short ratio history", "method": "fetchLongShortRatioHistory", "url": "https://api.bitget.com/api/v2/mix/market/account-long-short?symbol=BTCUSDT", "input": ["BTC/USDT:USDT"]}, {"description": "spot margin fetch the long short ratio history", "method": "fetchLongShortRatioHistory", "url": "https://api.bitget.com/api/v2/margin/market/long-short-ratio?symbol=BTCUSDT", "input": ["BTC/USDT"]}], "fetchBorrowInterest": [{"description": "isolated fetch borrow interest", "method": "fetchBorrowInterest", "url": "https://api.bitget.com/api/v2/margin/isolated/interest-history?coin=USDT&startTime=*************&symbol=BTCUSDT", "input": ["USDT", "BTC/USDT", null, null, {"marginMode": "isolated"}]}, {"description": "cross fetch borrow interest", "method": "fetchBorrowInterest", "url": "https://api.bitget.com/api/v2/margin/crossed/interest-history?coin=USDT&startTime=*************", "input": ["USDT", null, null, null, {"marginMode": "cross"}]}], "fetchFundingRates": [{"description": "fetchFundingRates", "method": "fetchFundingRates", "url": "https://api.bitget.com/api/v2/mix/market/tickers?productType=USDT-FUTURES", "input": []}]}}