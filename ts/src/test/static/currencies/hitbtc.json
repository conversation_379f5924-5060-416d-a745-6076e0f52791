{"BTC": {"info": {"full_name": "Bitcoin", "crypto": true, "payin_enabled": true, "payout_enabled": true, "transfer_enabled": true, "transfer_to_wallet_enabled": true, "transfer_to_exchange_enabled": true, "sign": "฿", "account_top_order": "1", "crypto_payment_id_name": "", "crypto_explorer": "https://blockchair.com/bitcoin/transaction/{tx}?from=hitbtc", "precision_transfer": "0.********", "delisted": false, "networks": [{"code": "BTC", "network_name": "Bitcoin", "network": "BTC", "protocol": "BTC", "default": true, "is_ens_available": false, "payin_enabled": true, "payout_enabled": true, "precision_payout": "0.********", "payout_fee": "0.************", "payout_is_payment_id": false, "payin_payment_id": false, "payin_confirmations": "1", "address_regex": "^([13][a-km-zA-HJ-NP-Z1-9A-HJ-NP-Z]{25,34}|[bB][cC]1[a-zA-HJ-NP-Z0-9]{39,59})$", "low_processing_time": "52.684", "high_processing_time": "704.61", "avg_processing_time": "305.*************", "crypto_payment_id_name": "", "crypto_explorer": "", "is_multichain": true}]}, "id": "BTC", "numericId": null, "code": "BTC", "precision": 1e-08, "type": null, "name": "Bitcoin", "active": true, "deposit": true, "withdraw": true, "fee": 0.0013, "fees": {}, "networks": {"OMNI": {"info": {"code": "BTC", "network_name": "Bitcoin", "network": "BTC", "protocol": "BTC", "default": true, "is_ens_available": false, "payin_enabled": true, "payout_enabled": true, "precision_payout": "0.********", "payout_fee": "0.************", "payout_is_payment_id": false, "payin_payment_id": false, "payin_confirmations": "1", "address_regex": "^([13][a-km-zA-HJ-NP-Z1-9A-HJ-NP-Z]{25,34}|[bB][cC]1[a-zA-HJ-NP-Z0-9]{39,59})$", "low_processing_time": "52.684", "high_processing_time": "704.61", "avg_processing_time": "305.*************", "crypto_payment_id_name": "", "crypto_explorer": "", "is_multichain": true}, "id": "BTC", "network": "OMNI", "active": true, "fee": 0.0013, "deposit": true, "withdraw": true, "precision": 1e-08, "limits": {"withdraw": {"min": null, "max": null}}}}, "limits": {"amount": {"min": null, "max": null}, "deposit": {"min": null, "max": null}, "withdraw": {"min": null, "max": null}}}, "USDT": {"info": {"full_name": "<PERSON><PERSON>", "crypto": true, "payin_enabled": true, "payout_enabled": true, "transfer_enabled": true, "transfer_to_wallet_enabled": true, "transfer_to_exchange_enabled": true, "sign": "₮", "account_top_order": "3", "crypto_payment_id_name": "", "crypto_explorer": "http://omniexplorer.info/lookuptx.aspx?txid={tx}", "precision_transfer": "0.000001", "delisted": false, "networks": [{"code": "AVAC", "network_name": "Avalanche C-Chain", "network": "AVAC", "protocol": "ERC-20", "default": false, "is_ens_available": false, "payin_enabled": true, "payout_enabled": true, "precision_payout": "0.0000000000********", "payout_fee": "10.************", "payout_is_payment_id": false, "payin_payment_id": false, "payin_confirmations": "5", "contract_address": "******************************************", "low_processing_time": "6.252", "high_processing_time": "26.35", "avg_processing_time": "13.**************", "crypto_payment_id_name": "", "crypto_explorer": "", "is_multichain": true, "asset_id": {"contract_address": "******************************************"}}, {"code": "BSC", "network_name": "Binance Smart Chain", "network": "BSC", "protocol": "BEP-20", "default": false, "is_ens_available": true, "payin_enabled": true, "payout_enabled": true, "precision_payout": "0.0000000000********", "payout_fee": "10.************", "payout_is_payment_id": false, "payin_payment_id": false, "payin_confirmations": "10", "contract_address": "******************************************", "low_processing_time": "2.847", "high_processing_time": "1029.318", "avg_processing_time": "29.264989795918368", "crypto_payment_id_name": "", "crypto_explorer": "https://bscscan.com/tx/{tx}", "is_multichain": true, "asset_id": {"contract_address": "******************************************"}}, {"code": "BTC", "network_name": "Bitcoin", "network": "BTC", "protocol": "OMNI", "default": false, "is_ens_available": false, "payin_enabled": false, "payout_enabled": false, "precision_payout": "0.********", "payout_fee": "50.************", "payout_is_payment_id": false, "payin_payment_id": false, "payin_confirmations": "2", "crypto_payment_id_name": "", "crypto_explorer": "", "is_multichain": true}, {"code": "ETH", "network_name": "Ethereum", "network": "ETH", "protocol": "ERC-20", "default": true, "is_ens_available": true, "payin_enabled": true, "payout_enabled": true, "precision_payout": "0.000001", "payout_fee": "19.************", "payout_is_payment_id": false, "payin_payment_id": false, "payin_confirmations": "2", "contract_address": "******************************************", "low_processing_time": "19.963", "high_processing_time": "7832.583", "avg_processing_time": "591.6112631578948", "crypto_payment_id_name": "", "crypto_explorer": "https://etherscan.io/tx/{tx}", "is_multichain": true, "asset_id": {"contract_address": "******************************************"}}, {"code": "NEAR", "network_name": "Near", "network": "NEAR", "protocol": "NEP141", "default": false, "is_ens_available": false, "payin_enabled": true, "payout_enabled": true, "precision_payout": "0.000001", "payout_fee": "10.************", "payout_is_payment_id": false, "payin_payment_id": false, "payin_confirmations": "60", "contract_address": "usdt.tether-token.near", "crypto_payment_id_name": "", "crypto_explorer": "", "is_multichain": true, "asset_id": {"contract_address": "usdt.tether-token.near"}}, {"code": "SOL", "network_name": "Solana", "network": "SOL", "protocol": "SPL", "default": false, "is_ens_available": true, "payin_enabled": true, "payout_enabled": true, "precision_payout": "0.000001", "payout_fee": "10.************", "payout_is_payment_id": false, "payin_payment_id": false, "payin_confirmations": "5", "low_processing_time": "1.491", "high_processing_time": "6076.44", "avg_processing_time": "276.17768749999993", "crypto_payment_id_name": "", "crypto_explorer": "https://solscan.io/tx/{tx}", "is_multichain": true, "asset_id": {"id": "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB"}}, {"code": "TON", "network_name": "<PERSON><PERSON>in", "network": "TON", "protocol": "JETTON", "default": false, "is_ens_available": false, "payin_enabled": true, "payout_enabled": true, "precision_payout": "0.000001", "payout_fee": "10.************", "payout_is_payment_id": true, "payin_payment_id": false, "payin_confirmations": "2", "contract_address": "EQCxE6mUtQJKFnGfaROTKOt1lZbDiiX1kCixRv7Nw2Id_sDs", "address_regex": "^(?:(?:EQ|UQ|kQ)[A-Za-z0-9_-]{46}|0:[0-9a-fA-F]{64})$", "low_processing_time": "24.741", "high_processing_time": "24.741", "avg_processing_time": "24.74099999999999", "crypto_payment_id_name": "", "crypto_explorer": "", "is_multichain": true, "asset_id": {"contract_address": "EQCxE6mUtQJKFnGfaROTKOt1lZbDiiX1kCixRv7Nw2Id_sDs"}}, {"code": "TRX", "network_name": "Tron", "network": "TRX", "protocol": "TRC-20", "default": false, "is_ens_available": true, "payin_enabled": true, "payout_enabled": true, "precision_payout": "0.000001", "payout_fee": "10.************", "payout_is_payment_id": false, "payin_payment_id": false, "payin_confirmations": "3", "low_processing_time": "0.859", "high_processing_time": "1622.715", "avg_processing_time": "42.53982653061226", "crypto_payment_id_name": "", "crypto_explorer": "https://tronscan.org/#/transaction/{tx}", "is_multichain": true, "asset_id": {"id": "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t"}}, {"code": "XTZ", "network_name": "Tezos", "network": "XTZ", "protocol": "FA2", "default": false, "is_ens_available": false, "payin_enabled": true, "payout_enabled": true, "precision_payout": "0.000001", "payout_fee": "10.************", "payout_is_payment_id": false, "payin_payment_id": false, "payin_confirmations": "30", "contract_address": "KT1XnTn74bUtxHfDtBmm2bGZAQfhPbvKWR8o", "crypto_payment_id_name": "", "crypto_explorer": "", "is_multichain": true, "asset_id": {"contract_address": "KT1XnTn74bUtxHfDtBmm2bGZAQfhPbvKWR8o", "id": "0"}}]}, "id": "USDT", "numericId": null, "code": "USDT", "precision": 1e-06, "type": null, "name": "<PERSON><PERSON>", "active": true, "deposit": true, "withdraw": true, "fee": 10, "fees": {}, "networks": {"ERC-20": {"info": {"code": "ETH", "network_name": "Ethereum", "network": "ETH", "protocol": "ERC-20", "default": true, "is_ens_available": true, "payin_enabled": true, "payout_enabled": true, "precision_payout": "0.000001", "payout_fee": "19.************", "payout_is_payment_id": false, "payin_payment_id": false, "payin_confirmations": "2", "contract_address": "******************************************", "low_processing_time": "19.963", "high_processing_time": "7832.583", "avg_processing_time": "591.6112631578948", "crypto_payment_id_name": "", "crypto_explorer": "https://etherscan.io/tx/{tx}", "is_multichain": true, "asset_id": {"contract_address": "******************************************"}}, "id": "ERC-20", "network": "ERC-20", "active": true, "fee": 19, "deposit": true, "withdraw": true, "precision": 1e-06, "limits": {"withdraw": {"min": null, "max": null}}}, "BEP-20": {"info": {"code": "BSC", "network_name": "Binance Smart Chain", "network": "BSC", "protocol": "BEP-20", "default": false, "is_ens_available": true, "payin_enabled": true, "payout_enabled": true, "precision_payout": "0.0000000000********", "payout_fee": "10.************", "payout_is_payment_id": false, "payin_payment_id": false, "payin_confirmations": "10", "contract_address": "******************************************", "low_processing_time": "2.847", "high_processing_time": "1029.318", "avg_processing_time": "29.264989795918368", "crypto_payment_id_name": "", "crypto_explorer": "https://bscscan.com/tx/{tx}", "is_multichain": true, "asset_id": {"contract_address": "******************************************"}}, "id": "BEP-20", "network": "BEP-20", "active": true, "fee": 10, "deposit": true, "withdraw": true, "precision": 1e-18, "limits": {"withdraw": {"min": null, "max": null}}}, "OMNI": {"info": {"code": "BTC", "network_name": "Bitcoin", "network": "BTC", "protocol": "OMNI", "default": false, "is_ens_available": false, "payin_enabled": false, "payout_enabled": false, "precision_payout": "0.********", "payout_fee": "50.************", "payout_is_payment_id": false, "payin_payment_id": false, "payin_confirmations": "2", "crypto_payment_id_name": "", "crypto_explorer": "", "is_multichain": true}, "id": "OMNI", "network": "OMNI", "active": false, "fee": 50, "deposit": false, "withdraw": false, "precision": 1e-08, "limits": {"withdraw": {"min": null, "max": null}}}, "NEP141": {"info": {"code": "NEAR", "network_name": "Near", "network": "NEAR", "protocol": "NEP141", "default": false, "is_ens_available": false, "payin_enabled": true, "payout_enabled": true, "precision_payout": "0.000001", "payout_fee": "10.************", "payout_is_payment_id": false, "payin_payment_id": false, "payin_confirmations": "60", "contract_address": "usdt.tether-token.near", "crypto_payment_id_name": "", "crypto_explorer": "", "is_multichain": true, "asset_id": {"contract_address": "usdt.tether-token.near"}}, "id": "NEP141", "network": "NEP141", "active": true, "fee": 10, "deposit": true, "withdraw": true, "precision": 1e-06, "limits": {"withdraw": {"min": null, "max": null}}}, "SPL": {"info": {"code": "SOL", "network_name": "Solana", "network": "SOL", "protocol": "SPL", "default": false, "is_ens_available": true, "payin_enabled": true, "payout_enabled": true, "precision_payout": "0.000001", "payout_fee": "10.************", "payout_is_payment_id": false, "payin_payment_id": false, "payin_confirmations": "5", "low_processing_time": "1.491", "high_processing_time": "6076.44", "avg_processing_time": "276.17768749999993", "crypto_payment_id_name": "", "crypto_explorer": "https://solscan.io/tx/{tx}", "is_multichain": true, "asset_id": {"id": "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB"}}, "id": "SPL", "network": "SPL", "active": true, "fee": 10, "deposit": true, "withdraw": true, "precision": 1e-06, "limits": {"withdraw": {"min": null, "max": null}}}, "JETTON": {"info": {"code": "TON", "network_name": "<PERSON><PERSON>in", "network": "TON", "protocol": "JETTON", "default": false, "is_ens_available": false, "payin_enabled": true, "payout_enabled": true, "precision_payout": "0.000001", "payout_fee": "10.************", "payout_is_payment_id": true, "payin_payment_id": false, "payin_confirmations": "2", "contract_address": "EQCxE6mUtQJKFnGfaROTKOt1lZbDiiX1kCixRv7Nw2Id_sDs", "address_regex": "^(?:(?:EQ|UQ|kQ)[A-Za-z0-9_-]{46}|0:[0-9a-fA-F]{64})$", "low_processing_time": "24.741", "high_processing_time": "24.741", "avg_processing_time": "24.74099999999999", "crypto_payment_id_name": "", "crypto_explorer": "", "is_multichain": true, "asset_id": {"contract_address": "EQCxE6mUtQJKFnGfaROTKOt1lZbDiiX1kCixRv7Nw2Id_sDs"}}, "id": "JETTON", "network": "JETTON", "active": true, "fee": 10, "deposit": true, "withdraw": true, "precision": 1e-06, "limits": {"withdraw": {"min": null, "max": null}}}, "TRC-20": {"info": {"code": "TRX", "network_name": "Tron", "network": "TRX", "protocol": "TRC-20", "default": false, "is_ens_available": true, "payin_enabled": true, "payout_enabled": true, "precision_payout": "0.000001", "payout_fee": "10.************", "payout_is_payment_id": false, "payin_payment_id": false, "payin_confirmations": "3", "low_processing_time": "0.859", "high_processing_time": "1622.715", "avg_processing_time": "42.53982653061226", "crypto_payment_id_name": "", "crypto_explorer": "https://tronscan.org/#/transaction/{tx}", "is_multichain": true, "asset_id": {"id": "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t"}}, "id": "TRC-20", "network": "TRC-20", "active": true, "fee": 10, "deposit": true, "withdraw": true, "precision": 1e-06, "limits": {"withdraw": {"min": null, "max": null}}}, "FA2": {"info": {"code": "XTZ", "network_name": "Tezos", "network": "XTZ", "protocol": "FA2", "default": false, "is_ens_available": false, "payin_enabled": true, "payout_enabled": true, "precision_payout": "0.000001", "payout_fee": "10.************", "payout_is_payment_id": false, "payin_payment_id": false, "payin_confirmations": "30", "contract_address": "KT1XnTn74bUtxHfDtBmm2bGZAQfhPbvKWR8o", "crypto_payment_id_name": "", "crypto_explorer": "", "is_multichain": true, "asset_id": {"contract_address": "KT1XnTn74bUtxHfDtBmm2bGZAQfhPbvKWR8o", "id": "0"}}, "id": "FA2", "network": "FA2", "active": true, "fee": 10, "deposit": true, "withdraw": true, "precision": 1e-06, "limits": {"withdraw": {"min": null, "max": null}}}}, "limits": {"amount": {"min": null, "max": null}, "deposit": {"min": null, "max": null}, "withdraw": {"min": null, "max": null}}}, "LTC": {"info": {"full_name": "Litecoin", "crypto": true, "payin_enabled": true, "payout_enabled": true, "transfer_enabled": true, "transfer_to_wallet_enabled": true, "transfer_to_exchange_enabled": true, "sign": "Ł", "account_top_order": "16", "crypto_payment_id_name": "", "crypto_explorer": " http://live.blockcypher.com/ltc/tx/{tx}", "precision_transfer": "0.********", "delisted": false, "networks": [{"code": "LTC", "network_name": "Litecoin", "network": "LTC", "protocol": "LTC", "default": true, "is_ens_available": true, "payin_enabled": true, "payout_enabled": true, "precision_payout": "0.********", "payout_fee": "0.************", "payout_is_payment_id": false, "payin_payment_id": false, "payin_confirmations": "5", "low_processing_time": "14.468", "high_processing_time": "869.384", "avg_processing_time": "189.**************", "crypto_payment_id_name": "", "crypto_explorer": "", "is_multichain": true}]}, "id": "LTC", "numericId": null, "code": "LTC", "precision": 1e-08, "type": null, "name": "Litecoin", "active": true, "deposit": true, "withdraw": true, "fee": 0.106, "fees": {}, "networks": {"LTC": {"info": {"code": "LTC", "network_name": "Litecoin", "network": "LTC", "protocol": "LTC", "default": true, "is_ens_available": true, "payin_enabled": true, "payout_enabled": true, "precision_payout": "0.********", "payout_fee": "0.************", "payout_is_payment_id": false, "payin_payment_id": false, "payin_confirmations": "5", "low_processing_time": "14.468", "high_processing_time": "869.384", "avg_processing_time": "189.**************", "crypto_payment_id_name": "", "crypto_explorer": "", "is_multichain": true}, "id": "LTC", "network": "LTC", "active": true, "fee": 0.106, "deposit": true, "withdraw": true, "precision": 1e-08, "limits": {"withdraw": {"min": null, "max": null}}}}, "limits": {"amount": {"min": null, "max": null}, "deposit": {"min": null, "max": null}, "withdraw": {"min": null, "max": null}}}, "ETH": {"info": {"full_name": "Ethereum", "crypto": true, "payin_enabled": true, "payout_enabled": true, "transfer_enabled": true, "transfer_to_wallet_enabled": true, "transfer_to_exchange_enabled": true, "sign": "E", "account_top_order": "2", "crypto_payment_id_name": "", "crypto_explorer": "https://www.etherchain.org/tx/{tx}", "precision_transfer": "0.0000********", "delisted": false, "networks": [{"code": "ETH", "network_name": "Ethereum", "network": "ETH", "protocol": "ETH", "default": true, "is_ens_available": true, "payin_enabled": true, "payout_enabled": true, "precision_payout": "0.0000000000********", "payout_fee": "0.************", "payout_is_payment_id": false, "payin_payment_id": false, "payin_confirmations": "2", "low_processing_time": "7.425", "high_processing_time": "64.919", "avg_processing_time": "30.**************", "crypto_payment_id_name": "", "crypto_explorer": "https://etherscan.io/tx/{tx}", "is_multichain": true}]}, "id": "ETH", "numericId": null, "code": "ETH", "precision": 1e-12, "type": null, "name": "Ethereum", "active": true, "deposit": true, "withdraw": true, "fee": 0.0045, "fees": {}, "networks": {"ERC20": {"info": {"code": "ETH", "network_name": "Ethereum", "network": "ETH", "protocol": "ETH", "default": true, "is_ens_available": true, "payin_enabled": true, "payout_enabled": true, "precision_payout": "0.0000000000********", "payout_fee": "0.************", "payout_is_payment_id": false, "payin_payment_id": false, "payin_confirmations": "2", "low_processing_time": "7.425", "high_processing_time": "64.919", "avg_processing_time": "30.**************", "crypto_payment_id_name": "", "crypto_explorer": "https://etherscan.io/tx/{tx}", "is_multichain": true}, "id": "ETH", "network": "ERC20", "active": true, "fee": 0.0045, "deposit": true, "withdraw": true, "precision": 1e-18, "limits": {"withdraw": {"min": null, "max": null}}}}, "limits": {"amount": {"min": null, "max": null}, "deposit": {"min": null, "max": null}, "withdraw": {"min": null, "max": null}}}, "ADA": {"info": {"full_name": "Cardano", "crypto": true, "payin_enabled": true, "payout_enabled": true, "transfer_enabled": true, "transfer_to_wallet_enabled": true, "transfer_to_exchange_enabled": true, "sign": "A", "crypto_payment_id_name": "", "crypto_explorer": "https://cardanoexplorer.com/tx/{tx}", "precision_transfer": "0.000001", "delisted": false, "networks": [{"code": "ADA", "network_name": "Cardano", "network": "ADA", "protocol": "ADA", "default": true, "is_ens_available": true, "payin_enabled": true, "payout_enabled": true, "precision_payout": "0.000001", "payout_fee": "13.400000000000", "payout_is_payment_id": false, "payin_payment_id": false, "payin_confirmations": "30", "low_processing_time": "24.932", "high_processing_time": "1662.666", "avg_processing_time": "332.560696969697", "crypto_payment_id_name": "", "crypto_explorer": "", "is_multichain": true}]}, "id": "ADA", "numericId": null, "code": "ADA", "precision": 1e-06, "type": null, "name": "Cardano", "active": true, "deposit": true, "withdraw": true, "fee": 13.4, "fees": {}, "networks": {"ADA": {"info": {"code": "ADA", "network_name": "Cardano", "network": "ADA", "protocol": "ADA", "default": true, "is_ens_available": true, "payin_enabled": true, "payout_enabled": true, "precision_payout": "0.000001", "payout_fee": "13.400000000000", "payout_is_payment_id": false, "payin_payment_id": false, "payin_confirmations": "30", "low_processing_time": "24.932", "high_processing_time": "1662.666", "avg_processing_time": "332.560696969697", "crypto_payment_id_name": "", "crypto_explorer": "", "is_multichain": true}, "id": "ADA", "network": "ADA", "active": true, "fee": 13.4, "deposit": true, "withdraw": true, "precision": 1e-06, "limits": {"withdraw": {"min": null, "max": null}}}}, "limits": {"amount": {"min": null, "max": null}, "deposit": {"min": null, "max": null}, "withdraw": {"min": null, "max": null}}}, "XRP": {"info": {"full_name": "<PERSON><PERSON><PERSON>", "crypto": true, "payin_enabled": true, "payout_enabled": true, "transfer_enabled": true, "transfer_to_wallet_enabled": true, "transfer_to_exchange_enabled": true, "sign": "R", "account_top_order": "15", "crypto_payment_id_name": "DestinationTag", "crypto_explorer": "https://xrpcharts.ripple.com/#/transactions/{tx}", "precision_transfer": "0.000001", "delisted": false, "networks": [{"code": "XRP", "network_name": "<PERSON><PERSON><PERSON>", "network": "XRP", "protocol": "XRP", "default": true, "is_ens_available": false, "payin_enabled": true, "payout_enabled": true, "precision_payout": "0.000001", "payout_fee": "4.************", "payout_is_payment_id": true, "payin_payment_id": true, "payin_confirmations": "1", "low_processing_time": "2.278", "high_processing_time": "62.292", "avg_processing_time": "26.***************", "crypto_payment_id_name": "", "crypto_explorer": "", "is_multichain": true}]}, "id": "XRP", "numericId": null, "code": "XRP", "precision": 1e-06, "type": null, "name": "<PERSON><PERSON><PERSON>", "active": true, "deposit": true, "withdraw": true, "fee": 4.24, "fees": {}, "networks": {"XRP": {"info": {"code": "XRP", "network_name": "<PERSON><PERSON><PERSON>", "network": "XRP", "protocol": "XRP", "default": true, "is_ens_available": false, "payin_enabled": true, "payout_enabled": true, "precision_payout": "0.000001", "payout_fee": "4.************", "payout_is_payment_id": true, "payin_payment_id": true, "payin_confirmations": "1", "low_processing_time": "2.278", "high_processing_time": "62.292", "avg_processing_time": "26.***************", "crypto_payment_id_name": "", "crypto_explorer": "", "is_multichain": true}, "id": "XRP", "network": "XRP", "active": true, "fee": 4.24, "deposit": true, "withdraw": true, "precision": 1e-06, "limits": {"withdraw": {"min": null, "max": null}}}}, "limits": {"amount": {"min": null, "max": null}, "deposit": {"min": null, "max": null}, "withdraw": {"min": null, "max": null}}}, "USDC": {"info": {"full_name": "USD Coin", "crypto": true, "payin_enabled": true, "payout_enabled": true, "transfer_enabled": true, "transfer_to_wallet_enabled": true, "transfer_to_exchange_enabled": true, "sign": "U", "crypto_payment_id_name": "", "crypto_explorer": "https://etherscan.io/tx/{tx}", "precision_transfer": "0.000001", "delisted": false, "networks": [{"code": "ARB", "network_name": "Arbitrum One", "network": "ARB", "protocol": "ERC-20", "default": false, "is_ens_available": true, "payin_enabled": true, "payout_enabled": true, "precision_payout": "0.0000000000********", "payout_fee": "10.************", "payout_is_payment_id": false, "payin_payment_id": false, "payin_confirmations": "10", "contract_address": "******************************************", "low_processing_time": "4.419", "high_processing_time": "2105.429", "avg_processing_time": "1106.8960000000004", "crypto_payment_id_name": "", "crypto_explorer": "", "is_multichain": true, "asset_id": {"contract_address": "******************************************"}}, {"code": "AVAC", "network_name": "Avalanche C-Chain", "network": "AVAC", "protocol": "ERC-20", "default": false, "is_ens_available": false, "payin_enabled": true, "payout_enabled": true, "precision_payout": "0.0000000000********", "payout_fee": "10.************", "payout_is_payment_id": false, "payin_payment_id": false, "payin_confirmations": "5", "contract_address": "******************************************", "crypto_payment_id_name": "", "crypto_explorer": "", "is_multichain": true, "asset_id": {"contract_address": "******************************************"}}, {"code": "BASE", "network_name": "Base", "network": "BASE", "protocol": "ERC-20", "default": false, "is_ens_available": false, "payin_enabled": true, "payout_enabled": true, "precision_payout": "0.0000000000********", "payout_fee": "10.************", "payout_is_payment_id": false, "payin_payment_id": false, "payin_confirmations": "20", "contract_address": "******************************************", "low_processing_time": "848.862", "high_processing_time": "848.862", "avg_processing_time": "848.8619999999999", "crypto_payment_id_name": "", "crypto_explorer": "", "is_multichain": true, "asset_id": {"contract_address": "******************************************"}}, {"code": "BSC", "network_name": "Binance Smart Chain", "network": "BSC", "protocol": "BEP-20", "default": false, "is_ens_available": true, "payin_enabled": true, "payout_enabled": true, "precision_payout": "0.0000000000********", "payout_fee": "10.************", "payout_is_payment_id": false, "payin_payment_id": false, "payin_confirmations": "10", "contract_address": "******************************************", "low_processing_time": "5.659", "high_processing_time": "27.679", "avg_processing_time": "16.143756097560974", "crypto_payment_id_name": "", "crypto_explorer": "https://bscscan.com/tx/{tx}", "is_multichain": true, "asset_id": {"contract_address": "******************************************"}}, {"code": "ETH", "network_name": "Ethereum", "network": "ETH", "protocol": "ERC-20", "default": true, "is_ens_available": true, "payin_enabled": true, "payout_enabled": true, "precision_payout": "0.0000000000********", "payout_fee": "26.************", "payout_is_payment_id": false, "payin_payment_id": false, "payin_confirmations": "2", "contract_address": "******************************************", "low_processing_time": "42.613", "high_processing_time": "743.01", "avg_processing_time": "313.0477282608694", "crypto_payment_id_name": "", "crypto_explorer": "https://etherscan.io/tx/{tx}", "is_multichain": true, "asset_id": {"contract_address": "******************************************"}}, {"code": "NEAR", "network_name": "Near", "network": "NEAR", "protocol": "NEP141", "default": false, "is_ens_available": false, "payin_enabled": true, "payout_enabled": true, "precision_payout": "0.000001", "payout_fee": "10.100000000000", "payout_is_payment_id": false, "payin_payment_id": false, "payin_confirmations": "60", "contract_address": "17208628f84f5d6ad33f0da3bbbeb27ffcb398eac501a31bd6ad2011e36133a1", "crypto_payment_id_name": "", "crypto_explorer": "", "is_multichain": true, "asset_id": {"contract_address": "17208628f84f5d6ad33f0da3bbbeb27ffcb398eac501a31bd6ad2011e36133a1"}}, {"code": "OP", "network_name": "Optimism", "network": "OP", "protocol": "ERC-20", "default": false, "is_ens_available": true, "payin_enabled": true, "payout_enabled": true, "precision_payout": "0.0000000000********", "payout_fee": "10.100000000000", "payout_is_payment_id": false, "payin_payment_id": false, "payin_confirmations": "10", "contract_address": "******************************************", "low_processing_time": "1064.515", "high_processing_time": "1064.515", "avg_processing_time": "1064.5149999999996", "crypto_payment_id_name": "", "crypto_explorer": "", "is_multichain": true, "asset_id": {"contract_address": "******************************************"}}, {"code": "POLYGON", "network_name": "Polygon (Matic)", "network": "POLYGON", "protocol": "ERC-20", "default": false, "is_ens_available": false, "payin_enabled": true, "payout_enabled": true, "precision_payout": "0.000001", "payout_fee": "10.************", "payout_is_payment_id": false, "payin_payment_id": false, "payin_confirmations": "100", "contract_address": "******************************************", "low_processing_time": "5.392", "high_processing_time": "2825.277", "avg_processing_time": "1039.681457142857", "crypto_payment_id_name": "", "crypto_explorer": "", "is_multichain": true, "asset_id": {"contract_address": "******************************************"}}, {"code": "SOL", "network_name": "Solana", "network": "SOL", "protocol": "SPL", "default": false, "is_ens_available": true, "payin_enabled": true, "payout_enabled": true, "precision_payout": "0.000001", "payout_fee": "10.************", "payout_is_payment_id": false, "payin_payment_id": false, "payin_confirmations": "5", "low_processing_time": "1.37", "high_processing_time": "3984.995", "avg_processing_time": "347.70007692307695", "crypto_payment_id_name": "", "crypto_explorer": "https://solscan.io/tx/{tx}", "is_multichain": true, "asset_id": {"id": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"}}, {"code": "SUI", "network_name": "<PERSON><PERSON>", "network": "SUI", "protocol": "TOKEN", "default": false, "is_ens_available": false, "payin_enabled": true, "payout_enabled": true, "precision_payout": "0.000001", "payout_fee": "10.100000000000", "payout_is_payment_id": false, "payin_payment_id": false, "payin_confirmations": "10", "crypto_payment_id_name": "", "crypto_explorer": "", "is_multichain": true, "asset_id": {"id": "0xdba34672e30cb065b1f93e3ab55318768fd6fef66c15942c9f7cb846e2f900e7::usdc::USDC"}}, {"code": "XLM", "network_name": "Stellar", "network": "XLM", "protocol": "TOKEN", "default": false, "is_ens_available": false, "payin_enabled": true, "payout_enabled": true, "precision_payout": "0.0000001", "payout_fee": "10.100000000000", "payout_is_payment_id": true, "payin_payment_id": true, "payin_confirmations": "1", "crypto_payment_id_name": "", "crypto_explorer": "", "is_multichain": true, "asset_id": {"code": "USDC", "issuer": "GA5ZSEJYB37JRC5AVCIA5MOP4RHTM335X2KGX3IHOJAPP5RE34K4KZVN"}}]}, "id": "USDC", "numericId": null, "code": "USDC", "precision": 1e-06, "type": null, "name": "USD Coin", "active": true, "deposit": true, "withdraw": true, "fee": 10, "fees": {}, "networks": {"ERC-20": {"info": {"code": "POLYGON", "network_name": "Polygon (Matic)", "network": "POLYGON", "protocol": "ERC-20", "default": false, "is_ens_available": false, "payin_enabled": true, "payout_enabled": true, "precision_payout": "0.000001", "payout_fee": "10.************", "payout_is_payment_id": false, "payin_payment_id": false, "payin_confirmations": "100", "contract_address": "******************************************", "low_processing_time": "5.392", "high_processing_time": "2825.277", "avg_processing_time": "1039.681457142857", "crypto_payment_id_name": "", "crypto_explorer": "", "is_multichain": true, "asset_id": {"contract_address": "******************************************"}}, "id": "ERC-20", "network": "ERC-20", "active": true, "fee": 10, "deposit": true, "withdraw": true, "precision": 1e-06, "limits": {"withdraw": {"min": null, "max": null}}}, "BEP-20": {"info": {"code": "BSC", "network_name": "Binance Smart Chain", "network": "BSC", "protocol": "BEP-20", "default": false, "is_ens_available": true, "payin_enabled": true, "payout_enabled": true, "precision_payout": "0.0000000000********", "payout_fee": "10.************", "payout_is_payment_id": false, "payin_payment_id": false, "payin_confirmations": "10", "contract_address": "******************************************", "low_processing_time": "5.659", "high_processing_time": "27.679", "avg_processing_time": "16.143756097560974", "crypto_payment_id_name": "", "crypto_explorer": "https://bscscan.com/tx/{tx}", "is_multichain": true, "asset_id": {"contract_address": "******************************************"}}, "id": "BEP-20", "network": "BEP-20", "active": true, "fee": 10, "deposit": true, "withdraw": true, "precision": 1e-18, "limits": {"withdraw": {"min": null, "max": null}}}, "NEP141": {"info": {"code": "NEAR", "network_name": "Near", "network": "NEAR", "protocol": "NEP141", "default": false, "is_ens_available": false, "payin_enabled": true, "payout_enabled": true, "precision_payout": "0.000001", "payout_fee": "10.100000000000", "payout_is_payment_id": false, "payin_payment_id": false, "payin_confirmations": "60", "contract_address": "17208628f84f5d6ad33f0da3bbbeb27ffcb398eac501a31bd6ad2011e36133a1", "crypto_payment_id_name": "", "crypto_explorer": "", "is_multichain": true, "asset_id": {"contract_address": "17208628f84f5d6ad33f0da3bbbeb27ffcb398eac501a31bd6ad2011e36133a1"}}, "id": "NEP141", "network": "NEP141", "active": true, "fee": 10.1, "deposit": true, "withdraw": true, "precision": 1e-06, "limits": {"withdraw": {"min": null, "max": null}}}, "SPL": {"info": {"code": "SOL", "network_name": "Solana", "network": "SOL", "protocol": "SPL", "default": false, "is_ens_available": true, "payin_enabled": true, "payout_enabled": true, "precision_payout": "0.000001", "payout_fee": "10.************", "payout_is_payment_id": false, "payin_payment_id": false, "payin_confirmations": "5", "low_processing_time": "1.37", "high_processing_time": "3984.995", "avg_processing_time": "347.70007692307695", "crypto_payment_id_name": "", "crypto_explorer": "https://solscan.io/tx/{tx}", "is_multichain": true, "asset_id": {"id": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"}}, "id": "SPL", "network": "SPL", "active": true, "fee": 10, "deposit": true, "withdraw": true, "precision": 1e-06, "limits": {"withdraw": {"min": null, "max": null}}}, "TOKEN": {"info": {"code": "XLM", "network_name": "Stellar", "network": "XLM", "protocol": "TOKEN", "default": false, "is_ens_available": false, "payin_enabled": true, "payout_enabled": true, "precision_payout": "0.0000001", "payout_fee": "10.100000000000", "payout_is_payment_id": true, "payin_payment_id": true, "payin_confirmations": "1", "crypto_payment_id_name": "", "crypto_explorer": "", "is_multichain": true, "asset_id": {"code": "USDC", "issuer": "GA5ZSEJYB37JRC5AVCIA5MOP4RHTM335X2KGX3IHOJAPP5RE34K4KZVN"}}, "id": "TOKEN", "network": "TOKEN", "active": true, "fee": 10.1, "deposit": true, "withdraw": true, "precision": 1e-07, "limits": {"withdraw": {"min": null, "max": null}}}}, "limits": {"amount": {"min": null, "max": null}, "deposit": {"min": null, "max": null}, "withdraw": {"min": null, "max": null}}}}