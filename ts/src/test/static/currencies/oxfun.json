{"BTC": {"info": [{"asset": "BTC", "isCollateral": true, "loanToValue": "0.950000000", "loanToValueFactor": "0.000000000", "networkList": [{"network": "Bitcoin", "transactionPrecision": "8", "isWithdrawalFeeChargedToUser": true, "canDeposit": true, "canWithdraw": true, "minDeposit": "0.0001", "minWithdrawal": "0.0001"}]}], "id": "BTC", "numericId": null, "code": "BTC", "precision": 1e-08, "type": null, "name": null, "active": true, "deposit": true, "withdraw": true, "fee": null, "fees": {}, "networks": {"BTC": {"id": "Bitcoin", "network": "BTC", "margin": null, "deposit": true, "withdraw": true, "active": true, "fee": null, "precision": 1e-08, "limits": {"deposit": {"min": 0.0001, "max": null}, "withdraw": {"min": 0.0001, "max": null}}, "info": {"network": "Bitcoin", "transactionPrecision": "8", "isWithdrawalFeeChargedToUser": true, "canDeposit": true, "canWithdraw": true, "minDeposit": "0.0001", "minWithdrawal": "0.0001"}}}, "limits": {"withdraw": {"min": 0.0001, "max": null}, "deposit": {"min": 0.0001, "max": null}}}, "ETH": {"info": [{"asset": "ETH", "isCollateral": true, "loanToValue": "0.950000000", "loanToValueFactor": "0.000000000", "networkList": [{"network": "Ethereum", "transactionPrecision": "18", "isWithdrawalFeeChargedToUser": true, "canDeposit": true, "canWithdraw": true, "minDeposit": "0.0001", "minWithdrawal": "0.0001"}]}, {"asset": "ETH.ARB", "isCollateral": true, "loanToValue": "0.950000000", "loanToValueFactor": "0.000000000", "networkList": [{"network": "Arbitrum", "transactionPrecision": "18", "isWithdrawalFeeChargedToUser": true, "canDeposit": true, "canWithdraw": true, "minDeposit": "0.0001", "minWithdrawal": "0.0001"}]}, {"asset": "ETH.BASE", "isCollateral": true, "loanToValue": "0.950000000", "loanToValueFactor": "0.000000000", "networkList": [{"network": "Base", "transactionPrecision": "18", "isWithdrawalFeeChargedToUser": true, "canDeposit": true, "canWithdraw": true, "minDeposit": "0.0001", "minWithdrawal": "0.0001"}]}, {"asset": "ETH.MATIC", "isCollateral": true, "loanToValue": "0.950000000", "loanToValueFactor": "0.000000000", "networkList": [{"network": "Polygon", "tokenId": "******************************************", "transactionPrecision": "18", "isWithdrawalFeeChargedToUser": true, "canDeposit": true, "canWithdraw": true, "minDeposit": "0.0001", "minWithdrawal": "0.0001"}]}, {"asset": "ETH.BSC", "isCollateral": true, "loanToValue": "0.950000000", "loanToValueFactor": "0.000000000", "networkList": [{"network": "BNBSmartChain", "tokenId": "******************************************", "transactionPrecision": "18", "isWithdrawalFeeChargedToUser": true, "canDeposit": true, "canWithdraw": true, "minDeposit": "0.0001", "minWithdrawal": "0.0001"}]}, {"asset": "ETH.OPT", "isCollateral": true, "loanToValue": "0.950000000", "loanToValueFactor": "0.000000000", "networkList": [{"network": "Optimism", "transactionPrecision": "18", "isWithdrawalFeeChargedToUser": true, "canDeposit": true, "canWithdraw": true, "minDeposit": "0.0001", "minWithdrawal": "0.0001"}]}, {"asset": "ETH.BLAST", "isCollateral": true, "loanToValue": "0.950000000", "loanToValueFactor": "0.000000000", "networkList": [{"network": "Blast", "transactionPrecision": "18", "isWithdrawalFeeChargedToUser": true, "canDeposit": true, "canWithdraw": true, "minDeposit": "0.0001", "minWithdrawal": "0.0001"}]}, {"asset": "ETH.SCR", "isCollateral": true, "loanToValue": "0.950000000", "loanToValueFactor": "0.000000000", "networkList": [{"network": "<PERSON><PERSON>", "transactionPrecision": "18", "isWithdrawalFeeChargedToUser": true, "canDeposit": true, "canWithdraw": true, "minDeposit": "0.0001", "minWithdrawal": "0.0001"}]}], "id": "ETH", "numericId": null, "code": "ETH", "precision": 1e-18, "type": null, "name": null, "active": true, "deposit": true, "withdraw": true, "fee": null, "fees": {}, "networks": {"ERC20": {"id": "Ethereum", "network": "ERC20", "margin": null, "deposit": true, "withdraw": true, "active": true, "fee": null, "precision": 1e-18, "limits": {"deposit": {"min": 0.0001, "max": null}, "withdraw": {"min": 0.0001, "max": null}}, "info": {"network": "Ethereum", "transactionPrecision": "18", "isWithdrawalFeeChargedToUser": true, "canDeposit": true, "canWithdraw": true, "minDeposit": "0.0001", "minWithdrawal": "0.0001"}}, "ARB": {"id": "Arbitrum", "network": "ARB", "margin": null, "deposit": true, "withdraw": true, "active": true, "fee": null, "precision": 1e-18, "limits": {"deposit": {"min": 0.0001, "max": null}, "withdraw": {"min": 0.0001, "max": null}}, "info": {"network": "Arbitrum", "transactionPrecision": "18", "isWithdrawalFeeChargedToUser": true, "canDeposit": true, "canWithdraw": true, "minDeposit": "0.0001", "minWithdrawal": "0.0001"}}, "BASE": {"id": "Base", "network": "BASE", "margin": null, "deposit": true, "withdraw": true, "active": true, "fee": null, "precision": 1e-18, "limits": {"deposit": {"min": 0.0001, "max": null}, "withdraw": {"min": 0.0001, "max": null}}, "info": {"network": "Base", "transactionPrecision": "18", "isWithdrawalFeeChargedToUser": true, "canDeposit": true, "canWithdraw": true, "minDeposit": "0.0001", "minWithdrawal": "0.0001"}}, "MATIC": {"id": "Polygon", "network": "MATIC", "margin": null, "deposit": true, "withdraw": true, "active": true, "fee": null, "precision": 1e-18, "limits": {"deposit": {"min": 0.0001, "max": null}, "withdraw": {"min": 0.0001, "max": null}}, "info": {"network": "Polygon", "tokenId": "******************************************", "transactionPrecision": "18", "isWithdrawalFeeChargedToUser": true, "canDeposit": true, "canWithdraw": true, "minDeposit": "0.0001", "minWithdrawal": "0.0001"}}, "BNB": {"id": "BNBSmartChain", "network": "BNB", "margin": null, "deposit": true, "withdraw": true, "active": true, "fee": null, "precision": 1e-18, "limits": {"deposit": {"min": 0.0001, "max": null}, "withdraw": {"min": 0.0001, "max": null}}, "info": {"network": "BNBSmartChain", "tokenId": "******************************************", "transactionPrecision": "18", "isWithdrawalFeeChargedToUser": true, "canDeposit": true, "canWithdraw": true, "minDeposit": "0.0001", "minWithdrawal": "0.0001"}}, "OPTIMISM": {"id": "Optimism", "network": "OPTIMISM", "margin": null, "deposit": true, "withdraw": true, "active": true, "fee": null, "precision": 1e-18, "limits": {"deposit": {"min": 0.0001, "max": null}, "withdraw": {"min": 0.0001, "max": null}}, "info": {"network": "Optimism", "transactionPrecision": "18", "isWithdrawalFeeChargedToUser": true, "canDeposit": true, "canWithdraw": true, "minDeposit": "0.0001", "minWithdrawal": "0.0001"}}, "Blast": {"id": "Blast", "network": "Blast", "margin": null, "deposit": true, "withdraw": true, "active": true, "fee": null, "precision": 1e-18, "limits": {"deposit": {"min": 0.0001, "max": null}, "withdraw": {"min": 0.0001, "max": null}}, "info": {"network": "Blast", "transactionPrecision": "18", "isWithdrawalFeeChargedToUser": true, "canDeposit": true, "canWithdraw": true, "minDeposit": "0.0001", "minWithdrawal": "0.0001"}}, "Scroll": {"id": "<PERSON><PERSON>", "network": "<PERSON><PERSON>", "margin": null, "deposit": true, "withdraw": true, "active": true, "fee": null, "precision": 1e-18, "limits": {"deposit": {"min": 0.0001, "max": null}, "withdraw": {"min": 0.0001, "max": null}}, "info": {"network": "<PERSON><PERSON>", "transactionPrecision": "18", "isWithdrawalFeeChargedToUser": true, "canDeposit": true, "canWithdraw": true, "minDeposit": "0.0001", "minWithdrawal": "0.0001"}}}, "limits": {"withdraw": {"min": 0.0001, "max": null}, "deposit": {"min": 0.0001, "max": null}}}, "USDT": {"info": [{"asset": "USDT", "isCollateral": true, "loanToValue": "0.950000000", "loanToValueFactor": "0.000000000", "networkList": [{"network": "Ethereum", "tokenId": "******************************************", "transactionPrecision": "6", "isWithdrawalFeeChargedToUser": true, "canDeposit": true, "canWithdraw": true, "minDeposit": "0.0001", "minWithdrawal": "0.01"}]}, {"asset": "USDT.BSC", "isCollateral": true, "loanToValue": "0.950000000", "loanToValueFactor": "0.000000000", "networkList": [{"network": "BNBSmartChain", "tokenId": "******************************************", "transactionPrecision": "18", "isWithdrawalFeeChargedToUser": true, "canDeposit": true, "canWithdraw": true, "minDeposit": "0.0001", "minWithdrawal": "0.01"}]}, {"asset": "USDT.ARB", "isCollateral": true, "loanToValue": "0.950000000", "loanToValueFactor": "0.000000000", "networkList": [{"network": "Arbitrum", "tokenId": "******************************************", "transactionPrecision": "6", "isWithdrawalFeeChargedToUser": true, "canDeposit": true, "canWithdraw": true, "minDeposit": "0.0001", "minWithdrawal": "0.01"}]}, {"asset": "USDT.SPL", "isCollateral": true, "loanToValue": "0.950000000", "loanToValueFactor": "0.000000000", "networkList": [{"network": "Solana", "tokenId": "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB", "transactionPrecision": "6", "isWithdrawalFeeChargedToUser": true, "canDeposit": true, "canWithdraw": true, "minDeposit": "0.0001", "minWithdrawal": "0.01"}]}, {"asset": "USDT.TRC20", "isCollateral": true, "loanToValue": "0.950000000", "loanToValueFactor": "0.000000000", "networkList": [{"network": "Tron", "tokenId": "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t", "transactionPrecision": "6", "isWithdrawalFeeChargedToUser": true, "canDeposit": true, "canWithdraw": true, "minDeposit": "0.0001", "minWithdrawal": "0.01"}]}, {"asset": "USDT.OPT", "isCollateral": true, "loanToValue": "0.950000000", "loanToValueFactor": "0.000000000", "networkList": [{"network": "Optimism", "tokenId": "0x94b008aA00579c1307B0EF2c499aD98a8ce58e58", "transactionPrecision": "6", "isWithdrawalFeeChargedToUser": true, "canDeposit": true, "canWithdraw": true, "minDeposit": "0.0001", "minWithdrawal": "0.01"}]}, {"asset": "USDT.AVAX", "isCollateral": true, "loanToValue": "0.950000000", "loanToValueFactor": "0.000000000", "networkList": [{"network": "Avalanche", "tokenId": "0x9702230A8Ea53601f5cD2dc00fDBc13d4dF4A8c7", "transactionPrecision": "6", "isWithdrawalFeeChargedToUser": true, "canDeposit": true, "canWithdraw": true, "minDeposit": "0.0001", "minWithdrawal": "0.0001"}]}, {"asset": "USDT.BASE", "isCollateral": true, "loanToValue": "0.950000000", "loanToValueFactor": "0.000000000", "networkList": [{"network": "Base", "tokenId": "******************************************", "transactionPrecision": "6", "isWithdrawalFeeChargedToUser": true, "canDeposit": true, "canWithdraw": true, "minDeposit": "0.0001", "minWithdrawal": "0.0001"}]}], "id": "USDT", "numericId": null, "code": "USDT", "precision": 1e-06, "type": null, "name": null, "active": true, "deposit": true, "withdraw": true, "fee": null, "fees": {}, "networks": {"ERC20": {"id": "Ethereum", "network": "ERC20", "margin": null, "deposit": true, "withdraw": true, "active": true, "fee": null, "precision": 1e-06, "limits": {"deposit": {"min": 0.0001, "max": null}, "withdraw": {"min": 0.01, "max": null}}, "info": {"network": "Ethereum", "tokenId": "******************************************", "transactionPrecision": "6", "isWithdrawalFeeChargedToUser": true, "canDeposit": true, "canWithdraw": true, "minDeposit": "0.0001", "minWithdrawal": "0.01"}}, "BNB": {"id": "BNBSmartChain", "network": "BNB", "margin": null, "deposit": true, "withdraw": true, "active": true, "fee": null, "precision": 1e-18, "limits": {"deposit": {"min": 0.0001, "max": null}, "withdraw": {"min": 0.01, "max": null}}, "info": {"network": "BNBSmartChain", "tokenId": "******************************************", "transactionPrecision": "18", "isWithdrawalFeeChargedToUser": true, "canDeposit": true, "canWithdraw": true, "minDeposit": "0.0001", "minWithdrawal": "0.01"}}, "ARB": {"id": "Arbitrum", "network": "ARB", "margin": null, "deposit": true, "withdraw": true, "active": true, "fee": null, "precision": 1e-06, "limits": {"deposit": {"min": 0.0001, "max": null}, "withdraw": {"min": 0.01, "max": null}}, "info": {"network": "Arbitrum", "tokenId": "******************************************", "transactionPrecision": "6", "isWithdrawalFeeChargedToUser": true, "canDeposit": true, "canWithdraw": true, "minDeposit": "0.0001", "minWithdrawal": "0.01"}}, "SOL": {"id": "Solana", "network": "SOL", "margin": null, "deposit": true, "withdraw": true, "active": true, "fee": null, "precision": 1e-06, "limits": {"deposit": {"min": 0.0001, "max": null}, "withdraw": {"min": 0.01, "max": null}}, "info": {"network": "Solana", "tokenId": "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB", "transactionPrecision": "6", "isWithdrawalFeeChargedToUser": true, "canDeposit": true, "canWithdraw": true, "minDeposit": "0.0001", "minWithdrawal": "0.01"}}, "Tron": {"id": "Tron", "network": "Tron", "margin": null, "deposit": true, "withdraw": true, "active": true, "fee": null, "precision": 1e-06, "limits": {"deposit": {"min": 0.0001, "max": null}, "withdraw": {"min": 0.01, "max": null}}, "info": {"network": "Tron", "tokenId": "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t", "transactionPrecision": "6", "isWithdrawalFeeChargedToUser": true, "canDeposit": true, "canWithdraw": true, "minDeposit": "0.0001", "minWithdrawal": "0.01"}}, "OPTIMISM": {"id": "Optimism", "network": "OPTIMISM", "margin": null, "deposit": true, "withdraw": true, "active": true, "fee": null, "precision": 1e-06, "limits": {"deposit": {"min": 0.0001, "max": null}, "withdraw": {"min": 0.01, "max": null}}, "info": {"network": "Optimism", "tokenId": "0x94b008aA00579c1307B0EF2c499aD98a8ce58e58", "transactionPrecision": "6", "isWithdrawalFeeChargedToUser": true, "canDeposit": true, "canWithdraw": true, "minDeposit": "0.0001", "minWithdrawal": "0.01"}}, "AVAX": {"id": "Avalanche", "network": "AVAX", "margin": null, "deposit": true, "withdraw": true, "active": true, "fee": null, "precision": 1e-06, "limits": {"deposit": {"min": 0.0001, "max": null}, "withdraw": {"min": 0.0001, "max": null}}, "info": {"network": "Avalanche", "tokenId": "0x9702230A8Ea53601f5cD2dc00fDBc13d4dF4A8c7", "transactionPrecision": "6", "isWithdrawalFeeChargedToUser": true, "canDeposit": true, "canWithdraw": true, "minDeposit": "0.0001", "minWithdrawal": "0.0001"}}, "BASE": {"id": "Base", "network": "BASE", "margin": null, "deposit": true, "withdraw": true, "active": true, "fee": null, "precision": 1e-06, "limits": {"deposit": {"min": 0.0001, "max": null}, "withdraw": {"min": 0.0001, "max": null}}, "info": {"network": "Base", "tokenId": "******************************************", "transactionPrecision": "6", "isWithdrawalFeeChargedToUser": true, "canDeposit": true, "canWithdraw": true, "minDeposit": "0.0001", "minWithdrawal": "0.0001"}}}, "limits": {"withdraw": {"min": 0.0001, "max": null}, "deposit": {"min": 0.0001, "max": null}}}, "USDC": {"info": [{"asset": "USDC", "isCollateral": true, "loanToValue": "0.950000000", "loanToValueFactor": "0.000000000", "networkList": [{"network": "Ethereum", "tokenId": "******************************************", "transactionPrecision": "6", "isWithdrawalFeeChargedToUser": true, "canDeposit": true, "canWithdraw": true, "minDeposit": "0.0001", "minWithdrawal": "0.01"}]}, {"asset": "USDC.ARB", "isCollateral": true, "loanToValue": "0.950000000", "loanToValueFactor": "0.000000000", "networkList": [{"network": "Arbitrum", "tokenId": "******************************************", "transactionPrecision": "6", "isWithdrawalFeeChargedToUser": true, "canDeposit": true, "canWithdraw": true, "minDeposit": "0.0001", "minWithdrawal": "0.01"}]}, {"asset": "USDC.BASE", "isCollateral": true, "loanToValue": "0.950000000", "loanToValueFactor": "0.000000000", "networkList": [{"network": "Base", "tokenId": "******************************************", "transactionPrecision": "6", "isWithdrawalFeeChargedToUser": true, "canDeposit": true, "canWithdraw": true, "minDeposit": "0.0001", "minWithdrawal": "0.01"}]}, {"asset": "USDC.SPL", "isCollateral": true, "loanToValue": "0.950000000", "loanToValueFactor": "0.000000000", "networkList": [{"network": "Solana", "tokenId": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v", "transactionPrecision": "6", "isWithdrawalFeeChargedToUser": true, "canDeposit": true, "canWithdraw": true, "minDeposit": "0.0001", "minWithdrawal": "0.01"}]}, {"asset": "USDC.MATIC", "isCollateral": true, "loanToValue": "0.950000000", "loanToValueFactor": "0.000000000", "networkList": [{"network": "Polygon", "tokenId": "0x3c499c542cef5e3811e1192ce70d8cc03d5c3359", "transactionPrecision": "6", "isWithdrawalFeeChargedToUser": true, "canDeposit": true, "canWithdraw": true, "minDeposit": "0.0001", "minWithdrawal": "0.0001"}]}, {"asset": "USDC.OP", "isCollateral": true, "loanToValue": "0.950000000", "loanToValueFactor": "0.000000000", "networkList": [{"network": "Optimism", "tokenId": "0x0b2C639c533813f4Aa9D7837CAf62653d097Ff85", "transactionPrecision": "6", "isWithdrawalFeeChargedToUser": true, "canDeposit": true, "canWithdraw": true, "minDeposit": "0.0001", "minWithdrawal": "0.0001"}]}, {"asset": "USDC.BSC", "isCollateral": true, "loanToValue": "0.950000000", "loanToValueFactor": "0.000000000", "networkList": [{"network": "BNBSmartChain", "tokenId": "0x8AC76a51cc950d9822D68b83fE1Ad97B32Cd580d", "transactionPrecision": "18", "isWithdrawalFeeChargedToUser": true, "canDeposit": true, "canWithdraw": true, "minDeposit": "0.0001", "minWithdrawal": "0.0001"}]}, {"asset": "USDC.RON", "isCollateral": true, "loanToValue": "0.950000000", "loanToValueFactor": "0.000000000", "networkList": [{"network": "<PERSON><PERSON>", "tokenId": "0x0b7007c13325c48911f73a2dad5fa5dcbf808adc", "transactionPrecision": "6", "isWithdrawalFeeChargedToUser": true, "canDeposit": true, "canWithdraw": true, "minDeposit": "0.0001", "minWithdrawal": "0.0001"}]}, {"asset": "USDC.AVAX", "isCollateral": true, "loanToValue": "0.950000000", "loanToValueFactor": "0.000000000", "networkList": [{"network": "Avalanche", "tokenId": "******************************************", "transactionPrecision": "6", "isWithdrawalFeeChargedToUser": true, "canDeposit": true, "canWithdraw": true, "minDeposit": "0.0001", "minWithdrawal": "0.0001"}]}], "id": "USDC", "numericId": null, "code": "USDC", "precision": 1e-06, "type": null, "name": null, "active": true, "deposit": true, "withdraw": true, "fee": null, "fees": {}, "networks": {"ERC20": {"id": "Ethereum", "network": "ERC20", "margin": null, "deposit": true, "withdraw": true, "active": true, "fee": null, "precision": 1e-06, "limits": {"deposit": {"min": 0.0001, "max": null}, "withdraw": {"min": 0.01, "max": null}}, "info": {"network": "Ethereum", "tokenId": "******************************************", "transactionPrecision": "6", "isWithdrawalFeeChargedToUser": true, "canDeposit": true, "canWithdraw": true, "minDeposit": "0.0001", "minWithdrawal": "0.01"}}, "ARB": {"id": "Arbitrum", "network": "ARB", "margin": null, "deposit": true, "withdraw": true, "active": true, "fee": null, "precision": 1e-06, "limits": {"deposit": {"min": 0.0001, "max": null}, "withdraw": {"min": 0.01, "max": null}}, "info": {"network": "Arbitrum", "tokenId": "******************************************", "transactionPrecision": "6", "isWithdrawalFeeChargedToUser": true, "canDeposit": true, "canWithdraw": true, "minDeposit": "0.0001", "minWithdrawal": "0.01"}}, "BASE": {"id": "Base", "network": "BASE", "margin": null, "deposit": true, "withdraw": true, "active": true, "fee": null, "precision": 1e-06, "limits": {"deposit": {"min": 0.0001, "max": null}, "withdraw": {"min": 0.01, "max": null}}, "info": {"network": "Base", "tokenId": "******************************************", "transactionPrecision": "6", "isWithdrawalFeeChargedToUser": true, "canDeposit": true, "canWithdraw": true, "minDeposit": "0.0001", "minWithdrawal": "0.01"}}, "SOL": {"id": "Solana", "network": "SOL", "margin": null, "deposit": true, "withdraw": true, "active": true, "fee": null, "precision": 1e-06, "limits": {"deposit": {"min": 0.0001, "max": null}, "withdraw": {"min": 0.01, "max": null}}, "info": {"network": "Solana", "tokenId": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v", "transactionPrecision": "6", "isWithdrawalFeeChargedToUser": true, "canDeposit": true, "canWithdraw": true, "minDeposit": "0.0001", "minWithdrawal": "0.01"}}, "MATIC": {"id": "Polygon", "network": "MATIC", "margin": null, "deposit": true, "withdraw": true, "active": true, "fee": null, "precision": 1e-06, "limits": {"deposit": {"min": 0.0001, "max": null}, "withdraw": {"min": 0.0001, "max": null}}, "info": {"network": "Polygon", "tokenId": "0x3c499c542cef5e3811e1192ce70d8cc03d5c3359", "transactionPrecision": "6", "isWithdrawalFeeChargedToUser": true, "canDeposit": true, "canWithdraw": true, "minDeposit": "0.0001", "minWithdrawal": "0.0001"}}, "OPTIMISM": {"id": "Optimism", "network": "OPTIMISM", "margin": null, "deposit": true, "withdraw": true, "active": true, "fee": null, "precision": 1e-06, "limits": {"deposit": {"min": 0.0001, "max": null}, "withdraw": {"min": 0.0001, "max": null}}, "info": {"network": "Optimism", "tokenId": "0x0b2C639c533813f4Aa9D7837CAf62653d097Ff85", "transactionPrecision": "6", "isWithdrawalFeeChargedToUser": true, "canDeposit": true, "canWithdraw": true, "minDeposit": "0.0001", "minWithdrawal": "0.0001"}}, "BNB": {"id": "BNBSmartChain", "network": "BNB", "margin": null, "deposit": true, "withdraw": true, "active": true, "fee": null, "precision": 1e-18, "limits": {"deposit": {"min": 0.0001, "max": null}, "withdraw": {"min": 0.0001, "max": null}}, "info": {"network": "BNBSmartChain", "tokenId": "0x8AC76a51cc950d9822D68b83fE1Ad97B32Cd580d", "transactionPrecision": "18", "isWithdrawalFeeChargedToUser": true, "canDeposit": true, "canWithdraw": true, "minDeposit": "0.0001", "minWithdrawal": "0.0001"}}, "Ronin": {"id": "<PERSON><PERSON>", "network": "<PERSON><PERSON>", "margin": null, "deposit": true, "withdraw": true, "active": true, "fee": null, "precision": 1e-06, "limits": {"deposit": {"min": 0.0001, "max": null}, "withdraw": {"min": 0.0001, "max": null}}, "info": {"network": "<PERSON><PERSON>", "tokenId": "0x0b7007c13325c48911f73a2dad5fa5dcbf808adc", "transactionPrecision": "6", "isWithdrawalFeeChargedToUser": true, "canDeposit": true, "canWithdraw": true, "minDeposit": "0.0001", "minWithdrawal": "0.0001"}}, "AVAX": {"id": "Avalanche", "network": "AVAX", "margin": null, "deposit": true, "withdraw": true, "active": true, "fee": null, "precision": 1e-06, "limits": {"deposit": {"min": 0.0001, "max": null}, "withdraw": {"min": 0.0001, "max": null}}, "info": {"network": "Avalanche", "tokenId": "******************************************", "transactionPrecision": "6", "isWithdrawalFeeChargedToUser": true, "canDeposit": true, "canWithdraw": true, "minDeposit": "0.0001", "minWithdrawal": "0.0001"}}}, "limits": {"withdraw": {"min": 0.0001, "max": null}, "deposit": {"min": 0.0001, "max": null}}}, "LTC": {"info": [{"asset": "LTC", "isCollateral": true, "loanToValue": "0.850000000", "loanToValueFactor": "0.002330000", "networkList": [{"network": "Litecoin", "transactionPrecision": "8", "isWithdrawalFeeChargedToUser": true, "canDeposit": true, "canWithdraw": true, "minDeposit": "0.0001", "minWithdrawal": "0.0001"}]}], "id": "LTC", "numericId": null, "code": "LTC", "precision": 1e-08, "type": null, "name": null, "active": true, "deposit": true, "withdraw": true, "fee": null, "fees": {}, "networks": {"Litecoin": {"id": "Litecoin", "network": "Litecoin", "margin": null, "deposit": true, "withdraw": true, "active": true, "fee": null, "precision": 1e-08, "limits": {"deposit": {"min": 0.0001, "max": null}, "withdraw": {"min": 0.0001, "max": null}}, "info": {"network": "Litecoin", "transactionPrecision": "8", "isWithdrawalFeeChargedToUser": true, "canDeposit": true, "canWithdraw": true, "minDeposit": "0.0001", "minWithdrawal": "0.0001"}}}, "limits": {"withdraw": {"min": 0.0001, "max": null}, "deposit": {"min": 0.0001, "max": null}}}, "DOGE": {"info": [{"asset": "DOGE", "isCollateral": true, "loanToValue": "0.850000000", "loanToValueFactor": "0.000081500", "networkList": [{"network": "<PERSON><PERSON><PERSON><PERSON>", "transactionPrecision": "8", "isWithdrawalFeeChargedToUser": true, "canDeposit": true, "canWithdraw": true, "minDeposit": "0.0001", "minWithdrawal": "1"}]}], "id": "DOGE", "numericId": null, "code": "DOGE", "precision": 1e-08, "type": null, "name": null, "active": true, "deposit": true, "withdraw": true, "fee": null, "fees": {}, "networks": {"Dogecoin": {"id": "<PERSON><PERSON><PERSON><PERSON>", "network": "<PERSON><PERSON><PERSON><PERSON>", "margin": null, "deposit": true, "withdraw": true, "active": true, "fee": null, "precision": 1e-08, "limits": {"deposit": {"min": 0.0001, "max": null}, "withdraw": {"min": 1, "max": null}}, "info": {"network": "<PERSON><PERSON><PERSON><PERSON>", "transactionPrecision": "8", "isWithdrawalFeeChargedToUser": true, "canDeposit": true, "canWithdraw": true, "minDeposit": "0.0001", "minWithdrawal": "1"}}}, "limits": {"withdraw": {"min": 1, "max": null}, "deposit": {"min": 0.0001, "max": null}}}, "OX": {"info": [{"asset": "OX", "isCollateral": true, "loanToValue": "1.000000000", "loanToValueFactor": "0.000000000", "networkList": [{"network": "Ethereum", "tokenId": "******************************************", "transactionPrecision": "18", "isWithdrawalFeeChargedToUser": true, "canDeposit": true, "canWithdraw": true, "minDeposit": "0.0001", "minWithdrawal": "0.0001"}, {"network": "Solana", "tokenId": "********************************************", "transactionPrecision": "9", "isWithdrawalFeeChargedToUser": true, "canDeposit": true, "canWithdraw": true, "minDeposit": "0.0001", "minWithdrawal": "0.0001"}, {"network": "BNBSmartChain", "tokenId": "******************************************", "transactionPrecision": "18", "isWithdrawalFeeChargedToUser": true, "canDeposit": true, "canWithdraw": true, "minDeposit": "0.0001", "minWithdrawal": "0.0001"}, {"network": "Blast", "tokenId": "******************************************", "transactionPrecision": "18", "isWithdrawalFeeChargedToUser": true, "canDeposit": true, "canWithdraw": true, "minDeposit": "0.0001", "minWithdrawal": "0.0001"}, {"network": "Base", "tokenId": "******************************************", "transactionPrecision": "18", "isWithdrawalFeeChargedToUser": true, "canDeposit": true, "canWithdraw": true, "minDeposit": "0.0001", "minWithdrawal": "0.0001"}, {"network": "Avalanche", "tokenId": "******************************************", "transactionPrecision": "18", "isWithdrawalFeeChargedToUser": true, "canDeposit": true, "canWithdraw": true, "minDeposit": "0.0001", "minWithdrawal": "0.0001"}, {"network": "Solana", "tokenId": "DV3845GEAVXfwpyVGGgWbqBVCtzHdCXNCGfcdboSEuZz", "transactionPrecision": "8", "isWithdrawalFeeChargedToUser": false, "canDeposit": true, "canWithdraw": false, "minDeposit": "0.0001", "minWithdrawal": "0.0001"}, {"network": "Arbitrum", "tokenId": "******************************************", "transactionPrecision": "18", "isWithdrawalFeeChargedToUser": true, "canDeposit": true, "canWithdraw": true, "minDeposit": "0.0001", "minWithdrawal": "0.0001"}]}], "id": "OX", "numericId": null, "code": "OX", "precision": 1e-08, "type": null, "name": null, "active": true, "deposit": true, "withdraw": true, "fee": null, "fees": {}, "networks": {"ERC20": {"id": "Ethereum", "network": "ERC20", "margin": null, "deposit": true, "withdraw": true, "active": true, "fee": null, "precision": 1e-18, "limits": {"deposit": {"min": 0.0001, "max": null}, "withdraw": {"min": 0.0001, "max": null}}, "info": {"network": "Ethereum", "tokenId": "******************************************", "transactionPrecision": "18", "isWithdrawalFeeChargedToUser": true, "canDeposit": true, "canWithdraw": true, "minDeposit": "0.0001", "minWithdrawal": "0.0001"}}, "SOL": {"id": "Solana", "network": "SOL", "margin": null, "deposit": true, "withdraw": false, "active": false, "fee": null, "precision": 1e-08, "limits": {"deposit": {"min": 0.0001, "max": null}, "withdraw": {"min": 0.0001, "max": null}}, "info": {"network": "Solana", "tokenId": "DV3845GEAVXfwpyVGGgWbqBVCtzHdCXNCGfcdboSEuZz", "transactionPrecision": "8", "isWithdrawalFeeChargedToUser": false, "canDeposit": true, "canWithdraw": false, "minDeposit": "0.0001", "minWithdrawal": "0.0001"}}, "BNB": {"id": "BNBSmartChain", "network": "BNB", "margin": null, "deposit": true, "withdraw": true, "active": true, "fee": null, "precision": 1e-18, "limits": {"deposit": {"min": 0.0001, "max": null}, "withdraw": {"min": 0.0001, "max": null}}, "info": {"network": "BNBSmartChain", "tokenId": "******************************************", "transactionPrecision": "18", "isWithdrawalFeeChargedToUser": true, "canDeposit": true, "canWithdraw": true, "minDeposit": "0.0001", "minWithdrawal": "0.0001"}}, "Blast": {"id": "Blast", "network": "Blast", "margin": null, "deposit": true, "withdraw": true, "active": true, "fee": null, "precision": 1e-18, "limits": {"deposit": {"min": 0.0001, "max": null}, "withdraw": {"min": 0.0001, "max": null}}, "info": {"network": "Blast", "tokenId": "******************************************", "transactionPrecision": "18", "isWithdrawalFeeChargedToUser": true, "canDeposit": true, "canWithdraw": true, "minDeposit": "0.0001", "minWithdrawal": "0.0001"}}, "BASE": {"id": "Base", "network": "BASE", "margin": null, "deposit": true, "withdraw": true, "active": true, "fee": null, "precision": 1e-18, "limits": {"deposit": {"min": 0.0001, "max": null}, "withdraw": {"min": 0.0001, "max": null}}, "info": {"network": "Base", "tokenId": "******************************************", "transactionPrecision": "18", "isWithdrawalFeeChargedToUser": true, "canDeposit": true, "canWithdraw": true, "minDeposit": "0.0001", "minWithdrawal": "0.0001"}}, "AVAX": {"id": "Avalanche", "network": "AVAX", "margin": null, "deposit": true, "withdraw": true, "active": true, "fee": null, "precision": 1e-18, "limits": {"deposit": {"min": 0.0001, "max": null}, "withdraw": {"min": 0.0001, "max": null}}, "info": {"network": "Avalanche", "tokenId": "******************************************", "transactionPrecision": "18", "isWithdrawalFeeChargedToUser": true, "canDeposit": true, "canWithdraw": true, "minDeposit": "0.0001", "minWithdrawal": "0.0001"}}, "ARB": {"id": "Arbitrum", "network": "ARB", "margin": null, "deposit": true, "withdraw": true, "active": true, "fee": null, "precision": 1e-18, "limits": {"deposit": {"min": 0.0001, "max": null}, "withdraw": {"min": 0.0001, "max": null}}, "info": {"network": "Arbitrum", "tokenId": "******************************************", "transactionPrecision": "18", "isWithdrawalFeeChargedToUser": true, "canDeposit": true, "canWithdraw": true, "minDeposit": "0.0001", "minWithdrawal": "0.0001"}}}, "limits": {"withdraw": {"min": 0.0001, "max": null}, "deposit": {"min": 0.0001, "max": null}}}}