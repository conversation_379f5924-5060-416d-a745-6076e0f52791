{"BTC": {"info": {"currency": "BTC", "name": "BTC", "fullName": "Bitcoin", "precision": 8, "confirms": null, "contractAddress": null, "isMarginEnabled": true, "isDebitEnabled": true, "chains": [{"chainName": "BTC", "withdrawalMinSize": "0.0005", "depositMinSize": "0.0002", "withdrawFeeRate": "0", "withdrawalMinFee": "0.00025", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 3, "preConfirms": 1, "contractAddress": "", "withdrawPrecision": 8, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "btc"}, {"chainName": "Lightning Network", "withdrawalMinSize": "0.00004", "depositMinSize": "0.00001", "withdrawFeeRate": "0", "withdrawalMinFee": "0.00002", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 1, "preConfirms": 1, "contractAddress": "", "withdrawPrecision": 8, "maxWithdraw": null, "maxDeposit": "0.03", "needTag": false, "chainId": "btcln"}, {"chainName": "KCC", "withdrawalMinSize": "0.0008", "depositMinSize": null, "withdrawFeeRate": "0", "withdrawalMinFee": "0.00002", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 20, "preConfirms": 20, "contractAddress": "******************************************", "withdrawPrecision": 8, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "kcc"}, {"chainName": "ARBITRUM", "withdrawalMinSize": "0.0005", "depositMinSize": null, "withdrawFeeRate": "0", "withdrawalMinFee": "0.0001", "isWithdrawEnabled": false, "isDepositEnabled": false, "confirms": 1900, "preConfirms": 120, "contractAddress": "******************************************", "withdrawPrecision": 8, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "arbitrum"}, {"chainName": "BTC-Segwit", "withdrawalMinSize": "0.0008", "depositMinSize": "0.0002", "withdrawFeeRate": "0", "withdrawalMinFee": "0.0005", "isWithdrawEnabled": false, "isDepositEnabled": true, "confirms": 2, "preConfirms": 2, "contractAddress": "", "withdrawPrecision": 8, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "bech32"}]}, "id": "BTC", "numericId": null, "code": "BTC", "precision": 1e-08, "type": "crypto", "name": "Bitcoin", "active": true, "deposit": true, "withdraw": true, "fee": 2e-05, "fees": {}, "networks": {"BRC20": {"info": {"chainName": "BTC", "withdrawalMinSize": "0.0005", "depositMinSize": "0.0002", "withdrawFeeRate": "0", "withdrawalMinFee": "0.00025", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 3, "preConfirms": 1, "contractAddress": "", "withdrawPrecision": 8, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "btc"}, "id": "btc", "name": "BTC", "code": "BRC20", "active": true, "fee": 0.00025, "deposit": true, "withdraw": true, "precision": 1e-08, "limits": {"withdraw": {"min": 0.0005, "max": null}, "deposit": {"min": 0.0002, "max": null}}}, "btcln": {"info": {"chainName": "Lightning Network", "withdrawalMinSize": "0.00004", "depositMinSize": "0.00001", "withdrawFeeRate": "0", "withdrawalMinFee": "0.00002", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 1, "preConfirms": 1, "contractAddress": "", "withdrawPrecision": 8, "maxWithdraw": null, "maxDeposit": "0.03", "needTag": false, "chainId": "btcln"}, "id": "btcln", "name": "Lightning Network", "code": "btcln", "active": true, "fee": 2e-05, "deposit": true, "withdraw": true, "precision": 1e-08, "limits": {"withdraw": {"min": 4e-05, "max": null}, "deposit": {"min": 1e-05, "max": 0.03}}}, "KCC": {"info": {"chainName": "KCC", "withdrawalMinSize": "0.0008", "depositMinSize": null, "withdrawFeeRate": "0", "withdrawalMinFee": "0.00002", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 20, "preConfirms": 20, "contractAddress": "******************************************", "withdrawPrecision": 8, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "kcc"}, "id": "kcc", "name": "KCC", "code": "KCC", "active": true, "fee": 2e-05, "deposit": true, "withdraw": true, "precision": 1e-08, "limits": {"withdraw": {"min": 0.0008, "max": null}, "deposit": {"min": null, "max": null}}}, "ARBONE": {"info": {"chainName": "ARBITRUM", "withdrawalMinSize": "0.0005", "depositMinSize": null, "withdrawFeeRate": "0", "withdrawalMinFee": "0.0001", "isWithdrawEnabled": false, "isDepositEnabled": false, "confirms": 1900, "preConfirms": 120, "contractAddress": "******************************************", "withdrawPrecision": 8, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "arbitrum"}, "id": "arbitrum", "name": "ARBITRUM", "code": "ARBONE", "active": false, "fee": 0.0001, "deposit": false, "withdraw": false, "precision": 1e-08, "limits": {"withdraw": {"min": 0.0005, "max": null}, "deposit": {"min": null, "max": null}}}, "BTCNATIVESEGWIT": {"info": {"chainName": "BTC-Segwit", "withdrawalMinSize": "0.0008", "depositMinSize": "0.0002", "withdrawFeeRate": "0", "withdrawalMinFee": "0.0005", "isWithdrawEnabled": false, "isDepositEnabled": true, "confirms": 2, "preConfirms": 2, "contractAddress": "", "withdrawPrecision": 8, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "bech32"}, "id": "bech32", "name": "BTC-Segwit", "code": "BTCNATIVESEGWIT", "active": false, "fee": 0.0005, "deposit": true, "withdraw": false, "precision": 1e-08, "limits": {"withdraw": {"min": 0.0008, "max": null}, "deposit": {"min": 0.0002, "max": null}}}}, "limits": {"deposit": {"min": 1e-05, "max": 0.03}, "withdraw": {"min": 4e-05, "max": null}}}, "USDT": {"info": {"currency": "USDT", "name": "USDT", "fullName": "<PERSON><PERSON>", "precision": 8, "confirms": null, "contractAddress": null, "isMarginEnabled": true, "isDebitEnabled": true, "chains": [{"chainName": "APT", "withdrawalMinSize": "1", "depositMinSize": null, "withdrawFeeRate": "0", "withdrawalMinFee": "0.5", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 1, "preConfirms": 1, "contractAddress": "0x357b0b74bc833e95a115ad22604854d6b0fca151cecd94111770e5d6ffc9dc2b", "withdrawPrecision": 6, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "aptos"}, {"chainName": "<PERSON><PERSON>(Polkadot)", "withdrawalMinSize": "10", "depositMinSize": "0.7", "withdrawFeeRate": "0", "withdrawalMinFee": "1", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 30, "preConfirms": 30, "contractAddress": "1984", "withdrawPrecision": 6, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "statemint"}, {"chainName": "OPTIMISM", "withdrawalMinSize": "3", "depositMinSize": null, "withdrawFeeRate": "0", "withdrawalMinFee": "1", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 500, "preConfirms": 100, "contractAddress": "0x94b008aa00579c1307b0ef2c499ad98a8ce58e58", "withdrawPrecision": 6, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "optimism"}, {"chainName": "KCC", "withdrawalMinSize": "1", "depositMinSize": null, "withdrawFeeRate": "0", "withdrawalMinFee": "0.5", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 20, "preConfirms": 20, "contractAddress": "0x0039f574ee5cc39bdd162e9a88e3eb1f111baf48", "withdrawPrecision": 8, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "kcc"}, {"chainName": "SOL", "withdrawalMinSize": "3", "depositMinSize": null, "withdrawFeeRate": "0", "withdrawalMinFee": "1.5", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 200, "preConfirms": 200, "contractAddress": "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB", "withdrawPrecision": 6, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "sol"}, {"chainName": "TON", "withdrawalMinSize": "1", "depositMinSize": "0.1", "withdrawFeeRate": "0", "withdrawalMinFee": "0.5", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 1, "preConfirms": 1, "contractAddress": "EQCxE6mUtQJKFnGfaROTKOt1lZbDiiX1kCixRv7Nw2Id_sDs", "withdrawPrecision": 6, "maxWithdraw": null, "maxDeposit": null, "needTag": true, "chainId": "ton"}, {"chainName": "BEP20", "withdrawalMinSize": "10", "depositMinSize": "1", "withdrawFeeRate": "0", "withdrawalMinFee": "1", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 60, "preConfirms": 15, "contractAddress": "0x55d398326f99059ff775485246999027b3197955", "withdrawPrecision": 8, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "bsc"}, {"chainName": "KAVA EVM Co-Chain", "withdrawalMinSize": "3", "depositMinSize": null, "withdrawFeeRate": "0", "withdrawalMinFee": "1", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 20, "preConfirms": 20, "contractAddress": "0x919c1c267bc06a7039e03fcc2ef738525769109c", "withdrawPrecision": 6, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "<PERSON><PERSON><PERSON><PERSON>"}, {"chainName": "Polygon POS", "withdrawalMinSize": "35", "depositMinSize": null, "withdrawFeeRate": "0", "withdrawalMinFee": "35", "isWithdrawEnabled": false, "isDepositEnabled": true, "confirms": 800, "preConfirms": 300, "contractAddress": "0xc2132d05d31c914a87c6611c10748aeb04b58e8f", "withdrawPrecision": 6, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "matic"}, {"chainName": "ERC20", "withdrawalMinSize": "30", "depositMinSize": "7.5", "withdrawFeeRate": "0.002", "withdrawMaxFee": "36", "withdrawalMinFee": "5.5", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 96, "preConfirms": 32, "contractAddress": "******************************************", "withdrawPrecision": 6, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "eth"}, {"chainName": "AVAX C-Chain", "withdrawalMinSize": "2", "depositMinSize": null, "withdrawFeeRate": "0", "withdrawalMinFee": "1", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 32, "preConfirms": 32, "contractAddress": "******************************************", "withdrawPrecision": 6, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "avaxc"}, {"chainName": "NEAR", "withdrawalMinSize": "3", "depositMinSize": null, "withdrawFeeRate": "0", "withdrawalMinFee": "0.5", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 5, "preConfirms": 5, "contractAddress": "usdt.tether-token.near", "withdrawPrecision": 6, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "near"}, {"chainName": "TRC20", "withdrawalMinSize": "3", "depositMinSize": "1", "withdrawFeeRate": "0", "withdrawalMinFee": "1.5", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 3, "preConfirms": 3, "contractAddress": "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t", "withdrawPrecision": 6, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "trx"}, {"chainName": "XTZ", "withdrawalMinSize": "2", "depositMinSize": null, "withdrawFeeRate": "0", "withdrawalMinFee": "1", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 10, "preConfirms": 10, "contractAddress": "KT1XnTn74bUtxHfDtBmm2bGZAQfhPbvKWR8o", "withdrawPrecision": 6, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "xtz"}, {"chainName": "ARBITRUM", "withdrawalMinSize": "5", "depositMinSize": null, "withdrawFeeRate": "0", "withdrawalMinFee": "1", "isWithdrawEnabled": false, "isDepositEnabled": true, "confirms": 1900, "preConfirms": 120, "contractAddress": "******************************************", "withdrawPrecision": 6, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "arbitrum"}]}, "id": "USDT", "numericId": null, "code": "USDT", "precision": 1e-06, "type": "crypto", "name": "<PERSON><PERSON>", "active": true, "deposit": true, "withdraw": true, "fee": 0.5, "fees": {}, "networks": {"APT": {"info": {"chainName": "APT", "withdrawalMinSize": "1", "depositMinSize": null, "withdrawFeeRate": "0", "withdrawalMinFee": "0.5", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 1, "preConfirms": 1, "contractAddress": "0x357b0b74bc833e95a115ad22604854d6b0fca151cecd94111770e5d6ffc9dc2b", "withdrawPrecision": 6, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "aptos"}, "id": "aptos", "name": "APT", "code": "APT", "active": true, "fee": 0.5, "deposit": true, "withdraw": true, "precision": 1e-06, "limits": {"withdraw": {"min": 1, "max": null}, "deposit": {"min": null, "max": null}}}, "statemint": {"info": {"chainName": "<PERSON><PERSON>(Polkadot)", "withdrawalMinSize": "10", "depositMinSize": "0.7", "withdrawFeeRate": "0", "withdrawalMinFee": "1", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 30, "preConfirms": 30, "contractAddress": "1984", "withdrawPrecision": 6, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "statemint"}, "id": "statemint", "name": "<PERSON><PERSON>(Polkadot)", "code": "statemint", "active": true, "fee": 1, "deposit": true, "withdraw": true, "precision": 1e-06, "limits": {"withdraw": {"min": 10, "max": null}, "deposit": {"min": 0.7, "max": null}}}, "OP": {"info": {"chainName": "OPTIMISM", "withdrawalMinSize": "3", "depositMinSize": null, "withdrawFeeRate": "0", "withdrawalMinFee": "1", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 500, "preConfirms": 100, "contractAddress": "0x94b008aa00579c1307b0ef2c499ad98a8ce58e58", "withdrawPrecision": 6, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "optimism"}, "id": "optimism", "name": "OPTIMISM", "code": "OP", "active": true, "fee": 1, "deposit": true, "withdraw": true, "precision": 1e-06, "limits": {"withdraw": {"min": 3, "max": null}, "deposit": {"min": null, "max": null}}}, "KCC": {"info": {"chainName": "KCC", "withdrawalMinSize": "1", "depositMinSize": null, "withdrawFeeRate": "0", "withdrawalMinFee": "0.5", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 20, "preConfirms": 20, "contractAddress": "0x0039f574ee5cc39bdd162e9a88e3eb1f111baf48", "withdrawPrecision": 8, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "kcc"}, "id": "kcc", "name": "KCC", "code": "KCC", "active": true, "fee": 0.5, "deposit": true, "withdraw": true, "precision": 1e-08, "limits": {"withdraw": {"min": 1, "max": null}, "deposit": {"min": null, "max": null}}}, "SOL": {"info": {"chainName": "SOL", "withdrawalMinSize": "3", "depositMinSize": null, "withdrawFeeRate": "0", "withdrawalMinFee": "1.5", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 200, "preConfirms": 200, "contractAddress": "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB", "withdrawPrecision": 6, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "sol"}, "id": "sol", "name": "SOL", "code": "SOL", "active": true, "fee": 1.5, "deposit": true, "withdraw": true, "precision": 1e-06, "limits": {"withdraw": {"min": 3, "max": null}, "deposit": {"min": null, "max": null}}}, "TON": {"info": {"chainName": "TON", "withdrawalMinSize": "1", "depositMinSize": "0.1", "withdrawFeeRate": "0", "withdrawalMinFee": "0.5", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 1, "preConfirms": 1, "contractAddress": "EQCxE6mUtQJKFnGfaROTKOt1lZbDiiX1kCixRv7Nw2Id_sDs", "withdrawPrecision": 6, "maxWithdraw": null, "maxDeposit": null, "needTag": true, "chainId": "ton"}, "id": "ton", "name": "TON", "code": "TON", "active": true, "fee": 0.5, "deposit": true, "withdraw": true, "precision": 1e-06, "limits": {"withdraw": {"min": 1, "max": null}, "deposit": {"min": 0.1, "max": null}}}, "BEP20": {"info": {"chainName": "BEP20", "withdrawalMinSize": "10", "depositMinSize": "1", "withdrawFeeRate": "0", "withdrawalMinFee": "1", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 60, "preConfirms": 15, "contractAddress": "0x55d398326f99059ff775485246999027b3197955", "withdrawPrecision": 8, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "bsc"}, "id": "bsc", "name": "BEP20", "code": "BEP20", "active": true, "fee": 1, "deposit": true, "withdraw": true, "precision": 1e-08, "limits": {"withdraw": {"min": 10, "max": null}, "deposit": {"min": 1, "max": null}}}, "kavaevm": {"info": {"chainName": "KAVA EVM Co-Chain", "withdrawalMinSize": "3", "depositMinSize": null, "withdrawFeeRate": "0", "withdrawalMinFee": "1", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 20, "preConfirms": 20, "contractAddress": "0x919c1c267bc06a7039e03fcc2ef738525769109c", "withdrawPrecision": 6, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "<PERSON><PERSON><PERSON><PERSON>"}, "id": "<PERSON><PERSON><PERSON><PERSON>", "name": "KAVA EVM Co-Chain", "code": "<PERSON><PERSON><PERSON><PERSON>", "active": true, "fee": 1, "deposit": true, "withdraw": true, "precision": 1e-06, "limits": {"withdraw": {"min": 3, "max": null}, "deposit": {"min": null, "max": null}}}, "MATIC": {"info": {"chainName": "Polygon POS", "withdrawalMinSize": "35", "depositMinSize": null, "withdrawFeeRate": "0", "withdrawalMinFee": "35", "isWithdrawEnabled": false, "isDepositEnabled": true, "confirms": 800, "preConfirms": 300, "contractAddress": "0xc2132d05d31c914a87c6611c10748aeb04b58e8f", "withdrawPrecision": 6, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "matic"}, "id": "matic", "name": "Polygon POS", "code": "MATIC", "active": false, "fee": 35, "deposit": true, "withdraw": false, "precision": 1e-06, "limits": {"withdraw": {"min": 35, "max": null}, "deposit": {"min": null, "max": null}}}, "ERC20": {"info": {"chainName": "ERC20", "withdrawalMinSize": "30", "depositMinSize": "7.5", "withdrawFeeRate": "0.002", "withdrawMaxFee": "36", "withdrawalMinFee": "5.5", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 96, "preConfirms": 32, "contractAddress": "******************************************", "withdrawPrecision": 6, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "eth"}, "id": "eth", "name": "ERC20", "code": "ERC20", "active": true, "fee": 5.5, "deposit": true, "withdraw": true, "precision": 1e-06, "limits": {"withdraw": {"min": 30, "max": null}, "deposit": {"min": 7.5, "max": null}}}, "AVAXC": {"info": {"chainName": "AVAX C-Chain", "withdrawalMinSize": "2", "depositMinSize": null, "withdrawFeeRate": "0", "withdrawalMinFee": "1", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 32, "preConfirms": 32, "contractAddress": "******************************************", "withdrawPrecision": 6, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "avaxc"}, "id": "avaxc", "name": "AVAX C-Chain", "code": "AVAXC", "active": true, "fee": 1, "deposit": true, "withdraw": true, "precision": 1e-06, "limits": {"withdraw": {"min": 2, "max": null}, "deposit": {"min": null, "max": null}}}, "NEAR": {"info": {"chainName": "NEAR", "withdrawalMinSize": "3", "depositMinSize": null, "withdrawFeeRate": "0", "withdrawalMinFee": "0.5", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 5, "preConfirms": 5, "contractAddress": "usdt.tether-token.near", "withdrawPrecision": 6, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "near"}, "id": "near", "name": "NEAR", "code": "NEAR", "active": true, "fee": 0.5, "deposit": true, "withdraw": true, "precision": 1e-06, "limits": {"withdraw": {"min": 3, "max": null}, "deposit": {"min": null, "max": null}}}, "TRC20": {"info": {"chainName": "TRC20", "withdrawalMinSize": "3", "depositMinSize": "1", "withdrawFeeRate": "0", "withdrawalMinFee": "1.5", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 3, "preConfirms": 3, "contractAddress": "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t", "withdrawPrecision": 6, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "trx"}, "id": "trx", "name": "TRC20", "code": "TRC20", "active": true, "fee": 1.5, "deposit": true, "withdraw": true, "precision": 1e-06, "limits": {"withdraw": {"min": 3, "max": null}, "deposit": {"min": 1, "max": null}}}, "XTZ": {"info": {"chainName": "XTZ", "withdrawalMinSize": "2", "depositMinSize": null, "withdrawFeeRate": "0", "withdrawalMinFee": "1", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 10, "preConfirms": 10, "contractAddress": "KT1XnTn74bUtxHfDtBmm2bGZAQfhPbvKWR8o", "withdrawPrecision": 6, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "xtz"}, "id": "xtz", "name": "XTZ", "code": "XTZ", "active": true, "fee": 1, "deposit": true, "withdraw": true, "precision": 1e-06, "limits": {"withdraw": {"min": 2, "max": null}, "deposit": {"min": null, "max": null}}}, "ARBONE": {"info": {"chainName": "ARBITRUM", "withdrawalMinSize": "5", "depositMinSize": null, "withdrawFeeRate": "0", "withdrawalMinFee": "1", "isWithdrawEnabled": false, "isDepositEnabled": true, "confirms": 1900, "preConfirms": 120, "contractAddress": "******************************************", "withdrawPrecision": 6, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "arbitrum"}, "id": "arbitrum", "name": "ARBITRUM", "code": "ARBONE", "active": false, "fee": 1, "deposit": true, "withdraw": false, "precision": 1e-06, "limits": {"withdraw": {"min": 5, "max": null}, "deposit": {"min": null, "max": null}}}}, "limits": {"deposit": {"min": 0.1, "max": null}, "withdraw": {"min": 1, "max": null}}}, "INR": {"info": {"currency": "INR", "name": "INR", "fullName": "Indian Rupee", "precision": 2, "confirms": null, "contractAddress": null, "isMarginEnabled": false, "isDebitEnabled": false, "chains": null}, "id": "INR", "numericId": null, "code": "INR", "precision": 0.01, "type": "fiat", "name": "Indian Rupee", "active": null, "deposit": null, "withdraw": null, "fee": null, "fees": {}, "networks": {}, "limits": null}, "USD": {"info": {"currency": "USD", "name": "USD", "fullName": "US Dollar", "precision": 2, "confirms": null, "contractAddress": null, "isMarginEnabled": false, "isDebitEnabled": false, "chains": null}, "id": "USD", "numericId": null, "code": "USD", "precision": 0.01, "type": "fiat", "name": "US Dollar", "active": null, "deposit": null, "withdraw": null, "fee": null, "fees": {}, "networks": {}, "limits": null}, "KRW": {"info": {"currency": "KRW", "name": "KRW", "fullName": "Won", "precision": 2, "confirms": null, "contractAddress": null, "isMarginEnabled": false, "isDebitEnabled": false, "chains": null}, "id": "KRW", "numericId": null, "code": "KRW", "precision": 0.01, "type": "fiat", "name": "Won", "active": null, "deposit": null, "withdraw": null, "fee": null, "fees": {}, "networks": {}, "limits": null}, "LTC": {"info": {"currency": "LTC", "name": "LTC", "fullName": "Litecoin", "precision": 8, "confirms": null, "contractAddress": null, "isMarginEnabled": true, "isDebitEnabled": true, "chains": [{"chainName": "LTC", "withdrawalMinSize": "0.0138", "depositMinSize": "0.00000546", "withdrawFeeRate": "0", "withdrawalMinFee": "0.0069", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 6, "preConfirms": 3, "contractAddress": "", "withdrawPrecision": 8, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "ltc"}]}, "id": "LTC", "numericId": null, "code": "LTC", "precision": 1e-08, "type": "crypto", "name": "Litecoin", "active": true, "deposit": true, "withdraw": true, "fee": 0.0069, "fees": {}, "networks": {"LTC": {"info": {"chainName": "LTC", "withdrawalMinSize": "0.0138", "depositMinSize": "0.00000546", "withdrawFeeRate": "0", "withdrawalMinFee": "0.0069", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 6, "preConfirms": 3, "contractAddress": "", "withdrawPrecision": 8, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "ltc"}, "id": "ltc", "name": "LTC", "code": "LTC", "active": true, "fee": 0.0069, "deposit": true, "withdraw": true, "precision": 1e-08, "limits": {"withdraw": {"min": 0.0138, "max": null}, "deposit": {"min": 5.46e-06, "max": null}}}}, "limits": {"deposit": {"min": 5.46e-06, "max": null}, "withdraw": {"min": 0.0138, "max": null}}}, "ETH": {"info": {"currency": "ETH", "name": "ETH", "fullName": "Ethereum", "precision": 8, "confirms": null, "contractAddress": null, "isMarginEnabled": true, "isDebitEnabled": true, "chains": [{"chainName": "BEP20", "withdrawalMinSize": "0.00048", "depositMinSize": null, "withdrawFeeRate": "0", "withdrawalMinFee": "0.00024", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 60, "preConfirms": 15, "contractAddress": "******************************************", "withdrawPrecision": 8, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "bsc"}, {"chainName": "OPTIMISM", "withdrawalMinSize": "0.01", "depositMinSize": null, "withdrawFeeRate": "0", "withdrawalMinFee": "0.0002", "isWithdrawEnabled": true, "isDepositEnabled": false, "confirms": 500, "preConfirms": 100, "contractAddress": "", "withdrawPrecision": 8, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "optimism"}, {"chainName": "ERC20", "withdrawalMinSize": "0.0054", "depositMinSize": "0.0021", "withdrawFeeRate": "0", "withdrawalMinFee": "0.0027", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 96, "preConfirms": 32, "contractAddress": "", "withdrawPrecision": 8, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "eth"}, {"chainName": "HRC20", "withdrawalMinSize": "0.01", "depositMinSize": null, "withdrawFeeRate": "0", "withdrawalMinFee": "0.0005", "isWithdrawEnabled": false, "isDepositEnabled": false, "confirms": 32, "preConfirms": 32, "contractAddress": "", "withdrawPrecision": 8, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "heco"}, {"chainName": "KCC", "withdrawalMinSize": "0.0006", "depositMinSize": null, "withdrawFeeRate": "0", "withdrawalMinFee": "0.0003", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 20, "preConfirms": 20, "contractAddress": "******************************************", "withdrawPrecision": 8, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "kcc"}, {"chainName": "TRC20", "withdrawalMinSize": "0.002", "depositMinSize": "0.00001", "withdrawFeeRate": "0", "withdrawalMinFee": "0.0005", "isWithdrawEnabled": false, "isDepositEnabled": false, "confirms": 3, "preConfirms": 3, "contractAddress": "THb4CqiFdwNHsWsQCs4JhzwjMWys4aqCbF", "withdrawPrecision": 8, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "trx"}, {"chainName": "ARBITRUM", "withdrawalMinSize": "0.0004", "depositMinSize": null, "withdrawFeeRate": "0", "withdrawalMinFee": "0.0002", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 1900, "preConfirms": 120, "contractAddress": "", "withdrawPrecision": 8, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "arbitrum"}]}, "id": "ETH", "numericId": null, "code": "ETH", "precision": 1e-08, "type": "crypto", "name": "Ethereum", "active": true, "deposit": true, "withdraw": true, "fee": 0.0002, "fees": {}, "networks": {"BEP20": {"info": {"chainName": "BEP20", "withdrawalMinSize": "0.00048", "depositMinSize": null, "withdrawFeeRate": "0", "withdrawalMinFee": "0.00024", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 60, "preConfirms": 15, "contractAddress": "******************************************", "withdrawPrecision": 8, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "bsc"}, "id": "bsc", "name": "BEP20", "code": "BEP20", "active": true, "fee": 0.00024, "deposit": true, "withdraw": true, "precision": 1e-08, "limits": {"withdraw": {"min": 0.00048, "max": null}, "deposit": {"min": null, "max": null}}}, "OP": {"info": {"chainName": "OPTIMISM", "withdrawalMinSize": "0.01", "depositMinSize": null, "withdrawFeeRate": "0", "withdrawalMinFee": "0.0002", "isWithdrawEnabled": true, "isDepositEnabled": false, "confirms": 500, "preConfirms": 100, "contractAddress": "", "withdrawPrecision": 8, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "optimism"}, "id": "optimism", "name": "OPTIMISM", "code": "OP", "active": false, "fee": 0.0002, "deposit": false, "withdraw": true, "precision": 1e-08, "limits": {"withdraw": {"min": 0.01, "max": null}, "deposit": {"min": null, "max": null}}}, "ETH": {"info": {"chainName": "ERC20", "withdrawalMinSize": "0.0054", "depositMinSize": "0.0021", "withdrawFeeRate": "0", "withdrawalMinFee": "0.0027", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 96, "preConfirms": 32, "contractAddress": "", "withdrawPrecision": 8, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "eth"}, "id": "eth", "name": "ERC20", "code": "ETH", "active": true, "fee": 0.0027, "deposit": true, "withdraw": true, "precision": 1e-08, "limits": {"withdraw": {"min": 0.0054, "max": null}, "deposit": {"min": 0.0021, "max": null}}}, "HRC20": {"info": {"chainName": "HRC20", "withdrawalMinSize": "0.01", "depositMinSize": null, "withdrawFeeRate": "0", "withdrawalMinFee": "0.0005", "isWithdrawEnabled": false, "isDepositEnabled": false, "confirms": 32, "preConfirms": 32, "contractAddress": "", "withdrawPrecision": 8, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "heco"}, "id": "heco", "name": "HRC20", "code": "HRC20", "active": false, "fee": 0.0005, "deposit": false, "withdraw": false, "precision": 1e-08, "limits": {"withdraw": {"min": 0.01, "max": null}, "deposit": {"min": null, "max": null}}}, "KCC": {"info": {"chainName": "KCC", "withdrawalMinSize": "0.0006", "depositMinSize": null, "withdrawFeeRate": "0", "withdrawalMinFee": "0.0003", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 20, "preConfirms": 20, "contractAddress": "******************************************", "withdrawPrecision": 8, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "kcc"}, "id": "kcc", "name": "KCC", "code": "KCC", "active": true, "fee": 0.0003, "deposit": true, "withdraw": true, "precision": 1e-08, "limits": {"withdraw": {"min": 0.0006, "max": null}, "deposit": {"min": null, "max": null}}}, "TRC20": {"info": {"chainName": "TRC20", "withdrawalMinSize": "0.002", "depositMinSize": "0.00001", "withdrawFeeRate": "0", "withdrawalMinFee": "0.0005", "isWithdrawEnabled": false, "isDepositEnabled": false, "confirms": 3, "preConfirms": 3, "contractAddress": "THb4CqiFdwNHsWsQCs4JhzwjMWys4aqCbF", "withdrawPrecision": 8, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "trx"}, "id": "trx", "name": "TRC20", "code": "TRC20", "active": false, "fee": 0.0005, "deposit": false, "withdraw": false, "precision": 1e-08, "limits": {"withdraw": {"min": 0.002, "max": null}, "deposit": {"min": 1e-05, "max": null}}}, "ARBONE": {"info": {"chainName": "ARBITRUM", "withdrawalMinSize": "0.0004", "depositMinSize": null, "withdrawFeeRate": "0", "withdrawalMinFee": "0.0002", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 1900, "preConfirms": 120, "contractAddress": "", "withdrawPrecision": 8, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "arbitrum"}, "id": "arbitrum", "name": "ARBITRUM", "code": "ARBONE", "active": true, "fee": 0.0002, "deposit": true, "withdraw": true, "precision": 1e-08, "limits": {"withdraw": {"min": 0.0004, "max": null}, "deposit": {"min": null, "max": null}}}}, "limits": {"deposit": {"min": 1e-05, "max": null}, "withdraw": {"min": 0.0004, "max": null}}}, "ADA": {"info": {"currency": "ADA", "name": "ADA", "fullName": "Cardano", "precision": 8, "confirms": null, "contractAddress": null, "isMarginEnabled": true, "isDebitEnabled": true, "chains": [{"chainName": "ADA", "withdrawalMinSize": "3.8", "depositMinSize": "1", "withdrawFeeRate": "0", "withdrawalMinFee": "1.8", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 30, "preConfirms": 30, "contractAddress": "", "withdrawPrecision": 6, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "ada"}]}, "id": "ADA", "numericId": null, "code": "ADA", "precision": 1e-06, "type": "crypto", "name": "Cardano", "active": true, "deposit": true, "withdraw": true, "fee": 1.8, "fees": {}, "networks": {"ADA": {"info": {"chainName": "ADA", "withdrawalMinSize": "3.8", "depositMinSize": "1", "withdrawFeeRate": "0", "withdrawalMinFee": "1.8", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 30, "preConfirms": 30, "contractAddress": "", "withdrawPrecision": 6, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "ada"}, "id": "ada", "name": "ADA", "code": "ADA", "active": true, "fee": 1.8, "deposit": true, "withdraw": true, "precision": 1e-06, "limits": {"withdraw": {"min": 3.8, "max": null}, "deposit": {"min": 1, "max": null}}}}, "limits": {"deposit": {"min": 1, "max": null}, "withdraw": {"min": 3.8, "max": null}}}, "XRP": {"info": {"currency": "XRP", "name": "XRP", "fullName": "XRP", "precision": 8, "confirms": null, "contractAddress": null, "isMarginEnabled": true, "isDebitEnabled": true, "chains": [{"chainName": "XRP", "withdrawalMinSize": "14", "depositMinSize": "0.1", "withdrawFeeRate": "0", "withdrawalMinFee": "0.3", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 12, "preConfirms": 12, "contractAddress": "", "withdrawPrecision": 6, "maxWithdraw": null, "maxDeposit": null, "needTag": true, "chainId": "xrp"}]}, "id": "XRP", "numericId": null, "code": "XRP", "precision": 1e-06, "type": "crypto", "name": "XRP", "active": true, "deposit": true, "withdraw": true, "fee": 0.3, "fees": {}, "networks": {"XRP": {"info": {"chainName": "XRP", "withdrawalMinSize": "14", "depositMinSize": "0.1", "withdrawFeeRate": "0", "withdrawalMinFee": "0.3", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 12, "preConfirms": 12, "contractAddress": "", "withdrawPrecision": 6, "maxWithdraw": null, "maxDeposit": null, "needTag": true, "chainId": "xrp"}, "id": "xrp", "name": "XRP", "code": "XRP", "active": true, "fee": 0.3, "deposit": true, "withdraw": true, "precision": 1e-06, "limits": {"withdraw": {"min": 14, "max": null}, "deposit": {"min": 0.1, "max": null}}}}, "limits": {"deposit": {"min": 0.1, "max": null}, "withdraw": {"min": 14, "max": null}}}, "USDC": {"info": {"currency": "USDC", "name": "USDC", "fullName": "USD Coin", "precision": 8, "confirms": null, "contractAddress": null, "isMarginEnabled": true, "isDebitEnabled": true, "chains": [{"chainName": "Sonic", "withdrawalMinSize": "70", "depositMinSize": null, "withdrawFeeRate": "0", "withdrawalMinFee": "35", "isWithdrawEnabled": false, "isDepositEnabled": true, "confirms": 10, "preConfirms": 10, "contractAddress": "******************************************", "withdrawPrecision": 6, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "sonic"}, {"chainName": "<PERSON><PERSON>(Polkadot)", "withdrawalMinSize": "2", "depositMinSize": "0.01", "withdrawFeeRate": "0", "withdrawalMinFee": "1", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 30, "preConfirms": 30, "contractAddress": "1337", "withdrawPrecision": 6, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "statemint"}, {"chainName": "OPTIMISM", "withdrawalMinSize": "3", "depositMinSize": "0.1", "withdrawFeeRate": "0", "withdrawalMinFee": "1", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 500, "preConfirms": 100, "contractAddress": "0x0b2c639c533813f4aa9d7837caf62653d097ff85", "withdrawPrecision": 6, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "optimism"}, {"chainName": "SUI", "withdrawalMinSize": "1", "depositMinSize": null, "withdrawFeeRate": "0", "withdrawalMinFee": "0.5", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 1, "preConfirms": 1, "contractAddress": "0xdba34672e30cb065b1f93e3ab55318768fd6fef66c15942c9f7cb846e2f900e7::usdc::USDC", "withdrawPrecision": 6, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "sui"}, {"chainName": "KCC", "withdrawalMinSize": "2", "depositMinSize": null, "withdrawFeeRate": "0", "withdrawalMinFee": "0.5", "isWithdrawEnabled": false, "isDepositEnabled": false, "confirms": 20, "preConfirms": 20, "contractAddress": "0x980a5afef3d17ad98635f6c5aebcbaeded3c3430", "withdrawPrecision": 8, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "kcc"}, {"chainName": "SOL", "withdrawalMinSize": "3", "depositMinSize": "0.1", "withdrawFeeRate": "0", "withdrawalMinFee": "1.5", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 200, "preConfirms": 200, "contractAddress": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v", "withdrawPrecision": 6, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "sol"}, {"chainName": "<PERSON>", "withdrawalMinSize": "2", "depositMinSize": "0", "withdrawFeeRate": "0", "withdrawalMinFee": "1", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 12, "preConfirms": 0, "contractAddress": "uusdc", "withdrawPrecision": 6, "maxWithdraw": null, "maxDeposit": null, "needTag": true, "chainId": "noble"}, {"chainName": "ERC20", "withdrawalMinSize": "30", "depositMinSize": "7.5", "withdrawFeeRate": "0.002", "withdrawMaxFee": "36", "withdrawalMinFee": "5.5", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 96, "preConfirms": 32, "contractAddress": "******************************************", "withdrawPrecision": 6, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "eth"}, {"chainName": "AVAX C-Chain", "withdrawalMinSize": "2", "depositMinSize": null, "withdrawFeeRate": "0", "withdrawalMinFee": "1", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 32, "preConfirms": 32, "contractAddress": "******************************************", "withdrawPrecision": 6, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "avaxc"}, {"chainName": "NEAR", "withdrawalMinSize": "1", "depositMinSize": null, "withdrawFeeRate": "0", "withdrawalMinFee": "0.5", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 5, "preConfirms": 5, "contractAddress": "17208628f84f5d6ad33f0da3bbbeb27ffcb398eac501a31bd6ad2011e36133a1", "withdrawPrecision": 6, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "near"}, {"chainName": "ALGO", "withdrawalMinSize": "2", "depositMinSize": null, "withdrawFeeRate": "0", "withdrawalMinFee": "1", "isWithdrawEnabled": false, "isDepositEnabled": false, "confirms": 8, "preConfirms": 8, "contractAddress": "31566704", "withdrawPrecision": 6, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "algo"}, {"chainName": "Base", "withdrawalMinSize": "1", "depositMinSize": "0", "withdrawFeeRate": "0", "withdrawalMinFee": "0.5", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 240, "preConfirms": 100, "contractAddress": "0x833589fcd6edb6e08f4c7c32d4f71b54bda02913", "withdrawPrecision": 6, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "base"}, {"chainName": "ARBITRUM", "withdrawalMinSize": "5", "depositMinSize": "0.05", "withdrawFeeRate": "0", "withdrawalMinFee": "1", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 1900, "preConfirms": 120, "contractAddress": "******************************************", "withdrawPrecision": 6, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "arbitrum"}]}, "id": "USDC", "numericId": null, "code": "USDC", "precision": 1e-06, "type": "crypto", "name": "USD Coin", "active": true, "deposit": true, "withdraw": true, "fee": 0.5, "fees": {}, "networks": {"sonic": {"info": {"chainName": "Sonic", "withdrawalMinSize": "70", "depositMinSize": null, "withdrawFeeRate": "0", "withdrawalMinFee": "35", "isWithdrawEnabled": false, "isDepositEnabled": true, "confirms": 10, "preConfirms": 10, "contractAddress": "******************************************", "withdrawPrecision": 6, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "sonic"}, "id": "sonic", "name": "Sonic", "code": "sonic", "active": false, "fee": 35, "deposit": true, "withdraw": false, "precision": 1e-06, "limits": {"withdraw": {"min": 70, "max": null}, "deposit": {"min": null, "max": null}}}, "statemint": {"info": {"chainName": "<PERSON><PERSON>(Polkadot)", "withdrawalMinSize": "2", "depositMinSize": "0.01", "withdrawFeeRate": "0", "withdrawalMinFee": "1", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 30, "preConfirms": 30, "contractAddress": "1337", "withdrawPrecision": 6, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "statemint"}, "id": "statemint", "name": "<PERSON><PERSON>(Polkadot)", "code": "statemint", "active": true, "fee": 1, "deposit": true, "withdraw": true, "precision": 1e-06, "limits": {"withdraw": {"min": 2, "max": null}, "deposit": {"min": 0.01, "max": null}}}, "OP": {"info": {"chainName": "OPTIMISM", "withdrawalMinSize": "3", "depositMinSize": "0.1", "withdrawFeeRate": "0", "withdrawalMinFee": "1", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 500, "preConfirms": 100, "contractAddress": "0x0b2c639c533813f4aa9d7837caf62653d097ff85", "withdrawPrecision": 6, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "optimism"}, "id": "optimism", "name": "OPTIMISM", "code": "OP", "active": true, "fee": 1, "deposit": true, "withdraw": true, "precision": 1e-06, "limits": {"withdraw": {"min": 3, "max": null}, "deposit": {"min": 0.1, "max": null}}}, "sui": {"info": {"chainName": "SUI", "withdrawalMinSize": "1", "depositMinSize": null, "withdrawFeeRate": "0", "withdrawalMinFee": "0.5", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 1, "preConfirms": 1, "contractAddress": "0xdba34672e30cb065b1f93e3ab55318768fd6fef66c15942c9f7cb846e2f900e7::usdc::USDC", "withdrawPrecision": 6, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "sui"}, "id": "sui", "name": "SUI", "code": "sui", "active": true, "fee": 0.5, "deposit": true, "withdraw": true, "precision": 1e-06, "limits": {"withdraw": {"min": 1, "max": null}, "deposit": {"min": null, "max": null}}}, "KCC": {"info": {"chainName": "KCC", "withdrawalMinSize": "2", "depositMinSize": null, "withdrawFeeRate": "0", "withdrawalMinFee": "0.5", "isWithdrawEnabled": false, "isDepositEnabled": false, "confirms": 20, "preConfirms": 20, "contractAddress": "0x980a5afef3d17ad98635f6c5aebcbaeded3c3430", "withdrawPrecision": 8, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "kcc"}, "id": "kcc", "name": "KCC", "code": "KCC", "active": false, "fee": 0.5, "deposit": false, "withdraw": false, "precision": 1e-08, "limits": {"withdraw": {"min": 2, "max": null}, "deposit": {"min": null, "max": null}}}, "SOL": {"info": {"chainName": "SOL", "withdrawalMinSize": "3", "depositMinSize": "0.1", "withdrawFeeRate": "0", "withdrawalMinFee": "1.5", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 200, "preConfirms": 200, "contractAddress": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v", "withdrawPrecision": 6, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "sol"}, "id": "sol", "name": "SOL", "code": "SOL", "active": true, "fee": 1.5, "deposit": true, "withdraw": true, "precision": 1e-06, "limits": {"withdraw": {"min": 3, "max": null}, "deposit": {"min": 0.1, "max": null}}}, "noble": {"info": {"chainName": "<PERSON>", "withdrawalMinSize": "2", "depositMinSize": "0", "withdrawFeeRate": "0", "withdrawalMinFee": "1", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 12, "preConfirms": 0, "contractAddress": "uusdc", "withdrawPrecision": 6, "maxWithdraw": null, "maxDeposit": null, "needTag": true, "chainId": "noble"}, "id": "noble", "name": "<PERSON>", "code": "noble", "active": true, "fee": 1, "deposit": true, "withdraw": true, "precision": 1e-06, "limits": {"withdraw": {"min": 2, "max": null}, "deposit": {"min": 0, "max": null}}}, "ERC20": {"info": {"chainName": "ERC20", "withdrawalMinSize": "30", "depositMinSize": "7.5", "withdrawFeeRate": "0.002", "withdrawMaxFee": "36", "withdrawalMinFee": "5.5", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 96, "preConfirms": 32, "contractAddress": "******************************************", "withdrawPrecision": 6, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "eth"}, "id": "eth", "name": "ERC20", "code": "ERC20", "active": true, "fee": 5.5, "deposit": true, "withdraw": true, "precision": 1e-06, "limits": {"withdraw": {"min": 30, "max": null}, "deposit": {"min": 7.5, "max": null}}}, "AVAXC": {"info": {"chainName": "AVAX C-Chain", "withdrawalMinSize": "2", "depositMinSize": null, "withdrawFeeRate": "0", "withdrawalMinFee": "1", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 32, "preConfirms": 32, "contractAddress": "******************************************", "withdrawPrecision": 6, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "avaxc"}, "id": "avaxc", "name": "AVAX C-Chain", "code": "AVAXC", "active": true, "fee": 1, "deposit": true, "withdraw": true, "precision": 1e-06, "limits": {"withdraw": {"min": 2, "max": null}, "deposit": {"min": null, "max": null}}}, "NEAR": {"info": {"chainName": "NEAR", "withdrawalMinSize": "1", "depositMinSize": null, "withdrawFeeRate": "0", "withdrawalMinFee": "0.5", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 5, "preConfirms": 5, "contractAddress": "17208628f84f5d6ad33f0da3bbbeb27ffcb398eac501a31bd6ad2011e36133a1", "withdrawPrecision": 6, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "near"}, "id": "near", "name": "NEAR", "code": "NEAR", "active": true, "fee": 0.5, "deposit": true, "withdraw": true, "precision": 1e-06, "limits": {"withdraw": {"min": 1, "max": null}, "deposit": {"min": null, "max": null}}}, "ALGO": {"info": {"chainName": "ALGO", "withdrawalMinSize": "2", "depositMinSize": null, "withdrawFeeRate": "0", "withdrawalMinFee": "1", "isWithdrawEnabled": false, "isDepositEnabled": false, "confirms": 8, "preConfirms": 8, "contractAddress": "31566704", "withdrawPrecision": 6, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "algo"}, "id": "algo", "name": "ALGO", "code": "ALGO", "active": false, "fee": 1, "deposit": false, "withdraw": false, "precision": 1e-06, "limits": {"withdraw": {"min": 2, "max": null}, "deposit": {"min": null, "max": null}}}, "BASE": {"info": {"chainName": "Base", "withdrawalMinSize": "1", "depositMinSize": "0", "withdrawFeeRate": "0", "withdrawalMinFee": "0.5", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 240, "preConfirms": 100, "contractAddress": "0x833589fcd6edb6e08f4c7c32d4f71b54bda02913", "withdrawPrecision": 6, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "base"}, "id": "base", "name": "Base", "code": "BASE", "active": true, "fee": 0.5, "deposit": true, "withdraw": true, "precision": 1e-06, "limits": {"withdraw": {"min": 1, "max": null}, "deposit": {"min": 0, "max": null}}}, "ARBONE": {"info": {"chainName": "ARBITRUM", "withdrawalMinSize": "5", "depositMinSize": "0.05", "withdrawFeeRate": "0", "withdrawalMinFee": "1", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 1900, "preConfirms": 120, "contractAddress": "******************************************", "withdrawPrecision": 6, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "arbitrum"}, "id": "arbitrum", "name": "ARBITRUM", "code": "ARBONE", "active": true, "fee": 1, "deposit": true, "withdraw": true, "precision": 1e-06, "limits": {"withdraw": {"min": 5, "max": null}, "deposit": {"min": 0.05, "max": null}}}}, "limits": {"deposit": {"min": 0, "max": null}, "withdraw": {"min": 1, "max": null}}}, "ALT": {"info": {"currency": "KALT", "name": "kALT", "fullName": "Altlayer", "precision": 8, "confirms": null, "contractAddress": null, "isMarginEnabled": false, "isDebitEnabled": true, "chains": [{"chainName": "BEP20", "withdrawalMinSize": "40", "depositMinSize": null, "withdrawFeeRate": "0", "withdrawalMinFee": "20", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 60, "preConfirms": 15, "contractAddress": "******************************************", "withdrawPrecision": 8, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "bsc"}, {"chainName": "ERC20", "withdrawalMinSize": "312", "depositMinSize": "53", "withdrawFeeRate": "0", "withdrawalMinFee": "156", "isWithdrawEnabled": false, "isDepositEnabled": true, "confirms": 96, "preConfirms": 32, "contractAddress": "******************************************", "withdrawPrecision": 8, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "eth"}]}, "id": "KALT", "numericId": null, "code": "ALT", "precision": 1e-08, "type": "crypto", "name": "Altlayer", "active": true, "deposit": true, "withdraw": true, "fee": 20, "fees": {}, "networks": {"BEP20": {"info": {"chainName": "BEP20", "withdrawalMinSize": "40", "depositMinSize": null, "withdrawFeeRate": "0", "withdrawalMinFee": "20", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 60, "preConfirms": 15, "contractAddress": "******************************************", "withdrawPrecision": 8, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "bsc"}, "id": "bsc", "name": "BEP20", "code": "BEP20", "active": true, "fee": 20, "deposit": true, "withdraw": true, "precision": 1e-08, "limits": {"withdraw": {"min": 40, "max": null}, "deposit": {"min": null, "max": null}}}, "ERC20": {"info": {"chainName": "ERC20", "withdrawalMinSize": "312", "depositMinSize": "53", "withdrawFeeRate": "0", "withdrawalMinFee": "156", "isWithdrawEnabled": false, "isDepositEnabled": true, "confirms": 96, "preConfirms": 32, "contractAddress": "******************************************", "withdrawPrecision": 8, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "eth"}, "id": "eth", "name": "ERC20", "code": "ERC20", "active": false, "fee": 156, "deposit": true, "withdraw": false, "precision": 1e-08, "limits": {"withdraw": {"min": 312, "max": null}, "deposit": {"min": 53, "max": null}}}}, "limits": {"deposit": {"min": 53, "max": null}, "withdraw": {"min": 40, "max": null}}}, "BNB": {"info": {"currency": "BNB", "name": "BNB", "fullName": "Binance Chain Native Token", "precision": 8, "confirms": null, "contractAddress": null, "isMarginEnabled": true, "isDebitEnabled": true, "chains": [{"chainName": "BEP20", "withdrawalMinSize": "0.001906", "depositMinSize": "0.002", "withdrawFeeRate": "0", "withdrawalMinFee": "0.000953", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 60, "preConfirms": 15, "contractAddress": "", "withdrawPrecision": 8, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "bsc"}, {"chainName": "BEP2", "withdrawalMinSize": "0.01", "depositMinSize": "0.005", "withdrawFeeRate": "0", "withdrawalMinFee": "0.005", "isWithdrawEnabled": false, "isDepositEnabled": false, "confirms": 2, "preConfirms": 2, "contractAddress": "BNB", "withdrawPrecision": 8, "maxWithdraw": null, "maxDeposit": null, "needTag": true, "chainId": "bnb"}]}, "id": "BNB", "numericId": null, "code": "BNB", "precision": 1e-08, "type": "crypto", "name": "Binance Chain Native Token", "active": true, "deposit": true, "withdraw": true, "fee": 0.000953, "fees": {}, "networks": {"BEP20": {"info": {"chainName": "BEP20", "withdrawalMinSize": "0.001906", "depositMinSize": "0.002", "withdrawFeeRate": "0", "withdrawalMinFee": "0.000953", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 60, "preConfirms": 15, "contractAddress": "", "withdrawPrecision": 8, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "bsc"}, "id": "bsc", "name": "BEP20", "code": "BEP20", "active": true, "fee": 0.000953, "deposit": true, "withdraw": true, "precision": 1e-08, "limits": {"withdraw": {"min": 0.001906, "max": null}, "deposit": {"min": 0.002, "max": null}}}, "BEP2": {"info": {"chainName": "BEP2", "withdrawalMinSize": "0.01", "depositMinSize": "0.005", "withdrawFeeRate": "0", "withdrawalMinFee": "0.005", "isWithdrawEnabled": false, "isDepositEnabled": false, "confirms": 2, "preConfirms": 2, "contractAddress": "BNB", "withdrawPrecision": 8, "maxWithdraw": null, "maxDeposit": null, "needTag": true, "chainId": "bnb"}, "id": "bnb", "name": "BEP2", "code": "BEP2", "active": false, "fee": 0.005, "deposit": false, "withdraw": false, "precision": 1e-08, "limits": {"withdraw": {"min": 0.01, "max": null}, "deposit": {"min": 0.005, "max": null}}}}, "limits": {"deposit": {"min": 0.002, "max": null}, "withdraw": {"min": 0.001906, "max": null}}}}