{"BTC": {"info": {"coin": "BTC", "name": "BTC", "networkList": [{"name": "BTC", "network": "BTC", "isDefault": true, "minConfirm": "2", "withdrawEnable": true, "depositEnable": true, "withdrawFee": "0.000043", "withdrawMax": "42.3435423", "withdrawMin": "0.00004846", "depositMin": "0.0002", "withdrawPrecision": "8", "depositPrecision": "8", "contractAddress": "", "needTagOrMemo": "false", "displayName": "BTC"}, {"name": "BTC", "network": "BEP20", "isDefault": false, "minConfirm": "10", "withdrawEnable": true, "depositEnable": true, "withdrawFee": "0.000001", "withdrawMax": "42.3435423", "withdrawMin": "0.000043", "depositMin": "0.0002", "withdrawPrecision": "8", "depositPrecision": "18", "contractAddress": "******************************************", "needTagOrMemo": "false", "displayName": "BTCBEP20"}]}, "id": "BTC", "numericId": null, "code": "BTC", "precision": 1e-08, "type": "crypto", "name": "BTC", "active": true, "deposit": true, "withdraw": true, "fee": 1e-06, "fees": {}, "networks": {"BTC": {"info": {"name": "BTC", "network": "BTC", "isDefault": true, "minConfirm": "2", "withdrawEnable": true, "depositEnable": true, "withdrawFee": "0.000043", "withdrawMax": "42.3435423", "withdrawMin": "0.00004846", "depositMin": "0.0002", "withdrawPrecision": "8", "depositPrecision": "8", "contractAddress": "", "needTagOrMemo": "false", "displayName": "BTC"}, "id": "BTC", "network": "BTC", "fee": 4.3e-05, "active": true, "deposit": true, "withdraw": true, "precision": 1e-08, "limits": {"withdraw": {"min": 4.846e-05, "max": 42.3435423}, "deposit": {"min": 0.0002, "max": null}}}, "BEP20": {"info": {"name": "BTC", "network": "BEP20", "isDefault": false, "minConfirm": "10", "withdrawEnable": true, "depositEnable": true, "withdrawFee": "0.000001", "withdrawMax": "42.3435423", "withdrawMin": "0.000043", "depositMin": "0.0002", "withdrawPrecision": "8", "depositPrecision": "18", "contractAddress": "******************************************", "needTagOrMemo": "false", "displayName": "BTCBEP20"}, "id": "BEP20", "network": "BEP20", "fee": 1e-06, "active": true, "deposit": true, "withdraw": true, "precision": 1e-08, "limits": {"withdraw": {"min": 4.3e-05, "max": 42.3435423}, "deposit": {"min": 0.0002, "max": null}}}}, "limits": {"deposit": {"min": 0.0002, "max": null}, "withdraw": {"min": 4.3e-05, "max": 42.3435423}}}, "USDT": {"info": {"coin": "USDT", "name": "USDT", "networkList": [{"name": "USDT", "network": "ERC20", "isDefault": false, "minConfirm": "12", "withdrawEnable": true, "depositEnable": true, "withdrawFee": "1.41", "withdrawMax": "5000000", "withdrawMin": "5", "depositMin": "1", "withdrawPrecision": "6", "depositPrecision": "6", "contractAddress": "******************************************", "needTagOrMemo": "false", "displayName": "USDT20"}, {"name": "USDT", "network": "TRC20", "isDefault": false, "minConfirm": "2", "withdrawEnable": true, "depositEnable": true, "withdrawFee": "1", "withdrawMax": "5000000", "withdrawMin": "8", "depositMin": "1", "withdrawPrecision": "6", "depositPrecision": "6", "contractAddress": "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t", "needTagOrMemo": "false", "displayName": "usdttrc20"}, {"name": "USDT", "network": "BEP20", "isDefault": false, "minConfirm": "10", "withdrawEnable": true, "depositEnable": true, "withdrawFee": "0", "withdrawMax": "5000000", "withdrawMin": "3", "depositMin": "0.8", "withdrawPrecision": "8", "depositPrecision": "18", "contractAddress": "0x55d398326f99059ff775485246999027b3197955", "needTagOrMemo": "false", "displayName": "USDTBEP20"}, {"name": "USDT", "network": "SOL", "isDefault": false, "minConfirm": "50", "withdrawEnable": true, "depositEnable": true, "withdrawFee": "1", "withdrawMax": "5000000", "withdrawMin": "1.00000000000000000001", "depositMin": "0.9", "withdrawPrecision": "6", "depositPrecision": "6", "contractAddress": "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB", "needTagOrMemo": "false", "displayName": "USDTSOL"}, {"name": "USDT", "network": "POLYGON", "isDefault": false, "minConfirm": "64", "withdrawEnable": true, "depositEnable": true, "withdrawFee": "0.8", "withdrawMax": "5000000", "withdrawMin": "10", "depositMin": "0.9", "withdrawPrecision": "6", "depositPrecision": "6", "contractAddress": "0xc2132d05d31c914a87c6611c10748aeb04b58e8f", "needTagOrMemo": "false", "displayName": "USDTPOLYGON"}, {"name": "USDT", "network": "ARBITRUM", "isDefault": false, "minConfirm": "30", "withdrawEnable": true, "depositEnable": true, "withdrawFee": "0.5", "withdrawMax": "5000000", "withdrawMin": "0.5", "depositMin": "5", "withdrawPrecision": "6", "depositPrecision": "6", "contractAddress": "0xfd086bc7cd5c481dcc9c85ebe478a1c0b69fcbb9", "needTagOrMemo": "false", "displayName": "USDTARBITRUM"}, {"name": "USDT", "network": "OPTIMISM", "isDefault": false, "minConfirm": "50", "withdrawEnable": false, "depositEnable": true, "withdrawFee": "1", "withdrawMax": "5000000", "withdrawMin": "1.1", "depositMin": "5", "withdrawPrecision": "6", "depositPrecision": "6", "contractAddress": "0x94b008aa00579c1307b0ef2c499ad98a8ce58e58", "needTagOrMemo": "false", "displayName": "USDTOPTIMISM"}, {"name": "USDT", "network": "TON", "isDefault": false, "minConfirm": "25", "withdrawEnable": true, "depositEnable": true, "withdrawFee": "0.15", "withdrawMax": "5000000", "withdrawMin": "5", "depositMin": "5", "withdrawPrecision": "6", "depositPrecision": "6", "contractAddress": "EQCxE6mUtQJKFnGfaROTKOt1lZbDiiX1kCixRv7Nw2Id_sDs", "needTagOrMemo": "false", "displayName": "USDTTON"}, {"name": "USDT", "network": "OPBNB", "isDefault": false, "minConfirm": "30", "withdrawEnable": false, "depositEnable": true, "withdrawFee": "5", "withdrawMax": "5000000", "withdrawMin": "5.5", "depositMin": "5", "withdrawPrecision": "8", "depositPrecision": "18", "contractAddress": "0x9e5aac1ba1a2e6aed6b32689dfcf62a509ca96f3", "needTagOrMemo": "false", "displayName": "USDTOPBNB"}, {"name": "USDT", "network": "APT", "isDefault": false, "minConfirm": "10", "withdrawEnable": true, "depositEnable": true, "withdrawFee": "0.03", "withdrawMax": "5000000", "withdrawMin": "5", "depositMin": "5", "withdrawPrecision": "6", "depositPrecision": "6", "contractAddress": "0x357b0b74bc833e95a115ad22604854d6b0fca151cecd94111770e5d6ffc9dc2b", "needTagOrMemo": "false", "displayName": "USDTAPT"}]}, "id": "USDT", "numericId": null, "code": "USDT", "precision": 1e-06, "type": "crypto", "name": "USDT", "active": true, "deposit": true, "withdraw": true, "fee": 0, "fees": {}, "networks": {"ERC20": {"info": {"name": "USDT", "network": "ERC20", "isDefault": false, "minConfirm": "12", "withdrawEnable": true, "depositEnable": true, "withdrawFee": "1.41", "withdrawMax": "5000000", "withdrawMin": "5", "depositMin": "1", "withdrawPrecision": "6", "depositPrecision": "6", "contractAddress": "******************************************", "needTagOrMemo": "false", "displayName": "USDT20"}, "id": "ERC20", "network": "ERC20", "fee": 1.41, "active": true, "deposit": true, "withdraw": true, "precision": 1e-06, "limits": {"withdraw": {"min": 5, "max": 5000000}, "deposit": {"min": 1, "max": null}}}, "TRC20": {"info": {"name": "USDT", "network": "TRC20", "isDefault": false, "minConfirm": "2", "withdrawEnable": true, "depositEnable": true, "withdrawFee": "1", "withdrawMax": "5000000", "withdrawMin": "8", "depositMin": "1", "withdrawPrecision": "6", "depositPrecision": "6", "contractAddress": "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t", "needTagOrMemo": "false", "displayName": "usdttrc20"}, "id": "TRC20", "network": "TRC20", "fee": 1, "active": true, "deposit": true, "withdraw": true, "precision": 1e-06, "limits": {"withdraw": {"min": 8, "max": 5000000}, "deposit": {"min": 1, "max": null}}}, "BEP20": {"info": {"name": "USDT", "network": "BEP20", "isDefault": false, "minConfirm": "10", "withdrawEnable": true, "depositEnable": true, "withdrawFee": "0", "withdrawMax": "5000000", "withdrawMin": "3", "depositMin": "0.8", "withdrawPrecision": "8", "depositPrecision": "18", "contractAddress": "0x55d398326f99059ff775485246999027b3197955", "needTagOrMemo": "false", "displayName": "USDTBEP20"}, "id": "BEP20", "network": "BEP20", "fee": 0, "active": true, "deposit": true, "withdraw": true, "precision": 1e-08, "limits": {"withdraw": {"min": 3, "max": 5000000}, "deposit": {"min": 0.8, "max": null}}}, "SOL": {"info": {"name": "USDT", "network": "SOL", "isDefault": false, "minConfirm": "50", "withdrawEnable": true, "depositEnable": true, "withdrawFee": "1", "withdrawMax": "5000000", "withdrawMin": "1.00000000000000000001", "depositMin": "0.9", "withdrawPrecision": "6", "depositPrecision": "6", "contractAddress": "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB", "needTagOrMemo": "false", "displayName": "USDTSOL"}, "id": "SOL", "network": "SOL", "fee": 1, "active": true, "deposit": true, "withdraw": true, "precision": 1e-06, "limits": {"withdraw": {"min": 1, "max": 5000000}, "deposit": {"min": 0.9, "max": null}}}, "MATIC": {"info": {"name": "USDT", "network": "POLYGON", "isDefault": false, "minConfirm": "64", "withdrawEnable": true, "depositEnable": true, "withdrawFee": "0.8", "withdrawMax": "5000000", "withdrawMin": "10", "depositMin": "0.9", "withdrawPrecision": "6", "depositPrecision": "6", "contractAddress": "0xc2132d05d31c914a87c6611c10748aeb04b58e8f", "needTagOrMemo": "false", "displayName": "USDTPOLYGON"}, "id": "POLYGON", "network": "MATIC", "fee": 0.8, "active": true, "deposit": true, "withdraw": true, "precision": 1e-06, "limits": {"withdraw": {"min": 10, "max": 5000000}, "deposit": {"min": 0.9, "max": null}}}, "ARB": {"info": {"name": "USDT", "network": "ARBITRUM", "isDefault": false, "minConfirm": "30", "withdrawEnable": true, "depositEnable": true, "withdrawFee": "0.5", "withdrawMax": "5000000", "withdrawMin": "0.5", "depositMin": "5", "withdrawPrecision": "6", "depositPrecision": "6", "contractAddress": "0xfd086bc7cd5c481dcc9c85ebe478a1c0b69fcbb9", "needTagOrMemo": "false", "displayName": "USDTARBITRUM"}, "id": "ARBITRUM", "network": "ARB", "fee": 0.5, "active": true, "deposit": true, "withdraw": true, "precision": 1e-06, "limits": {"withdraw": {"min": 0.5, "max": 5000000}, "deposit": {"min": 5, "max": null}}}, "OPTIMISM": {"info": {"name": "USDT", "network": "OPTIMISM", "isDefault": false, "minConfirm": "50", "withdrawEnable": false, "depositEnable": true, "withdrawFee": "1", "withdrawMax": "5000000", "withdrawMin": "1.1", "depositMin": "5", "withdrawPrecision": "6", "depositPrecision": "6", "contractAddress": "0x94b008aa00579c1307b0ef2c499ad98a8ce58e58", "needTagOrMemo": "false", "displayName": "USDTOPTIMISM"}, "id": "OPTIMISM", "network": "OPTIMISM", "fee": 1, "active": false, "deposit": true, "withdraw": false, "precision": 1e-06, "limits": {"withdraw": {"min": 1.1, "max": 5000000}, "deposit": {"min": 5, "max": null}}}, "TON": {"info": {"name": "USDT", "network": "TON", "isDefault": false, "minConfirm": "25", "withdrawEnable": true, "depositEnable": true, "withdrawFee": "0.15", "withdrawMax": "5000000", "withdrawMin": "5", "depositMin": "5", "withdrawPrecision": "6", "depositPrecision": "6", "contractAddress": "EQCxE6mUtQJKFnGfaROTKOt1lZbDiiX1kCixRv7Nw2Id_sDs", "needTagOrMemo": "false", "displayName": "USDTTON"}, "id": "TON", "network": "TON", "fee": 0.15, "active": true, "deposit": true, "withdraw": true, "precision": 1e-06, "limits": {"withdraw": {"min": 5, "max": 5000000}, "deposit": {"min": 5, "max": null}}}, "OPBNB": {"info": {"name": "USDT", "network": "OPBNB", "isDefault": false, "minConfirm": "30", "withdrawEnable": false, "depositEnable": true, "withdrawFee": "5", "withdrawMax": "5000000", "withdrawMin": "5.5", "depositMin": "5", "withdrawPrecision": "8", "depositPrecision": "18", "contractAddress": "0x9e5aac1ba1a2e6aed6b32689dfcf62a509ca96f3", "needTagOrMemo": "false", "displayName": "USDTOPBNB"}, "id": "OPBNB", "network": "OPBNB", "fee": 5, "active": false, "deposit": true, "withdraw": false, "precision": 1e-08, "limits": {"withdraw": {"min": 5.5, "max": 5000000}, "deposit": {"min": 5, "max": null}}}, "APT": {"info": {"name": "USDT", "network": "APT", "isDefault": false, "minConfirm": "10", "withdrawEnable": true, "depositEnable": true, "withdrawFee": "0.03", "withdrawMax": "5000000", "withdrawMin": "5", "depositMin": "5", "withdrawPrecision": "6", "depositPrecision": "6", "contractAddress": "0x357b0b74bc833e95a115ad22604854d6b0fca151cecd94111770e5d6ffc9dc2b", "needTagOrMemo": "false", "displayName": "USDTAPT"}, "id": "APT", "network": "APT", "fee": 0.03, "active": true, "deposit": true, "withdraw": true, "precision": 1e-06, "limits": {"withdraw": {"min": 5, "max": 5000000}, "deposit": {"min": 5, "max": null}}}}, "limits": {"deposit": {"min": 0.8, "max": null}, "withdraw": {"min": 0.5, "max": 5000000}}}, "LTC": {"info": {"coin": "LTC", "name": "LTC", "networkList": [{"name": "LTC", "network": "BEP20", "isDefault": true, "minConfirm": "10", "withdrawEnable": true, "depositEnable": true, "withdrawFee": "0.00015", "withdrawMax": "50231.06288929", "withdrawMin": "0.049976", "depositMin": "0.038125", "withdrawPrecision": "8", "depositPrecision": "18", "contractAddress": "******************************************", "needTagOrMemo": "false", "displayName": "LTC"}, {"name": "LTC", "network": "LTC", "isDefault": false, "minConfirm": "5", "withdrawEnable": true, "depositEnable": true, "withdrawFee": "0.0001", "withdrawMax": "50231.06288929", "withdrawMin": "0.049986", "depositMin": "0.2543", "withdrawPrecision": "8", "depositPrecision": "8", "contractAddress": "", "needTagOrMemo": "false", "displayName": "LTCLTC"}]}, "id": "LTC", "numericId": null, "code": "LTC", "precision": 1e-08, "type": "crypto", "name": "LTC", "active": true, "deposit": true, "withdraw": true, "fee": 0.0001, "fees": {}, "networks": {"BEP20": {"info": {"name": "LTC", "network": "BEP20", "isDefault": true, "minConfirm": "10", "withdrawEnable": true, "depositEnable": true, "withdrawFee": "0.00015", "withdrawMax": "50231.06288929", "withdrawMin": "0.049976", "depositMin": "0.038125", "withdrawPrecision": "8", "depositPrecision": "18", "contractAddress": "******************************************", "needTagOrMemo": "false", "displayName": "LTC"}, "id": "BEP20", "network": "BEP20", "fee": 0.00015, "active": true, "deposit": true, "withdraw": true, "precision": 1e-08, "limits": {"withdraw": {"min": 0.049976, "max": 50231.06288929}, "deposit": {"min": 0.038125, "max": null}}}, "LTC": {"info": {"name": "LTC", "network": "LTC", "isDefault": false, "minConfirm": "5", "withdrawEnable": true, "depositEnable": true, "withdrawFee": "0.0001", "withdrawMax": "50231.06288929", "withdrawMin": "0.049986", "depositMin": "0.2543", "withdrawPrecision": "8", "depositPrecision": "8", "contractAddress": "", "needTagOrMemo": "false", "displayName": "LTCLTC"}, "id": "LTC", "network": "LTC", "fee": 0.0001, "active": true, "deposit": true, "withdraw": true, "precision": 1e-08, "limits": {"withdraw": {"min": 0.049986, "max": 50231.06288929}, "deposit": {"min": 0.2543, "max": null}}}}, "limits": {"deposit": {"min": 0.038125, "max": null}, "withdraw": {"min": 0.049976, "max": 50231.06288929}}}, "ETH": {"info": {"coin": "ETH", "name": "ETH", "networkList": [{"name": "ETH", "network": "ERC20", "isDefault": true, "minConfirm": "12", "withdrawEnable": true, "depositEnable": true, "withdrawFee": "0.0003", "withdrawMax": "1456.40541666", "withdrawMin": "0.001446", "depositMin": "0.002", "withdrawPrecision": "8", "depositPrecision": "8", "contractAddress": "", "needTagOrMemo": "false", "displayName": "ETH"}, {"name": "ETH", "network": "BEP20", "isDefault": false, "minConfirm": "10", "withdrawEnable": true, "depositEnable": true, "withdrawFee": "0.0000042", "withdrawMax": "1456.40541666", "withdrawMin": "0.001446", "depositMin": "0.000993", "withdrawPrecision": "8", "depositPrecision": "18", "contractAddress": "******************************************", "needTagOrMemo": "false", "displayName": "ETHBEP20"}, {"name": "ETH", "network": "ARBITRUM", "isDefault": false, "minConfirm": "30", "withdrawEnable": true, "depositEnable": true, "withdrawFee": "0.0001", "withdrawMax": "1456.40541666", "withdrawMin": "0.00010000000000000001", "depositMin": "0.001", "withdrawPrecision": "8", "depositPrecision": "18", "contractAddress": "", "needTagOrMemo": "false", "displayName": "ETHARBITRUM"}, {"name": "ETH", "network": "OPTIMISM", "isDefault": false, "minConfirm": "50", "withdrawEnable": true, "depositEnable": true, "withdrawFee": "0.0001", "withdrawMax": "1456.40541666", "withdrawMin": "0.00010000000000000001", "depositMin": "0.0009", "withdrawPrecision": "8", "depositPrecision": "18", "contractAddress": "", "needTagOrMemo": "false", "displayName": "ETHOPTIMISM"}, {"name": "ETH", "network": "ZKSYNCERA", "isDefault": false, "minConfirm": "32", "withdrawEnable": false, "depositEnable": false, "withdrawFee": "0.00004", "withdrawMax": "1456.40541666", "withdrawMin": "0.001447", "depositMin": "0.015", "withdrawPrecision": "8", "depositPrecision": "18", "contractAddress": "", "needTagOrMemo": "false", "displayName": "ETHZKSYNC2"}, {"name": "ETH", "network": "BASE", "isDefault": false, "minConfirm": "32", "withdrawEnable": true, "depositEnable": true, "withdrawFee": "0.00003", "withdrawMax": "1456.40541666", "withdrawMin": "0.001447", "depositMin": "0.01", "withdrawPrecision": "8", "depositPrecision": "18", "contractAddress": "", "needTagOrMemo": "false", "displayName": "ETHBASE"}, {"name": "ETH", "network": "STARKNET", "isDefault": false, "minConfirm": "15", "withdrawEnable": false, "depositEnable": false, "withdrawFee": "0.000094", "withdrawMax": "1456.40541666", "withdrawMin": "0.001447", "depositMin": "0.002", "withdrawPrecision": "8", "depositPrecision": "18", "contractAddress": "0x049d36570d4e46f48e99674bd3fcc84644ddd6b96f7c741b1562b82f9e004dc7", "needTagOrMemo": "false", "displayName": "ETHSTARKNET"}, {"name": "ETH", "network": "MTL", "isDefault": false, "minConfirm": "25", "withdrawEnable": false, "depositEnable": false, "withdrawFee": "0.000882", "withdrawMax": "1456.40541666", "withdrawMin": "0.001447", "depositMin": "0.002421", "withdrawPrecision": "8", "depositPrecision": "18", "contractAddress": "", "needTagOrMemo": "false", "displayName": "ETHMTL"}]}, "id": "ETH", "numericId": null, "code": "ETH", "precision": 1e-08, "type": "crypto", "name": "ETH", "active": true, "deposit": true, "withdraw": true, "fee": 4.2e-06, "fees": {}, "networks": {"ERC20": {"info": {"name": "ETH", "network": "ERC20", "isDefault": true, "minConfirm": "12", "withdrawEnable": true, "depositEnable": true, "withdrawFee": "0.0003", "withdrawMax": "1456.40541666", "withdrawMin": "0.001446", "depositMin": "0.002", "withdrawPrecision": "8", "depositPrecision": "8", "contractAddress": "", "needTagOrMemo": "false", "displayName": "ETH"}, "id": "ERC20", "network": "ERC20", "fee": 0.0003, "active": true, "deposit": true, "withdraw": true, "precision": 1e-08, "limits": {"withdraw": {"min": 0.001446, "max": 1456.40541666}, "deposit": {"min": 0.002, "max": null}}}, "BEP20": {"info": {"name": "ETH", "network": "BEP20", "isDefault": false, "minConfirm": "10", "withdrawEnable": true, "depositEnable": true, "withdrawFee": "0.0000042", "withdrawMax": "1456.40541666", "withdrawMin": "0.001446", "depositMin": "0.000993", "withdrawPrecision": "8", "depositPrecision": "18", "contractAddress": "******************************************", "needTagOrMemo": "false", "displayName": "ETHBEP20"}, "id": "BEP20", "network": "BEP20", "fee": 4.2e-06, "active": true, "deposit": true, "withdraw": true, "precision": 1e-08, "limits": {"withdraw": {"min": 0.001446, "max": 1456.40541666}, "deposit": {"min": 0.000993, "max": null}}}, "ARB": {"info": {"name": "ETH", "network": "ARBITRUM", "isDefault": false, "minConfirm": "30", "withdrawEnable": true, "depositEnable": true, "withdrawFee": "0.0001", "withdrawMax": "1456.40541666", "withdrawMin": "0.00010000000000000001", "depositMin": "0.001", "withdrawPrecision": "8", "depositPrecision": "18", "contractAddress": "", "needTagOrMemo": "false", "displayName": "ETHARBITRUM"}, "id": "ARBITRUM", "network": "ARB", "fee": 0.0001, "active": true, "deposit": true, "withdraw": true, "precision": 1e-08, "limits": {"withdraw": {"min": 0.0001, "max": 1456.40541666}, "deposit": {"min": 0.001, "max": null}}}, "OPTIMISM": {"info": {"name": "ETH", "network": "OPTIMISM", "isDefault": false, "minConfirm": "50", "withdrawEnable": true, "depositEnable": true, "withdrawFee": "0.0001", "withdrawMax": "1456.40541666", "withdrawMin": "0.00010000000000000001", "depositMin": "0.0009", "withdrawPrecision": "8", "depositPrecision": "18", "contractAddress": "", "needTagOrMemo": "false", "displayName": "ETHOPTIMISM"}, "id": "OPTIMISM", "network": "OPTIMISM", "fee": 0.0001, "active": true, "deposit": true, "withdraw": true, "precision": 1e-08, "limits": {"withdraw": {"min": 0.0001, "max": 1456.40541666}, "deposit": {"min": 0.0009, "max": null}}}, "ZKSYNCERA": {"info": {"name": "ETH", "network": "ZKSYNCERA", "isDefault": false, "minConfirm": "32", "withdrawEnable": false, "depositEnable": false, "withdrawFee": "0.00004", "withdrawMax": "1456.40541666", "withdrawMin": "0.001447", "depositMin": "0.015", "withdrawPrecision": "8", "depositPrecision": "18", "contractAddress": "", "needTagOrMemo": "false", "displayName": "ETHZKSYNC2"}, "id": "ZKSYNCERA", "network": "ZKSYNCERA", "fee": 4e-05, "active": false, "deposit": false, "withdraw": false, "precision": 1e-08, "limits": {"withdraw": {"min": 0.001447, "max": 1456.40541666}, "deposit": {"min": 0.015, "max": null}}}, "BASE": {"info": {"name": "ETH", "network": "BASE", "isDefault": false, "minConfirm": "32", "withdrawEnable": true, "depositEnable": true, "withdrawFee": "0.00003", "withdrawMax": "1456.40541666", "withdrawMin": "0.001447", "depositMin": "0.01", "withdrawPrecision": "8", "depositPrecision": "18", "contractAddress": "", "needTagOrMemo": "false", "displayName": "ETHBASE"}, "id": "BASE", "network": "BASE", "fee": 3e-05, "active": true, "deposit": true, "withdraw": true, "precision": 1e-08, "limits": {"withdraw": {"min": 0.001447, "max": 1456.40541666}, "deposit": {"min": 0.01, "max": null}}}, "STARKNET": {"info": {"name": "ETH", "network": "STARKNET", "isDefault": false, "minConfirm": "15", "withdrawEnable": false, "depositEnable": false, "withdrawFee": "0.000094", "withdrawMax": "1456.40541666", "withdrawMin": "0.001447", "depositMin": "0.002", "withdrawPrecision": "8", "depositPrecision": "18", "contractAddress": "0x049d36570d4e46f48e99674bd3fcc84644ddd6b96f7c741b1562b82f9e004dc7", "needTagOrMemo": "false", "displayName": "ETHSTARKNET"}, "id": "STARKNET", "network": "STARKNET", "fee": 9.4e-05, "active": false, "deposit": false, "withdraw": false, "precision": 1e-08, "limits": {"withdraw": {"min": 0.001447, "max": 1456.40541666}, "deposit": {"min": 0.002, "max": null}}}, "MTL": {"info": {"name": "ETH", "network": "MTL", "isDefault": false, "minConfirm": "25", "withdrawEnable": false, "depositEnable": false, "withdrawFee": "0.000882", "withdrawMax": "1456.40541666", "withdrawMin": "0.001447", "depositMin": "0.002421", "withdrawPrecision": "8", "depositPrecision": "18", "contractAddress": "", "needTagOrMemo": "false", "displayName": "ETHMTL"}, "id": "MTL", "network": "MTL", "fee": 0.000882, "active": false, "deposit": false, "withdraw": false, "precision": 1e-08, "limits": {"withdraw": {"min": 0.001447, "max": 1456.40541666}, "deposit": {"min": 0.002421, "max": null}}}}, "limits": {"deposit": {"min": 0.0009, "max": null}, "withdraw": {"min": 0.0001, "max": 1456.40541666}}}, "ADA": {"info": {"coin": "ADA", "name": "ADA", "networkList": [{"name": "ADA", "network": "BEP20", "isDefault": true, "minConfirm": "10", "withdrawEnable": true, "depositEnable": true, "withdrawFee": "0.019", "withdrawMax": "6134216.66053245", "withdrawMin": "6.099793", "depositMin": "4.342677", "withdrawPrecision": "8", "depositPrecision": "18", "contractAddress": "******************************************", "needTagOrMemo": "false", "displayName": "ADA"}, {"name": "ADA", "network": "ADA", "isDefault": false, "minConfirm": "30", "withdrawEnable": true, "depositEnable": true, "withdrawFee": "0.613", "withdrawMax": "6134216.66053245", "withdrawMin": "6.105752", "depositMin": "2", "withdrawPrecision": "6", "depositPrecision": "6", "contractAddress": "", "needTagOrMemo": "false", "displayName": "ADAADA"}]}, "id": "ADA", "numericId": null, "code": "ADA", "precision": 1e-06, "type": "crypto", "name": "ADA", "active": true, "deposit": true, "withdraw": true, "fee": 0.019, "fees": {}, "networks": {"BEP20": {"info": {"name": "ADA", "network": "BEP20", "isDefault": true, "minConfirm": "10", "withdrawEnable": true, "depositEnable": true, "withdrawFee": "0.019", "withdrawMax": "6134216.66053245", "withdrawMin": "6.099793", "depositMin": "4.342677", "withdrawPrecision": "8", "depositPrecision": "18", "contractAddress": "******************************************", "needTagOrMemo": "false", "displayName": "ADA"}, "id": "BEP20", "network": "BEP20", "fee": 0.019, "active": true, "deposit": true, "withdraw": true, "precision": 1e-08, "limits": {"withdraw": {"min": 6.099793, "max": 6134216.66053245}, "deposit": {"min": 4.342677, "max": null}}}, "ADA": {"info": {"name": "ADA", "network": "ADA", "isDefault": false, "minConfirm": "30", "withdrawEnable": true, "depositEnable": true, "withdrawFee": "0.613", "withdrawMax": "6134216.66053245", "withdrawMin": "6.105752", "depositMin": "2", "withdrawPrecision": "6", "depositPrecision": "6", "contractAddress": "", "needTagOrMemo": "false", "displayName": "ADAADA"}, "id": "ADA", "network": "ADA", "fee": 0.613, "active": true, "deposit": true, "withdraw": true, "precision": 1e-06, "limits": {"withdraw": {"min": 6.105752, "max": 6134216.66053245}, "deposit": {"min": 2, "max": null}}}}, "limits": {"deposit": {"min": 2, "max": null}, "withdraw": {"min": 6.099793, "max": 6134216.66053245}}}, "XRP": {"info": {"coin": "XRP", "name": "XRP", "networkList": [{"name": "XRP", "network": "XRP", "isDefault": true, "minConfirm": "5", "withdrawEnable": true, "depositEnable": true, "withdrawFee": "0.2", "withdrawMax": "1532567.04980842", "withdrawMin": "10.2", "depositMin": "0.1", "withdrawPrecision": "6", "depositPrecision": "6", "contractAddress": "", "needTagOrMemo": "true", "displayName": "XRP"}, {"name": "XRP", "network": "BEP20", "isDefault": false, "minConfirm": "10", "withdrawEnable": false, "depositEnable": false, "withdrawFee": "0.0047", "withdrawMax": "1532567.04980842", "withdrawMin": "1.527931", "depositMin": "1.091704", "withdrawPrecision": "8", "depositPrecision": "18", "contractAddress": "******************************************", "needTagOrMemo": "false", "displayName": "XRPBEP20"}]}, "id": "XRP", "numericId": null, "code": "XRP", "precision": 1e-06, "type": "crypto", "name": "XRP", "active": true, "deposit": true, "withdraw": true, "fee": 0.0047, "fees": {}, "networks": {"XRP": {"info": {"name": "XRP", "network": "XRP", "isDefault": true, "minConfirm": "5", "withdrawEnable": true, "depositEnable": true, "withdrawFee": "0.2", "withdrawMax": "1532567.04980842", "withdrawMin": "10.2", "depositMin": "0.1", "withdrawPrecision": "6", "depositPrecision": "6", "contractAddress": "", "needTagOrMemo": "true", "displayName": "XRP"}, "id": "XRP", "network": "XRP", "fee": 0.2, "active": true, "deposit": true, "withdraw": true, "precision": 1e-06, "limits": {"withdraw": {"min": 10.2, "max": 1532567.04980842}, "deposit": {"min": 0.1, "max": null}}}, "BEP20": {"info": {"name": "XRP", "network": "BEP20", "isDefault": false, "minConfirm": "10", "withdrawEnable": false, "depositEnable": false, "withdrawFee": "0.0047", "withdrawMax": "1532567.04980842", "withdrawMin": "1.527931", "depositMin": "1.091704", "withdrawPrecision": "8", "depositPrecision": "18", "contractAddress": "******************************************", "needTagOrMemo": "false", "displayName": "XRPBEP20"}, "id": "BEP20", "network": "BEP20", "fee": 0.0047, "active": false, "deposit": false, "withdraw": false, "precision": 1e-08, "limits": {"withdraw": {"min": 1.527931, "max": 1532567.04980842}, "deposit": {"min": 1.091704, "max": null}}}}, "limits": {"deposit": {"min": 0.1, "max": null}, "withdraw": {"min": 1.527931, "max": 1532567.04980842}}}, "USDC": {"info": {"coin": "USDC", "name": "USDC", "networkList": [{"name": "USDC", "network": "ERC20", "isDefault": true, "minConfirm": "12", "withdrawEnable": true, "depositEnable": true, "withdrawFee": "1.1", "withdrawMax": "5003001.80108064", "withdrawMin": "5.003002", "depositMin": "11.004403", "withdrawPrecision": "6", "depositPrecision": "6", "contractAddress": "******************************************", "needTagOrMemo": "false", "displayName": "USDC"}, {"name": "USDC", "network": "BEP20", "isDefault": false, "minConfirm": "10", "withdrawEnable": true, "depositEnable": true, "withdrawFee": "0.5", "withdrawMax": "5003001.80108064", "withdrawMin": "3", "depositMin": "1", "withdrawPrecision": "8", "depositPrecision": "18", "contractAddress": "0x8ac76a51cc950d9822d68b83fe1ad97b32cd580d", "needTagOrMemo": "false", "displayName": "USDCBEP20"}, {"name": "USDC", "network": "SOL", "isDefault": false, "minConfirm": "50", "withdrawEnable": true, "depositEnable": true, "withdrawFee": "1", "withdrawMax": "5003001.80108064", "withdrawMin": "1.00000000000000000001", "depositMin": "0.9", "withdrawPrecision": "6", "depositPrecision": "6", "contractAddress": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v", "needTagOrMemo": "false", "displayName": "USDCSOL"}, {"name": "USDC", "network": "Polygon(Bridged)", "isDefault": false, "minConfirm": "64", "withdrawEnable": false, "depositEnable": false, "withdrawFee": "0.5", "withdrawMax": "5003001.80108064", "withdrawMin": "0.5", "depositMin": "10", "withdrawPrecision": "6", "depositPrecision": "6", "contractAddress": "0x2791bca1f2de4661ed88a30c99a7a9449aa84174", "needTagOrMemo": "false", "displayName": "USDCPOLYGON"}, {"name": "USDC", "network": "TRC20", "isDefault": false, "minConfirm": "2", "withdrawEnable": false, "depositEnable": false, "withdrawFee": "1", "withdrawMax": "5003001.80108064", "withdrawMin": "1", "depositMin": "5", "withdrawPrecision": "6", "depositPrecision": "6", "contractAddress": "TEkxiTehnzSmSe2XqrBj4w32RUN966rdz8-OFFLINE", "needTagOrMemo": "false", "displayName": "USDCTRC20"}, {"name": "USDC", "network": "Optimism(Bridged)", "isDefault": false, "minConfirm": "50", "withdrawEnable": false, "depositEnable": false, "withdrawFee": "0.3", "withdrawMax": "5003001.80108064", "withdrawMin": "5.489574", "depositMin": "5", "withdrawPrecision": "6", "depositPrecision": "6", "contractAddress": "0x7f5c764cbc14f9669b88837ca1490cca17c31607", "needTagOrMemo": "false", "displayName": "USDCOPTIMISM"}, {"name": "USDC", "network": "ArbitrumOne(Bridged)", "isDefault": false, "minConfirm": "30", "withdrawEnable": false, "depositEnable": false, "withdrawFee": "0.5", "withdrawMax": "5003001.80108064", "withdrawMin": "5.489574", "depositMin": "5", "withdrawPrecision": "6", "depositPrecision": "6", "contractAddress": "0xff970a61a04b1ca14834a43f5de4533ebddb5cc8", "needTagOrMemo": "false", "displayName": "USDCARBITRUM"}, {"name": "USDC", "network": "ARBITRUM", "isDefault": false, "minConfirm": "30", "withdrawEnable": true, "depositEnable": true, "withdrawFee": "0.5", "withdrawMax": "5003001.80108064", "withdrawMin": "5.489574", "depositMin": "5", "withdrawPrecision": "6", "depositPrecision": "6", "contractAddress": "0xaf88d065e77c8cc2239327c5edb3a432268e5831", "needTagOrMemo": "false", "displayName": "USDC.C"}, {"name": "USDC", "network": "OPTIMISM", "isDefault": false, "minConfirm": "50", "withdrawEnable": true, "depositEnable": true, "withdrawFee": "0.3", "withdrawMax": "5003001.80108064", "withdrawMin": "5.489574", "depositMin": "5", "withdrawPrecision": "6", "depositPrecision": "6", "contractAddress": "0x0b2c639c533813f4aa9d7837caf62653d097ff85", "needTagOrMemo": "false", "displayName": "USDC.COPTIMISM"}, {"name": "USDC", "network": "POLYGON", "isDefault": false, "minConfirm": "64", "withdrawEnable": true, "depositEnable": true, "withdrawFee": "0.5", "withdrawMax": "5003001.80108064", "withdrawMin": "5.489574", "depositMin": "10", "withdrawPrecision": "6", "depositPrecision": "6", "contractAddress": "0x3c499c542cef5e3811e1192ce70d8cc03d5c3359", "needTagOrMemo": "false", "displayName": "USDC.CPOLYGON"}, {"name": "USDC", "network": "BASE", "isDefault": false, "minConfirm": "32", "withdrawEnable": true, "depositEnable": true, "withdrawFee": "0.1", "withdrawMax": "5003001.80108064", "withdrawMin": "5.003002", "depositMin": "5", "withdrawPrecision": "6", "depositPrecision": "6", "contractAddress": "0x833589fcd6edb6e08f4c7c32d4f71b54bda02913", "needTagOrMemo": "false", "displayName": "USDCBASE"}, {"name": "USDC", "network": "AVAX-C", "isDefault": false, "minConfirm": "30", "withdrawEnable": false, "depositEnable": false, "withdrawFee": "0.067", "withdrawMax": "5003001.80108064", "withdrawMin": "5.003002", "depositMin": "5", "withdrawPrecision": "6", "depositPrecision": "6", "contractAddress": "0xb97ef9ef8734c71904d8002f8b6bc66dd9c48a6e", "needTagOrMemo": "false", "displayName": "USDCAVAX-C"}, {"name": "USDC", "network": "APT", "isDefault": false, "minConfirm": "10", "withdrawEnable": true, "depositEnable": true, "withdrawFee": "0.1", "withdrawMax": "5003001.80108064", "withdrawMin": "5.003002", "depositMin": "5", "withdrawPrecision": "6", "depositPrecision": "6", "contractAddress": "0xbae207659db88bea0cbead6da0ed00aac12edcdda169e591cd41c94180b46f3b", "needTagOrMemo": "false", "displayName": "USDCAPT"}]}, "id": "USDC", "numericId": null, "code": "USDC", "precision": 1e-06, "type": "crypto", "name": "USDC", "active": true, "deposit": true, "withdraw": true, "fee": 0.067, "fees": {}, "networks": {"ERC20": {"info": {"name": "USDC", "network": "ERC20", "isDefault": true, "minConfirm": "12", "withdrawEnable": true, "depositEnable": true, "withdrawFee": "1.1", "withdrawMax": "5003001.80108064", "withdrawMin": "5.003002", "depositMin": "11.004403", "withdrawPrecision": "6", "depositPrecision": "6", "contractAddress": "******************************************", "needTagOrMemo": "false", "displayName": "USDC"}, "id": "ERC20", "network": "ERC20", "fee": 1.1, "active": true, "deposit": true, "withdraw": true, "precision": 1e-06, "limits": {"withdraw": {"min": 5.003002, "max": 5003001.80108064}, "deposit": {"min": 11.004403, "max": null}}}, "BEP20": {"info": {"name": "USDC", "network": "BEP20", "isDefault": false, "minConfirm": "10", "withdrawEnable": true, "depositEnable": true, "withdrawFee": "0.5", "withdrawMax": "5003001.80108064", "withdrawMin": "3", "depositMin": "1", "withdrawPrecision": "8", "depositPrecision": "18", "contractAddress": "0x8ac76a51cc950d9822d68b83fe1ad97b32cd580d", "needTagOrMemo": "false", "displayName": "USDCBEP20"}, "id": "BEP20", "network": "BEP20", "fee": 0.5, "active": true, "deposit": true, "withdraw": true, "precision": 1e-08, "limits": {"withdraw": {"min": 3, "max": 5003001.80108064}, "deposit": {"min": 1, "max": null}}}, "SOL": {"info": {"name": "USDC", "network": "SOL", "isDefault": false, "minConfirm": "50", "withdrawEnable": true, "depositEnable": true, "withdrawFee": "1", "withdrawMax": "5003001.80108064", "withdrawMin": "1.00000000000000000001", "depositMin": "0.9", "withdrawPrecision": "6", "depositPrecision": "6", "contractAddress": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v", "needTagOrMemo": "false", "displayName": "USDCSOL"}, "id": "SOL", "network": "SOL", "fee": 1, "active": true, "deposit": true, "withdraw": true, "precision": 1e-06, "limits": {"withdraw": {"min": 1, "max": 5003001.80108064}, "deposit": {"min": 0.9, "max": null}}}, "Polygon(Bridged)": {"info": {"name": "USDC", "network": "Polygon(Bridged)", "isDefault": false, "minConfirm": "64", "withdrawEnable": false, "depositEnable": false, "withdrawFee": "0.5", "withdrawMax": "5003001.80108064", "withdrawMin": "0.5", "depositMin": "10", "withdrawPrecision": "6", "depositPrecision": "6", "contractAddress": "0x2791bca1f2de4661ed88a30c99a7a9449aa84174", "needTagOrMemo": "false", "displayName": "USDCPOLYGON"}, "id": "Polygon(Bridged)", "network": "Polygon(Bridged)", "fee": 0.5, "active": false, "deposit": false, "withdraw": false, "precision": 1e-06, "limits": {"withdraw": {"min": 0.5, "max": 5003001.80108064}, "deposit": {"min": 10, "max": null}}}, "TRC20": {"info": {"name": "USDC", "network": "TRC20", "isDefault": false, "minConfirm": "2", "withdrawEnable": false, "depositEnable": false, "withdrawFee": "1", "withdrawMax": "5003001.80108064", "withdrawMin": "1", "depositMin": "5", "withdrawPrecision": "6", "depositPrecision": "6", "contractAddress": "TEkxiTehnzSmSe2XqrBj4w32RUN966rdz8-OFFLINE", "needTagOrMemo": "false", "displayName": "USDCTRC20"}, "id": "TRC20", "network": "TRC20", "fee": 1, "active": false, "deposit": false, "withdraw": false, "precision": 1e-06, "limits": {"withdraw": {"min": 1, "max": 5003001.80108064}, "deposit": {"min": 5, "max": null}}}, "Optimism(Bridged)": {"info": {"name": "USDC", "network": "Optimism(Bridged)", "isDefault": false, "minConfirm": "50", "withdrawEnable": false, "depositEnable": false, "withdrawFee": "0.3", "withdrawMax": "5003001.80108064", "withdrawMin": "5.489574", "depositMin": "5", "withdrawPrecision": "6", "depositPrecision": "6", "contractAddress": "0x7f5c764cbc14f9669b88837ca1490cca17c31607", "needTagOrMemo": "false", "displayName": "USDCOPTIMISM"}, "id": "Optimism(Bridged)", "network": "Optimism(Bridged)", "fee": 0.3, "active": false, "deposit": false, "withdraw": false, "precision": 1e-06, "limits": {"withdraw": {"min": 5.489574, "max": 5003001.80108064}, "deposit": {"min": 5, "max": null}}}, "ArbitrumOne(Bridged)": {"info": {"name": "USDC", "network": "ArbitrumOne(Bridged)", "isDefault": false, "minConfirm": "30", "withdrawEnable": false, "depositEnable": false, "withdrawFee": "0.5", "withdrawMax": "5003001.80108064", "withdrawMin": "5.489574", "depositMin": "5", "withdrawPrecision": "6", "depositPrecision": "6", "contractAddress": "0xff970a61a04b1ca14834a43f5de4533ebddb5cc8", "needTagOrMemo": "false", "displayName": "USDCARBITRUM"}, "id": "ArbitrumOne(Bridged)", "network": "ArbitrumOne(Bridged)", "fee": 0.5, "active": false, "deposit": false, "withdraw": false, "precision": 1e-06, "limits": {"withdraw": {"min": 5.489574, "max": 5003001.80108064}, "deposit": {"min": 5, "max": null}}}, "ARB": {"info": {"name": "USDC", "network": "ARBITRUM", "isDefault": false, "minConfirm": "30", "withdrawEnable": true, "depositEnable": true, "withdrawFee": "0.5", "withdrawMax": "5003001.80108064", "withdrawMin": "5.489574", "depositMin": "5", "withdrawPrecision": "6", "depositPrecision": "6", "contractAddress": "0xaf88d065e77c8cc2239327c5edb3a432268e5831", "needTagOrMemo": "false", "displayName": "USDC.C"}, "id": "ARBITRUM", "network": "ARB", "fee": 0.5, "active": true, "deposit": true, "withdraw": true, "precision": 1e-06, "limits": {"withdraw": {"min": 5.489574, "max": 5003001.80108064}, "deposit": {"min": 5, "max": null}}}, "OPTIMISM": {"info": {"name": "USDC", "network": "OPTIMISM", "isDefault": false, "minConfirm": "50", "withdrawEnable": true, "depositEnable": true, "withdrawFee": "0.3", "withdrawMax": "5003001.80108064", "withdrawMin": "5.489574", "depositMin": "5", "withdrawPrecision": "6", "depositPrecision": "6", "contractAddress": "0x0b2c639c533813f4aa9d7837caf62653d097ff85", "needTagOrMemo": "false", "displayName": "USDC.COPTIMISM"}, "id": "OPTIMISM", "network": "OPTIMISM", "fee": 0.3, "active": true, "deposit": true, "withdraw": true, "precision": 1e-06, "limits": {"withdraw": {"min": 5.489574, "max": 5003001.80108064}, "deposit": {"min": 5, "max": null}}}, "MATIC": {"info": {"name": "USDC", "network": "POLYGON", "isDefault": false, "minConfirm": "64", "withdrawEnable": true, "depositEnable": true, "withdrawFee": "0.5", "withdrawMax": "5003001.80108064", "withdrawMin": "5.489574", "depositMin": "10", "withdrawPrecision": "6", "depositPrecision": "6", "contractAddress": "0x3c499c542cef5e3811e1192ce70d8cc03d5c3359", "needTagOrMemo": "false", "displayName": "USDC.CPOLYGON"}, "id": "POLYGON", "network": "MATIC", "fee": 0.5, "active": true, "deposit": true, "withdraw": true, "precision": 1e-06, "limits": {"withdraw": {"min": 5.489574, "max": 5003001.80108064}, "deposit": {"min": 10, "max": null}}}, "BASE": {"info": {"name": "USDC", "network": "BASE", "isDefault": false, "minConfirm": "32", "withdrawEnable": true, "depositEnable": true, "withdrawFee": "0.1", "withdrawMax": "5003001.80108064", "withdrawMin": "5.003002", "depositMin": "5", "withdrawPrecision": "6", "depositPrecision": "6", "contractAddress": "0x833589fcd6edb6e08f4c7c32d4f71b54bda02913", "needTagOrMemo": "false", "displayName": "USDCBASE"}, "id": "BASE", "network": "BASE", "fee": 0.1, "active": true, "deposit": true, "withdraw": true, "precision": 1e-06, "limits": {"withdraw": {"min": 5.003002, "max": 5003001.80108064}, "deposit": {"min": 5, "max": null}}}, "AVAX-C": {"info": {"name": "USDC", "network": "AVAX-C", "isDefault": false, "minConfirm": "30", "withdrawEnable": false, "depositEnable": false, "withdrawFee": "0.067", "withdrawMax": "5003001.80108064", "withdrawMin": "5.003002", "depositMin": "5", "withdrawPrecision": "6", "depositPrecision": "6", "contractAddress": "0xb97ef9ef8734c71904d8002f8b6bc66dd9c48a6e", "needTagOrMemo": "false", "displayName": "USDCAVAX-C"}, "id": "AVAX-C", "network": "AVAX-C", "fee": 0.067, "active": false, "deposit": false, "withdraw": false, "precision": 1e-06, "limits": {"withdraw": {"min": 5.003002, "max": 5003001.80108064}, "deposit": {"min": 5, "max": null}}}, "APT": {"info": {"name": "USDC", "network": "APT", "isDefault": false, "minConfirm": "10", "withdrawEnable": true, "depositEnable": true, "withdrawFee": "0.1", "withdrawMax": "5003001.80108064", "withdrawMin": "5.003002", "depositMin": "5", "withdrawPrecision": "6", "depositPrecision": "6", "contractAddress": "0xbae207659db88bea0cbead6da0ed00aac12edcdda169e591cd41c94180b46f3b", "needTagOrMemo": "false", "displayName": "USDCAPT"}, "id": "APT", "network": "APT", "fee": 0.1, "active": true, "deposit": true, "withdraw": true, "precision": 1e-06, "limits": {"withdraw": {"min": 5.003002, "max": 5003001.80108064}, "deposit": {"min": 5, "max": null}}}}, "limits": {"deposit": {"min": 0.9, "max": null}, "withdraw": {"min": 0.5, "max": 5003001.80108064}}}, "TRUMP": {"info": {"coin": "TRUMPSOL", "name": "TRUMPSOL", "networkList": [{"name": "TRUMPSOL", "network": "SOL", "isDefault": true, "minConfirm": "50", "withdrawEnable": true, "depositEnable": true, "withdrawFee": "0.099207", "withdrawMax": "494559.84174085", "withdrawMin": "0.496032", "depositMin": "0.377575", "withdrawPrecision": "6", "depositPrecision": "6", "contractAddress": "6p6xgHyF7AeE6TZkSmFsko444wqoP15icUSqi2jfGiPN", "needTagOrMemo": "false", "displayName": "TRUMPSOL"}]}, "id": "TRUMPSOL", "numericId": null, "code": "TRUMP", "precision": 1e-06, "type": "crypto", "name": "TRUMPSOL", "active": true, "deposit": true, "withdraw": true, "fee": 0.099207, "fees": {}, "networks": {"SOL": {"info": {"name": "TRUMPSOL", "network": "SOL", "isDefault": true, "minConfirm": "50", "withdrawEnable": true, "depositEnable": true, "withdrawFee": "0.099207", "withdrawMax": "494559.84174085", "withdrawMin": "0.496032", "depositMin": "0.377575", "withdrawPrecision": "6", "depositPrecision": "6", "contractAddress": "6p6xgHyF7AeE6TZkSmFsko444wqoP15icUSqi2jfGiPN", "needTagOrMemo": "false", "displayName": "TRUMPSOL"}, "id": "SOL", "network": "SOL", "fee": 0.099207, "active": true, "deposit": true, "withdraw": true, "precision": 1e-06, "limits": {"withdraw": {"min": 0.496032, "max": 494559.84174085}, "deposit": {"min": 0.377575, "max": null}}}}, "limits": {"deposit": {"min": 0.377575, "max": null}, "withdraw": {"min": 0.496032, "max": 494559.84174085}}}, "TRUMPSOL": {"info": {"coin": "TRUMPSOL", "name": "TRUMPSOL", "networkList": [{"name": "TRUMPSOL", "network": "SOL", "isDefault": true, "minConfirm": "50", "withdrawEnable": true, "depositEnable": true, "withdrawFee": "0.049803", "withdrawMax": "248877.7", "withdrawMin": "0.249014", "depositMin": "1.5", "withdrawPrecision": "6", "depositPrecision": "6", "contractAddress": "6p6xgHyF7AeE6TZkSmFsko444wqoP15icUSqi2jfGiPN", "needTagOrMemo": "false"}]}, "code": "TRUMPSOL", "id": "TRUMPSOL", "precision": null, "name": "TRUMPSOL", "active": true, "deposit": true, "withdraw": true, "networks": {"SOL": {"info": {"name": "TRUMPSOL", "network": "SOL", "isDefault": true, "minConfirm": "50", "withdrawEnable": true, "depositEnable": true, "withdrawFee": "0.049803", "withdrawMax": "248877.7", "withdrawMin": "0.249014", "depositMin": "1.5", "withdrawPrecision": "6", "depositPrecision": "6", "contractAddress": "6p6xgHyF7AeE6TZkSmFsko444wqoP15icUSqi2jfGiPN", "needTagOrMemo": "false"}, "id": "SOL", "network": "SOL", "fee": 0.049803, "active": true, "deposit": true, "withdraw": true, "precision": 6, "limits": {"withdraw": {"min": 0.249014, "max": 248877.7}}}}, "fee": 0.049803, "limits": {"withdraw": {"min": 0.249014, "max": 248877.7}}}}