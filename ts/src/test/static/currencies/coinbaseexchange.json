{"BTC": {"info": {"id": "BTC", "name": "Bitcoin", "min_size": "0.********", "status": "online", "message": "", "max_precision": "0.********", "convertible_to": [], "details": {"type": "crypto", "symbol": "₿", "network_confirmations": "2", "sort_order": "20", "crypto_address_link": "https://live.blockcypher.com/btc/address/{{address}}", "crypto_transaction_link": "https://live.blockcypher.com/btc/tx/{{txId}}", "push_payment_methods": ["crypto"], "group_types": ["btc", "crypto"], "display_name": null, "processing_time_seconds": null, "min_withdrawal_amount": "0.0001", "max_withdrawal_amount": "2400"}, "default_network": "bitcoin", "supported_networks": [{"id": "arbitrum", "name": "Arbitrum", "status": "online", "contract_address": "", "crypto_address_link": "https://arbiscan.io/address/{{address}}", "crypto_transaction_link": "https://arbiscan.io/tx/{{txId}}", "min_withdrawal_amount": "0.00002", "max_withdrawal_amount": "200", "network_confirmations": null, "processing_time_seconds": null}, {"id": "base", "name": "Base", "status": "online", "contract_address": "", "crypto_address_link": "https://basescan.org/address/{{address}}", "crypto_transaction_link": "https://basescan.org/tx/{{txId}}", "min_withdrawal_amount": "0.00002", "max_withdrawal_amount": "200", "network_confirmations": null, "processing_time_seconds": null}, {"id": "bitcoin", "name": "Bitcoin", "status": "online", "contract_address": null, "crypto_address_link": "https://live.blockcypher.com/btc/address/{{address}}", "crypto_transaction_link": "https://live.blockcypher.com/btc/tx/{{txId}}", "min_withdrawal_amount": "0.0001", "max_withdrawal_amount": "2400", "network_confirmations": "2", "processing_time_seconds": null}, {"id": "ethereum", "name": "Ethereum", "status": "online", "contract_address": "", "crypto_address_link": "https://etherscan.io/token/******************************************?a={{address}}", "crypto_transaction_link": "https://etherscan.io/tx/0x{{txId}}", "min_withdrawal_amount": "0.00002", "max_withdrawal_amount": "200", "network_confirmations": "35", "processing_time_seconds": null}, {"id": "solana", "name": "Solana", "status": "online", "contract_address": "", "crypto_address_link": "https://explorer.solana.com/address/{{address}}", "crypto_transaction_link": "https://explorer.solana.com/tx/{{txId}}", "min_withdrawal_amount": "0.00002", "max_withdrawal_amount": "200", "network_confirmations": "31", "processing_time_seconds": null}], "display_name": "BTC"}, "id": "BTC", "numericId": null, "code": "BTC", "precision": 1e-08, "type": "crypto", "name": "Bitcoin", "active": true, "deposit": null, "withdraw": null, "fee": null, "fees": {}, "networks": {"ARBITRUM": {"id": "arbitrum", "name": "Arbitrum", "active": true, "fee": null, "precision": null, "limits": {"withdraw": {"min": 2e-05, "max": 200}}, "contract": null, "info": {"id": "arbitrum", "name": "Arbitrum", "status": "online", "contract_address": "", "crypto_address_link": "https://arbiscan.io/address/{{address}}", "crypto_transaction_link": "https://arbiscan.io/tx/{{txId}}", "min_withdrawal_amount": "0.00002", "max_withdrawal_amount": "200", "network_confirmations": null, "processing_time_seconds": null}}, "BASE": {"id": "base", "name": "Base", "active": true, "fee": null, "precision": null, "limits": {"withdraw": {"min": 2e-05, "max": 200}}, "contract": null, "info": {"id": "base", "name": "Base", "status": "online", "contract_address": "", "crypto_address_link": "https://basescan.org/address/{{address}}", "crypto_transaction_link": "https://basescan.org/tx/{{txId}}", "min_withdrawal_amount": "0.00002", "max_withdrawal_amount": "200", "network_confirmations": null, "processing_time_seconds": null}}, "BITCOIN": {"id": "bitcoin", "name": "Bitcoin", "active": true, "fee": null, "precision": null, "limits": {"withdraw": {"min": 0.0001, "max": 2400}}, "contract": null, "info": {"id": "bitcoin", "name": "Bitcoin", "status": "online", "contract_address": null, "crypto_address_link": "https://live.blockcypher.com/btc/address/{{address}}", "crypto_transaction_link": "https://live.blockcypher.com/btc/tx/{{txId}}", "min_withdrawal_amount": "0.0001", "max_withdrawal_amount": "2400", "network_confirmations": "2", "processing_time_seconds": null}}, "ETHEREUM": {"id": "ethereum", "name": "Ethereum", "active": true, "fee": null, "precision": null, "limits": {"withdraw": {"min": 2e-05, "max": 200}}, "contract": null, "info": {"id": "ethereum", "name": "Ethereum", "status": "online", "contract_address": "", "crypto_address_link": "https://etherscan.io/token/******************************************?a={{address}}", "crypto_transaction_link": "https://etherscan.io/tx/0x{{txId}}", "min_withdrawal_amount": "0.00002", "max_withdrawal_amount": "200", "network_confirmations": "35", "processing_time_seconds": null}}, "SOLANA": {"id": "solana", "name": "Solana", "active": true, "fee": null, "precision": null, "limits": {"withdraw": {"min": 2e-05, "max": 200}}, "contract": null, "info": {"id": "solana", "name": "Solana", "status": "online", "contract_address": "", "crypto_address_link": "https://explorer.solana.com/address/{{address}}", "crypto_transaction_link": "https://explorer.solana.com/tx/{{txId}}", "min_withdrawal_amount": "0.00002", "max_withdrawal_amount": "200", "network_confirmations": "31", "processing_time_seconds": null}}}, "limits": {"amount": {"min": null, "max": null}, "withdraw": {"min": 2e-05, "max": 2400}, "deposit": {"min": null, "max": null}}}, "USDT": {"info": {"id": "USDT", "name": "<PERSON><PERSON>", "min_size": "0.000001", "status": "online", "message": "", "max_precision": "0.000001", "convertible_to": [], "details": {"type": "crypto", "symbol": null, "network_confirmations": "14", "sort_order": "0", "crypto_address_link": "https://etherscan.io/token/******************************************?a={{address}}", "crypto_transaction_link": "https://etherscan.io/tx/0x{{txId}}", "push_payment_methods": [], "group_types": [], "display_name": null, "processing_time_seconds": null, "min_withdrawal_amount": "0.000001", "max_withdrawal_amount": "********"}, "default_network": "ethereum", "supported_networks": [{"id": "ethereum", "name": "Ethereum", "status": "online", "contract_address": "******************************************", "crypto_address_link": "https://etherscan.io/token/******************************************?a={{address}}", "crypto_transaction_link": "https://etherscan.io/tx/0x{{txId}}", "min_withdrawal_amount": "0.000001", "max_withdrawal_amount": "********", "network_confirmations": "14", "processing_time_seconds": null}], "display_name": "USDT"}, "id": "USDT", "numericId": null, "code": "USDT", "precision": 1e-06, "type": "crypto", "name": "<PERSON><PERSON>", "active": true, "deposit": null, "withdraw": null, "fee": null, "fees": {}, "networks": {"ETHEREUM": {"id": "ethereum", "name": "Ethereum", "active": true, "fee": null, "precision": null, "limits": {"withdraw": {"min": 1e-06, "max": ********}}, "contract": "******************************************", "info": {"id": "ethereum", "name": "Ethereum", "status": "online", "contract_address": "******************************************", "crypto_address_link": "https://etherscan.io/token/******************************************?a={{address}}", "crypto_transaction_link": "https://etherscan.io/tx/0x{{txId}}", "min_withdrawal_amount": "0.000001", "max_withdrawal_amount": "********", "network_confirmations": "14", "processing_time_seconds": null}}}, "limits": {"amount": {"min": null, "max": null}, "withdraw": {"min": 1e-06, "max": ********}, "deposit": {"min": null, "max": null}}}, "EUR": {"info": {"id": "EUR", "name": "Euro", "min_size": "0.01", "status": "online", "message": "", "max_precision": "0.01", "convertible_to": ["EURC"], "details": {"type": "fiat", "symbol": "€", "network_confirmations": null, "sort_order": "2", "crypto_address_link": null, "crypto_transaction_link": null, "push_payment_methods": ["sepa_bank_account"], "group_types": ["fiat", "eur"], "display_name": null, "processing_time_seconds": null, "min_withdrawal_amount": null, "max_withdrawal_amount": null}, "default_network": "", "supported_networks": [], "display_name": "EUR"}, "id": "EUR", "numericId": null, "code": "EUR", "precision": 0.01, "type": "fiat", "name": "Euro", "active": true, "deposit": null, "withdraw": null, "fee": null, "fees": {}, "networks": {}, "limits": {"amount": {"min": null, "max": null}, "withdraw": {"min": null, "max": null}}}, "USD": {"info": {"id": "USD", "name": "United States Dollar", "min_size": "0.01", "status": "online", "message": "", "max_precision": "0.01", "convertible_to": ["USDC", "PYUSD"], "details": {"type": "fiat", "symbol": "$", "network_confirmations": null, "sort_order": "1", "crypto_address_link": null, "crypto_transaction_link": null, "push_payment_methods": ["bank_wire", "fedwire", "swift_bank_account", "intra_bank_account"], "group_types": ["fiat", "usd"], "display_name": "US Dollar", "processing_time_seconds": null, "min_withdrawal_amount": null, "max_withdrawal_amount": null}, "default_network": "", "supported_networks": [], "display_name": "USD"}, "id": "USD", "numericId": null, "code": "USD", "precision": 0.01, "type": "fiat", "name": "United States Dollar", "active": true, "deposit": null, "withdraw": null, "fee": null, "fees": {}, "networks": {}, "limits": {"amount": {"min": null, "max": null}, "withdraw": {"min": null, "max": null}}}, "LTC": {"info": {"id": "LTC", "name": "Litecoin", "min_size": "0.********", "status": "online", "message": "", "max_precision": "0.********", "convertible_to": [], "details": {"type": "crypto", "symbol": "Ł", "network_confirmations": "6", "sort_order": "35", "crypto_address_link": "https://live.blockcypher.com/ltc/address/{{address}}", "crypto_transaction_link": "https://live.blockcypher.com/ltc/tx/{{txId}}", "push_payment_methods": ["crypto"], "group_types": [], "display_name": null, "processing_time_seconds": null, "min_withdrawal_amount": "0.001", "max_withdrawal_amount": "54400"}, "default_network": "litecoin", "supported_networks": [{"id": "litecoin", "name": "Litecoin", "status": "online", "contract_address": null, "crypto_address_link": "https://live.blockcypher.com/ltc/address/{{address}}", "crypto_transaction_link": "https://live.blockcypher.com/ltc/tx/{{txId}}", "min_withdrawal_amount": "0.001", "max_withdrawal_amount": "54400", "network_confirmations": "6", "processing_time_seconds": null}], "display_name": "LTC"}, "id": "LTC", "numericId": null, "code": "LTC", "precision": 1e-08, "type": "crypto", "name": "Litecoin", "active": true, "deposit": null, "withdraw": null, "fee": null, "fees": {}, "networks": {"LITECOIN": {"id": "litecoin", "name": "Litecoin", "active": true, "fee": null, "precision": null, "limits": {"withdraw": {"min": 0.001, "max": 54400}}, "contract": null, "info": {"id": "litecoin", "name": "Litecoin", "status": "online", "contract_address": null, "crypto_address_link": "https://live.blockcypher.com/ltc/address/{{address}}", "crypto_transaction_link": "https://live.blockcypher.com/ltc/tx/{{txId}}", "min_withdrawal_amount": "0.001", "max_withdrawal_amount": "54400", "network_confirmations": "6", "processing_time_seconds": null}}}, "limits": {"amount": {"min": null, "max": null}, "withdraw": {"min": 0.001, "max": 54400}, "deposit": {"min": null, "max": null}}}, "ETH": {"info": {"id": "ETH", "name": "<PERSON><PERSON>", "min_size": "0.********", "status": "online", "message": "", "max_precision": "0.********", "convertible_to": [], "details": {"type": "crypto", "symbol": null, "network_confirmations": "14", "sort_order": "25", "crypto_address_link": "https://etherscan.io/address/{{address}}", "crypto_transaction_link": "https://etherscan.io/tx/0x{{txId}}", "push_payment_methods": ["crypto"], "group_types": [], "display_name": null, "processing_time_seconds": null, "min_withdrawal_amount": "0.0001", "max_withdrawal_amount": "40000"}, "default_network": "ethereum", "supported_networks": [{"id": "arbitrum", "name": "Arbitrum", "status": "online", "contract_address": "", "crypto_address_link": "https://arbiscan.io/address/{{address}}", "crypto_transaction_link": "https://arbiscan.io/tx/{{txId}}", "min_withdrawal_amount": "0.0001", "max_withdrawal_amount": "133", "network_confirmations": null, "processing_time_seconds": null}, {"id": "base", "name": "Base", "status": "online", "contract_address": "", "crypto_address_link": "https://basescan.org/address/{{address}}", "crypto_transaction_link": "https://basescan.org/tx/{{txId}}", "min_withdrawal_amount": "0.0001", "max_withdrawal_amount": "1000", "network_confirmations": null, "processing_time_seconds": null}, {"id": "ethereum", "name": "Ethereum", "status": "online", "contract_address": null, "crypto_address_link": "https://etherscan.io/address/{{address}}", "crypto_transaction_link": "https://etherscan.io/tx/0x{{txId}}", "min_withdrawal_amount": "0.0001", "max_withdrawal_amount": "40000", "network_confirmations": "14", "processing_time_seconds": null}, {"id": "optimism", "name": "Optimism", "status": "online", "contract_address": "", "crypto_address_link": "https://optimistic.etherscan.io/address/{{address}}", "crypto_transaction_link": "https://optimistic.etherscan.io/tx/{{txId}}", "min_withdrawal_amount": "0.0001", "max_withdrawal_amount": "111", "network_confirmations": null, "processing_time_seconds": null}, {"id": "polygon", "name": "Polygon", "status": "online", "contract_address": "******************************************", "crypto_address_link": "https://polygonscan.com/address/{{address}}", "crypto_transaction_link": "https://polygonscan.com/tx/{{txId}}", "min_withdrawal_amount": "0.0001", "max_withdrawal_amount": "30", "network_confirmations": "128", "processing_time_seconds": null}, {"id": "unichain", "name": "<PERSON><PERSON><PERSON>", "status": "online", "contract_address": "", "crypto_address_link": "https://unichain-d7a86fxp.blockscout.com/address/{{address}}", "crypto_transaction_link": "https://unichain-d7a86fxp.blockscout.com/tx/{{txId}}", "min_withdrawal_amount": "0.0001", "max_withdrawal_amount": "111", "network_confirmations": null, "processing_time_seconds": null}], "display_name": "ETH"}, "id": "ETH", "numericId": null, "code": "ETH", "precision": 1e-08, "type": "crypto", "name": "<PERSON><PERSON>", "active": true, "deposit": null, "withdraw": null, "fee": null, "fees": {}, "networks": {"ARBITRUM": {"id": "arbitrum", "name": "Arbitrum", "active": true, "fee": null, "precision": null, "limits": {"withdraw": {"min": 0.0001, "max": 133}}, "contract": null, "info": {"id": "arbitrum", "name": "Arbitrum", "status": "online", "contract_address": "", "crypto_address_link": "https://arbiscan.io/address/{{address}}", "crypto_transaction_link": "https://arbiscan.io/tx/{{txId}}", "min_withdrawal_amount": "0.0001", "max_withdrawal_amount": "133", "network_confirmations": null, "processing_time_seconds": null}}, "BASE": {"id": "base", "name": "Base", "active": true, "fee": null, "precision": null, "limits": {"withdraw": {"min": 0.0001, "max": 1000}}, "contract": null, "info": {"id": "base", "name": "Base", "status": "online", "contract_address": "", "crypto_address_link": "https://basescan.org/address/{{address}}", "crypto_transaction_link": "https://basescan.org/tx/{{txId}}", "min_withdrawal_amount": "0.0001", "max_withdrawal_amount": "1000", "network_confirmations": null, "processing_time_seconds": null}}, "ETHEREUM": {"id": "ethereum", "name": "Ethereum", "active": true, "fee": null, "precision": null, "limits": {"withdraw": {"min": 0.0001, "max": 40000}}, "contract": null, "info": {"id": "ethereum", "name": "Ethereum", "status": "online", "contract_address": null, "crypto_address_link": "https://etherscan.io/address/{{address}}", "crypto_transaction_link": "https://etherscan.io/tx/0x{{txId}}", "min_withdrawal_amount": "0.0001", "max_withdrawal_amount": "40000", "network_confirmations": "14", "processing_time_seconds": null}}, "OPTIMISM": {"id": "optimism", "name": "Optimism", "active": true, "fee": null, "precision": null, "limits": {"withdraw": {"min": 0.0001, "max": 111}}, "contract": null, "info": {"id": "optimism", "name": "Optimism", "status": "online", "contract_address": "", "crypto_address_link": "https://optimistic.etherscan.io/address/{{address}}", "crypto_transaction_link": "https://optimistic.etherscan.io/tx/{{txId}}", "min_withdrawal_amount": "0.0001", "max_withdrawal_amount": "111", "network_confirmations": null, "processing_time_seconds": null}}, "POLYGON": {"id": "polygon", "name": "Polygon", "active": true, "fee": null, "precision": null, "limits": {"withdraw": {"min": 0.0001, "max": 30}}, "contract": "******************************************", "info": {"id": "polygon", "name": "Polygon", "status": "online", "contract_address": "******************************************", "crypto_address_link": "https://polygonscan.com/address/{{address}}", "crypto_transaction_link": "https://polygonscan.com/tx/{{txId}}", "min_withdrawal_amount": "0.0001", "max_withdrawal_amount": "30", "network_confirmations": "128", "processing_time_seconds": null}}, "UNICHAIN": {"id": "unichain", "name": "<PERSON><PERSON><PERSON>", "active": true, "fee": null, "precision": null, "limits": {"withdraw": {"min": 0.0001, "max": 111}}, "contract": null, "info": {"id": "unichain", "name": "<PERSON><PERSON><PERSON>", "status": "online", "contract_address": "", "crypto_address_link": "https://unichain-d7a86fxp.blockscout.com/address/{{address}}", "crypto_transaction_link": "https://unichain-d7a86fxp.blockscout.com/tx/{{txId}}", "min_withdrawal_amount": "0.0001", "max_withdrawal_amount": "111", "network_confirmations": null, "processing_time_seconds": null}}}, "limits": {"amount": {"min": null, "max": null}, "withdraw": {"min": 0.0001, "max": 40000}, "deposit": {"min": null, "max": null}}}, "ADA": {"info": {"id": "ADA", "name": "Cardano", "min_size": "1", "status": "online", "message": "", "max_precision": "0.1", "convertible_to": [], "details": {"type": "crypto", "symbol": null, "network_confirmations": "10", "sort_order": "28", "crypto_address_link": "https://explorer.cardano.org/en/address?address={{address}}", "crypto_transaction_link": "null", "push_payment_methods": ["crypto"], "group_types": [], "display_name": null, "processing_time_seconds": null, "min_withdrawal_amount": "1", "max_withdrawal_amount": "5000000"}, "default_network": "cardano", "supported_networks": [{"id": "cardano", "name": "Cardano", "status": "online", "contract_address": null, "crypto_address_link": "https://explorer.cardano.org/en/address?address={{address}}", "crypto_transaction_link": "", "min_withdrawal_amount": "1", "max_withdrawal_amount": "5000000", "network_confirmations": "10", "processing_time_seconds": null}], "display_name": "ADA"}, "id": "ADA", "numericId": null, "code": "ADA", "precision": 0.1, "type": "crypto", "name": "Cardano", "active": true, "deposit": null, "withdraw": null, "fee": null, "fees": {}, "networks": {"CARDANO": {"id": "cardano", "name": "Cardano", "active": true, "fee": null, "precision": null, "limits": {"withdraw": {"min": 1, "max": 5000000}}, "contract": null, "info": {"id": "cardano", "name": "Cardano", "status": "online", "contract_address": null, "crypto_address_link": "https://explorer.cardano.org/en/address?address={{address}}", "crypto_transaction_link": "", "min_withdrawal_amount": "1", "max_withdrawal_amount": "5000000", "network_confirmations": "10", "processing_time_seconds": null}}}, "limits": {"amount": {"min": null, "max": null}, "withdraw": {"min": 1, "max": 5000000}, "deposit": {"min": null, "max": null}}}, "XRP": {"info": {"id": "XRP", "name": "XRP", "min_size": "1", "status": "online", "message": "", "max_precision": "0.000001", "convertible_to": [], "details": {"type": "crypto", "symbol": "$", "network_confirmations": null, "sort_order": "27", "crypto_address_link": "https://bithomp.com/explorer/{{address}}", "crypto_transaction_link": "https://bithomp.com/explorer/{{txId}}", "push_payment_methods": ["crypto"], "group_types": [], "display_name": null, "processing_time_seconds": "600", "min_withdrawal_amount": "1", "max_withdrawal_amount": "10000000"}, "default_network": "ripple", "supported_networks": [{"id": "ripple", "name": "<PERSON><PERSON><PERSON>", "status": "online", "contract_address": null, "crypto_address_link": "https://bithomp.com/explorer/{{address}}", "crypto_transaction_link": "https://bithomp.com/explorer/{{txId}}", "min_withdrawal_amount": "1", "max_withdrawal_amount": "10000000", "network_confirmations": "0", "processing_time_seconds": null}], "display_name": "XRP"}, "id": "XRP", "numericId": null, "code": "XRP", "precision": 1e-06, "type": "crypto", "name": "XRP", "active": true, "deposit": null, "withdraw": null, "fee": null, "fees": {}, "networks": {"RIPPLE": {"id": "ripple", "name": "<PERSON><PERSON><PERSON>", "active": true, "fee": null, "precision": null, "limits": {"withdraw": {"min": 1, "max": 10000000}}, "contract": null, "info": {"id": "ripple", "name": "<PERSON><PERSON><PERSON>", "status": "online", "contract_address": null, "crypto_address_link": "https://bithomp.com/explorer/{{address}}", "crypto_transaction_link": "https://bithomp.com/explorer/{{txId}}", "min_withdrawal_amount": "1", "max_withdrawal_amount": "10000000", "network_confirmations": "0", "processing_time_seconds": null}}}, "limits": {"amount": {"min": null, "max": null}, "withdraw": {"min": 1, "max": 10000000}, "deposit": {"min": null, "max": null}}}, "USDC": {"info": {"id": "USDC", "name": "USDC", "min_size": "0.000001", "status": "online", "message": "", "max_precision": "0.000001", "convertible_to": ["USD"], "details": {"type": "crypto", "symbol": null, "network_confirmations": "14", "sort_order": "0", "crypto_address_link": "https://etherscan.io/token/******************************************?a={{address}}", "crypto_transaction_link": "https://etherscan.io/tx/0x{{txId}}", "push_payment_methods": [], "group_types": [], "display_name": null, "processing_time_seconds": null, "min_withdrawal_amount": "1", "max_withdrawal_amount": "*********"}, "default_network": "ethereum", "supported_networks": [{"id": "algorand", "name": "Algorand", "status": "online", "contract_address": "********", "crypto_address_link": "https://allo.info/account/{{address}}", "crypto_transaction_link": "https://allo.info/tx/{{txId}}", "min_withdrawal_amount": "1", "max_withdrawal_amount": "*********", "network_confirmations": null, "processing_time_seconds": null}, {"id": "aptos", "name": "Aptos", "status": "online", "contract_address": "", "crypto_address_link": "https://explorer.aptoslabs.com/account/{{address}}", "crypto_transaction_link": "https://explorer.aptoslabs.com/txn/0x{{txId}}", "min_withdrawal_amount": "1", "max_withdrawal_amount": "5000000", "network_confirmations": null, "processing_time_seconds": null}, {"id": "arbitrum", "name": "Arbitrum", "status": "online", "contract_address": "", "crypto_address_link": "https://arbiscan.io/address/{{address}}", "crypto_transaction_link": "https://arbiscan.io/tx/{{txId}}", "min_withdrawal_amount": "1", "max_withdrawal_amount": "5000000", "network_confirmations": null, "processing_time_seconds": null}, {"id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "Avalanche C-Chain", "status": "online", "contract_address": "******************************************", "crypto_address_link": "https://cchain.explorer.avax.network/address/{{address}}/transactions", "crypto_transaction_link": "https://cchain.explorer.avax.network/tx/{{txId}}/token-transfers", "min_withdrawal_amount": "1", "max_withdrawal_amount": "500000", "network_confirmations": "1", "processing_time_seconds": null}, {"id": "base", "name": "Base", "status": "online", "contract_address": "", "crypto_address_link": "https://basescan.org/address/{{address}}", "crypto_transaction_link": "https://basescan.org/tx/{{txId}}", "min_withdrawal_amount": "1", "max_withdrawal_amount": "15000000", "network_confirmations": null, "processing_time_seconds": null}, {"id": "ethereum", "name": "Ethereum", "status": "online", "contract_address": "******************************************", "crypto_address_link": "https://etherscan.io/token/******************************************?a={{address}}", "crypto_transaction_link": "https://etherscan.io/tx/0x{{txId}}", "min_withdrawal_amount": "1", "max_withdrawal_amount": "*********", "network_confirmations": "14", "processing_time_seconds": null}, {"id": "noble", "name": "<PERSON>", "status": "online", "contract_address": "", "crypto_address_link": "https://www.mintscan.io/noble/address/{{address}}", "crypto_transaction_link": "https://www.mintscan.io/noble/tx/{{txId}}", "min_withdrawal_amount": "1", "max_withdrawal_amount": "2000000", "network_confirmations": null, "processing_time_seconds": null}, {"id": "optimism", "name": "Optimism", "status": "online", "contract_address": "", "crypto_address_link": "https://optimistic.etherscan.io/token/******************************************?a={{address}}", "crypto_transaction_link": "https://optimistic.etherscan.io/tx/{{txId}}", "min_withdrawal_amount": "1", "max_withdrawal_amount": "1000000", "network_confirmations": null, "processing_time_seconds": null}, {"id": "polygon", "name": "Polygon", "status": "online", "contract_address": "******************************************", "crypto_address_link": "https://polygonscan.com/address/{{address}}", "crypto_transaction_link": "https://polygonscan.com/tx/{{txId}}", "min_withdrawal_amount": "1", "max_withdrawal_amount": "5000000", "network_confirmations": "128", "processing_time_seconds": null}, {"id": "solana", "name": "Solana", "status": "online", "contract_address": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v", "crypto_address_link": "https://explorer.solana.com/address/{{address}}", "crypto_transaction_link": "https://explorer.solana.com/tx/{{txId}}", "min_withdrawal_amount": "1", "max_withdrawal_amount": "10000000", "network_confirmations": "31", "processing_time_seconds": null}, {"id": "sui", "name": "<PERSON><PERSON>", "status": "online", "contract_address": "", "crypto_address_link": "https://explorer.sui.io/transaction/{{txId}}?network=mainnet", "crypto_transaction_link": "https://explorer.sui.io/addresses/{{address}}?network=mainnet", "min_withdrawal_amount": "1", "max_withdrawal_amount": "5000000", "network_confirmations": null, "processing_time_seconds": null}, {"id": "unichain", "name": "<PERSON><PERSON><PERSON>", "status": "online", "contract_address": "", "crypto_address_link": "https://uniscan.xyz/address/{{address}}", "crypto_transaction_link": "https://uniscan.xyz/tx/{{txId}}", "min_withdrawal_amount": "1", "max_withdrawal_amount": "1000000", "network_confirmations": null, "processing_time_seconds": null}], "display_name": "USDC"}, "id": "USDC", "numericId": null, "code": "USDC", "precision": 1e-06, "type": "crypto", "name": "USDC", "active": true, "deposit": null, "withdraw": null, "fee": null, "fees": {}, "networks": {"ALGORAND": {"id": "algorand", "name": "Algorand", "active": true, "fee": null, "precision": null, "limits": {"withdraw": {"min": 1, "max": *********}}, "contract": "********", "info": {"id": "algorand", "name": "Algorand", "status": "online", "contract_address": "********", "crypto_address_link": "https://allo.info/account/{{address}}", "crypto_transaction_link": "https://allo.info/tx/{{txId}}", "min_withdrawal_amount": "1", "max_withdrawal_amount": "*********", "network_confirmations": null, "processing_time_seconds": null}}, "APTOS": {"id": "aptos", "name": "Aptos", "active": true, "fee": null, "precision": null, "limits": {"withdraw": {"min": 1, "max": 5000000}}, "contract": null, "info": {"id": "aptos", "name": "Aptos", "status": "online", "contract_address": "", "crypto_address_link": "https://explorer.aptoslabs.com/account/{{address}}", "crypto_transaction_link": "https://explorer.aptoslabs.com/txn/0x{{txId}}", "min_withdrawal_amount": "1", "max_withdrawal_amount": "5000000", "network_confirmations": null, "processing_time_seconds": null}}, "ARBITRUM": {"id": "arbitrum", "name": "Arbitrum", "active": true, "fee": null, "precision": null, "limits": {"withdraw": {"min": 1, "max": 5000000}}, "contract": null, "info": {"id": "arbitrum", "name": "Arbitrum", "status": "online", "contract_address": "", "crypto_address_link": "https://arbiscan.io/address/{{address}}", "crypto_transaction_link": "https://arbiscan.io/tx/{{txId}}", "min_withdrawal_amount": "1", "max_withdrawal_amount": "5000000", "network_confirmations": null, "processing_time_seconds": null}}, "AVACCHAIN": {"id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "Avalanche C-Chain", "active": true, "fee": null, "precision": null, "limits": {"withdraw": {"min": 1, "max": 500000}}, "contract": "******************************************", "info": {"id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "Avalanche C-Chain", "status": "online", "contract_address": "******************************************", "crypto_address_link": "https://cchain.explorer.avax.network/address/{{address}}/transactions", "crypto_transaction_link": "https://cchain.explorer.avax.network/tx/{{txId}}/token-transfers", "min_withdrawal_amount": "1", "max_withdrawal_amount": "500000", "network_confirmations": "1", "processing_time_seconds": null}}, "BASE": {"id": "base", "name": "Base", "active": true, "fee": null, "precision": null, "limits": {"withdraw": {"min": 1, "max": 15000000}}, "contract": null, "info": {"id": "base", "name": "Base", "status": "online", "contract_address": "", "crypto_address_link": "https://basescan.org/address/{{address}}", "crypto_transaction_link": "https://basescan.org/tx/{{txId}}", "min_withdrawal_amount": "1", "max_withdrawal_amount": "15000000", "network_confirmations": null, "processing_time_seconds": null}}, "ETHEREUM": {"id": "ethereum", "name": "Ethereum", "active": true, "fee": null, "precision": null, "limits": {"withdraw": {"min": 1, "max": *********}}, "contract": "******************************************", "info": {"id": "ethereum", "name": "Ethereum", "status": "online", "contract_address": "******************************************", "crypto_address_link": "https://etherscan.io/token/******************************************?a={{address}}", "crypto_transaction_link": "https://etherscan.io/tx/0x{{txId}}", "min_withdrawal_amount": "1", "max_withdrawal_amount": "*********", "network_confirmations": "14", "processing_time_seconds": null}}, "NOBLE": {"id": "noble", "name": "<PERSON>", "active": true, "fee": null, "precision": null, "limits": {"withdraw": {"min": 1, "max": 2000000}}, "contract": null, "info": {"id": "noble", "name": "<PERSON>", "status": "online", "contract_address": "", "crypto_address_link": "https://www.mintscan.io/noble/address/{{address}}", "crypto_transaction_link": "https://www.mintscan.io/noble/tx/{{txId}}", "min_withdrawal_amount": "1", "max_withdrawal_amount": "2000000", "network_confirmations": null, "processing_time_seconds": null}}, "OPTIMISM": {"id": "optimism", "name": "Optimism", "active": true, "fee": null, "precision": null, "limits": {"withdraw": {"min": 1, "max": 1000000}}, "contract": null, "info": {"id": "optimism", "name": "Optimism", "status": "online", "contract_address": "", "crypto_address_link": "https://optimistic.etherscan.io/token/******************************************?a={{address}}", "crypto_transaction_link": "https://optimistic.etherscan.io/tx/{{txId}}", "min_withdrawal_amount": "1", "max_withdrawal_amount": "1000000", "network_confirmations": null, "processing_time_seconds": null}}, "POLYGON": {"id": "polygon", "name": "Polygon", "active": true, "fee": null, "precision": null, "limits": {"withdraw": {"min": 1, "max": 5000000}}, "contract": "******************************************", "info": {"id": "polygon", "name": "Polygon", "status": "online", "contract_address": "******************************************", "crypto_address_link": "https://polygonscan.com/address/{{address}}", "crypto_transaction_link": "https://polygonscan.com/tx/{{txId}}", "min_withdrawal_amount": "1", "max_withdrawal_amount": "5000000", "network_confirmations": "128", "processing_time_seconds": null}}, "SOLANA": {"id": "solana", "name": "Solana", "active": true, "fee": null, "precision": null, "limits": {"withdraw": {"min": 1, "max": 10000000}}, "contract": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v", "info": {"id": "solana", "name": "Solana", "status": "online", "contract_address": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v", "crypto_address_link": "https://explorer.solana.com/address/{{address}}", "crypto_transaction_link": "https://explorer.solana.com/tx/{{txId}}", "min_withdrawal_amount": "1", "max_withdrawal_amount": "10000000", "network_confirmations": "31", "processing_time_seconds": null}}, "SUI": {"id": "sui", "name": "<PERSON><PERSON>", "active": true, "fee": null, "precision": null, "limits": {"withdraw": {"min": 1, "max": 5000000}}, "contract": null, "info": {"id": "sui", "name": "<PERSON><PERSON>", "status": "online", "contract_address": "", "crypto_address_link": "https://explorer.sui.io/transaction/{{txId}}?network=mainnet", "crypto_transaction_link": "https://explorer.sui.io/addresses/{{address}}?network=mainnet", "min_withdrawal_amount": "1", "max_withdrawal_amount": "5000000", "network_confirmations": null, "processing_time_seconds": null}}, "UNICHAIN": {"id": "unichain", "name": "<PERSON><PERSON><PERSON>", "active": true, "fee": null, "precision": null, "limits": {"withdraw": {"min": 1, "max": 1000000}}, "contract": null, "info": {"id": "unichain", "name": "<PERSON><PERSON><PERSON>", "status": "online", "contract_address": "", "crypto_address_link": "https://uniscan.xyz/address/{{address}}", "crypto_transaction_link": "https://uniscan.xyz/tx/{{txId}}", "min_withdrawal_amount": "1", "max_withdrawal_amount": "1000000", "network_confirmations": null, "processing_time_seconds": null}}}, "limits": {"amount": {"min": null, "max": null}, "withdraw": {"min": 1, "max": *********}, "deposit": {"min": null, "max": null}}}}