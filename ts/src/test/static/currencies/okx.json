{"BTC": {"code": "BTC", "id": "BTC", "name": "Bitcoin", "active": true, "deposit": true, "withdraw": true, "precision": 1e-08, "limits": {"amount": {}}, "networks": {"BTC": {"id": "BTC-Bitcoin", "network": "BTC", "active": true, "deposit": true, "withdraw": true, "fee": 7e-05, "precision": 1e-08, "limits": {"withdraw": {"min": 0.0005, "max": 500}}, "info": {"burningFeeRate": "", "canDep": true, "canInternal": true, "canWd": true, "ccy": "BTC", "chain": "BTC-Bitcoin", "ctAddr": "", "depEstOpenTime": "", "depQuotaFixed": "", "depQuoteDailyLayer2": "", "fee": "0.00007", "logoLink": "https://static.coinall.ltd/cdn/oksupport/asset/currency/icon/btc20230419112752.png", "mainNet": true, "maxFee": "0.00007", "maxFeeForCtAddr": "", "maxWd": "500", "minDep": "0.0005", "minDepArrivalConfirm": "1", "minFee": "0.00007", "minFeeForCtAddr": "", "minInternal": "0.0001", "minWd": "0.0005", "minWdUnlockConfirm": "2", "name": "Bitcoin", "needTag": false, "usedDepQuotaFixed": "", "usedWdQuota": "0", "wdEstOpenTime": "", "wdQuota": "10000000", "wdTickSz": "8"}}, "BTCLN": {"id": "BTC-Lightning", "network": "BTCLN", "active": true, "deposit": true, "withdraw": true, "fee": 1e-06, "precision": 1e-08, "limits": {"withdraw": {"min": 1e-06, "max": 0.1}}, "info": {"burningFeeRate": "", "canDep": true, "canInternal": true, "canWd": true, "ccy": "BTC", "chain": "BTC-Lightning", "ctAddr": "", "depEstOpenTime": "", "depQuotaFixed": "", "depQuoteDailyLayer2": "", "fee": "0.000001", "logoLink": "https://static.coinall.ltd/cdn/oksupport/asset/currency/icon/btc20230419112752.png", "mainNet": false, "maxFee": "0.000001", "maxFeeForCtAddr": "", "maxWd": "0.1", "minDep": "0.000001", "minDepArrivalConfirm": "1", "minFee": "0.000001", "minFeeForCtAddr": "", "minInternal": "0.0001", "minWd": "0.000001", "minWdUnlockConfirm": "1", "name": "Bitcoin", "needTag": false, "usedDepQuotaFixed": "", "usedWdQuota": "0", "wdEstOpenTime": "", "wdQuota": "20000", "wdTickSz": "8"}}, "OKTC": {"id": "BTCK-OKTC", "network": "OKTC", "active": false, "deposit": true, "withdraw": false, "fee": 0, "precision": 1e-08, "limits": {"withdraw": {"min": 0, "max": 500}}, "info": {"burningFeeRate": "", "canDep": true, "canInternal": true, "canWd": false, "ccy": "BTC", "chain": "BTCK-OKTC", "ctAddr": "******************************************", "depEstOpenTime": "", "depQuotaFixed": "", "depQuoteDailyLayer2": "", "fee": "0", "logoLink": "https://static.coinall.ltd/cdn/oksupport/asset/currency/icon/btc20230419112752.png", "mainNet": false, "maxFee": "0", "maxFeeForCtAddr": "", "maxWd": "500", "minDep": "0.00000001", "minDepArrivalConfirm": "2", "minFee": "0", "minFeeForCtAddr": "", "minInternal": "0.0001", "minWd": "0", "minWdUnlockConfirm": "4", "name": "Bitcoin", "needTag": false, "usedDepQuotaFixed": "", "usedWdQuota": "0", "wdEstOpenTime": "4090615200000", "wdQuota": "10000000", "wdTickSz": "8"}}}}, "ETH": {"code": "ETH", "id": "ETH", "name": "Ethereum", "active": true, "deposit": true, "withdraw": true, "precision": 1e-08, "limits": {"amount": {}}, "networks": {"ETH": {"id": "ETH-ERC20", "network": "ETH", "active": true, "deposit": true, "withdraw": true, "fee": 0.00084, "precision": 1e-08, "limits": {"withdraw": {"min": 0.01, "max": 14600}}, "info": {"burningFeeRate": "", "canDep": true, "canInternal": true, "canWd": true, "ccy": "ETH", "chain": "ETH-ERC20", "ctAddr": "", "depEstOpenTime": "", "depQuotaFixed": "", "depQuoteDailyLayer2": "", "fee": "0.00084", "logoLink": "https://static.coinall.ltd/cdn/oksupport/asset/currency/icon/eth20230419112854.png", "mainNet": true, "maxFee": "0.00084", "maxFeeForCtAddr": "", "maxWd": "14600", "minDep": "0.001", "minDepArrivalConfirm": "32", "minFee": "0.00084", "minFeeForCtAddr": "", "minInternal": "0.01", "minWd": "0.01", "minWdUnlockConfirm": "96", "name": "Ethereum", "needTag": false, "usedDepQuotaFixed": "", "usedWdQuota": "0", "wdEstOpenTime": "", "wdQuota": "10000000", "wdTickSz": "8"}}, "ARBONE": {"id": "ETH-Arbitrum One", "network": "ARBONE", "active": true, "deposit": true, "withdraw": true, "fee": 4e-05, "precision": 1e-08, "limits": {"withdraw": {"min": 0.001, "max": 5840}}, "info": {"burningFeeRate": "", "canDep": true, "canInternal": true, "canWd": true, "ccy": "ETH", "chain": "ETH-Arbitrum One", "ctAddr": "", "depEstOpenTime": "", "depQuotaFixed": "", "depQuoteDailyLayer2": "200000", "fee": "0.00004", "logoLink": "https://static.coinall.ltd/cdn/oksupport/asset/currency/icon/eth20230419112854.png", "mainNet": false, "maxFee": "0.00004", "maxFeeForCtAddr": "", "maxWd": "5840", "minDep": "0.00000001", "minDepArrivalConfirm": "1850", "minFee": "0.00004", "minFeeForCtAddr": "", "minInternal": "0.01", "minWd": "0.001", "minWdUnlockConfirm": "1850", "name": "Ethereum", "needTag": false, "usedDepQuotaFixed": "", "usedWdQuota": "0", "wdEstOpenTime": "", "wdQuota": "10000000", "wdTickSz": "8"}}, "Base": {"id": "ETH-Base", "network": "Base", "active": true, "deposit": true, "withdraw": true, "fee": 4e-05, "precision": 1e-08, "limits": {"withdraw": {"min": 0.002, "max": 2920}}, "info": {"burningFeeRate": "", "canDep": true, "canInternal": true, "canWd": true, "ccy": "ETH", "chain": "ETH-Base", "ctAddr": "", "depEstOpenTime": "", "depQuotaFixed": "", "depQuoteDailyLayer2": "50000", "fee": "0.00004", "logoLink": "https://static.coinall.ltd/cdn/oksupport/asset/currency/icon/eth20230419112854.png", "mainNet": false, "maxFee": "0.00004", "maxFeeForCtAddr": "", "maxWd": "2920", "minDep": "0.00000001", "minDepArrivalConfirm": "120", "minFee": "0.00004", "minFeeForCtAddr": "", "minInternal": "0.01", "minWd": "0.002", "minWdUnlockConfirm": "120", "name": "Ethereum", "needTag": false, "usedDepQuotaFixed": "", "usedWdQuota": "0", "wdEstOpenTime": "", "wdQuota": "10000000", "wdTickSz": "8"}}, "Linea": {"id": "ETH-Linea", "network": "Linea", "active": true, "deposit": true, "withdraw": true, "fee": 0.0002, "precision": 1e-08, "limits": {"withdraw": {"min": 0.0001, "max": 2920}}, "info": {"burningFeeRate": "", "canDep": true, "canInternal": true, "canWd": true, "ccy": "ETH", "chain": "ETH-Linea", "ctAddr": "", "depEstOpenTime": "", "depQuotaFixed": "", "depQuoteDailyLayer2": "50000", "fee": "0.0002", "logoLink": "https://static.coinall.ltd/cdn/oksupport/asset/currency/icon/eth20230419112854.png", "mainNet": false, "maxFee": "0.0002", "maxFeeForCtAddr": "", "maxWd": "2920", "minDep": "0.00000001", "minDepArrivalConfirm": "5000", "minFee": "0.0002", "minFeeForCtAddr": "", "minInternal": "0.01", "minWd": "0.0001", "minWdUnlockConfirm": "5000", "name": "Ethereum", "needTag": false, "usedDepQuotaFixed": "", "usedWdQuota": "0", "wdEstOpenTime": "", "wdQuota": "10000000", "wdTickSz": "8"}}, "OKTC": {"id": "ETHK-OKTC", "network": "OKTC", "active": false, "deposit": true, "withdraw": false, "fee": 0, "precision": 1e-08, "limits": {"withdraw": {"min": 0, "max": 14600}}, "info": {"burningFeeRate": "", "canDep": true, "canInternal": true, "canWd": false, "ccy": "ETH", "chain": "ETHK-OKTC", "ctAddr": "******************************************", "depEstOpenTime": "", "depQuotaFixed": "", "depQuoteDailyLayer2": "", "fee": "0", "logoLink": "https://static.coinall.ltd/cdn/oksupport/asset/currency/icon/eth20230419112854.png", "mainNet": false, "maxFee": "0", "maxFeeForCtAddr": "", "maxWd": "14600", "minDep": "0.00000001", "minDepArrivalConfirm": "2", "minFee": "0", "minFeeForCtAddr": "", "minInternal": "0.01", "minWd": "0", "minWdUnlockConfirm": "4", "name": "Ethereum", "needTag": false, "usedDepQuotaFixed": "", "usedWdQuota": "0", "wdEstOpenTime": "4090615200000", "wdQuota": "10000000", "wdTickSz": "8"}}, "OPTIMISM": {"id": "ETH-Optimism", "network": "OPTIMISM", "active": true, "deposit": true, "withdraw": true, "fee": 4e-05, "precision": 1e-08, "limits": {"withdraw": {"min": 0.0001, "max": 5840}}, "info": {"burningFeeRate": "", "canDep": true, "canInternal": true, "canWd": true, "ccy": "ETH", "chain": "ETH-Optimism", "ctAddr": " ", "depEstOpenTime": "", "depQuotaFixed": "", "depQuoteDailyLayer2": "100000", "fee": "0.00004", "logoLink": "https://static.coinall.ltd/cdn/oksupport/asset/currency/icon/eth20230419112854.png", "mainNet": false, "maxFee": "0.00004", "maxFeeForCtAddr": "", "maxWd": "5840", "minDep": "0.00000001", "minDepArrivalConfirm": "450", "minFee": "0.00004", "minFeeForCtAddr": "", "minInternal": "0.01", "minWd": "0.0001", "minWdUnlockConfirm": "450", "name": "Ethereum", "needTag": false, "usedDepQuotaFixed": "", "usedWdQuota": "0", "wdEstOpenTime": "", "wdQuota": "10000000", "wdTickSz": "8"}}, "Starknet": {"id": "ETH-Starknet", "network": "Stark<PERSON>", "active": true, "deposit": true, "withdraw": true, "fee": 9.5e-05, "precision": 1e-08, "limits": {"withdraw": {"min": 1e-05, "max": 5840}}, "info": {"burningFeeRate": "", "canDep": true, "canInternal": true, "canWd": true, "ccy": "ETH", "chain": "ETH-Starknet", "ctAddr": "0x049d36570d4e46f48e99674bd3fcc84644ddd6b96f7c741b1562b82f9e004dc7", "depEstOpenTime": "", "depQuotaFixed": "", "depQuoteDailyLayer2": "100000", "fee": "0.000095", "logoLink": "https://static.coinall.ltd/cdn/oksupport/asset/currency/icon/eth20230419112854.png", "mainNet": false, "maxFee": "0.000095", "maxFeeForCtAddr": "", "maxWd": "5840", "minDep": "0.0008", "minDepArrivalConfirm": "5", "minFee": "0.000095", "minFeeForCtAddr": "", "minInternal": "0.01", "minWd": "0.00001", "minWdUnlockConfirm": "5", "name": "Ethereum", "needTag": false, "usedDepQuotaFixed": "", "usedWdQuota": "0", "wdEstOpenTime": "", "wdQuota": "10000000", "wdTickSz": "8"}}, "X Layer": {"id": "ETH-X Layer", "network": "X Layer", "active": true, "deposit": true, "withdraw": true, "fee": 3e-05, "precision": 1e-08, "limits": {"withdraw": {"min": 0.0001, "max": 4380}}, "info": {"burningFeeRate": "", "canDep": true, "canInternal": true, "canWd": true, "ccy": "ETH", "chain": "ETH-X Layer", "ctAddr": "******************************************", "depEstOpenTime": "", "depQuotaFixed": "", "depQuoteDailyLayer2": "", "fee": "0.00003", "logoLink": "https://static.coinall.ltd/cdn/oksupport/asset/currency/icon/eth20230419112854.png", "mainNet": false, "maxFee": "0.00003", "maxFeeForCtAddr": "", "maxWd": "4380", "minDep": "0.00000001", "minDepArrivalConfirm": "120", "minFee": "0.00003", "minFeeForCtAddr": "", "minInternal": "0.01", "minWd": "0.0001", "minWdUnlockConfirm": "120", "name": "Ethereum", "needTag": false, "usedDepQuotaFixed": "", "usedWdQuota": "0", "wdEstOpenTime": "", "wdQuota": "10000000", "wdTickSz": "8"}}, "zkSync Era": {"id": "ETH-zkSync Era", "network": "zkSync Era", "active": true, "deposit": true, "withdraw": true, "fee": 4.1e-05, "precision": 1e-08, "limits": {"withdraw": {"min": 0.0001, "max": 5840}}, "info": {"burningFeeRate": "", "canDep": true, "canInternal": true, "canWd": true, "ccy": "ETH", "chain": "ETH-zkSync Era", "ctAddr": "", "depEstOpenTime": "", "depQuotaFixed": "", "depQuoteDailyLayer2": "", "fee": "0.000041", "logoLink": "https://static.coinall.ltd/cdn/oksupport/asset/currency/icon/eth20230419112854.png", "mainNet": false, "maxFee": "0.000041", "maxFeeForCtAddr": "", "maxWd": "5840", "minDep": "0.00000001", "minDepArrivalConfirm": "12", "minFee": "0.000041", "minFeeForCtAddr": "", "minInternal": "0.01", "minWd": "0.0001", "minWdUnlockConfirm": "30", "name": "Ethereum", "needTag": false, "usedDepQuotaFixed": "", "usedWdQuota": "0", "wdEstOpenTime": "", "wdQuota": "10000000", "wdTickSz": "8"}}}}, "USDT": {"code": "USDT", "id": "USDT", "name": "<PERSON><PERSON>", "active": true, "deposit": true, "withdraw": true, "precision": 1e-08, "limits": {"amount": {}}, "networks": {"TRC20": {"id": "USDT-TRC20", "network": "TRC20", "active": true, "deposit": true, "withdraw": true, "fee": 1.5, "precision": 1e-06, "limits": {"withdraw": {"min": 2, "max": 48464600}}, "info": {"burningFeeRate": "", "canDep": true, "canInternal": true, "canWd": true, "ccy": "USDT", "chain": "USDT-TRC20", "ctAddr": "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t", "depEstOpenTime": "", "depQuotaFixed": "", "depQuoteDailyLayer2": "", "fee": "1.5", "logoLink": "https://static.coinall.ltd/cdn/oksupport/asset/currency/icon/usdt20240813135750.png", "mainNet": false, "maxFee": "1.5", "maxFeeForCtAddr": "", "maxWd": "48464600", "minDep": "0.00000001", "minDepArrivalConfirm": "19", "minFee": "1.5", "minFeeForCtAddr": "", "minInternal": "0.01", "minWd": "2", "minWdUnlockConfirm": "38", "name": "<PERSON><PERSON>", "needTag": false, "usedDepQuotaFixed": "", "usedWdQuota": "0", "wdEstOpenTime": "", "wdQuota": "10000000", "wdTickSz": "6"}}, "ERC20": {"id": "USDT-ERC20", "network": "ERC20", "active": true, "deposit": true, "withdraw": true, "fee": 2.77, "precision": 1e-06, "limits": {"withdraw": {"min": 2, "max": 48464600}}, "info": {"burningFeeRate": "", "canDep": true, "canInternal": true, "canWd": true, "ccy": "USDT", "chain": "USDT-ERC20", "ctAddr": "0xdac17f958d2ee523a2206206994597c13d831ec7", "depEstOpenTime": "", "depQuotaFixed": "", "depQuoteDailyLayer2": "", "fee": "2.77", "logoLink": "https://static.coinall.ltd/cdn/oksupport/asset/currency/icon/usdt20240813135750.png", "mainNet": false, "maxFee": "2.77", "maxFeeForCtAddr": "", "maxWd": "48464600", "minDep": "0.00000001", "minDepArrivalConfirm": "32", "minFee": "2.77", "minFeeForCtAddr": "", "minInternal": "0.01", "minWd": "2", "minWdUnlockConfirm": "96", "name": "<PERSON><PERSON>", "needTag": false, "usedDepQuotaFixed": "", "usedWdQuota": "0", "wdEstOpenTime": "", "wdQuota": "10000000", "wdTickSz": "6"}}, "ARBONE": {"id": "USDT-Arbitrum One", "network": "ARBONE", "active": true, "deposit": true, "withdraw": true, "fee": 0.098, "precision": 1e-06, "limits": {"withdraw": {"min": 0.1, "max": 48464600}}, "info": {"burningFeeRate": "", "canDep": true, "canInternal": true, "canWd": true, "ccy": "USDT", "chain": "USDT-Arbitrum One", "ctAddr": "0xfd086bc7cd5c481dcc9c85ebe478a1c0b69fcbb9", "depEstOpenTime": "", "depQuotaFixed": "", "depQuoteDailyLayer2": "200000", "fee": "0.098", "logoLink": "https://static.coinall.ltd/cdn/oksupport/asset/currency/icon/usdt20240813135750.png", "mainNet": false, "maxFee": "0.098", "maxFeeForCtAddr": "", "maxWd": "48464600", "minDep": "0.00000001", "minDepArrivalConfirm": "1850", "minFee": "0.098", "minFeeForCtAddr": "", "minInternal": "0.01", "minWd": "0.1", "minWdUnlockConfirm": "1850", "name": "<PERSON><PERSON>", "needTag": false, "usedDepQuotaFixed": "", "usedWdQuota": "0", "wdEstOpenTime": "", "wdQuota": "10000000", "wdTickSz": "6"}}, "AVAXC": {"id": "USDT-Avalanche C-Chain", "network": "AVAXC", "active": true, "deposit": true, "withdraw": true, "fee": 0.22, "precision": 1e-06, "limits": {"withdraw": {"min": 0.01, "max": 9692920}}, "info": {"burningFeeRate": "", "canDep": true, "canInternal": true, "canWd": true, "ccy": "USDT", "chain": "USDT-Avalanche C-Chain", "ctAddr": "0x9702230a8ea53601f5cd2dc00fdbc13d4df4a8c7", "depEstOpenTime": "", "depQuotaFixed": "", "depQuoteDailyLayer2": "", "fee": "0.22", "logoLink": "https://static.coinall.ltd/cdn/oksupport/asset/currency/icon/usdt20240813135750.png", "mainNet": false, "maxFee": "0.22", "maxFeeForCtAddr": "", "maxWd": "9692920", "minDep": "0.00000001", "minDepArrivalConfirm": "30", "minFee": "0.22", "minFeeForCtAddr": "", "minInternal": "0.01", "minWd": "0.01", "minWdUnlockConfirm": "120", "name": "<PERSON><PERSON>", "needTag": false, "usedDepQuotaFixed": "", "usedWdQuota": "0", "wdEstOpenTime": "", "wdQuota": "10000000", "wdTickSz": "6"}}, "OKTC": {"id": "USDT-OKTC", "network": "OKTC", "active": false, "deposit": true, "withdraw": false, "fee": 0, "precision": 1e-08, "limits": {"withdraw": {"min": 0, "max": 48464600}}, "info": {"burningFeeRate": "", "canDep": true, "canInternal": true, "canWd": false, "ccy": "USDT", "chain": "USDT-OKTC", "ctAddr": "0x382bb369d343125bfb2117af9c149795c6c65c50", "depEstOpenTime": "", "depQuotaFixed": "", "depQuoteDailyLayer2": "", "fee": "0", "logoLink": "https://static.coinall.ltd/cdn/oksupport/asset/currency/icon/usdt20240813135750.png", "mainNet": false, "maxFee": "0", "maxFeeForCtAddr": "", "maxWd": "48464600", "minDep": "0.00000001", "minDepArrivalConfirm": "2", "minFee": "0", "minFeeForCtAddr": "", "minInternal": "0.01", "minWd": "0", "minWdUnlockConfirm": "4", "name": "<PERSON><PERSON>", "needTag": false, "usedDepQuotaFixed": "", "usedWdQuota": "0", "wdEstOpenTime": "4090615200000", "wdQuota": "10000000", "wdTickSz": "8"}}, "OPTIMISM": {"id": "USDT-Optimism", "network": "OPTIMISM", "active": true, "deposit": true, "withdraw": true, "fee": 0.15, "precision": 1e-06, "limits": {"withdraw": {"min": 1, "max": 48464600}}, "info": {"burningFeeRate": "", "canDep": true, "canInternal": true, "canWd": true, "ccy": "USDT", "chain": "USDT-Optimism", "ctAddr": "0x94b008aa00579c1307b0ef2c499ad98a8ce58e58", "depEstOpenTime": "", "depQuotaFixed": "", "depQuoteDailyLayer2": "200000", "fee": "0.15", "logoLink": "https://static.coinall.ltd/cdn/oksupport/asset/currency/icon/usdt20240813135750.png", "mainNet": false, "maxFee": "0.15", "maxFeeForCtAddr": "", "maxWd": "48464600", "minDep": "0.05", "minDepArrivalConfirm": "450", "minFee": "0.15", "minFeeForCtAddr": "", "minInternal": "0.01", "minWd": "1", "minWdUnlockConfirm": "450", "name": "<PERSON><PERSON>", "needTag": false, "usedDepQuotaFixed": "", "usedWdQuota": "0", "wdEstOpenTime": "", "wdQuota": "10000000", "wdTickSz": "6"}}, "MATIC": {"id": "USDT-Polygon", "network": "MATIC", "active": true, "deposit": true, "withdraw": true, "fee": 0.8, "precision": 1e-06, "limits": {"withdraw": {"min": 0.01, "max": 48464600}}, "info": {"burningFeeRate": "", "canDep": true, "canInternal": true, "canWd": true, "ccy": "USDT", "chain": "USDT-Polygon", "ctAddr": "0xc2132d05d31c914a87c6611c10748aeb04b58e8f", "depEstOpenTime": "", "depQuotaFixed": "", "depQuoteDailyLayer2": "", "fee": "0.8", "logoLink": "https://static.coinall.ltd/cdn/oksupport/asset/currency/icon/usdt20240813135750.png", "mainNet": false, "maxFee": "0.8", "maxFeeForCtAddr": "", "maxWd": "48464600", "minDep": "0.00000001", "minDepArrivalConfirm": "300", "minFee": "0.8", "minFeeForCtAddr": "", "minInternal": "0.01", "minWd": "0.01", "minWdUnlockConfirm": "800", "name": "<PERSON><PERSON>", "needTag": false, "usedDepQuotaFixed": "", "usedWdQuota": "0", "wdEstOpenTime": "", "wdQuota": "10000000", "wdTickSz": "6"}}, "SOL": {"id": "USDT-Solana", "network": "SOL", "active": true, "deposit": true, "withdraw": true, "fee": 1, "precision": 1e-06, "limits": {"withdraw": {"min": 0.1, "max": 48464600}}, "info": {"burningFeeRate": "", "canDep": true, "canInternal": true, "canWd": true, "ccy": "USDT", "chain": "USDT-Solana", "ctAddr": "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB", "depEstOpenTime": "", "depQuotaFixed": "", "depQuoteDailyLayer2": "", "fee": "1", "logoLink": "https://static.coinall.ltd/cdn/oksupport/asset/currency/icon/usdt20240813135750.png", "mainNet": false, "maxFee": "1", "maxFeeForCtAddr": "", "maxWd": "48464600", "minDep": "0.00000001", "minDepArrivalConfirm": "200", "minFee": "1", "minFeeForCtAddr": "", "minInternal": "0.01", "minWd": "0.1", "minWdUnlockConfirm": "200", "name": "<PERSON><PERSON>", "needTag": false, "usedDepQuotaFixed": "", "usedWdQuota": "0", "wdEstOpenTime": "", "wdQuota": "10000000", "wdTickSz": "6"}}, "TON": {"id": "USDT-TON", "network": "TON", "active": true, "deposit": true, "withdraw": true, "fee": 0.15, "precision": 1e-06, "limits": {"withdraw": {"min": 0.0001, "max": 9692920}}, "info": {"burningFeeRate": "", "canDep": true, "canInternal": true, "canWd": true, "ccy": "USDT", "chain": "USDT-TON", "ctAddr": "EQCxE6mUtQJKFnGfaROTKOt1lZbDiiX1kCixRv7Nw2Id_sDs", "depEstOpenTime": "", "depQuotaFixed": "", "depQuoteDailyLayer2": "", "fee": "0.15", "logoLink": "https://static.coinall.ltd/cdn/oksupport/asset/currency/icon/usdt20240813135750.png", "mainNet": false, "maxFee": "0.15", "maxFeeForCtAddr": "", "maxWd": "9692920", "minDep": "0.00000001", "minDepArrivalConfirm": "50", "minFee": "0.15", "minFeeForCtAddr": "", "minInternal": "0.01", "minWd": "0.0001", "minWdUnlockConfirm": "150", "name": "<PERSON><PERSON>", "needTag": false, "usedDepQuotaFixed": "", "usedWdQuota": "0", "wdEstOpenTime": "", "wdQuota": "10000000", "wdTickSz": "6"}}, "X Layer": {"id": "USDT-X Layer", "network": "X Layer", "active": true, "deposit": true, "withdraw": true, "fee": 0.1, "precision": 1e-06, "limits": {"withdraw": {"min": 0.0001, "max": 14539380}}, "info": {"burningFeeRate": "", "canDep": true, "canInternal": true, "canWd": true, "ccy": "USDT", "chain": "USDT-X Layer", "ctAddr": "0x1E4a5963aBFD975d8c9021ce480b42188849D41d", "depEstOpenTime": "", "depQuotaFixed": "", "depQuoteDailyLayer2": "", "fee": "0.1", "logoLink": "https://static.coinall.ltd/cdn/oksupport/asset/currency/icon/usdt20240813135750.png", "mainNet": false, "maxFee": "0.1", "maxFeeForCtAddr": "", "maxWd": "14539380", "minDep": "0.00000001", "minDepArrivalConfirm": "120", "minFee": "0.1", "minFeeForCtAddr": "", "minInternal": "0.01", "minWd": "0.0001", "minWdUnlockConfirm": "120", "name": "<PERSON><PERSON>", "needTag": false, "usedDepQuotaFixed": "", "usedWdQuota": "0", "wdEstOpenTime": "", "wdQuota": "10000000", "wdTickSz": "6"}}}}, "USDC": {"code": "USDC", "id": "USDC", "name": "USD Coin", "active": true, "deposit": true, "withdraw": true, "precision": 1e-08, "limits": {"amount": {}}, "networks": {"ERC20": {"id": "USDC-ERC20", "network": "ERC20", "active": true, "deposit": true, "withdraw": true, "fee": 2.77, "precision": 1e-06, "limits": {"withdraw": {"min": 1, "max": 48517970}}, "info": {"burningFeeRate": "", "canDep": true, "canInternal": true, "canWd": true, "ccy": "USDC", "chain": "USDC-ERC20", "ctAddr": "0xa0b86991c6218b36c1d19d4a2e9eb0ce3606eb48", "depEstOpenTime": "", "depQuotaFixed": "", "depQuoteDailyLayer2": "", "fee": "2.77", "logoLink": "https://static.coinall.ltd/cdn/oksupport/asset/currency/icon/usdc20230419113033.png", "mainNet": true, "maxFee": "2.77", "maxFeeForCtAddr": "", "maxWd": "48517970", "minDep": "0.00000001", "minDepArrivalConfirm": "32", "minFee": "2.77", "minFeeForCtAddr": "", "minInternal": "1", "minWd": "1", "minWdUnlockConfirm": "96", "name": "USD Coin", "needTag": false, "usedDepQuotaFixed": "", "usedWdQuota": "0", "wdEstOpenTime": "", "wdQuota": "10000000", "wdTickSz": "6"}}, "ARBONE": {"id": "USDC-Arbitrum One", "network": "ARBONE", "active": true, "deposit": true, "withdraw": true, "fee": 0.098, "precision": 1e-06, "limits": {"withdraw": {"min": 0.1, "max": 29110782}}, "info": {"burningFeeRate": "", "canDep": true, "canInternal": true, "canWd": true, "ccy": "USDC", "chain": "USDC-Arbitrum One", "ctAddr": "0xaf88d065e77c8cC2239327C5EDb3A432268e5831", "depEstOpenTime": "", "depQuotaFixed": "", "depQuoteDailyLayer2": "200000", "fee": "0.098", "logoLink": "https://static.coinall.ltd/cdn/oksupport/asset/currency/icon/usdc20230419113033.png", "mainNet": false, "maxFee": "0.098", "maxFeeForCtAddr": "", "maxWd": "29110782", "minDep": "0.00000001", "minDepArrivalConfirm": "1850", "minFee": "0.098", "minFeeForCtAddr": "", "minInternal": "1", "minWd": "0.1", "minWdUnlockConfirm": "1850", "name": "USD Coin", "needTag": false, "usedDepQuotaFixed": "", "usedWdQuota": "0", "wdEstOpenTime": "", "wdQuota": "10000000", "wdTickSz": "6"}}, "AVAXC": {"id": "USDC-Avalanche C-Chain", "network": "AVAXC", "active": true, "deposit": true, "withdraw": true, "fee": 0.8, "precision": 1e-06, "limits": {"withdraw": {"min": 0.01, "max": 29110782}}, "info": {"burningFeeRate": "", "canDep": true, "canInternal": true, "canWd": true, "ccy": "USDC", "chain": "USDC-Avalanche C-Chain", "ctAddr": "0xB97EF9Ef8734C71904D8002F8b6Bc66Dd9c48a6E", "depEstOpenTime": "", "depQuotaFixed": "", "depQuoteDailyLayer2": "", "fee": "0.8", "logoLink": "https://static.coinall.ltd/cdn/oksupport/asset/currency/icon/usdc20230419113033.png", "mainNet": false, "maxFee": "0.8", "maxFeeForCtAddr": "", "maxWd": "29110782", "minDep": "0.00000001", "minDepArrivalConfirm": "30", "minFee": "0.8", "minFeeForCtAddr": "", "minInternal": "1", "minWd": "0.01", "minWdUnlockConfirm": "120", "name": "USD Coin", "needTag": false, "usedDepQuotaFixed": "", "usedWdQuota": "0", "wdEstOpenTime": "", "wdQuota": "10000000", "wdTickSz": "6"}}, "Base": {"id": "USDC-Base", "network": "Base", "active": true, "deposit": true, "withdraw": true, "fee": 0.1, "precision": 1e-06, "limits": {"withdraw": {"min": 0.1, "max": 9703594}}, "info": {"burningFeeRate": "", "canDep": true, "canInternal": true, "canWd": true, "ccy": "USDC", "chain": "USDC-Base", "ctAddr": "0x833589fCD6eDb6E08f4c7C32D4f71b54bdA02913", "depEstOpenTime": "", "depQuotaFixed": "", "depQuoteDailyLayer2": "", "fee": "0.1", "logoLink": "https://static.coinall.ltd/cdn/oksupport/asset/currency/icon/usdc20230419113033.png", "mainNet": false, "maxFee": "0.1", "maxFeeForCtAddr": "", "maxWd": "9703594", "minDep": "0.00000001", "minDepArrivalConfirm": "120", "minFee": "0.1", "minFeeForCtAddr": "", "minInternal": "1", "minWd": "0.1", "minWdUnlockConfirm": "120", "name": "USD Coin", "needTag": false, "usedDepQuotaFixed": "", "usedWdQuota": "0", "wdEstOpenTime": "", "wdQuota": "10000000", "wdTickSz": "6"}}, "OKTC": {"id": "USDC-OKTC", "network": "OKTC", "active": false, "deposit": true, "withdraw": false, "fee": 0, "precision": 1e-08, "limits": {"withdraw": {"min": 0, "max": 48517970}}, "info": {"burningFeeRate": "", "canDep": true, "canInternal": true, "canWd": false, "ccy": "USDC", "chain": "USDC-OKTC", "ctAddr": "0xc946daf81b08146b1c7a8da2a851ddf2b3eaaf85", "depEstOpenTime": "", "depQuotaFixed": "", "depQuoteDailyLayer2": "", "fee": "0", "logoLink": "https://static.coinall.ltd/cdn/oksupport/asset/currency/icon/usdc20230419113033.png", "mainNet": false, "maxFee": "0", "maxFeeForCtAddr": "", "maxWd": "48517970", "minDep": "0.00000001", "minDepArrivalConfirm": "2", "minFee": "0", "minFeeForCtAddr": "", "minInternal": "1", "minWd": "0", "minWdUnlockConfirm": "4", "name": "USD Coin", "needTag": false, "usedDepQuotaFixed": "", "usedWdQuota": "0", "wdEstOpenTime": "4090615200000", "wdQuota": "10000000", "wdTickSz": "8"}}, "OPTIMISM": {"id": "USDC-Optimism", "network": "OPTIMISM", "active": true, "deposit": true, "withdraw": true, "fee": 0.15, "precision": 1e-06, "limits": {"withdraw": {"min": 0.1, "max": 29110782}}, "info": {"burningFeeRate": "", "canDep": true, "canInternal": true, "canWd": true, "ccy": "USDC", "chain": "USDC-Optimism", "ctAddr": "0x0b2c639c533813f4aa9d7837caf62653d097ff85", "depEstOpenTime": "", "depQuotaFixed": "", "depQuoteDailyLayer2": "", "fee": "0.15", "logoLink": "https://static.coinall.ltd/cdn/oksupport/asset/currency/icon/usdc20230419113033.png", "mainNet": false, "maxFee": "0.15", "maxFeeForCtAddr": "", "maxWd": "29110782", "minDep": "0.00000001", "minDepArrivalConfirm": "450", "minFee": "0.15", "minFeeForCtAddr": "", "minInternal": "1", "minWd": "0.1", "minWdUnlockConfirm": "450", "name": "USD Coin", "needTag": false, "usedDepQuotaFixed": "", "usedWdQuota": "0", "wdEstOpenTime": "", "wdQuota": "10000000", "wdTickSz": "6"}}, "MATIC": {"id": "USDC-Polygon", "network": "MATIC", "active": true, "deposit": true, "withdraw": true, "fee": 0.8, "precision": 1e-06, "limits": {"withdraw": {"min": 0.1, "max": 9703594}}, "info": {"burningFeeRate": "", "canDep": true, "canInternal": true, "canWd": true, "ccy": "USDC", "chain": "USDC-Polygon", "ctAddr": "0x3c499c542cEF5E3811e1192ce70d8cC03d5c3359", "depEstOpenTime": "", "depQuotaFixed": "", "depQuoteDailyLayer2": "", "fee": "0.8", "logoLink": "https://static.coinall.ltd/cdn/oksupport/asset/currency/icon/usdc20230419113033.png", "mainNet": false, "maxFee": "0.8", "maxFeeForCtAddr": "", "maxWd": "9703594", "minDep": "0.00000001", "minDepArrivalConfirm": "300", "minFee": "0.8", "minFeeForCtAddr": "", "minInternal": "1", "minWd": "0.1", "minWdUnlockConfirm": "800", "name": "USD Coin", "needTag": false, "usedDepQuotaFixed": "", "usedWdQuota": "0", "wdEstOpenTime": "", "wdQuota": "10000000", "wdTickSz": "6"}}, "Polygon (Bridged)": {"id": "USDC-Polygon (Bridged)", "network": "Polygon (Bridged)", "active": true, "deposit": true, "withdraw": true, "fee": 0.8, "precision": 1e-06, "limits": {"withdraw": {"min": 0.1, "max": 48517970}}, "info": {"burningFeeRate": "", "canDep": true, "canInternal": true, "canWd": true, "ccy": "USDC", "chain": "USDC-Polygon (Bridged)", "ctAddr": "0x2791bca1f2de4661ed88a30c99a7a9449aa84174", "depEstOpenTime": "", "depQuotaFixed": "", "depQuoteDailyLayer2": "", "fee": "0.8", "logoLink": "https://static.coinall.ltd/cdn/oksupport/asset/currency/icon/usdc20230419113033.png", "mainNet": false, "maxFee": "0.8", "maxFeeForCtAddr": "", "maxWd": "48517970", "minDep": "0.00000001", "minDepArrivalConfirm": "300", "minFee": "0.8", "minFeeForCtAddr": "", "minInternal": "1", "minWd": "0.1", "minWdUnlockConfirm": "800", "name": "USD Coin", "needTag": false, "usedDepQuotaFixed": "", "usedWdQuota": "0", "wdEstOpenTime": "", "wdQuota": "10000000", "wdTickSz": "6"}}, "SOL": {"id": "USDC-Solana", "network": "SOL", "active": true, "deposit": true, "withdraw": true, "fee": 1, "precision": 1e-06, "limits": {"withdraw": {"min": 0.01, "max": 48517970}}, "info": {"burningFeeRate": "", "canDep": true, "canInternal": true, "canWd": true, "ccy": "USDC", "chain": "USDC-Solana", "ctAddr": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v", "depEstOpenTime": "", "depQuotaFixed": "", "depQuoteDailyLayer2": "", "fee": "1", "logoLink": "https://static.coinall.ltd/cdn/oksupport/asset/currency/icon/usdc20230419113033.png", "mainNet": false, "maxFee": "1", "maxFeeForCtAddr": "", "maxWd": "48517970", "minDep": "0.1", "minDepArrivalConfirm": "200", "minFee": "1", "minFeeForCtAddr": "", "minInternal": "1", "minWd": "0.01", "minWdUnlockConfirm": "200", "name": "USD Coin", "needTag": false, "usedDepQuotaFixed": "", "usedWdQuota": "0", "wdEstOpenTime": "", "wdQuota": "10000000", "wdTickSz": "6"}}, "X Layer": {"id": "USDC-X Layer", "network": "X Layer", "active": true, "deposit": true, "withdraw": true, "fee": 0.1, "precision": 1e-06, "limits": {"withdraw": {"min": 0.0001, "max": 14555391}}, "info": {"burningFeeRate": "", "canDep": true, "canInternal": true, "canWd": true, "ccy": "USDC", "chain": "USDC-X Layer", "ctAddr": "0x74b7f16337b8972027f6196a17a631ac6de26d22", "depEstOpenTime": "", "depQuotaFixed": "", "depQuoteDailyLayer2": "", "fee": "0.1", "logoLink": "https://static.coinall.ltd/cdn/oksupport/asset/currency/icon/usdc20230419113033.png", "mainNet": false, "maxFee": "0.1", "maxFeeForCtAddr": "", "maxWd": "14555391", "minDep": "0.00000001", "minDepArrivalConfirm": "120", "minFee": "0.1", "minFeeForCtAddr": "", "minInternal": "1", "minWd": "0.0001", "minWdUnlockConfirm": "120", "name": "USD Coin", "needTag": false, "usedDepQuotaFixed": "", "usedWdQuota": "0", "wdEstOpenTime": "", "wdQuota": "10000000", "wdTickSz": "6"}}}}, "LTC": {"code": "LTC", "id": "LTC", "name": "Litecoin", "active": true, "deposit": true, "withdraw": true, "precision": 1e-08, "limits": {"amount": {}}, "networks": {"LTC": {"id": "LTC-Litecoin", "network": "LTC", "active": true, "deposit": true, "withdraw": true, "fee": 0.001, "precision": 1e-08, "limits": {"withdraw": {"min": 0.01, "max": 206540}}, "info": {"burningFeeRate": "", "canDep": true, "canInternal": true, "canWd": true, "ccy": "LTC", "chain": "LTC-Litecoin", "ctAddr": "", "depEstOpenTime": "", "depQuotaFixed": "", "depQuoteDailyLayer2": "", "fee": "0.001", "logoLink": "https://static.coinall.ltd/cdn/oksupport/asset/currency/icon/ltc20230419112918.png", "mainNet": true, "maxFee": "0.001", "maxFeeForCtAddr": "", "maxWd": "206540", "minDep": "0.0001", "minDepArrivalConfirm": "3", "minFee": "0.001", "minFeeForCtAddr": "", "minInternal": "0.01", "minWd": "0.01", "minWdUnlockConfirm": "6", "name": "Litecoin", "needTag": false, "usedDepQuotaFixed": "", "usedWdQuota": "0", "wdEstOpenTime": "", "wdQuota": "10000000", "wdTickSz": "8"}}, "OKTC": {"id": "LTCK-OKTC", "network": "OKTC", "active": false, "deposit": true, "withdraw": false, "fee": 0, "precision": 1e-08, "limits": {"withdraw": {"min": 0, "max": 516350}}, "info": {"burningFeeRate": "", "canDep": true, "canInternal": true, "canWd": false, "ccy": "LTC", "chain": "LTCK-OKTC", "ctAddr": "0xfa520efc34c81bfc1e3dd48b7fe9ff326049f986", "depEstOpenTime": "", "depQuotaFixed": "", "depQuoteDailyLayer2": "", "fee": "0", "logoLink": "https://static.coinall.ltd/cdn/oksupport/asset/currency/icon/ltc20230419112918.png", "mainNet": false, "maxFee": "0", "maxFeeForCtAddr": "", "maxWd": "516350", "minDep": "0.00000001", "minDepArrivalConfirm": "2", "minFee": "0", "minFeeForCtAddr": "", "minInternal": "0.01", "minWd": "0", "minWdUnlockConfirm": "4", "name": "Litecoin", "needTag": false, "usedDepQuotaFixed": "", "usedWdQuota": "0", "wdEstOpenTime": "4090615200000", "wdQuota": "10000000", "wdTickSz": "8"}}}}, "DOGE": {"code": "DOGE", "id": "DOGE", "name": "<PERSON><PERSON><PERSON><PERSON>", "active": true, "deposit": true, "withdraw": true, "precision": 1e-08, "limits": {"amount": {}}, "networks": {"DOGE": {"id": "DOGE-Dogecoin", "network": "DOGE", "active": true, "deposit": true, "withdraw": true, "fee": 4, "precision": 1e-08, "limits": {"withdraw": {"min": 10, "max": 352863314}}, "info": {"burningFeeRate": "", "canDep": true, "canInternal": true, "canWd": true, "ccy": "DOGE", "chain": "DOGE-Dogecoin", "ctAddr": "", "depEstOpenTime": "", "depQuotaFixed": "", "depQuoteDailyLayer2": "", "fee": "4", "logoLink": "https://static.coinall.ltd/cdn/oksupport/asset/currency/icon/doge20230419112833.png", "mainNet": true, "maxFee": "4", "maxFeeForCtAddr": "", "maxWd": "352863314", "minDep": "0.00000001", "minDepArrivalConfirm": "6", "minFee": "4", "minFeeForCtAddr": "", "minInternal": "1", "minWd": "10", "minWdUnlockConfirm": "36", "name": "<PERSON><PERSON><PERSON><PERSON>", "needTag": false, "usedDepQuotaFixed": "", "usedWdQuota": "0", "wdEstOpenTime": "", "wdQuota": "10000000", "wdTickSz": "8"}}}}, "ADA": {"code": "ADA", "id": "ADA", "name": "Cardano", "active": true, "deposit": true, "withdraw": true, "precision": 1e-06, "limits": {"amount": {}}, "networks": {"ADA": {"id": "ADA-Cardano", "network": "ADA", "active": true, "deposit": true, "withdraw": true, "fee": 0.8, "precision": 1e-06, "limits": {"withdraw": {"min": 2, "max": 29772458}}, "info": {"burningFeeRate": "", "canDep": true, "canInternal": true, "canWd": true, "ccy": "ADA", "chain": "ADA-Cardano", "ctAddr": "", "depEstOpenTime": "", "depQuotaFixed": "", "depQuoteDailyLayer2": "", "fee": "0.8", "logoLink": "https://static.coinall.ltd/cdn/assets/imgs/221/5FFE6AFD21B40243.png", "mainNet": true, "maxFee": "0.8", "maxFeeForCtAddr": "", "maxWd": "29772458", "minDep": "0.00000001", "minDepArrivalConfirm": "40", "minFee": "0.8", "minFeeForCtAddr": "", "minInternal": "1", "minWd": "2", "minWdUnlockConfirm": "120", "name": "Cardano", "needTag": false, "usedDepQuotaFixed": "", "usedWdQuota": "0", "wdEstOpenTime": "", "wdQuota": "10000000", "wdTickSz": "6"}}}}, "SOL": {"code": "SOL", "id": "SOL", "name": "Solana", "active": true, "deposit": true, "withdraw": true, "precision": 1e-08, "limits": {"amount": {}}, "networks": {"SOL": {"id": "SOL-Solana", "network": "SOL", "active": true, "deposit": true, "withdraw": true, "fee": 0.0064, "precision": 1e-08, "limits": {"withdraw": {"min": 0.001, "max": 314718}}, "info": {"burningFeeRate": "", "canDep": true, "canInternal": true, "canWd": true, "ccy": "SOL", "chain": "SOL-Solana", "ctAddr": "", "depEstOpenTime": "", "depQuotaFixed": "", "depQuoteDailyLayer2": "", "fee": "0.0064", "logoLink": "https://static.coinall.ltd/cdn/oksupport/asset/currency/icon/sol20230419112951.png", "mainNet": true, "maxFee": "0.0064", "maxFeeForCtAddr": "", "maxWd": "314718", "minDep": "0.005", "minDepArrivalConfirm": "200", "minFee": "0.0064", "minFeeForCtAddr": "", "minInternal": "0.1", "minWd": "0.001", "minWdUnlockConfirm": "200", "name": "Solana", "needTag": false, "usedDepQuotaFixed": "", "usedWdQuota": "0", "wdEstOpenTime": "", "wdQuota": "10000000", "wdTickSz": "8"}}}}, "POL": {"code": "POL", "id": "POL", "name": "Polygon", "active": true, "deposit": true, "withdraw": true, "precision": 1e-08, "limits": {"amount": {}}, "networks": {"ERC20": {"id": "POL-ERC20", "network": "ERC20", "active": true, "deposit": true, "withdraw": true, "fee": 10, "precision": 1e-08, "limits": {"withdraw": {"min": 0.1, "max": 53929451}}, "info": {"burningFeeRate": "", "canDep": true, "canInternal": true, "canWd": true, "ccy": "POL", "chain": "POL-ERC20", "ctAddr": "0x455e53cbb86018ac2b8092fdcd39d8444affc3f6", "depEstOpenTime": "", "depQuotaFixed": "", "depQuoteDailyLayer2": "", "fee": "10", "logoLink": "https://static.coinall.ltd/cdn/oksupport/asset/currency/icon/pol20240903114912.png", "mainNet": true, "maxFee": "10", "maxFeeForCtAddr": "", "maxWd": "53929451", "minDep": "0.00000001", "minDepArrivalConfirm": "32", "minFee": "10", "minFeeForCtAddr": "", "minInternal": "0.1", "minWd": "0.1", "minWdUnlockConfirm": "96", "name": "Polygon", "needTag": false, "usedDepQuotaFixed": "", "usedWdQuota": "0", "wdEstOpenTime": "", "wdQuota": "10000000", "wdTickSz": "8"}}, "MATIC": {"id": "POL-Polygon", "network": "MATIC", "active": true, "deposit": true, "withdraw": true, "fee": 0.1, "precision": 1e-08, "limits": {"withdraw": {"min": 0.001, "max": 89882418}}, "info": {"burningFeeRate": "", "canDep": true, "canInternal": true, "canWd": true, "ccy": "POL", "chain": "POL-Polygon", "ctAddr": "", "depEstOpenTime": "", "depQuotaFixed": "", "depQuoteDailyLayer2": "", "fee": "0.1", "logoLink": "https://static.coinall.ltd/cdn/oksupport/asset/currency/icon/pol20240903114912.png", "mainNet": false, "maxFee": "0.1", "maxFeeForCtAddr": "", "maxWd": "89882418", "minDep": "0.00000001", "minDepArrivalConfirm": "300", "minFee": "0.1", "minFeeForCtAddr": "", "minInternal": "0.1", "minWd": "0.001", "minWdUnlockConfirm": "800", "name": "Polygon", "needTag": false, "usedDepQuotaFixed": "", "usedWdQuota": "0", "wdEstOpenTime": "", "wdQuota": "10000000", "wdTickSz": "8"}}}}, "TUSD": {"id": "TUSD", "code": "TUSD", "precision": 1e-06, "fees": {}, "networks": {}, "limits": {"deposit": {}, "withdraw": {}}}}