{"BTC": {"info": {"orgId": "9001", "coinId": "BTC", "coinName": "BTC", "coinFullName": "Bitcoin", "allowWithdraw": true, "allowDeposit": true, "status": "1", "tokenType": "CHAIN_TOKEN", "chainTypes": [{"chainType": "Bitcoin", "withdrawFee": "0.0005", "minWithdrawQuantity": "0.002", "maxWithdrawQuantity": "0", "minDepositQuantity": "0.0005", "allowDeposit": true, "allowWithdraw": true}, {"chainType": "Merlin Chain", "withdrawFee": "0.00001", "minWithdrawQuantity": "0.00002", "maxWithdrawQuantity": "0", "minDepositQuantity": "0.00002", "allowDeposit": false, "allowWithdraw": false}]}, "id": "BTC", "numericId": null, "code": "BTC", "precision": null, "type": "crypto", "name": "Bitcoin", "active": true, "deposit": true, "withdraw": true, "fee": 1e-05, "fees": {}, "networks": {"Bitcoin": {"id": "Bitcoin", "network": "Bitcoin", "limits": {"withdraw": {"min": 0.002, "max": null}, "deposit": {"min": 0.0005, "max": null}}, "active": true, "deposit": true, "withdraw": true, "fee": 0.0005, "precision": null, "info": {"chainType": "Bitcoin", "withdrawFee": "0.0005", "minWithdrawQuantity": "0.002", "maxWithdrawQuantity": "0", "minDepositQuantity": "0.0005", "allowDeposit": true, "allowWithdraw": true}}, "Merlin Chain": {"id": "Merlin Chain", "network": "Merlin Chain", "limits": {"withdraw": {"min": 2e-05, "max": null}, "deposit": {"min": 2e-05, "max": null}}, "active": false, "deposit": false, "withdraw": false, "fee": 1e-05, "precision": null, "info": {"chainType": "Merlin Chain", "withdrawFee": "0.00001", "minWithdrawQuantity": "0.00002", "maxWithdrawQuantity": "0", "minDepositQuantity": "0.00002", "allowDeposit": false, "allowWithdraw": false}}}, "limits": {"deposit": {"min": 2e-05, "max": null}, "withdraw": {"min": 2e-05, "max": null}}}, "ETH": {"info": {"orgId": "9001", "coinId": "ETH", "coinName": "ETH", "coinFullName": "Ethereum", "allowWithdraw": true, "allowDeposit": true, "status": "1", "tokenType": "ERC20_TOKEN", "chainTypes": [{"chainType": "ETH", "withdrawFee": "0.0023", "minWithdrawQuantity": "0.04", "maxWithdrawQuantity": "0", "minDepositQuantity": "0.01", "allowDeposit": true, "allowWithdraw": true}, {"chainType": "Optimism", "withdrawFee": "0.008", "minWithdrawQuantity": "0.05", "maxWithdrawQuantity": "0", "minDepositQuantity": "0.04", "allowDeposit": false, "allowWithdraw": false}, {"chainType": "Arbitrum", "withdrawFee": "0.0002", "minWithdrawQuantity": "0.0002", "maxWithdrawQuantity": "0", "minDepositQuantity": "0.0002", "allowDeposit": true, "allowWithdraw": true}, {"chainType": "zkSync", "withdrawFee": "0.00008", "minWithdrawQuantity": "0.001", "maxWithdrawQuantity": "0", "minDepositQuantity": "0.001", "allowDeposit": false, "allowWithdraw": false}, {"chainType": "Base", "withdrawFee": "0.00002", "minWithdrawQuantity": "0.0000003", "maxWithdrawQuantity": "0", "minDepositQuantity": "0.0000003", "allowDeposit": true, "allowWithdraw": true}, {"chainType": "<PERSON><PERSON>", "withdrawFee": "0.001", "minWithdrawQuantity": "0.001", "maxWithdrawQuantity": "0", "minDepositQuantity": "0.001", "allowDeposit": false, "allowWithdraw": false}, {"chainType": "Kroma", "withdrawFee": "0.0003", "minWithdrawQuantity": "0.0003", "maxWithdrawQuantity": "0", "minDepositQuantity": "0.0003", "allowDeposit": false, "allowWithdraw": false}, {"chainType": "Zircuit", "withdrawFee": "0", "minWithdrawQuantity": "0.001", "maxWithdrawQuantity": "0", "minDepositQuantity": "0.001", "allowDeposit": false, "allowWithdraw": false}]}, "id": "ETH", "numericId": null, "code": "ETH", "precision": null, "type": "crypto", "name": "Ethereum", "active": true, "deposit": true, "withdraw": true, "fee": 0, "fees": {}, "networks": {"ETH": {"id": "ETH", "network": "ETH", "limits": {"withdraw": {"min": 0.04, "max": null}, "deposit": {"min": 0.01, "max": null}}, "active": true, "deposit": true, "withdraw": true, "fee": 0.0023, "precision": null, "info": {"chainType": "ETH", "withdrawFee": "0.0023", "minWithdrawQuantity": "0.04", "maxWithdrawQuantity": "0", "minDepositQuantity": "0.01", "allowDeposit": true, "allowWithdraw": true}}, "Optimism": {"id": "Optimism", "network": "Optimism", "limits": {"withdraw": {"min": 0.05, "max": null}, "deposit": {"min": 0.04, "max": null}}, "active": false, "deposit": false, "withdraw": false, "fee": 0.008, "precision": null, "info": {"chainType": "Optimism", "withdrawFee": "0.008", "minWithdrawQuantity": "0.05", "maxWithdrawQuantity": "0", "minDepositQuantity": "0.04", "allowDeposit": false, "allowWithdraw": false}}, "Arbitrum": {"id": "Arbitrum", "network": "Arbitrum", "limits": {"withdraw": {"min": 0.0002, "max": null}, "deposit": {"min": 0.0002, "max": null}}, "active": true, "deposit": true, "withdraw": true, "fee": 0.0002, "precision": null, "info": {"chainType": "Arbitrum", "withdrawFee": "0.0002", "minWithdrawQuantity": "0.0002", "maxWithdrawQuantity": "0", "minDepositQuantity": "0.0002", "allowDeposit": true, "allowWithdraw": true}}, "zkSync": {"id": "zkSync", "network": "zkSync", "limits": {"withdraw": {"min": 0.001, "max": null}, "deposit": {"min": 0.001, "max": null}}, "active": false, "deposit": false, "withdraw": false, "fee": 8e-05, "precision": null, "info": {"chainType": "zkSync", "withdrawFee": "0.00008", "minWithdrawQuantity": "0.001", "maxWithdrawQuantity": "0", "minDepositQuantity": "0.001", "allowDeposit": false, "allowWithdraw": false}}, "Base": {"id": "Base", "network": "Base", "limits": {"withdraw": {"min": 3e-07, "max": null}, "deposit": {"min": 3e-07, "max": null}}, "active": true, "deposit": true, "withdraw": true, "fee": 2e-05, "precision": null, "info": {"chainType": "Base", "withdrawFee": "0.00002", "minWithdrawQuantity": "0.0000003", "maxWithdrawQuantity": "0", "minDepositQuantity": "0.0000003", "allowDeposit": true, "allowWithdraw": true}}, "Scroll": {"id": "<PERSON><PERSON>", "network": "<PERSON><PERSON>", "limits": {"withdraw": {"min": 0.001, "max": null}, "deposit": {"min": 0.001, "max": null}}, "active": false, "deposit": false, "withdraw": false, "fee": 0.001, "precision": null, "info": {"chainType": "<PERSON><PERSON>", "withdrawFee": "0.001", "minWithdrawQuantity": "0.001", "maxWithdrawQuantity": "0", "minDepositQuantity": "0.001", "allowDeposit": false, "allowWithdraw": false}}, "Kroma": {"id": "Kroma", "network": "Kroma", "limits": {"withdraw": {"min": 0.0003, "max": null}, "deposit": {"min": 0.0003, "max": null}}, "active": false, "deposit": false, "withdraw": false, "fee": 0.0003, "precision": null, "info": {"chainType": "Kroma", "withdrawFee": "0.0003", "minWithdrawQuantity": "0.0003", "maxWithdrawQuantity": "0", "minDepositQuantity": "0.0003", "allowDeposit": false, "allowWithdraw": false}}, "Zircuit": {"id": "Zircuit", "network": "Zircuit", "limits": {"withdraw": {"min": 0.001, "max": null}, "deposit": {"min": 0.001, "max": null}}, "active": false, "deposit": false, "withdraw": false, "fee": 0, "precision": null, "info": {"chainType": "Zircuit", "withdrawFee": "0", "minWithdrawQuantity": "0.001", "maxWithdrawQuantity": "0", "minDepositQuantity": "0.001", "allowDeposit": false, "allowWithdraw": false}}}, "limits": {"deposit": {"min": 3e-07, "max": null}, "withdraw": {"min": 3e-07, "max": null}}}, "USDT": {"info": {"orgId": "9001", "coinId": "USDT", "coinName": "USDT", "coinFullName": "<PERSON><PERSON>", "allowWithdraw": true, "allowDeposit": true, "status": "1", "tokenType": "ERC20_TOKEN", "chainTypes": [{"chainType": "ETH", "withdrawFee": "8", "minWithdrawQuantity": "25", "maxWithdrawQuantity": "0", "minDepositQuantity": "1", "allowDeposit": true, "allowWithdraw": true}, {"chainType": "TRC20", "withdrawFee": "1", "minWithdrawQuantity": "1", "maxWithdrawQuantity": "0", "minDepositQuantity": "1", "allowDeposit": true, "allowWithdraw": true}, {"chainType": "Arbitrum", "withdrawFee": "0.3", "minWithdrawQuantity": "1", "maxWithdrawQuantity": "0", "minDepositQuantity": "1", "allowDeposit": true, "allowWithdraw": true}, {"chainType": "BSC(BEP20)", "withdrawFee": "0", "minWithdrawQuantity": "1", "maxWithdrawQuantity": "0", "minDepositQuantity": "1", "allowDeposit": true, "allowWithdraw": true}, {"chainType": "TON", "withdrawFee": "1", "minWithdrawQuantity": "0.5", "maxWithdrawQuantity": "0", "minDepositQuantity": "1", "allowDeposit": false, "allowWithdraw": false}, {"chainType": "Solana", "withdrawFee": "2", "minWithdrawQuantity": "1", "maxWithdrawQuantity": "0", "minDepositQuantity": "1", "allowDeposit": true, "allowWithdraw": true}, {"chainType": "<PERSON><PERSON>", "withdrawFee": "0", "minWithdrawQuantity": "1", "maxWithdrawQuantity": "0", "minDepositQuantity": "1", "allowDeposit": false, "allowWithdraw": false}, {"chainType": "<PERSON><PERSON><PERSON><PERSON>", "withdrawFee": "0.2", "minWithdrawQuantity": "1", "maxWithdrawQuantity": "0", "minDepositQuantity": "1", "allowDeposit": true, "allowWithdraw": true}]}, "id": "USDT", "numericId": null, "code": "USDT", "precision": null, "type": "crypto", "name": "<PERSON><PERSON>", "active": true, "deposit": true, "withdraw": true, "fee": 0, "fees": {}, "networks": {"ETH": {"id": "ETH", "network": "ETH", "limits": {"withdraw": {"min": 25, "max": null}, "deposit": {"min": 1, "max": null}}, "active": true, "deposit": true, "withdraw": true, "fee": 8, "precision": null, "info": {"chainType": "ETH", "withdrawFee": "8", "minWithdrawQuantity": "25", "maxWithdrawQuantity": "0", "minDepositQuantity": "1", "allowDeposit": true, "allowWithdraw": true}}, "Tron": {"id": "TRC20", "network": "Tron", "limits": {"withdraw": {"min": 1, "max": null}, "deposit": {"min": 1, "max": null}}, "active": true, "deposit": true, "withdraw": true, "fee": 1, "precision": null, "info": {"chainType": "TRC20", "withdrawFee": "1", "minWithdrawQuantity": "1", "maxWithdrawQuantity": "0", "minDepositQuantity": "1", "allowDeposit": true, "allowWithdraw": true}}, "Arbitrum": {"id": "Arbitrum", "network": "Arbitrum", "limits": {"withdraw": {"min": 1, "max": null}, "deposit": {"min": 1, "max": null}}, "active": true, "deposit": true, "withdraw": true, "fee": 0.3, "precision": null, "info": {"chainType": "Arbitrum", "withdrawFee": "0.3", "minWithdrawQuantity": "1", "maxWithdrawQuantity": "0", "minDepositQuantity": "1", "allowDeposit": true, "allowWithdraw": true}}, "BSC(BEP20)": {"id": "BSC(BEP20)", "network": "BSC(BEP20)", "limits": {"withdraw": {"min": 1, "max": null}, "deposit": {"min": 1, "max": null}}, "active": true, "deposit": true, "withdraw": true, "fee": 0, "precision": null, "info": {"chainType": "BSC(BEP20)", "withdrawFee": "0", "minWithdrawQuantity": "1", "maxWithdrawQuantity": "0", "minDepositQuantity": "1", "allowDeposit": true, "allowWithdraw": true}}, "TON": {"id": "TON", "network": "TON", "limits": {"withdraw": {"min": 0.5, "max": null}, "deposit": {"min": 1, "max": null}}, "active": false, "deposit": false, "withdraw": false, "fee": 1, "precision": null, "info": {"chainType": "TON", "withdrawFee": "1", "minWithdrawQuantity": "0.5", "maxWithdrawQuantity": "0", "minDepositQuantity": "1", "allowDeposit": false, "allowWithdraw": false}}, "Solana": {"id": "Solana", "network": "Solana", "limits": {"withdraw": {"min": 1, "max": null}, "deposit": {"min": 1, "max": null}}, "active": true, "deposit": true, "withdraw": true, "fee": 2, "precision": null, "info": {"chainType": "Solana", "withdrawFee": "2", "minWithdrawQuantity": "1", "maxWithdrawQuantity": "0", "minDepositQuantity": "1", "allowDeposit": true, "allowWithdraw": true}}, "Scroll": {"id": "<PERSON><PERSON>", "network": "<PERSON><PERSON>", "limits": {"withdraw": {"min": 1, "max": null}, "deposit": {"min": 1, "max": null}}, "active": false, "deposit": false, "withdraw": false, "fee": 0, "precision": null, "info": {"chainType": "<PERSON><PERSON>", "withdrawFee": "0", "minWithdrawQuantity": "1", "maxWithdrawQuantity": "0", "minDepositQuantity": "1", "allowDeposit": false, "allowWithdraw": false}}, "HashKey": {"id": "<PERSON><PERSON><PERSON><PERSON>", "network": "<PERSON><PERSON><PERSON><PERSON>", "limits": {"withdraw": {"min": 1, "max": null}, "deposit": {"min": 1, "max": null}}, "active": true, "deposit": true, "withdraw": true, "fee": 0.2, "precision": null, "info": {"chainType": "<PERSON><PERSON><PERSON><PERSON>", "withdrawFee": "0.2", "minWithdrawQuantity": "1", "maxWithdrawQuantity": "0", "minDepositQuantity": "1", "allowDeposit": true, "allowWithdraw": true}}}, "limits": {"deposit": {"min": 1, "max": null}, "withdraw": {"min": 0.5, "max": null}}}, "USDC": {"info": {"orgId": "9001", "coinId": "USDC", "coinName": "USDC", "coinFullName": "USD Coin", "allowWithdraw": true, "allowDeposit": true, "status": "1", "tokenType": "ERC20_TOKEN", "chainTypes": [{"chainType": "ETH", "withdrawFee": "8", "minWithdrawQuantity": "25", "maxWithdrawQuantity": "0", "minDepositQuantity": "1", "allowDeposit": true, "allowWithdraw": true}, {"chainType": "Arbitrum", "withdrawFee": "1", "minWithdrawQuantity": "1", "maxWithdrawQuantity": "0", "minDepositQuantity": "1", "allowDeposit": false, "allowWithdraw": false}]}, "id": "USDC", "numericId": null, "code": "USDC", "precision": null, "type": "crypto", "name": "USD Coin", "active": true, "deposit": true, "withdraw": true, "fee": 1, "fees": {}, "networks": {"ETH": {"id": "ETH", "network": "ETH", "limits": {"withdraw": {"min": 25, "max": null}, "deposit": {"min": 1, "max": null}}, "active": true, "deposit": true, "withdraw": true, "fee": 8, "precision": null, "info": {"chainType": "ETH", "withdrawFee": "8", "minWithdrawQuantity": "25", "maxWithdrawQuantity": "0", "minDepositQuantity": "1", "allowDeposit": true, "allowWithdraw": true}}, "Arbitrum": {"id": "Arbitrum", "network": "Arbitrum", "limits": {"withdraw": {"min": 1, "max": null}, "deposit": {"min": 1, "max": null}}, "active": false, "deposit": false, "withdraw": false, "fee": 1, "precision": null, "info": {"chainType": "Arbitrum", "withdrawFee": "1", "minWithdrawQuantity": "1", "maxWithdrawQuantity": "0", "minDepositQuantity": "1", "allowDeposit": false, "allowWithdraw": false}}}, "limits": {"deposit": {"min": 1, "max": null}, "withdraw": {"min": 1, "max": null}}}, "LTC": {"info": {"orgId": "9001", "coinId": "LTC", "coinName": "LTC", "coinFullName": "Litecoin", "allowWithdraw": true, "allowDeposit": true, "status": "1", "tokenType": "CHAIN_TOKEN", "chainTypes": [{"chainType": "Litecoin", "withdrawFee": "0.0002", "minWithdrawQuantity": "0.0052", "maxWithdrawQuantity": "0", "minDepositQuantity": "0.0006", "allowDeposit": true, "allowWithdraw": true}]}, "id": "LTC", "numericId": null, "code": "LTC", "precision": null, "type": "crypto", "name": "Litecoin", "active": true, "deposit": true, "withdraw": true, "fee": 0.0002, "fees": {}, "networks": {"Litecoin": {"id": "Litecoin", "network": "Litecoin", "limits": {"withdraw": {"min": 0.0052, "max": null}, "deposit": {"min": 0.0006, "max": null}}, "active": true, "deposit": true, "withdraw": true, "fee": 0.0002, "precision": null, "info": {"chainType": "Litecoin", "withdrawFee": "0.0002", "minWithdrawQuantity": "0.0052", "maxWithdrawQuantity": "0", "minDepositQuantity": "0.0006", "allowDeposit": true, "allowWithdraw": true}}}, "limits": {"deposit": {"min": 0.0006, "max": null}, "withdraw": {"min": 0.0052, "max": null}}}, "DOGE": {"info": {"orgId": "9001", "coinId": "DOGE", "coinName": "DOGE", "coinFullName": "<PERSON><PERSON><PERSON><PERSON>", "allowWithdraw": true, "allowDeposit": true, "status": "1", "tokenType": "CHAIN_TOKEN", "chainTypes": [{"chainType": "<PERSON><PERSON><PERSON><PERSON>", "withdrawFee": "8", "minWithdrawQuantity": "20", "maxWithdrawQuantity": "0", "minDepositQuantity": "0.55", "allowDeposit": true, "allowWithdraw": true}]}, "id": "DOGE", "numericId": null, "code": "DOGE", "precision": null, "type": "crypto", "name": "<PERSON><PERSON><PERSON><PERSON>", "active": true, "deposit": true, "withdraw": true, "fee": 8, "fees": {}, "networks": {"Dogecoin": {"id": "<PERSON><PERSON><PERSON><PERSON>", "network": "<PERSON><PERSON><PERSON><PERSON>", "limits": {"withdraw": {"min": 20, "max": null}, "deposit": {"min": 0.55, "max": null}}, "active": true, "deposit": true, "withdraw": true, "fee": 8, "precision": null, "info": {"chainType": "<PERSON><PERSON><PERSON><PERSON>", "withdrawFee": "8", "minWithdrawQuantity": "20", "maxWithdrawQuantity": "0", "minDepositQuantity": "0.55", "allowDeposit": true, "allowWithdraw": true}}}, "limits": {"deposit": {"min": 0.55, "max": null}, "withdraw": {"min": 20, "max": null}}}}