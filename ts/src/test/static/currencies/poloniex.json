{"BTC": {"info": {"id": 28, "name": "Bitcoin", "description": "BTC Clone", "type": "address", "withdrawalFee": "0.********", "minConf": 2, "depositAddress": null, "blockchain": "BTC", "delisted": false, "tradingState": "NORMAL", "walletState": "ENABLED", "walletDepositState": "ENABLED", "walletWithdrawalState": "ENABLED", "supportCollateral": true, "supportBorrow": true, "parentChain": null, "isMultiChain": true, "isChildChain": false, "childChains": ["BTCB", "BTCTRON", "WBTCETH"]}, "id": "BTC", "numericId": 28, "code": "BTC", "precision": null, "type": "crypto", "name": "Bitcoin", "active": null, "deposit": null, "withdraw": null, "fee": 2e-05, "fees": {}, "networks": {"BEP20": {"info": {"id": 581, "name": "Binance-Peg BTCB Token", "description": "Sweep to Main Account", "type": "address", "withdrawalFee": "0.********", "minConf": 15, "depositAddress": null, "blockchain": "BSC", "delisted": false, "tradingState": "OFFLINE", "walletState": "DISABLED", "walletDepositState": "DISABLED", "walletWithdrawalState": "DISABLED", "supportCollateral": false, "supportBorrow": false, "parentChain": "BTC", "isMultiChain": true, "isChildChain": true, "childChains": []}, "id": "BTCB", "numericId": 581, "network": "BEP20", "active": null, "deposit": null, "withdraw": null, "fee": 3e-05, "precision": null, "limits": {"withdraw": {"min": null, "max": null}, "deposit": {"min": null, "max": null}}}, "TRC20": {"info": {"id": 445, "name": "BTC on TRON", "description": "Sweep to Main Account", "type": "address", "withdrawalFee": "0.********", "minConf": 0, "depositAddress": null, "blockchain": "TRX", "delisted": false, "tradingState": "OFFLINE", "walletState": "ENABLED", "walletDepositState": "ENABLED", "walletWithdrawalState": "ENABLED", "supportCollateral": false, "supportBorrow": false, "parentChain": "BTC", "isMultiChain": true, "isChildChain": true, "childChains": []}, "id": "BTCTRON", "numericId": 445, "network": "TRC20", "active": null, "deposit": null, "withdraw": null, "fee": 4e-05, "precision": null, "limits": {"withdraw": {"min": null, "max": null}, "deposit": {"min": null, "max": null}}}, "ERC20": {"info": {"id": 2207, "name": "Wrapped BTC", "description": "Sweep to Main Account", "type": "address", "withdrawalFee": "0.********", "minConf": 64, "depositAddress": null, "blockchain": "ETH", "delisted": false, "tradingState": "NORMAL", "walletState": "ENABLED", "walletDepositState": "ENABLED", "walletWithdrawalState": "DISABLED", "supportCollateral": false, "supportBorrow": false, "parentChain": "BTC", "isMultiChain": true, "isChildChain": true, "childChains": []}, "id": "WBTCETH", "numericId": 2207, "network": "ERC20", "active": null, "deposit": null, "withdraw": null, "fee": 2e-05, "precision": null, "limits": {"withdraw": {"min": null, "max": null}, "deposit": {"min": null, "max": null}}}, "BTC": {"info": {"id": 28, "name": "Bitcoin", "description": "BTC Clone", "type": "address", "withdrawalFee": "0.********", "minConf": 2, "depositAddress": null, "blockchain": "BTC", "delisted": false, "tradingState": "NORMAL", "walletState": "ENABLED", "walletDepositState": "ENABLED", "walletWithdrawalState": "ENABLED", "supportCollateral": true, "supportBorrow": true, "parentChain": null, "isMultiChain": true, "isChildChain": false, "childChains": ["BTCB", "BTCTRON", "WBTCETH"]}, "id": "BTC", "numericId": 28, "network": "BTC", "active": null, "deposit": null, "withdraw": null, "fee": 0.001, "precision": null, "limits": {"withdraw": {"min": null, "max": null}, "deposit": {"min": null, "max": null}}}}, "limits": {"amount": {"min": null, "max": null}, "withdraw": {"min": null, "max": null}, "deposit": {"min": null, "max": null}}}, "USDT": {"info": {"id": 214, "name": "Tether USD", "description": "Sweep to Main Account", "type": "address", "withdrawalFee": "0.********", "minConf": 2, "depositAddress": null, "blockchain": "OMNI", "delisted": false, "tradingState": "NORMAL", "walletState": "DISABLED", "walletDepositState": "DISABLED", "walletWithdrawalState": "DISABLED", "supportCollateral": true, "supportBorrow": true, "parentChain": null, "isMultiChain": true, "isChildChain": false, "childChains": ["USDTBSC", "USDTETH", "USDTSOL", "USDTTRON"]}, "id": "USDT", "numericId": 214, "code": "USDT", "precision": null, "type": "crypto", "name": "Tether USD", "active": null, "deposit": null, "withdraw": null, "fee": 0, "fees": {}, "networks": {"BEP20": {"info": {"id": 582, "name": "Binance-Peg BSC-USD", "description": "Sweep to Main Account", "type": "address", "withdrawalFee": "0.********", "minConf": 15, "depositAddress": null, "blockchain": "BSC", "delisted": false, "tradingState": "OFFLINE", "walletState": "ENABLED", "walletDepositState": "ENABLED", "walletWithdrawalState": "DISABLED", "supportCollateral": false, "supportBorrow": false, "parentChain": "USDT", "isMultiChain": true, "isChildChain": true, "childChains": []}, "id": "USDTBSC", "numericId": 582, "network": "BEP20", "active": null, "deposit": null, "withdraw": null, "fee": 0, "precision": null, "limits": {"withdraw": {"min": null, "max": null}, "deposit": {"min": null, "max": null}}}, "ERC20": {"info": {"id": 318, "name": "USDT on ETH", "description": "Sweep to Main Account", "type": "address", "withdrawalFee": "1.********", "minConf": 64, "depositAddress": null, "blockchain": "ETH", "delisted": false, "tradingState": "NORMAL", "walletState": "ENABLED", "walletDepositState": "ENABLED", "walletWithdrawalState": "ENABLED", "supportCollateral": false, "supportBorrow": false, "parentChain": "USDT", "isMultiChain": true, "isChildChain": true, "childChains": []}, "id": "USDTETH", "numericId": 318, "network": "ERC20", "active": null, "deposit": null, "withdraw": null, "fee": 1.152502, "precision": null, "limits": {"withdraw": {"min": null, "max": null}, "deposit": {"min": null, "max": null}}}, "SOL": {"info": {"id": 1936, "name": "USDT", "description": "Sweep to Main Account", "type": "address", "withdrawalFee": "0.********", "minConf": 300, "depositAddress": null, "blockchain": "SOL", "delisted": false, "tradingState": "OFFLINE", "walletState": "ENABLED", "walletDepositState": "ENABLED", "walletWithdrawalState": "DISABLED", "supportCollateral": false, "supportBorrow": false, "parentChain": "USDT", "isMultiChain": true, "isChildChain": true, "childChains": []}, "id": "USDTSOL", "numericId": 1936, "network": "SOL", "active": null, "deposit": null, "withdraw": null, "fee": 0.91, "precision": null, "limits": {"withdraw": {"min": null, "max": null}, "deposit": {"min": null, "max": null}}}, "TRC20": {"info": {"id": 316, "name": "USDT on TRON", "description": "Sweep to Main Account", "type": "address", "withdrawalFee": "1.********", "minConf": 0, "depositAddress": null, "blockchain": "TRX", "delisted": false, "tradingState": "NORMAL", "walletState": "ENABLED", "walletDepositState": "ENABLED", "walletWithdrawalState": "ENABLED", "supportCollateral": false, "supportBorrow": false, "parentChain": "USDT", "isMultiChain": true, "isChildChain": true, "childChains": []}, "id": "USDTTRON", "numericId": 316, "network": "TRC20", "active": null, "deposit": null, "withdraw": null, "fee": 1.2, "precision": null, "limits": {"withdraw": {"min": null, "max": null}, "deposit": {"min": null, "max": null}}}, "OMNI": {"info": {"id": 214, "name": "Tether USD", "description": "Sweep to Main Account", "type": "address", "withdrawalFee": "0.********", "minConf": 2, "depositAddress": null, "blockchain": "OMNI", "delisted": false, "tradingState": "NORMAL", "walletState": "DISABLED", "walletDepositState": "DISABLED", "walletWithdrawalState": "DISABLED", "supportCollateral": true, "supportBorrow": true, "parentChain": null, "isMultiChain": true, "isChildChain": false, "childChains": ["USDTBSC", "USDTETH", "USDTSOL", "USDTTRON"]}, "id": "USDT", "numericId": 214, "network": "OMNI", "active": null, "deposit": null, "withdraw": null, "fee": 0, "precision": null, "limits": {"withdraw": {"min": null, "max": null}, "deposit": {"min": null, "max": null}}}}, "limits": {"amount": {"min": null, "max": null}, "withdraw": {"min": null, "max": null}, "deposit": {"min": null, "max": null}}}, "LTC": {"info": {"id": 125, "name": "Litecoin", "description": "BTC Clone", "type": "address", "withdrawalFee": "0.********", "minConf": 4, "depositAddress": null, "blockchain": "LTC", "delisted": false, "tradingState": "NORMAL", "walletState": "ENABLED", "walletDepositState": "ENABLED", "walletWithdrawalState": "ENABLED", "supportCollateral": true, "supportBorrow": false, "parentChain": null, "isMultiChain": true, "isChildChain": false, "childChains": ["LTCTRON"]}, "id": "LTC", "numericId": 125, "code": "LTC", "precision": null, "type": "crypto", "name": "Litecoin", "active": null, "deposit": null, "withdraw": null, "fee": 0.01, "fees": {}, "networks": {"TRC20": {"info": {"id": 494, "name": "LTC on TRON", "description": "Sweep to Main Account", "type": "address", "withdrawalFee": "0.********", "minConf": 0, "depositAddress": null, "blockchain": "TRX", "delisted": false, "tradingState": "OFFLINE", "walletState": "DISABLED", "walletDepositState": "DISABLED", "walletWithdrawalState": "DISABLED", "supportCollateral": false, "supportBorrow": false, "parentChain": "LTC", "isMultiChain": true, "isChildChain": true, "childChains": []}, "id": "LTCTRON", "numericId": 494, "network": "TRC20", "active": null, "deposit": null, "withdraw": null, "fee": 0.02, "precision": null, "limits": {"withdraw": {"min": null, "max": null}, "deposit": {"min": null, "max": null}}}, "LTC": {"info": {"id": 125, "name": "Litecoin", "description": "BTC Clone", "type": "address", "withdrawalFee": "0.********", "minConf": 4, "depositAddress": null, "blockchain": "LTC", "delisted": false, "tradingState": "NORMAL", "walletState": "ENABLED", "walletDepositState": "ENABLED", "walletWithdrawalState": "ENABLED", "supportCollateral": true, "supportBorrow": false, "parentChain": null, "isMultiChain": true, "isChildChain": false, "childChains": ["LTCTRON"]}, "id": "LTC", "numericId": 125, "network": "LTC", "active": null, "deposit": null, "withdraw": null, "fee": 0.01, "precision": null, "limits": {"withdraw": {"min": null, "max": null}, "deposit": {"min": null, "max": null}}}}, "limits": {"amount": {"min": null, "max": null}, "withdraw": {"min": null, "max": null}, "deposit": {"min": null, "max": null}}}, "ETH": {"info": {"id": 267, "name": "Ethereum", "description": "Sweep to Main Account", "type": "address", "withdrawalFee": "0.********", "minConf": 64, "depositAddress": null, "blockchain": "ETH", "delisted": false, "tradingState": "NORMAL", "walletState": "ENABLED", "walletDepositState": "ENABLED", "walletWithdrawalState": "ENABLED", "supportCollateral": true, "supportBorrow": true, "parentChain": null, "isMultiChain": true, "isChildChain": false, "childChains": ["ETHBLAST", "ETHBSC"]}, "id": "ETH", "numericId": 267, "code": "ETH", "precision": null, "type": "crypto", "name": "Ethereum", "active": null, "deposit": null, "withdraw": null, "fee": 0, "fees": {}, "networks": {"ETHBLAST": {"info": {"id": 1690, "name": "Blast Network", "description": "Sweep to Main Account", "type": "address", "withdrawalFee": "0.********", "minConf": 64, "depositAddress": null, "blockchain": "ETHBLAST", "delisted": false, "tradingState": "OFFLINE", "walletState": "ENABLED", "walletDepositState": "DISABLED", "walletWithdrawalState": "DISABLED", "supportCollateral": false, "supportBorrow": false, "parentChain": "ETH", "isMultiChain": true, "isChildChain": true, "childChains": []}, "id": "ETHBLAST", "numericId": 1690, "network": "ETHBLAST", "active": null, "deposit": null, "withdraw": null, "fee": 0, "precision": null, "limits": {"withdraw": {"min": null, "max": null}, "deposit": {"min": null, "max": null}}}, "BEP20": {"info": {"id": 580, "name": "Binance-Peg Ethereum Token", "description": "Sweep to Main Account", "type": "address", "withdrawalFee": "0.********", "minConf": 15, "depositAddress": null, "blockchain": "BSC", "delisted": false, "tradingState": "OFFLINE", "walletState": "DISABLED", "walletDepositState": "DISABLED", "walletWithdrawalState": "DISABLED", "supportCollateral": false, "supportBorrow": false, "parentChain": "ETH", "isMultiChain": true, "isChildChain": true, "childChains": []}, "id": "ETHBSC", "numericId": 580, "network": "BEP20", "active": null, "deposit": null, "withdraw": null, "fee": 0.0006, "precision": null, "limits": {"withdraw": {"min": null, "max": null}, "deposit": {"min": null, "max": null}}}, "ETH": {"info": {"id": 267, "name": "Ethereum", "description": "Sweep to Main Account", "type": "address", "withdrawalFee": "0.********", "minConf": 64, "depositAddress": null, "blockchain": "ETH", "delisted": false, "tradingState": "NORMAL", "walletState": "ENABLED", "walletDepositState": "ENABLED", "walletWithdrawalState": "ENABLED", "supportCollateral": true, "supportBorrow": true, "parentChain": null, "isMultiChain": true, "isChildChain": false, "childChains": ["ETHBLAST", "ETHBSC"]}, "id": "ETH", "numericId": 267, "network": "ETH", "active": null, "deposit": null, "withdraw": null, "fee": 0.********, "precision": null, "limits": {"withdraw": {"min": null, "max": null}, "deposit": {"min": null, "max": null}}}}, "limits": {"amount": {"min": null, "max": null}, "withdraw": {"min": null, "max": null}, "deposit": {"min": null, "max": null}}}, "ADA": {"info": {"id": 668, "name": "Cardano", "description": "BTC Clone", "type": "address", "withdrawalFee": "3.********", "minConf": 30, "depositAddress": null, "blockchain": "ADA", "delisted": false, "tradingState": "NORMAL", "walletState": "ENABLED", "walletDepositState": "ENABLED", "walletWithdrawalState": "ENABLED", "supportCollateral": false, "supportBorrow": false, "parentChain": null, "isMultiChain": true, "isChildChain": false, "childChains": []}, "id": "ADA", "numericId": 668, "code": "ADA", "precision": null, "type": "crypto", "name": "Cardano", "active": null, "deposit": null, "withdraw": null, "fee": 3, "fees": {}, "networks": {"ADA": {"info": {"id": 668, "name": "Cardano", "description": "BTC Clone", "type": "address", "withdrawalFee": "3.********", "minConf": 30, "depositAddress": null, "blockchain": "ADA", "delisted": false, "tradingState": "NORMAL", "walletState": "ENABLED", "walletDepositState": "ENABLED", "walletWithdrawalState": "ENABLED", "supportCollateral": false, "supportBorrow": false, "parentChain": null, "isMultiChain": true, "isChildChain": false, "childChains": []}, "id": "ADA", "numericId": 668, "network": "ADA", "active": null, "deposit": null, "withdraw": null, "fee": 3, "precision": null, "limits": {"withdraw": {"min": null, "max": null}, "deposit": {"min": null, "max": null}}}}, "limits": {"amount": {"min": null, "max": null}, "withdraw": {"min": null, "max": null}, "deposit": {"min": null, "max": null}}}, "XRP": {"info": {"id": 243, "name": "XRP", "description": "Payment ID", "type": "address-payment-id", "withdrawalFee": "0.********", "minConf": 2, "depositAddress": "rwJXYKC1VMzGYc6RHnhnbe38syj5EE34cS", "blockchain": "XRP", "delisted": false, "tradingState": "NORMAL", "walletState": "ENABLED", "walletDepositState": "ENABLED", "walletWithdrawalState": "ENABLED", "supportCollateral": true, "supportBorrow": true, "parentChain": null, "isMultiChain": false, "isChildChain": false, "childChains": []}, "id": "XRP", "numericId": 243, "code": "XRP", "precision": null, "type": "crypto", "name": "XRP", "active": null, "deposit": null, "withdraw": null, "fee": 0.2, "fees": {}, "networks": {"XRP": {"info": {"id": 243, "name": "XRP", "description": "Payment ID", "type": "address-payment-id", "withdrawalFee": "0.********", "minConf": 2, "depositAddress": "rwJXYKC1VMzGYc6RHnhnbe38syj5EE34cS", "blockchain": "XRP", "delisted": false, "tradingState": "NORMAL", "walletState": "ENABLED", "walletDepositState": "ENABLED", "walletWithdrawalState": "ENABLED", "supportCollateral": true, "supportBorrow": true, "parentChain": null, "isMultiChain": false, "isChildChain": false, "childChains": []}, "id": "XRP", "numericId": 243, "network": "XRP", "active": null, "deposit": null, "withdraw": null, "fee": 0.2, "precision": null, "limits": {"withdraw": {"min": null, "max": null}, "deposit": {"min": null, "max": null}}}}, "limits": {"amount": {"min": null, "max": null}, "withdraw": {"min": null, "max": null}, "deposit": {"min": null, "max": null}}}, "USDC": {"info": {"id": 299, "name": "USD Coin", "description": "Sweep to Main Account", "type": "address", "withdrawalFee": "1.********", "minConf": 64, "depositAddress": null, "blockchain": "ETH", "delisted": false, "tradingState": "NORMAL", "walletState": "ENABLED", "walletDepositState": "ENABLED", "walletWithdrawalState": "ENABLED", "supportCollateral": false, "supportBorrow": false, "parentChain": null, "isMultiChain": true, "isChildChain": false, "childChains": ["USDCBSC", "USDCSOL", "USDCTRON"]}, "id": "USDC", "numericId": 299, "code": "USDC", "precision": null, "type": "crypto", "name": "USD Coin", "active": null, "deposit": null, "withdraw": null, "fee": 0.3, "fees": {}, "networks": {"BEP20": {"info": {"id": 583, "name": "Binance-Peg USD Coin", "description": "Sweep to Main Account", "type": "address", "withdrawalFee": "0.********", "minConf": 15, "depositAddress": null, "blockchain": "BSC", "delisted": false, "tradingState": "OFFLINE", "walletState": "ENABLED", "walletDepositState": "ENABLED", "walletWithdrawalState": "DISABLED", "supportCollateral": false, "supportBorrow": false, "parentChain": "USDC", "isMultiChain": true, "isChildChain": true, "childChains": []}, "id": "USDCBSC", "numericId": 583, "network": "BEP20", "active": null, "deposit": null, "withdraw": null, "fee": 0.3, "precision": null, "limits": {"withdraw": {"min": null, "max": null}, "deposit": {"min": null, "max": null}}}, "SOL": {"info": {"id": 1696, "name": "USDCSOL", "description": "Sweep to Main Account", "type": "address", "withdrawalFee": "0.********", "minConf": 300, "depositAddress": null, "blockchain": "SOL", "delisted": false, "tradingState": "OFFLINE", "walletState": "ENABLED", "walletDepositState": "ENABLED", "walletWithdrawalState": "DISABLED", "supportCollateral": false, "supportBorrow": false, "parentChain": "USDC", "isMultiChain": true, "isChildChain": true, "childChains": []}, "id": "USDCSOL", "numericId": 1696, "network": "SOL", "active": null, "deposit": null, "withdraw": null, "fee": 0.81, "precision": null, "limits": {"withdraw": {"min": null, "max": null}, "deposit": {"min": null, "max": null}}}, "TRC20": {"info": {"id": 1952, "name": "USD Coin", "description": "Sweep to Main Account", "type": "address", "withdrawalFee": "2.********", "minConf": 0, "depositAddress": null, "blockchain": "TRX", "delisted": false, "tradingState": "OFFLINE", "walletState": "ENABLED", "walletDepositState": "DISABLED", "walletWithdrawalState": "DISABLED", "supportCollateral": false, "supportBorrow": false, "parentChain": "USDC", "isMultiChain": true, "isChildChain": true, "childChains": []}, "id": "USDCTRON", "numericId": 1952, "network": "TRC20", "active": null, "deposit": null, "withdraw": null, "fee": 2, "precision": null, "limits": {"withdraw": {"min": null, "max": null}, "deposit": {"min": null, "max": null}}}, "ERC20": {"info": {"id": 299, "name": "USD Coin", "description": "Sweep to Main Account", "type": "address", "withdrawalFee": "1.********", "minConf": 64, "depositAddress": null, "blockchain": "ETH", "delisted": false, "tradingState": "NORMAL", "walletState": "ENABLED", "walletDepositState": "ENABLED", "walletWithdrawalState": "ENABLED", "supportCollateral": false, "supportBorrow": false, "parentChain": null, "isMultiChain": true, "isChildChain": false, "childChains": ["USDCBSC", "USDCSOL", "USDCTRON"]}, "id": "USDC", "numericId": 299, "network": "ERC20", "active": null, "deposit": null, "withdraw": null, "fee": 1.811256, "precision": null, "limits": {"withdraw": {"min": null, "max": null}, "deposit": {"min": null, "max": null}}}}, "limits": {"amount": {"min": null, "max": null}, "withdraw": {"min": null, "max": null}, "deposit": {"min": null, "max": null}}}}