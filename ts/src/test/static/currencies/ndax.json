{"BTC": {"info": {"OMSId": "1", "ProductId": "1", "Product": "BTC", "ProductFullName": "Bitcoin", "MasterDataUniqueProductSymbol": "", "ProductType": "CryptoCurrency", "DecimalPlaces": "8", "TickSize": "0.0000000100000000000000000000", "DepositEnabled": true, "WithdrawEnabled": true, "NoFees": false, "IsDisabled": false, "MarginEnabled": false}, "id": "1", "numericId": null, "code": "BTC", "precision": 1e-08, "type": "crypto", "name": "Bitcoin", "active": true, "deposit": true, "withdraw": true, "fee": null, "fees": {}, "networks": {}, "limits": {"amount": {"min": null, "max": null}, "withdraw": {"min": null, "max": null}}, "margin": false}, "USDT": {"info": {"OMSId": "1", "ProductId": "16", "Product": "USDT", "ProductFullName": "<PERSON><PERSON>", "MasterDataUniqueProductSymbol": "", "ProductType": "CryptoCurrency", "DecimalPlaces": "2", "TickSize": "0.0100000000000000000000000000", "DepositEnabled": true, "WithdrawEnabled": true, "NoFees": false, "IsDisabled": false, "MarginEnabled": false}, "id": "16", "numericId": null, "code": "USDT", "precision": 0.01, "type": "crypto", "name": "<PERSON><PERSON>", "active": true, "deposit": true, "withdraw": true, "fee": null, "fees": {}, "networks": {}, "limits": {"amount": {"min": null, "max": null}, "withdraw": {"min": null, "max": null}}, "margin": false}, "EUR": {"info": {"OMSId": "1", "ProductId": "44", "Product": "EUR", "ProductFullName": "EUR", "MasterDataUniqueProductSymbol": "", "ProductType": "NationalCurrency", "DecimalPlaces": "2", "TickSize": "0.0100000000000000000000000000", "DepositEnabled": true, "WithdrawEnabled": true, "NoFees": false, "IsDisabled": false, "MarginEnabled": false}, "id": "44", "numericId": null, "code": "EUR", "precision": 0.01, "type": "fiat", "name": "EUR", "active": true, "deposit": true, "withdraw": true, "fee": null, "fees": {}, "networks": {}, "limits": {"amount": {"min": null, "max": null}, "withdraw": {"min": null, "max": null}}, "margin": false}, "USD": {"info": {"OMSId": "1", "ProductId": "6", "Product": "USD", "ProductFullName": "United States Dollar", "MasterDataUniqueProductSymbol": "", "ProductType": "NationalCurrency", "DecimalPlaces": "8", "TickSize": "0.0000000100000000000000000000", "DepositEnabled": true, "WithdrawEnabled": true, "NoFees": false, "IsDisabled": false, "MarginEnabled": false}, "id": "6", "numericId": null, "code": "USD", "precision": 1e-08, "type": "fiat", "name": "United States Dollar", "active": true, "deposit": true, "withdraw": true, "fee": null, "fees": {}, "networks": {}, "limits": {"amount": {"min": null, "max": null}, "withdraw": {"min": null, "max": null}}, "margin": false}, "LTC": {"info": {"OMSId": "1", "ProductId": "2", "Product": "LTC", "ProductFullName": "Litecoin", "MasterDataUniqueProductSymbol": "", "ProductType": "CryptoCurrency", "DecimalPlaces": "8", "TickSize": "0.0000000100000000000000000000", "DepositEnabled": true, "WithdrawEnabled": true, "NoFees": false, "IsDisabled": false, "MarginEnabled": false}, "id": "2", "numericId": null, "code": "LTC", "precision": 1e-08, "type": "crypto", "name": "Litecoin", "active": true, "deposit": true, "withdraw": true, "fee": null, "fees": {}, "networks": {}, "limits": {"amount": {"min": null, "max": null}, "withdraw": {"min": null, "max": null}}, "margin": false}, "ETH": {"info": {"OMSId": "1", "ProductId": "3", "Product": "ETH", "ProductFullName": "<PERSON><PERSON>", "MasterDataUniqueProductSymbol": "", "ProductType": "CryptoCurrency", "DecimalPlaces": "8", "TickSize": "0.0000000100000000000000000000", "DepositEnabled": true, "WithdrawEnabled": true, "NoFees": false, "IsDisabled": false, "MarginEnabled": false}, "id": "3", "numericId": null, "code": "ETH", "precision": 1e-08, "type": "crypto", "name": "<PERSON><PERSON>", "active": true, "deposit": true, "withdraw": true, "fee": null, "fees": {}, "networks": {}, "limits": {"amount": {"min": null, "max": null}, "withdraw": {"min": null, "max": null}}, "margin": false}, "ADA": {"info": {"OMSId": "1", "ProductId": "14", "Product": "ADA", "ProductFullName": "Cardano", "MasterDataUniqueProductSymbol": "", "ProductType": "CryptoCurrency", "DecimalPlaces": "5", "TickSize": "0.0000100000000000000000000000", "DepositEnabled": true, "WithdrawEnabled": true, "NoFees": false, "IsDisabled": false, "MarginEnabled": false}, "id": "14", "numericId": null, "code": "ADA", "precision": 1e-05, "type": "crypto", "name": "Cardano", "active": true, "deposit": true, "withdraw": true, "fee": null, "fees": {}, "networks": {}, "limits": {"amount": {"min": null, "max": null}, "withdraw": {"min": null, "max": null}}, "margin": false}, "XRP": {"info": {"OMSId": "1", "ProductId": "4", "Product": "XRP", "ProductFullName": "XRP", "MasterDataUniqueProductSymbol": "", "ProductType": "CryptoCurrency", "DecimalPlaces": "4", "TickSize": "0.0001000000000000000000000000", "DepositEnabled": true, "WithdrawEnabled": true, "NoFees": false, "IsDisabled": false, "MarginEnabled": false}, "id": "4", "numericId": null, "code": "XRP", "precision": 0.0001, "type": "crypto", "name": "XRP", "active": true, "deposit": true, "withdraw": true, "fee": null, "fees": {}, "networks": {}, "limits": {"amount": {"min": null, "max": null}, "withdraw": {"min": null, "max": null}}, "margin": false}, "USDC": {"info": {"OMSId": "1", "ProductId": "24", "Product": "USDC", "ProductFullName": "USDC", "MasterDataUniqueProductSymbol": "", "ProductType": "CryptoCurrency", "DecimalPlaces": "2", "TickSize": "0.0100000000000000000000000000", "DepositEnabled": true, "WithdrawEnabled": true, "NoFees": false, "IsDisabled": false, "MarginEnabled": false}, "id": "24", "numericId": null, "code": "USDC", "precision": 0.01, "type": "crypto", "name": "USDC", "active": true, "deposit": true, "withdraw": true, "fee": null, "fees": {}, "networks": {}, "limits": {"amount": {"min": null, "max": null}, "withdraw": {"min": null, "max": null}}, "margin": false}, "SOL": {"info": {"OMSId": "1", "ProductId": "34", "Product": "SOL", "ProductFullName": "Solana", "MasterDataUniqueProductSymbol": "", "ProductType": "CryptoCurrency", "DecimalPlaces": "4", "TickSize": "0.0001000000000000000000000000", "DepositEnabled": true, "WithdrawEnabled": true, "NoFees": false, "IsDisabled": false, "MarginEnabled": false}, "id": "34", "numericId": null, "code": "SOL", "precision": 0.0001, "type": "crypto", "name": "Solana", "active": true, "deposit": true, "withdraw": true, "fee": null, "fees": {}, "networks": {}, "limits": {"amount": {"min": null, "max": null}, "withdraw": {"min": null, "max": null}}, "margin": false}, "CAD": {"info": {"OMSId": "1", "ProductId": "7", "Product": "CAD", "ProductFullName": "Canadian Dollar", "MasterDataUniqueProductSymbol": "", "ProductType": "NationalCurrency", "DecimalPlaces": "8", "TickSize": "0.0000000100000000000000000000", "DepositEnabled": true, "WithdrawEnabled": true, "NoFees": false, "IsDisabled": false, "MarginEnabled": false}, "id": "7", "numericId": null, "code": "CAD", "precision": 1e-08, "type": "fiat", "name": "Canadian Dollar", "active": true, "deposit": true, "withdraw": true, "fee": null, "fees": {}, "networks": {}, "limits": {"amount": {"min": null, "max": null}, "withdraw": {"min": null, "max": null}}, "margin": false}}