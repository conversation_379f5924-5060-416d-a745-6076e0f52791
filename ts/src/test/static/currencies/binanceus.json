{"BTC": {"id": "BTC", "name": "Bitcoin", "code": "BTC", "precision": 1e-08, "info": {"coin": "BTC", "depositAllEnable": true, "withdrawAllEnable": true, "name": "Bitcoin", "free": "0", "locked": "0", "freeze": "0", "withdrawing": "0", "ipoing": "0", "ipoable": "0", "storage": "0", "isLegalMoney": false, "trading": true, "networkList": [{"network": "BNB", "coin": "BTC", "withdrawIntegerMultiple": "0.00000001", "isDefault": false, "depositEnable": true, "withdrawEnable": false, "depositDesc": "", "withdrawDesc": "Wallet Maintenance, Withdrawal Suspended", "specialTips": "Both a MEMO and an Address are required to successfully deposit your BEP2-BTCB tokens to Binance.", "name": "BNB Beacon Chain (BEP2)", "resetAddressStatus": false, "addressRegex": "^(bnb1)[0-9a-z]{38}$", "memoRegex": "^[0-9A-Za-z\\-_]{1,120}$", "withdrawFee": "0.0000066", "withdrawMin": "0.000013", "withdrawMax": "10000000000", "minConfirm": "1", "unLockConfirm": "0"}, {"network": "BSC", "coin": "BTC", "withdrawIntegerMultiple": "0.00000001", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "name": "BNB Smart Chain (BEP20)", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "0.0000033", "withdrawMin": "0.0000066", "withdrawMax": "10000000000", "minConfirm": "15", "unLockConfirm": "0"}, {"network": "BTC", "coin": "BTC", "withdrawIntegerMultiple": "0.00000001", "isDefault": true, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "name": "Bitcoin", "resetAddressStatus": false, "addressRegex": "^[13][a-km-zA-HJ-NP-Z1-9]{25,34}$|^[(bc1q)|(bc1p)][0-9A-Za-z]{37,62}$", "memoRegex": "", "withdrawFee": "0.00025", "withdrawMin": "0.0005", "withdrawMax": "10000000000", "minConfirm": "1", "unLockConfirm": "2"}]}, "active": true, "deposit": true, "withdraw": true, "networks": [{"network": "BNB", "coin": "BTC", "withdrawIntegerMultiple": "0.00000001", "isDefault": false, "depositEnable": true, "withdrawEnable": false, "depositDesc": "", "withdrawDesc": "Wallet Maintenance, Withdrawal Suspended", "specialTips": "Both a MEMO and an Address are required to successfully deposit your BEP2-BTCB tokens to Binance.", "name": "BNB Beacon Chain (BEP2)", "resetAddressStatus": false, "addressRegex": "^(bnb1)[0-9a-z]{38}$", "memoRegex": "^[0-9A-Za-z\\-_]{1,120}$", "withdrawFee": "0.0000066", "withdrawMin": "0.000013", "withdrawMax": "10000000000", "minConfirm": "1", "unLockConfirm": "0"}, {"network": "BSC", "coin": "BTC", "withdrawIntegerMultiple": "0.00000001", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "name": "BNB Smart Chain (BEP20)", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "0.0000033", "withdrawMin": "0.0000066", "withdrawMax": "10000000000", "minConfirm": "15", "unLockConfirm": "0"}, {"network": "BTC", "coin": "BTC", "withdrawIntegerMultiple": "0.00000001", "isDefault": true, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "name": "Bitcoin", "resetAddressStatus": false, "addressRegex": "^[13][a-km-zA-HJ-NP-Z1-9]{25,34}$|^[(bc1q)|(bc1p)][0-9A-Za-z]{37,62}$", "memoRegex": "", "withdrawFee": "0.00025", "withdrawMin": "0.0005", "withdrawMax": "10000000000", "minConfirm": "1", "unLockConfirm": "2"}], "fee": 0.00025, "fees": {"BNB": 6.6e-06, "BSC": 3.3e-06, "BTC": 0.00025}, "limits": {"leverage": {}, "amount": {}, "price": {}, "cost": {}}}, "USDT": {"id": "USDT", "name": "TetherUS", "code": "USDT", "precision": 1e-08, "info": {"coin": "USDT", "depositAllEnable": true, "withdrawAllEnable": true, "name": "TetherUS", "free": "0", "locked": "0", "freeze": "0", "withdrawing": "0", "ipoing": "0", "ipoable": "0", "storage": "0", "isLegalMoney": false, "trading": true, "networkList": [{"network": "AVAXC", "coin": "USDT", "withdrawIntegerMultiple": "0.000001", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "name": "AVAX C-Chain", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "1", "withdrawMin": "50", "withdrawMax": "9999999", "minConfirm": "12", "unLockConfirm": "0"}, {"network": "BNB", "coin": "USDT", "withdrawIntegerMultiple": "0.000001", "isDefault": false, "depositEnable": true, "withdrawEnable": false, "depositDesc": "", "withdrawDesc": "", "specialTips": "Both a MEMO and an Address are required to successfully deposit your BEP2 tokens to Binance US.", "name": "BNB Beacon Chain (BEP2)", "resetAddressStatus": false, "addressRegex": "^(bnb1)[0-9a-z]{38}$", "memoRegex": "^[0-9A-Za-z\\-_]{1,120}$", "withdrawFee": "1", "withdrawMin": "10", "withdrawMax": "10000000000", "minConfirm": "1", "unLockConfirm": "0"}, {"network": "BSC", "coin": "USDT", "withdrawIntegerMultiple": "0.00000001", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "name": "BNB Smart Chain (BEP20)", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "0.29", "withdrawMin": "10", "withdrawMax": "10000000000", "minConfirm": "15", "unLockConfirm": "0"}, {"network": "ETH", "coin": "USDT", "withdrawIntegerMultiple": "0.000001", "isDefault": true, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "name": "Ethereum (ERC20)", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "8", "withdrawMin": "50", "withdrawMax": "10000000000", "minConfirm": "6", "unLockConfirm": "64"}, {"network": "MATIC", "coin": "USDT", "withdrawIntegerMultiple": "0.000001", "isDefault": false, "depositEnable": true, "withdrawEnable": false, "depositDesc": "", "specialTips": "", "name": "Polygon", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "1", "withdrawMin": "10", "withdrawMax": "10000000000", "minConfirm": "300", "unLockConfirm": "800"}, {"network": "SOL", "coin": "USDT", "withdrawIntegerMultiple": "0.000001", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "name": "Solana", "resetAddressStatus": false, "addressRegex": "^[1-9A-HJ-NP-Za-km-z]{32,44}$", "memoRegex": "", "withdrawFee": "1", "withdrawMin": "2", "withdrawMax": "9999999", "minConfirm": "1", "unLockConfirm": "0"}, {"network": "TRX", "coin": "USDT", "withdrawIntegerMultiple": "0.000001", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "name": "Tron (TRC20)", "resetAddressStatus": false, "addressRegex": "^T[1-9A-HJ-NP-Za-km-z]{33}$", "memoRegex": "", "withdrawFee": "3", "withdrawMin": "10", "withdrawMax": "10000000000", "minConfirm": "1", "unLockConfirm": "0"}]}, "active": true, "deposit": true, "withdraw": true, "networks": [{"network": "AVAXC", "coin": "USDT", "withdrawIntegerMultiple": "0.000001", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "name": "AVAX C-Chain", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "1", "withdrawMin": "50", "withdrawMax": "9999999", "minConfirm": "12", "unLockConfirm": "0"}, {"network": "BNB", "coin": "USDT", "withdrawIntegerMultiple": "0.000001", "isDefault": false, "depositEnable": true, "withdrawEnable": false, "depositDesc": "", "withdrawDesc": "", "specialTips": "Both a MEMO and an Address are required to successfully deposit your BEP2 tokens to Binance US.", "name": "BNB Beacon Chain (BEP2)", "resetAddressStatus": false, "addressRegex": "^(bnb1)[0-9a-z]{38}$", "memoRegex": "^[0-9A-Za-z\\-_]{1,120}$", "withdrawFee": "1", "withdrawMin": "10", "withdrawMax": "10000000000", "minConfirm": "1", "unLockConfirm": "0"}, {"network": "BSC", "coin": "USDT", "withdrawIntegerMultiple": "0.00000001", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "name": "BNB Smart Chain (BEP20)", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "0.29", "withdrawMin": "10", "withdrawMax": "10000000000", "minConfirm": "15", "unLockConfirm": "0"}, {"network": "ETH", "coin": "USDT", "withdrawIntegerMultiple": "0.000001", "isDefault": true, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "name": "Ethereum (ERC20)", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "8", "withdrawMin": "50", "withdrawMax": "10000000000", "minConfirm": "6", "unLockConfirm": "64"}, {"network": "MATIC", "coin": "USDT", "withdrawIntegerMultiple": "0.000001", "isDefault": false, "depositEnable": true, "withdrawEnable": false, "depositDesc": "", "specialTips": "", "name": "Polygon", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "1", "withdrawMin": "10", "withdrawMax": "10000000000", "minConfirm": "300", "unLockConfirm": "800"}, {"network": "SOL", "coin": "USDT", "withdrawIntegerMultiple": "0.000001", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "name": "Solana", "resetAddressStatus": false, "addressRegex": "^[1-9A-HJ-NP-Za-km-z]{32,44}$", "memoRegex": "", "withdrawFee": "1", "withdrawMin": "2", "withdrawMax": "9999999", "minConfirm": "1", "unLockConfirm": "0"}, {"network": "TRX", "coin": "USDT", "withdrawIntegerMultiple": "0.000001", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "name": "Tron (TRC20)", "resetAddressStatus": false, "addressRegex": "^T[1-9A-HJ-NP-Za-km-z]{33}$", "memoRegex": "", "withdrawFee": "3", "withdrawMin": "10", "withdrawMax": "10000000000", "minConfirm": "1", "unLockConfirm": "0"}], "fee": 8, "fees": {"AVAXC": 1, "BNB": 1, "BSC": 0.29, "ETH": 8, "MATIC": 1, "SOL": 1, "TRX": 3}, "limits": {"leverage": {}, "amount": {}, "price": {}, "cost": {}}}, "LTC": {"id": "LTC", "name": "Litecoin", "code": "LTC", "precision": 1e-08, "info": {"coin": "LTC", "depositAllEnable": true, "withdrawAllEnable": true, "name": "Litecoin", "free": "0", "locked": "0", "freeze": "0", "withdrawing": "0", "ipoing": "0", "ipoable": "0", "storage": "0", "isLegalMoney": false, "trading": true, "networkList": [{"network": "BNB", "coin": "LTC", "withdrawIntegerMultiple": "0.00000001", "isDefault": false, "depositEnable": true, "withdrawEnable": false, "depositDesc": "", "specialTips": "Both a MEMO and an Address are required to successfully deposit your LTC BEP2 tokens to Binance.", "name": "BNB Beacon Chain (BEP2)", "resetAddressStatus": false, "addressRegex": "^(bnb1)[0-9a-z]{38}$", "memoRegex": "^[0-9A-Za-z\\-_]{1,120}$", "withdrawFee": "0.0034", "withdrawMin": "0.0068", "withdrawMax": "10000000000", "minConfirm": "1", "unLockConfirm": "0"}, {"network": "BSC", "coin": "LTC", "withdrawIntegerMultiple": "0.00000001", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "name": "BNB Smart Chain (BEP20)", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "0.0017", "withdrawMin": "0.0034", "withdrawMax": "10000000000", "minConfirm": "15", "unLockConfirm": "0"}, {"network": "LTC", "coin": "LTC", "withdrawIntegerMultiple": "0.00000001", "isDefault": true, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "name": "Litecoin", "resetAddressStatus": false, "addressRegex": "^(L|M)[A-Za-z0-9]{33}$|^(ltc1)[0-9A-Za-z]{39}$", "memoRegex": "", "withdrawFee": "0.001", "withdrawMin": "0.002", "withdrawMax": "10000000000", "minConfirm": "3", "unLockConfirm": "4"}]}, "active": true, "deposit": true, "withdraw": true, "networks": [{"network": "BNB", "coin": "LTC", "withdrawIntegerMultiple": "0.00000001", "isDefault": false, "depositEnable": true, "withdrawEnable": false, "depositDesc": "", "specialTips": "Both a MEMO and an Address are required to successfully deposit your LTC BEP2 tokens to Binance.", "name": "BNB Beacon Chain (BEP2)", "resetAddressStatus": false, "addressRegex": "^(bnb1)[0-9a-z]{38}$", "memoRegex": "^[0-9A-Za-z\\-_]{1,120}$", "withdrawFee": "0.0034", "withdrawMin": "0.0068", "withdrawMax": "10000000000", "minConfirm": "1", "unLockConfirm": "0"}, {"network": "BSC", "coin": "LTC", "withdrawIntegerMultiple": "0.00000001", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "name": "BNB Smart Chain (BEP20)", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "0.0017", "withdrawMin": "0.0034", "withdrawMax": "10000000000", "minConfirm": "15", "unLockConfirm": "0"}, {"network": "LTC", "coin": "LTC", "withdrawIntegerMultiple": "0.00000001", "isDefault": true, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "name": "Litecoin", "resetAddressStatus": false, "addressRegex": "^(L|M)[A-Za-z0-9]{33}$|^(ltc1)[0-9A-Za-z]{39}$", "memoRegex": "", "withdrawFee": "0.001", "withdrawMin": "0.002", "withdrawMax": "10000000000", "minConfirm": "3", "unLockConfirm": "4"}], "fee": 0.001, "fees": {"BNB": 0.0034, "BSC": 0.0017, "LTC": 0.001}, "limits": {"leverage": {}, "amount": {}, "price": {}, "cost": {}}}, "ETH": {"id": "ETH", "name": "Ethereum", "code": "ETH", "precision": 1e-08, "info": {"coin": "ETH", "depositAllEnable": true, "withdrawAllEnable": true, "name": "Ethereum", "free": "0", "locked": "0", "freeze": "0", "withdrawing": "0", "ipoing": "0", "ipoable": "0", "storage": "0", "isLegalMoney": false, "trading": true, "networkList": [{"network": "ARBITRUM", "coin": "ETH", "withdrawIntegerMultiple": "0.00000001", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "name": "Arbitrum One", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "0.00035", "withdrawMin": "0.0008", "withdrawMax": "9999999", "minConfirm": "100", "unLockConfirm": "0"}, {"network": "BNB", "coin": "ETH", "withdrawIntegerMultiple": "0.00000001", "isDefault": false, "depositEnable": true, "withdrawEnable": false, "depositDesc": "", "withdrawDesc": "", "specialTips": "Both a MEMO and an Address are required to successfully deposit your BEP2 tokens to Binance US.", "name": "BNB Beacon Chain (BEP2)", "resetAddressStatus": false, "addressRegex": "^(bnb1)[0-9a-z]{38}$", "memoRegex": "^[0-9A-Za-z\\-_]{1,120}$", "withdrawFee": "0.000086", "withdrawMin": "0.0005", "withdrawMax": "10000000000", "minConfirm": "1", "unLockConfirm": "0"}, {"network": "BSC", "coin": "ETH", "withdrawIntegerMultiple": "0.00000001", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "name": "BNB Smart Chain (BEP20)", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "0.000059", "withdrawMin": "0.00012", "withdrawMax": "10000000000", "minConfirm": "15", "unLockConfirm": "0"}, {"network": "ETH", "coin": "ETH", "withdrawIntegerMultiple": "0.00000001", "isDefault": true, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "name": "Ethereum (ERC20)", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "0.00155", "withdrawMin": "0.0031", "withdrawMax": "10000000000", "minConfirm": "6", "unLockConfirm": "64"}, {"network": "OPTIMISM", "coin": "ETH", "withdrawIntegerMultiple": "0.00000001", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "name": "Optimism", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "0.00035", "withdrawMin": "0.001", "withdrawMax": "10000000000", "minConfirm": "100", "unLockConfirm": "120"}]}, "active": true, "deposit": true, "withdraw": true, "networks": [{"network": "ARBITRUM", "coin": "ETH", "withdrawIntegerMultiple": "0.00000001", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "name": "Arbitrum One", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "0.00035", "withdrawMin": "0.0008", "withdrawMax": "9999999", "minConfirm": "100", "unLockConfirm": "0"}, {"network": "BNB", "coin": "ETH", "withdrawIntegerMultiple": "0.00000001", "isDefault": false, "depositEnable": true, "withdrawEnable": false, "depositDesc": "", "withdrawDesc": "", "specialTips": "Both a MEMO and an Address are required to successfully deposit your BEP2 tokens to Binance US.", "name": "BNB Beacon Chain (BEP2)", "resetAddressStatus": false, "addressRegex": "^(bnb1)[0-9a-z]{38}$", "memoRegex": "^[0-9A-Za-z\\-_]{1,120}$", "withdrawFee": "0.000086", "withdrawMin": "0.0005", "withdrawMax": "10000000000", "minConfirm": "1", "unLockConfirm": "0"}, {"network": "BSC", "coin": "ETH", "withdrawIntegerMultiple": "0.00000001", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "name": "BNB Smart Chain (BEP20)", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "0.000059", "withdrawMin": "0.00012", "withdrawMax": "10000000000", "minConfirm": "15", "unLockConfirm": "0"}, {"network": "ETH", "coin": "ETH", "withdrawIntegerMultiple": "0.00000001", "isDefault": true, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "name": "Ethereum (ERC20)", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "0.00155", "withdrawMin": "0.0031", "withdrawMax": "10000000000", "minConfirm": "6", "unLockConfirm": "64"}, {"network": "OPTIMISM", "coin": "ETH", "withdrawIntegerMultiple": "0.00000001", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "name": "Optimism", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "0.00035", "withdrawMin": "0.001", "withdrawMax": "10000000000", "minConfirm": "100", "unLockConfirm": "120"}], "fee": 0.00155, "fees": {"ARBITRUM": 0.00035, "BNB": 8.6e-05, "BSC": 5.9e-05, "ETH": 0.00155, "OPTIMISM": 0.00035}, "limits": {"leverage": {}, "amount": {}, "price": {}, "cost": {}}}, "ADA": {"id": "ADA", "name": "Cardano", "code": "ADA", "precision": 1e-08, "info": {"coin": "ADA", "depositAllEnable": true, "withdrawAllEnable": true, "name": "Cardano", "free": "0", "locked": "0", "freeze": "0", "withdrawing": "0", "ipoing": "0", "ipoable": "0", "storage": "0", "isLegalMoney": false, "trading": true, "networkList": [{"network": "ADA", "coin": "ADA", "withdrawIntegerMultiple": "0.000001", "isDefault": true, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "name": "Cardano", "resetAddressStatus": false, "addressRegex": "^(([0-9A-Za-z]{57,59})|([0-9A-Za-z]{100,104}))$", "memoRegex": "", "withdrawFee": "1", "withdrawMin": "10", "withdrawMax": "999998", "minConfirm": "30", "unLockConfirm": "0"}, {"network": "BNB", "coin": "ADA", "withdrawIntegerMultiple": "0.00000001", "isDefault": false, "depositEnable": true, "withdrawEnable": false, "depositDesc": "", "specialTips": "Both a MEMO and an Address are required to successfully deposit your BEP2 tokens to Binance.", "name": "BNB Beacon Chain (BEP2)", "resetAddressStatus": false, "addressRegex": "^(bnb1)[0-9a-z]{38}$", "memoRegex": "^[0-9A-Za-z\\-_]{1,120}$", "withdrawFee": "0.68", "withdrawMin": "1.36", "withdrawMax": "10000000000", "minConfirm": "1", "unLockConfirm": "0"}, {"network": "BSC", "coin": "ADA", "withdrawIntegerMultiple": "0.00000001", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "name": "BNB Smart Chain (BEP20)", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "0.34", "withdrawMin": "0.68", "withdrawMax": "10000000000", "minConfirm": "15", "unLockConfirm": "0"}]}, "active": true, "deposit": true, "withdraw": true, "networks": [{"network": "ADA", "coin": "ADA", "withdrawIntegerMultiple": "0.000001", "isDefault": true, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "name": "Cardano", "resetAddressStatus": false, "addressRegex": "^(([0-9A-Za-z]{57,59})|([0-9A-Za-z]{100,104}))$", "memoRegex": "", "withdrawFee": "1", "withdrawMin": "10", "withdrawMax": "999998", "minConfirm": "30", "unLockConfirm": "0"}, {"network": "BNB", "coin": "ADA", "withdrawIntegerMultiple": "0.00000001", "isDefault": false, "depositEnable": true, "withdrawEnable": false, "depositDesc": "", "specialTips": "Both a MEMO and an Address are required to successfully deposit your BEP2 tokens to Binance.", "name": "BNB Beacon Chain (BEP2)", "resetAddressStatus": false, "addressRegex": "^(bnb1)[0-9a-z]{38}$", "memoRegex": "^[0-9A-Za-z\\-_]{1,120}$", "withdrawFee": "0.68", "withdrawMin": "1.36", "withdrawMax": "10000000000", "minConfirm": "1", "unLockConfirm": "0"}, {"network": "BSC", "coin": "ADA", "withdrawIntegerMultiple": "0.00000001", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "name": "BNB Smart Chain (BEP20)", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "0.34", "withdrawMin": "0.68", "withdrawMax": "10000000000", "minConfirm": "15", "unLockConfirm": "0"}], "fee": 1, "fees": {"ADA": 1, "BNB": 0.68, "BSC": 0.34}, "limits": {"leverage": {}, "amount": {}, "price": {}, "cost": {}}}, "XRP": {"id": "XRP", "name": "<PERSON><PERSON><PERSON>", "code": "XRP", "precision": 1e-08, "info": {"coin": "XRP", "depositAllEnable": true, "withdrawAllEnable": true, "name": "<PERSON><PERSON><PERSON>", "free": "0", "locked": "0", "freeze": "0", "withdrawing": "0", "ipoing": "0", "ipoable": "0", "storage": "0", "isLegalMoney": false, "trading": true, "networkList": [{"network": "BNB", "coin": "XRP", "withdrawIntegerMultiple": "0.00000001", "isDefault": false, "depositEnable": false, "withdrawEnable": false, "depositDesc": "Wallet Maintenance, Deposit Suspended", "withdrawDesc": "Wallet Maintenance, Withdrawal Suspended", "specialTips": "Both a MEMO and an Address are required to successfully deposit your XRP BEP2 tokens to Binance.", "name": "BNB Beacon Chain (BEP2)", "resetAddressStatus": false, "addressRegex": "^(bnb1)[0-9a-z]{38}$", "memoRegex": "^[0-9A-Za-z\\-_]{1,120}$", "withdrawFee": "0.37", "withdrawMin": "0.7", "withdrawMax": "10000000000", "minConfirm": "1", "unLockConfirm": "0"}, {"network": "XRP", "coin": "XRP", "withdrawIntegerMultiple": "0.000001", "isDefault": true, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "name": "<PERSON><PERSON><PERSON>", "resetAddressStatus": false, "addressRegex": "^r[1-9A-HJ-NP-Za-km-z]{25,34}$", "memoRegex": "^((?!0)[0-9]{1,10})$", "withdrawFee": "0.3", "withdrawMin": "30", "withdrawMax": "10000000000", "minConfirm": "1", "unLockConfirm": "0"}]}, "active": true, "deposit": true, "withdraw": true, "networks": [{"network": "BNB", "coin": "XRP", "withdrawIntegerMultiple": "0.00000001", "isDefault": false, "depositEnable": false, "withdrawEnable": false, "depositDesc": "Wallet Maintenance, Deposit Suspended", "withdrawDesc": "Wallet Maintenance, Withdrawal Suspended", "specialTips": "Both a MEMO and an Address are required to successfully deposit your XRP BEP2 tokens to Binance.", "name": "BNB Beacon Chain (BEP2)", "resetAddressStatus": false, "addressRegex": "^(bnb1)[0-9a-z]{38}$", "memoRegex": "^[0-9A-Za-z\\-_]{1,120}$", "withdrawFee": "0.37", "withdrawMin": "0.7", "withdrawMax": "10000000000", "minConfirm": "1", "unLockConfirm": "0"}, {"network": "XRP", "coin": "XRP", "withdrawIntegerMultiple": "0.000001", "isDefault": true, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "name": "<PERSON><PERSON><PERSON>", "resetAddressStatus": false, "addressRegex": "^r[1-9A-HJ-NP-Za-km-z]{25,34}$", "memoRegex": "^((?!0)[0-9]{1,10})$", "withdrawFee": "0.3", "withdrawMin": "30", "withdrawMax": "10000000000", "minConfirm": "1", "unLockConfirm": "0"}], "fee": 0.3, "fees": {"BNB": 0.37, "XRP": 0.3}, "limits": {"leverage": {}, "amount": {}, "price": {}, "cost": {}}}, "USDC": {"id": "USDC", "name": "USD Coin", "code": "USDC", "precision": 1e-08, "info": {"coin": "USDC", "depositAllEnable": true, "withdrawAllEnable": true, "name": "USD Coin", "free": "0", "locked": "0", "freeze": "0", "withdrawing": "0", "ipoing": "0", "ipoable": "0", "storage": "0", "isLegalMoney": false, "trading": true, "networkList": [{"network": "AVAXC", "coin": "USDC", "withdrawIntegerMultiple": "0.000001", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "name": "AVAX C-Chain", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "1", "withdrawMin": "50", "withdrawMax": "9999999", "minConfirm": "12", "unLockConfirm": "0"}, {"network": "BSC", "coin": "USDC", "withdrawIntegerMultiple": "0.00000001", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "name": "BNB Smart Chain (BEP20)", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "0.29", "withdrawMin": "10", "withdrawMax": "10000000000", "minConfirm": "15", "unLockConfirm": "0"}, {"network": "ETH", "coin": "USDC", "withdrawIntegerMultiple": "0.000001", "isDefault": true, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "name": "Ethereum (ERC20)", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "9.94", "withdrawMin": "19.88", "withdrawMax": "10000000000", "minConfirm": "6", "unLockConfirm": "64"}, {"network": "MATIC", "coin": "USDC", "withdrawIntegerMultiple": "0.000001", "isDefault": false, "depositEnable": false, "withdrawEnable": false, "specialTips": "", "name": "Polygon", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "1", "withdrawMin": "10", "withdrawMax": "9999999", "minConfirm": "300", "unLockConfirm": "800"}, {"network": "SOL", "coin": "USDC", "withdrawIntegerMultiple": "0.000001", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "name": "Solana", "resetAddressStatus": false, "addressRegex": "^[1-9A-HJ-NP-Za-km-z]{32,44}$", "memoRegex": "", "withdrawFee": "1", "withdrawMin": "10", "withdrawMax": "250000100", "minConfirm": "1", "unLockConfirm": "0"}, {"network": "TRX", "coin": "USDC", "withdrawIntegerMultiple": "0.000001", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "name": "Tron (TRC20)", "resetAddressStatus": false, "addressRegex": "^T[1-9A-HJ-NP-Za-km-z]{33}$", "memoRegex": "", "withdrawFee": "1.5", "withdrawMin": "10", "withdrawMax": "10000000000", "minConfirm": "1", "unLockConfirm": "0"}]}, "active": true, "deposit": true, "withdraw": true, "networks": [{"network": "AVAXC", "coin": "USDC", "withdrawIntegerMultiple": "0.000001", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "name": "AVAX C-Chain", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "1", "withdrawMin": "50", "withdrawMax": "9999999", "minConfirm": "12", "unLockConfirm": "0"}, {"network": "BSC", "coin": "USDC", "withdrawIntegerMultiple": "0.00000001", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "name": "BNB Smart Chain (BEP20)", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "0.29", "withdrawMin": "10", "withdrawMax": "10000000000", "minConfirm": "15", "unLockConfirm": "0"}, {"network": "ETH", "coin": "USDC", "withdrawIntegerMultiple": "0.000001", "isDefault": true, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "name": "Ethereum (ERC20)", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "9.94", "withdrawMin": "19.88", "withdrawMax": "10000000000", "minConfirm": "6", "unLockConfirm": "64"}, {"network": "MATIC", "coin": "USDC", "withdrawIntegerMultiple": "0.000001", "isDefault": false, "depositEnable": false, "withdrawEnable": false, "specialTips": "", "name": "Polygon", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "1", "withdrawMin": "10", "withdrawMax": "9999999", "minConfirm": "300", "unLockConfirm": "800"}, {"network": "SOL", "coin": "USDC", "withdrawIntegerMultiple": "0.000001", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "name": "Solana", "resetAddressStatus": false, "addressRegex": "^[1-9A-HJ-NP-Za-km-z]{32,44}$", "memoRegex": "", "withdrawFee": "1", "withdrawMin": "10", "withdrawMax": "250000100", "minConfirm": "1", "unLockConfirm": "0"}, {"network": "TRX", "coin": "USDC", "withdrawIntegerMultiple": "0.000001", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "name": "Tron (TRC20)", "resetAddressStatus": false, "addressRegex": "^T[1-9A-HJ-NP-Za-km-z]{33}$", "memoRegex": "", "withdrawFee": "1.5", "withdrawMin": "10", "withdrawMax": "10000000000", "minConfirm": "1", "unLockConfirm": "0"}], "fee": 9.94, "fees": {"AVAXC": 1, "BSC": 0.29, "ETH": 9.94, "MATIC": 1, "SOL": 1, "TRX": 1.5}, "limits": {"leverage": {}, "amount": {}, "price": {}, "cost": {}}}}