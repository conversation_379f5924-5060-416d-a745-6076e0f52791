{"BTC": {"info": {"symbol": "BTC", "name": "Bitcoin", "color": "#FFA500", "type": "coin", "canDeposit": true, "canWithdraw": true, "canTrade": true, "notabeneDecimals": "8", "canMarket": true, "maxSwap": "10000", "digits": "6", "multiplier": "1000000", "bookDigits": "8", "bookMultiplier": "100000000", "sentimentData": {"sentiment": "50.35444444444445", "interest": "0.9811717053176572"}, "minQty": "0.00005"}, "id": "BTC", "numericId": null, "code": "BTC", "precision": 1e-06, "type": "crypto", "name": "BTC", "active": true, "deposit": true, "withdraw": true, "fee": null, "fees": {}, "networks": {}, "limits": {"amount": {"min": 5e-05, "max": null}, "withdraw": {"min": null, "max": null}}}, "ETH": {"info": {"symbol": "ETH", "name": "Ethereum", "color": "#CCCCCC", "type": "coin", "canDeposit": true, "canWithdraw": true, "canTrade": true, "notabeneDecimals": "18", "canMarket": true, "maxSwap": "10000", "digits": "4", "multiplier": "10000", "bookDigits": "6", "bookMultiplier": "1000000", "sentimentData": {"sentiment": "46.21777777777777", "interest": "0.854381778089728"}, "minQty": "0.002"}, "id": "ETH", "numericId": null, "code": "ETH", "precision": 0.0001, "type": "crypto", "name": "ETH", "active": true, "deposit": true, "withdraw": true, "fee": null, "fees": {}, "networks": {}, "limits": {"amount": {"min": 0.002, "max": null}, "withdraw": {"min": null, "max": null}}}, "USDT": {"info": {"symbol": "USDT", "name": "<PERSON><PERSON>", "type": "erc20", "canDeposit": true, "canWithdraw": true, "canTrade": true, "notabeneDecimals": "6", "canMarket": true, "maxSwap": "500", "digits": "2", "multiplier": "100", "bookDigits": "3", "bookMultiplier": "1000", "sentimentData": {"sentiment": "49.41777777777777", "interest": "1.0259279424741377"}, "minQty": "5"}, "id": "USDT", "numericId": null, "code": "USDT", "precision": 0.01, "type": "crypto", "name": "USDT", "active": true, "deposit": true, "withdraw": true, "fee": null, "fees": {}, "networks": {}, "limits": {"amount": {"min": 5, "max": null}, "withdraw": {"min": null, "max": null}}}, "USDC": {"info": {"symbol": "USDC", "name": "USDC", "color": "#2775CA", "type": "token", "canDeposit": true, "canWithdraw": true, "canTrade": true, "notabeneDecimals": "6", "canMarket": true, "maxSwap": "10000", "digits": "2", "multiplier": "100", "bookDigits": "3", "bookMultiplier": "1000", "minQty": "6"}, "id": "USDC", "numericId": null, "code": "USDC", "precision": 0.01, "type": "crypto", "name": "USDC", "active": true, "deposit": true, "withdraw": true, "fee": null, "fees": {}, "networks": {}, "limits": {"amount": {"min": 6, "max": null}, "withdraw": {"min": null, "max": null}}}, "EUR": {"info": {"symbol": "EUR", "name": "Euro", "color": "#1246FF", "type": "fiat", "canDeposit": true, "canWithdraw": true, "canTrade": true, "canMarket": true, "maxSwap": "10000", "digits": "2", "multiplier": "100", "bookDigits": "3", "bookMultiplier": "1000", "minQty": "5"}, "id": "EUR", "numericId": null, "code": "EUR", "precision": 0.01, "type": "fiat", "name": "EUR", "active": true, "deposit": true, "withdraw": true, "fee": null, "fees": {}, "networks": {}, "limits": {"amount": {"min": 5, "max": null}, "withdraw": {"min": null, "max": null}}}, "LTC": {"info": {"symbol": "LTC", "name": "Litecoin", "color": "#345D9D", "type": "coin", "canDeposit": true, "canWithdraw": true, "canTrade": true, "notabeneDecimals": "8", "canMarket": true, "maxSwap": "5000", "digits": "4", "multiplier": "10000", "bookDigits": "6", "bookMultiplier": "1000000", "sentimentData": {"sentiment": "58.75777777777777", "interest": "0.9192667337857687"}, "minQty": "0.06"}, "id": "LTC", "numericId": null, "code": "LTC", "precision": 0.0001, "type": "crypto", "name": "LTC", "active": true, "deposit": true, "withdraw": true, "fee": null, "fees": {}, "networks": {}, "limits": {"amount": {"min": 0.06, "max": null}, "withdraw": {"min": null, "max": null}}}, "DOGE": {"info": {"symbol": "DOGE", "name": "DOGEcoin", "color": "#FFDA82", "type": "coin", "canDeposit": true, "canWithdraw": true, "canTrade": true, "notabeneDecimals": "8", "canMarket": true, "maxSwap": "500", "digits": "1", "multiplier": "10", "bookDigits": "4", "bookMultiplier": "10000", "sentimentData": {"sentiment": "55.96888888888889", "interest": "0.9798765858788889"}, "minQty": "20"}, "id": "DOGE", "numericId": null, "code": "DOGE", "precision": 0.1, "type": "crypto", "name": "DOGE", "active": true, "deposit": true, "withdraw": true, "fee": null, "fees": {}, "networks": {}, "limits": {"amount": {"min": 20, "max": null}, "withdraw": {"min": null, "max": null}}}, "ADA": {"info": {"symbol": "ADA", "name": "Cardano", "color": "#2F69F4", "contrastColor": "", "type": "coin", "canDeposit": true, "canWithdraw": true, "canTrade": true, "notabeneDecimals": "6", "canMarket": true, "maxSwap": "5000", "digits": "2", "multiplier": "100", "bookDigits": "3", "bookMultiplier": "1000", "sentimentData": {"sentiment": "25.30111111111111", "interest": "0.8757131927361556"}, "minQty": "7"}, "id": "ADA", "numericId": null, "code": "ADA", "precision": 0.01, "type": "crypto", "name": "ADA", "active": true, "deposit": true, "withdraw": true, "fee": null, "fees": {}, "networks": {}, "limits": {"amount": {"min": 7, "max": null}, "withdraw": {"min": null, "max": null}}}}