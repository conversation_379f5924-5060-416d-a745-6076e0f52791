{"BTC": {"info": {"chains": [{"browserUrl": "https://blockchair.com/bitcoin/transaction/", "chain": "BITCOIN", "chainId": "10", "coinChainId": "10", "depositConfirm": "1", "extraWithDrawFee": "0", "minDepositAmount": "0.00001", "minWithdrawAmount": "0.001", "needTag": "false", "rechargeable": "true", "shortName": "BTC", "withdrawConfirm": "1", "withdrawFee": "0.00008", "withdrawable": "true"}], "coinDisplayName": "BTC", "coinId": "1", "coinName": "BTC", "transfer": "true"}, "id": "BTC", "numericId": 1, "code": "BTC", "precision": null, "type": null, "name": null, "active": true, "deposit": true, "withdraw": true, "fee": 8e-05, "fees": {}, "networks": {"BITCOIN": {"id": "BITCOIN", "network": "BITCOIN", "limits": {"deposit": {"min": 1e-05, "max": null}, "withdraw": {"min": 0.001, "max": null}}, "active": true, "deposit": true, "withdraw": true, "fee": 8e-05, "precision": null, "info": {"browserUrl": "https://blockchair.com/bitcoin/transaction/", "chain": "BITCOIN", "chainId": "10", "coinChainId": "10", "depositConfirm": "1", "extraWithDrawFee": "0", "minDepositAmount": "0.00001", "minWithdrawAmount": "0.001", "needTag": "false", "rechargeable": "true", "shortName": "BTC", "withdrawConfirm": "1", "withdrawFee": "0.00008", "withdrawable": "true"}}}, "limits": {"deposit": {"min": 1e-05, "max": null}, "withdraw": {"min": 0.001, "max": null}}}, "USDT": {"info": {"chains": [{"browserUrl": "https://etherscan.io/tx/", "chain": "ERC20", "chainId": "70", "coinChainId": "70", "depositConfirm": "12", "extraWithDrawFee": "0", "minDepositAmount": "0.9", "minWithdrawAmount": "1", "needTag": "false", "rechargeable": "true", "shortName": "ETH", "withdrawConfirm": "64", "withdrawFee": "3", "withdrawable": "true"}, {"browserUrl": "https://tronscan.org/#/transaction/", "chain": "TRC20", "chainId": "73", "coinChainId": "73", "depositConfirm": "1", "extraWithDrawFee": "0", "minDepositAmount": "1", "minWithdrawAmount": "1", "needTag": "false", "rechargeable": "true", "shortName": "TRX", "withdrawConfirm": "1", "withdrawFee": "1", "withdrawable": "true"}, {"browserUrl": "https://bscscan.com/tx/", "chain": "BEP20", "chainId": "1000", "coinChainId": "1000", "depositConfirm": "15", "extraWithDrawFee": "0", "minDepositAmount": "0.01", "minWithdrawAmount": "0.01", "needTag": "false", "rechargeable": "true", "shortName": "BSC", "withdrawConfirm": "15", "withdrawFee": "0.29", "withdrawable": "true"}, {"browserUrl": "https://arbiscan.io/tx/", "chain": "ArbitrumOne", "chainId": "4720", "coinChainId": "4720", "depositConfirm": "24", "extraWithDrawFee": "0", "minDepositAmount": "0.1", "minWithdrawAmount": "10", "needTag": "false", "rechargeable": "true", "shortName": "ARBITRUMONE", "withdrawConfirm": "24", "withdrawFee": "0.8", "withdrawable": "true"}, {"browserUrl": "https://solscan.io/tx/", "chain": "SOL", "chainId": "3000", "coinChainId": "3000", "depositConfirm": "10", "extraWithDrawFee": "0", "minDepositAmount": "0.0014", "minWithdrawAmount": "1", "needTag": "false", "rechargeable": "true", "shortName": "SOL", "withdrawConfirm": "10", "withdrawFee": "1", "withdrawable": "true"}, {"browserUrl": "https://polygonscan.com/tx/", "chain": "Polygon", "chainId": "4400", "coinChainId": "4400", "depositConfirm": "60", "extraWithDrawFee": "0", "minDepositAmount": "0.01", "minWithdrawAmount": "10", "needTag": "false", "rechargeable": "true", "shortName": "POL", "withdrawConfirm": "60", "withdrawFee": "0.2", "withdrawable": "true"}], "coinDisplayName": "USDT", "coinId": "2", "coinName": "USDT", "transfer": "true"}, "id": "USDT", "numericId": 2, "code": "USDT", "precision": null, "type": null, "name": null, "active": true, "deposit": true, "withdraw": true, "fee": 0.2, "fees": {}, "networks": {"ERC20": {"id": "ERC20", "network": "ERC20", "limits": {"deposit": {"min": 0.9, "max": null}, "withdraw": {"min": 1, "max": null}}, "active": true, "deposit": true, "withdraw": true, "fee": 3, "precision": null, "info": {"browserUrl": "https://etherscan.io/tx/", "chain": "ERC20", "chainId": "70", "coinChainId": "70", "depositConfirm": "12", "extraWithDrawFee": "0", "minDepositAmount": "0.9", "minWithdrawAmount": "1", "needTag": "false", "rechargeable": "true", "shortName": "ETH", "withdrawConfirm": "64", "withdrawFee": "3", "withdrawable": "true"}}, "TRC20": {"id": "TRC20", "network": "TRC20", "limits": {"deposit": {"min": 1, "max": null}, "withdraw": {"min": 1, "max": null}}, "active": true, "deposit": true, "withdraw": true, "fee": 1, "precision": null, "info": {"browserUrl": "https://tronscan.org/#/transaction/", "chain": "TRC20", "chainId": "73", "coinChainId": "73", "depositConfirm": "1", "extraWithDrawFee": "0", "minDepositAmount": "1", "minWithdrawAmount": "1", "needTag": "false", "rechargeable": "true", "shortName": "TRX", "withdrawConfirm": "1", "withdrawFee": "1", "withdrawable": "true"}}, "BEP20": {"id": "BEP20", "network": "BEP20", "limits": {"deposit": {"min": 0.01, "max": null}, "withdraw": {"min": 0.01, "max": null}}, "active": true, "deposit": true, "withdraw": true, "fee": 0.29, "precision": null, "info": {"browserUrl": "https://bscscan.com/tx/", "chain": "BEP20", "chainId": "1000", "coinChainId": "1000", "depositConfirm": "15", "extraWithDrawFee": "0", "minDepositAmount": "0.01", "minWithdrawAmount": "0.01", "needTag": "false", "rechargeable": "true", "shortName": "BSC", "withdrawConfirm": "15", "withdrawFee": "0.29", "withdrawable": "true"}}, "ArbitrumOne": {"id": "ArbitrumOne", "network": "ArbitrumOne", "limits": {"deposit": {"min": 0.1, "max": null}, "withdraw": {"min": 10, "max": null}}, "active": true, "deposit": true, "withdraw": true, "fee": 0.8, "precision": null, "info": {"browserUrl": "https://arbiscan.io/tx/", "chain": "ArbitrumOne", "chainId": "4720", "coinChainId": "4720", "depositConfirm": "24", "extraWithDrawFee": "0", "minDepositAmount": "0.1", "minWithdrawAmount": "10", "needTag": "false", "rechargeable": "true", "shortName": "ARBITRUMONE", "withdrawConfirm": "24", "withdrawFee": "0.8", "withdrawable": "true"}}, "SOL": {"id": "SOL", "network": "SOL", "limits": {"deposit": {"min": 0.0014, "max": null}, "withdraw": {"min": 1, "max": null}}, "active": true, "deposit": true, "withdraw": true, "fee": 1, "precision": null, "info": {"browserUrl": "https://solscan.io/tx/", "chain": "SOL", "chainId": "3000", "coinChainId": "3000", "depositConfirm": "10", "extraWithDrawFee": "0", "minDepositAmount": "0.0014", "minWithdrawAmount": "1", "needTag": "false", "rechargeable": "true", "shortName": "SOL", "withdrawConfirm": "10", "withdrawFee": "1", "withdrawable": "true"}}, "Polygon": {"id": "Polygon", "network": "Polygon", "limits": {"deposit": {"min": 0.01, "max": null}, "withdraw": {"min": 10, "max": null}}, "active": true, "deposit": true, "withdraw": true, "fee": 0.2, "precision": null, "info": {"browserUrl": "https://polygonscan.com/tx/", "chain": "Polygon", "chainId": "4400", "coinChainId": "4400", "depositConfirm": "60", "extraWithDrawFee": "0", "minDepositAmount": "0.01", "minWithdrawAmount": "10", "needTag": "false", "rechargeable": "true", "shortName": "POL", "withdrawConfirm": "60", "withdrawFee": "0.2", "withdrawable": "true"}}}, "limits": {"deposit": {"min": 0.0014, "max": null}, "withdraw": {"min": 0.01, "max": null}}}, "LTC": {"info": {"chains": [{"browserUrl": "https://blockchair.com/litecoin/transaction/", "chain": "LTC", "chainId": "0", "coinChainId": "0", "depositConfirm": "6", "extraWithDrawFee": "0", "minDepositAmount": "0.0001", "minWithdrawAmount": "0.1", "needTag": "false", "rechargeable": "true", "shortName": "LTC", "withdrawConfirm": "1", "withdrawFee": "0.001", "withdrawable": "true"}], "coinDisplayName": "LTC", "coinId": "5", "coinName": "LTC", "transfer": "false"}, "id": "LTC", "numericId": 5, "code": "LTC", "precision": null, "type": null, "name": null, "active": true, "deposit": true, "withdraw": true, "fee": 0.001, "fees": {}, "networks": {"LTC": {"id": "LTC", "network": "LTC", "limits": {"deposit": {"min": 0.0001, "max": null}, "withdraw": {"min": 0.1, "max": null}}, "active": true, "deposit": true, "withdraw": true, "fee": 0.001, "precision": null, "info": {"browserUrl": "https://blockchair.com/litecoin/transaction/", "chain": "LTC", "chainId": "0", "coinChainId": "0", "depositConfirm": "6", "extraWithDrawFee": "0", "minDepositAmount": "0.0001", "minWithdrawAmount": "0.1", "needTag": "false", "rechargeable": "true", "shortName": "LTC", "withdrawConfirm": "1", "withdrawFee": "0.001", "withdrawable": "true"}}}, "limits": {"deposit": {"min": 0.0001, "max": null}, "withdraw": {"min": 0.1, "max": null}}}, "ETH": {"info": {"chains": [{"browserUrl": "https://etherscan.io/tx/", "chain": "ERC20", "chainId": "70", "coinChainId": "70", "depositConfirm": "12", "extraWithDrawFee": "0", "minDepositAmount": "0.00001", "minWithdrawAmount": "0.001", "needTag": "false", "rechargeable": "true", "shortName": "ETH", "withdrawConfirm": "64", "withdrawFee": "0.00076", "withdrawable": "true"}, {"browserUrl": "https://optimistic.etherscan.io/tx/", "chain": "Optimism", "chainId": "4746", "coinChainId": "4746", "depositConfirm": "50", "extraWithDrawFee": "0", "minDepositAmount": "0.0001", "minWithdrawAmount": "0.0001", "needTag": "false", "rechargeable": "true", "shortName": "OPTIMISM", "withdrawConfirm": "50", "withdrawFee": "0.00023", "withdrawable": "false"}, {"browserUrl": "https://basescan.org/tx/", "chain": "BASE", "chainId": "5831", "coinChainId": "5831", "depositConfirm": "30", "extraWithDrawFee": "0", "minDepositAmount": "0.00004", "minWithdrawAmount": "0.03", "needTag": "false", "rechargeable": "true", "shortName": "BASE", "withdrawConfirm": "120", "withdrawFee": "0.00004", "withdrawable": "false"}], "coinDisplayName": "ETH", "coinId": "3", "coinName": "ETH", "transfer": "true"}, "id": "ETH", "numericId": 3, "code": "ETH", "precision": null, "type": null, "name": null, "active": true, "deposit": true, "withdraw": true, "fee": 4e-05, "fees": {}, "networks": {"ERC20": {"id": "ERC20", "network": "ERC20", "limits": {"deposit": {"min": 1e-05, "max": null}, "withdraw": {"min": 0.001, "max": null}}, "active": true, "deposit": true, "withdraw": true, "fee": 0.00076, "precision": null, "info": {"browserUrl": "https://etherscan.io/tx/", "chain": "ERC20", "chainId": "70", "coinChainId": "70", "depositConfirm": "12", "extraWithDrawFee": "0", "minDepositAmount": "0.00001", "minWithdrawAmount": "0.001", "needTag": "false", "rechargeable": "true", "shortName": "ETH", "withdrawConfirm": "64", "withdrawFee": "0.00076", "withdrawable": "true"}}, "Optimism": {"id": "Optimism", "network": "Optimism", "limits": {"deposit": {"min": 0.0001, "max": null}, "withdraw": {"min": 0.0001, "max": null}}, "active": false, "deposit": true, "withdraw": false, "fee": 0.00023, "precision": null, "info": {"browserUrl": "https://optimistic.etherscan.io/tx/", "chain": "Optimism", "chainId": "4746", "coinChainId": "4746", "depositConfirm": "50", "extraWithDrawFee": "0", "minDepositAmount": "0.0001", "minWithdrawAmount": "0.0001", "needTag": "false", "rechargeable": "true", "shortName": "OPTIMISM", "withdrawConfirm": "50", "withdrawFee": "0.00023", "withdrawable": "false"}}, "BASE": {"id": "BASE", "network": "BASE", "limits": {"deposit": {"min": 4e-05, "max": null}, "withdraw": {"min": 0.03, "max": null}}, "active": false, "deposit": true, "withdraw": false, "fee": 4e-05, "precision": null, "info": {"browserUrl": "https://basescan.org/tx/", "chain": "BASE", "chainId": "5831", "coinChainId": "5831", "depositConfirm": "30", "extraWithDrawFee": "0", "minDepositAmount": "0.00004", "minWithdrawAmount": "0.03", "needTag": "false", "rechargeable": "true", "shortName": "BASE", "withdrawConfirm": "120", "withdrawFee": "0.00004", "withdrawable": "false"}}}, "limits": {"deposit": {"min": 1e-05, "max": null}, "withdraw": {"min": 0.0001, "max": null}}}, "ADA": {"info": {"chains": [{"browserUrl": "https://cardanoscan.io/transaction/", "chain": "Cardano", "chainId": "4705", "coinChainId": "4705", "depositConfirm": "30", "extraWithDrawFee": "0", "minDepositAmount": "2", "minWithdrawAmount": "4", "needTag": "false", "rechargeable": "true", "shortName": "ADA", "withdrawConfirm": "30", "withdrawFee": "0.8", "withdrawable": "true"}], "coinDisplayName": "ADA", "coinId": "125", "coinName": "ADA", "transfer": "false"}, "id": "ADA", "numericId": 125, "code": "ADA", "precision": null, "type": null, "name": null, "active": true, "deposit": true, "withdraw": true, "fee": 0.8, "fees": {}, "networks": {"Cardano": {"id": "Cardano", "network": "Cardano", "limits": {"deposit": {"min": 2, "max": null}, "withdraw": {"min": 4, "max": null}}, "active": true, "deposit": true, "withdraw": true, "fee": 0.8, "precision": null, "info": {"browserUrl": "https://cardanoscan.io/transaction/", "chain": "Cardano", "chainId": "4705", "coinChainId": "4705", "depositConfirm": "30", "extraWithDrawFee": "0", "minDepositAmount": "2", "minWithdrawAmount": "4", "needTag": "false", "rechargeable": "true", "shortName": "ADA", "withdrawConfirm": "30", "withdrawFee": "0.8", "withdrawable": "true"}}}, "limits": {"deposit": {"min": 2, "max": null}, "withdraw": {"min": 4, "max": null}}}, "XRP": {"info": {"chains": [{"browserUrl": "https://bithomp.com/explorer/", "chain": "XRP", "chainId": "0", "coinChainId": "0", "depositConfirm": "10", "extraWithDrawFee": "0", "minDepositAmount": "0.1", "minWithdrawAmount": "11", "needTag": "true", "rechargeable": "true", "shortName": "XRP", "withdrawConfirm": "10", "withdrawFee": "0.1", "withdrawable": "true"}], "coinDisplayName": "XRP", "coinId": "56", "coinName": "XRP", "transfer": "true"}, "id": "XRP", "numericId": 56, "code": "XRP", "precision": null, "type": null, "name": null, "active": true, "deposit": true, "withdraw": true, "fee": 0.1, "fees": {}, "networks": {"XRP": {"id": "XRP", "network": "XRP", "limits": {"deposit": {"min": 0.1, "max": null}, "withdraw": {"min": 11, "max": null}}, "active": true, "deposit": true, "withdraw": true, "fee": 0.1, "precision": null, "info": {"browserUrl": "https://bithomp.com/explorer/", "chain": "XRP", "chainId": "0", "coinChainId": "0", "depositConfirm": "10", "extraWithDrawFee": "0", "minDepositAmount": "0.1", "minWithdrawAmount": "11", "needTag": "true", "rechargeable": "true", "shortName": "XRP", "withdrawConfirm": "10", "withdrawFee": "0.1", "withdrawable": "true"}}}, "limits": {"deposit": {"min": 0.1, "max": null}, "withdraw": {"min": 11, "max": null}}}, "USDC": {"info": {"chains": [{"browserUrl": "https://etherscan.io/tx/", "chain": "ERC20", "chainId": "70", "coinChainId": "70", "depositConfirm": "12", "extraWithDrawFee": "0", "minDepositAmount": "1", "minWithdrawAmount": "50", "needTag": "false", "rechargeable": "true", "shortName": "ETH", "withdrawConfirm": "64", "withdrawFee": "1.5", "withdrawable": "true"}, {"browserUrl": "https://solscan.io/tx/", "chain": "SOL", "chainId": "3000", "coinChainId": "3000", "depositConfirm": "5", "extraWithDrawFee": "0", "minDepositAmount": "0.01", "minWithdrawAmount": "30", "needTag": "false", "rechargeable": "true", "shortName": "SOL", "withdrawConfirm": "5", "withdrawFee": "3", "withdrawable": "true"}, {"browserUrl": "https://bscscan.com/tx/", "chain": "BEP20", "chainId": "1000", "coinChainId": "1000", "depositConfirm": "15", "extraWithDrawFee": "0", "minDepositAmount": "0.01", "minWithdrawAmount": "10", "needTag": "false", "rechargeable": "true", "shortName": "BSC", "withdrawConfirm": "15", "withdrawFee": "1.0003001", "withdrawable": "false"}, {"browserUrl": "https://basescan.org/tx/", "chain": "BASE", "chainId": "5831", "coinChainId": "5831", "depositConfirm": "50", "extraWithDrawFee": "0", "minDepositAmount": "0.01", "minWithdrawAmount": "10", "needTag": "false", "rechargeable": "true", "shortName": "BASE", "withdrawConfirm": "120", "withdrawFee": "0.09998001", "withdrawable": "false"}, {"browserUrl": "https://polygonscan.com/tx/", "chain": "Polygon", "chainId": "4400", "coinChainId": "4400", "depositConfirm": "60", "extraWithDrawFee": "0", "minDepositAmount": "0.1", "minWithdrawAmount": "10", "needTag": "false", "rechargeable": "true", "shortName": "POL", "withdrawConfirm": "60", "withdrawFee": "0.64", "withdrawable": "false"}], "coinDisplayName": "USDC", "coinId": "74", "coinName": "USDC", "transfer": "false"}, "id": "USDC", "numericId": 74, "code": "USDC", "precision": null, "type": null, "name": null, "active": true, "deposit": true, "withdraw": true, "fee": 0.09998001, "fees": {}, "networks": {"ERC20": {"id": "ERC20", "network": "ERC20", "limits": {"deposit": {"min": 1, "max": null}, "withdraw": {"min": 50, "max": null}}, "active": true, "deposit": true, "withdraw": true, "fee": 1.5, "precision": null, "info": {"browserUrl": "https://etherscan.io/tx/", "chain": "ERC20", "chainId": "70", "coinChainId": "70", "depositConfirm": "12", "extraWithDrawFee": "0", "minDepositAmount": "1", "minWithdrawAmount": "50", "needTag": "false", "rechargeable": "true", "shortName": "ETH", "withdrawConfirm": "64", "withdrawFee": "1.5", "withdrawable": "true"}}, "SOL": {"id": "SOL", "network": "SOL", "limits": {"deposit": {"min": 0.01, "max": null}, "withdraw": {"min": 30, "max": null}}, "active": true, "deposit": true, "withdraw": true, "fee": 3, "precision": null, "info": {"browserUrl": "https://solscan.io/tx/", "chain": "SOL", "chainId": "3000", "coinChainId": "3000", "depositConfirm": "5", "extraWithDrawFee": "0", "minDepositAmount": "0.01", "minWithdrawAmount": "30", "needTag": "false", "rechargeable": "true", "shortName": "SOL", "withdrawConfirm": "5", "withdrawFee": "3", "withdrawable": "true"}}, "BEP20": {"id": "BEP20", "network": "BEP20", "limits": {"deposit": {"min": 0.01, "max": null}, "withdraw": {"min": 10, "max": null}}, "active": false, "deposit": true, "withdraw": false, "fee": 1.0003001, "precision": null, "info": {"browserUrl": "https://bscscan.com/tx/", "chain": "BEP20", "chainId": "1000", "coinChainId": "1000", "depositConfirm": "15", "extraWithDrawFee": "0", "minDepositAmount": "0.01", "minWithdrawAmount": "10", "needTag": "false", "rechargeable": "true", "shortName": "BSC", "withdrawConfirm": "15", "withdrawFee": "1.0003001", "withdrawable": "false"}}, "BASE": {"id": "BASE", "network": "BASE", "limits": {"deposit": {"min": 0.01, "max": null}, "withdraw": {"min": 10, "max": null}}, "active": false, "deposit": true, "withdraw": false, "fee": 0.09998001, "precision": null, "info": {"browserUrl": "https://basescan.org/tx/", "chain": "BASE", "chainId": "5831", "coinChainId": "5831", "depositConfirm": "50", "extraWithDrawFee": "0", "minDepositAmount": "0.01", "minWithdrawAmount": "10", "needTag": "false", "rechargeable": "true", "shortName": "BASE", "withdrawConfirm": "120", "withdrawFee": "0.09998001", "withdrawable": "false"}}, "Polygon": {"id": "Polygon", "network": "Polygon", "limits": {"deposit": {"min": 0.1, "max": null}, "withdraw": {"min": 10, "max": null}}, "active": false, "deposit": true, "withdraw": false, "fee": 0.64, "precision": null, "info": {"browserUrl": "https://polygonscan.com/tx/", "chain": "Polygon", "chainId": "4400", "coinChainId": "4400", "depositConfirm": "60", "extraWithDrawFee": "0", "minDepositAmount": "0.1", "minWithdrawAmount": "10", "needTag": "false", "rechargeable": "true", "shortName": "POL", "withdrawConfirm": "60", "withdrawFee": "0.64", "withdrawable": "false"}}}, "limits": {"deposit": {"min": 0.01, "max": null}, "withdraw": {"min": 10, "max": null}}}}