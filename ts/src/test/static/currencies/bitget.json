{"BTC": {"info": {"coinId": "1", "coin": "BTC", "transfer": "true", "chains": [{"chain": "BTC", "needTag": "false", "withdrawable": "true", "rechargeable": "true", "withdrawFee": "0.00004", "extraWithdrawFee": "0", "depositConfirm": "1", "withdrawConfirm": "1", "minDepositAmount": "0.00000912", "minWithdrawAmount": "0.00045632", "browserUrl": "https://www.blockchain.com/explorer/transactions/btc/", "contractAddress": null, "withdrawStep": "0", "withdrawMinScale": "8", "congestion": "normal"}, {"chain": "LIGHTNING", "needTag": "false", "withdrawable": "true", "rechargeable": "true", "withdrawFee": "0.00001", "extraWithdrawFee": "0", "depositConfirm": "1", "withdrawConfirm": "5", "minDepositAmount": "0.00000009", "minWithdrawAmount": "0.00001889", "browserUrl": "", "contractAddress": null, "withdrawStep": "0", "withdrawMinScale": "8", "congestion": "normal"}, {"chain": "BEP20", "needTag": "false", "withdrawable": "true", "rechargeable": "true", "withdrawFee": "0.00000654", "extraWithdrawFee": "0", "depositConfirm": "15", "withdrawConfirm": "15", "minDepositAmount": "0.00000084", "minWithdrawAmount": "0.00009126", "browserUrl": "https://bscscan.com/tx/", "contractAddress": "******************************************", "withdrawStep": "0", "withdrawMinScale": "8", "congestion": "normal"}], "areaCoin": "no"}, "id": "BTC", "numericId": null, "code": "BTC", "precision": 1e-08, "type": "crypto", "name": null, "active": true, "deposit": true, "withdraw": true, "fee": 6.54e-06, "fees": {}, "networks": {"BTC": {"info": {"chain": "BTC", "needTag": "false", "withdrawable": "true", "rechargeable": "true", "withdrawFee": "0.00004", "extraWithdrawFee": "0", "depositConfirm": "1", "withdrawConfirm": "1", "minDepositAmount": "0.00000912", "minWithdrawAmount": "0.00045632", "browserUrl": "https://www.blockchain.com/explorer/transactions/btc/", "contractAddress": null, "withdrawStep": "0", "withdrawMinScale": "8", "congestion": "normal"}, "id": "BTC", "network": "BTC", "limits": {"withdraw": {"min": 0.00045632, "max": null}, "deposit": {"min": 9.12e-06, "max": null}}, "active": true, "withdraw": true, "deposit": true, "fee": 4e-05, "precision": 1e-08}, "LIGHTNING": {"info": {"chain": "LIGHTNING", "needTag": "false", "withdrawable": "true", "rechargeable": "true", "withdrawFee": "0.00001", "extraWithdrawFee": "0", "depositConfirm": "1", "withdrawConfirm": "5", "minDepositAmount": "0.00000009", "minWithdrawAmount": "0.00001889", "browserUrl": "", "contractAddress": null, "withdrawStep": "0", "withdrawMinScale": "8", "congestion": "normal"}, "id": "LIGHTNING", "network": "LIGHTNING", "limits": {"withdraw": {"min": 1.889e-05, "max": null}, "deposit": {"min": 9e-08, "max": null}}, "active": true, "withdraw": true, "deposit": true, "fee": 1e-05, "precision": 1e-08}, "BSC": {"info": {"chain": "BEP20", "needTag": "false", "withdrawable": "true", "rechargeable": "true", "withdrawFee": "0.00000654", "extraWithdrawFee": "0", "depositConfirm": "15", "withdrawConfirm": "15", "minDepositAmount": "0.00000084", "minWithdrawAmount": "0.00009126", "browserUrl": "https://bscscan.com/tx/", "contractAddress": "******************************************", "withdrawStep": "0", "withdrawMinScale": "8", "congestion": "normal"}, "id": "BEP20", "network": "BSC", "limits": {"withdraw": {"min": 9.126e-05, "max": null}, "deposit": {"min": 8.4e-07, "max": null}}, "active": true, "withdraw": true, "deposit": true, "fee": 6.54e-06, "precision": 1e-08}}, "limits": {"amount": {"min": null, "max": null}, "withdraw": {"min": 1.889e-05, "max": null}, "deposit": {"min": 9e-08, "max": null}}, "created": null}, "USDT": {"info": {"coinId": "2", "coin": "USDT", "transfer": "true", "chains": [{"chain": "ERC20", "needTag": "false", "withdrawable": "true", "rechargeable": "true", "withdrawFee": "3", "extraWithdrawFee": "0", "depositConfirm": "12", "withdrawConfirm": "64", "minDepositAmount": "0.01", "minWithdrawAmount": "10", "browserUrl": "https://etherscan.io/tx/", "contractAddress": "******************************************", "withdrawStep": "0", "withdrawMinScale": "6", "congestion": "normal"}, {"chain": "TRC20", "needTag": "false", "withdrawable": "true", "rechargeable": "true", "withdrawFee": "1", "extraWithdrawFee": "0", "depositConfirm": "1", "withdrawConfirm": "1", "minDepositAmount": "0.01", "minWithdrawAmount": "10", "browserUrl": "https://tronscan.org/#/transaction/", "contractAddress": "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t", "withdrawStep": "0", "withdrawMinScale": "6", "congestion": "normal"}, {"chain": "ArbitrumOne", "needTag": "false", "withdrawable": "true", "rechargeable": "true", "withdrawFee": "0.2", "extraWithdrawFee": "0", "depositConfirm": "24", "withdrawConfirm": "24", "minDepositAmount": "0.01", "minWithdrawAmount": "10", "browserUrl": "https://arbiscan.io/tx/", "contractAddress": "******************************************", "withdrawStep": "0", "withdrawMinScale": "6", "congestion": "normal"}, {"chain": "SOL", "needTag": "false", "withdrawable": "true", "rechargeable": "true", "withdrawFee": "1", "extraWithdrawFee": "0", "depositConfirm": "5", "withdrawConfirm": "5", "minDepositAmount": "0.01", "minWithdrawAmount": "10", "browserUrl": "https://solscan.io/tx/", "contractAddress": "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB", "withdrawStep": "0", "withdrawMinScale": "6", "congestion": "normal"}, {"chain": "<PERSON><PERSON>", "needTag": "false", "withdrawable": "false", "rechargeable": "true", "withdrawFee": "1", "extraWithdrawFee": "0", "depositConfirm": "20", "withdrawConfirm": "20", "minDepositAmount": "0.01", "minWithdrawAmount": "10", "browserUrl": "https://scope.klaytn.com/tx/", "contractAddress": "******************************************", "withdrawStep": "0", "withdrawMinScale": "6", "congestion": "normal"}, {"chain": "Optimism", "needTag": "false", "withdrawable": "true", "rechargeable": "true", "withdrawFee": "0.15", "extraWithdrawFee": "0", "depositConfirm": "50", "withdrawConfirm": "50", "minDepositAmount": "0.01", "minWithdrawAmount": "10", "browserUrl": "https://optimistic.etherscan.io/tx/", "contractAddress": "******************************************", "withdrawStep": "0", "withdrawMinScale": "6", "congestion": "normal"}, {"chain": "BEP20", "needTag": "false", "withdrawable": "true", "rechargeable": "true", "withdrawFee": "0", "extraWithdrawFee": "0", "depositConfirm": "15", "withdrawConfirm": "15", "minDepositAmount": "0.01", "minWithdrawAmount": "10", "browserUrl": "https://bscscan.com/tx/", "contractAddress": "******************************************", "withdrawStep": "0", "withdrawMinScale": "8", "congestion": "normal"}, {"chain": "KAVAEVMToken", "needTag": "false", "withdrawable": "true", "rechargeable": "true", "withdrawFee": "0.2", "extraWithdrawFee": "0", "depositConfirm": "12", "withdrawConfirm": "12", "minDepositAmount": "0.01", "minWithdrawAmount": "10", "browserUrl": "https://explorer.kava.io/tx/", "contractAddress": "******************************************", "withdrawStep": "0", "withdrawMinScale": "6", "congestion": "normal"}, {"chain": "Polygon", "needTag": "false", "withdrawable": "true", "rechargeable": "true", "withdrawFee": "0.2", "extraWithdrawFee": "0", "depositConfirm": "60", "withdrawConfirm": "60", "minDepositAmount": "0.01", "minWithdrawAmount": "10", "browserUrl": "https://polygonscan.com/tx/", "contractAddress": "0xc2132d05d31c914a87c6611c10748aeb04b58e8f", "withdrawStep": "0", "withdrawMinScale": "6", "congestion": "normal"}, {"chain": "AVAXC-Chain", "needTag": "false", "withdrawable": "true", "rechargeable": "true", "withdrawFee": "0.11", "extraWithdrawFee": "0", "depositConfirm": "60", "withdrawConfirm": "60", "minDepositAmount": "0.01", "minWithdrawAmount": "10", "browserUrl": "https://snowtrace.io/tx/", "contractAddress": "******************************************", "withdrawStep": "0", "withdrawMinScale": "6", "congestion": "normal"}, {"chain": "PolkadotAssetHub", "needTag": "false", "withdrawable": "false", "rechargeable": "true", "withdrawFee": "1", "extraWithdrawFee": "0", "depositConfirm": "30", "withdrawConfirm": "30", "minDepositAmount": "0.01", "minWithdrawAmount": "10", "browserUrl": "https://assethub-polkadot.subscan.io/extrinsic/", "contractAddress": "1984", "withdrawStep": "0", "withdrawMinScale": "6", "congestion": "normal"}, {"chain": "Aptos", "needTag": "false", "withdrawable": "true", "rechargeable": "true", "withdrawFee": "0.03", "extraWithdrawFee": "0", "depositConfirm": "10", "withdrawConfirm": "10", "minDepositAmount": "0.01", "minWithdrawAmount": "9", "browserUrl": "https://aptoscan.com/transaction/", "contractAddress": "0x357b0b74bc833e95a115ad22604854d6b0fca151cecd94111770e5d6ffc9dc2b", "withdrawStep": "0", "withdrawMinScale": "6", "congestion": "normal"}, {"chain": "TON", "needTag": "false", "withdrawable": "true", "rechargeable": "true", "withdrawFee": "0.15", "extraWithdrawFee": "0", "depositConfirm": "10", "withdrawConfirm": "10", "minDepositAmount": "0.01", "minWithdrawAmount": "10", "browserUrl": "https://tonviewer.com/transaction/", "contractAddress": "EQCxE6mUtQJKFnGfaROTKOt1lZbDiiX1kCixRv7Nw2Id_sDs", "withdrawStep": "0", "withdrawMinScale": "6", "congestion": "normal"}], "areaCoin": "no"}, "id": "USDT", "numericId": null, "code": "USDT", "precision": 1e-06, "type": "crypto", "name": null, "active": true, "deposit": true, "withdraw": true, "fee": 0, "fees": {}, "networks": {"ERC20": {"info": {"chain": "ERC20", "needTag": "false", "withdrawable": "true", "rechargeable": "true", "withdrawFee": "3", "extraWithdrawFee": "0", "depositConfirm": "12", "withdrawConfirm": "64", "minDepositAmount": "0.01", "minWithdrawAmount": "10", "browserUrl": "https://etherscan.io/tx/", "contractAddress": "******************************************", "withdrawStep": "0", "withdrawMinScale": "6", "congestion": "normal"}, "id": "ERC20", "network": "ERC20", "limits": {"withdraw": {"min": 10, "max": null}, "deposit": {"min": 0.01, "max": null}}, "active": true, "withdraw": true, "deposit": true, "fee": 3, "precision": 1e-06}, "TRC20": {"info": {"chain": "TRC20", "needTag": "false", "withdrawable": "true", "rechargeable": "true", "withdrawFee": "1", "extraWithdrawFee": "0", "depositConfirm": "1", "withdrawConfirm": "1", "minDepositAmount": "0.01", "minWithdrawAmount": "10", "browserUrl": "https://tronscan.org/#/transaction/", "contractAddress": "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t", "withdrawStep": "0", "withdrawMinScale": "6", "congestion": "normal"}, "id": "TRC20", "network": "TRC20", "limits": {"withdraw": {"min": 10, "max": null}, "deposit": {"min": 0.01, "max": null}}, "active": true, "withdraw": true, "deposit": true, "fee": 1, "precision": 1e-06}, "ARBONE": {"info": {"chain": "ArbitrumOne", "needTag": "false", "withdrawable": "true", "rechargeable": "true", "withdrawFee": "0.2", "extraWithdrawFee": "0", "depositConfirm": "24", "withdrawConfirm": "24", "minDepositAmount": "0.01", "minWithdrawAmount": "10", "browserUrl": "https://arbiscan.io/tx/", "contractAddress": "******************************************", "withdrawStep": "0", "withdrawMinScale": "6", "congestion": "normal"}, "id": "ArbitrumOne", "network": "ARBONE", "limits": {"withdraw": {"min": 10, "max": null}, "deposit": {"min": 0.01, "max": null}}, "active": true, "withdraw": true, "deposit": true, "fee": 0.2, "precision": 1e-06}, "SOL": {"info": {"chain": "SOL", "needTag": "false", "withdrawable": "true", "rechargeable": "true", "withdrawFee": "1", "extraWithdrawFee": "0", "depositConfirm": "5", "withdrawConfirm": "5", "minDepositAmount": "0.01", "minWithdrawAmount": "10", "browserUrl": "https://solscan.io/tx/", "contractAddress": "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB", "withdrawStep": "0", "withdrawMinScale": "6", "congestion": "normal"}, "id": "SOL", "network": "SOL", "limits": {"withdraw": {"min": 10, "max": null}, "deposit": {"min": 0.01, "max": null}}, "active": true, "withdraw": true, "deposit": true, "fee": 1, "precision": 1e-06}, "KAIA": {"info": {"chain": "<PERSON><PERSON>", "needTag": "false", "withdrawable": "false", "rechargeable": "true", "withdrawFee": "1", "extraWithdrawFee": "0", "depositConfirm": "20", "withdrawConfirm": "20", "minDepositAmount": "0.01", "minWithdrawAmount": "10", "browserUrl": "https://scope.klaytn.com/tx/", "contractAddress": "******************************************", "withdrawStep": "0", "withdrawMinScale": "6", "congestion": "normal"}, "id": "<PERSON><PERSON>", "network": "KAIA", "limits": {"withdraw": {"min": 10, "max": null}, "deposit": {"min": 0.01, "max": null}}, "active": false, "withdraw": false, "deposit": true, "fee": 1, "precision": 1e-06}, "OPTIMISM": {"info": {"chain": "Optimism", "needTag": "false", "withdrawable": "true", "rechargeable": "true", "withdrawFee": "0.15", "extraWithdrawFee": "0", "depositConfirm": "50", "withdrawConfirm": "50", "minDepositAmount": "0.01", "minWithdrawAmount": "10", "browserUrl": "https://optimistic.etherscan.io/tx/", "contractAddress": "******************************************", "withdrawStep": "0", "withdrawMinScale": "6", "congestion": "normal"}, "id": "Optimism", "network": "OPTIMISM", "limits": {"withdraw": {"min": 10, "max": null}, "deposit": {"min": 0.01, "max": null}}, "active": true, "withdraw": true, "deposit": true, "fee": 0.15, "precision": 1e-06}, "BSC": {"info": {"chain": "BEP20", "needTag": "false", "withdrawable": "true", "rechargeable": "true", "withdrawFee": "0", "extraWithdrawFee": "0", "depositConfirm": "15", "withdrawConfirm": "15", "minDepositAmount": "0.01", "minWithdrawAmount": "10", "browserUrl": "https://bscscan.com/tx/", "contractAddress": "******************************************", "withdrawStep": "0", "withdrawMinScale": "8", "congestion": "normal"}, "id": "BEP20", "network": "BSC", "limits": {"withdraw": {"min": 10, "max": null}, "deposit": {"min": 0.01, "max": null}}, "active": true, "withdraw": true, "deposit": true, "fee": 0, "precision": 1e-08}, "KAVAEVMTOKEN": {"info": {"chain": "KAVAEVMToken", "needTag": "false", "withdrawable": "true", "rechargeable": "true", "withdrawFee": "0.2", "extraWithdrawFee": "0", "depositConfirm": "12", "withdrawConfirm": "12", "minDepositAmount": "0.01", "minWithdrawAmount": "10", "browserUrl": "https://explorer.kava.io/tx/", "contractAddress": "******************************************", "withdrawStep": "0", "withdrawMinScale": "6", "congestion": "normal"}, "id": "KAVAEVMToken", "network": "KAVAEVMTOKEN", "limits": {"withdraw": {"min": 10, "max": null}, "deposit": {"min": 0.01, "max": null}}, "active": true, "withdraw": true, "deposit": true, "fee": 0.2, "precision": 1e-06}, "MATIC": {"info": {"chain": "Polygon", "needTag": "false", "withdrawable": "true", "rechargeable": "true", "withdrawFee": "0.2", "extraWithdrawFee": "0", "depositConfirm": "60", "withdrawConfirm": "60", "minDepositAmount": "0.01", "minWithdrawAmount": "10", "browserUrl": "https://polygonscan.com/tx/", "contractAddress": "0xc2132d05d31c914a87c6611c10748aeb04b58e8f", "withdrawStep": "0", "withdrawMinScale": "6", "congestion": "normal"}, "id": "Polygon", "network": "MATIC", "limits": {"withdraw": {"min": 10, "max": null}, "deposit": {"min": 0.01, "max": null}}, "active": true, "withdraw": true, "deposit": true, "fee": 0.2, "precision": 1e-06}, "AVAXC-CHAIN": {"info": {"chain": "AVAXC-Chain", "needTag": "false", "withdrawable": "true", "rechargeable": "true", "withdrawFee": "0.11", "extraWithdrawFee": "0", "depositConfirm": "60", "withdrawConfirm": "60", "minDepositAmount": "0.01", "minWithdrawAmount": "10", "browserUrl": "https://snowtrace.io/tx/", "contractAddress": "******************************************", "withdrawStep": "0", "withdrawMinScale": "6", "congestion": "normal"}, "id": "AVAXC-Chain", "network": "AVAXC-CHAIN", "limits": {"withdraw": {"min": 10, "max": null}, "deposit": {"min": 0.01, "max": null}}, "active": true, "withdraw": true, "deposit": true, "fee": 0.11, "precision": 1e-06}, "POLKADOTASSETHUB": {"info": {"chain": "PolkadotAssetHub", "needTag": "false", "withdrawable": "false", "rechargeable": "true", "withdrawFee": "1", "extraWithdrawFee": "0", "depositConfirm": "30", "withdrawConfirm": "30", "minDepositAmount": "0.01", "minWithdrawAmount": "10", "browserUrl": "https://assethub-polkadot.subscan.io/extrinsic/", "contractAddress": "1984", "withdrawStep": "0", "withdrawMinScale": "6", "congestion": "normal"}, "id": "PolkadotAssetHub", "network": "POLKADOTASSETHUB", "limits": {"withdraw": {"min": 10, "max": null}, "deposit": {"min": 0.01, "max": null}}, "active": false, "withdraw": false, "deposit": true, "fee": 1, "precision": 1e-06}, "APT": {"info": {"chain": "Aptos", "needTag": "false", "withdrawable": "true", "rechargeable": "true", "withdrawFee": "0.03", "extraWithdrawFee": "0", "depositConfirm": "10", "withdrawConfirm": "10", "minDepositAmount": "0.01", "minWithdrawAmount": "9", "browserUrl": "https://aptoscan.com/transaction/", "contractAddress": "0x357b0b74bc833e95a115ad22604854d6b0fca151cecd94111770e5d6ffc9dc2b", "withdrawStep": "0", "withdrawMinScale": "6", "congestion": "normal"}, "id": "Aptos", "network": "APT", "limits": {"withdraw": {"min": 9, "max": null}, "deposit": {"min": 0.01, "max": null}}, "active": true, "withdraw": true, "deposit": true, "fee": 0.03, "precision": 1e-06}, "TON": {"info": {"chain": "TON", "needTag": "false", "withdrawable": "true", "rechargeable": "true", "withdrawFee": "0.15", "extraWithdrawFee": "0", "depositConfirm": "10", "withdrawConfirm": "10", "minDepositAmount": "0.01", "minWithdrawAmount": "10", "browserUrl": "https://tonviewer.com/transaction/", "contractAddress": "EQCxE6mUtQJKFnGfaROTKOt1lZbDiiX1kCixRv7Nw2Id_sDs", "withdrawStep": "0", "withdrawMinScale": "6", "congestion": "normal"}, "id": "TON", "network": "TON", "limits": {"withdraw": {"min": 10, "max": null}, "deposit": {"min": 0.01, "max": null}}, "active": true, "withdraw": true, "deposit": true, "fee": 0.15, "precision": 1e-06}}, "limits": {"amount": {"min": null, "max": null}, "withdraw": {"min": 9, "max": null}, "deposit": {"min": 0.01, "max": null}}, "created": null}, "EUR": {"info": {"coinId": "590", "coin": "EUR", "transfer": "false", "chains": [], "areaCoin": "no"}, "id": "EUR", "numericId": null, "code": "EUR", "precision": null, "type": "fiat", "name": null, "active": null, "deposit": null, "withdraw": null, "fee": null, "fees": {}, "networks": {}, "limits": {"amount": {"min": null, "max": null}, "withdraw": {"min": null, "max": null}, "deposit": {"min": null, "max": null}}, "created": null}, "LTC": {"info": {"coinId": "5", "coin": "LTC", "transfer": "true", "chains": [{"chain": "LTC", "needTag": "false", "withdrawable": "true", "rechargeable": "true", "withdrawFee": "0.001", "extraWithdrawFee": "0", "depositConfirm": "3", "withdrawConfirm": "6", "minDepositAmount": "0", "minWithdrawAmount": "0.10407993", "browserUrl": "https://blockchair.com/litecoin/transaction/", "contractAddress": null, "withdrawStep": "0", "withdrawMinScale": "8", "congestion": "normal"}], "areaCoin": "no"}, "id": "LTC", "numericId": null, "code": "LTC", "precision": 1e-08, "type": "crypto", "name": null, "active": true, "deposit": true, "withdraw": true, "fee": 0.001, "fees": {}, "networks": {"LTC": {"info": {"chain": "LTC", "needTag": "false", "withdrawable": "true", "rechargeable": "true", "withdrawFee": "0.001", "extraWithdrawFee": "0", "depositConfirm": "3", "withdrawConfirm": "6", "minDepositAmount": "0", "minWithdrawAmount": "0.10407993", "browserUrl": "https://blockchair.com/litecoin/transaction/", "contractAddress": null, "withdrawStep": "0", "withdrawMinScale": "8", "congestion": "normal"}, "id": "LTC", "network": "LTC", "limits": {"withdraw": {"min": 0.10407993, "max": null}, "deposit": {"min": 0, "max": null}}, "active": true, "withdraw": true, "deposit": true, "fee": 0.001, "precision": 1e-08}}, "limits": {"amount": {"min": null, "max": null}, "withdraw": {"min": 0.10407993, "max": null}, "deposit": {"min": 0, "max": null}}, "created": null}, "ETH": {"info": {"coinId": "3", "coin": "ETH", "transfer": "true", "chains": [{"chain": "ETH", "needTag": "false", "withdrawable": "true", "rechargeable": "true", "withdrawFee": "0.00050805", "extraWithdrawFee": "0", "depositConfirm": "12", "withdrawConfirm": "64", "minDepositAmount": "0.0000039", "minWithdrawAmount": "0.003908", "browserUrl": "https://etherscan.io/tx/", "contractAddress": null, "withdrawStep": "0", "withdrawMinScale": "8", "congestion": "normal"}, {"chain": "ArbitrumOne", "needTag": "false", "withdrawable": "true", "rechargeable": "true", "withdrawFee": "0.00004", "extraWithdrawFee": "0", "depositConfirm": "12", "withdrawConfirm": "96", "minDepositAmount": "0.0000039", "minWithdrawAmount": "0.003908", "browserUrl": "https://arbiscan.io/tx/", "contractAddress": null, "withdrawStep": "0", "withdrawMinScale": "8", "congestion": "normal"}, {"chain": "Optimism", "needTag": "false", "withdrawable": "true", "rechargeable": "true", "withdrawFee": "0.00003", "extraWithdrawFee": "0", "depositConfirm": "50", "withdrawConfirm": "50", "minDepositAmount": "0.0000039", "minWithdrawAmount": "0.0035172", "browserUrl": "https://optimistic.etherscan.io/tx/", "contractAddress": null, "withdrawStep": "0", "withdrawMinScale": "8", "congestion": "normal"}, {"chain": "SCROLL", "needTag": "false", "withdrawable": "true", "rechargeable": "true", "withdrawFee": "0.0001", "extraWithdrawFee": "0", "depositConfirm": "120", "withdrawConfirm": "300", "minDepositAmount": "0.0000039", "minWithdrawAmount": "0.003908", "browserUrl": "https://scrollscan.com/tx/", "contractAddress": null, "withdrawStep": "0", "withdrawMinScale": "8", "congestion": "normal"}, {"chain": "Stark<PERSON>", "needTag": "false", "withdrawable": "true", "rechargeable": "true", "withdrawFee": "0.0001", "extraWithdrawFee": "0", "depositConfirm": "10", "withdrawConfirm": "10", "minDepositAmount": "0.0000039", "minWithdrawAmount": "0.003908", "browserUrl": "https://voyager.online/tx/", "contractAddress": "0x049d36570d4e46f48e99674bd3fcc84644ddd6b96f7c741b1562b82f9e004dc7", "withdrawStep": "0", "withdrawMinScale": "8", "congestion": "normal"}, {"chain": "zkSyncEra", "needTag": "false", "withdrawable": "true", "rechargeable": "true", "withdrawFee": "0.00004", "extraWithdrawFee": "0", "depositConfirm": "96", "withdrawConfirm": "96", "minDepositAmount": "0.0000039", "minWithdrawAmount": "0.003908", "browserUrl": "https://explorer.zksync.io/tx/", "contractAddress": null, "withdrawStep": "0", "withdrawMinScale": "8", "congestion": "normal"}, {"chain": "BEP20", "needTag": "false", "withdrawable": "true", "rechargeable": "true", "withdrawFee": "0.00027357", "extraWithdrawFee": "0", "depositConfirm": "15", "withdrawConfirm": "15", "minDepositAmount": "0.0000039", "minWithdrawAmount": "0.003908", "browserUrl": "https://bscscan.com/tx/", "contractAddress": "0x2170ed0880ac9a755fd29b2688956bd959f933f8", "withdrawStep": "0", "withdrawMinScale": "8", "congestion": "normal"}, {"chain": "Morph", "needTag": "false", "withdrawable": "true", "rechargeable": "true", "withdrawFee": "0.0003", "extraWithdrawFee": "0", "depositConfirm": "64", "withdrawConfirm": "96", "minDepositAmount": "0.0000039", "minWithdrawAmount": "0.003908", "browserUrl": "https://explorer.morphl2.io/tx/", "contractAddress": null, "withdrawStep": "0", "withdrawMinScale": "8", "congestion": "normal"}, {"chain": "LINEA", "needTag": "false", "withdrawable": "true", "rechargeable": "true", "withdrawFee": "0.00117241", "extraWithdrawFee": "0", "depositConfirm": "64", "withdrawConfirm": "96", "minDepositAmount": "0.0000039", "minWithdrawAmount": "0.003908", "browserUrl": "https://lineascan.build/tx/", "contractAddress": null, "withdrawStep": "0", "withdrawMinScale": "8", "congestion": "normal"}, {"chain": "BASE", "needTag": "false", "withdrawable": "true", "rechargeable": "true", "withdrawFee": "0.00004", "extraWithdrawFee": "0", "depositConfirm": "30", "withdrawConfirm": "120", "minDepositAmount": "0.0000039", "minWithdrawAmount": "0.003908", "browserUrl": "https://basescan.org/tx/", "contractAddress": null, "withdrawStep": "0", "withdrawMinScale": "8", "congestion": "normal"}], "areaCoin": "no"}, "id": "ETH", "numericId": null, "code": "ETH", "precision": 1e-08, "type": "crypto", "name": null, "active": true, "deposit": true, "withdraw": true, "fee": 3e-05, "fees": {}, "networks": {"ETH": {"info": {"chain": "ETH", "needTag": "false", "withdrawable": "true", "rechargeable": "true", "withdrawFee": "0.00050805", "extraWithdrawFee": "0", "depositConfirm": "12", "withdrawConfirm": "64", "minDepositAmount": "0.0000039", "minWithdrawAmount": "0.003908", "browserUrl": "https://etherscan.io/tx/", "contractAddress": null, "withdrawStep": "0", "withdrawMinScale": "8", "congestion": "normal"}, "id": "ETH", "network": "ETH", "limits": {"withdraw": {"min": 0.003908, "max": null}, "deposit": {"min": 3.9e-06, "max": null}}, "active": true, "withdraw": true, "deposit": true, "fee": 0.00050805, "precision": 1e-08}, "ARBONE": {"info": {"chain": "ArbitrumOne", "needTag": "false", "withdrawable": "true", "rechargeable": "true", "withdrawFee": "0.00004", "extraWithdrawFee": "0", "depositConfirm": "12", "withdrawConfirm": "96", "minDepositAmount": "0.0000039", "minWithdrawAmount": "0.003908", "browserUrl": "https://arbiscan.io/tx/", "contractAddress": null, "withdrawStep": "0", "withdrawMinScale": "8", "congestion": "normal"}, "id": "ArbitrumOne", "network": "ARBONE", "limits": {"withdraw": {"min": 0.003908, "max": null}, "deposit": {"min": 3.9e-06, "max": null}}, "active": true, "withdraw": true, "deposit": true, "fee": 4e-05, "precision": 1e-08}, "OPTIMISM": {"info": {"chain": "Optimism", "needTag": "false", "withdrawable": "true", "rechargeable": "true", "withdrawFee": "0.00003", "extraWithdrawFee": "0", "depositConfirm": "50", "withdrawConfirm": "50", "minDepositAmount": "0.0000039", "minWithdrawAmount": "0.0035172", "browserUrl": "https://optimistic.etherscan.io/tx/", "contractAddress": null, "withdrawStep": "0", "withdrawMinScale": "8", "congestion": "normal"}, "id": "Optimism", "network": "OPTIMISM", "limits": {"withdraw": {"min": 0.0035172, "max": null}, "deposit": {"min": 3.9e-06, "max": null}}, "active": true, "withdraw": true, "deposit": true, "fee": 3e-05, "precision": 1e-08}, "SCROLL": {"info": {"chain": "SCROLL", "needTag": "false", "withdrawable": "true", "rechargeable": "true", "withdrawFee": "0.0001", "extraWithdrawFee": "0", "depositConfirm": "120", "withdrawConfirm": "300", "minDepositAmount": "0.0000039", "minWithdrawAmount": "0.003908", "browserUrl": "https://scrollscan.com/tx/", "contractAddress": null, "withdrawStep": "0", "withdrawMinScale": "8", "congestion": "normal"}, "id": "SCROLL", "network": "SCROLL", "limits": {"withdraw": {"min": 0.003908, "max": null}, "deposit": {"min": 3.9e-06, "max": null}}, "active": true, "withdraw": true, "deposit": true, "fee": 0.0001, "precision": 1e-08}, "STARKNET": {"info": {"chain": "Stark<PERSON>", "needTag": "false", "withdrawable": "true", "rechargeable": "true", "withdrawFee": "0.0001", "extraWithdrawFee": "0", "depositConfirm": "10", "withdrawConfirm": "10", "minDepositAmount": "0.0000039", "minWithdrawAmount": "0.003908", "browserUrl": "https://voyager.online/tx/", "contractAddress": "0x049d36570d4e46f48e99674bd3fcc84644ddd6b96f7c741b1562b82f9e004dc7", "withdrawStep": "0", "withdrawMinScale": "8", "congestion": "normal"}, "id": "Stark<PERSON>", "network": "STARKNET", "limits": {"withdraw": {"min": 0.003908, "max": null}, "deposit": {"min": 3.9e-06, "max": null}}, "active": true, "withdraw": true, "deposit": true, "fee": 0.0001, "precision": 1e-08}, "ZKSYNC": {"info": {"chain": "zkSyncEra", "needTag": "false", "withdrawable": "true", "rechargeable": "true", "withdrawFee": "0.00004", "extraWithdrawFee": "0", "depositConfirm": "96", "withdrawConfirm": "96", "minDepositAmount": "0.0000039", "minWithdrawAmount": "0.003908", "browserUrl": "https://explorer.zksync.io/tx/", "contractAddress": null, "withdrawStep": "0", "withdrawMinScale": "8", "congestion": "normal"}, "id": "zkSyncEra", "network": "ZKSYNC", "limits": {"withdraw": {"min": 0.003908, "max": null}, "deposit": {"min": 3.9e-06, "max": null}}, "active": true, "withdraw": true, "deposit": true, "fee": 4e-05, "precision": 1e-08}, "BSC": {"info": {"chain": "BEP20", "needTag": "false", "withdrawable": "true", "rechargeable": "true", "withdrawFee": "0.00027357", "extraWithdrawFee": "0", "depositConfirm": "15", "withdrawConfirm": "15", "minDepositAmount": "0.0000039", "minWithdrawAmount": "0.003908", "browserUrl": "https://bscscan.com/tx/", "contractAddress": "0x2170ed0880ac9a755fd29b2688956bd959f933f8", "withdrawStep": "0", "withdrawMinScale": "8", "congestion": "normal"}, "id": "BEP20", "network": "BSC", "limits": {"withdraw": {"min": 0.003908, "max": null}, "deposit": {"min": 3.9e-06, "max": null}}, "active": true, "withdraw": true, "deposit": true, "fee": 0.00027357, "precision": 1e-08}, "MORPH": {"info": {"chain": "Morph", "needTag": "false", "withdrawable": "true", "rechargeable": "true", "withdrawFee": "0.0003", "extraWithdrawFee": "0", "depositConfirm": "64", "withdrawConfirm": "96", "minDepositAmount": "0.0000039", "minWithdrawAmount": "0.003908", "browserUrl": "https://explorer.morphl2.io/tx/", "contractAddress": null, "withdrawStep": "0", "withdrawMinScale": "8", "congestion": "normal"}, "id": "Morph", "network": "MORPH", "limits": {"withdraw": {"min": 0.003908, "max": null}, "deposit": {"min": 3.9e-06, "max": null}}, "active": true, "withdraw": true, "deposit": true, "fee": 0.0003, "precision": 1e-08}, "LINEA": {"info": {"chain": "LINEA", "needTag": "false", "withdrawable": "true", "rechargeable": "true", "withdrawFee": "0.00117241", "extraWithdrawFee": "0", "depositConfirm": "64", "withdrawConfirm": "96", "minDepositAmount": "0.0000039", "minWithdrawAmount": "0.003908", "browserUrl": "https://lineascan.build/tx/", "contractAddress": null, "withdrawStep": "0", "withdrawMinScale": "8", "congestion": "normal"}, "id": "LINEA", "network": "LINEA", "limits": {"withdraw": {"min": 0.003908, "max": null}, "deposit": {"min": 3.9e-06, "max": null}}, "active": true, "withdraw": true, "deposit": true, "fee": 0.00117241, "precision": 1e-08}, "BASE": {"info": {"chain": "BASE", "needTag": "false", "withdrawable": "true", "rechargeable": "true", "withdrawFee": "0.00004", "extraWithdrawFee": "0", "depositConfirm": "30", "withdrawConfirm": "120", "minDepositAmount": "0.0000039", "minWithdrawAmount": "0.003908", "browserUrl": "https://basescan.org/tx/", "contractAddress": null, "withdrawStep": "0", "withdrawMinScale": "8", "congestion": "normal"}, "id": "BASE", "network": "BASE", "limits": {"withdraw": {"min": 0.003908, "max": null}, "deposit": {"min": 3.9e-06, "max": null}}, "active": true, "withdraw": true, "deposit": true, "fee": 4e-05, "precision": 1e-08}}, "limits": {"amount": {"min": null, "max": null}, "withdraw": {"min": 0.0035172, "max": null}, "deposit": {"min": 3.9e-06, "max": null}}, "created": null}, "ADA": {"info": {"coinId": "125", "coin": "ADA", "transfer": "true", "chains": [{"chain": "Cardano", "needTag": "false", "withdrawable": "true", "rechargeable": "true", "withdrawFee": "0.8", "extraWithdrawFee": "0", "depositConfirm": "30", "withdrawConfirm": "30", "minDepositAmount": "1.312163", "minWithdrawAmount": "13.121637", "browserUrl": "https://cardanoscan.io/transaction/", "contractAddress": null, "withdrawStep": "0", "withdrawMinScale": "6", "congestion": "normal"}], "areaCoin": "no"}, "id": "ADA", "numericId": null, "code": "ADA", "precision": 1e-06, "type": "crypto", "name": null, "active": true, "deposit": true, "withdraw": true, "fee": 0.8, "fees": {}, "networks": {"ADA": {"info": {"chain": "Cardano", "needTag": "false", "withdrawable": "true", "rechargeable": "true", "withdrawFee": "0.8", "extraWithdrawFee": "0", "depositConfirm": "30", "withdrawConfirm": "30", "minDepositAmount": "1.312163", "minWithdrawAmount": "13.121637", "browserUrl": "https://cardanoscan.io/transaction/", "contractAddress": null, "withdrawStep": "0", "withdrawMinScale": "6", "congestion": "normal"}, "id": "Cardano", "network": "ADA", "limits": {"withdraw": {"min": 13.121637, "max": null}, "deposit": {"min": 1.312163, "max": null}}, "active": true, "withdraw": true, "deposit": true, "fee": 0.8, "precision": 1e-06}}, "limits": {"amount": {"min": null, "max": null}, "withdraw": {"min": 13.121637, "max": null}, "deposit": {"min": 1.312163, "max": null}}, "created": null}, "XRP": {"info": {"coinId": "56", "coin": "XRP", "transfer": "true", "chains": [{"chain": "XRP", "needTag": "true", "withdrawable": "true", "rechargeable": "true", "withdrawFee": "0.2", "extraWithdrawFee": "0", "depositConfirm": "10", "withdrawConfirm": "10", "minDepositAmount": "0.004273", "minWithdrawAmount": "13.569535", "browserUrl": "https://bithomp.com/explorer/", "contractAddress": null, "withdrawStep": "0", "withdrawMinScale": "6", "congestion": "normal"}], "areaCoin": "no"}, "id": "XRP", "numericId": null, "code": "XRP", "precision": 1e-06, "type": "crypto", "name": null, "active": true, "deposit": true, "withdraw": true, "fee": 0.2, "fees": {}, "networks": {"XRP": {"info": {"chain": "XRP", "needTag": "true", "withdrawable": "true", "rechargeable": "true", "withdrawFee": "0.2", "extraWithdrawFee": "0", "depositConfirm": "10", "withdrawConfirm": "10", "minDepositAmount": "0.004273", "minWithdrawAmount": "13.569535", "browserUrl": "https://bithomp.com/explorer/", "contractAddress": null, "withdrawStep": "0", "withdrawMinScale": "6", "congestion": "normal"}, "id": "XRP", "network": "XRP", "limits": {"withdraw": {"min": 13.569535, "max": null}, "deposit": {"min": 0.004273, "max": null}}, "active": true, "withdraw": true, "deposit": true, "fee": 0.2, "precision": 1e-06}}, "limits": {"amount": {"min": null, "max": null}, "withdraw": {"min": 13.569535, "max": null}, "deposit": {"min": 0.004273, "max": null}}, "created": null}, "USDC": {"info": {"coinId": "74", "coin": "USDC", "transfer": "true", "chains": [{"chain": "OPTIMISM", "needTag": "false", "withdrawable": "true", "rechargeable": "true", "withdrawFee": "0.1", "extraWithdrawFee": "0", "depositConfirm": "50", "withdrawConfirm": "50", "minDepositAmount": "0.010005", "minWithdrawAmount": "10.005002", "browserUrl": "https://optimistic.etherscan.io/tx/", "contractAddress": "******************************************", "withdrawStep": "0", "withdrawMinScale": "6", "congestion": "normal"}, {"chain": "POLYGON", "needTag": "false", "withdrawable": "true", "rechargeable": "true", "withdrawFee": "0.64", "extraWithdrawFee": "0", "depositConfirm": "60", "withdrawConfirm": "60", "minDepositAmount": "0.010005", "minWithdrawAmount": "10.005002", "browserUrl": "https://polygonscan.com/tx/", "contractAddress": "******************************************", "withdrawStep": "0", "withdrawMinScale": "6", "congestion": "normal"}, {"chain": "SOL", "needTag": "false", "withdrawable": "true", "rechargeable": "true", "withdrawFee": "1.000501", "extraWithdrawFee": "0", "depositConfirm": "5", "withdrawConfirm": "5", "minDepositAmount": "0.010005", "minWithdrawAmount": "10.005002", "browserUrl": "https://solscan.io/tx/", "contractAddress": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v", "withdrawStep": "0", "withdrawMinScale": "6", "congestion": "normal"}, {"chain": "Aptos", "needTag": "false", "withdrawable": "false", "rechargeable": "true", "withdrawFee": "0.03", "extraWithdrawFee": "0", "depositConfirm": "10", "withdrawConfirm": "10", "minDepositAmount": "0.010005", "minWithdrawAmount": "10.005002", "browserUrl": "https://aptoscan.com/transaction/", "contractAddress": "0xbae207659db88bea0cbead6da0ed00aac12edcdda169e591cd41c94180b46f3b", "withdrawStep": "0", "withdrawMinScale": "6", "congestion": "normal"}, {"chain": "SONIC", "needTag": "false", "withdrawable": "false", "rechargeable": "true", "withdrawFee": "0.123932", "extraWithdrawFee": "0", "depositConfirm": "1", "withdrawConfirm": "64", "minDepositAmount": "0.123931", "minWithdrawAmount": "123.931094", "browserUrl": "https://sonicscan.org/tx/", "contractAddress": "0x29219dd400f2bf60e5a23d13be72b486d4038894", "withdrawStep": "0", "withdrawMinScale": "6", "congestion": "normal"}, {"chain": "BEP20", "needTag": "false", "withdrawable": "true", "rechargeable": "true", "withdrawFee": "0", "extraWithdrawFee": "0", "depositConfirm": "15", "withdrawConfirm": "15", "minDepositAmount": "0.010005", "minWithdrawAmount": "10.0050025", "browserUrl": "https://bscscan.com/tx/", "contractAddress": "******************************************", "withdrawStep": "0", "withdrawMinScale": "8", "congestion": "normal"}, {"chain": "ERC20", "needTag": "false", "withdrawable": "true", "rechargeable": "true", "withdrawFee": "3.001501", "extraWithdrawFee": "0", "depositConfirm": "12", "withdrawConfirm": "64", "minDepositAmount": "0.010005", "minWithdrawAmount": "10.005002", "browserUrl": "https://etherscan.io/tx/", "contractAddress": "******************************************", "withdrawStep": "0", "withdrawMinScale": "6", "congestion": "normal"}, {"chain": "BASE", "needTag": "false", "withdrawable": "true", "rechargeable": "true", "withdrawFee": "0.100051", "extraWithdrawFee": "0", "depositConfirm": "50", "withdrawConfirm": "50", "minDepositAmount": "0.010005", "minWithdrawAmount": "10.005002", "browserUrl": "https://basescan.org/tx/", "contractAddress": "******************************************", "withdrawStep": "0", "withdrawMinScale": "6", "congestion": "normal"}, {"chain": "ArbitrumOne", "needTag": "false", "withdrawable": "true", "rechargeable": "true", "withdrawFee": "0.200101", "extraWithdrawFee": "0", "depositConfirm": "12", "withdrawConfirm": "12", "minDepositAmount": "0.010005", "minWithdrawAmount": "10.005002", "browserUrl": "https://arbiscan.io/tx/", "contractAddress": "******************************************", "withdrawStep": "0", "withdrawMinScale": "6", "congestion": "normal"}, {"chain": "PolkadotAssetHub", "needTag": "false", "withdrawable": "false", "rechargeable": "true", "withdrawFee": "1.000501", "extraWithdrawFee": "0", "depositConfirm": "30", "withdrawConfirm": "30", "minDepositAmount": "0.010005", "minWithdrawAmount": "10.005002", "browserUrl": "https://assethub-polkadot.subscan.io/extrinsic/", "contractAddress": "1337", "withdrawStep": "0", "withdrawMinScale": "6", "congestion": "normal"}, {"chain": "AVAXC-Chain", "needTag": "false", "withdrawable": "true", "rechargeable": "true", "withdrawFee": "1.000501", "extraWithdrawFee": "0", "depositConfirm": "60", "withdrawConfirm": "60", "minDepositAmount": "0.010005", "minWithdrawAmount": "10.005002", "browserUrl": "https://snowtrace.io/tx/", "contractAddress": "******************************************", "withdrawStep": "0", "withdrawMinScale": "6", "congestion": "normal"}, {"chain": "<PERSON>", "needTag": "false", "withdrawable": "false", "rechargeable": "true", "withdrawFee": "0", "extraWithdrawFee": "0", "depositConfirm": "16", "withdrawConfirm": "16", "minDepositAmount": "0.010005", "minWithdrawAmount": "10.005002", "browserUrl": "https://www.mintscan.io/noble/tx/", "contractAddress": null, "withdrawStep": "0", "withdrawMinScale": "6", "congestion": "normal"}], "areaCoin": "no"}, "id": "USDC", "numericId": null, "code": "USDC", "precision": 1e-06, "type": "crypto", "name": null, "active": true, "deposit": true, "withdraw": true, "fee": 0, "fees": {}, "networks": {"OPTIMISM": {"info": {"chain": "OPTIMISM", "needTag": "false", "withdrawable": "true", "rechargeable": "true", "withdrawFee": "0.1", "extraWithdrawFee": "0", "depositConfirm": "50", "withdrawConfirm": "50", "minDepositAmount": "0.010005", "minWithdrawAmount": "10.005002", "browserUrl": "https://optimistic.etherscan.io/tx/", "contractAddress": "******************************************", "withdrawStep": "0", "withdrawMinScale": "6", "congestion": "normal"}, "id": "OPTIMISM", "network": "OPTIMISM", "limits": {"withdraw": {"min": 10.005002, "max": null}, "deposit": {"min": 0.010005, "max": null}}, "active": true, "withdraw": true, "deposit": true, "fee": 0.1, "precision": 1e-06}, "POLYGON": {"info": {"chain": "POLYGON", "needTag": "false", "withdrawable": "true", "rechargeable": "true", "withdrawFee": "0.64", "extraWithdrawFee": "0", "depositConfirm": "60", "withdrawConfirm": "60", "minDepositAmount": "0.010005", "minWithdrawAmount": "10.005002", "browserUrl": "https://polygonscan.com/tx/", "contractAddress": "******************************************", "withdrawStep": "0", "withdrawMinScale": "6", "congestion": "normal"}, "id": "POLYGON", "network": "POLYGON", "limits": {"withdraw": {"min": 10.005002, "max": null}, "deposit": {"min": 0.010005, "max": null}}, "active": true, "withdraw": true, "deposit": true, "fee": 0.64, "precision": 1e-06}, "SOL": {"info": {"chain": "SOL", "needTag": "false", "withdrawable": "true", "rechargeable": "true", "withdrawFee": "1.000501", "extraWithdrawFee": "0", "depositConfirm": "5", "withdrawConfirm": "5", "minDepositAmount": "0.010005", "minWithdrawAmount": "10.005002", "browserUrl": "https://solscan.io/tx/", "contractAddress": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v", "withdrawStep": "0", "withdrawMinScale": "6", "congestion": "normal"}, "id": "SOL", "network": "SOL", "limits": {"withdraw": {"min": 10.005002, "max": null}, "deposit": {"min": 0.010005, "max": null}}, "active": true, "withdraw": true, "deposit": true, "fee": 1.000501, "precision": 1e-06}, "APT": {"info": {"chain": "Aptos", "needTag": "false", "withdrawable": "false", "rechargeable": "true", "withdrawFee": "0.03", "extraWithdrawFee": "0", "depositConfirm": "10", "withdrawConfirm": "10", "minDepositAmount": "0.010005", "minWithdrawAmount": "10.005002", "browserUrl": "https://aptoscan.com/transaction/", "contractAddress": "0xbae207659db88bea0cbead6da0ed00aac12edcdda169e591cd41c94180b46f3b", "withdrawStep": "0", "withdrawMinScale": "6", "congestion": "normal"}, "id": "Aptos", "network": "APT", "limits": {"withdraw": {"min": 10.005002, "max": null}, "deposit": {"min": 0.010005, "max": null}}, "active": false, "withdraw": false, "deposit": true, "fee": 0.03, "precision": 1e-06}, "SONIC": {"info": {"chain": "SONIC", "needTag": "false", "withdrawable": "false", "rechargeable": "true", "withdrawFee": "0.123932", "extraWithdrawFee": "0", "depositConfirm": "1", "withdrawConfirm": "64", "minDepositAmount": "0.123931", "minWithdrawAmount": "123.931094", "browserUrl": "https://sonicscan.org/tx/", "contractAddress": "0x29219dd400f2bf60e5a23d13be72b486d4038894", "withdrawStep": "0", "withdrawMinScale": "6", "congestion": "normal"}, "id": "SONIC", "network": "SONIC", "limits": {"withdraw": {"min": 123.931094, "max": null}, "deposit": {"min": 0.123931, "max": null}}, "active": false, "withdraw": false, "deposit": true, "fee": 0.123932, "precision": 1e-06}, "BSC": {"info": {"chain": "BEP20", "needTag": "false", "withdrawable": "true", "rechargeable": "true", "withdrawFee": "0", "extraWithdrawFee": "0", "depositConfirm": "15", "withdrawConfirm": "15", "minDepositAmount": "0.010005", "minWithdrawAmount": "10.0050025", "browserUrl": "https://bscscan.com/tx/", "contractAddress": "******************************************", "withdrawStep": "0", "withdrawMinScale": "8", "congestion": "normal"}, "id": "BEP20", "network": "BSC", "limits": {"withdraw": {"min": 10.0050025, "max": null}, "deposit": {"min": 0.010005, "max": null}}, "active": true, "withdraw": true, "deposit": true, "fee": 0, "precision": 1e-08}, "ERC20": {"info": {"chain": "ERC20", "needTag": "false", "withdrawable": "true", "rechargeable": "true", "withdrawFee": "3.001501", "extraWithdrawFee": "0", "depositConfirm": "12", "withdrawConfirm": "64", "minDepositAmount": "0.010005", "minWithdrawAmount": "10.005002", "browserUrl": "https://etherscan.io/tx/", "contractAddress": "******************************************", "withdrawStep": "0", "withdrawMinScale": "6", "congestion": "normal"}, "id": "ERC20", "network": "ERC20", "limits": {"withdraw": {"min": 10.005002, "max": null}, "deposit": {"min": 0.010005, "max": null}}, "active": true, "withdraw": true, "deposit": true, "fee": 3.001501, "precision": 1e-06}, "BASE": {"info": {"chain": "BASE", "needTag": "false", "withdrawable": "true", "rechargeable": "true", "withdrawFee": "0.100051", "extraWithdrawFee": "0", "depositConfirm": "50", "withdrawConfirm": "50", "minDepositAmount": "0.010005", "minWithdrawAmount": "10.005002", "browserUrl": "https://basescan.org/tx/", "contractAddress": "******************************************", "withdrawStep": "0", "withdrawMinScale": "6", "congestion": "normal"}, "id": "BASE", "network": "BASE", "limits": {"withdraw": {"min": 10.005002, "max": null}, "deposit": {"min": 0.010005, "max": null}}, "active": true, "withdraw": true, "deposit": true, "fee": 0.100051, "precision": 1e-06}, "ARBONE": {"info": {"chain": "ArbitrumOne", "needTag": "false", "withdrawable": "true", "rechargeable": "true", "withdrawFee": "0.200101", "extraWithdrawFee": "0", "depositConfirm": "12", "withdrawConfirm": "12", "minDepositAmount": "0.010005", "minWithdrawAmount": "10.005002", "browserUrl": "https://arbiscan.io/tx/", "contractAddress": "******************************************", "withdrawStep": "0", "withdrawMinScale": "6", "congestion": "normal"}, "id": "ArbitrumOne", "network": "ARBONE", "limits": {"withdraw": {"min": 10.005002, "max": null}, "deposit": {"min": 0.010005, "max": null}}, "active": true, "withdraw": true, "deposit": true, "fee": 0.200101, "precision": 1e-06}, "POLKADOTASSETHUB": {"info": {"chain": "PolkadotAssetHub", "needTag": "false", "withdrawable": "false", "rechargeable": "true", "withdrawFee": "1.000501", "extraWithdrawFee": "0", "depositConfirm": "30", "withdrawConfirm": "30", "minDepositAmount": "0.010005", "minWithdrawAmount": "10.005002", "browserUrl": "https://assethub-polkadot.subscan.io/extrinsic/", "contractAddress": "1337", "withdrawStep": "0", "withdrawMinScale": "6", "congestion": "normal"}, "id": "PolkadotAssetHub", "network": "POLKADOTASSETHUB", "limits": {"withdraw": {"min": 10.005002, "max": null}, "deposit": {"min": 0.010005, "max": null}}, "active": false, "withdraw": false, "deposit": true, "fee": 1.000501, "precision": 1e-06}, "AVAXC-CHAIN": {"info": {"chain": "AVAXC-Chain", "needTag": "false", "withdrawable": "true", "rechargeable": "true", "withdrawFee": "1.000501", "extraWithdrawFee": "0", "depositConfirm": "60", "withdrawConfirm": "60", "minDepositAmount": "0.010005", "minWithdrawAmount": "10.005002", "browserUrl": "https://snowtrace.io/tx/", "contractAddress": "******************************************", "withdrawStep": "0", "withdrawMinScale": "6", "congestion": "normal"}, "id": "AVAXC-Chain", "network": "AVAXC-CHAIN", "limits": {"withdraw": {"min": 10.005002, "max": null}, "deposit": {"min": 0.010005, "max": null}}, "active": true, "withdraw": true, "deposit": true, "fee": 1.000501, "precision": 1e-06}, "NOBLE": {"info": {"chain": "<PERSON>", "needTag": "false", "withdrawable": "false", "rechargeable": "true", "withdrawFee": "0", "extraWithdrawFee": "0", "depositConfirm": "16", "withdrawConfirm": "16", "minDepositAmount": "0.010005", "minWithdrawAmount": "10.005002", "browserUrl": "https://www.mintscan.io/noble/tx/", "contractAddress": null, "withdrawStep": "0", "withdrawMinScale": "6", "congestion": "normal"}, "id": "<PERSON>", "network": "NOBLE", "limits": {"withdraw": {"min": 10.005002, "max": null}, "deposit": {"min": 0.010005, "max": null}}, "active": false, "withdraw": false, "deposit": true, "fee": 0, "precision": 1e-06}}, "limits": {"amount": {"min": null, "max": null}, "withdraw": {"min": 10.005002, "max": null}, "deposit": {"min": 0.010005, "max": null}}, "created": null}, "$AI": {"info": {"coinId": "1035", "coin": "$AI", "transfer": "false", "chains": [{"chain": "BEP20", "needTag": "false", "withdrawable": "true", "rechargeable": "true", "withdrawFee": "0.89235", "extraWithdrawFee": "0", "depositConfirm": "15", "withdrawConfirm": "15", "minDepositAmount": "0.1", "minWithdrawAmount": "200", "browserUrl": "https://bscscan.com/tx/"}], "areaCoin": "no"}, "id": "$AI", "code": "$AI", "networks": {"BEP20": {"info": {"chain": "BEP20", "needTag": "false", "withdrawable": "true", "rechargeable": "true", "withdrawFee": "0.89235", "extraWithdrawFee": "0", "depositConfirm": "15", "withdrawConfirm": "15", "minDepositAmount": "0.1", "minWithdrawAmount": "200", "browserUrl": "https://bscscan.com/tx/"}, "id": "BEP20", "network": "BEP20", "limits": {"withdraw": {"min": 200}, "deposit": {"min": 0.1}}, "active": true, "withdraw": true, "deposit": true, "fee": 0.89235}}, "active": true, "deposit": true, "withdraw": true, "fee": 0.89235, "limits": {"amount": {}, "withdraw": {"min": 200}, "deposit": {"min": 0.1}}}}