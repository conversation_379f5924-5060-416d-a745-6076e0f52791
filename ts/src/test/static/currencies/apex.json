{"BTC": null, "USDT": {"info": {"tokenId": "141", "token": "USDT", "displayName": "Tether USD Coin", "decimals": "18", "showStep": "0.01", "iconUrl": "https://static-pro.apex.exchange/chains/chain_tokens/Ethereum/Ethereum_USDT.svg", "l2WithdrawFee": "0", "enableCollateral": true, "enableCrossCollateral": false, "crossCollateralDiscountRate": null, "isGray": false}, "code": "USDT", "id": "USDT", "name": "Tether USD Coin", "active": true, "deposit": true, "withdraw": true, "fee": 2, "precision": 1e-18, "limits": {"amount": {"min": null, "max": null}, "withdraw": {"min": null, "max": null}, "deposit": {"min": null, "max": null}}, "networks": {"3": {"info": {"chain": "BNB Chain(BSC)", "chainId": "3", "chainType": "0", "l1ChainId": "56", "chainIconUrl": "https://static-pro.apex.exchange/chains/chain_tokens/BSC/BNB_BNB.svg", "contractAddress": "******************************************", "swapContractAddress": "******************************************", "stopDeposit": false, "feeLess": false, "gasLess": false, "gasToken": "BNB", "dynamicFee": false, "gasTokenDecimals": "18", "feeGasLimit": "300000", "blockTimeSeconds": "15", "rpcUrl": "https://bsc.pro.apex.exchange", "minSwapUsdtAmount": "", "maxSwapUsdtAmount": "", "webRpcUrl": "https://bsc.pro.apex.exchange", "webTxUrl": "https://bscscan.com/tx/", "backupRpcUrl": "https://binance.llamarpc.com", "txConfirm": "20", "withdrawGasFeeLess": false, "tokens": [{"decimals": "18", "iconUrl": "https://l2dex-image-static.dev.apexplus.exchange/icon/USDT.svg", "token": "USDT", "tokenAddress": "0x55d398326f99059fF775485246999027B3197955", "pullOff": false, "withdrawEnable": true, "depositDisable": false, "fastWithdrawEnable": true, "slippage": "", "isDefaultToken": false, "displayToken": "USDT", "needResetApproval": true, "minFee": "2", "maxFee": "20", "feeRate": "0.0001", "maxWithdraw": "", "minDeposit": "", "minWithdraw": "", "maxFastWithdrawAmount": "20000", "minFastWithdrawAmount": "1", "depositGasLess": false, "isGray": false, "tokenName": "", "domainVersion": ""}, {"decimals": "18", "iconUrl": "https://l2dex-image-static.dev.apexplus.exchange/icon/USDC.svg", "token": "USDC", "tokenAddress": "0x8ac76a51cc950d9822d68b83fe1ad97b32cd580d", "pullOff": false, "withdrawEnable": true, "depositDisable": false, "fastWithdrawEnable": false, "slippage": "", "isDefaultToken": false, "displayToken": "USDC", "needResetApproval": true, "minFee": "2", "maxFee": "20", "feeRate": "0.0001", "maxWithdraw": "", "minDeposit": "", "minWithdraw": "", "maxFastWithdrawAmount": "1", "minFastWithdrawAmount": "1", "depositGasLess": false, "isGray": false, "tokenName": "", "domainVersion": ""}, {"decimals": "18", "iconUrl": "https://static-pro.apex.exchange/icon/WETH.svg", "token": "WETH", "tokenAddress": "******************************************", "pullOff": false, "withdrawEnable": true, "depositDisable": false, "fastWithdrawEnable": false, "slippage": "", "isDefaultToken": false, "displayToken": "WETH", "needResetApproval": false, "minFee": "2", "maxFee": "20", "feeRate": "0.0001", "maxWithdraw": "", "minDeposit": "", "minWithdraw": "", "maxFastWithdrawAmount": "0.0001", "minFastWithdrawAmount": "0.0001", "depositGasLess": false, "isGray": false, "tokenName": "", "domainVersion": ""}], "isGray": false}, "id": "3", "network": "3", "active": true, "deposit": true, "withdraw": true, "fee": 2, "precision": 1e-18, "limits": {"withdraw": {"min": null, "max": null}, "deposit": {"min": null, "max": null}}}, "4": {"info": {"chain": "Ethereum", "chainId": "4", "chainType": "0", "l1ChainId": "1", "chainIconUrl": "https://static-pro.apex.exchange/chains/chain_logos/Ethereum.svg", "contractAddress": "******************************************", "swapContractAddress": "******************************************", "stopDeposit": false, "feeLess": false, "gasLess": false, "gasToken": "ETH", "dynamicFee": true, "gasTokenDecimals": "18", "feeGasLimit": "300000", "blockTimeSeconds": "15", "rpcUrl": "https://eth.pro.apex.exchange", "minSwapUsdtAmount": "", "maxSwapUsdtAmount": "", "webRpcUrl": "https://eth.pro.apex.exchange", "webTxUrl": "https://etherscan.io/tx/", "backupRpcUrl": "https://eth.llamarpc.com", "txConfirm": "64", "withdrawGasFeeLess": false, "tokens": [{"decimals": "6", "iconUrl": "https://static-pro.apex.exchange/chains/chain_tokens/Ethereum/Ethereum_USDT.svg", "token": "USDT", "tokenAddress": "******************************************", "pullOff": false, "withdrawEnable": true, "depositDisable": false, "fastWithdrawEnable": true, "slippage": "", "isDefaultToken": false, "displayToken": "USDT", "needResetApproval": true, "minFee": "2", "maxFee": "100", "feeRate": "0.0001", "maxWithdraw": "", "minDeposit": "", "minWithdraw": "", "maxFastWithdrawAmount": "50000", "minFastWithdrawAmount": "1", "depositGasLess": false, "isGray": false, "tokenName": "", "domainVersion": ""}, {"decimals": "6", "iconUrl": "https://static-pro.apex.exchange/chains/chain_tokens/Ethereum/Ethereum_USDC.svg", "token": "USDC", "tokenAddress": "******************************************", "pullOff": false, "withdrawEnable": true, "depositDisable": false, "fastWithdrawEnable": false, "slippage": "", "isDefaultToken": false, "displayToken": "USDC", "needResetApproval": true, "minFee": "2", "maxFee": "100", "feeRate": "0.0001", "maxWithdraw": "", "minDeposit": "", "minWithdraw": "", "maxFastWithdrawAmount": "1", "minFastWithdrawAmount": "1", "depositGasLess": false, "isGray": false, "tokenName": "", "domainVersion": ""}, {"decimals": "8", "iconUrl": "https://static-pro.apex.exchange/icon/WBTC.svg", "token": "WBTC", "tokenAddress": "******************************************", "pullOff": false, "withdrawEnable": true, "depositDisable": false, "fastWithdrawEnable": false, "slippage": "", "isDefaultToken": false, "displayToken": "WBTC", "needResetApproval": false, "minFee": "2", "maxFee": "20", "feeRate": "0.0001", "maxWithdraw": "", "minDeposit": "", "minWithdraw": "", "maxFastWithdrawAmount": "0.0001", "minFastWithdrawAmount": "0.0001", "depositGasLess": false, "isGray": false, "tokenName": "", "domainVersion": ""}, {"decimals": "18", "iconUrl": "https://static-pro.apex.exchange/chains/chain_tokens/Ethereum/Ethereum_ETH.svg", "token": "ETH", "tokenAddress": "******************************************", "pullOff": false, "withdrawEnable": true, "depositDisable": false, "fastWithdrawEnable": false, "slippage": "", "isDefaultToken": false, "displayToken": "ETH", "needResetApproval": false, "minFee": "2", "maxFee": "20", "feeRate": "0.0001", "maxWithdraw": "", "minDeposit": "", "minWithdraw": "", "maxFastWithdrawAmount": "0.0001", "minFastWithdrawAmount": "0.0001", "depositGasLess": false, "isGray": false, "tokenName": "", "domainVersion": ""}, {"decimals": "18", "iconUrl": "https://static-pro.apex.exchange/icon/WETH.svg", "token": "WETH", "tokenAddress": "******************************************", "pullOff": false, "withdrawEnable": true, "depositDisable": false, "fastWithdrawEnable": false, "slippage": "", "isDefaultToken": false, "displayToken": "WETH", "needResetApproval": false, "minFee": "2", "maxFee": "20", "feeRate": "0.0001", "maxWithdraw": "", "minDeposit": "", "minWithdraw": "", "maxFastWithdrawAmount": "0.0001", "minFastWithdrawAmount": "0.0001", "depositGasLess": false, "isGray": false, "tokenName": "", "domainVersion": ""}, {"decimals": "18", "iconUrl": "https://static-pro.apex.exchange/icon/USDE.svg", "token": "USDE", "tokenAddress": "******************************************", "pullOff": false, "withdrawEnable": true, "depositDisable": false, "fastWithdrawEnable": false, "slippage": "", "isDefaultToken": false, "displayToken": "USDE", "needResetApproval": false, "minFee": "2", "maxFee": "20", "feeRate": "0.0001", "maxWithdraw": "", "minDeposit": "", "minWithdraw": "", "maxFastWithdrawAmount": "0.0001", "minFastWithdrawAmount": "0.0001", "depositGasLess": false, "isGray": false, "tokenName": "", "domainVersion": ""}], "isGray": false}, "id": "4", "network": "4", "active": true, "deposit": true, "withdraw": true, "fee": 2, "precision": 1e-18, "limits": {"withdraw": {"min": null, "max": null}, "deposit": {"min": null, "max": null}}}, "9": {"info": {"chain": "Arbitrum One", "chainId": "9", "chainType": "0", "l1ChainId": "42161", "chainIconUrl": "https://static-pro.apex.exchange/chains/chain_logos/Arbitrum.svg", "contractAddress": "******************************************", "swapContractAddress": "******************************************", "stopDeposit": false, "feeLess": false, "gasLess": false, "gasToken": "ETH", "dynamicFee": true, "gasTokenDecimals": "18", "feeGasLimit": "300000", "blockTimeSeconds": "2", "rpcUrl": "https://arb.pro.apex.exchange", "minSwapUsdtAmount": "", "maxSwapUsdtAmount": "", "webRpcUrl": "https://arb.pro.apex.exchange", "webTxUrl": "https://arbiscan.io/tx/", "backupRpcUrl": "https://arb-mainnet.g.alchemy.com/v2/********************************", "txConfirm": "20", "withdrawGasFeeLess": false, "tokens": [{"decimals": "6", "iconUrl": "https://static-pro.apex.exchange/chains/chain_tokens/Arbitrum/Arbitrum_USDT.svg", "token": "USDT", "tokenAddress": "******************************************", "pullOff": false, "withdrawEnable": true, "depositDisable": false, "fastWithdrawEnable": true, "slippage": "", "isDefaultToken": false, "displayToken": "USDT", "needResetApproval": true, "minFee": "2", "maxFee": "40", "feeRate": "0.0001", "maxWithdraw": "", "minDeposit": "", "minWithdraw": "", "maxFastWithdrawAmount": "40000", "minFastWithdrawAmount": "1", "depositGasLess": true, "isGray": false, "tokenName": "", "domainVersion": ""}, {"decimals": "6", "iconUrl": "https://static-pro.apex.exchange/chains/chain_tokens/Arbitrum/Arbitrum_USDC.svg", "token": "USDC", "tokenAddress": "******************************************", "pullOff": false, "withdrawEnable": true, "depositDisable": false, "fastWithdrawEnable": false, "slippage": "", "isDefaultToken": false, "displayToken": "USDC", "needResetApproval": true, "minFee": "2", "maxFee": "20", "feeRate": "0.0001", "maxWithdraw": "", "minDeposit": "", "minWithdraw": "", "maxFastWithdrawAmount": "1", "minFastWithdrawAmount": "1", "depositGasLess": true, "isGray": false, "tokenName": "", "domainVersion": ""}, {"decimals": "18", "iconUrl": "https://static-pro.apex.exchange/chains/chain_tokens/Ethereum/Ethereum_ETH.svg", "token": "ETH", "tokenAddress": "******************************************", "pullOff": false, "withdrawEnable": true, "depositDisable": false, "fastWithdrawEnable": false, "slippage": "", "isDefaultToken": false, "displayToken": "ETH", "needResetApproval": false, "minFee": "2", "maxFee": "20", "feeRate": "0.0001", "maxWithdraw": "", "minDeposit": "", "minWithdraw": "", "maxFastWithdrawAmount": "0.0001", "minFastWithdrawAmount": "0.0001", "depositGasLess": false, "isGray": false, "tokenName": "", "domainVersion": ""}, {"decimals": "18", "iconUrl": "https://static-pro.apex.exchange/icon/WETH.svg", "token": "WETH", "tokenAddress": "******************************************", "pullOff": false, "withdrawEnable": true, "depositDisable": false, "fastWithdrawEnable": false, "slippage": "", "isDefaultToken": false, "displayToken": "WETH", "needResetApproval": false, "minFee": "2", "maxFee": "20", "feeRate": "0.0001", "maxWithdraw": "", "minDeposit": "", "minWithdraw": "", "maxFastWithdrawAmount": "0.0001", "minFastWithdrawAmount": "0.0001", "depositGasLess": false, "isGray": false, "tokenName": "", "domainVersion": ""}], "isGray": false}, "id": "9", "network": "9", "active": true, "deposit": true, "withdraw": true, "fee": 2, "precision": 1e-18, "limits": {"withdraw": {"min": null, "max": null}, "deposit": {"min": null, "max": null}}}, "11": {"info": {"chain": "Base Chain", "chainId": "11", "chainType": "0", "l1ChainId": "8453", "chainIconUrl": "https://static-pro.apex.exchange/icon/BASE.png", "contractAddress": "******************************************", "swapContractAddress": "******************************************", "stopDeposit": false, "feeLess": false, "gasLess": false, "gasToken": "ETH", "dynamicFee": false, "gasTokenDecimals": "18", "feeGasLimit": "300000", "blockTimeSeconds": "2", "rpcUrl": "https://base.pro.apex.exchange", "minSwapUsdtAmount": "", "maxSwapUsdtAmount": "", "webRpcUrl": "https://base.pro.apex.exchange", "webTxUrl": "https://basescan.org/tx/", "backupRpcUrl": "https://base-mainnet.g.alchemy.com/v2/********************************", "txConfirm": "30", "withdrawGasFeeLess": false, "tokens": [{"decimals": "6", "iconUrl": "https://static-pro.apex.exchange/icon/USDT.svg", "token": "USDT", "tokenAddress": "******************************************", "pullOff": false, "withdrawEnable": true, "depositDisable": false, "fastWithdrawEnable": false, "slippage": "", "isDefaultToken": false, "displayToken": "USDT", "needResetApproval": true, "minFee": "2", "maxFee": "20", "feeRate": "0.0001", "maxWithdraw": "", "minDeposit": "", "minWithdraw": "", "maxFastWithdrawAmount": "20000", "minFastWithdrawAmount": "1", "depositGasLess": false, "isGray": false, "tokenName": "", "domainVersion": ""}, {"decimals": "6", "iconUrl": "https://static-pro.apex.exchange/icon/USDC.svg", "token": "USDC", "tokenAddress": "0x833589fcd6edb6e08f4c7c32d4f71b54bda02913", "pullOff": false, "withdrawEnable": true, "depositDisable": false, "fastWithdrawEnable": false, "slippage": "", "isDefaultToken": false, "displayToken": "USDC", "needResetApproval": true, "minFee": "2", "maxFee": "20", "feeRate": "0.0001", "maxWithdraw": "", "minDeposit": "", "minWithdraw": "", "maxFastWithdrawAmount": "1", "minFastWithdrawAmount": "1", "depositGasLess": true, "isGray": false, "tokenName": "", "domainVersion": ""}, {"decimals": "18", "iconUrl": "https://static-pro.apex.exchange/chains/chain_tokens/Ethereum/Ethereum_ETH.svg", "token": "ETH", "tokenAddress": "******************************************", "pullOff": false, "withdrawEnable": true, "depositDisable": false, "fastWithdrawEnable": false, "slippage": "", "isDefaultToken": false, "displayToken": "ETH", "needResetApproval": false, "minFee": "2", "maxFee": "20", "feeRate": "0.0001", "maxWithdraw": "", "minDeposit": "", "minWithdraw": "", "maxFastWithdrawAmount": "0.0001", "minFastWithdrawAmount": "0.0001", "depositGasLess": false, "isGray": false, "tokenName": "", "domainVersion": ""}, {"decimals": "18", "iconUrl": "https://static-pro.apex.exchange/icon/WETH.svg", "token": "WETH", "tokenAddress": "******************************************", "pullOff": false, "withdrawEnable": true, "depositDisable": false, "fastWithdrawEnable": false, "slippage": "", "isDefaultToken": false, "displayToken": "WETH", "needResetApproval": false, "minFee": "2", "maxFee": "20", "feeRate": "0.0001", "maxWithdraw": "", "minDeposit": "", "minWithdraw": "", "maxFastWithdrawAmount": "0.0001", "minFastWithdrawAmount": "0.0001", "depositGasLess": false, "isGray": false, "tokenName": "", "domainVersion": ""}], "isGray": false}, "id": "11", "network": "11", "active": true, "deposit": true, "withdraw": true, "fee": 2, "precision": 1e-18, "limits": {"withdraw": {"min": null, "max": null}, "deposit": {"min": null, "max": null}}}, "12": {"info": {"chain": "Mantle Network", "chainId": "12", "chainType": "0", "l1ChainId": "5000", "chainIconUrl": "https://static-pro.apex.exchange/chains/chain_logos/Mantle.svg", "contractAddress": "******************************************", "swapContractAddress": "", "stopDeposit": false, "feeLess": false, "gasLess": false, "gasToken": "MNT", "dynamicFee": false, "gasTokenDecimals": "18", "feeGasLimit": "300000", "blockTimeSeconds": "2", "rpcUrl": "https://mantle.pro.apex.exchange", "minSwapUsdtAmount": "", "maxSwapUsdtAmount": "", "webRpcUrl": "https://mantle.pro.apex.exchange", "webTxUrl": "https://explorer.mantle.xyz/tx/", "backupRpcUrl": "https://mantle-mainnet.g.alchemy.com/v2/********************************", "txConfirm": "60", "withdrawGasFeeLess": false, "tokens": [{"decimals": "6", "iconUrl": "https://static-pro.apex.exchange/icon/USDT.svg", "token": "USDT", "tokenAddress": "******************************************", "pullOff": false, "withdrawEnable": true, "depositDisable": false, "fastWithdrawEnable": true, "slippage": "", "isDefaultToken": false, "displayToken": "USDT", "needResetApproval": true, "minFee": "2", "maxFee": "20", "feeRate": "0.0001", "maxWithdraw": "", "minDeposit": "", "minWithdraw": "", "maxFastWithdrawAmount": "5000", "minFastWithdrawAmount": "1", "depositGasLess": false, "isGray": false, "tokenName": "", "domainVersion": ""}, {"decimals": "6", "iconUrl": "https://static-pro.apex.exchange/icon/USDC.svg", "token": "USDC", "tokenAddress": "******************************************", "pullOff": false, "withdrawEnable": true, "depositDisable": false, "fastWithdrawEnable": false, "slippage": "", "isDefaultToken": false, "displayToken": "USDC", "needResetApproval": true, "minFee": "2", "maxFee": "20", "feeRate": "0.0001", "maxWithdraw": "", "minDeposit": "", "minWithdraw": "", "maxFastWithdrawAmount": "1", "minFastWithdrawAmount": "1", "depositGasLess": false, "isGray": false, "tokenName": "", "domainVersion": ""}, {"decimals": "18", "iconUrl": "https://static-pro.apex.exchange/icon/METH.svg", "token": "METH", "tokenAddress": "******************************************", "pullOff": false, "withdrawEnable": true, "depositDisable": false, "fastWithdrawEnable": false, "slippage": "", "isDefaultToken": false, "displayToken": "METH", "needResetApproval": false, "minFee": "2", "maxFee": "20", "feeRate": "0.0001", "maxWithdraw": "", "minDeposit": "", "minWithdraw": "", "maxFastWithdrawAmount": "0.0001", "minFastWithdrawAmount": "0.0001", "depositGasLess": false, "isGray": false, "tokenName": "", "domainVersion": ""}, {"decimals": "18", "iconUrl": "https://static-pro.apex.exchange/icon/CMETH.svg", "token": "CMETH", "tokenAddress": "******************************************", "pullOff": false, "withdrawEnable": true, "depositDisable": false, "fastWithdrawEnable": false, "slippage": "", "isDefaultToken": false, "displayToken": "CMETH", "needResetApproval": false, "minFee": "2", "maxFee": "20", "feeRate": "0.0001", "maxWithdraw": "", "minDeposit": "", "minWithdraw": "", "maxFastWithdrawAmount": "0.0001", "minFastWithdrawAmount": "0.0001", "depositGasLess": false, "isGray": false, "tokenName": "", "domainVersion": ""}, {"decimals": "8", "iconUrl": "https://static-pro.apex.exchange/icon/WBTC.svg", "token": "WBTC", "tokenAddress": "******************************************", "pullOff": false, "withdrawEnable": true, "depositDisable": false, "fastWithdrawEnable": false, "slippage": "", "isDefaultToken": false, "displayToken": "WBTC", "needResetApproval": false, "minFee": "2", "maxFee": "20", "feeRate": "0.0001", "maxWithdraw": "", "minDeposit": "", "minWithdraw": "", "maxFastWithdrawAmount": "0.0001", "minFastWithdrawAmount": "0.0001", "depositGasLess": false, "isGray": false, "tokenName": "", "domainVersion": ""}, {"decimals": "18", "iconUrl": "https://static-pro.apex.exchange/icon/WETH.svg", "token": "WETH", "tokenAddress": "******************************************", "pullOff": false, "withdrawEnable": true, "depositDisable": false, "fastWithdrawEnable": false, "slippage": "", "isDefaultToken": false, "displayToken": "WETH", "needResetApproval": false, "minFee": "2", "maxFee": "20", "feeRate": "0.0001", "maxWithdraw": "", "minDeposit": "", "minWithdraw": "", "maxFastWithdrawAmount": "0.0001", "minFastWithdrawAmount": "0.0001", "depositGasLess": false, "isGray": false, "tokenName": "", "domainVersion": ""}, {"decimals": "18", "iconUrl": "https://static-pro.apex.exchange/icon/USDE.svg", "token": "USDE", "tokenAddress": "******************************************", "pullOff": false, "withdrawEnable": true, "depositDisable": false, "fastWithdrawEnable": false, "slippage": "", "isDefaultToken": false, "displayToken": "USDE", "needResetApproval": false, "minFee": "2", "maxFee": "20", "feeRate": "0.0001", "maxWithdraw": "", "minDeposit": "", "minWithdraw": "", "maxFastWithdrawAmount": "0.0001", "minFastWithdrawAmount": "0.0001", "depositGasLess": false, "isGray": false, "tokenName": "", "domainVersion": ""}], "isGray": false}, "id": "12", "network": "12", "active": true, "deposit": true, "withdraw": true, "fee": 2, "precision": 1e-18, "limits": {"withdraw": {"min": null, "max": null}, "deposit": {"min": null, "max": null}}}}}, "LTC": null, "ETH": {"info": {"tokenId": "36", "token": "ETH", "displayName": "Ethereum", "decimals": "18", "showStep": "0.00000001", "iconUrl": "https://static-pro.apex.exchange/chains/chain_tokens/Ethereum/Ethereum_ETH.svg", "l2WithdrawFee": "0", "enableCollateral": false, "enableCrossCollateral": true, "crossCollateralDiscountRate": null, "prepMaxCrossCollateralSize": "200", "isGray": false}, "code": "ETH", "id": "ETH", "name": "Ethereum", "active": true, "deposit": true, "withdraw": true, "fee": 2, "precision": 1e-18, "limits": {"amount": {"min": null, "max": null}, "withdraw": {"min": null, "max": null}, "deposit": {"min": null, "max": null}}, "networks": {"4": {"info": {"chain": "Ethereum", "chainId": "4", "chainType": "0", "l1ChainId": "1", "chainIconUrl": "https://static-pro.apex.exchange/chains/chain_logos/Ethereum.svg", "contractAddress": "******************************************", "swapContractAddress": "******************************************", "stopDeposit": false, "feeLess": false, "gasLess": false, "gasToken": "ETH", "dynamicFee": true, "gasTokenDecimals": "18", "feeGasLimit": "300000", "blockTimeSeconds": "15", "rpcUrl": "https://eth.pro.apex.exchange", "minSwapUsdtAmount": "", "maxSwapUsdtAmount": "", "webRpcUrl": "https://eth.pro.apex.exchange", "webTxUrl": "https://etherscan.io/tx/", "backupRpcUrl": "https://eth.llamarpc.com", "txConfirm": "64", "withdrawGasFeeLess": false, "tokens": [{"decimals": "6", "iconUrl": "https://static-pro.apex.exchange/chains/chain_tokens/Ethereum/Ethereum_USDT.svg", "token": "USDT", "tokenAddress": "******************************************", "pullOff": false, "withdrawEnable": true, "depositDisable": false, "fastWithdrawEnable": true, "slippage": "", "isDefaultToken": false, "displayToken": "USDT", "needResetApproval": true, "minFee": "2", "maxFee": "100", "feeRate": "0.0001", "maxWithdraw": "", "minDeposit": "", "minWithdraw": "", "maxFastWithdrawAmount": "50000", "minFastWithdrawAmount": "1", "depositGasLess": false, "isGray": false, "tokenName": "", "domainVersion": ""}, {"decimals": "6", "iconUrl": "https://static-pro.apex.exchange/chains/chain_tokens/Ethereum/Ethereum_USDC.svg", "token": "USDC", "tokenAddress": "******************************************", "pullOff": false, "withdrawEnable": true, "depositDisable": false, "fastWithdrawEnable": false, "slippage": "", "isDefaultToken": false, "displayToken": "USDC", "needResetApproval": true, "minFee": "2", "maxFee": "100", "feeRate": "0.0001", "maxWithdraw": "", "minDeposit": "", "minWithdraw": "", "maxFastWithdrawAmount": "1", "minFastWithdrawAmount": "1", "depositGasLess": false, "isGray": false, "tokenName": "", "domainVersion": ""}, {"decimals": "8", "iconUrl": "https://static-pro.apex.exchange/icon/WBTC.svg", "token": "WBTC", "tokenAddress": "******************************************", "pullOff": false, "withdrawEnable": true, "depositDisable": false, "fastWithdrawEnable": false, "slippage": "", "isDefaultToken": false, "displayToken": "WBTC", "needResetApproval": false, "minFee": "2", "maxFee": "20", "feeRate": "0.0001", "maxWithdraw": "", "minDeposit": "", "minWithdraw": "", "maxFastWithdrawAmount": "0.0001", "minFastWithdrawAmount": "0.0001", "depositGasLess": false, "isGray": false, "tokenName": "", "domainVersion": ""}, {"decimals": "18", "iconUrl": "https://static-pro.apex.exchange/chains/chain_tokens/Ethereum/Ethereum_ETH.svg", "token": "ETH", "tokenAddress": "******************************************", "pullOff": false, "withdrawEnable": true, "depositDisable": false, "fastWithdrawEnable": false, "slippage": "", "isDefaultToken": false, "displayToken": "ETH", "needResetApproval": false, "minFee": "2", "maxFee": "20", "feeRate": "0.0001", "maxWithdraw": "", "minDeposit": "", "minWithdraw": "", "maxFastWithdrawAmount": "0.0001", "minFastWithdrawAmount": "0.0001", "depositGasLess": false, "isGray": false, "tokenName": "", "domainVersion": ""}, {"decimals": "18", "iconUrl": "https://static-pro.apex.exchange/icon/WETH.svg", "token": "WETH", "tokenAddress": "******************************************", "pullOff": false, "withdrawEnable": true, "depositDisable": false, "fastWithdrawEnable": false, "slippage": "", "isDefaultToken": false, "displayToken": "WETH", "needResetApproval": false, "minFee": "2", "maxFee": "20", "feeRate": "0.0001", "maxWithdraw": "", "minDeposit": "", "minWithdraw": "", "maxFastWithdrawAmount": "0.0001", "minFastWithdrawAmount": "0.0001", "depositGasLess": false, "isGray": false, "tokenName": "", "domainVersion": ""}, {"decimals": "18", "iconUrl": "https://static-pro.apex.exchange/icon/USDE.svg", "token": "USDE", "tokenAddress": "******************************************", "pullOff": false, "withdrawEnable": true, "depositDisable": false, "fastWithdrawEnable": false, "slippage": "", "isDefaultToken": false, "displayToken": "USDE", "needResetApproval": false, "minFee": "2", "maxFee": "20", "feeRate": "0.0001", "maxWithdraw": "", "minDeposit": "", "minWithdraw": "", "maxFastWithdrawAmount": "0.0001", "minFastWithdrawAmount": "0.0001", "depositGasLess": false, "isGray": false, "tokenName": "", "domainVersion": ""}], "isGray": false}, "id": "4", "network": "4", "active": true, "deposit": true, "withdraw": true, "fee": 2, "precision": 1e-18, "limits": {"withdraw": {"min": null, "max": null}, "deposit": {"min": null, "max": null}}}, "9": {"info": {"chain": "Arbitrum One", "chainId": "9", "chainType": "0", "l1ChainId": "42161", "chainIconUrl": "https://static-pro.apex.exchange/chains/chain_logos/Arbitrum.svg", "contractAddress": "******************************************", "swapContractAddress": "******************************************", "stopDeposit": false, "feeLess": false, "gasLess": false, "gasToken": "ETH", "dynamicFee": true, "gasTokenDecimals": "18", "feeGasLimit": "300000", "blockTimeSeconds": "2", "rpcUrl": "https://arb.pro.apex.exchange", "minSwapUsdtAmount": "", "maxSwapUsdtAmount": "", "webRpcUrl": "https://arb.pro.apex.exchange", "webTxUrl": "https://arbiscan.io/tx/", "backupRpcUrl": "https://arb-mainnet.g.alchemy.com/v2/********************************", "txConfirm": "20", "withdrawGasFeeLess": false, "tokens": [{"decimals": "6", "iconUrl": "https://static-pro.apex.exchange/chains/chain_tokens/Arbitrum/Arbitrum_USDT.svg", "token": "USDT", "tokenAddress": "******************************************", "pullOff": false, "withdrawEnable": true, "depositDisable": false, "fastWithdrawEnable": true, "slippage": "", "isDefaultToken": false, "displayToken": "USDT", "needResetApproval": true, "minFee": "2", "maxFee": "40", "feeRate": "0.0001", "maxWithdraw": "", "minDeposit": "", "minWithdraw": "", "maxFastWithdrawAmount": "40000", "minFastWithdrawAmount": "1", "depositGasLess": true, "isGray": false, "tokenName": "", "domainVersion": ""}, {"decimals": "6", "iconUrl": "https://static-pro.apex.exchange/chains/chain_tokens/Arbitrum/Arbitrum_USDC.svg", "token": "USDC", "tokenAddress": "******************************************", "pullOff": false, "withdrawEnable": true, "depositDisable": false, "fastWithdrawEnable": false, "slippage": "", "isDefaultToken": false, "displayToken": "USDC", "needResetApproval": true, "minFee": "2", "maxFee": "20", "feeRate": "0.0001", "maxWithdraw": "", "minDeposit": "", "minWithdraw": "", "maxFastWithdrawAmount": "1", "minFastWithdrawAmount": "1", "depositGasLess": true, "isGray": false, "tokenName": "", "domainVersion": ""}, {"decimals": "18", "iconUrl": "https://static-pro.apex.exchange/chains/chain_tokens/Ethereum/Ethereum_ETH.svg", "token": "ETH", "tokenAddress": "******************************************", "pullOff": false, "withdrawEnable": true, "depositDisable": false, "fastWithdrawEnable": false, "slippage": "", "isDefaultToken": false, "displayToken": "ETH", "needResetApproval": false, "minFee": "2", "maxFee": "20", "feeRate": "0.0001", "maxWithdraw": "", "minDeposit": "", "minWithdraw": "", "maxFastWithdrawAmount": "0.0001", "minFastWithdrawAmount": "0.0001", "depositGasLess": false, "isGray": false, "tokenName": "", "domainVersion": ""}, {"decimals": "18", "iconUrl": "https://static-pro.apex.exchange/icon/WETH.svg", "token": "WETH", "tokenAddress": "******************************************", "pullOff": false, "withdrawEnable": true, "depositDisable": false, "fastWithdrawEnable": false, "slippage": "", "isDefaultToken": false, "displayToken": "WETH", "needResetApproval": false, "minFee": "2", "maxFee": "20", "feeRate": "0.0001", "maxWithdraw": "", "minDeposit": "", "minWithdraw": "", "maxFastWithdrawAmount": "0.0001", "minFastWithdrawAmount": "0.0001", "depositGasLess": false, "isGray": false, "tokenName": "", "domainVersion": ""}], "isGray": false}, "id": "9", "network": "9", "active": true, "deposit": true, "withdraw": true, "fee": 2, "precision": 1e-18, "limits": {"withdraw": {"min": null, "max": null}, "deposit": {"min": null, "max": null}}}, "11": {"info": {"chain": "Base Chain", "chainId": "11", "chainType": "0", "l1ChainId": "8453", "chainIconUrl": "https://static-pro.apex.exchange/icon/BASE.png", "contractAddress": "******************************************", "swapContractAddress": "******************************************", "stopDeposit": false, "feeLess": false, "gasLess": false, "gasToken": "ETH", "dynamicFee": false, "gasTokenDecimals": "18", "feeGasLimit": "300000", "blockTimeSeconds": "2", "rpcUrl": "https://base.pro.apex.exchange", "minSwapUsdtAmount": "", "maxSwapUsdtAmount": "", "webRpcUrl": "https://base.pro.apex.exchange", "webTxUrl": "https://basescan.org/tx/", "backupRpcUrl": "https://base-mainnet.g.alchemy.com/v2/********************************", "txConfirm": "30", "withdrawGasFeeLess": false, "tokens": [{"decimals": "6", "iconUrl": "https://static-pro.apex.exchange/icon/USDT.svg", "token": "USDT", "tokenAddress": "******************************************", "pullOff": false, "withdrawEnable": true, "depositDisable": false, "fastWithdrawEnable": false, "slippage": "", "isDefaultToken": false, "displayToken": "USDT", "needResetApproval": true, "minFee": "2", "maxFee": "20", "feeRate": "0.0001", "maxWithdraw": "", "minDeposit": "", "minWithdraw": "", "maxFastWithdrawAmount": "20000", "minFastWithdrawAmount": "1", "depositGasLess": false, "isGray": false, "tokenName": "", "domainVersion": ""}, {"decimals": "6", "iconUrl": "https://static-pro.apex.exchange/icon/USDC.svg", "token": "USDC", "tokenAddress": "0x833589fcd6edb6e08f4c7c32d4f71b54bda02913", "pullOff": false, "withdrawEnable": true, "depositDisable": false, "fastWithdrawEnable": false, "slippage": "", "isDefaultToken": false, "displayToken": "USDC", "needResetApproval": true, "minFee": "2", "maxFee": "20", "feeRate": "0.0001", "maxWithdraw": "", "minDeposit": "", "minWithdraw": "", "maxFastWithdrawAmount": "1", "minFastWithdrawAmount": "1", "depositGasLess": true, "isGray": false, "tokenName": "", "domainVersion": ""}, {"decimals": "18", "iconUrl": "https://static-pro.apex.exchange/chains/chain_tokens/Ethereum/Ethereum_ETH.svg", "token": "ETH", "tokenAddress": "******************************************", "pullOff": false, "withdrawEnable": true, "depositDisable": false, "fastWithdrawEnable": false, "slippage": "", "isDefaultToken": false, "displayToken": "ETH", "needResetApproval": false, "minFee": "2", "maxFee": "20", "feeRate": "0.0001", "maxWithdraw": "", "minDeposit": "", "minWithdraw": "", "maxFastWithdrawAmount": "0.0001", "minFastWithdrawAmount": "0.0001", "depositGasLess": false, "isGray": false, "tokenName": "", "domainVersion": ""}, {"decimals": "18", "iconUrl": "https://static-pro.apex.exchange/icon/WETH.svg", "token": "WETH", "tokenAddress": "******************************************", "pullOff": false, "withdrawEnable": true, "depositDisable": false, "fastWithdrawEnable": false, "slippage": "", "isDefaultToken": false, "displayToken": "WETH", "needResetApproval": false, "minFee": "2", "maxFee": "20", "feeRate": "0.0001", "maxWithdraw": "", "minDeposit": "", "minWithdraw": "", "maxFastWithdrawAmount": "0.0001", "minFastWithdrawAmount": "0.0001", "depositGasLess": false, "isGray": false, "tokenName": "", "domainVersion": ""}], "isGray": false}, "id": "11", "network": "11", "active": true, "deposit": true, "withdraw": true, "fee": 2, "precision": 1e-18, "limits": {"withdraw": {"min": null, "max": null}, "deposit": {"min": null, "max": null}}}}}}