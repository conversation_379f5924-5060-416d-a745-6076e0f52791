{"BTC": {"id": "XBt", "code": "BTC", "info": {"asset": "XBT", "currency": "XBt", "majorCurrency": "XBT", "name": "Bitcoin", "currencyType": "Crypto", "scale": "8", "enabled": true, "isMarginCurrency": true, "minDepositAmount": "10000", "minWithdrawalAmount": "1000", "maxWithdrawalAmount": "100000000000000", "networks": [{"asset": "btc", "tokenAddress": "", "depositEnabled": true, "withdrawalEnabled": true, "withdrawalFee": "20000", "minFee": "31016", "maxFee": "10000000"}]}, "name": "Bitcoin", "active": true, "deposit": true, "withdraw": true, "precision": 1e-08, "limits": {"amount": {}, "withdraw": {"min": 1e-05, "max": 1000000}, "deposit": {"min": 0.0001}}, "networks": {"BTC": {"info": {"asset": "btc", "tokenAddress": "", "depositEnabled": true, "withdrawalEnabled": true, "withdrawalFee": "20000", "minFee": "31016", "maxFee": "10000000"}, "id": "btc", "network": "BTC", "active": true, "deposit": true, "withdraw": true, "fee": 0.0002, "limits": {"withdraw": {}, "deposit": {}}}}}, "USDT": {"id": "USDt", "code": "USDT", "info": {"asset": "USDT", "currency": "USDt", "majorCurrency": "USDT", "name": "USD Tether", "currencyType": "Crypto", "scale": "6", "enabled": true, "isMarginCurrency": true, "minDepositAmount": "1000000", "minWithdrawalAmount": "10000000", "maxWithdrawalAmount": "100000000000000", "networks": [{"asset": "eth", "tokenAddress": "******************************************", "depositEnabled": true, "withdrawalEnabled": true, "withdrawalFee": "10000000", "minFee": "10000000", "maxFee": "10000000"}, {"asset": "tron", "tokenAddress": "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t", "depositEnabled": true, "withdrawalEnabled": true, "withdrawalFee": "1000000", "minFee": "1000000", "maxFee": "1000000"}, {"asset": "sol", "tokenAddress": "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB", "depositEnabled": true, "withdrawalEnabled": true, "withdrawalFee": "1000000", "minFee": "1000000", "maxFee": "1000000"}]}, "name": "USD Tether", "active": true, "deposit": true, "withdraw": true, "precision": 1e-06, "limits": {"amount": {}, "withdraw": {"min": 10, "max": 100000000}, "deposit": {"min": 1}}, "networks": {"ERC20": {"info": {"asset": "eth", "tokenAddress": "******************************************", "depositEnabled": true, "withdrawalEnabled": true, "withdrawalFee": "10000000", "minFee": "10000000", "maxFee": "10000000"}, "id": "eth", "network": "ERC20", "active": true, "deposit": true, "withdraw": true, "fee": 10, "limits": {"withdraw": {}, "deposit": {}}}, "TRC20": {"info": {"asset": "tron", "tokenAddress": "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t", "depositEnabled": true, "withdrawalEnabled": true, "withdrawalFee": "1000000", "minFee": "1000000", "maxFee": "1000000"}, "id": "tron", "network": "TRC20", "active": true, "deposit": true, "withdraw": true, "fee": 1, "limits": {"withdraw": {}, "deposit": {}}}, "SOL": {"info": {"asset": "sol", "tokenAddress": "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB", "depositEnabled": true, "withdrawalEnabled": true, "withdrawalFee": "1000000", "minFee": "1000000", "maxFee": "1000000"}, "id": "sol", "network": "SOL", "active": true, "deposit": true, "withdraw": true, "fee": 1, "limits": {"withdraw": {}, "deposit": {}}}}}, "LTC": {"id": "LTc", "code": "LTC", "info": {"asset": "LTC", "currency": "LTc", "majorCurrency": "LTC", "name": "Litecoin", "currencyType": "Crypto", "scale": "8", "enabled": true, "isMarginCurrency": false, "minDepositAmount": "800000", "minWithdrawalAmount": "800000", "maxWithdrawalAmount": "80000000000000", "networks": [{"asset": "ltc", "tokenAddress": "", "depositEnabled": true, "withdrawalEnabled": true, "withdrawalFee": "800000", "minFee": "800000", "maxFee": "800000"}]}, "name": "Litecoin", "active": true, "deposit": true, "withdraw": true, "precision": 1e-08, "limits": {"amount": {}, "withdraw": {"min": 0.008, "max": 800000}, "deposit": {"min": 0.008}}, "networks": {"ltc": {"info": {"asset": "ltc", "tokenAddress": "", "depositEnabled": true, "withdrawalEnabled": true, "withdrawalFee": "800000", "minFee": "800000", "maxFee": "800000"}, "id": "ltc", "network": "ltc", "active": true, "deposit": true, "withdraw": true, "fee": 0.008, "limits": {"withdraw": {}, "deposit": {}}}}}, "ETH": {"id": "Gwei", "code": "ETH", "info": {"asset": "ETH", "currency": "Gwei", "majorCurrency": "ETH", "name": "Ethereum", "currencyType": "Crypto", "scale": "9", "enabled": true, "isMarginCurrency": true, "minDepositAmount": "10000000", "minWithdrawalAmount": "10000000", "maxWithdrawalAmount": "100000000000000000", "networks": [{"asset": "eth", "tokenAddress": "", "depositEnabled": true, "withdrawalEnabled": true, "withdrawalFee": "5000000", "minFee": "5000000", "maxFee": "5000000"}]}, "name": "Ethereum", "active": true, "deposit": true, "withdraw": true, "precision": 1e-09, "limits": {"amount": {}, "withdraw": {"min": 0.01, "max": 100000000}, "deposit": {"min": 0.01}}, "networks": {"ERC20": {"info": {"asset": "eth", "tokenAddress": "", "depositEnabled": true, "withdrawalEnabled": true, "withdrawalFee": "5000000", "minFee": "5000000", "maxFee": "5000000"}, "id": "eth", "network": "ERC20", "active": true, "deposit": true, "withdraw": true, "fee": 0.005, "limits": {"withdraw": {}, "deposit": {}}}}}, "ADA": {"id": "ADa", "code": "ADA", "info": {"asset": "ADA", "currency": "ADa", "majorCurrency": "ADA", "name": "Cardano", "currencyType": "Crypto", "scale": "8", "enabled": true, "isMarginCurrency": false, "minDepositAmount": "200000000", "minWithdrawalAmount": "200000000", "maxWithdrawalAmount": "20000000000000000", "networks": [{"asset": "ada", "tokenAddress": "", "depositEnabled": true, "withdrawalEnabled": true, "withdrawalFee": "200000000", "minFee": "200000000", "maxFee": "200000000"}]}, "name": "Cardano", "active": true, "deposit": true, "withdraw": true, "precision": 1e-08, "limits": {"amount": {}, "withdraw": {"min": 2, "max": 200000000}, "deposit": {"min": 2}}, "networks": {"ADA": {"info": {"asset": "ada", "tokenAddress": "", "depositEnabled": true, "withdrawalEnabled": true, "withdrawalFee": "200000000", "minFee": "200000000", "maxFee": "200000000"}, "id": "ada", "network": "ADA", "active": true, "deposit": true, "withdraw": true, "fee": 2, "limits": {"withdraw": {}, "deposit": {}}}}}, "USDC": {"id": "USDc", "code": "USDC", "info": {"asset": "USDC", "currency": "USDc", "majorCurrency": "USDC", "name": "USD Coin", "currencyType": "Crypto", "scale": "6", "enabled": true, "isMarginCurrency": false, "minDepositAmount": "10000000", "minWithdrawalAmount": "10000000", "maxWithdrawalAmount": "100000000000000", "networks": [{"asset": "eth", "tokenAddress": "******************************************", "depositEnabled": true, "withdrawalEnabled": true, "withdrawalFee": "25000000", "minFee": "25000000", "maxFee": "25000000"}]}, "name": "USD Coin", "active": true, "deposit": true, "withdraw": true, "precision": 1e-06, "limits": {"amount": {}, "withdraw": {"min": 10, "max": 100000000}, "deposit": {"min": 10}}, "networks": {"ERC20": {"info": {"asset": "eth", "tokenAddress": "******************************************", "depositEnabled": true, "withdrawalEnabled": true, "withdrawalFee": "25000000", "minFee": "25000000", "maxFee": "25000000"}, "id": "eth", "network": "ERC20", "active": true, "deposit": true, "withdraw": true, "fee": 25, "limits": {"withdraw": {}, "deposit": {}}}}}}