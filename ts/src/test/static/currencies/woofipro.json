{"USDC": {"info": {"token": "USDC", "token_hash": "0xd6aca1be9729c13d677335161321649cccae6a591554772516700f986f942eaa", "decimals": "6", "minimum_withdraw_amount": "0.000001", "chain_details": [{"chain_id": "42161", "contract_address": "******************************************", "decimals": "6", "withdrawal_fee": "1.00000000", "cross_chain_withdrawal_fee": "1.00000000", "display_name": "USDC"}, {"chain_id": "10", "contract_address": "******************************************", "decimals": "6", "withdrawal_fee": "1.00000000", "cross_chain_withdrawal_fee": "1.00000000", "display_name": "USDC"}, {"chain_id": "137", "contract_address": "******************************************", "decimals": "6", "withdrawal_fee": "1.00000000", "cross_chain_withdrawal_fee": "1.00000000", "display_name": "USDC"}, {"chain_id": "8453", "contract_address": "******************************************", "decimals": "6", "withdrawal_fee": "1.00000000", "cross_chain_withdrawal_fee": "1.00000000", "display_name": "USDC"}, {"chain_id": "5000", "contract_address": "******************************************", "decimals": "6", "withdrawal_fee": "2.00000000", "cross_chain_withdrawal_fee": "2.00000000", "display_name": "USDC.e"}, {"chain_id": "1", "contract_address": "******************************************", "decimals": "6", "withdrawal_fee": "10.00000000", "cross_chain_withdrawal_fee": "10.00000000", "display_name": "USDC"}, {"chain_id": "1329", "contract_address": "******************************************", "decimals": "6", "withdrawal_fee": "1.00000000", "cross_chain_withdrawal_fee": "1.00000000", "display_name": "USDC"}, {"chain_id": "43114", "contract_address": "******************************************", "decimals": "6", "withdrawal_fee": "1.00000000", "cross_chain_withdrawal_fee": "1.00000000", "display_name": "USDC"}, {"chain_id": "900900900", "contract_address": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v", "decimals": "6", "withdrawal_fee": "1.00000000", "cross_chain_withdrawal_fee": "1.00000000", "display_name": "USDC"}, {"chain_id": "2818", "contract_address": "******************************************", "decimals": "6", "withdrawal_fee": "1.00000000", "cross_chain_withdrawal_fee": "1.00000000", "display_name": "USDC.e"}, {"chain_id": "146", "contract_address": "******************************************", "decimals": "6", "withdrawal_fee": "1.00000000", "cross_chain_withdrawal_fee": "1.00000000", "display_name": "USDC.e"}, {"chain_id": "80094", "contract_address": "******************************************", "decimals": "6", "withdrawal_fee": "1.00000000", "cross_chain_withdrawal_fee": "1.00000000", "display_name": "USDC.e"}, {"chain_id": "1514", "contract_address": "******************************************", "decimals": "6", "withdrawal_fee": "1.00000000", "cross_chain_withdrawal_fee": "1.00000000", "display_name": "USDC"}, {"chain_id": "34443", "contract_address": "******************************************", "decimals": "6", "withdrawal_fee": "1.00000000", "cross_chain_withdrawal_fee": "1.00000000", "display_name": "USDC"}, {"chain_id": "98866", "contract_address": "******************************************", "decimals": "6", "withdrawal_fee": "1.00000000", "cross_chain_withdrawal_fee": "1.00000000", "display_name": "USDC.e"}, {"chain_id": "2741", "contract_address": "******************************************", "decimals": "6", "withdrawal_fee": "1.00000000", "cross_chain_withdrawal_fee": "1.00000000", "display_name": "USDC"}]}, "id": "USDC", "numericId": null, "code": "USDC", "precision": 1e-06, "type": null, "name": null, "active": null, "deposit": null, "withdraw": null, "fee": 1, "fees": {}, "networks": {"Arbitrum": {"id": "42161", "network": "Arbitrum", "limits": {"withdraw": {"min": null, "max": null}, "deposit": {"min": null, "max": null}}, "active": null, "deposit": null, "withdraw": null, "fee": 1, "precision": 1e-06, "info": [{"chain_id": "42161", "contract_address": "******************************************", "decimals": "6", "withdrawal_fee": "1.00000000", "cross_chain_withdrawal_fee": "1.00000000", "display_name": "USDC"}, {"name": "Arbitrum", "public_rpc_url": "https://arb1.arbitrum.io/rpc", "chain_id": "42161", "currency_symbol": "ETH", "currency_decimal": "18", "explorer_base_url": "https://arbiscan.io", "vault_address": "******************************************", "broker_ids": ["woofi_pro", "0xfin", "busywhale", "logx", "emdx_dex", "bitoro_network", "quick_perps", "empyreal", "ibx", "ascendex", "prime_protocol", "sharpe_ai", "coolwallet", "vooi", "fusionx_pro", "galar_fin", "elixir", "xade_finance", "<PERSON><PERSON>n", "what_exchange", "unibot", "sable", "alphanaut", "rage_trade", "ox_markets", "ask_jimmy", "flash_x", "pinde", "funl_ai", "kai", "crust_finance", "btse_dex", "orderoo", "boodex_com", "tcmp", "tealstreet", "ape_terminal", "vls", "dvx", "zotto", "book_x", "veeno_dex", "primex", "atlas", "demo", "eisen", "panda_terminal", "blazpay", "if_exchange", "one_bow", "filament", "bun_dex", "raydium", "linear_finance", "habit", "funnymoney", "boom", "saros", "cryptotraders", "cru", "orbidex", "pump_space", "coin98", "jojo", "navigator", "pegasus", "aark", "denx", "kyrrio", "mode", "yummy", "noot", "angry<PERSON><PERSON>", "bbx", "aiw3", "purps", "interlink", "involio", "hyperx", "ranger", "luma", "hyperai", "lolol", "bgsc"]}]}, "Optimism": {"id": "10", "network": "Optimism", "limits": {"withdraw": {"min": null, "max": null}, "deposit": {"min": null, "max": null}}, "active": null, "deposit": null, "withdraw": null, "fee": 1, "precision": 1e-06, "info": [{"chain_id": "10", "contract_address": "******************************************", "decimals": "6", "withdrawal_fee": "1.00000000", "cross_chain_withdrawal_fee": "1.00000000", "display_name": "USDC"}, {"name": "Optimism", "public_rpc_url": "https://optimism.publicnode.com", "chain_id": "10", "currency_symbol": "ETH", "currency_decimal": "18", "explorer_base_url": "https://optimistic.etherscan.io/", "vault_address": "******************************************", "broker_ids": ["woofi_pro", "0xfin", "busywhale", "logx", "emdx_dex", "bitoro_network", "quick_perps", "empyreal", "ibx", "ascendex", "prime_protocol", "sharpe_ai", "coolwallet", "vooi", "fusionx_pro", "galar_fin", "elixir", "xade_finance", "<PERSON><PERSON>n", "what_exchange", "unibot", "sable", "alphanaut", "rage_trade", "ox_markets", "ask_jimmy", "flash_x", "pinde", "funl_ai", "kai", "crust_finance", "btse_dex", "orderoo", "boodex_com", "tcmp", "tealstreet", "ape_terminal", "vls", "dvx", "zotto", "book_x", "veeno_dex", "primex", "atlas", "demo", "eisen", "panda_terminal", "blazpay", "if_exchange", "one_bow", "filament", "bun_dex", "raydium", "linear_finance", "habit", "funnymoney", "boom", "saros", "cryptotraders", "cru", "orbidex", "pump_space", "coin98", "jojo", "navigator", "pegasus", "aark", "denx", "kyrrio", "mode", "yummy", "noot", "angry<PERSON><PERSON>", "bbx", "aiw3", "purps", "interlink", "involio", "hyperx", "ranger", "luma", "hyperai", "lolol", "bgsc"]}]}, "Polygon": {"id": "137", "network": "Polygon", "limits": {"withdraw": {"min": null, "max": null}, "deposit": {"min": null, "max": null}}, "active": null, "deposit": null, "withdraw": null, "fee": 1, "precision": 1e-06, "info": [{"chain_id": "137", "contract_address": "******************************************", "decimals": "6", "withdrawal_fee": "1.00000000", "cross_chain_withdrawal_fee": "1.00000000", "display_name": "USDC"}, {"name": "Polygon", "public_rpc_url": "https://polygon-bor.publicnode.com", "chain_id": "137", "currency_symbol": "MATIC", "currency_decimal": "18", "explorer_base_url": "https://polygonscan.com/", "vault_address": "******************************************", "broker_ids": ["woofi_pro", "quick_perps"]}]}, "Base": {"id": "8453", "network": "Base", "limits": {"withdraw": {"min": null, "max": null}, "deposit": {"min": null, "max": null}}, "active": null, "deposit": null, "withdraw": null, "fee": 1, "precision": 1e-06, "info": [{"chain_id": "8453", "contract_address": "******************************************", "decimals": "6", "withdrawal_fee": "1.00000000", "cross_chain_withdrawal_fee": "1.00000000", "display_name": "USDC"}, {"name": "Base", "public_rpc_url": "https://base-rpc.publicnode.com", "chain_id": "8453", "currency_symbol": "ETH", "currency_decimal": "18", "explorer_base_url": "https://basescan.org/", "vault_address": "******************************************", "broker_ids": ["woofi_pro", "0xfin", "busywhale", "logx", "emdx_dex", "bitoro_network", "quick_perps", "empyreal", "ibx", "ascendex", "prime_protocol", "sharpe_ai", "coolwallet", "vooi", "fusionx_pro", "galar_fin", "elixir", "xade_finance", "<PERSON><PERSON>n", "what_exchange", "unibot", "sable", "alphanaut", "rage_trade", "ox_markets", "ask_jimmy", "flash_x", "pinde", "funl_ai", "kai", "crust_finance", "btse_dex", "orderoo", "boodex_com", "tcmp", "tealstreet", "ape_terminal", "vls", "dvx", "zotto", "book_x", "veeno_dex", "primex", "atlas", "demo", "eisen", "panda_terminal", "blazpay", "if_exchange", "one_bow", "filament", "bun_dex", "raydium", "linear_finance", "habit", "funnymoney", "boom", "saros", "cryptotraders", "cru", "orbidex", "pump_space", "coin98", "jojo", "navigator", "pegasus", "aark", "denx", "kyrrio", "mode", "yummy", "noot", "angry<PERSON><PERSON>", "bbx", "aiw3", "purps", "interlink", "involio", "hyperx", "ranger", "luma", "hyperai", "lolol", "bgsc"]}]}, "Mantle": {"id": "5000", "network": "Mantle", "limits": {"withdraw": {"min": null, "max": null}, "deposit": {"min": null, "max": null}}, "active": null, "deposit": null, "withdraw": null, "fee": 2, "precision": 1e-06, "info": [{"chain_id": "5000", "contract_address": "******************************************", "decimals": "6", "withdrawal_fee": "2.00000000", "cross_chain_withdrawal_fee": "2.00000000", "display_name": "USDC.e"}, {"name": "Mantle", "public_rpc_url": "https://rpc.mantle.xyz/", "chain_id": "5000", "currency_symbol": "MNT", "currency_decimal": "18", "explorer_base_url": "https://mantlescan.xyz/", "vault_address": "******************************************", "broker_ids": ["woofi_pro", "0xfin", "busywhale", "logx", "emdx_dex", "bitoro_network", "quick_perps", "empyreal", "ibx", "ascendex", "prime_protocol", "sharpe_ai", "coolwallet", "vooi", "fusionx_pro", "galar_fin", "elixir", "xade_finance", "<PERSON><PERSON>n", "what_exchange", "unibot", "sable", "alphanaut", "rage_trade", "ox_markets", "ask_jimmy", "flash_x", "pinde", "funl_ai", "kai", "crust_finance", "btse_dex", "orderoo", "boodex_com", "tcmp", "tealstreet", "ape_terminal", "vls", "dvx", "zotto", "book_x", "veeno_dex", "primex", "atlas", "demo", "eisen", "panda_terminal", "blazpay", "if_exchange", "one_bow", "filament", "bun_dex", "raydium", "linear_finance", "habit", "funnymoney", "boom", "saros", "cryptotraders", "cru", "orbidex", "pump_space", "coin98", "jojo", "navigator", "pegasus", "aark", "denx", "kyrrio", "mode", "yummy", "noot", "angry<PERSON><PERSON>", "bbx", "aiw3", "purps", "interlink", "involio", "hyperx", "ranger", "luma", "hyperai", "lolol", "bgsc"]}]}, "Ethereum": {"id": "1", "network": "Ethereum", "limits": {"withdraw": {"min": null, "max": null}, "deposit": {"min": null, "max": null}}, "active": null, "deposit": null, "withdraw": null, "fee": 10, "precision": 1e-06, "info": [{"chain_id": "1", "contract_address": "******************************************", "decimals": "6", "withdrawal_fee": "10.00000000", "cross_chain_withdrawal_fee": "10.00000000", "display_name": "USDC"}, {"name": "Ethereum", "public_rpc_url": "https://ethereum-rpc.publicnode.com", "chain_id": "1", "currency_symbol": "ETH", "currency_decimal": "18", "explorer_base_url": "https://etherscan.io/", "vault_address": "******************************************", "broker_ids": ["woofi_pro", "0xfin", "busywhale", "logx", "emdx_dex", "bitoro_network", "quick_perps", "empyreal", "ibx", "ascendex", "prime_protocol", "sharpe_ai", "coolwallet", "vooi", "fusionx_pro", "galar_fin", "elixir", "xade_finance", "<PERSON><PERSON>n", "what_exchange", "unibot", "sable", "alphanaut", "rage_trade", "ox_markets", "ask_jimmy", "flash_x", "pinde", "funl_ai", "kai", "crust_finance", "btse_dex", "orderoo", "boodex_com", "tcmp", "tealstreet", "ape_terminal", "vls", "dvx", "zotto", "book_x", "veeno_dex", "primex", "atlas", "demo", "eisen", "panda_terminal", "blazpay", "if_exchange", "one_bow", "filament", "bun_dex", "raydium", "linear_finance", "habit", "funnymoney", "boom", "saros", "cryptotraders", "cru", "orbidex", "pump_space", "coin98", "jojo", "navigator", "pegasus", "aark", "denx", "kyrrio", "mode", "yummy", "noot", "angry<PERSON><PERSON>", "bbx", "aiw3", "purps", "interlink", "involio", "hyperx", "ranger", "luma", "hyperai", "lolol", "bgsc"]}]}, "Sei Network": {"id": "1329", "network": "Sei Network", "limits": {"withdraw": {"min": null, "max": null}, "deposit": {"min": null, "max": null}}, "active": null, "deposit": null, "withdraw": null, "fee": 1, "precision": 1e-06, "info": [{"chain_id": "1329", "contract_address": "******************************************", "decimals": "6", "withdrawal_fee": "1.00000000", "cross_chain_withdrawal_fee": "1.00000000", "display_name": "USDC"}, {"name": "Sei Network", "public_rpc_url": "https://evm-rpc.sei-apis.com", "chain_id": "1329", "currency_symbol": "SEI", "currency_decimal": "18", "explorer_base_url": "https://seitrace.com/?chain=pacific-1", "vault_address": "******************************************", "broker_ids": ["woofi_pro", "0xfin", "busywhale", "logx", "emdx_dex", "bitoro_network", "quick_perps", "empyreal", "ibx", "ascendex", "prime_protocol", "sharpe_ai", "coolwallet", "vooi", "fusionx_pro", "galar_fin", "elixir", "xade_finance", "<PERSON><PERSON>n", "what_exchange", "unibot", "sable", "alphanaut", "rage_trade", "ox_markets", "ask_jimmy", "flash_x", "pinde", "funl_ai", "kai", "crust_finance", "btse_dex", "orderoo", "boodex_com", "tcmp", "tealstreet", "ape_terminal", "vls", "dvx", "zotto", "book_x", "veeno_dex", "primex", "atlas", "demo", "eisen", "panda_terminal", "blazpay", "if_exchange", "one_bow", "filament", "bun_dex", "raydium", "linear_finance", "habit", "funnymoney", "boom", "saros", "cryptotraders", "cru", "orbidex", "pump_space", "coin98", "jojo", "navigator", "pegasus", "aark", "denx", "kyrrio", "mode", "yummy", "noot", "angry<PERSON><PERSON>", "bbx", "aiw3", "purps", "interlink", "involio", "hyperx", "ranger", "luma", "hyperai", "lolol", "bgsc"]}]}, "Avalanche": {"id": "43114", "network": "Avalanche", "limits": {"withdraw": {"min": null, "max": null}, "deposit": {"min": null, "max": null}}, "active": null, "deposit": null, "withdraw": null, "fee": 1, "precision": 1e-06, "info": [{"chain_id": "43114", "contract_address": "******************************************", "decimals": "6", "withdrawal_fee": "1.00000000", "cross_chain_withdrawal_fee": "1.00000000", "display_name": "USDC"}, {"name": "Avalanche", "public_rpc_url": "https://avalanche-c-chain-rpc.publicnode.com", "chain_id": "43114", "currency_symbol": "AVAX", "currency_decimal": "18", "explorer_base_url": "https://snowtrace.io/", "vault_address": "******************************************", "broker_ids": ["woofi_pro", "0xfin", "busywhale", "logx", "emdx_dex", "bitoro_network", "quick_perps", "empyreal", "ibx", "ascendex", "prime_protocol", "sharpe_ai", "coolwallet", "vooi", "fusionx_pro", "galar_fin", "elixir", "xade_finance", "<PERSON><PERSON>n", "what_exchange", "unibot", "sable", "alphanaut", "rage_trade", "ox_markets", "ask_jimmy", "flash_x", "pinde", "funl_ai", "kai", "crust_finance", "btse_dex", "orderoo", "boodex_com", "tcmp", "tealstreet", "ape_terminal", "vls", "dvx", "zotto", "book_x", "veeno_dex", "primex", "atlas", "demo", "eisen", "panda_terminal", "blazpay", "if_exchange", "one_bow", "filament", "bun_dex", "raydium", "linear_finance", "habit", "funnymoney", "boom", "saros", "cryptotraders", "cru", "orbidex", "pump_space", "coin98", "jojo", "navigator", "pegasus", "aark", "denx", "kyrrio", "mode", "yummy", "noot", "angry<PERSON><PERSON>", "bbx", "aiw3", "purps", "interlink", "involio", "hyperx", "ranger", "luma", "hyperai", "lolol", "bgsc"]}]}, "Solana": {"id": "900900900", "network": "Solana", "limits": {"withdraw": {"min": null, "max": null}, "deposit": {"min": null, "max": null}}, "active": null, "deposit": null, "withdraw": null, "fee": 1, "precision": 1e-06, "info": [{"chain_id": "900900900", "contract_address": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v", "decimals": "6", "withdrawal_fee": "1.00000000", "cross_chain_withdrawal_fee": "1.00000000", "display_name": "USDC"}, {"name": "Solana", "public_rpc_url": "https://api.mainnet-beta.solana.co", "chain_id": "900900900", "currency_symbol": "SOL", "currency_decimal": "9", "explorer_base_url": "https://explorer.solana.com", "vault_address": "ErBmAD61mGFKvrFNaTJuxoPwqrS8GgtwtqJTJVjFWx9Q", "broker_ids": ["woofi_pro", "0xfin", "busywhale", "logx", "emdx_dex", "bitoro_network", "quick_perps", "empyreal", "ibx", "ascendex", "prime_protocol", "sharpe_ai", "coolwallet", "vooi", "fusionx_pro", "galar_fin", "elixir", "xade_finance", "<PERSON><PERSON>n", "what_exchange", "unibot", "sable", "alphanaut", "rage_trade", "ox_markets", "ask_jimmy", "flash_x", "pinde", "funl_ai", "kai", "crust_finance", "btse_dex", "orderoo", "boodex_com", "tcmp", "tealstreet", "ape_terminal", "vls", "dvx", "zotto", "book_x", "veeno_dex", "primex", "atlas", "demo", "eisen", "panda_terminal", "blazpay", "if_exchange", "one_bow", "filament", "bun_dex", "raydium", "linear_finance", "habit", "funnymoney", "boom", "saros", "cryptotraders", "cru", "orbidex", "pump_space", "coin98", "jojo", "navigator", "pegasus", "aark", "denx", "kyrrio", "mode", "yummy", "noot", "angry<PERSON><PERSON>", "bbx", "aiw3", "purps", "interlink", "involio", "hyperx", "ranger", "luma", "hyperai", "lolol", "bgsc"]}]}, "Morph": {"id": "2818", "network": "Morph", "limits": {"withdraw": {"min": null, "max": null}, "deposit": {"min": null, "max": null}}, "active": null, "deposit": null, "withdraw": null, "fee": 1, "precision": 1e-06, "info": [{"chain_id": "2818", "contract_address": "******************************************", "decimals": "6", "withdrawal_fee": "1.00000000", "cross_chain_withdrawal_fee": "1.00000000", "display_name": "USDC.e"}, {"name": "Morph", "public_rpc_url": "https://rpc-quicknode.morphl2.io", "chain_id": "2818", "currency_symbol": "ETH", "currency_decimal": "18", "explorer_base_url": "https://explorer.morphl2.io/", "vault_address": "******************************************", "broker_ids": ["woofi_pro", "0xfin", "busywhale", "logx", "emdx_dex", "bitoro_network", "quick_perps", "empyreal", "ibx", "ascendex", "prime_protocol", "sharpe_ai", "coolwallet", "vooi", "fusionx_pro", "galar_fin", "elixir", "xade_finance", "<PERSON><PERSON>n", "what_exchange", "unibot", "sable", "alphanaut", "rage_trade", "ox_markets", "ask_jimmy", "flash_x", "pinde", "funl_ai", "kai", "crust_finance", "btse_dex", "orderoo", "boodex_com", "tcmp", "tealstreet", "ape_terminal", "vls", "dvx", "zotto", "book_x", "veeno_dex", "primex", "atlas", "demo", "eisen", "panda_terminal", "blazpay", "if_exchange", "one_bow", "filament", "bun_dex", "raydium", "linear_finance", "habit", "funnymoney", "boom", "saros", "cryptotraders", "cru", "orbidex", "pump_space", "coin98", "jojo", "navigator", "pegasus", "aark", "denx", "kyrrio", "mode", "yummy", "noot", "angry<PERSON><PERSON>", "bbx", "aiw3", "purps", "interlink", "involio", "hyperx", "ranger", "luma", "hyperai", "lolol", "bgsc"]}]}, "Sonic": {"id": "146", "network": "Sonic", "limits": {"withdraw": {"min": null, "max": null}, "deposit": {"min": null, "max": null}}, "active": null, "deposit": null, "withdraw": null, "fee": 1, "precision": 1e-06, "info": [{"chain_id": "146", "contract_address": "******************************************", "decimals": "6", "withdrawal_fee": "1.00000000", "cross_chain_withdrawal_fee": "1.00000000", "display_name": "USDC.e"}, {"name": "Sonic", "public_rpc_url": "https://sonic.drpc.org/", "chain_id": "146", "currency_symbol": "S", "currency_decimal": "18", "explorer_base_url": "https://sonicscan.org/", "vault_address": "******************************************", "broker_ids": ["woofi_pro", "0xfin", "busywhale", "logx", "emdx_dex", "bitoro_network", "quick_perps", "empyreal", "ibx", "ascendex", "prime_protocol", "sharpe_ai", "coolwallet", "vooi", "fusionx_pro", "galar_fin", "elixir", "xade_finance", "<PERSON><PERSON>n", "what_exchange", "unibot", "sable", "alphanaut", "rage_trade", "ox_markets", "ask_jimmy", "flash_x", "pinde", "funl_ai", "kai", "crust_finance", "btse_dex", "orderoo", "boodex_com", "tcmp", "tealstreet", "ape_terminal", "vls", "dvx", "zotto", "book_x", "veeno_dex", "primex", "atlas", "demo", "eisen", "panda_terminal", "blazpay", "if_exchange", "one_bow", "filament", "bun_dex", "raydium", "linear_finance", "habit", "funnymoney", "boom", "saros", "cryptotraders", "cru", "orbidex", "pump_space", "coin98", "jojo", "navigator", "pegasus", "aark", "denx", "kyrrio", "mode", "yummy", "noot", "angry<PERSON><PERSON>", "bbx", "aiw3", "purps", "interlink", "involio", "hyperx", "ranger", "luma", "hyperai", "lolol", "bgsc"]}]}, "Berachain": {"id": "80094", "network": "<PERSON><PERSON><PERSON><PERSON>", "limits": {"withdraw": {"min": null, "max": null}, "deposit": {"min": null, "max": null}}, "active": null, "deposit": null, "withdraw": null, "fee": 1, "precision": 1e-06, "info": [{"chain_id": "80094", "contract_address": "******************************************", "decimals": "6", "withdrawal_fee": "1.00000000", "cross_chain_withdrawal_fee": "1.00000000", "display_name": "USDC.e"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "public_rpc_url": "https://rpc.berachain.com", "chain_id": "80094", "currency_symbol": "BERA", "currency_decimal": "18", "explorer_base_url": "https://berascan.com", "vault_address": "******************************************", "broker_ids": ["woofi_pro", "0xfin", "busywhale", "logx", "emdx_dex", "bitoro_network", "quick_perps", "empyreal", "ibx", "ascendex", "prime_protocol", "sharpe_ai", "coolwallet", "vooi", "fusionx_pro", "galar_fin", "elixir", "xade_finance", "<PERSON><PERSON>n", "what_exchange", "unibot", "sable", "alphanaut", "rage_trade", "ox_markets", "ask_jimmy", "flash_x", "pinde", "funl_ai", "kai", "crust_finance", "btse_dex", "orderoo", "boodex_com", "tcmp", "tealstreet", "ape_terminal", "vls", "dvx", "zotto", "book_x", "veeno_dex", "primex", "atlas", "demo", "eisen", "panda_terminal", "blazpay", "if_exchange", "one_bow", "filament", "bun_dex", "raydium", "linear_finance", "habit", "funnymoney", "boom", "saros", "cryptotraders", "cru", "orbidex", "pump_space", "coin98", "jojo", "navigator", "pegasus", "aark", "denx", "kyrrio", "mode", "yummy", "noot", "angry<PERSON><PERSON>", "bbx", "aiw3", "purps", "interlink", "involio", "hyperx", "ranger", "luma", "hyperai", "lolol", "bgsc"]}]}, "Story": {"id": "1514", "network": "Story", "limits": {"withdraw": {"min": null, "max": null}, "deposit": {"min": null, "max": null}}, "active": null, "deposit": null, "withdraw": null, "fee": 1, "precision": 1e-06, "info": [{"chain_id": "1514", "contract_address": "******************************************", "decimals": "6", "withdrawal_fee": "1.00000000", "cross_chain_withdrawal_fee": "1.00000000", "display_name": "USDC"}, {"name": "Story", "public_rpc_url": "https://mainnet.storyrpc.io", "chain_id": "1514", "currency_symbol": "IP", "currency_decimal": "18", "explorer_base_url": "https://www.storyscan.xyz/", "vault_address": "******************************************", "broker_ids": ["woofi_pro", "0xfin", "busywhale", "logx", "emdx_dex", "bitoro_network", "quick_perps", "empyreal", "ibx", "ascendex", "prime_protocol", "sharpe_ai", "coolwallet", "vooi", "fusionx_pro", "galar_fin", "elixir", "xade_finance", "<PERSON><PERSON>n", "what_exchange", "unibot", "sable", "alphanaut", "rage_trade", "ox_markets", "ask_jimmy", "flash_x", "pinde", "funl_ai", "kai", "crust_finance", "btse_dex", "orderoo", "boodex_com", "tcmp", "tealstreet", "ape_terminal", "vls", "dvx", "zotto", "book_x", "veeno_dex", "primex", "atlas", "demo", "eisen", "panda_terminal", "blazpay", "if_exchange", "one_bow", "filament", "bun_dex", "raydium", "linear_finance", "habit", "funnymoney", "boom", "saros", "cryptotraders", "cru", "orbidex", "pump_space", "coin98", "jojo", "navigator", "pegasus", "aark", "denx", "kyrrio", "mode", "yummy", "noot", "angry<PERSON><PERSON>", "bbx", "aiw3", "purps", "interlink", "involio", "hyperx", "ranger", "luma", "hyperai", "lolol", "bgsc"]}]}, "Mode": {"id": "34443", "network": "Mode", "limits": {"withdraw": {"min": null, "max": null}, "deposit": {"min": null, "max": null}}, "active": null, "deposit": null, "withdraw": null, "fee": 1, "precision": 1e-06, "info": [{"chain_id": "34443", "contract_address": "******************************************", "decimals": "6", "withdrawal_fee": "1.00000000", "cross_chain_withdrawal_fee": "1.00000000", "display_name": "USDC"}, {"name": "Mode", "public_rpc_url": "https://mainnet.mode.network", "chain_id": "34443", "currency_symbol": "ETH", "currency_decimal": "18", "explorer_base_url": "https://modescan.io/", "vault_address": "******************************************", "broker_ids": ["woofi_pro", "0xfin", "busywhale", "logx", "emdx_dex", "bitoro_network", "quick_perps", "empyreal", "ibx", "ascendex", "prime_protocol", "sharpe_ai", "coolwallet", "vooi", "fusionx_pro", "galar_fin", "elixir", "xade_finance", "<PERSON><PERSON>n", "what_exchange", "unibot", "sable", "alphanaut", "rage_trade", "ox_markets", "ask_jimmy", "flash_x", "pinde", "funl_ai", "kai", "crust_finance", "btse_dex", "orderoo", "boodex_com", "tcmp", "tealstreet", "ape_terminal", "vls", "dvx", "zotto", "book_x", "veeno_dex", "primex", "atlas", "demo", "eisen", "panda_terminal", "blazpay", "if_exchange", "one_bow", "filament", "bun_dex", "raydium", "linear_finance", "habit", "funnymoney", "boom", "saros", "cryptotraders", "cru", "orbidex", "pump_space", "coin98", "jojo", "navigator", "pegasus", "aark", "denx", "kyrrio", "mode", "yummy", "noot", "angry<PERSON><PERSON>", "bbx", "aiw3", "purps", "interlink", "involio", "hyperx", "ranger", "luma", "hyperai", "lolol", "bgsc"]}]}, "Plume Mainnet": {"id": "98866", "network": "Plume Mainnet", "limits": {"withdraw": {"min": null, "max": null}, "deposit": {"min": null, "max": null}}, "active": null, "deposit": null, "withdraw": null, "fee": 1, "precision": 1e-06, "info": [{"chain_id": "98866", "contract_address": "******************************************", "decimals": "6", "withdrawal_fee": "1.00000000", "cross_chain_withdrawal_fee": "1.00000000", "display_name": "USDC.e"}, {"name": "Plume Mainnet", "public_rpc_url": "https://phoenix-rpc.plumenetwork.xyz", "chain_id": "98866", "currency_symbol": "PLUME", "currency_decimal": "18", "explorer_base_url": "https://phoenix-explorer.plumenetwork.xyz/", "vault_address": "******************************************", "broker_ids": ["woofi_pro", "0xfin", "busywhale", "logx", "emdx_dex", "bitoro_network", "quick_perps", "empyreal", "ibx", "ascendex", "prime_protocol", "sharpe_ai", "coolwallet", "vooi", "fusionx_pro", "galar_fin", "elixir", "xade_finance", "<PERSON><PERSON>n", "what_exchange", "unibot", "sable", "alphanaut", "rage_trade", "ox_markets", "ask_jimmy", "flash_x", "pinde", "funl_ai", "kai", "crust_finance", "btse_dex", "orderoo", "boodex_com", "tcmp", "tealstreet", "ape_terminal", "vls", "dvx", "zotto", "book_x", "veeno_dex", "primex", "atlas", "demo", "eisen", "panda_terminal", "blazpay", "if_exchange", "one_bow", "filament", "bun_dex", "raydium", "linear_finance", "habit", "funnymoney", "boom", "saros", "cryptotraders", "cru", "orbidex", "pump_space", "coin98", "jojo", "navigator", "pegasus", "aark", "denx", "kyrrio", "mode", "yummy", "noot", "angry<PERSON><PERSON>", "bbx", "aiw3", "purps", "interlink", "involio", "hyperx", "ranger", "luma", "hyperai", "lolol", "bgsc"]}]}, "Abstract": {"id": "2741", "network": "Abstract", "limits": {"withdraw": {"min": null, "max": null}, "deposit": {"min": null, "max": null}}, "active": null, "deposit": null, "withdraw": null, "fee": 1, "precision": 1e-06, "info": [{"chain_id": "2741", "contract_address": "******************************************", "decimals": "6", "withdrawal_fee": "1.00000000", "cross_chain_withdrawal_fee": "1.00000000", "display_name": "USDC"}, {"name": "Abstract", "public_rpc_url": "https://api.mainnet.abs.xyz", "chain_id": "2741", "currency_symbol": "ETH", "currency_decimal": "18", "explorer_base_url": "https://abscan.org/", "vault_address": "******************************************", "broker_ids": ["woofi_pro", "0xfin", "busywhale", "logx", "emdx_dex", "bitoro_network", "quick_perps", "empyreal", "ibx", "ascendex", "prime_protocol", "sharpe_ai", "coolwallet", "vooi", "fusionx_pro", "galar_fin", "elixir", "xade_finance", "<PERSON><PERSON>n", "what_exchange", "unibot", "sable", "alphanaut", "rage_trade", "ox_markets", "ask_jimmy", "flash_x", "pinde", "funl_ai", "kai", "crust_finance", "btse_dex", "orderoo", "boodex_com", "tcmp", "tealstreet", "ape_terminal", "vls", "dvx", "zotto", "book_x", "veeno_dex", "primex", "atlas", "demo", "eisen", "panda_terminal", "blazpay", "if_exchange", "one_bow", "filament", "bun_dex", "raydium", "linear_finance", "habit", "funnymoney", "boom", "saros", "cryptotraders", "cru", "orbidex", "pump_space", "coin98", "jojo", "navigator", "pegasus", "aark", "denx", "kyrrio", "mode", "yummy", "noot", "angry<PERSON><PERSON>", "bbx", "aiw3", "purps", "interlink", "involio", "hyperx", "ranger", "luma", "hyperai", "lolol", "bgsc"]}]}}, "limits": {"deposit": {"min": null, "max": null}, "withdraw": {"min": 1e-06, "max": null}}}}