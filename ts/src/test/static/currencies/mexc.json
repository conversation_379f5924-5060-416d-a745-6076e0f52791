{"BTC": {"info": {"coin": "BTC", "name": "BTC-BSC", "networkList": [{"coin": "BTC", "depositDesc": null, "depositEnable": true, "minConfirm": "3", "name": "Bitcoin", "network": "Bitcoin(BTC)", "withdrawEnable": true, "withdrawFee": "0.00001", "withdrawIntegerMultiple": null, "withdrawMax": "100", "withdrawMin": "0.00035", "sameAddress": false, "contract": "", "withdrawTips": "", "depositTips": null, "netWork": "BTC"}, {"coin": "BTC", "depositDesc": null, "depositEnable": true, "minConfirm": "61", "name": "BTC-BSC", "network": "BNB Smart Chain(BEP20)", "withdrawEnable": true, "withdrawFee": "0.00000534", "withdrawIntegerMultiple": null, "withdrawMax": "100", "withdrawMin": "0.00007", "sameAddress": false, "contract": "******************************************", "withdrawTips": null, "depositTips": null, "netWork": "BSC"}]}, "id": "BTC", "numericId": null, "code": "BTC", "precision": null, "type": "crypto", "name": "BTC-BSC", "active": true, "deposit": true, "withdraw": true, "fee": 5.34e-06, "fees": {}, "networks": {"BTC": {"info": {"coin": "BTC", "depositDesc": null, "depositEnable": true, "minConfirm": "3", "name": "Bitcoin", "network": "Bitcoin(BTC)", "withdrawEnable": true, "withdrawFee": "0.00001", "withdrawIntegerMultiple": null, "withdrawMax": "100", "withdrawMin": "0.00035", "sameAddress": false, "contract": "", "withdrawTips": "", "depositTips": null, "netWork": "BTC"}, "id": "BTC", "network": "BTC", "active": true, "deposit": true, "withdraw": true, "fee": 1e-05, "precision": null, "limits": {"withdraw": {"min": "0.00035", "max": "100"}}}, "BEP20": {"info": {"coin": "BTC", "depositDesc": null, "depositEnable": true, "minConfirm": "61", "name": "BTC-BSC", "network": "BNB Smart Chain(BEP20)", "withdrawEnable": true, "withdrawFee": "0.00000534", "withdrawIntegerMultiple": null, "withdrawMax": "100", "withdrawMin": "0.00007", "sameAddress": false, "contract": "******************************************", "withdrawTips": null, "depositTips": null, "netWork": "BSC"}, "id": "BSC", "network": "BEP20", "active": true, "deposit": true, "withdraw": true, "fee": 5.34e-06, "precision": null, "limits": {"withdraw": {"min": "0.00007", "max": "100"}}}}, "limits": {"amount": {"min": null, "max": null}, "deposit": {"min": null, "max": null}, "withdraw": {"min": 7e-05, "max": 100}}}, "USDT": {"info": {"coin": "USDT", "name": "USDT", "networkList": [{"coin": "USDT", "depositDesc": null, "depositEnable": true, "minConfirm": "96", "name": "<PERSON><PERSON>", "network": "Ethereum(ERC20)", "withdrawEnable": true, "withdrawFee": "0.5", "withdrawIntegerMultiple": null, "withdrawMax": "26000000", "withdrawMin": "10", "sameAddress": false, "contract": "******************************************", "withdrawTips": null, "depositTips": null, "netWork": "ETH"}, {"coin": "USDT", "depositDesc": null, "depositEnable": true, "minConfirm": "20", "name": "<PERSON><PERSON>", "network": "Tron(TRC20)", "withdrawEnable": true, "withdrawFee": "1", "withdrawIntegerMultiple": null, "withdrawMax": "30000000", "withdrawMin": "3", "sameAddress": false, "contract": "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t", "withdrawTips": null, "depositTips": null, "netWork": "TRX"}, {"coin": "USDT", "depositDesc": null, "depositEnable": true, "minConfirm": "61", "name": "USDT-BSC", "network": "BNB Smart Chain(BEP20)", "withdrawEnable": true, "withdrawFee": "0", "withdrawIntegerMultiple": null, "withdrawMax": "20000000", "withdrawMin": "10", "sameAddress": false, "contract": "******************************************", "withdrawTips": null, "depositTips": "", "netWork": "BSC"}, {"coin": "USDT", "depositDesc": null, "depositEnable": true, "minConfirm": "100", "name": "USDT-SOL", "network": "Solana(SOL)", "withdrawEnable": true, "withdrawFee": "0.5", "withdrawIntegerMultiple": null, "withdrawMax": "9000000", "withdrawMin": "10", "sameAddress": false, "contract": "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB", "withdrawTips": "The withdrawal address must be the owner’s address or an activated secondary address. Otherwise, you will lose your funds. Due to network congestion of the Solana chain, it might take longer time to process withdrawal requests. Please confirm before proceeding.", "depositTips": "SOL network is experiencing a congestion so there might be a delay in your deposit.", "netWork": "SOL"}, {"coin": "USDT", "depositDesc": null, "depositEnable": true, "minConfirm": "450", "name": "USDT-MATIC", "network": "Polygon(MATIC)", "withdrawEnable": true, "withdrawFee": "0.02", "withdrawIntegerMultiple": null, "withdrawMax": "5000000", "withdrawMin": "5", "sameAddress": false, "contract": "0xc2132d05d31c914a87c6611c10748aeb04b58e8f", "withdrawTips": null, "depositTips": null, "netWork": "MATIC"}, {"coin": "USDT", "depositDesc": null, "depositEnable": true, "minConfirm": "35", "name": "<PERSON><PERSON>", "network": "OKT", "withdrawEnable": false, "withdrawFee": "0", "withdrawIntegerMultiple": null, "withdrawMax": "1000000", "withdrawMin": "10", "sameAddress": false, "contract": "0x382bb369d343125bfb2117af9c149795c6c65c50", "withdrawTips": null, "depositTips": null, "netWork": "OKT"}, {"coin": "USDT", "depositDesc": null, "depositEnable": true, "minConfirm": "100", "name": "<PERSON><PERSON>", "network": "Arbitrum One(ARB)", "withdrawEnable": true, "withdrawFee": "0.11", "withdrawIntegerMultiple": null, "withdrawMax": "5000000", "withdrawMin": "1", "sameAddress": false, "contract": "0xfd086bc7cd5c481dcc9c85ebe478a1c0b69fcbb9", "withdrawTips": null, "depositTips": null, "netWork": "ARB"}, {"coin": "USDT", "depositDesc": null, "depositEnable": true, "minConfirm": "200", "name": "<PERSON><PERSON>", "network": "Optimism(OP)", "withdrawEnable": true, "withdrawFee": "0.015", "withdrawIntegerMultiple": null, "withdrawMax": "2000000", "withdrawMin": "3", "sameAddress": false, "contract": "0x94b008aA00579c1307B0EF2c499aD98a8ce58e58", "withdrawTips": null, "depositTips": null, "netWork": "OP"}, {"coin": "USDT", "depositDesc": null, "depositEnable": true, "minConfirm": "35", "name": "<PERSON><PERSON>", "network": "Avalanche C Chain(AVAX CCHAIN)", "withdrawEnable": true, "withdrawFee": "0.12", "withdrawIntegerMultiple": null, "withdrawMax": "2000000", "withdrawMin": "5", "sameAddress": false, "contract": "******************************************", "withdrawTips": null, "depositTips": null, "netWork": "AVAX_CCHAIN"}, {"coin": "USDT", "depositDesc": null, "depositEnable": true, "minConfirm": "100", "name": "USDT", "network": "NEAR Protocol(NEAR)", "withdrawEnable": true, "withdrawFee": "0.2", "withdrawIntegerMultiple": null, "withdrawMax": "200000", "withdrawMin": "2", "sameAddress": false, "contract": "usdt.tether-token.near", "withdrawTips": null, "depositTips": null, "netWork": "NEAR"}, {"coin": "USDT", "depositDesc": null, "depositEnable": true, "minConfirm": "10", "name": "USDT", "network": "<PERSON><PERSON><PERSON>(TON)", "withdrawEnable": true, "withdrawFee": "0", "withdrawIntegerMultiple": null, "withdrawMax": "5000000", "withdrawMin": "10", "sameAddress": true, "contract": "EQCxE6mUtQJKFnGfaROTKOt1lZbDiiX1kCixRv7Nw2Id_sDs", "withdrawTips": null, "depositTips": "", "netWork": "TONCOIN"}, {"coin": "USDT", "depositDesc": null, "depositEnable": true, "minConfirm": "24", "name": "USDT", "network": "CELO", "withdrawEnable": true, "withdrawFee": "0.49", "withdrawIntegerMultiple": null, "withdrawMax": "5000000", "withdrawMin": "2", "sameAddress": false, "contract": "******************************************", "withdrawTips": null, "depositTips": null, "netWork": "CELO"}, {"coin": "USDT", "depositDesc": null, "depositEnable": true, "minConfirm": "12", "name": "USDT", "network": "APTOS(APT)", "withdrawEnable": true, "withdrawFee": "0.03", "withdrawIntegerMultiple": null, "withdrawMax": "2000000", "withdrawMin": "10", "sameAddress": false, "contract": "0x357b0b74bc833e95a115ad22604854d6b0fca151cecd94111770e5d6ffc9dc2b", "withdrawTips": null, "depositTips": null, "netWork": "APTOS"}]}, "id": "USDT", "numericId": null, "code": "USDT", "precision": null, "type": "crypto", "name": "USDT", "active": true, "deposit": true, "withdraw": true, "fee": 0, "fees": {}, "networks": {"ERC20": {"info": {"coin": "USDT", "depositDesc": null, "depositEnable": true, "minConfirm": "96", "name": "<PERSON><PERSON>", "network": "Ethereum(ERC20)", "withdrawEnable": true, "withdrawFee": "0.5", "withdrawIntegerMultiple": null, "withdrawMax": "26000000", "withdrawMin": "10", "sameAddress": false, "contract": "******************************************", "withdrawTips": null, "depositTips": null, "netWork": "ETH"}, "id": "ETH", "network": "ERC20", "active": true, "deposit": true, "withdraw": true, "fee": 0.5, "precision": null, "limits": {"withdraw": {"min": "10", "max": "26000000"}}}, "TRC20": {"info": {"coin": "USDT", "depositDesc": null, "depositEnable": true, "minConfirm": "20", "name": "<PERSON><PERSON>", "network": "Tron(TRC20)", "withdrawEnable": true, "withdrawFee": "1", "withdrawIntegerMultiple": null, "withdrawMax": "30000000", "withdrawMin": "3", "sameAddress": false, "contract": "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t", "withdrawTips": null, "depositTips": null, "netWork": "TRX"}, "id": "TRX", "network": "TRC20", "active": true, "deposit": true, "withdraw": true, "fee": 1, "precision": null, "limits": {"withdraw": {"min": "3", "max": "30000000"}}}, "BEP20": {"info": {"coin": "USDT", "depositDesc": null, "depositEnable": true, "minConfirm": "61", "name": "USDT-BSC", "network": "BNB Smart Chain(BEP20)", "withdrawEnable": true, "withdrawFee": "0", "withdrawIntegerMultiple": null, "withdrawMax": "20000000", "withdrawMin": "10", "sameAddress": false, "contract": "******************************************", "withdrawTips": null, "depositTips": "", "netWork": "BSC"}, "id": "BSC", "network": "BEP20", "active": true, "deposit": true, "withdraw": true, "fee": 0, "precision": null, "limits": {"withdraw": {"min": "10", "max": "20000000"}}}, "SOL": {"info": {"coin": "USDT", "depositDesc": null, "depositEnable": true, "minConfirm": "100", "name": "USDT-SOL", "network": "Solana(SOL)", "withdrawEnable": true, "withdrawFee": "0.5", "withdrawIntegerMultiple": null, "withdrawMax": "9000000", "withdrawMin": "10", "sameAddress": false, "contract": "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB", "withdrawTips": "The withdrawal address must be the owner’s address or an activated secondary address. Otherwise, you will lose your funds. Due to network congestion of the Solana chain, it might take longer time to process withdrawal requests. Please confirm before proceeding.", "depositTips": "SOL network is experiencing a congestion so there might be a delay in your deposit.", "netWork": "SOL"}, "id": "SOL", "network": "SOL", "active": true, "deposit": true, "withdraw": true, "fee": 0.5, "precision": null, "limits": {"withdraw": {"min": "10", "max": "9000000"}}}, "MATIC": {"info": {"coin": "USDT", "depositDesc": null, "depositEnable": true, "minConfirm": "450", "name": "USDT-MATIC", "network": "Polygon(MATIC)", "withdrawEnable": true, "withdrawFee": "0.02", "withdrawIntegerMultiple": null, "withdrawMax": "5000000", "withdrawMin": "5", "sameAddress": false, "contract": "0xc2132d05d31c914a87c6611c10748aeb04b58e8f", "withdrawTips": null, "depositTips": null, "netWork": "MATIC"}, "id": "MATIC", "network": "MATIC", "active": true, "deposit": true, "withdraw": true, "fee": 0.02, "precision": null, "limits": {"withdraw": {"min": "5", "max": "5000000"}}}, "OKC": {"info": {"coin": "USDT", "depositDesc": null, "depositEnable": true, "minConfirm": "35", "name": "<PERSON><PERSON>", "network": "OKT", "withdrawEnable": false, "withdrawFee": "0", "withdrawIntegerMultiple": null, "withdrawMax": "1000000", "withdrawMin": "10", "sameAddress": false, "contract": "0x382bb369d343125bfb2117af9c149795c6c65c50", "withdrawTips": null, "depositTips": null, "netWork": "OKT"}, "id": "OKT", "network": "OKC", "active": false, "deposit": true, "withdraw": false, "fee": 0, "precision": null, "limits": {"withdraw": {"min": "10", "max": "1000000"}}}, "ARB": {"info": {"coin": "USDT", "depositDesc": null, "depositEnable": true, "minConfirm": "100", "name": "<PERSON><PERSON>", "network": "Arbitrum One(ARB)", "withdrawEnable": true, "withdrawFee": "0.11", "withdrawIntegerMultiple": null, "withdrawMax": "5000000", "withdrawMin": "1", "sameAddress": false, "contract": "0xfd086bc7cd5c481dcc9c85ebe478a1c0b69fcbb9", "withdrawTips": null, "depositTips": null, "netWork": "ARB"}, "id": "ARB", "network": "ARB", "active": true, "deposit": true, "withdraw": true, "fee": 0.11, "precision": null, "limits": {"withdraw": {"min": "1", "max": "5000000"}}}, "OPTIMISM": {"info": {"coin": "USDT", "depositDesc": null, "depositEnable": true, "minConfirm": "200", "name": "<PERSON><PERSON>", "network": "Optimism(OP)", "withdrawEnable": true, "withdrawFee": "0.015", "withdrawIntegerMultiple": null, "withdrawMax": "2000000", "withdrawMin": "3", "sameAddress": false, "contract": "0x94b008aA00579c1307B0EF2c499aD98a8ce58e58", "withdrawTips": null, "depositTips": null, "netWork": "OP"}, "id": "OP", "network": "OPTIMISM", "active": true, "deposit": true, "withdraw": true, "fee": 0.015, "precision": null, "limits": {"withdraw": {"min": "3", "max": "2000000"}}}, "AVAXC": {"info": {"coin": "USDT", "depositDesc": null, "depositEnable": true, "minConfirm": "35", "name": "<PERSON><PERSON>", "network": "Avalanche C Chain(AVAX CCHAIN)", "withdrawEnable": true, "withdrawFee": "0.12", "withdrawIntegerMultiple": null, "withdrawMax": "2000000", "withdrawMin": "5", "sameAddress": false, "contract": "******************************************", "withdrawTips": null, "depositTips": null, "netWork": "AVAX_CCHAIN"}, "id": "AVAX_CCHAIN", "network": "AVAXC", "active": true, "deposit": true, "withdraw": true, "fee": 0.12, "precision": null, "limits": {"withdraw": {"min": "5", "max": "2000000"}}}, "NEAR": {"info": {"coin": "USDT", "depositDesc": null, "depositEnable": true, "minConfirm": "100", "name": "USDT", "network": "NEAR Protocol(NEAR)", "withdrawEnable": true, "withdrawFee": "0.2", "withdrawIntegerMultiple": null, "withdrawMax": "200000", "withdrawMin": "2", "sameAddress": false, "contract": "usdt.tether-token.near", "withdrawTips": null, "depositTips": null, "netWork": "NEAR"}, "id": "NEAR", "network": "NEAR", "active": true, "deposit": true, "withdraw": true, "fee": 0.2, "precision": null, "limits": {"withdraw": {"min": "2", "max": "200000"}}}, "TON": {"info": {"coin": "USDT", "depositDesc": null, "depositEnable": true, "minConfirm": "10", "name": "USDT", "network": "<PERSON><PERSON><PERSON>(TON)", "withdrawEnable": true, "withdrawFee": "0", "withdrawIntegerMultiple": null, "withdrawMax": "5000000", "withdrawMin": "10", "sameAddress": true, "contract": "EQCxE6mUtQJKFnGfaROTKOt1lZbDiiX1kCixRv7Nw2Id_sDs", "withdrawTips": null, "depositTips": "", "netWork": "TONCOIN"}, "id": "TONCOIN", "network": "TON", "active": true, "deposit": true, "withdraw": true, "fee": 0, "precision": null, "limits": {"withdraw": {"min": "10", "max": "5000000"}}}, "CELO": {"info": {"coin": "USDT", "depositDesc": null, "depositEnable": true, "minConfirm": "24", "name": "USDT", "network": "CELO", "withdrawEnable": true, "withdrawFee": "0.49", "withdrawIntegerMultiple": null, "withdrawMax": "5000000", "withdrawMin": "2", "sameAddress": false, "contract": "******************************************", "withdrawTips": null, "depositTips": null, "netWork": "CELO"}, "id": "CELO", "network": "CELO", "active": true, "deposit": true, "withdraw": true, "fee": 0.49, "precision": null, "limits": {"withdraw": {"min": "2", "max": "5000000"}}}, "APTOS": {"info": {"coin": "USDT", "depositDesc": null, "depositEnable": true, "minConfirm": "12", "name": "USDT", "network": "APTOS(APT)", "withdrawEnable": true, "withdrawFee": "0.03", "withdrawIntegerMultiple": null, "withdrawMax": "2000000", "withdrawMin": "10", "sameAddress": false, "contract": "0x357b0b74bc833e95a115ad22604854d6b0fca151cecd94111770e5d6ffc9dc2b", "withdrawTips": null, "depositTips": null, "netWork": "APTOS"}, "id": "APTOS", "network": "APTOS", "active": true, "deposit": true, "withdraw": true, "fee": 0.03, "precision": null, "limits": {"withdraw": {"min": "10", "max": "2000000"}}}}, "limits": {"amount": {"min": null, "max": null}, "deposit": {"min": null, "max": null}, "withdraw": {"min": 1, "max": 30000000}}}, "LTC": {"info": {"coin": "LTC", "name": "Litecoin", "networkList": [{"coin": "LTC", "depositDesc": null, "depositEnable": true, "minConfirm": "10", "name": "Litecoin", "network": "Litecoin(LTC)", "withdrawEnable": true, "withdrawFee": "0.0001", "withdrawIntegerMultiple": null, "withdrawMax": "5000", "withdrawMin": "0.05", "sameAddress": false, "contract": "", "withdrawTips": null, "depositTips": null, "netWork": "LTC"}]}, "id": "LTC", "numericId": null, "code": "LTC", "precision": null, "type": "crypto", "name": "Litecoin", "active": true, "deposit": true, "withdraw": true, "fee": 0.0001, "fees": {}, "networks": {"LTC": {"info": {"coin": "LTC", "depositDesc": null, "depositEnable": true, "minConfirm": "10", "name": "Litecoin", "network": "Litecoin(LTC)", "withdrawEnable": true, "withdrawFee": "0.0001", "withdrawIntegerMultiple": null, "withdrawMax": "5000", "withdrawMin": "0.05", "sameAddress": false, "contract": "", "withdrawTips": null, "depositTips": null, "netWork": "LTC"}, "id": "LTC", "network": "LTC", "active": true, "deposit": true, "withdraw": true, "fee": 0.0001, "precision": null, "limits": {"withdraw": {"min": "0.05", "max": "5000"}}}}, "limits": {"amount": {"min": null, "max": null}, "deposit": {"min": null, "max": null}, "withdraw": {"min": 0.05, "max": 5000}}}, "ETH": {"info": {"coin": "ETH", "name": "ETH", "networkList": [{"coin": "ETH", "depositDesc": null, "depositEnable": true, "minConfirm": "96", "name": "Ethereum", "network": "Ethereum(ERC20)", "withdrawEnable": true, "withdrawFee": "0.00034", "withdrawIntegerMultiple": null, "withdrawMax": "1500", "withdrawMin": "0.002", "sameAddress": false, "contract": "", "withdrawTips": null, "depositTips": null, "netWork": "ETH"}, {"coin": "ETH", "depositDesc": null, "depositEnable": true, "minConfirm": "61", "name": "ETH-BSC", "network": "BNB Smart Chain(BEP20)", "withdrawEnable": true, "withdrawFee": "0.000213", "withdrawIntegerMultiple": null, "withdrawMax": "10000", "withdrawMin": "0.0005", "sameAddress": false, "contract": "******************************************", "withdrawTips": null, "depositTips": null, "netWork": "BSC"}, {"coin": "ETH", "depositDesc": "Deposit suspended due to wallet maintenance", "depositEnable": false, "minConfirm": "100", "name": "ETH", "network": "Solana(SOL)", "withdrawEnable": false, "withdrawFee": "0.0004", "withdrawIntegerMultiple": null, "withdrawMax": "1200", "withdrawMin": "0.001", "sameAddress": false, "contract": "2FPyTwcZLUg1MDrwsyoP4D6s1tM7hAkHYRjkNb5w6Pxk", "withdrawTips": null, "depositTips": "Due to block scanning delay on SOL, deposits may be slightly delayed.", "netWork": "SOL"}, {"coin": "ETH", "depositDesc": null, "depositEnable": true, "minConfirm": "100", "name": "Ethereum", "network": "Arbitrum One(ARB)", "withdrawEnable": true, "withdrawFee": "0.00003", "withdrawIntegerMultiple": null, "withdrawMax": "1000", "withdrawMin": "0.001", "sameAddress": false, "contract": "", "withdrawTips": null, "depositTips": null, "netWork": "ARB"}, {"coin": "ETH", "depositDesc": null, "depositEnable": true, "minConfirm": "200", "name": "ETH", "network": "Optimism(OP)", "withdrawEnable": true, "withdrawFee": "0.00001", "withdrawIntegerMultiple": null, "withdrawMax": "1000", "withdrawMin": "0.0003", "sameAddress": false, "contract": "", "withdrawTips": null, "depositTips": null, "netWork": "OP"}, {"coin": "ETH", "depositDesc": null, "depositEnable": true, "minConfirm": "4", "name": "ETH", "network": "zkSync Lite", "withdrawEnable": true, "withdrawFee": "0.0003", "withdrawIntegerMultiple": null, "withdrawMax": "200", "withdrawMin": "0.0005", "sameAddress": false, "contract": "", "withdrawTips": "The token is currently zkSync Lite 1.0, Please confirm before proceeding.", "depositTips": "The token is currently zkSync Lite 1.0, Please confirm before proceeding.", "netWork": "ZKSYNC"}, {"coin": "ETH", "depositDesc": null, "depositEnable": true, "minConfirm": "40", "name": "ETH", "network": "Starknet(STARK)", "withdrawEnable": true, "withdrawFee": "0.00009", "withdrawIntegerMultiple": null, "withdrawMax": "1000000", "withdrawMin": "0.0002", "sameAddress": false, "contract": "", "withdrawTips": null, "depositTips": null, "netWork": "STARK"}, {"coin": "ETH", "depositDesc": null, "depositEnable": true, "minConfirm": "10", "name": "ETH-LINEA", "network": "Linea", "withdrawEnable": true, "withdrawFee": "0.00019", "withdrawIntegerMultiple": null, "withdrawMax": "300", "withdrawMin": "0.0008", "sameAddress": false, "contract": "", "withdrawTips": null, "depositTips": null, "netWork": "LINEA"}, {"coin": "ETH", "depositDesc": null, "depositEnable": true, "minConfirm": "60", "name": "ETH-BASE", "network": "BASE", "withdrawEnable": true, "withdrawFee": "0.00003", "withdrawIntegerMultiple": null, "withdrawMax": "500", "withdrawMin": "0.003", "sameAddress": false, "contract": "", "withdrawTips": null, "depositTips": null, "netWork": "BASE"}, {"coin": "ETH", "depositDesc": null, "depositEnable": true, "minConfirm": "720", "name": "ETH", "network": "MORPH", "withdrawEnable": true, "withdrawFee": "0.00008", "withdrawIntegerMultiple": null, "withdrawMax": "76.923076923076923076923076923076", "withdrawMin": "0.0001", "sameAddress": false, "contract": "", "withdrawTips": null, "depositTips": null, "netWork": "MORPH"}, {"coin": "ETH", "depositDesc": "Deposit suspended due to wallet maintenance", "depositEnable": false, "minConfirm": "60", "name": "ETH", "network": "MINTCHAIN", "withdrawEnable": false, "withdrawFee": "0.00001", "withdrawIntegerMultiple": null, "withdrawMax": "100", "withdrawMin": "0.00002", "sameAddress": false, "contract": "", "withdrawTips": null, "depositTips": null, "netWork": "MINTCHAIN"}, {"coin": "ETH", "depositDesc": "Deposit suspended as project integration is in progress", "depositEnable": false, "minConfirm": "20", "name": "ETH", "network": "HEMI", "withdrawEnable": false, "withdrawFee": "0.00001", "withdrawIntegerMultiple": null, "withdrawMax": "125", "withdrawMin": "0.00002", "sameAddress": false, "contract": "", "withdrawTips": null, "depositTips": null, "netWork": "HEMI"}, {"coin": "ETH", "depositDesc": "Deposit suspended due to wallet maintenance", "depositEnable": false, "minConfirm": "20", "name": "ETH", "network": "MINDNETWORK", "withdrawEnable": false, "withdrawFee": "0.00001", "withdrawIntegerMultiple": null, "withdrawMax": "117.647058823529411764705882352941", "withdrawMin": "0.00002", "sameAddress": false, "contract": "", "withdrawTips": null, "depositTips": null, "netWork": "MINDNETWORK"}]}, "id": "ETH", "numericId": null, "code": "ETH", "precision": null, "type": "crypto", "name": "ETH", "active": true, "deposit": true, "withdraw": true, "fee": 1e-05, "fees": {}, "networks": {"ERC20": {"info": {"coin": "ETH", "depositDesc": null, "depositEnable": true, "minConfirm": "96", "name": "Ethereum", "network": "Ethereum(ERC20)", "withdrawEnable": true, "withdrawFee": "0.00034", "withdrawIntegerMultiple": null, "withdrawMax": "1500", "withdrawMin": "0.002", "sameAddress": false, "contract": "", "withdrawTips": null, "depositTips": null, "netWork": "ETH"}, "id": "ETH", "network": "ERC20", "active": true, "deposit": true, "withdraw": true, "fee": 0.00034, "precision": null, "limits": {"withdraw": {"min": "0.002", "max": "1500"}}}, "BEP20": {"info": {"coin": "ETH", "depositDesc": null, "depositEnable": true, "minConfirm": "61", "name": "ETH-BSC", "network": "BNB Smart Chain(BEP20)", "withdrawEnable": true, "withdrawFee": "0.000213", "withdrawIntegerMultiple": null, "withdrawMax": "10000", "withdrawMin": "0.0005", "sameAddress": false, "contract": "******************************************", "withdrawTips": null, "depositTips": null, "netWork": "BSC"}, "id": "BSC", "network": "BEP20", "active": true, "deposit": true, "withdraw": true, "fee": 0.000213, "precision": null, "limits": {"withdraw": {"min": "0.0005", "max": "10000"}}}, "SOL": {"info": {"coin": "ETH", "depositDesc": "Deposit suspended due to wallet maintenance", "depositEnable": false, "minConfirm": "100", "name": "ETH", "network": "Solana(SOL)", "withdrawEnable": false, "withdrawFee": "0.0004", "withdrawIntegerMultiple": null, "withdrawMax": "1200", "withdrawMin": "0.001", "sameAddress": false, "contract": "2FPyTwcZLUg1MDrwsyoP4D6s1tM7hAkHYRjkNb5w6Pxk", "withdrawTips": null, "depositTips": "Due to block scanning delay on SOL, deposits may be slightly delayed.", "netWork": "SOL"}, "id": "SOL", "network": "SOL", "active": false, "deposit": false, "withdraw": false, "fee": 0.0004, "precision": null, "limits": {"withdraw": {"min": "0.001", "max": "1200"}}}, "ARB": {"info": {"coin": "ETH", "depositDesc": null, "depositEnable": true, "minConfirm": "100", "name": "Ethereum", "network": "Arbitrum One(ARB)", "withdrawEnable": true, "withdrawFee": "0.00003", "withdrawIntegerMultiple": null, "withdrawMax": "1000", "withdrawMin": "0.001", "sameAddress": false, "contract": "", "withdrawTips": null, "depositTips": null, "netWork": "ARB"}, "id": "ARB", "network": "ARB", "active": true, "deposit": true, "withdraw": true, "fee": 3e-05, "precision": null, "limits": {"withdraw": {"min": "0.001", "max": "1000"}}}, "OPTIMISM": {"info": {"coin": "ETH", "depositDesc": null, "depositEnable": true, "minConfirm": "200", "name": "ETH", "network": "Optimism(OP)", "withdrawEnable": true, "withdrawFee": "0.00001", "withdrawIntegerMultiple": null, "withdrawMax": "1000", "withdrawMin": "0.0003", "sameAddress": false, "contract": "", "withdrawTips": null, "depositTips": null, "netWork": "OP"}, "id": "OP", "network": "OPTIMISM", "active": true, "deposit": true, "withdraw": true, "fee": 1e-05, "precision": null, "limits": {"withdraw": {"min": "0.0003", "max": "1000"}}}, "ZKSYNC": {"info": {"coin": "ETH", "depositDesc": null, "depositEnable": true, "minConfirm": "4", "name": "ETH", "network": "zkSync Lite", "withdrawEnable": true, "withdrawFee": "0.0003", "withdrawIntegerMultiple": null, "withdrawMax": "200", "withdrawMin": "0.0005", "sameAddress": false, "contract": "", "withdrawTips": "The token is currently zkSync Lite 1.0, Please confirm before proceeding.", "depositTips": "The token is currently zkSync Lite 1.0, Please confirm before proceeding.", "netWork": "ZKSYNC"}, "id": "ZKSYNC", "network": "ZKSYNC", "active": true, "deposit": true, "withdraw": true, "fee": 0.0003, "precision": null, "limits": {"withdraw": {"min": "0.0005", "max": "200"}}}, "STARK": {"info": {"coin": "ETH", "depositDesc": null, "depositEnable": true, "minConfirm": "40", "name": "ETH", "network": "Starknet(STARK)", "withdrawEnable": true, "withdrawFee": "0.00009", "withdrawIntegerMultiple": null, "withdrawMax": "1000000", "withdrawMin": "0.0002", "sameAddress": false, "contract": "", "withdrawTips": null, "depositTips": null, "netWork": "STARK"}, "id": "STARK", "network": "STARK", "active": true, "deposit": true, "withdraw": true, "fee": 9e-05, "precision": null, "limits": {"withdraw": {"min": "0.0002", "max": "1000000"}}}, "LINEA": {"info": {"coin": "ETH", "depositDesc": null, "depositEnable": true, "minConfirm": "10", "name": "ETH-LINEA", "network": "Linea", "withdrawEnable": true, "withdrawFee": "0.00019", "withdrawIntegerMultiple": null, "withdrawMax": "300", "withdrawMin": "0.0008", "sameAddress": false, "contract": "", "withdrawTips": null, "depositTips": null, "netWork": "LINEA"}, "id": "LINEA", "network": "LINEA", "active": true, "deposit": true, "withdraw": true, "fee": 0.00019, "precision": null, "limits": {"withdraw": {"min": "0.0008", "max": "300"}}}, "BASE": {"info": {"coin": "ETH", "depositDesc": null, "depositEnable": true, "minConfirm": "60", "name": "ETH-BASE", "network": "BASE", "withdrawEnable": true, "withdrawFee": "0.00003", "withdrawIntegerMultiple": null, "withdrawMax": "500", "withdrawMin": "0.003", "sameAddress": false, "contract": "", "withdrawTips": null, "depositTips": null, "netWork": "BASE"}, "id": "BASE", "network": "BASE", "active": true, "deposit": true, "withdraw": true, "fee": 3e-05, "precision": null, "limits": {"withdraw": {"min": "0.003", "max": "500"}}}, "MORPH": {"info": {"coin": "ETH", "depositDesc": null, "depositEnable": true, "minConfirm": "720", "name": "ETH", "network": "MORPH", "withdrawEnable": true, "withdrawFee": "0.00008", "withdrawIntegerMultiple": null, "withdrawMax": "76.923076923076923076923076923076", "withdrawMin": "0.0001", "sameAddress": false, "contract": "", "withdrawTips": null, "depositTips": null, "netWork": "MORPH"}, "id": "MORPH", "network": "MORPH", "active": true, "deposit": true, "withdraw": true, "fee": 8e-05, "precision": null, "limits": {"withdraw": {"min": "0.0001", "max": "76.923076923076923076923076923076"}}}, "MINTCHAIN": {"info": {"coin": "ETH", "depositDesc": "Deposit suspended due to wallet maintenance", "depositEnable": false, "minConfirm": "60", "name": "ETH", "network": "MINTCHAIN", "withdrawEnable": false, "withdrawFee": "0.00001", "withdrawIntegerMultiple": null, "withdrawMax": "100", "withdrawMin": "0.00002", "sameAddress": false, "contract": "", "withdrawTips": null, "depositTips": null, "netWork": "MINTCHAIN"}, "id": "MINTCHAIN", "network": "MINTCHAIN", "active": false, "deposit": false, "withdraw": false, "fee": 1e-05, "precision": null, "limits": {"withdraw": {"min": "0.00002", "max": "100"}}}, "HEMI": {"info": {"coin": "ETH", "depositDesc": "Deposit suspended as project integration is in progress", "depositEnable": false, "minConfirm": "20", "name": "ETH", "network": "HEMI", "withdrawEnable": false, "withdrawFee": "0.00001", "withdrawIntegerMultiple": null, "withdrawMax": "125", "withdrawMin": "0.00002", "sameAddress": false, "contract": "", "withdrawTips": null, "depositTips": null, "netWork": "HEMI"}, "id": "HEMI", "network": "HEMI", "active": false, "deposit": false, "withdraw": false, "fee": 1e-05, "precision": null, "limits": {"withdraw": {"min": "0.00002", "max": "125"}}}, "MINDNETWORK": {"info": {"coin": "ETH", "depositDesc": "Deposit suspended due to wallet maintenance", "depositEnable": false, "minConfirm": "20", "name": "ETH", "network": "MINDNETWORK", "withdrawEnable": false, "withdrawFee": "0.00001", "withdrawIntegerMultiple": null, "withdrawMax": "117.647058823529411764705882352941", "withdrawMin": "0.00002", "sameAddress": false, "contract": "", "withdrawTips": null, "depositTips": null, "netWork": "MINDNETWORK"}, "id": "MINDNETWORK", "network": "MINDNETWORK", "active": false, "deposit": false, "withdraw": false, "fee": 1e-05, "precision": null, "limits": {"withdraw": {"min": "0.00002", "max": "117.647058823529411764705882352941"}}}}, "limits": {"amount": {"min": null, "max": null}, "deposit": {"min": null, "max": null}, "withdraw": {"min": 2e-05, "max": 1000000}}}, "ADA": {"info": {"coin": "ADA", "name": "Cardano", "networkList": [{"coin": "ADA", "depositDesc": null, "depositEnable": true, "minConfirm": "35", "name": "Cardano", "network": "Cardano(ADA)", "withdrawEnable": true, "withdrawFee": "1", "withdrawIntegerMultiple": null, "withdrawMax": "200000", "withdrawMin": "7", "sameAddress": false, "contract": "", "withdrawTips": null, "depositTips": null, "netWork": "ADA"}]}, "id": "ADA", "numericId": null, "code": "ADA", "precision": null, "type": "crypto", "name": "Cardano", "active": true, "deposit": true, "withdraw": true, "fee": 1, "fees": {}, "networks": {"ADA": {"info": {"coin": "ADA", "depositDesc": null, "depositEnable": true, "minConfirm": "35", "name": "Cardano", "network": "Cardano(ADA)", "withdrawEnable": true, "withdrawFee": "1", "withdrawIntegerMultiple": null, "withdrawMax": "200000", "withdrawMin": "7", "sameAddress": false, "contract": "", "withdrawTips": null, "depositTips": null, "netWork": "ADA"}, "id": "ADA", "network": "ADA", "active": true, "deposit": true, "withdraw": true, "fee": 1, "precision": null, "limits": {"withdraw": {"min": "7", "max": "200000"}}}}, "limits": {"amount": {"min": null, "max": null}, "deposit": {"min": null, "max": null}, "withdraw": {"min": 7, "max": 200000}}}, "XRP": {"info": {"coin": "XRP", "name": "XRP", "networkList": [{"coin": "XRP", "depositDesc": null, "depositEnable": true, "minConfirm": "30", "name": "XRP", "network": "R<PERSON><PERSON>(XRP)", "withdrawEnable": true, "withdrawFee": "0.187", "withdrawIntegerMultiple": null, "withdrawMax": "1000000", "withdrawMin": "11", "sameAddress": true, "contract": "", "withdrawTips": null, "depositTips": "", "netWork": "XRP"}]}, "id": "XRP", "numericId": null, "code": "XRP", "precision": null, "type": "crypto", "name": "XRP", "active": true, "deposit": true, "withdraw": true, "fee": 0.187, "fees": {}, "networks": {"XRP": {"info": {"coin": "XRP", "depositDesc": null, "depositEnable": true, "minConfirm": "30", "name": "XRP", "network": "R<PERSON><PERSON>(XRP)", "withdrawEnable": true, "withdrawFee": "0.187", "withdrawIntegerMultiple": null, "withdrawMax": "1000000", "withdrawMin": "11", "sameAddress": true, "contract": "", "withdrawTips": null, "depositTips": "", "netWork": "XRP"}, "id": "XRP", "network": "XRP", "active": true, "deposit": true, "withdraw": true, "fee": 0.187, "precision": null, "limits": {"withdraw": {"min": "11", "max": "1000000"}}}}, "limits": {"amount": {"min": null, "max": null}, "deposit": {"min": null, "max": null}, "withdraw": {"min": 11, "max": 1000000}}}, "USDC": {"info": {"coin": "USDC", "name": "usdc", "networkList": [{"coin": "USDC", "depositDesc": null, "depositEnable": true, "minConfirm": "96", "name": "USDCoin", "network": "Ethereum(ERC20)", "withdrawEnable": true, "withdrawFee": "0.5", "withdrawIntegerMultiple": null, "withdrawMax": "10000000", "withdrawMin": "10", "sameAddress": false, "contract": "******************************************", "withdrawTips": null, "depositTips": null, "netWork": "ETH"}, {"coin": "USDC", "depositDesc": null, "depositEnable": true, "minConfirm": "100", "name": "USDC-SOL", "network": "Solana(SOL)", "withdrawEnable": true, "withdrawFee": "0.5", "withdrawIntegerMultiple": null, "withdrawMax": "5000000", "withdrawMin": "20", "sameAddress": false, "contract": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v", "withdrawTips": "The withdrawal address must be the owner’s address or an activated secondary address. Otherwise, you will lose your funds. Due to network congestion of the Solana chain, it might take longer time to process withdrawal requests. Please confirm before proceeding.", "depositTips": "The delay in the SOL chain scanning may lead to a delay in your deposit.", "netWork": "SOL"}, {"coin": "USDC", "depositDesc": null, "depositEnable": true, "minConfirm": "450", "name": "USDCoin", "network": "Polygon(MATIC-Bridged)", "withdrawEnable": true, "withdrawFee": "0.02", "withdrawIntegerMultiple": null, "withdrawMax": "1000000", "withdrawMin": "2", "sameAddress": false, "contract": "0x2791bca1f2de4661ed88a30c99a7a9449aa84174", "withdrawTips": "The last digits of the token's contract address are 4174, please make sure they're correct before taking action.", "depositTips": "The last digits of the token's contract address are 4174, please make sure they're correct before taking action.", "netWork": "MATIC"}, {"coin": "USDC", "depositDesc": null, "depositEnable": true, "minConfirm": "10", "name": "USDCoin", "network": "Algorand(ALGO)", "withdrawEnable": true, "withdrawFee": "0.8", "withdrawIntegerMultiple": null, "withdrawMax": "5000000", "withdrawMin": "2", "sameAddress": false, "contract": "31566704", "withdrawTips": "As USDC is on ALGO chain, it’s mandatory for users to activate USDC(ASSET : 31566704) on the certain address. .Otherwise the withdrawal will not be successful.", "depositTips": null, "netWork": "ALGO"}, {"coin": "USDC", "depositDesc": null, "depositEnable": true, "minConfirm": "61", "name": "USD Coin", "network": "BNB Smart Chain(BEP20)", "withdrawEnable": true, "withdrawFee": "0", "withdrawIntegerMultiple": null, "withdrawMax": "5000000", "withdrawMin": "10", "sameAddress": false, "contract": "0x8AC76a51cc950d9822D68b83fE1Ad97B32Cd580d", "withdrawTips": null, "depositTips": null, "netWork": "BSC"}, {"coin": "USDC", "depositDesc": null, "depositEnable": true, "minConfirm": "200", "name": "USDC", "network": "Optimism(OP-Bridged)", "withdrawEnable": true, "withdrawFee": "0.05", "withdrawIntegerMultiple": null, "withdrawMax": "500000", "withdrawMin": "1", "sameAddress": false, "contract": "0x7F5c764cBc14f9669B88837ca1490cCa17c31607", "withdrawTips": "The last digits of the token's contract address are 31607, please make sure they're correct before taking action.", "depositTips": "The last digits of the token's contract address are 31607, please make sure they're correct before taking action.", "netWork": "OP"}, {"coin": "USDC", "depositDesc": null, "depositEnable": true, "minConfirm": "100", "name": "USDC", "network": "Arbitrum One(ARB)", "withdrawEnable": true, "withdrawFee": "0.097", "withdrawIntegerMultiple": null, "withdrawMax": "2000000", "withdrawMin": "2", "sameAddress": false, "contract": "0xaf88d065e77c8cC2239327C5EDb3A432268e5831", "withdrawTips": "The last digits of the token's contract address are 5831, please make sure they're correct before taking action.", "depositTips": "The last digits of the token's contract address are 5831, please make sure they're correct before taking action.", "netWork": "ARB"}, {"coin": "USDC", "depositDesc": null, "depositEnable": true, "minConfirm": "450", "name": "USDCoin", "network": "Polygon(MATIC)", "withdrawEnable": true, "withdrawFee": "0.02", "withdrawIntegerMultiple": null, "withdrawMax": "1000000", "withdrawMin": "2", "sameAddress": false, "contract": "0x3c499c542cEF5E3811e1192ce70d8cC03d5c3359", "withdrawTips": "The last digits of the token's contract address are 3359, please make sure they're correct before taking action.", "depositTips": "The last digits of the token's contract address are 3359, please make sure they're correct before taking action.", "netWork": "MATIC"}, {"coin": "USDC", "depositDesc": null, "depositEnable": true, "minConfirm": "200", "name": "USDC", "network": "Optimism(OP)", "withdrawEnable": true, "withdrawFee": "0.015", "withdrawIntegerMultiple": null, "withdrawMax": "2000000", "withdrawMin": "2", "sameAddress": false, "contract": "0x0b2C639c533813f4Aa9D7837CAf62653d097Ff85", "withdrawTips": "The last digits of the token's contract address are 7Ff85, please make sure they're correct before taking action.", "depositTips": "The last digits of the token's contract address are 7Ff85, please make sure they're correct before taking action.", "netWork": "OP"}, {"coin": "USDC", "depositDesc": null, "depositEnable": true, "minConfirm": "35", "name": "<PERSON><PERSON>", "network": "Avalanche C Chain(AVAX CCHAIN)", "withdrawEnable": true, "withdrawFee": "0.12", "withdrawIntegerMultiple": null, "withdrawMax": "1000000", "withdrawMin": "5", "sameAddress": false, "contract": "0xB97EF9Ef8734C71904D8002F8b6Bc66Dd9c48a6E", "withdrawTips": null, "depositTips": null, "netWork": "AVAX_CCHAIN"}, {"coin": "USDC", "depositDesc": null, "depositEnable": true, "minConfirm": "100", "name": "USDC", "network": "NEAR Protocol(NEAR)", "withdrawEnable": true, "withdrawFee": "0.2", "withdrawIntegerMultiple": null, "withdrawMax": "400000", "withdrawMin": "2", "sameAddress": false, "contract": "17208628f84f5d6ad33f0da3bbbeb27ffcb398eac501a31bd6ad2011e36133a1", "withdrawTips": null, "depositTips": null, "netWork": "NEAR"}, {"coin": "USDC", "depositDesc": null, "depositEnable": true, "minConfirm": "12", "name": "usdc", "network": "APTOS(APT)", "withdrawEnable": true, "withdrawFee": "1", "withdrawIntegerMultiple": null, "withdrawMax": "500000", "withdrawMin": "2", "sameAddress": false, "contract": "0xbae207659db88bea0cbead6da0ed00aac12edcdda169e591cd41c94180b46f3b", "withdrawTips": null, "depositTips": null, "netWork": "APTOS"}]}, "id": "USDC", "numericId": null, "code": "USDC", "precision": null, "type": "crypto", "name": "usdc", "active": true, "deposit": true, "withdraw": true, "fee": 0, "fees": {}, "networks": {"ERC20": {"info": {"coin": "USDC", "depositDesc": null, "depositEnable": true, "minConfirm": "96", "name": "USDCoin", "network": "Ethereum(ERC20)", "withdrawEnable": true, "withdrawFee": "0.5", "withdrawIntegerMultiple": null, "withdrawMax": "10000000", "withdrawMin": "10", "sameAddress": false, "contract": "******************************************", "withdrawTips": null, "depositTips": null, "netWork": "ETH"}, "id": "ETH", "network": "ERC20", "active": true, "deposit": true, "withdraw": true, "fee": 0.5, "precision": null, "limits": {"withdraw": {"min": "10", "max": "10000000"}}}, "SOL": {"info": {"coin": "USDC", "depositDesc": null, "depositEnable": true, "minConfirm": "100", "name": "USDC-SOL", "network": "Solana(SOL)", "withdrawEnable": true, "withdrawFee": "0.5", "withdrawIntegerMultiple": null, "withdrawMax": "5000000", "withdrawMin": "20", "sameAddress": false, "contract": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v", "withdrawTips": "The withdrawal address must be the owner’s address or an activated secondary address. Otherwise, you will lose your funds. Due to network congestion of the Solana chain, it might take longer time to process withdrawal requests. Please confirm before proceeding.", "depositTips": "The delay in the SOL chain scanning may lead to a delay in your deposit.", "netWork": "SOL"}, "id": "SOL", "network": "SOL", "active": true, "deposit": true, "withdraw": true, "fee": 0.5, "precision": null, "limits": {"withdraw": {"min": "20", "max": "5000000"}}}, "MATIC": {"info": {"coin": "USDC", "depositDesc": null, "depositEnable": true, "minConfirm": "450", "name": "USDCoin", "network": "Polygon(MATIC)", "withdrawEnable": true, "withdrawFee": "0.02", "withdrawIntegerMultiple": null, "withdrawMax": "1000000", "withdrawMin": "2", "sameAddress": false, "contract": "0x3c499c542cEF5E3811e1192ce70d8cC03d5c3359", "withdrawTips": "The last digits of the token's contract address are 3359, please make sure they're correct before taking action.", "depositTips": "The last digits of the token's contract address are 3359, please make sure they're correct before taking action.", "netWork": "MATIC"}, "id": "MATIC", "network": "MATIC", "active": true, "deposit": true, "withdraw": true, "fee": 0.02, "precision": null, "limits": {"withdraw": {"min": "2", "max": "1000000"}}}, "ALGO": {"info": {"coin": "USDC", "depositDesc": null, "depositEnable": true, "minConfirm": "10", "name": "USDCoin", "network": "Algorand(ALGO)", "withdrawEnable": true, "withdrawFee": "0.8", "withdrawIntegerMultiple": null, "withdrawMax": "5000000", "withdrawMin": "2", "sameAddress": false, "contract": "31566704", "withdrawTips": "As USDC is on ALGO chain, it’s mandatory for users to activate USDC(ASSET : 31566704) on the certain address. .Otherwise the withdrawal will not be successful.", "depositTips": null, "netWork": "ALGO"}, "id": "ALGO", "network": "ALGO", "active": true, "deposit": true, "withdraw": true, "fee": 0.8, "precision": null, "limits": {"withdraw": {"min": "2", "max": "5000000"}}}, "BEP20": {"info": {"coin": "USDC", "depositDesc": null, "depositEnable": true, "minConfirm": "61", "name": "USD Coin", "network": "BNB Smart Chain(BEP20)", "withdrawEnable": true, "withdrawFee": "0", "withdrawIntegerMultiple": null, "withdrawMax": "5000000", "withdrawMin": "10", "sameAddress": false, "contract": "0x8AC76a51cc950d9822D68b83fE1Ad97B32Cd580d", "withdrawTips": null, "depositTips": null, "netWork": "BSC"}, "id": "BSC", "network": "BEP20", "active": true, "deposit": true, "withdraw": true, "fee": 0, "precision": null, "limits": {"withdraw": {"min": "10", "max": "5000000"}}}, "OPTIMISM": {"info": {"coin": "USDC", "depositDesc": null, "depositEnable": true, "minConfirm": "200", "name": "USDC", "network": "Optimism(OP)", "withdrawEnable": true, "withdrawFee": "0.015", "withdrawIntegerMultiple": null, "withdrawMax": "2000000", "withdrawMin": "2", "sameAddress": false, "contract": "0x0b2C639c533813f4Aa9D7837CAf62653d097Ff85", "withdrawTips": "The last digits of the token's contract address are 7Ff85, please make sure they're correct before taking action.", "depositTips": "The last digits of the token's contract address are 7Ff85, please make sure they're correct before taking action.", "netWork": "OP"}, "id": "OP", "network": "OPTIMISM", "active": true, "deposit": true, "withdraw": true, "fee": 0.015, "precision": null, "limits": {"withdraw": {"min": "2", "max": "2000000"}}}, "ARB": {"info": {"coin": "USDC", "depositDesc": null, "depositEnable": true, "minConfirm": "100", "name": "USDC", "network": "Arbitrum One(ARB)", "withdrawEnable": true, "withdrawFee": "0.097", "withdrawIntegerMultiple": null, "withdrawMax": "2000000", "withdrawMin": "2", "sameAddress": false, "contract": "0xaf88d065e77c8cC2239327C5EDb3A432268e5831", "withdrawTips": "The last digits of the token's contract address are 5831, please make sure they're correct before taking action.", "depositTips": "The last digits of the token's contract address are 5831, please make sure they're correct before taking action.", "netWork": "ARB"}, "id": "ARB", "network": "ARB", "active": true, "deposit": true, "withdraw": true, "fee": 0.097, "precision": null, "limits": {"withdraw": {"min": "2", "max": "2000000"}}}, "AVAXC": {"info": {"coin": "USDC", "depositDesc": null, "depositEnable": true, "minConfirm": "35", "name": "<PERSON><PERSON>", "network": "Avalanche C Chain(AVAX CCHAIN)", "withdrawEnable": true, "withdrawFee": "0.12", "withdrawIntegerMultiple": null, "withdrawMax": "1000000", "withdrawMin": "5", "sameAddress": false, "contract": "0xB97EF9Ef8734C71904D8002F8b6Bc66Dd9c48a6E", "withdrawTips": null, "depositTips": null, "netWork": "AVAX_CCHAIN"}, "id": "AVAX_CCHAIN", "network": "AVAXC", "active": true, "deposit": true, "withdraw": true, "fee": 0.12, "precision": null, "limits": {"withdraw": {"min": "5", "max": "1000000"}}}, "NEAR": {"info": {"coin": "USDC", "depositDesc": null, "depositEnable": true, "minConfirm": "100", "name": "USDC", "network": "NEAR Protocol(NEAR)", "withdrawEnable": true, "withdrawFee": "0.2", "withdrawIntegerMultiple": null, "withdrawMax": "400000", "withdrawMin": "2", "sameAddress": false, "contract": "17208628f84f5d6ad33f0da3bbbeb27ffcb398eac501a31bd6ad2011e36133a1", "withdrawTips": null, "depositTips": null, "netWork": "NEAR"}, "id": "NEAR", "network": "NEAR", "active": true, "deposit": true, "withdraw": true, "fee": 0.2, "precision": null, "limits": {"withdraw": {"min": "2", "max": "400000"}}}, "APTOS": {"info": {"coin": "USDC", "depositDesc": null, "depositEnable": true, "minConfirm": "12", "name": "usdc", "network": "APTOS(APT)", "withdrawEnable": true, "withdrawFee": "1", "withdrawIntegerMultiple": null, "withdrawMax": "500000", "withdrawMin": "2", "sameAddress": false, "contract": "0xbae207659db88bea0cbead6da0ed00aac12edcdda169e591cd41c94180b46f3b", "withdrawTips": null, "depositTips": null, "netWork": "APTOS"}, "id": "APTOS", "network": "APTOS", "active": true, "deposit": true, "withdraw": true, "fee": 1, "precision": null, "limits": {"withdraw": {"min": "2", "max": "500000"}}}}, "limits": {"amount": {"min": null, "max": null}, "deposit": {"min": null, "max": null}, "withdraw": {"min": 2, "max": 10000000}}}}