{"BTC": {"id": "BTC", "name": "Bitcoin", "code": "BTC", "type": "crypto", "precision": 1e-08, "info": {"coin": "BTC", "depositAllEnable": true, "withdrawAllEnable": true, "name": "Bitcoin", "free": "0", "locked": "0", "freeze": "0", "withdrawing": "0", "ipoing": "0", "ipoable": "0", "storage": "0", "isLegalMoney": false, "trading": true, "networkList": [{"network": "BSC", "coin": "BTC", "withdrawIntegerMultiple": "0.********", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "specialWithdrawTips": "The network you have selected is BSC. Please ensure that the withdrawal address supports the BNB Smart Chain network. You will potentially lose your assets if the chosen platform does not support refunds of wrongfully deposited assets.", "name": "BNB Smart Chain (BEP20)", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "0.0000069", "withdrawMin": "0.000014", "withdrawMax": "10000000000", "withdrawInternalMin": "0.********", "depositDust": "0.00000002", "minConfirm": "15", "unLockConfirm": "0", "sameAddress": false, "estimatedArrivalTime": "2", "busy": false, "contractAddressUrl": "https://bsctrace.com/token/", "contractAddress": "******************************************"}, {"network": "BTC", "coin": "BTC", "withdrawIntegerMultiple": "0.********", "isDefault": true, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "specialWithdrawTips": "Please note that withdrawal fees have been adjusted in due of the recent increase in activities on the BTC network.", "name": "Bitcoin", "resetAddressStatus": false, "addressRegex": "^[13][a-km-zA-HJ-NP-Z1-9]{25,34}$|^[(bc1q)|(bc1p)][0-9A-Za-z]{37,62}$", "memoRegex": "", "withdrawFee": "0.00003", "withdrawMin": "0.00011", "withdrawMax": "4000", "withdrawInternalMin": "0.********", "depositDust": "0.000006", "minConfirm": "1", "unLockConfirm": "2", "sameAddress": false, "estimatedArrivalTime": "60", "busy": false}, {"network": "SEGWITBTC", "coin": "BTC", "withdrawIntegerMultiple": "0.********", "isDefault": false, "depositEnable": true, "withdrawEnable": false, "depositDesc": "", "withdrawDesc": "Withdrawal for this token is not supported, please try other networks.", "specialTips": "", "specialWithdrawTips": "", "name": "BTC(SegWit)", "resetAddressStatus": false, "addressRegex": "", "memoRegex": "", "withdrawFee": "0.001", "withdrawMin": "0.002", "withdrawMax": "10000000000", "withdrawInternalMin": "0.********", "depositDust": "0.000006", "minConfirm": "1", "unLockConfirm": "2", "sameAddress": false, "estimatedArrivalTime": "0", "busy": false}, {"network": "ETH", "coin": "BTC", "withdrawIntegerMultiple": "0.********", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "You are depositing Binance Wrapped BTC (BBTC). Please ensure the token contract address ends with 22541.", "specialWithdrawTips": "You are withdrawing Binance Wrapped BTC (BBTC). Please ensure that the receiving platform supports this token or you will be at risk of losing your assets.", "name": "Ethereum (ERC20)", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "0.000019", "withdrawMin": "0.000038", "withdrawMax": "10000000000", "withdrawInternalMin": "0.********", "depositDust": "0.00000002", "minConfirm": "6", "unLockConfirm": "64", "sameAddress": false, "estimatedArrivalTime": "3", "busy": false, "contractAddressUrl": "https://etherscan.io/address/", "contractAddress": "******************************************"}, {"network": "LIGHTNING", "coin": "BTC", "withdrawIntegerMultiple": "0.********", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialWithdrawTips": "Withdrawal invoices containing non-standard feature bits are currently not supported. Refer to https://github.com/lightning/bolts/blob/master/09-features.md for the list of standard feature bits.", "name": "Lightning Network", "resetAddressStatus": false, "addressRegex": "^lnbc[a-z0-9]{200,500}$", "memoRegex": "", "withdrawFee": "0.000001", "withdrawMin": "0.00002", "withdrawMax": "0.01", "withdrawInternalMin": "0.********", "depositDust": "0.00001999", "minConfirm": "1", "unLockConfirm": "0", "sameAddress": false, "estimatedArrivalTime": "0", "busy": false}]}, "active": true, "deposit": true, "withdraw": true, "networks": {"BEP20": {"info": {"network": "BSC", "coin": "BTC", "withdrawIntegerMultiple": "0.********", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "specialWithdrawTips": "The network you have selected is BSC. Please ensure that the withdrawal address supports the BNB Smart Chain network. You will potentially lose your assets if the chosen platform does not support refunds of wrongfully deposited assets.", "name": "BNB Smart Chain (BEP20)", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "0.0000069", "withdrawMin": "0.000014", "withdrawMax": "10000000000", "withdrawInternalMin": "0.********", "depositDust": "0.00000002", "minConfirm": "15", "unLockConfirm": "0", "sameAddress": false, "estimatedArrivalTime": "2", "busy": false, "contractAddressUrl": "https://bsctrace.com/token/", "contractAddress": "******************************************"}, "id": "BSC", "network": "BEP20", "active": true, "deposit": true, "withdraw": true, "fee": 6.9e-06, "precision": 1e-08, "limits": {"withdraw": {"min": 1.4e-05, "max": 10000000000}, "deposit": {"min": 2e-08, "max": null}}}, "BTC": {"info": {"network": "BTC", "coin": "BTC", "withdrawIntegerMultiple": "0.********", "isDefault": true, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "specialWithdrawTips": "Please note that withdrawal fees have been adjusted in due of the recent increase in activities on the BTC network.", "name": "Bitcoin", "resetAddressStatus": false, "addressRegex": "^[13][a-km-zA-HJ-NP-Z1-9]{25,34}$|^[(bc1q)|(bc1p)][0-9A-Za-z]{37,62}$", "memoRegex": "", "withdrawFee": "0.00003", "withdrawMin": "0.00011", "withdrawMax": "4000", "withdrawInternalMin": "0.********", "depositDust": "0.000006", "minConfirm": "1", "unLockConfirm": "2", "sameAddress": false, "estimatedArrivalTime": "60", "busy": false}, "id": "BTC", "network": "BTC", "active": true, "deposit": true, "withdraw": true, "fee": 3e-05, "precision": 1e-08, "limits": {"withdraw": {"min": 0.00011, "max": 4000}, "deposit": {"min": 6e-06, "max": null}}}, "SEGWITBTC": {"info": {"network": "SEGWITBTC", "coin": "BTC", "withdrawIntegerMultiple": "0.********", "isDefault": false, "depositEnable": true, "withdrawEnable": false, "depositDesc": "", "withdrawDesc": "Withdrawal for this token is not supported, please try other networks.", "specialTips": "", "specialWithdrawTips": "", "name": "BTC(SegWit)", "resetAddressStatus": false, "addressRegex": "", "memoRegex": "", "withdrawFee": "0.001", "withdrawMin": "0.002", "withdrawMax": "10000000000", "withdrawInternalMin": "0.********", "depositDust": "0.000006", "minConfirm": "1", "unLockConfirm": "2", "sameAddress": false, "estimatedArrivalTime": "0", "busy": false}, "id": "SEGWITBTC", "network": "SEGWITBTC", "active": false, "deposit": true, "withdraw": false, "fee": 0.001, "precision": 1e-08, "limits": {"withdraw": {"min": 0.002, "max": 10000000000}, "deposit": {"min": 6e-06, "max": null}}}, "ERC20": {"info": {"network": "ETH", "coin": "BTC", "withdrawIntegerMultiple": "0.********", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "You are depositing Binance Wrapped BTC (BBTC). Please ensure the token contract address ends with 22541.", "specialWithdrawTips": "You are withdrawing Binance Wrapped BTC (BBTC). Please ensure that the receiving platform supports this token or you will be at risk of losing your assets.", "name": "Ethereum (ERC20)", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "0.000019", "withdrawMin": "0.000038", "withdrawMax": "10000000000", "withdrawInternalMin": "0.********", "depositDust": "0.00000002", "minConfirm": "6", "unLockConfirm": "64", "sameAddress": false, "estimatedArrivalTime": "3", "busy": false, "contractAddressUrl": "https://etherscan.io/address/", "contractAddress": "******************************************"}, "id": "ETH", "network": "ERC20", "active": true, "deposit": true, "withdraw": true, "fee": 1.9e-05, "precision": 1e-08, "limits": {"withdraw": {"min": 3.8e-05, "max": 10000000000}, "deposit": {"min": 2e-08, "max": null}}}, "LIGHTNING": {"info": {"network": "LIGHTNING", "coin": "BTC", "withdrawIntegerMultiple": "0.********", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialWithdrawTips": "Withdrawal invoices containing non-standard feature bits are currently not supported. Refer to https://github.com/lightning/bolts/blob/master/09-features.md for the list of standard feature bits.", "name": "Lightning Network", "resetAddressStatus": false, "addressRegex": "^lnbc[a-z0-9]{200,500}$", "memoRegex": "", "withdrawFee": "0.000001", "withdrawMin": "0.00002", "withdrawMax": "0.01", "withdrawInternalMin": "0.********", "depositDust": "0.00001999", "minConfirm": "1", "unLockConfirm": "0", "sameAddress": false, "estimatedArrivalTime": "0", "busy": false}, "id": "LIGHTNING", "network": "LIGHTNING", "active": true, "deposit": true, "withdraw": true, "fee": 1e-06, "precision": 1e-08, "limits": {"withdraw": {"min": 2e-05, "max": 0.01}, "deposit": {"min": 1.999e-05, "max": null}}}}, "fee": 3e-05, "fees": {"BSC": 6.9e-06, "BTC": 3e-05, "SEGWITBTC": 0.001, "ETH": 1.9e-05, "LIGHTNING": 1e-06}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": null, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": null, "max": null}}, "margin": null}, "USDT": {"id": "USDT", "name": "TetherUS", "code": "USDT", "type": "crypto", "precision": 1e-08, "info": {"coin": "USDT", "depositAllEnable": true, "withdrawAllEnable": true, "name": "TetherUS", "free": "1.28648069", "locked": "0", "freeze": "0", "withdrawing": "0", "ipoing": "0", "ipoable": "0", "storage": "0", "isLegalMoney": false, "trading": true, "networkList": [{"network": "BSC", "coin": "USDT", "withdrawIntegerMultiple": "0.********", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "specialWithdrawTips": "The network you have selected is BSC. Please ensure that the withdrawal address supports the BNB Smart Chain network. You will potentially lose your assets if the chosen platform does not support refunds of wrongfully deposited assets.", "name": "BNB Smart Chain (BEP20)", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "0", "withdrawMin": "10", "withdrawMax": "10000000000", "withdrawInternalMin": "0.01", "depositDust": "0.01", "minConfirm": "15", "unLockConfirm": "0", "sameAddress": false, "estimatedArrivalTime": "2", "busy": false, "contractAddressUrl": "https://bsctrace.com/token/", "contractAddress": "0x55d398326f99059ff775485246999027b3197955"}, {"network": "EOS", "coin": "USDT", "withdrawIntegerMultiple": "0.0001", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "Both a memo/tag and an address are required to successfully deposit your assets to Binance.", "name": "EOS", "resetAddressStatus": false, "addressRegex": "^[1-5a-z\\.]{1,12}$", "memoRegex": "^[0-9A-Za-z\\-_,]{1,120}$", "withdrawFee": "1", "withdrawMin": "10", "withdrawMax": "10000000000", "withdrawInternalMin": "0.0001", "depositDust": "0.001", "minConfirm": "1", "unLockConfirm": "0", "sameAddress": true, "estimatedArrivalTime": "2", "busy": false, "contractAddressUrl": "https://eosauthority.com/tokens/", "contractAddress": "tethertether"}, {"network": "NEAR", "coin": "USDT", "withdrawIntegerMultiple": "0.000001", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "Please make sure that the token contract address is usdt.tether-token.near, depositing a token with different contract address may result in loss of funds. Thank you for your understanding", "specialWithdrawTips": "CAUTION: Ensure the recipient address is activated and authorized to receive the token contract address; otherwise, your withdrawal will fail.", "name": "NEAR Protocol", "resetAddressStatus": false, "addressRegex": "^(?!0x)(?!bc1)(?!bnb1)[a-z0-9_-]{1}[a-z0-9_.-]{0,62}[a-z0-9_-]{1}$", "memoRegex": "", "withdrawFee": "0.2", "withdrawMin": "10", "withdrawMax": "10000000000", "withdrawInternalMin": "0.********", "depositDust": "0.001", "minConfirm": "1", "unLockConfirm": "5", "sameAddress": false, "estimatedArrivalTime": "2", "busy": false, "contractAddressUrl": "https://nearblocks.io/token/", "contractAddress": "usdt.tether-token.near"}, {"network": "AVAXC", "coin": "USDT", "withdrawIntegerMultiple": "0.000001", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "Please note that this deposit address supports C-Chain deposits. For X-Chain deposits, please use the AVAX network.", "specialWithdrawTips": "", "name": "AVAX C-Chain", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "0.16", "withdrawMin": "0.32", "withdrawMax": "450000000", "withdrawInternalMin": "0.000001", "depositDust": "0.001", "minConfirm": "12", "unLockConfirm": "0", "sameAddress": false, "estimatedArrivalTime": "2", "busy": false, "contractAddressUrl": "https://avascan.info/blockchain/c/token/", "contractAddress": "******************************************"}, {"network": "APT", "coin": "USDT", "withdrawIntegerMultiple": "0.000001", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "specialWithdrawTips": "", "name": "Aptos", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Za-z]{64}$", "memoRegex": "", "withdrawFee": "0.05", "withdrawMin": "10", "withdrawMax": "9999999", "withdrawInternalMin": "0", "depositDust": "0.000001", "minConfirm": "1", "unLockConfirm": "0", "sameAddress": false, "estimatedArrivalTime": "0", "busy": false, "contractAddressUrl": "https://explorer.aptoslabs.com/fungible_asset/", "contractAddress": "0x357b0b74bc833e95a115ad22604854d6b0fca151cecd94111770e5d6ffc9dc2b"}, {"network": "ARBITRUM", "coin": "USDT", "withdrawIntegerMultiple": "0.000001", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "specialWithdrawTips": "", "name": "Arbitrum One", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "0.13", "withdrawMin": "0.26", "withdrawMax": "********", "withdrawInternalMin": "0.********", "depositDust": "0.001", "minConfirm": "120", "unLockConfirm": "120", "sameAddress": false, "estimatedArrivalTime": "3", "busy": false, "contractAddressUrl": "https://arbiscan.io/token/", "contractAddress": "0xfd086bc7cd5c481dcc9c85ebe478a1c0b69fcbb9"}, {"network": "STATEMINT", "coin": "USDT", "withdrawIntegerMultiple": "0.000001", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "specialWithdrawTips": "", "name": "<PERSON><PERSON> (Polkadot)", "resetAddressStatus": false, "addressRegex": "^(1)[0-9a-z-A-Z]{44,50}$", "memoRegex": "", "withdrawFee": "0.7", "withdrawMin": "10", "withdrawMax": "********", "withdrawInternalMin": "0.000001", "depositDust": "0.71", "minConfirm": "3", "unLockConfirm": "0", "sameAddress": false, "estimatedArrivalTime": "2", "busy": false, "contractAddressUrl": "https://assethub-polkadot.subscan.io/assets/"}, {"network": "CELO", "coin": "USDT", "withdrawIntegerMultiple": "0.000001", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "specialWithdrawTips": "", "name": "CELO", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "0.03", "withdrawMin": "10", "withdrawMax": "********", "withdrawInternalMin": "0", "depositDust": "0.001", "minConfirm": "1", "unLockConfirm": "0", "sameAddress": false, "estimatedArrivalTime": "0", "busy": false, "contractAddressUrl": "https://celoscan.io/token/", "contractAddress": "******************************************"}, {"network": "ETH", "coin": "USDT", "withdrawIntegerMultiple": "0.000001", "isDefault": true, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "specialWithdrawTips": "", "name": "Ethereum (ERC20)", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "1.5", "withdrawMin": "18", "withdrawMax": "10000000000", "withdrawInternalMin": "0.000001", "depositDust": "0.001", "minConfirm": "6", "unLockConfirm": "64", "sameAddress": false, "estimatedArrivalTime": "3", "busy": false, "contractAddressUrl": "https://etherscan.io/address/", "contractAddress": "******************************************"}, {"network": "KAVAEVM", "coin": "USDT", "withdrawIntegerMultiple": "0.000001", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "specialWithdrawTips": "", "name": "KAVAEVM", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "0.2", "withdrawMin": "10", "withdrawMax": "********", "withdrawInternalMin": "0", "depositDust": "0.001", "minConfirm": "1", "unLockConfirm": "0", "sameAddress": false, "estimatedArrivalTime": "0", "busy": false, "contractAddressUrl": "https://kavascan.com/token/", "contractAddress": "******************************************"}, {"network": "OPTIMISM", "coin": "USDT", "withdrawIntegerMultiple": "0.000001", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "specialWithdrawTips": "", "name": "Optimism", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "0.016", "withdrawMin": "0.032", "withdrawMax": "********", "withdrawInternalMin": "0.********", "depositDust": "0.001", "minConfirm": "25", "unLockConfirm": "100", "sameAddress": false, "estimatedArrivalTime": "2", "busy": false, "contractAddressUrl": "https://optimistic.etherscan.io/token/", "contractAddress": "******************************************"}, {"network": "MATIC", "coin": "USDT", "withdrawIntegerMultiple": "0.000001", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "specialWithdrawTips": "", "name": "Polygon POS", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "0.02", "withdrawMin": "4", "withdrawMax": "1000000000", "withdrawInternalMin": "0.000001", "depositDust": "0.001", "minConfirm": "200", "unLockConfirm": "300", "sameAddress": false, "estimatedArrivalTime": "8", "busy": true, "contractAddressUrl": "https://polygonscan.com/token/", "contractAddress": "******************************************"}, {"network": "SCROLL", "coin": "USDT", "withdrawIntegerMultiple": "0.000001", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "SCROLL is a L2 network. After crediting, its corresponding L1 transaction needs to be confirmed before unlocking, which takes approximately 1-2 hours.", "specialWithdrawTips": "", "name": "<PERSON><PERSON>", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "0.1", "withdrawMin": "10", "withdrawMax": "********", "withdrawInternalMin": "0", "depositDust": "0.000001", "minConfirm": "1", "unLockConfirm": "0", "sameAddress": false, "estimatedArrivalTime": "0", "busy": false, "contractAddressUrl": "https://scrollscan.com/token/", "contractAddress": "0xf55bec9cafdbe8730f096aa55dad6d22d44099df"}, {"network": "SOL", "coin": "USDT", "withdrawIntegerMultiple": "0.000001", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "Please note that SOL addresses are case sensitive.", "specialWithdrawTips": "Please note that SOL addresses are case sensitive.", "name": "Solana", "resetAddressStatus": false, "addressRegex": "^[1-9A-HJ-NP-Za-km-z]{32,44}$", "memoRegex": "", "withdrawFee": "1", "withdrawMin": "30", "withdrawMax": "********", "withdrawInternalMin": "0.000001", "depositDust": "0.001", "minConfirm": "1", "unLockConfirm": "0", "sameAddress": false, "estimatedArrivalTime": "2", "busy": false, "contractAddressUrl": "https://explorer.solana.com/address/", "contractAddress": "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB"}, {"network": "XTZ", "coin": "USDT", "withdrawIntegerMultiple": "0.000001", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "specialWithdrawTips": "", "name": "Tezos", "resetAddressStatus": false, "addressRegex": "^(tz[1,2,3])[a-zA-Z0-9]{33}$", "memoRegex": "", "withdrawFee": "0.1", "withdrawMin": "10", "withdrawMax": "10000000000", "withdrawInternalMin": "0.000001", "depositDust": "0.001", "minConfirm": "4", "unLockConfirm": "0", "sameAddress": false, "estimatedArrivalTime": "5", "busy": false, "contractAddressUrl": "https://tzkt.io/", "contractAddress": "KT1XnTn74bUtxHfDtBmm2bGZAQfhPbvKWR8o"}, {"network": "TON", "coin": "USDT", "withdrawIntegerMultiple": "0.000001", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "Both a memo/tag and an address are required to successfully deposit your assets to Binance.", "specialWithdrawTips": "", "name": "The Open Network", "resetAddressStatus": false, "addressRegex": "^[UE][Qf][0-9a-z-A-Z\\-\\_]{46}$", "memoRegex": "^[0-9A-Za-z\\-_]{1,120}$", "withdrawFee": "0.3", "withdrawMin": "5", "withdrawMax": "********", "withdrawInternalMin": "0", "depositDust": "0.001", "minConfirm": "1", "unLockConfirm": "0", "sameAddress": true, "estimatedArrivalTime": "0", "busy": false, "contractAddressUrl": "https://tonviewer.com/", "contractAddress": "EQCxE6mUtQJKFnGfaROTKOt1lZbDiiX1kCixRv7Nw2Id_sDs"}, {"network": "TRX", "coin": "USDT", "withdrawIntegerMultiple": "0.000001", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "specialWithdrawTips": "CAUTION: Ensure the recipient address has been activated; otherwise, your withdrawal will fail.", "name": "Tron (TRC20)", "resetAddressStatus": false, "addressRegex": "^T[1-9A-HJ-NP-Za-km-z]{33}$", "memoRegex": "", "withdrawFee": "1", "withdrawMin": "10", "withdrawMax": "10000000000", "withdrawInternalMin": "0.000001", "depositDust": "0.01", "minConfirm": "1", "unLockConfirm": "0", "sameAddress": false, "estimatedArrivalTime": "2", "busy": false, "contractAddressUrl": "https://tronscan.org/#/token20/", "contractAddress": "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t"}, {"network": "OPBNB", "coin": "USDT", "withdrawIntegerMultiple": "0.********", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "specialWithdrawTips": "The network you have selected is OPBNB. Please ensure that the withdrawal address supports the OPBNB network. You will potentially lose your assets if the chosen platform does not support refund of wrongfully deposited assets.", "name": "opBNB", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "0", "withdrawMin": "20", "withdrawMax": "********", "withdrawInternalMin": "0", "depositDust": "0.001", "minConfirm": "1", "unLockConfirm": "100", "sameAddress": false, "estimatedArrivalTime": "2", "busy": false, "contractAddressUrl": "https://opbnbscan.com/token/", "contractAddress": "******************************************"}]}, "active": true, "deposit": true, "withdraw": true, "networks": {"BEP20": {"info": {"network": "BSC", "coin": "USDT", "withdrawIntegerMultiple": "0.********", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "specialWithdrawTips": "The network you have selected is BSC. Please ensure that the withdrawal address supports the BNB Smart Chain network. You will potentially lose your assets if the chosen platform does not support refunds of wrongfully deposited assets.", "name": "BNB Smart Chain (BEP20)", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "0", "withdrawMin": "10", "withdrawMax": "10000000000", "withdrawInternalMin": "0.01", "depositDust": "0.01", "minConfirm": "15", "unLockConfirm": "0", "sameAddress": false, "estimatedArrivalTime": "2", "busy": false, "contractAddressUrl": "https://bsctrace.com/token/", "contractAddress": "0x55d398326f99059ff775485246999027b3197955"}, "id": "BSC", "network": "BEP20", "active": true, "deposit": true, "withdraw": true, "fee": 0, "precision": 1e-08, "limits": {"withdraw": {"min": 10, "max": 10000000000}, "deposit": {"min": 0.01, "max": null}}}, "EOS": {"info": {"network": "EOS", "coin": "USDT", "withdrawIntegerMultiple": "0.0001", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "Both a memo/tag and an address are required to successfully deposit your assets to Binance.", "name": "EOS", "resetAddressStatus": false, "addressRegex": "^[1-5a-z\\.]{1,12}$", "memoRegex": "^[0-9A-Za-z\\-_,]{1,120}$", "withdrawFee": "1", "withdrawMin": "10", "withdrawMax": "10000000000", "withdrawInternalMin": "0.0001", "depositDust": "0.001", "minConfirm": "1", "unLockConfirm": "0", "sameAddress": true, "estimatedArrivalTime": "2", "busy": false, "contractAddressUrl": "https://eosauthority.com/tokens/", "contractAddress": "tethertether"}, "id": "EOS", "network": "EOS", "active": true, "deposit": true, "withdraw": true, "fee": 1, "precision": 0.0001, "limits": {"withdraw": {"min": 10, "max": 10000000000}, "deposit": {"min": 0.001, "max": null}}}, "NEAR": {"info": {"network": "NEAR", "coin": "USDT", "withdrawIntegerMultiple": "0.000001", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "Please make sure that the token contract address is usdt.tether-token.near, depositing a token with different contract address may result in loss of funds. Thank you for your understanding", "specialWithdrawTips": "CAUTION: Ensure the recipient address is activated and authorized to receive the token contract address; otherwise, your withdrawal will fail.", "name": "NEAR Protocol", "resetAddressStatus": false, "addressRegex": "^(?!0x)(?!bc1)(?!bnb1)[a-z0-9_-]{1}[a-z0-9_.-]{0,62}[a-z0-9_-]{1}$", "memoRegex": "", "withdrawFee": "0.2", "withdrawMin": "10", "withdrawMax": "10000000000", "withdrawInternalMin": "0.********", "depositDust": "0.001", "minConfirm": "1", "unLockConfirm": "5", "sameAddress": false, "estimatedArrivalTime": "2", "busy": false, "contractAddressUrl": "https://nearblocks.io/token/", "contractAddress": "usdt.tether-token.near"}, "id": "NEAR", "network": "NEAR", "active": true, "deposit": true, "withdraw": true, "fee": 0.2, "precision": 1e-06, "limits": {"withdraw": {"min": 10, "max": 10000000000}, "deposit": {"min": 0.001, "max": null}}}, "AVAXC": {"info": {"network": "AVAXC", "coin": "USDT", "withdrawIntegerMultiple": "0.000001", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "Please note that this deposit address supports C-Chain deposits. For X-Chain deposits, please use the AVAX network.", "specialWithdrawTips": "", "name": "AVAX C-Chain", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "0.16", "withdrawMin": "0.32", "withdrawMax": "450000000", "withdrawInternalMin": "0.000001", "depositDust": "0.001", "minConfirm": "12", "unLockConfirm": "0", "sameAddress": false, "estimatedArrivalTime": "2", "busy": false, "contractAddressUrl": "https://avascan.info/blockchain/c/token/", "contractAddress": "******************************************"}, "id": "AVAXC", "network": "AVAXC", "active": true, "deposit": true, "withdraw": true, "fee": 0.16, "precision": 1e-06, "limits": {"withdraw": {"min": 0.32, "max": 450000000}, "deposit": {"min": 0.001, "max": null}}}, "APT": {"info": {"network": "APT", "coin": "USDT", "withdrawIntegerMultiple": "0.000001", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "specialWithdrawTips": "", "name": "Aptos", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Za-z]{64}$", "memoRegex": "", "withdrawFee": "0.05", "withdrawMin": "10", "withdrawMax": "9999999", "withdrawInternalMin": "0", "depositDust": "0.000001", "minConfirm": "1", "unLockConfirm": "0", "sameAddress": false, "estimatedArrivalTime": "0", "busy": false, "contractAddressUrl": "https://explorer.aptoslabs.com/fungible_asset/", "contractAddress": "0x357b0b74bc833e95a115ad22604854d6b0fca151cecd94111770e5d6ffc9dc2b"}, "id": "APT", "network": "APT", "active": true, "deposit": true, "withdraw": true, "fee": 0.05, "precision": 1e-06, "limits": {"withdraw": {"min": 10, "max": 9999999}, "deposit": {"min": 1e-06, "max": null}}}, "ARBITRUM": {"info": {"network": "ARBITRUM", "coin": "USDT", "withdrawIntegerMultiple": "0.000001", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "specialWithdrawTips": "", "name": "Arbitrum One", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "0.13", "withdrawMin": "0.26", "withdrawMax": "********", "withdrawInternalMin": "0.********", "depositDust": "0.001", "minConfirm": "120", "unLockConfirm": "120", "sameAddress": false, "estimatedArrivalTime": "3", "busy": false, "contractAddressUrl": "https://arbiscan.io/token/", "contractAddress": "0xfd086bc7cd5c481dcc9c85ebe478a1c0b69fcbb9"}, "id": "ARBITRUM", "network": "ARBITRUM", "active": true, "deposit": true, "withdraw": true, "fee": 0.13, "precision": 1e-06, "limits": {"withdraw": {"min": 0.26, "max": ********}, "deposit": {"min": 0.001, "max": null}}}, "STATEMINT": {"info": {"network": "STATEMINT", "coin": "USDT", "withdrawIntegerMultiple": "0.000001", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "specialWithdrawTips": "", "name": "<PERSON><PERSON> (Polkadot)", "resetAddressStatus": false, "addressRegex": "^(1)[0-9a-z-A-Z]{44,50}$", "memoRegex": "", "withdrawFee": "0.7", "withdrawMin": "10", "withdrawMax": "********", "withdrawInternalMin": "0.000001", "depositDust": "0.71", "minConfirm": "3", "unLockConfirm": "0", "sameAddress": false, "estimatedArrivalTime": "2", "busy": false, "contractAddressUrl": "https://assethub-polkadot.subscan.io/assets/"}, "id": "STATEMINT", "network": "STATEMINT", "active": true, "deposit": true, "withdraw": true, "fee": 0.7, "precision": 1e-06, "limits": {"withdraw": {"min": 10, "max": ********}, "deposit": {"min": 0.71, "max": null}}}, "CELO": {"info": {"network": "CELO", "coin": "USDT", "withdrawIntegerMultiple": "0.000001", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "specialWithdrawTips": "", "name": "CELO", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "0.03", "withdrawMin": "10", "withdrawMax": "********", "withdrawInternalMin": "0", "depositDust": "0.001", "minConfirm": "1", "unLockConfirm": "0", "sameAddress": false, "estimatedArrivalTime": "0", "busy": false, "contractAddressUrl": "https://celoscan.io/token/", "contractAddress": "******************************************"}, "id": "CELO", "network": "CELO", "active": true, "deposit": true, "withdraw": true, "fee": 0.03, "precision": 1e-06, "limits": {"withdraw": {"min": 10, "max": ********}, "deposit": {"min": 0.001, "max": null}}}, "ERC20": {"info": {"network": "ETH", "coin": "USDT", "withdrawIntegerMultiple": "0.000001", "isDefault": true, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "specialWithdrawTips": "", "name": "Ethereum (ERC20)", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "1.5", "withdrawMin": "18", "withdrawMax": "10000000000", "withdrawInternalMin": "0.000001", "depositDust": "0.001", "minConfirm": "6", "unLockConfirm": "64", "sameAddress": false, "estimatedArrivalTime": "3", "busy": false, "contractAddressUrl": "https://etherscan.io/address/", "contractAddress": "******************************************"}, "id": "ETH", "network": "ERC20", "active": true, "deposit": true, "withdraw": true, "fee": 1.5, "precision": 1e-06, "limits": {"withdraw": {"min": 18, "max": 10000000000}, "deposit": {"min": 0.001, "max": null}}}, "KAVAEVM": {"info": {"network": "KAVAEVM", "coin": "USDT", "withdrawIntegerMultiple": "0.000001", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "specialWithdrawTips": "", "name": "KAVAEVM", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "0.2", "withdrawMin": "10", "withdrawMax": "********", "withdrawInternalMin": "0", "depositDust": "0.001", "minConfirm": "1", "unLockConfirm": "0", "sameAddress": false, "estimatedArrivalTime": "0", "busy": false, "contractAddressUrl": "https://kavascan.com/token/", "contractAddress": "******************************************"}, "id": "KAVAEVM", "network": "KAVAEVM", "active": true, "deposit": true, "withdraw": true, "fee": 0.2, "precision": 1e-06, "limits": {"withdraw": {"min": 10, "max": ********}, "deposit": {"min": 0.001, "max": null}}}, "OPTIMISM": {"info": {"network": "OPTIMISM", "coin": "USDT", "withdrawIntegerMultiple": "0.000001", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "specialWithdrawTips": "", "name": "Optimism", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "0.016", "withdrawMin": "0.032", "withdrawMax": "********", "withdrawInternalMin": "0.********", "depositDust": "0.001", "minConfirm": "25", "unLockConfirm": "100", "sameAddress": false, "estimatedArrivalTime": "2", "busy": false, "contractAddressUrl": "https://optimistic.etherscan.io/token/", "contractAddress": "******************************************"}, "id": "OPTIMISM", "network": "OPTIMISM", "active": true, "deposit": true, "withdraw": true, "fee": 0.016, "precision": 1e-06, "limits": {"withdraw": {"min": 0.032, "max": ********}, "deposit": {"min": 0.001, "max": null}}}, "MATIC": {"info": {"network": "MATIC", "coin": "USDT", "withdrawIntegerMultiple": "0.000001", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "specialWithdrawTips": "", "name": "Polygon POS", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "0.02", "withdrawMin": "4", "withdrawMax": "1000000000", "withdrawInternalMin": "0.000001", "depositDust": "0.001", "minConfirm": "200", "unLockConfirm": "300", "sameAddress": false, "estimatedArrivalTime": "8", "busy": true, "contractAddressUrl": "https://polygonscan.com/token/", "contractAddress": "******************************************"}, "id": "MATIC", "network": "MATIC", "active": true, "deposit": true, "withdraw": true, "fee": 0.02, "precision": 1e-06, "limits": {"withdraw": {"min": 4, "max": 1000000000}, "deposit": {"min": 0.001, "max": null}}}, "SCROLL": {"info": {"network": "SCROLL", "coin": "USDT", "withdrawIntegerMultiple": "0.000001", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "SCROLL is a L2 network. After crediting, its corresponding L1 transaction needs to be confirmed before unlocking, which takes approximately 1-2 hours.", "specialWithdrawTips": "", "name": "<PERSON><PERSON>", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "0.1", "withdrawMin": "10", "withdrawMax": "********", "withdrawInternalMin": "0", "depositDust": "0.000001", "minConfirm": "1", "unLockConfirm": "0", "sameAddress": false, "estimatedArrivalTime": "0", "busy": false, "contractAddressUrl": "https://scrollscan.com/token/", "contractAddress": "0xf55bec9cafdbe8730f096aa55dad6d22d44099df"}, "id": "SCROLL", "network": "SCROLL", "active": true, "deposit": true, "withdraw": true, "fee": 0.1, "precision": 1e-06, "limits": {"withdraw": {"min": 10, "max": ********}, "deposit": {"min": 1e-06, "max": null}}}, "SOL": {"info": {"network": "SOL", "coin": "USDT", "withdrawIntegerMultiple": "0.000001", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "Please note that SOL addresses are case sensitive.", "specialWithdrawTips": "Please note that SOL addresses are case sensitive.", "name": "Solana", "resetAddressStatus": false, "addressRegex": "^[1-9A-HJ-NP-Za-km-z]{32,44}$", "memoRegex": "", "withdrawFee": "1", "withdrawMin": "30", "withdrawMax": "********", "withdrawInternalMin": "0.000001", "depositDust": "0.001", "minConfirm": "1", "unLockConfirm": "0", "sameAddress": false, "estimatedArrivalTime": "2", "busy": false, "contractAddressUrl": "https://explorer.solana.com/address/", "contractAddress": "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB"}, "id": "SOL", "network": "SOL", "active": true, "deposit": true, "withdraw": true, "fee": 1, "precision": 1e-06, "limits": {"withdraw": {"min": 30, "max": ********}, "deposit": {"min": 0.001, "max": null}}}, "XTZ": {"info": {"network": "XTZ", "coin": "USDT", "withdrawIntegerMultiple": "0.000001", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "specialWithdrawTips": "", "name": "Tezos", "resetAddressStatus": false, "addressRegex": "^(tz[1,2,3])[a-zA-Z0-9]{33}$", "memoRegex": "", "withdrawFee": "0.1", "withdrawMin": "10", "withdrawMax": "10000000000", "withdrawInternalMin": "0.000001", "depositDust": "0.001", "minConfirm": "4", "unLockConfirm": "0", "sameAddress": false, "estimatedArrivalTime": "5", "busy": false, "contractAddressUrl": "https://tzkt.io/", "contractAddress": "KT1XnTn74bUtxHfDtBmm2bGZAQfhPbvKWR8o"}, "id": "XTZ", "network": "XTZ", "active": true, "deposit": true, "withdraw": true, "fee": 0.1, "precision": 1e-06, "limits": {"withdraw": {"min": 10, "max": 10000000000}, "deposit": {"min": 0.001, "max": null}}}, "TON": {"info": {"network": "TON", "coin": "USDT", "withdrawIntegerMultiple": "0.000001", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "Both a memo/tag and an address are required to successfully deposit your assets to Binance.", "specialWithdrawTips": "", "name": "The Open Network", "resetAddressStatus": false, "addressRegex": "^[UE][Qf][0-9a-z-A-Z\\-\\_]{46}$", "memoRegex": "^[0-9A-Za-z\\-_]{1,120}$", "withdrawFee": "0.3", "withdrawMin": "5", "withdrawMax": "********", "withdrawInternalMin": "0", "depositDust": "0.001", "minConfirm": "1", "unLockConfirm": "0", "sameAddress": true, "estimatedArrivalTime": "0", "busy": false, "contractAddressUrl": "https://tonviewer.com/", "contractAddress": "EQCxE6mUtQJKFnGfaROTKOt1lZbDiiX1kCixRv7Nw2Id_sDs"}, "id": "TON", "network": "TON", "active": true, "deposit": true, "withdraw": true, "fee": 0.3, "precision": 1e-06, "limits": {"withdraw": {"min": 5, "max": ********}, "deposit": {"min": 0.001, "max": null}}}, "TRC20": {"info": {"network": "TRX", "coin": "USDT", "withdrawIntegerMultiple": "0.000001", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "specialWithdrawTips": "CAUTION: Ensure the recipient address has been activated; otherwise, your withdrawal will fail.", "name": "Tron (TRC20)", "resetAddressStatus": false, "addressRegex": "^T[1-9A-HJ-NP-Za-km-z]{33}$", "memoRegex": "", "withdrawFee": "1", "withdrawMin": "10", "withdrawMax": "10000000000", "withdrawInternalMin": "0.000001", "depositDust": "0.01", "minConfirm": "1", "unLockConfirm": "0", "sameAddress": false, "estimatedArrivalTime": "2", "busy": false, "contractAddressUrl": "https://tronscan.org/#/token20/", "contractAddress": "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t"}, "id": "TRX", "network": "TRC20", "active": true, "deposit": true, "withdraw": true, "fee": 1, "precision": 1e-06, "limits": {"withdraw": {"min": 10, "max": 10000000000}, "deposit": {"min": 0.01, "max": null}}}, "OPBNB": {"info": {"network": "OPBNB", "coin": "USDT", "withdrawIntegerMultiple": "0.********", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "specialWithdrawTips": "The network you have selected is OPBNB. Please ensure that the withdrawal address supports the OPBNB network. You will potentially lose your assets if the chosen platform does not support refund of wrongfully deposited assets.", "name": "opBNB", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "0", "withdrawMin": "20", "withdrawMax": "********", "withdrawInternalMin": "0", "depositDust": "0.001", "minConfirm": "1", "unLockConfirm": "100", "sameAddress": false, "estimatedArrivalTime": "2", "busy": false, "contractAddressUrl": "https://opbnbscan.com/token/", "contractAddress": "******************************************"}, "id": "OPBNB", "network": "OPBNB", "active": true, "deposit": true, "withdraw": true, "fee": 0, "precision": 1e-08, "limits": {"withdraw": {"min": 20, "max": ********}, "deposit": {"min": 0.001, "max": null}}}}, "fee": 1.5, "fees": {"BSC": 0, "EOS": 1, "NEAR": 0.2, "AVAXC": 0.16, "APT": 0.05, "ARBITRUM": 0.13, "STATEMINT": 0.7, "CELO": 0.03, "ETH": 1.5, "KAVAEVM": 0.2, "OPTIMISM": 0.016, "MATIC": 0.02, "SCROLL": 0.1, "SOL": 1, "XTZ": 0.1, "TON": 0.3, "TRX": 1, "OPBNB": 0}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": null, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": null, "max": null}}, "margin": null}, "EUR": {"id": "EUR", "name": "Euro", "code": "EUR", "type": "fiat", "precision": 1e-08, "info": {"coin": "EUR", "depositAllEnable": true, "withdrawAllEnable": true, "name": "Euro", "free": "0", "locked": "0", "freeze": "0", "withdrawing": "0", "ipoing": "0", "ipoable": "0", "storage": "0", "isLegalMoney": true, "trading": true, "networkList": [{"network": "FIAT_MONEY", "coin": "EUR", "withdrawIntegerMultiple": "0.********", "isDefault": true, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "name": "FIAT", "resetAddressStatus": false, "addressRegex": "", "memoRegex": "", "withdrawFee": "0", "withdrawMin": "0", "withdrawMax": "9999999999.99999999", "withdrawInternalMin": "0.********", "minConfirm": "0", "unLockConfirm": "0", "sameAddress": false, "estimatedArrivalTime": "1", "busy": false}]}, "active": true, "deposit": true, "withdraw": true, "networks": {"FIAT_MONEY": {"info": {"network": "FIAT_MONEY", "coin": "EUR", "withdrawIntegerMultiple": "0.********", "isDefault": true, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "name": "FIAT", "resetAddressStatus": false, "addressRegex": "", "memoRegex": "", "withdrawFee": "0", "withdrawMin": "0", "withdrawMax": "9999999999.99999999", "withdrawInternalMin": "0.********", "minConfirm": "0", "unLockConfirm": "0", "sameAddress": false, "estimatedArrivalTime": "1", "busy": false}, "id": "FIAT_MONEY", "network": "FIAT_MONEY", "active": true, "deposit": true, "withdraw": true, "fee": 0, "precision": 1e-08, "limits": {"withdraw": {"min": 0, "max": 10000000000}, "deposit": {"min": null, "max": null}}}}, "fee": 0, "fees": {"FIAT_MONEY": 0}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": null, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": null, "max": null}}, "margin": null}, "LTC": {"id": "LTC", "name": "Litecoin", "code": "LTC", "type": "crypto", "precision": 1e-08, "info": {"coin": "LTC", "depositAllEnable": true, "withdrawAllEnable": true, "name": "Litecoin", "free": "0", "locked": "0", "freeze": "0", "withdrawing": "0", "ipoing": "0", "ipoable": "0", "storage": "0", "isLegalMoney": false, "trading": true, "networkList": [{"network": "BSC", "coin": "LTC", "withdrawIntegerMultiple": "0.********", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "specialWithdrawTips": "The network you have selected is BSC. Please ensure that the withdrawal address supports the BNB Smart Chain network. You will potentially lose your assets if the chosen platform does not support refunds of wrongfully deposited assets.", "name": "BNB Smart Chain (BEP20)", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "0.0075", "withdrawMin": "0.015", "withdrawMax": "10000000000", "withdrawInternalMin": "0.********", "depositDust": "0.000015", "minConfirm": "15", "unLockConfirm": "0", "sameAddress": false, "estimatedArrivalTime": "2", "busy": false, "contractAddressUrl": "https://bsctrace.com/token/", "contractAddress": "0x4338665cbb7b2485a8855a139b75d5e34ab0db94"}, {"network": "LTC", "coin": "LTC", "withdrawIntegerMultiple": "0.********", "isDefault": true, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "Please note that confidential deposits via the MWEB function are not supported and will result in deposits being lost.", "specialWithdrawTips": "", "name": "Litecoin", "resetAddressStatus": false, "addressRegex": "^(L|M)[A-Za-z0-9]{33}$|^(ltc1)[0-9A-Za-z]{39}$", "memoRegex": "", "withdrawFee": "0.0001", "withdrawMin": "0.002", "withdrawMax": "10000000000", "withdrawInternalMin": "0.********", "depositDust": "0.001", "minConfirm": "3", "unLockConfirm": "4", "sameAddress": false, "estimatedArrivalTime": "20", "busy": false}]}, "active": true, "deposit": true, "withdraw": true, "networks": {"BEP20": {"info": {"network": "BSC", "coin": "LTC", "withdrawIntegerMultiple": "0.********", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "specialWithdrawTips": "The network you have selected is BSC. Please ensure that the withdrawal address supports the BNB Smart Chain network. You will potentially lose your assets if the chosen platform does not support refunds of wrongfully deposited assets.", "name": "BNB Smart Chain (BEP20)", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "0.0075", "withdrawMin": "0.015", "withdrawMax": "10000000000", "withdrawInternalMin": "0.********", "depositDust": "0.000015", "minConfirm": "15", "unLockConfirm": "0", "sameAddress": false, "estimatedArrivalTime": "2", "busy": false, "contractAddressUrl": "https://bsctrace.com/token/", "contractAddress": "0x4338665cbb7b2485a8855a139b75d5e34ab0db94"}, "id": "BSC", "network": "BEP20", "active": true, "deposit": true, "withdraw": true, "fee": 0.0075, "precision": 1e-08, "limits": {"withdraw": {"min": 0.015, "max": 10000000000}, "deposit": {"min": 1.5e-05, "max": null}}}, "LTC": {"info": {"network": "LTC", "coin": "LTC", "withdrawIntegerMultiple": "0.********", "isDefault": true, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "Please note that confidential deposits via the MWEB function are not supported and will result in deposits being lost.", "specialWithdrawTips": "", "name": "Litecoin", "resetAddressStatus": false, "addressRegex": "^(L|M)[A-Za-z0-9]{33}$|^(ltc1)[0-9A-Za-z]{39}$", "memoRegex": "", "withdrawFee": "0.0001", "withdrawMin": "0.002", "withdrawMax": "10000000000", "withdrawInternalMin": "0.********", "depositDust": "0.001", "minConfirm": "3", "unLockConfirm": "4", "sameAddress": false, "estimatedArrivalTime": "20", "busy": false}, "id": "LTC", "network": "LTC", "active": true, "deposit": true, "withdraw": true, "fee": 0.0001, "precision": 1e-08, "limits": {"withdraw": {"min": 0.002, "max": 10000000000}, "deposit": {"min": 0.001, "max": null}}}}, "fee": 0.0001, "fees": {"BSC": 0.0075, "LTC": 0.0001}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": null, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": null, "max": null}}, "margin": null}, "ETH": {"id": "ETH", "name": "Ethereum", "code": "ETH", "type": "crypto", "precision": 1e-08, "info": {"coin": "ETH", "depositAllEnable": true, "withdrawAllEnable": true, "name": "Ethereum", "free": "0", "locked": "0", "freeze": "0", "withdrawing": "0", "ipoing": "0", "ipoable": "0", "storage": "0", "isLegalMoney": false, "trading": true, "networkList": [{"network": "BSC", "coin": "ETH", "withdrawIntegerMultiple": "0.********", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "specialWithdrawTips": "The network you have selected is BSC. Please ensure that the withdrawal address supports the BNB Smart Chain network. You will potentially lose your assets if the chosen platform does not support refunds of wrongfully deposited assets.", "name": "BNB Smart Chain (BEP20)", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "0.00036", "withdrawMin": "0.00072", "withdrawMax": "10000000000", "withdrawInternalMin": "0.********", "depositDust": "0.0000003", "minConfirm": "15", "unLockConfirm": "0", "sameAddress": false, "estimatedArrivalTime": "2", "busy": false, "contractAddressUrl": "https://bsctrace.com/token/", "contractAddress": "******************************************"}, {"network": "ETH", "coin": "ETH", "withdrawIntegerMultiple": "0.********", "isDefault": true, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "DO NOT define your Binance deposit address as the receiving address for validator rewards. Validator rewards sent from the node to Binance deposit address is not supported and will not be credited. This will lead to asset loss.", "specialWithdrawTips": "", "name": "Ethereum (ERC20)", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "0.0006", "withdrawMin": "0.0029", "withdrawMax": "10000000000", "withdrawInternalMin": "0.********", "depositDust": "0.0000003", "minConfirm": "6", "unLockConfirm": "64", "sameAddress": false, "estimatedArrivalTime": "3", "busy": false, "contractAddressUrl": "https://etherscan.io/address/"}, {"network": "ARBITRUM", "coin": "ETH", "withdrawIntegerMultiple": "0.********", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "specialWithdrawTips": "", "name": "Arbitrum One", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "0.00004", "withdrawMin": "0.0003", "withdrawMax": "9999999", "withdrawInternalMin": "0.********", "depositDust": "0.0000003", "minConfirm": "120", "unLockConfirm": "120", "sameAddress": false, "estimatedArrivalTime": "3", "busy": false, "contractAddressUrl": "https://arbiscan.io/token/"}, {"network": "BASE", "coin": "ETH", "withdrawIntegerMultiple": "0.000001", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "specialWithdrawTips": "", "name": "Base", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "0.00004", "withdrawMin": "0.001", "withdrawMax": "9999999", "withdrawInternalMin": "0", "depositDust": "0.0000003", "minConfirm": "1", "unLockConfirm": "100", "sameAddress": false, "estimatedArrivalTime": "0", "busy": true, "contractAddressUrl": "https://basescan.org/token/"}, {"network": "MANTA", "coin": "ETH", "withdrawIntegerMultiple": "0.********", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "specialWithdrawTips": "", "name": "Manta Network", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "0.00003", "withdrawMin": "0.0003", "withdrawMax": "9999999", "withdrawInternalMin": "0", "depositDust": "0.0000003", "minConfirm": "1", "unLockConfirm": "0", "sameAddress": false, "estimatedArrivalTime": "0", "busy": false, "contractAddressUrl": "https://pacific-explorer.manta.network/token/"}, {"network": "OPTIMISM", "coin": "ETH", "withdrawIntegerMultiple": "0.********", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "specialWithdrawTips": "", "name": "Optimism", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "0.00001", "withdrawMin": "0.005", "withdrawMax": "1000000000", "withdrawInternalMin": "0.********", "depositDust": "0.0000003", "minConfirm": "25", "unLockConfirm": "100", "sameAddress": false, "estimatedArrivalTime": "2", "busy": false, "contractAddressUrl": "https://optimistic.etherscan.io/token/"}, {"network": "SCROLL", "coin": "ETH", "withdrawIntegerMultiple": "0.********", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "SCROLL is a L2 network. After crediting, its corresponding L1 transaction needs to be confirmed before unlocking, which takes approximately 1-2 hours.", "specialWithdrawTips": "", "name": "<PERSON><PERSON>", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "0.0001", "withdrawMin": "0.001", "withdrawMax": "9999999", "withdrawInternalMin": "0", "depositDust": "0.********", "minConfirm": "1", "unLockConfirm": "0", "sameAddress": false, "estimatedArrivalTime": "0", "busy": false, "contractAddressUrl": "https://scrollscan.com/token/"}, {"network": "STARKNET", "coin": "ETH", "withdrawIntegerMultiple": "0.********", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "specialWithdrawTips": "", "name": "Stark<PERSON>", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{60,64}$", "memoRegex": "", "withdrawFee": "0.0001", "withdrawMin": "0.002", "withdrawMax": "9999999", "withdrawInternalMin": "0", "depositDust": "0.0000003", "minConfirm": "1", "unLockConfirm": "0", "sameAddress": false, "estimatedArrivalTime": "0", "busy": false, "contractAddressUrl": "https://starkscan.co/token/", "contractAddress": "0x49d36570d4e46f48e99674bd3fcc84644ddd6b96f7c741b1562b82f9e004dc7"}, {"network": "ZKSYNCERA", "coin": "ETH", "withdrawIntegerMultiple": "0.********", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "ZKSYNCERA is a L2 network. After crediting, its corresponding L1 transaction needs to be confirmed before unlocking, which takes approximately 24 hours.", "specialWithdrawTips": "", "name": "zkSync Era", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "0.00004", "withdrawMin": "0.01", "withdrawMax": "9999999", "withdrawInternalMin": "0", "depositDust": "0.0000003", "minConfirm": "1", "unLockConfirm": "0", "sameAddress": false, "estimatedArrivalTime": "0", "busy": false, "contractAddressUrl": "https://explorer.zksync.io/address/", "contractAddress": "******************************************"}]}, "active": true, "deposit": true, "withdraw": true, "networks": {"BEP20": {"info": {"network": "BSC", "coin": "ETH", "withdrawIntegerMultiple": "0.********", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "specialWithdrawTips": "The network you have selected is BSC. Please ensure that the withdrawal address supports the BNB Smart Chain network. You will potentially lose your assets if the chosen platform does not support refunds of wrongfully deposited assets.", "name": "BNB Smart Chain (BEP20)", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "0.00036", "withdrawMin": "0.00072", "withdrawMax": "10000000000", "withdrawInternalMin": "0.********", "depositDust": "0.0000003", "minConfirm": "15", "unLockConfirm": "0", "sameAddress": false, "estimatedArrivalTime": "2", "busy": false, "contractAddressUrl": "https://bsctrace.com/token/", "contractAddress": "******************************************"}, "id": "BSC", "network": "BEP20", "active": true, "deposit": true, "withdraw": true, "fee": 0.00036, "precision": 1e-08, "limits": {"withdraw": {"min": 0.00072, "max": 10000000000}, "deposit": {"min": 3e-07, "max": null}}}, "ERC20": {"info": {"network": "ETH", "coin": "ETH", "withdrawIntegerMultiple": "0.********", "isDefault": true, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "DO NOT define your Binance deposit address as the receiving address for validator rewards. Validator rewards sent from the node to Binance deposit address is not supported and will not be credited. This will lead to asset loss.", "specialWithdrawTips": "", "name": "Ethereum (ERC20)", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "0.0006", "withdrawMin": "0.0029", "withdrawMax": "10000000000", "withdrawInternalMin": "0.********", "depositDust": "0.0000003", "minConfirm": "6", "unLockConfirm": "64", "sameAddress": false, "estimatedArrivalTime": "3", "busy": false, "contractAddressUrl": "https://etherscan.io/address/"}, "id": "ETH", "network": "ERC20", "active": true, "deposit": true, "withdraw": true, "fee": 0.0006, "precision": 1e-08, "limits": {"withdraw": {"min": 0.0029, "max": 10000000000}, "deposit": {"min": 3e-07, "max": null}}}, "ARBITRUM": {"info": {"network": "ARBITRUM", "coin": "ETH", "withdrawIntegerMultiple": "0.********", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "specialWithdrawTips": "", "name": "Arbitrum One", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "0.00004", "withdrawMin": "0.0003", "withdrawMax": "9999999", "withdrawInternalMin": "0.********", "depositDust": "0.0000003", "minConfirm": "120", "unLockConfirm": "120", "sameAddress": false, "estimatedArrivalTime": "3", "busy": false, "contractAddressUrl": "https://arbiscan.io/token/"}, "id": "ARBITRUM", "network": "ARBITRUM", "active": true, "deposit": true, "withdraw": true, "fee": 4e-05, "precision": 1e-08, "limits": {"withdraw": {"min": 0.0003, "max": 9999999}, "deposit": {"min": 3e-07, "max": null}}}, "BASE": {"info": {"network": "BASE", "coin": "ETH", "withdrawIntegerMultiple": "0.000001", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "specialWithdrawTips": "", "name": "Base", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "0.00004", "withdrawMin": "0.001", "withdrawMax": "9999999", "withdrawInternalMin": "0", "depositDust": "0.0000003", "minConfirm": "1", "unLockConfirm": "100", "sameAddress": false, "estimatedArrivalTime": "0", "busy": true, "contractAddressUrl": "https://basescan.org/token/"}, "id": "BASE", "network": "BASE", "active": true, "deposit": true, "withdraw": true, "fee": 4e-05, "precision": 1e-06, "limits": {"withdraw": {"min": 0.001, "max": 9999999}, "deposit": {"min": 3e-07, "max": null}}}, "MANTA": {"info": {"network": "MANTA", "coin": "ETH", "withdrawIntegerMultiple": "0.********", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "specialWithdrawTips": "", "name": "Manta Network", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "0.00003", "withdrawMin": "0.0003", "withdrawMax": "9999999", "withdrawInternalMin": "0", "depositDust": "0.0000003", "minConfirm": "1", "unLockConfirm": "0", "sameAddress": false, "estimatedArrivalTime": "0", "busy": false, "contractAddressUrl": "https://pacific-explorer.manta.network/token/"}, "id": "MANTA", "network": "MANTA", "active": true, "deposit": true, "withdraw": true, "fee": 3e-05, "precision": 1e-08, "limits": {"withdraw": {"min": 0.0003, "max": 9999999}, "deposit": {"min": 3e-07, "max": null}}}, "OPTIMISM": {"info": {"network": "OPTIMISM", "coin": "ETH", "withdrawIntegerMultiple": "0.********", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "specialWithdrawTips": "", "name": "Optimism", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "0.00001", "withdrawMin": "0.005", "withdrawMax": "1000000000", "withdrawInternalMin": "0.********", "depositDust": "0.0000003", "minConfirm": "25", "unLockConfirm": "100", "sameAddress": false, "estimatedArrivalTime": "2", "busy": false, "contractAddressUrl": "https://optimistic.etherscan.io/token/"}, "id": "OPTIMISM", "network": "OPTIMISM", "active": true, "deposit": true, "withdraw": true, "fee": 1e-05, "precision": 1e-08, "limits": {"withdraw": {"min": 0.005, "max": 1000000000}, "deposit": {"min": 3e-07, "max": null}}}, "SCROLL": {"info": {"network": "SCROLL", "coin": "ETH", "withdrawIntegerMultiple": "0.********", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "SCROLL is a L2 network. After crediting, its corresponding L1 transaction needs to be confirmed before unlocking, which takes approximately 1-2 hours.", "specialWithdrawTips": "", "name": "<PERSON><PERSON>", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "0.0001", "withdrawMin": "0.001", "withdrawMax": "9999999", "withdrawInternalMin": "0", "depositDust": "0.********", "minConfirm": "1", "unLockConfirm": "0", "sameAddress": false, "estimatedArrivalTime": "0", "busy": false, "contractAddressUrl": "https://scrollscan.com/token/"}, "id": "SCROLL", "network": "SCROLL", "active": true, "deposit": true, "withdraw": true, "fee": 0.0001, "precision": 1e-08, "limits": {"withdraw": {"min": 0.001, "max": 9999999}, "deposit": {"min": 1e-08, "max": null}}}, "STARKNET": {"info": {"network": "STARKNET", "coin": "ETH", "withdrawIntegerMultiple": "0.********", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "specialWithdrawTips": "", "name": "Stark<PERSON>", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{60,64}$", "memoRegex": "", "withdrawFee": "0.0001", "withdrawMin": "0.002", "withdrawMax": "9999999", "withdrawInternalMin": "0", "depositDust": "0.0000003", "minConfirm": "1", "unLockConfirm": "0", "sameAddress": false, "estimatedArrivalTime": "0", "busy": false, "contractAddressUrl": "https://starkscan.co/token/", "contractAddress": "0x49d36570d4e46f48e99674bd3fcc84644ddd6b96f7c741b1562b82f9e004dc7"}, "id": "STARKNET", "network": "STARKNET", "active": true, "deposit": true, "withdraw": true, "fee": 0.0001, "precision": 1e-08, "limits": {"withdraw": {"min": 0.002, "max": 9999999}, "deposit": {"min": 3e-07, "max": null}}}, "ZKSYNCERA": {"info": {"network": "ZKSYNCERA", "coin": "ETH", "withdrawIntegerMultiple": "0.********", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "ZKSYNCERA is a L2 network. After crediting, its corresponding L1 transaction needs to be confirmed before unlocking, which takes approximately 24 hours.", "specialWithdrawTips": "", "name": "zkSync Era", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "0.00004", "withdrawMin": "0.01", "withdrawMax": "9999999", "withdrawInternalMin": "0", "depositDust": "0.0000003", "minConfirm": "1", "unLockConfirm": "0", "sameAddress": false, "estimatedArrivalTime": "0", "busy": false, "contractAddressUrl": "https://explorer.zksync.io/address/", "contractAddress": "******************************************"}, "id": "ZKSYNCERA", "network": "ZKSYNCERA", "active": true, "deposit": true, "withdraw": true, "fee": 4e-05, "precision": 1e-08, "limits": {"withdraw": {"min": 0.01, "max": 9999999}, "deposit": {"min": 3e-07, "max": null}}}}, "fee": 0.0006, "fees": {"BSC": 0.00036, "ETH": 0.0006, "ARBITRUM": 4e-05, "BASE": 4e-05, "MANTA": 3e-05, "OPTIMISM": 1e-05, "SCROLL": 0.0001, "STARKNET": 0.0001, "ZKSYNCERA": 4e-05}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": null, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": null, "max": null}}, "margin": null}, "ADA": {"id": "ADA", "name": "Cardano", "code": "ADA", "type": "crypto", "precision": 1e-08, "info": {"coin": "ADA", "depositAllEnable": true, "withdrawAllEnable": true, "name": "Cardano", "free": "0", "locked": "0", "freeze": "0", "withdrawing": "0", "ipoing": "0", "ipoable": "0", "storage": "0", "isLegalMoney": false, "trading": true, "networkList": [{"network": "BSC", "coin": "ADA", "withdrawIntegerMultiple": "0.********", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "specialWithdrawTips": "The network you have selected is BSC. Please ensure that the withdrawal address supports the BNB Smart Chain network. You will potentially lose your assets if the chosen platform does not support refunds of wrongfully deposited assets.", "name": "BNB Smart Chain (BEP20)", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "0.91", "withdrawMin": "1.82", "withdrawMax": "10000000000", "withdrawInternalMin": "0.********", "depositDust": "0.0022", "minConfirm": "15", "unLockConfirm": "0", "sameAddress": false, "estimatedArrivalTime": "2", "busy": false, "contractAddressUrl": "https://bsctrace.com/token/", "contractAddress": "0x3ee2200efb3400fabb9aacf31297cbdd1d435d47"}, {"network": "ADA", "coin": "ADA", "withdrawIntegerMultiple": "0.000001", "isDefault": true, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "name": "Cardano", "resetAddressStatus": false, "addressRegex": "^(([0-9A-Za-z]{57,59})|([0-9A-Za-z]{100,104}))$", "memoRegex": "", "withdrawFee": "0.8", "withdrawMin": "2", "withdrawMax": "50000000", "withdrawInternalMin": "0.000001", "depositDust": "0.0022", "minConfirm": "30", "unLockConfirm": "0", "sameAddress": false, "estimatedArrivalTime": "0", "busy": false}]}, "active": true, "deposit": true, "withdraw": true, "networks": {"BEP20": {"info": {"network": "BSC", "coin": "ADA", "withdrawIntegerMultiple": "0.********", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "specialWithdrawTips": "The network you have selected is BSC. Please ensure that the withdrawal address supports the BNB Smart Chain network. You will potentially lose your assets if the chosen platform does not support refunds of wrongfully deposited assets.", "name": "BNB Smart Chain (BEP20)", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "0.91", "withdrawMin": "1.82", "withdrawMax": "10000000000", "withdrawInternalMin": "0.********", "depositDust": "0.0022", "minConfirm": "15", "unLockConfirm": "0", "sameAddress": false, "estimatedArrivalTime": "2", "busy": false, "contractAddressUrl": "https://bsctrace.com/token/", "contractAddress": "0x3ee2200efb3400fabb9aacf31297cbdd1d435d47"}, "id": "BSC", "network": "BEP20", "active": true, "deposit": true, "withdraw": true, "fee": 0.91, "precision": 1e-08, "limits": {"withdraw": {"min": 1.82, "max": 10000000000}, "deposit": {"min": 0.0022, "max": null}}}, "ADA": {"info": {"network": "ADA", "coin": "ADA", "withdrawIntegerMultiple": "0.000001", "isDefault": true, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "name": "Cardano", "resetAddressStatus": false, "addressRegex": "^(([0-9A-Za-z]{57,59})|([0-9A-Za-z]{100,104}))$", "memoRegex": "", "withdrawFee": "0.8", "withdrawMin": "2", "withdrawMax": "50000000", "withdrawInternalMin": "0.000001", "depositDust": "0.0022", "minConfirm": "30", "unLockConfirm": "0", "sameAddress": false, "estimatedArrivalTime": "0", "busy": false}, "id": "ADA", "network": "ADA", "active": true, "deposit": true, "withdraw": true, "fee": 0.8, "precision": 1e-06, "limits": {"withdraw": {"min": 2, "max": 50000000}, "deposit": {"min": 0.0022, "max": null}}}}, "fee": 0.8, "fees": {"BSC": 0.91, "ADA": 0.8}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": null, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": null, "max": null}}, "margin": null}, "XRP": {"id": "XRP", "name": "XRP", "code": "XRP", "type": "crypto", "precision": 1e-08, "info": {"coin": "XRP", "depositAllEnable": true, "withdrawAllEnable": true, "name": "XRP", "free": "0", "locked": "0", "freeze": "0", "withdrawing": "0", "ipoing": "0", "ipoable": "0", "storage": "0", "isLegalMoney": false, "trading": true, "networkList": [{"network": "BSC", "coin": "XRP", "withdrawIntegerMultiple": "0.********", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "specialWithdrawTips": "The network you have selected is BSC. Please ensure that the withdrawal address supports the BNB Smart Chain network. You will potentially lose your assets if the chosen platform does not support refunds of wrongfully deposited assets.", "name": "BNB Smart Chain (BEP20)", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "0.27", "withdrawMin": "0.54", "withdrawMax": "10000000000", "withdrawInternalMin": "0.********", "depositDust": "0.002", "minConfirm": "15", "unLockConfirm": "0", "sameAddress": false, "estimatedArrivalTime": "2", "busy": false, "contractAddressUrl": "https://bsctrace.com/token/", "contractAddress": "0x1d2f0da169ceb9fc7b3144628db156f3f6c60dbe"}, {"network": "XRP", "coin": "XRP", "withdrawIntegerMultiple": "0.000001", "isDefault": true, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "Both a memo/tag and an address are required to successfully deposit your assets to Binance.", "specialWithdrawTips": "CAUTION: Tag must be filled in when withdrawing to other exchanges; otherwise, your withdrawal will fail.", "name": "XRP Ledger", "resetAddressStatus": false, "addressRegex": "^r[1-9A-HJ-NP-Za-km-z]{25,34}$", "memoRegex": "^((?!0)[0-9]{1,10})$", "withdrawFee": "0.2", "withdrawMin": "2", "withdrawMax": "10000000000", "withdrawInternalMin": "0.000001", "depositDust": "0.001", "minConfirm": "1", "unLockConfirm": "0", "sameAddress": true, "estimatedArrivalTime": "5", "busy": false, "contractAddressUrl": "https://bithomp.com/explorer/"}, {"network": "ETH", "coin": "XRP", "withdrawIntegerMultiple": "0.********", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "Please make sure the XRP (ticker name: wXRP) you are depositing ends with the contract address 2e1b9.", "specialWithdrawTips": "The asset you are withdrawing is wXRP with contract ending 2e1B9. For more information, please refer to: https://medium.com/wrapped/wrapped-xrp-is-launching-on-ethereum-bcf0614e51d4", "name": "Ethereum (ERC20)", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "0.76", "withdrawMin": "1.52", "withdrawMax": "9999999", "withdrawInternalMin": "0", "depositDust": "0.002", "minConfirm": "6", "unLockConfirm": "64", "sameAddress": false, "estimatedArrivalTime": "3", "busy": false, "contractAddressUrl": "https://etherscan.io/address/", "contractAddress": "******************************************"}]}, "active": true, "deposit": true, "withdraw": true, "networks": {"BEP20": {"info": {"network": "BSC", "coin": "XRP", "withdrawIntegerMultiple": "0.********", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "specialWithdrawTips": "The network you have selected is BSC. Please ensure that the withdrawal address supports the BNB Smart Chain network. You will potentially lose your assets if the chosen platform does not support refunds of wrongfully deposited assets.", "name": "BNB Smart Chain (BEP20)", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "0.27", "withdrawMin": "0.54", "withdrawMax": "10000000000", "withdrawInternalMin": "0.********", "depositDust": "0.002", "minConfirm": "15", "unLockConfirm": "0", "sameAddress": false, "estimatedArrivalTime": "2", "busy": false, "contractAddressUrl": "https://bsctrace.com/token/", "contractAddress": "0x1d2f0da169ceb9fc7b3144628db156f3f6c60dbe"}, "id": "BSC", "network": "BEP20", "active": true, "deposit": true, "withdraw": true, "fee": 0.27, "precision": 1e-08, "limits": {"withdraw": {"min": 0.54, "max": 10000000000}, "deposit": {"min": 0.002, "max": null}}}, "XRP": {"info": {"network": "XRP", "coin": "XRP", "withdrawIntegerMultiple": "0.000001", "isDefault": true, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "Both a memo/tag and an address are required to successfully deposit your assets to Binance.", "specialWithdrawTips": "CAUTION: Tag must be filled in when withdrawing to other exchanges; otherwise, your withdrawal will fail.", "name": "XRP Ledger", "resetAddressStatus": false, "addressRegex": "^r[1-9A-HJ-NP-Za-km-z]{25,34}$", "memoRegex": "^((?!0)[0-9]{1,10})$", "withdrawFee": "0.2", "withdrawMin": "2", "withdrawMax": "10000000000", "withdrawInternalMin": "0.000001", "depositDust": "0.001", "minConfirm": "1", "unLockConfirm": "0", "sameAddress": true, "estimatedArrivalTime": "5", "busy": false, "contractAddressUrl": "https://bithomp.com/explorer/"}, "id": "XRP", "network": "XRP", "active": true, "deposit": true, "withdraw": true, "fee": 0.2, "precision": 1e-06, "limits": {"withdraw": {"min": 2, "max": 10000000000}, "deposit": {"min": 0.001, "max": null}}}, "ERC20": {"info": {"network": "ETH", "coin": "XRP", "withdrawIntegerMultiple": "0.********", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "Please make sure the XRP (ticker name: wXRP) you are depositing ends with the contract address 2e1b9.", "specialWithdrawTips": "The asset you are withdrawing is wXRP with contract ending 2e1B9. For more information, please refer to: https://medium.com/wrapped/wrapped-xrp-is-launching-on-ethereum-bcf0614e51d4", "name": "Ethereum (ERC20)", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "0.76", "withdrawMin": "1.52", "withdrawMax": "9999999", "withdrawInternalMin": "0", "depositDust": "0.002", "minConfirm": "6", "unLockConfirm": "64", "sameAddress": false, "estimatedArrivalTime": "3", "busy": false, "contractAddressUrl": "https://etherscan.io/address/", "contractAddress": "******************************************"}, "id": "ETH", "network": "ERC20", "active": true, "deposit": true, "withdraw": true, "fee": 0.76, "precision": 1e-08, "limits": {"withdraw": {"min": 1.52, "max": 9999999}, "deposit": {"min": 0.002, "max": null}}}}, "fee": 0.2, "fees": {"BSC": 0.27, "XRP": 0.2, "ETH": 0.76}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": null, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": null, "max": null}}, "margin": null}, "USDC": {"id": "USDC", "name": "USDC", "code": "USDC", "type": "crypto", "precision": 1e-08, "info": {"coin": "USDC", "depositAllEnable": true, "withdrawAllEnable": true, "name": "USDC", "free": "51.12", "locked": "0", "freeze": "0", "withdrawing": "0", "ipoing": "0", "ipoable": "0", "storage": "0", "isLegalMoney": false, "trading": true, "networkList": [{"network": "BSC", "coin": "USDC", "withdrawIntegerMultiple": "0.********", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "specialWithdrawTips": "", "name": "BNB Smart Chain (BEP20)", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "0", "withdrawMin": "10", "withdrawMax": "10000000000", "withdrawInternalMin": "0.********", "depositDust": "0.01", "minConfirm": "15", "unLockConfirm": "0", "sameAddress": false, "estimatedArrivalTime": "2", "busy": false, "contractAddressUrl": "https://bsctrace.com/token/", "contractAddress": "******************************************"}, {"network": "AVAXC", "coin": "USDC", "withdrawIntegerMultiple": "0.000001", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "specialWithdrawTips": "", "name": "AVAX C-Chain", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "0.16", "withdrawMin": "0.32", "withdrawMax": "********", "withdrawInternalMin": "0.000001", "depositDust": "0.001", "minConfirm": "12", "unLockConfirm": "0", "sameAddress": false, "estimatedArrivalTime": "2", "busy": false, "contractAddressUrl": "https://avascan.info/blockchain/c/token/", "contractAddress": "0xb97ef9ef8734c71904d8002f8b6bc66dd9c48a6e"}, {"network": "ALGO", "coin": "USDC", "withdrawIntegerMultiple": "0.000001", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "specialWithdrawTips": "", "name": "Algorand", "resetAddressStatus": false, "addressRegex": "^[A-Z0-9]{58,58}$", "memoRegex": "", "withdrawFee": "1", "withdrawMin": "10", "withdrawMax": "9999999", "withdrawInternalMin": "0.********", "depositDust": "0.001", "minConfirm": "1", "unLockConfirm": "0", "sameAddress": false, "estimatedArrivalTime": "0", "busy": false, "contractAddressUrl": "https://allo.info/asset/", "contractAddress": "31566704"}, {"network": "APT", "coin": "USDC", "withdrawIntegerMultiple": "0.000001", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "specialWithdrawTips": "", "name": "Aptos", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Za-z]{64}$", "memoRegex": "", "withdrawFee": "0.1", "withdrawMin": "10", "withdrawMax": "99999999", "withdrawInternalMin": "0", "depositDust": "0.000001", "minConfirm": "1", "unLockConfirm": "0", "sameAddress": false, "estimatedArrivalTime": "0", "busy": false, "contractAddressUrl": "https://explorer.aptoslabs.com/fungible_asset/", "contractAddress": "0xbae207659db88bea0cbead6da0ed00aac12edcdda169e591cd41c94180b46f3b"}, {"network": "ARBITRUM", "coin": "USDC", "withdrawIntegerMultiple": "0.000001", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "name": "Arbitrum One", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "0.13", "withdrawMin": "0.26", "withdrawMax": "********", "withdrawInternalMin": "0", "depositDust": "0.001", "minConfirm": "120", "unLockConfirm": "120", "sameAddress": false, "estimatedArrivalTime": "3", "busy": false, "contractAddressUrl": "https://arbiscan.io/token/", "contractAddress": "0xaf88d065e77c8cc2239327c5edb3a432268e5831"}, {"network": "STATEMINT", "coin": "USDC", "withdrawIntegerMultiple": "0.000001", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "specialWithdrawTips": "", "name": "<PERSON><PERSON> (Polkadot)", "resetAddressStatus": false, "addressRegex": "^(1)[0-9a-z-A-Z]{44,50}$", "memoRegex": "", "withdrawFee": "0.7", "withdrawMin": "10", "withdrawMax": "9999999", "withdrawInternalMin": "0.000001", "depositDust": "0.71", "minConfirm": "3", "unLockConfirm": "0", "sameAddress": false, "estimatedArrivalTime": "2", "busy": false, "contractAddressUrl": "https://assethub-polkadot.subscan.io/assets/"}, {"network": "BASE", "coin": "USDC", "withdrawIntegerMultiple": "0.000001", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "specialWithdrawTips": "", "name": "Base", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "0.22", "withdrawMin": "10", "withdrawMax": "50000000", "withdrawInternalMin": "0", "depositDust": "0.001", "minConfirm": "1", "unLockConfirm": "100", "sameAddress": false, "estimatedArrivalTime": "0", "busy": true, "contractAddressUrl": "https://basescan.org/token/", "contractAddress": "******************************************"}, {"network": "CELO", "coin": "USDC", "withdrawIntegerMultiple": "0.000001", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "specialWithdrawTips": "", "name": "CELO", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "0.05", "withdrawMin": "10", "withdrawMax": "9999999", "withdrawInternalMin": "0", "depositDust": "0.001", "minConfirm": "1", "unLockConfirm": "0", "sameAddress": false, "estimatedArrivalTime": "0", "busy": false, "contractAddressUrl": "https://celoscan.io/token/", "contractAddress": "******************************************"}, {"network": "ETH", "coin": "USDC", "withdrawIntegerMultiple": "0.000001", "isDefault": true, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "specialWithdrawTips": "", "name": "Ethereum (ERC20)", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "1.5", "withdrawMin": "20", "withdrawMax": "10000000000", "withdrawInternalMin": "0.000001", "depositDust": "0.001", "minConfirm": "6", "unLockConfirm": "64", "sameAddress": false, "estimatedArrivalTime": "3", "busy": false, "contractAddressUrl": "https://etherscan.io/address/", "contractAddress": "******************************************"}, {"network": "HBAR", "coin": "USDC", "withdrawIntegerMultiple": "0.000001", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "specialWithdrawTips": "", "name": "<PERSON><PERSON><PERSON>", "resetAddressStatus": false, "addressRegex": "^0[.]0[.][1-9]\\d{0,6}$", "memoRegex": "", "withdrawFee": "0.1", "withdrawMin": "10", "withdrawMax": "9999999", "withdrawInternalMin": "0", "depositDust": "0.0001", "minConfirm": "1", "unLockConfirm": "0", "sameAddress": true, "estimatedArrivalTime": "0", "busy": false}, {"network": "NEAR", "coin": "USDC", "withdrawIntegerMultiple": "0.000001", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "specialWithdrawTips": "CAUTION: Ensure the recipient address is activated and authorized to receive the token contract address; otherwise, your withdrawal will fail.", "name": "NEAR Protocol", "resetAddressStatus": false, "addressRegex": "^(?!0x)(?!bc1)(?!bnb1)[a-z0-9_-]{1}[a-z0-9_.-]{0,62}[a-z0-9_-]{1}$", "memoRegex": "", "withdrawFee": "0.2", "withdrawMin": "10", "withdrawMax": "21000000", "withdrawInternalMin": "0", "depositDust": "0.001", "minConfirm": "1", "unLockConfirm": "5", "sameAddress": false, "estimatedArrivalTime": "2", "busy": false, "contractAddressUrl": "https://nearblocks.io/token/", "contractAddress": "17208628f84f5d6ad33f0da3bbbeb27ffcb398eac501a31bd6ad2011e36133a1"}, {"network": "OPTIMISM", "coin": "USDC", "withdrawIntegerMultiple": "0.000001", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "Please note that this is the native USDC in Optimism network. Please verify the contract address before depositing.", "specialWithdrawTips": "Please note that this is the native USDC in Optimism network. Please verify the contract address before withdrawing.", "name": "Optimism", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "0.016", "withdrawMin": "0.032", "withdrawMax": "9999999", "withdrawInternalMin": "0", "depositDust": "0.001", "minConfirm": "25", "unLockConfirm": "100", "sameAddress": false, "estimatedArrivalTime": "2", "busy": false, "contractAddressUrl": "https://optimistic.etherscan.io/token/", "contractAddress": "******************************************"}, {"network": "MATIC", "coin": "USDC", "withdrawIntegerMultiple": "0.000001", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "Please note that this is the native USDC in Polygon network. Please verify the contract address before depositing.", "specialWithdrawTips": "Please note that this is the native USDC in Polygon network. Please verify the contract address before withdrawing.", "name": "Polygon POS", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "0.02", "withdrawMin": "10", "withdrawMax": "50000000", "withdrawInternalMin": "0", "depositDust": "0.001", "minConfirm": "200", "unLockConfirm": "300", "sameAddress": false, "estimatedArrivalTime": "8", "busy": true, "contractAddressUrl": "https://polygonscan.com/token/", "contractAddress": "0x3c499c542cef5e3811e1192ce70d8cc03d5c3359"}, {"network": "RON", "coin": "USDC", "withdrawIntegerMultiple": "0.000001", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "specialWithdrawTips": "", "name": "<PERSON><PERSON>", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "0.2", "withdrawMin": "50", "withdrawMax": "1000000000", "withdrawInternalMin": "0.000001", "depositDust": "0.001", "minConfirm": "12", "unLockConfirm": "0", "sameAddress": false, "estimatedArrivalTime": "0", "busy": false, "contractAddressUrl": "https://explorer.roninchain.com/token/", "contractAddress": "0x0b7007c13325c48911f73a2dad5fa5dcbf808adc"}, {"network": "SOL", "coin": "USDC", "withdrawIntegerMultiple": "0.000001", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "Please note that SOL addresses are case sensitive.", "specialWithdrawTips": "Please note that SOL addresses are case sensitive.", "name": "Solana", "resetAddressStatus": false, "addressRegex": "^[1-9A-HJ-NP-Za-km-z]{32,44}$", "memoRegex": "", "withdrawFee": "1", "withdrawMin": "50", "withdrawMax": "250000000", "withdrawInternalMin": "0.000001", "depositDust": "0.001", "minConfirm": "1", "unLockConfirm": "0", "sameAddress": false, "estimatedArrivalTime": "2", "busy": false, "contractAddressUrl": "https://explorer.solana.com/address/", "contractAddress": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"}, {"network": "XLM", "coin": "USDC", "withdrawIntegerMultiple": "0.0000001", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "specialWithdrawTips": "", "name": "Stellar Network", "resetAddressStatus": false, "addressRegex": "^G[A-D]{1}[A-Z2-7]{54}$", "memoRegex": "^[0-9A-Za-z-]{1,28}$", "withdrawFee": "1", "withdrawMin": "2", "withdrawMax": "********", "withdrawInternalMin": "0.0000001", "depositDust": "0.001", "minConfirm": "1", "unLockConfirm": "0", "sameAddress": true, "estimatedArrivalTime": "0", "busy": false, "contractAddressUrl": "https://stellar.expert/explorer/public/account/", "contractAddress": "GA5ZSEJYB37JRC5AVCIA5MOP4RHTM335X2KGX3IHOJAPP5RE34K4KZVN"}, {"network": "SUI", "coin": "USDC", "withdrawIntegerMultiple": "0.000001", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "specialWithdrawTips": "", "name": "<PERSON><PERSON>", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Za-z]{64}$", "memoRegex": "", "withdrawFee": "0.1", "withdrawMin": "10", "withdrawMax": "9999999", "withdrawInternalMin": "0.********", "depositDust": "0.0001", "minConfirm": "1", "unLockConfirm": "0", "sameAddress": false, "estimatedArrivalTime": "0", "busy": false, "contractAddressUrl": "https://suivision.xyz/coin/", "contractAddress": "0xdba34672e30cb065b1f93e3ab55318768fd6fef66c15942c9f7cb846e2f900e7::usdc::USDC"}, {"network": "TRX", "coin": "USDC", "withdrawIntegerMultiple": "0.000001", "isDefault": false, "depositEnable": false, "withdrawEnable": false, "depositDesc": "Deposit for this token is not supported, please try other networks.", "withdrawDesc": "Withdrawal for this token is not supported, please try other networks.", "specialTips": "", "specialWithdrawTips": "", "name": "Tron (TRC20)", "resetAddressStatus": false, "addressRegex": "^T[1-9A-HJ-NP-Za-km-z]{33}$", "memoRegex": "", "withdrawFee": "1", "withdrawMin": "10", "withdrawMax": "10000000000", "withdrawInternalMin": "0.000001", "depositDust": "0.001", "minConfirm": "1", "unLockConfirm": "0", "sameAddress": false, "estimatedArrivalTime": "2", "busy": false, "contractAddressUrl": "https://tronscan.org/#/token20/", "contractAddress": "TEkxiTehnzSmSe2XqrBj4w32RUN966rdz8"}, {"network": "ZKSYNCERA", "coin": "USDC", "withdrawIntegerMultiple": "0.000001", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "specialWithdrawTips": "", "name": "zkSync Era", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "0.065", "withdrawMin": "0.13", "withdrawMax": "9999999", "withdrawInternalMin": "0.********", "depositDust": "0.001", "minConfirm": "1", "unLockConfirm": "0", "sameAddress": false, "estimatedArrivalTime": "0", "busy": false, "contractAddressUrl": "https://explorer.zksync.io/address/", "contractAddress": "******************************************"}]}, "active": true, "deposit": true, "withdraw": true, "networks": {"BEP20": {"info": {"network": "BSC", "coin": "USDC", "withdrawIntegerMultiple": "0.********", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "specialWithdrawTips": "", "name": "BNB Smart Chain (BEP20)", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "0", "withdrawMin": "10", "withdrawMax": "10000000000", "withdrawInternalMin": "0.********", "depositDust": "0.01", "minConfirm": "15", "unLockConfirm": "0", "sameAddress": false, "estimatedArrivalTime": "2", "busy": false, "contractAddressUrl": "https://bsctrace.com/token/", "contractAddress": "******************************************"}, "id": "BSC", "network": "BEP20", "active": true, "deposit": true, "withdraw": true, "fee": 0, "precision": 1e-08, "limits": {"withdraw": {"min": 10, "max": 10000000000}, "deposit": {"min": 0.01, "max": null}}}, "AVAXC": {"info": {"network": "AVAXC", "coin": "USDC", "withdrawIntegerMultiple": "0.000001", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "specialWithdrawTips": "", "name": "AVAX C-Chain", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "0.16", "withdrawMin": "0.32", "withdrawMax": "********", "withdrawInternalMin": "0.000001", "depositDust": "0.001", "minConfirm": "12", "unLockConfirm": "0", "sameAddress": false, "estimatedArrivalTime": "2", "busy": false, "contractAddressUrl": "https://avascan.info/blockchain/c/token/", "contractAddress": "0xb97ef9ef8734c71904d8002f8b6bc66dd9c48a6e"}, "id": "AVAXC", "network": "AVAXC", "active": true, "deposit": true, "withdraw": true, "fee": 0.16, "precision": 1e-06, "limits": {"withdraw": {"min": 0.32, "max": ********}, "deposit": {"min": 0.001, "max": null}}}, "ALGO": {"info": {"network": "ALGO", "coin": "USDC", "withdrawIntegerMultiple": "0.000001", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "specialWithdrawTips": "", "name": "Algorand", "resetAddressStatus": false, "addressRegex": "^[A-Z0-9]{58,58}$", "memoRegex": "", "withdrawFee": "1", "withdrawMin": "10", "withdrawMax": "9999999", "withdrawInternalMin": "0.********", "depositDust": "0.001", "minConfirm": "1", "unLockConfirm": "0", "sameAddress": false, "estimatedArrivalTime": "0", "busy": false, "contractAddressUrl": "https://allo.info/asset/", "contractAddress": "31566704"}, "id": "ALGO", "network": "ALGO", "active": true, "deposit": true, "withdraw": true, "fee": 1, "precision": 1e-06, "limits": {"withdraw": {"min": 10, "max": 9999999}, "deposit": {"min": 0.001, "max": null}}}, "APT": {"info": {"network": "APT", "coin": "USDC", "withdrawIntegerMultiple": "0.000001", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "specialWithdrawTips": "", "name": "Aptos", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Za-z]{64}$", "memoRegex": "", "withdrawFee": "0.1", "withdrawMin": "10", "withdrawMax": "99999999", "withdrawInternalMin": "0", "depositDust": "0.000001", "minConfirm": "1", "unLockConfirm": "0", "sameAddress": false, "estimatedArrivalTime": "0", "busy": false, "contractAddressUrl": "https://explorer.aptoslabs.com/fungible_asset/", "contractAddress": "0xbae207659db88bea0cbead6da0ed00aac12edcdda169e591cd41c94180b46f3b"}, "id": "APT", "network": "APT", "active": true, "deposit": true, "withdraw": true, "fee": 0.1, "precision": 1e-06, "limits": {"withdraw": {"min": 10, "max": 99999999}, "deposit": {"min": 1e-06, "max": null}}}, "ARBITRUM": {"info": {"network": "ARBITRUM", "coin": "USDC", "withdrawIntegerMultiple": "0.000001", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "name": "Arbitrum One", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "0.13", "withdrawMin": "0.26", "withdrawMax": "********", "withdrawInternalMin": "0", "depositDust": "0.001", "minConfirm": "120", "unLockConfirm": "120", "sameAddress": false, "estimatedArrivalTime": "3", "busy": false, "contractAddressUrl": "https://arbiscan.io/token/", "contractAddress": "0xaf88d065e77c8cc2239327c5edb3a432268e5831"}, "id": "ARBITRUM", "network": "ARBITRUM", "active": true, "deposit": true, "withdraw": true, "fee": 0.13, "precision": 1e-06, "limits": {"withdraw": {"min": 0.26, "max": ********}, "deposit": {"min": 0.001, "max": null}}}, "STATEMINT": {"info": {"network": "STATEMINT", "coin": "USDC", "withdrawIntegerMultiple": "0.000001", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "specialWithdrawTips": "", "name": "<PERSON><PERSON> (Polkadot)", "resetAddressStatus": false, "addressRegex": "^(1)[0-9a-z-A-Z]{44,50}$", "memoRegex": "", "withdrawFee": "0.7", "withdrawMin": "10", "withdrawMax": "9999999", "withdrawInternalMin": "0.000001", "depositDust": "0.71", "minConfirm": "3", "unLockConfirm": "0", "sameAddress": false, "estimatedArrivalTime": "2", "busy": false, "contractAddressUrl": "https://assethub-polkadot.subscan.io/assets/"}, "id": "STATEMINT", "network": "STATEMINT", "active": true, "deposit": true, "withdraw": true, "fee": 0.7, "precision": 1e-06, "limits": {"withdraw": {"min": 10, "max": 9999999}, "deposit": {"min": 0.71, "max": null}}}, "BASE": {"info": {"network": "BASE", "coin": "USDC", "withdrawIntegerMultiple": "0.000001", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "specialWithdrawTips": "", "name": "Base", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "0.22", "withdrawMin": "10", "withdrawMax": "50000000", "withdrawInternalMin": "0", "depositDust": "0.001", "minConfirm": "1", "unLockConfirm": "100", "sameAddress": false, "estimatedArrivalTime": "0", "busy": true, "contractAddressUrl": "https://basescan.org/token/", "contractAddress": "******************************************"}, "id": "BASE", "network": "BASE", "active": true, "deposit": true, "withdraw": true, "fee": 0.22, "precision": 1e-06, "limits": {"withdraw": {"min": 10, "max": 50000000}, "deposit": {"min": 0.001, "max": null}}}, "CELO": {"info": {"network": "CELO", "coin": "USDC", "withdrawIntegerMultiple": "0.000001", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "specialWithdrawTips": "", "name": "CELO", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "0.05", "withdrawMin": "10", "withdrawMax": "9999999", "withdrawInternalMin": "0", "depositDust": "0.001", "minConfirm": "1", "unLockConfirm": "0", "sameAddress": false, "estimatedArrivalTime": "0", "busy": false, "contractAddressUrl": "https://celoscan.io/token/", "contractAddress": "******************************************"}, "id": "CELO", "network": "CELO", "active": true, "deposit": true, "withdraw": true, "fee": 0.05, "precision": 1e-06, "limits": {"withdraw": {"min": 10, "max": 9999999}, "deposit": {"min": 0.001, "max": null}}}, "ERC20": {"info": {"network": "ETH", "coin": "USDC", "withdrawIntegerMultiple": "0.000001", "isDefault": true, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "specialWithdrawTips": "", "name": "Ethereum (ERC20)", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "1.5", "withdrawMin": "20", "withdrawMax": "10000000000", "withdrawInternalMin": "0.000001", "depositDust": "0.001", "minConfirm": "6", "unLockConfirm": "64", "sameAddress": false, "estimatedArrivalTime": "3", "busy": false, "contractAddressUrl": "https://etherscan.io/address/", "contractAddress": "******************************************"}, "id": "ETH", "network": "ERC20", "active": true, "deposit": true, "withdraw": true, "fee": 1.5, "precision": 1e-06, "limits": {"withdraw": {"min": 20, "max": 10000000000}, "deposit": {"min": 0.001, "max": null}}}, "HBAR": {"info": {"network": "HBAR", "coin": "USDC", "withdrawIntegerMultiple": "0.000001", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "specialWithdrawTips": "", "name": "<PERSON><PERSON><PERSON>", "resetAddressStatus": false, "addressRegex": "^0[.]0[.][1-9]\\d{0,6}$", "memoRegex": "", "withdrawFee": "0.1", "withdrawMin": "10", "withdrawMax": "9999999", "withdrawInternalMin": "0", "depositDust": "0.0001", "minConfirm": "1", "unLockConfirm": "0", "sameAddress": true, "estimatedArrivalTime": "0", "busy": false}, "id": "HBAR", "network": "HBAR", "active": true, "deposit": true, "withdraw": true, "fee": 0.1, "precision": 1e-06, "limits": {"withdraw": {"min": 10, "max": 9999999}, "deposit": {"min": 0.0001, "max": null}}}, "NEAR": {"info": {"network": "NEAR", "coin": "USDC", "withdrawIntegerMultiple": "0.000001", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "specialWithdrawTips": "CAUTION: Ensure the recipient address is activated and authorized to receive the token contract address; otherwise, your withdrawal will fail.", "name": "NEAR Protocol", "resetAddressStatus": false, "addressRegex": "^(?!0x)(?!bc1)(?!bnb1)[a-z0-9_-]{1}[a-z0-9_.-]{0,62}[a-z0-9_-]{1}$", "memoRegex": "", "withdrawFee": "0.2", "withdrawMin": "10", "withdrawMax": "21000000", "withdrawInternalMin": "0", "depositDust": "0.001", "minConfirm": "1", "unLockConfirm": "5", "sameAddress": false, "estimatedArrivalTime": "2", "busy": false, "contractAddressUrl": "https://nearblocks.io/token/", "contractAddress": "17208628f84f5d6ad33f0da3bbbeb27ffcb398eac501a31bd6ad2011e36133a1"}, "id": "NEAR", "network": "NEAR", "active": true, "deposit": true, "withdraw": true, "fee": 0.2, "precision": 1e-06, "limits": {"withdraw": {"min": 10, "max": 21000000}, "deposit": {"min": 0.001, "max": null}}}, "OPTIMISM": {"info": {"network": "OPTIMISM", "coin": "USDC", "withdrawIntegerMultiple": "0.000001", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "Please note that this is the native USDC in Optimism network. Please verify the contract address before depositing.", "specialWithdrawTips": "Please note that this is the native USDC in Optimism network. Please verify the contract address before withdrawing.", "name": "Optimism", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "0.016", "withdrawMin": "0.032", "withdrawMax": "9999999", "withdrawInternalMin": "0", "depositDust": "0.001", "minConfirm": "25", "unLockConfirm": "100", "sameAddress": false, "estimatedArrivalTime": "2", "busy": false, "contractAddressUrl": "https://optimistic.etherscan.io/token/", "contractAddress": "******************************************"}, "id": "OPTIMISM", "network": "OPTIMISM", "active": true, "deposit": true, "withdraw": true, "fee": 0.016, "precision": 1e-06, "limits": {"withdraw": {"min": 0.032, "max": 9999999}, "deposit": {"min": 0.001, "max": null}}}, "MATIC": {"info": {"network": "MATIC", "coin": "USDC", "withdrawIntegerMultiple": "0.000001", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "Please note that this is the native USDC in Polygon network. Please verify the contract address before depositing.", "specialWithdrawTips": "Please note that this is the native USDC in Polygon network. Please verify the contract address before withdrawing.", "name": "Polygon POS", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "0.02", "withdrawMin": "10", "withdrawMax": "50000000", "withdrawInternalMin": "0", "depositDust": "0.001", "minConfirm": "200", "unLockConfirm": "300", "sameAddress": false, "estimatedArrivalTime": "8", "busy": true, "contractAddressUrl": "https://polygonscan.com/token/", "contractAddress": "0x3c499c542cef5e3811e1192ce70d8cc03d5c3359"}, "id": "MATIC", "network": "MATIC", "active": true, "deposit": true, "withdraw": true, "fee": 0.02, "precision": 1e-06, "limits": {"withdraw": {"min": 10, "max": 50000000}, "deposit": {"min": 0.001, "max": null}}}, "RON": {"info": {"network": "RON", "coin": "USDC", "withdrawIntegerMultiple": "0.000001", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "specialWithdrawTips": "", "name": "<PERSON><PERSON>", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "0.2", "withdrawMin": "50", "withdrawMax": "1000000000", "withdrawInternalMin": "0.000001", "depositDust": "0.001", "minConfirm": "12", "unLockConfirm": "0", "sameAddress": false, "estimatedArrivalTime": "0", "busy": false, "contractAddressUrl": "https://explorer.roninchain.com/token/", "contractAddress": "0x0b7007c13325c48911f73a2dad5fa5dcbf808adc"}, "id": "RON", "network": "RON", "active": true, "deposit": true, "withdraw": true, "fee": 0.2, "precision": 1e-06, "limits": {"withdraw": {"min": 50, "max": 1000000000}, "deposit": {"min": 0.001, "max": null}}}, "SOL": {"info": {"network": "SOL", "coin": "USDC", "withdrawIntegerMultiple": "0.000001", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "Please note that SOL addresses are case sensitive.", "specialWithdrawTips": "Please note that SOL addresses are case sensitive.", "name": "Solana", "resetAddressStatus": false, "addressRegex": "^[1-9A-HJ-NP-Za-km-z]{32,44}$", "memoRegex": "", "withdrawFee": "1", "withdrawMin": "50", "withdrawMax": "250000000", "withdrawInternalMin": "0.000001", "depositDust": "0.001", "minConfirm": "1", "unLockConfirm": "0", "sameAddress": false, "estimatedArrivalTime": "2", "busy": false, "contractAddressUrl": "https://explorer.solana.com/address/", "contractAddress": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"}, "id": "SOL", "network": "SOL", "active": true, "deposit": true, "withdraw": true, "fee": 1, "precision": 1e-06, "limits": {"withdraw": {"min": 50, "max": 250000000}, "deposit": {"min": 0.001, "max": null}}}, "XLM": {"info": {"network": "XLM", "coin": "USDC", "withdrawIntegerMultiple": "0.0000001", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "specialWithdrawTips": "", "name": "Stellar Network", "resetAddressStatus": false, "addressRegex": "^G[A-D]{1}[A-Z2-7]{54}$", "memoRegex": "^[0-9A-Za-z-]{1,28}$", "withdrawFee": "1", "withdrawMin": "2", "withdrawMax": "********", "withdrawInternalMin": "0.0000001", "depositDust": "0.001", "minConfirm": "1", "unLockConfirm": "0", "sameAddress": true, "estimatedArrivalTime": "0", "busy": false, "contractAddressUrl": "https://stellar.expert/explorer/public/account/", "contractAddress": "GA5ZSEJYB37JRC5AVCIA5MOP4RHTM335X2KGX3IHOJAPP5RE34K4KZVN"}, "id": "XLM", "network": "XLM", "active": true, "deposit": true, "withdraw": true, "fee": 1, "precision": 1e-07, "limits": {"withdraw": {"min": 2, "max": ********}, "deposit": {"min": 0.001, "max": null}}}, "SUI": {"info": {"network": "SUI", "coin": "USDC", "withdrawIntegerMultiple": "0.000001", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "specialWithdrawTips": "", "name": "<PERSON><PERSON>", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Za-z]{64}$", "memoRegex": "", "withdrawFee": "0.1", "withdrawMin": "10", "withdrawMax": "9999999", "withdrawInternalMin": "0.********", "depositDust": "0.0001", "minConfirm": "1", "unLockConfirm": "0", "sameAddress": false, "estimatedArrivalTime": "0", "busy": false, "contractAddressUrl": "https://suivision.xyz/coin/", "contractAddress": "0xdba34672e30cb065b1f93e3ab55318768fd6fef66c15942c9f7cb846e2f900e7::usdc::USDC"}, "id": "SUI", "network": "SUI", "active": true, "deposit": true, "withdraw": true, "fee": 0.1, "precision": 1e-06, "limits": {"withdraw": {"min": 10, "max": 9999999}, "deposit": {"min": 0.0001, "max": null}}}, "TRC20": {"info": {"network": "TRX", "coin": "USDC", "withdrawIntegerMultiple": "0.000001", "isDefault": false, "depositEnable": false, "withdrawEnable": false, "depositDesc": "Deposit for this token is not supported, please try other networks.", "withdrawDesc": "Withdrawal for this token is not supported, please try other networks.", "specialTips": "", "specialWithdrawTips": "", "name": "Tron (TRC20)", "resetAddressStatus": false, "addressRegex": "^T[1-9A-HJ-NP-Za-km-z]{33}$", "memoRegex": "", "withdrawFee": "1", "withdrawMin": "10", "withdrawMax": "10000000000", "withdrawInternalMin": "0.000001", "depositDust": "0.001", "minConfirm": "1", "unLockConfirm": "0", "sameAddress": false, "estimatedArrivalTime": "2", "busy": false, "contractAddressUrl": "https://tronscan.org/#/token20/", "contractAddress": "TEkxiTehnzSmSe2XqrBj4w32RUN966rdz8"}, "id": "TRX", "network": "TRC20", "active": false, "deposit": false, "withdraw": false, "fee": 1, "precision": 1e-06, "limits": {"withdraw": {"min": 10, "max": 10000000000}, "deposit": {"min": 0.001, "max": null}}}, "ZKSYNCERA": {"info": {"network": "ZKSYNCERA", "coin": "USDC", "withdrawIntegerMultiple": "0.000001", "isDefault": false, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "specialWithdrawTips": "", "name": "zkSync Era", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "0.065", "withdrawMin": "0.13", "withdrawMax": "9999999", "withdrawInternalMin": "0.********", "depositDust": "0.001", "minConfirm": "1", "unLockConfirm": "0", "sameAddress": false, "estimatedArrivalTime": "0", "busy": false, "contractAddressUrl": "https://explorer.zksync.io/address/", "contractAddress": "******************************************"}, "id": "ZKSYNCERA", "network": "ZKSYNCERA", "active": true, "deposit": true, "withdraw": true, "fee": 0.065, "precision": 1e-06, "limits": {"withdraw": {"min": 0.13, "max": 9999999}, "deposit": {"min": 0.001, "max": null}}}}, "fee": 1.5, "fees": {"BSC": 0, "AVAXC": 0.16, "ALGO": 1, "APT": 0.1, "ARBITRUM": 0.13, "STATEMINT": 0.7, "BASE": 0.22, "CELO": 0.05, "ETH": 1.5, "HBAR": 0.1, "NEAR": 0.2, "OPTIMISM": 0.016, "MATIC": 0.02, "RON": 0.2, "SOL": 1, "XLM": 1, "SUI": 0.1, "TRX": 1, "ZKSYNCERA": 0.065}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": null, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": null, "max": null}}, "margin": null}, "BUSD": {"id": "BUSD", "name": "BUSD", "code": "BUSD", "type": "crypto", "precision": 1e-08, "info": {"coin": "BUSD", "depositAllEnable": true, "withdrawAllEnable": true, "name": "BUSD", "free": "0", "locked": "0", "freeze": "0", "withdrawing": "0", "ipoing": "0", "ipoable": "0", "storage": "0", "isLegalMoney": false, "trading": false, "networkList": [{"network": "BSC", "coin": "BUSD", "withdrawIntegerMultiple": "0.********", "isDefault": false, "depositEnable": true, "withdrawEnable": false, "depositDesc": "", "withdrawDesc": "Withdrawal for this token is not supported, please try other networks.", "specialTips": "", "specialWithdrawTips": "The network you have selected is BSC. Please ensure that the withdrawal address supports the BNB Smart Chain network. You will potentially lose your assets if the chosen platform does not support refunds of wrongfully deposited assets.", "name": "BNB Smart Chain (BEP20)", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "0.59", "withdrawMin": "1.18", "withdrawMax": "10000000000", "withdrawInternalMin": "0.********", "depositDust": "0.001", "minConfirm": "15", "unLockConfirm": "0", "sameAddress": false, "estimatedArrivalTime": "2", "busy": false, "contractAddressUrl": "https://bsctrace.com/token/", "contractAddress": "******************************************"}, {"network": "AVAXC", "coin": "BUSD", "withdrawIntegerMultiple": "0.********", "isDefault": false, "depositEnable": true, "withdrawEnable": false, "depositDesc": "", "withdrawDesc": "The wallet is currently undergoing maintenance. Withdrawals for this asset will be resumed shortly.", "specialTips": "Please note that this deposit address supports C-Chain deposits. For X-Chain deposits, please use the AVAX network.", "specialWithdrawTips": "", "name": "AVAX C-Chain", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "0.16", "withdrawMin": "0.32", "withdrawMax": "9999999", "withdrawInternalMin": "0.********", "depositDust": "0.001", "minConfirm": "12", "unLockConfirm": "0", "sameAddress": false, "estimatedArrivalTime": "2", "busy": false, "contractAddressUrl": "https://avascan.info/blockchain/c/token/", "contractAddress": "******************************************"}, {"network": "ETH", "coin": "BUSD", "withdrawIntegerMultiple": "0.********", "isDefault": true, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "name": "Ethereum (ERC20)", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "5", "withdrawMin": "10", "withdrawMax": "10000000000", "withdrawInternalMin": "0.********", "depositDust": "0.001", "minConfirm": "6", "unLockConfirm": "64", "sameAddress": false, "estimatedArrivalTime": "3", "busy": false, "contractAddressUrl": "https://etherscan.io/address/", "contractAddress": "******************************************"}, {"network": "OPTIMISM", "coin": "BUSD", "withdrawIntegerMultiple": "0.********", "isDefault": false, "depositEnable": true, "withdrawEnable": false, "depositDesc": "", "withdrawDesc": "The wallet is currently undergoing maintenance. Withdrawals for this asset will be resumed shortly.", "specialTips": "", "specialWithdrawTips": "", "name": "Optimism", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "0.016", "withdrawMin": "0.032", "withdrawMax": "9999999", "withdrawInternalMin": "0.********", "depositDust": "0.001", "minConfirm": "25", "unLockConfirm": "100", "sameAddress": false, "estimatedArrivalTime": "2", "busy": false, "contractAddressUrl": "https://optimistic.etherscan.io/token/", "contractAddress": "******************************************"}, {"network": "MATIC", "coin": "BUSD", "withdrawIntegerMultiple": "0.********", "isDefault": false, "depositEnable": true, "withdrawEnable": false, "depositDesc": "", "withdrawDesc": "The wallet is currently undergoing maintenance. Withdrawals for this asset will be resumed shortly.", "specialTips": "", "specialWithdrawTips": "", "name": "Polygon POS", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "0.026", "withdrawMin": "0.052", "withdrawMax": "9999999", "withdrawInternalMin": "0.********", "depositDust": "0.001", "minConfirm": "200", "unLockConfirm": "300", "sameAddress": false, "estimatedArrivalTime": "8", "busy": true, "contractAddressUrl": "https://polygonscan.com/token/", "contractAddress": "******************************************"}, {"network": "TRX", "coin": "BUSD", "withdrawIntegerMultiple": "0.********", "isDefault": false, "depositEnable": true, "withdrawEnable": false, "depositDesc": "", "withdrawDesc": "The wallet is currently undergoing maintenance. Withdrawals for this asset will be resumed shortly.", "specialTips": "", "name": "Tron (TRC20)", "resetAddressStatus": false, "addressRegex": "^T[1-9A-HJ-NP-Za-km-z]{33}$", "memoRegex": "", "withdrawFee": "1", "withdrawMin": "10", "withdrawMax": "10000000000", "withdrawInternalMin": "0.********", "depositDust": "0.001", "minConfirm": "1", "unLockConfirm": "0", "sameAddress": false, "estimatedArrivalTime": "2", "busy": false, "contractAddressUrl": "https://tronscan.org/#/token20/", "contractAddress": "TMz2SWatiAtZVVcH2ebpsbVtYwUPT9EdjH"}]}, "active": false, "deposit": true, "withdraw": true, "networks": {"BEP20": {"info": {"network": "BSC", "coin": "BUSD", "withdrawIntegerMultiple": "0.********", "isDefault": false, "depositEnable": true, "withdrawEnable": false, "depositDesc": "", "withdrawDesc": "Withdrawal for this token is not supported, please try other networks.", "specialTips": "", "specialWithdrawTips": "The network you have selected is BSC. Please ensure that the withdrawal address supports the BNB Smart Chain network. You will potentially lose your assets if the chosen platform does not support refunds of wrongfully deposited assets.", "name": "BNB Smart Chain (BEP20)", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "0.59", "withdrawMin": "1.18", "withdrawMax": "10000000000", "withdrawInternalMin": "0.********", "depositDust": "0.001", "minConfirm": "15", "unLockConfirm": "0", "sameAddress": false, "estimatedArrivalTime": "2", "busy": false, "contractAddressUrl": "https://bsctrace.com/token/", "contractAddress": "******************************************"}, "id": "BSC", "network": "BEP20", "active": false, "deposit": true, "withdraw": false, "fee": 0.59, "precision": 1e-08, "limits": {"withdraw": {"min": 1.18, "max": 10000000000}, "deposit": {"min": 0.001, "max": null}}}, "AVAXC": {"info": {"network": "AVAXC", "coin": "BUSD", "withdrawIntegerMultiple": "0.********", "isDefault": false, "depositEnable": true, "withdrawEnable": false, "depositDesc": "", "withdrawDesc": "The wallet is currently undergoing maintenance. Withdrawals for this asset will be resumed shortly.", "specialTips": "Please note that this deposit address supports C-Chain deposits. For X-Chain deposits, please use the AVAX network.", "specialWithdrawTips": "", "name": "AVAX C-Chain", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "0.16", "withdrawMin": "0.32", "withdrawMax": "9999999", "withdrawInternalMin": "0.********", "depositDust": "0.001", "minConfirm": "12", "unLockConfirm": "0", "sameAddress": false, "estimatedArrivalTime": "2", "busy": false, "contractAddressUrl": "https://avascan.info/blockchain/c/token/", "contractAddress": "******************************************"}, "id": "AVAXC", "network": "AVAXC", "active": false, "deposit": true, "withdraw": false, "fee": 0.16, "precision": 1e-08, "limits": {"withdraw": {"min": 0.32, "max": 9999999}, "deposit": {"min": 0.001, "max": null}}}, "ERC20": {"info": {"network": "ETH", "coin": "BUSD", "withdrawIntegerMultiple": "0.********", "isDefault": true, "depositEnable": true, "withdrawEnable": true, "depositDesc": "", "withdrawDesc": "", "specialTips": "", "name": "Ethereum (ERC20)", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "5", "withdrawMin": "10", "withdrawMax": "10000000000", "withdrawInternalMin": "0.********", "depositDust": "0.001", "minConfirm": "6", "unLockConfirm": "64", "sameAddress": false, "estimatedArrivalTime": "3", "busy": false, "contractAddressUrl": "https://etherscan.io/address/", "contractAddress": "******************************************"}, "id": "ETH", "network": "ERC20", "active": true, "deposit": true, "withdraw": true, "fee": 5, "precision": 1e-08, "limits": {"withdraw": {"min": 10, "max": 10000000000}, "deposit": {"min": 0.001, "max": null}}}, "OPTIMISM": {"info": {"network": "OPTIMISM", "coin": "BUSD", "withdrawIntegerMultiple": "0.********", "isDefault": false, "depositEnable": true, "withdrawEnable": false, "depositDesc": "", "withdrawDesc": "The wallet is currently undergoing maintenance. Withdrawals for this asset will be resumed shortly.", "specialTips": "", "specialWithdrawTips": "", "name": "Optimism", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "0.016", "withdrawMin": "0.032", "withdrawMax": "9999999", "withdrawInternalMin": "0.********", "depositDust": "0.001", "minConfirm": "25", "unLockConfirm": "100", "sameAddress": false, "estimatedArrivalTime": "2", "busy": false, "contractAddressUrl": "https://optimistic.etherscan.io/token/", "contractAddress": "******************************************"}, "id": "OPTIMISM", "network": "OPTIMISM", "active": false, "deposit": true, "withdraw": false, "fee": 0.016, "precision": 1e-08, "limits": {"withdraw": {"min": 0.032, "max": 9999999}, "deposit": {"min": 0.001, "max": null}}}, "MATIC": {"info": {"network": "MATIC", "coin": "BUSD", "withdrawIntegerMultiple": "0.********", "isDefault": false, "depositEnable": true, "withdrawEnable": false, "depositDesc": "", "withdrawDesc": "The wallet is currently undergoing maintenance. Withdrawals for this asset will be resumed shortly.", "specialTips": "", "specialWithdrawTips": "", "name": "Polygon POS", "resetAddressStatus": false, "addressRegex": "^(0x)[0-9A-Fa-f]{40}$", "memoRegex": "", "withdrawFee": "0.026", "withdrawMin": "0.052", "withdrawMax": "9999999", "withdrawInternalMin": "0.********", "depositDust": "0.001", "minConfirm": "200", "unLockConfirm": "300", "sameAddress": false, "estimatedArrivalTime": "8", "busy": true, "contractAddressUrl": "https://polygonscan.com/token/", "contractAddress": "******************************************"}, "id": "MATIC", "network": "MATIC", "active": false, "deposit": true, "withdraw": false, "fee": 0.026, "precision": 1e-08, "limits": {"withdraw": {"min": 0.052, "max": 9999999}, "deposit": {"min": 0.001, "max": null}}}, "TRC20": {"info": {"network": "TRX", "coin": "BUSD", "withdrawIntegerMultiple": "0.********", "isDefault": false, "depositEnable": true, "withdrawEnable": false, "depositDesc": "", "withdrawDesc": "The wallet is currently undergoing maintenance. Withdrawals for this asset will be resumed shortly.", "specialTips": "", "name": "Tron (TRC20)", "resetAddressStatus": false, "addressRegex": "^T[1-9A-HJ-NP-Za-km-z]{33}$", "memoRegex": "", "withdrawFee": "1", "withdrawMin": "10", "withdrawMax": "10000000000", "withdrawInternalMin": "0.********", "depositDust": "0.001", "minConfirm": "1", "unLockConfirm": "0", "sameAddress": false, "estimatedArrivalTime": "2", "busy": false, "contractAddressUrl": "https://tronscan.org/#/token20/", "contractAddress": "TMz2SWatiAtZVVcH2ebpsbVtYwUPT9EdjH"}, "id": "TRX", "network": "TRC20", "active": false, "deposit": true, "withdraw": false, "fee": 1, "precision": 1e-08, "limits": {"withdraw": {"min": 10, "max": 10000000000}, "deposit": {"min": 0.001, "max": null}}}}, "fee": 5, "fees": {"BSC": 0.59, "AVAXC": 0.16, "ETH": 5, "OPTIMISM": 0.016, "MATIC": 0.026, "TRX": 1}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": null, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": null, "max": null}}, "margin": null}}