{"BTC/USDT": {"id": "BTC_USDT", "lowercaseId": null, "symbol": "BTC/USDT", "base": "BTC", "quote": "USDT", "settle": null, "baseId": "BTC", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": true, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.002, "maker": 0.002, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1e-05, "price": 0.1}, "limits": {"leverage": {"min": 1, "max": 20}, "amount": {"min": 1e-05, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": 3, "max": 5000000}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"id": "BTC_USDT", "base": "BTC", "quote": "USDT", "leverage": "20", "min_base_amount": "0.00001", "min_quote_amount": "3", "max_quote_amount": "5000000", "status": "1", "base_name": "Bitcoin", "quote_name": "<PERSON><PERSON>", "fee": "0.2", "max_base_amount": "100", "amount_precision": "5", "precision": "1", "trade_status": "tradable", "sell_start": "0", "buy_start": "0", "type": "normal", "trade_url": "https://www.gate.io/trade/BTC_USDT"}, "tierBased": true, "percentage": true, "feeSide": "get", "tiers": {"maker": [[0, 0.002], [1.5, 0.00185], [3, 0.00175], [6, 0.00165], [12.5, 0.00155], [25, 0.00145], [75, 0.00135], [200, 0.00125], [500, 0.00115], [1250, 0.00105], [2500, 0.00095], [3000, 0.00085], [6000, 0.00075], [11000, 0.00065], [20000, 0.00055], [40000, 0.00055], [75000, 0.00055]], "taker": [[0, 0.002], [1.5, 0.00195], [3, 0.00185], [6, 0.00175], [12.5, 0.00165], [25, 0.00155], [75, 0.00145], [200, 0.00135], [500, 0.00125], [1250, 0.00115], [2500, 0.00105], [3000, 0.00095], [6000, 0.00085], [11000, 0.00075], [20000, 0.00065], [40000, 0.00065], [75000, 0.00065]]}}, "ETH/USDT": {"id": "ETH_USDT", "lowercaseId": null, "symbol": "ETH/USDT", "base": "ETH", "quote": "USDT", "settle": null, "baseId": "ETH", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": true, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.002, "maker": 0.002, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.0001, "price": 0.01}, "limits": {"leverage": {"min": 1, "max": 20}, "amount": {"min": 0.001, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": 3, "max": 5000000}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"id": "ETH_USDT", "base": "ETH", "quote": "USDT", "leverage": "20", "min_base_amount": "0.001", "min_quote_amount": "3", "max_quote_amount": "5000000", "status": "1", "base_name": "Ethereum", "quote_name": "<PERSON><PERSON>", "fee": "0.2", "max_base_amount": "1000", "amount_precision": "4", "precision": "2", "trade_status": "tradable", "sell_start": "0", "buy_start": "0", "type": "normal", "trade_url": "https://www.gate.io/trade/ETH_USDT"}, "tierBased": true, "percentage": true, "feeSide": "get", "tiers": {"maker": [[0, 0.002], [1.5, 0.00185], [3, 0.00175], [6, 0.00165], [12.5, 0.00155], [25, 0.00145], [75, 0.00135], [200, 0.00125], [500, 0.00115], [1250, 0.00105], [2500, 0.00095], [3000, 0.00085], [6000, 0.00075], [11000, 0.00065], [20000, 0.00055], [40000, 0.00055], [75000, 0.00055]], "taker": [[0, 0.002], [1.5, 0.00195], [3, 0.00185], [6, 0.00175], [12.5, 0.00165], [25, 0.00155], [75, 0.00145], [200, 0.00135], [500, 0.00125], [1250, 0.00115], [2500, 0.00105], [3000, 0.00095], [6000, 0.00085], [11000, 0.00075], [20000, 0.00065], [40000, 0.00065], [75000, 0.00065]]}}, "ADA/USDT": {"id": "ADA_USDT", "lowercaseId": null, "symbol": "ADA/USDT", "base": "ADA", "quote": "USDT", "settle": null, "baseId": "ADA", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": true, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.002, "maker": 0.002, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.01, "price": 0.0001}, "limits": {"leverage": {"min": 1, "max": 20}, "amount": {"min": 0.01, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": 3, "max": 5000000}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"id": "ADA_USDT", "base": "ADA", "quote": "USDT", "leverage": "20", "min_base_amount": "0.01", "min_quote_amount": "3", "max_quote_amount": "5000000", "status": "1", "base_name": "Cardano", "quote_name": "<PERSON><PERSON>", "fee": "0.2", "amount_precision": "2", "precision": "4", "trade_status": "tradable", "sell_start": "0", "buy_start": "0", "type": "normal", "trade_url": "https://www.gate.io/trade/ADA_USDT"}, "tierBased": true, "percentage": true, "feeSide": "get", "tiers": {"maker": [[0, 0.002], [1.5, 0.00185], [3, 0.00175], [6, 0.00165], [12.5, 0.00155], [25, 0.00145], [75, 0.00135], [200, 0.00125], [500, 0.00115], [1250, 0.00105], [2500, 0.00095], [3000, 0.00085], [6000, 0.00075], [11000, 0.00065], [20000, 0.00055], [40000, 0.00055], [75000, 0.00055]], "taker": [[0, 0.002], [1.5, 0.00195], [3, 0.00185], [6, 0.00175], [12.5, 0.00165], [25, 0.00155], [75, 0.00145], [200, 0.00135], [500, 0.00125], [1250, 0.00115], [2500, 0.00105], [3000, 0.00095], [6000, 0.00085], [11000, 0.00075], [20000, 0.00065], [40000, 0.00065], [75000, 0.00065]]}}, "LTC/USDT": {"id": "LTC_USDT", "lowercaseId": null, "symbol": "LTC/USDT", "base": "LTC", "quote": "USDT", "settle": null, "baseId": "LTC", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": true, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.002, "maker": 0.002, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.0001, "price": 0.01}, "limits": {"leverage": {"min": 1, "max": 10}, "amount": {"min": 0.0001, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": 3, "max": 5000000}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"id": "LTC_USDT", "base": "LTC", "quote": "USDT", "leverage": "10", "min_base_amount": "0.0001", "min_quote_amount": "3", "max_quote_amount": "5000000", "status": "1", "base_name": "Litecoin", "quote_name": "<PERSON><PERSON>", "fee": "0.2", "amount_precision": "4", "precision": "2", "trade_status": "tradable", "sell_start": "0", "buy_start": "0", "type": "normal", "trade_url": "https://www.gate.io/trade/LTC_USDT"}, "tierBased": true, "percentage": true, "feeSide": "get", "tiers": {"maker": [[0, 0.002], [1.5, 0.00185], [3, 0.00175], [6, 0.00165], [12.5, 0.00155], [25, 0.00145], [75, 0.00135], [200, 0.00125], [500, 0.00115], [1250, 0.00105], [2500, 0.00095], [3000, 0.00085], [6000, 0.00075], [11000, 0.00065], [20000, 0.00055], [40000, 0.00055], [75000, 0.00055]], "taker": [[0, 0.002], [1.5, 0.00195], [3, 0.00185], [6, 0.00175], [12.5, 0.00165], [25, 0.00155], [75, 0.00145], [200, 0.00135], [500, 0.00125], [1250, 0.00115], [2500, 0.00105], [3000, 0.00095], [6000, 0.00085], [11000, 0.00075], [20000, 0.00065], [40000, 0.00065], [75000, 0.00065]]}}, "XRP/USDT": {"id": "XRP_USDT", "lowercaseId": null, "symbol": "XRP/USDT", "base": "XRP", "quote": "USDT", "settle": null, "baseId": "XRP", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": true, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.002, "maker": 0.002, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.001, "price": 0.001}, "limits": {"leverage": {"min": 1, "max": 20}, "amount": {"min": 0.001, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": 3, "max": 5000000}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"id": "XRP_USDT", "base": "XRP", "quote": "USDT", "leverage": "20", "min_base_amount": "0.001", "min_quote_amount": "3", "max_quote_amount": "5000000", "status": "1", "base_name": "XRP", "quote_name": "<PERSON><PERSON>", "fee": "0.2", "amount_precision": "3", "precision": "3", "trade_status": "tradable", "sell_start": "0", "buy_start": "0", "type": "normal", "trade_url": "https://www.gate.io/trade/XRP_USDT"}, "tierBased": true, "percentage": true, "feeSide": "get", "tiers": {"maker": [[0, 0.002], [1.5, 0.00185], [3, 0.00175], [6, 0.00165], [12.5, 0.00155], [25, 0.00145], [75, 0.00135], [200, 0.00125], [500, 0.00115], [1250, 0.00105], [2500, 0.00095], [3000, 0.00085], [6000, 0.00075], [11000, 0.00065], [20000, 0.00055], [40000, 0.00055], [75000, 0.00055]], "taker": [[0, 0.002], [1.5, 0.00195], [3, 0.00185], [6, 0.00175], [12.5, 0.00165], [25, 0.00155], [75, 0.00145], [200, 0.00135], [500, 0.00125], [1250, 0.00115], [2500, 0.00105], [3000, 0.00095], [6000, 0.00085], [11000, 0.00075], [20000, 0.00065], [40000, 0.00065], [75000, 0.00065]]}}, "DOGE/USDT": {"id": "DOGE_USDT", "lowercaseId": null, "symbol": "DOGE/USDT", "base": "DOGE", "quote": "USDT", "settle": null, "baseId": "DOGE", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": true, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.002, "maker": 0.002, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.01, "price": 1e-05}, "limits": {"leverage": {"min": 1, "max": 20}, "amount": {"min": 0.01, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": 3, "max": 5000000}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"id": "DOGE_USDT", "base": "DOGE", "quote": "USDT", "leverage": "20", "min_base_amount": "0.01", "min_quote_amount": "3", "max_quote_amount": "5000000", "status": "1", "base_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "quote_name": "<PERSON><PERSON>", "fee": "0.2", "amount_precision": "2", "precision": "5", "trade_status": "tradable", "sell_start": "0", "buy_start": "0", "type": "normal", "trade_url": "https://www.gate.io/trade/DOGE_USDT"}, "tierBased": true, "percentage": true, "feeSide": "get", "tiers": {"maker": [[0, 0.002], [1.5, 0.00185], [3, 0.00175], [6, 0.00165], [12.5, 0.00155], [25, 0.00145], [75, 0.00135], [200, 0.00125], [500, 0.00115], [1250, 0.00105], [2500, 0.00095], [3000, 0.00085], [6000, 0.00075], [11000, 0.00065], [20000, 0.00055], [40000, 0.00055], [75000, 0.00055]], "taker": [[0, 0.002], [1.5, 0.00195], [3, 0.00185], [6, 0.00175], [12.5, 0.00165], [25, 0.00155], [75, 0.00145], [200, 0.00135], [500, 0.00125], [1250, 0.00115], [2500, 0.00105], [3000, 0.00095], [6000, 0.00085], [11000, 0.00075], [20000, 0.00065], [40000, 0.00065], [75000, 0.00065]]}}, "TRX/USDT": {"id": "TRX_USDT", "lowercaseId": null, "symbol": "TRX/USDT", "base": "TRX", "quote": "USDT", "settle": null, "baseId": "TRX", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": true, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.002, "maker": 0.002, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.01, "price": 1e-05}, "limits": {"leverage": {"min": 1, "max": 20}, "amount": {"min": 0.01, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": 3, "max": 5000000}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"id": "TRX_USDT", "base": "TRX", "quote": "USDT", "leverage": "20", "min_base_amount": "0.01", "min_quote_amount": "3", "max_quote_amount": "5000000", "status": "1", "base_name": "TRON", "quote_name": "<PERSON><PERSON>", "fee": "0.2", "amount_precision": "2", "precision": "5", "trade_status": "tradable", "sell_start": "0", "buy_start": "0", "type": "normal", "trade_url": "https://www.gate.io/trade/TRX_USDT"}, "tierBased": true, "percentage": true, "feeSide": "get", "tiers": {"maker": [[0, 0.002], [1.5, 0.00185], [3, 0.00175], [6, 0.00165], [12.5, 0.00155], [25, 0.00145], [75, 0.00135], [200, 0.00125], [500, 0.00115], [1250, 0.00105], [2500, 0.00095], [3000, 0.00085], [6000, 0.00075], [11000, 0.00065], [20000, 0.00055], [40000, 0.00055], [75000, 0.00055]], "taker": [[0, 0.002], [1.5, 0.00195], [3, 0.00185], [6, 0.00175], [12.5, 0.00165], [25, 0.00155], [75, 0.00145], [200, 0.00135], [500, 0.00125], [1250, 0.00115], [2500, 0.00105], [3000, 0.00095], [6000, 0.00085], [11000, 0.00075], [20000, 0.00065], [40000, 0.00065], [75000, 0.00065]]}}, "BTC/USDT:USDT": {"id": "BTC_USDT", "lowercaseId": null, "symbol": "BTC/USDT:USDT", "base": "BTC", "quote": "USDT", "settle": "USDT", "baseId": "BTC", "quoteId": "USDT", "settleId": "usdt", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": 7.5e-06, "maker": -1e-06, "contractSize": 0.0001, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1, "price": 0.1}, "limits": {"leverage": {"min": 1, "max": 125}, "amount": {"min": 1, "max": 1000000}, "price": {"min": 99021.958, "max": 109445.322}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": 1574035200000, "info": {"funding_rate_indicative": "-0.000026", "mark_price_round": "0.01", "funding_offset": "0", "in_delisting": false, "risk_limit_base": "1000000", "interest_rate": "0.0003", "index_price": "104278.91", "order_price_round": "0.1", "order_size_min": "1", "ref_rebate_rate": "0.2", "name": "BTC_USDT", "ref_discount_rate": "0", "order_price_deviate": "0.05", "maintenance_rate": "0.004", "mark_type": "index", "funding_interval": "28800", "type": "direct", "risk_limit_step": "1499000000", "enable_bonus": true, "enable_credit": true, "leverage_min": "1", "funding_rate": "-0.000026", "last_price": "104233.9", "mark_price": "104233.64", "order_size_max": "1000000", "funding_next_apply": "1749254400", "short_users": "13635", "config_change_time": "1747732645", "create_time": "1574035200", "trade_size": "346627237303", "position_size": "341260344", "long_users": "9575", "quanto_multiplier": "0.0001", "funding_impact_value": "30000", "leverage_max": "125", "cross_leverage_default": "10", "risk_limit_max": "1500000000", "maker_fee_rate": "-0.0001", "taker_fee_rate": "0.00075", "orders_limit": "100", "trade_id": "517107976", "orderbook_id": "83761519236", "funding_cap_ratio": "0.75", "voucher_leverage": "2", "is_pre_market": false}, "tierBased": true, "percentage": true, "feeSide": "get", "tiers": {"maker": [[0, 0.002], [1.5, 0.00185], [3, 0.00175], [6, 0.00165], [12.5, 0.00155], [25, 0.00145], [75, 0.00135], [200, 0.00125], [500, 0.00115], [1250, 0.00105], [2500, 0.00095], [3000, 0.00085], [6000, 0.00075], [11000, 0.00065], [20000, 0.00055], [40000, 0.00055], [75000, 0.00055]], "taker": [[0, 0.002], [1.5, 0.00195], [3, 0.00185], [6, 0.00175], [12.5, 0.00165], [25, 0.00155], [75, 0.00145], [200, 0.00135], [500, 0.00125], [1250, 0.00115], [2500, 0.00105], [3000, 0.00095], [6000, 0.00085], [11000, 0.00075], [20000, 0.00065], [40000, 0.00065], [75000, 0.00065]]}}, "BTC/USD:BTC": {"id": "BTC_USD", "lowercaseId": null, "symbol": "BTC/USD:BTC", "base": "BTC", "quote": "USD", "settle": "BTC", "baseId": "BTC", "quoteId": "USD", "settleId": "btc", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": false, "inverse": true, "subType": "inverse", "taker": 7.5e-06, "maker": -2e-06, "contractSize": 1, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1, "price": 0.1}, "limits": {"leverage": {"min": 1, "max": 100}, "amount": {"min": 1, "max": 1000000}, "price": {"min": 52178.845, "max": 156536.535}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": 1545235200000, "info": {"funding_rate_indicative": "0.000002", "mark_price_round": "0.01", "funding_offset": "0", "in_delisting": false, "risk_limit_base": "3", "interest_rate": "0.0003", "index_price": "104357.59", "order_price_round": "0.1", "order_size_min": "1", "ref_rebate_rate": "0.2", "name": "BTC_USD", "ref_discount_rate": "0", "order_price_deviate": "0.5", "maintenance_rate": "0.005", "mark_type": "index", "funding_interval": "28800", "type": "inverse", "risk_limit_step": "2497", "enable_bonus": false, "enable_credit": true, "leverage_min": "1", "funding_rate": "0.000002", "last_price": "104216.8", "mark_price": "104357.69", "order_size_max": "1000000", "funding_next_apply": "1749254400", "short_users": "267", "config_change_time": "1732783287", "create_time": "1545235200", "trade_size": "60472017143", "position_size": "10682545", "long_users": "446", "quanto_multiplier": "0", "funding_impact_value": "0.5", "leverage_max": "100", "cross_leverage_default": "10", "risk_limit_max": "2500", "maker_fee_rate": "-0.0002", "taker_fee_rate": "0.00075", "orders_limit": "50", "trade_id": "46665241", "orderbook_id": "4964595762", "funding_cap_ratio": "0.75", "voucher_leverage": "0", "is_pre_market": false}, "tierBased": true, "percentage": true, "feeSide": "get", "tiers": {"maker": [[0, 0.002], [1.5, 0.00185], [3, 0.00175], [6, 0.00165], [12.5, 0.00155], [25, 0.00145], [75, 0.00135], [200, 0.00125], [500, 0.00115], [1250, 0.00105], [2500, 0.00095], [3000, 0.00085], [6000, 0.00075], [11000, 0.00065], [20000, 0.00055], [40000, 0.00055], [75000, 0.00055]], "taker": [[0, 0.002], [1.5, 0.00195], [3, 0.00185], [6, 0.00175], [12.5, 0.00165], [25, 0.00155], [75, 0.00145], [200, 0.00135], [500, 0.00125], [1250, 0.00115], [2500, 0.00105], [3000, 0.00095], [6000, 0.00085], [11000, 0.00075], [20000, 0.00065], [40000, 0.00065], [75000, 0.00065]]}}, "ETH/USDT:USDT": {"id": "ETH_USDT", "lowercaseId": null, "symbol": "ETH/USDT:USDT", "base": "ETH", "quote": "USDT", "settle": "USDT", "baseId": "ETH", "quoteId": "USDT", "settleId": "usdt", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": 7.5e-06, "maker": -1e-06, "contractSize": 0.01, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1, "price": 0.05}, "limits": {"leverage": {"min": 1, "max": 125}, "amount": {"min": 1, "max": 1000000}, "price": {"min": 2361.795, "max": 2610.405}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": 1574208000000, "info": {"funding_rate_indicative": "0.000005", "mark_price_round": "0.01", "funding_offset": "0", "in_delisting": false, "risk_limit_base": "200000", "interest_rate": "0.0003", "index_price": "2487.03", "order_price_round": "0.05", "order_size_min": "1", "ref_rebate_rate": "0.2", "name": "ETH_USDT", "ref_discount_rate": "0", "order_price_deviate": "0.05", "maintenance_rate": "0.004", "mark_type": "index", "funding_interval": "28800", "type": "direct", "risk_limit_step": "1499800000", "enable_bonus": true, "enable_credit": true, "leverage_min": "1", "funding_rate": "0.000005", "last_price": "2486.1", "mark_price": "2486.1", "order_size_max": "1000000", "funding_next_apply": "1749254400", "short_users": "7380", "config_change_time": "1747732645", "create_time": "1574208000", "trade_size": "66191450819", "position_size": "97225152", "long_users": "11455", "quanto_multiplier": "0.01", "funding_impact_value": "20000", "leverage_max": "125", "cross_leverage_default": "10", "risk_limit_max": "1500000000", "maker_fee_rate": "-0.0001", "taker_fee_rate": "0.00075", "orders_limit": "100", "trade_id": "476503490", "orderbook_id": "70187238100", "funding_cap_ratio": "0.75", "voucher_leverage": "2", "is_pre_market": false}, "tierBased": true, "percentage": true, "feeSide": "get", "tiers": {"maker": [[0, 0.002], [1.5, 0.00185], [3, 0.00175], [6, 0.00165], [12.5, 0.00155], [25, 0.00145], [75, 0.00135], [200, 0.00125], [500, 0.00115], [1250, 0.00105], [2500, 0.00095], [3000, 0.00085], [6000, 0.00075], [11000, 0.00065], [20000, 0.00055], [40000, 0.00055], [75000, 0.00055]], "taker": [[0, 0.002], [1.5, 0.00195], [3, 0.00185], [6, 0.00175], [12.5, 0.00165], [25, 0.00155], [75, 0.00145], [200, 0.00135], [500, 0.00125], [1250, 0.00115], [2500, 0.00105], [3000, 0.00095], [6000, 0.00085], [11000, 0.00075], [20000, 0.00065], [40000, 0.00065], [75000, 0.00065]]}}, "LTC/USDT:USDT": {"id": "LTC_USDT", "lowercaseId": null, "symbol": "LTC/USDT:USDT", "base": "LTC", "quote": "USDT", "settle": "USDT", "baseId": "LTC", "quoteId": "USDT", "settleId": "usdt", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": 7.5e-06, "maker": -1e-06, "contractSize": 0.1, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1, "price": 0.01}, "limits": {"leverage": {"min": 1, "max": 75}, "amount": {"min": 1, "max": 1000000}, "price": {"min": 78.138, "max": 95.502}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": 1577404800000, "info": {"funding_rate_indicative": "-0.000023", "mark_price_round": "0.01", "funding_offset": "0", "in_delisting": false, "risk_limit_base": "50000", "interest_rate": "0.0003", "index_price": "86.87", "order_price_round": "0.01", "order_size_min": "1", "ref_rebate_rate": "0.2", "name": "LTC_USDT", "ref_discount_rate": "0", "order_price_deviate": "0.1", "maintenance_rate": "0.0066", "mark_type": "index", "funding_interval": "28800", "type": "direct", "risk_limit_step": "49950000", "enable_bonus": true, "enable_credit": true, "leverage_min": "1", "funding_rate": "-0.000023", "last_price": "86.82", "mark_price": "86.82", "order_size_max": "1000000", "funding_next_apply": "1749254400", "short_users": "392", "config_change_time": "1747809905", "create_time": "1577404800", "trade_size": "2815141340", "position_size": "1248681", "long_users": "938", "quanto_multiplier": "0.1", "funding_impact_value": "10000", "leverage_max": "75", "cross_leverage_default": "10", "risk_limit_max": "50000000", "maker_fee_rate": "-0.0001", "taker_fee_rate": "0.00075", "orders_limit": "100", "trade_id": "48600418", "orderbook_id": "11574724525", "funding_cap_ratio": "0.75", "voucher_leverage": "2", "is_pre_market": false}, "tierBased": true, "percentage": true, "feeSide": "get", "tiers": {"maker": [[0, 0.002], [1.5, 0.00185], [3, 0.00175], [6, 0.00165], [12.5, 0.00155], [25, 0.00145], [75, 0.00135], [200, 0.00125], [500, 0.00115], [1250, 0.00105], [2500, 0.00095], [3000, 0.00085], [6000, 0.00075], [11000, 0.00065], [20000, 0.00055], [40000, 0.00055], [75000, 0.00055]], "taker": [[0, 0.002], [1.5, 0.00195], [3, 0.00185], [6, 0.00175], [12.5, 0.00165], [25, 0.00155], [75, 0.00145], [200, 0.00135], [500, 0.00125], [1250, 0.00115], [2500, 0.00105], [3000, 0.00095], [6000, 0.00085], [11000, 0.00075], [20000, 0.00065], [40000, 0.00065], [75000, 0.00065]]}}, "ADA/USDT:USDT": {"id": "ADA_USDT", "lowercaseId": null, "symbol": "ADA/USDT:USDT", "base": "ADA", "quote": "USDT", "settle": "USDT", "baseId": "ADA", "quoteId": "USDT", "settleId": "usdt", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": 7.5e-06, "maker": -1e-06, "contractSize": 10, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1, "price": 0.0001}, "limits": {"leverage": {"min": 1, "max": 75}, "amount": {"min": 1, "max": 1000000}, "price": {"min": 0.59634, "max": 0.72886}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": 1604880000000, "info": {"funding_rate_indicative": "0.0001", "mark_price_round": "0.0001", "funding_offset": "0", "in_delisting": false, "risk_limit_base": "50000", "interest_rate": "0.0003", "index_price": "0.66304", "order_price_round": "0.0001", "order_size_min": "1", "ref_rebate_rate": "0.2", "name": "ADA_USDT", "ref_discount_rate": "0", "order_price_deviate": "0.1", "maintenance_rate": "0.0066", "mark_type": "index", "funding_interval": "28800", "type": "direct", "risk_limit_step": "99950000", "enable_bonus": true, "enable_credit": true, "leverage_min": "1", "funding_rate": "0.0001", "last_price": "0.6625", "mark_price": "0.6626", "order_size_max": "1000000", "funding_next_apply": "1749254400", "short_users": "380", "config_change_time": "1746773287", "create_time": "1604880000", "trade_size": "1829649330", "position_size": "10020784", "long_users": "1090", "quanto_multiplier": "10", "funding_impact_value": "10000", "leverage_max": "75", "cross_leverage_default": "10", "risk_limit_max": "100000000", "maker_fee_rate": "-0.0001", "taker_fee_rate": "0.00075", "orders_limit": "100", "trade_id": "26563816", "orderbook_id": "11274455405", "funding_cap_ratio": "0.75", "voucher_leverage": "2", "is_pre_market": false}, "tierBased": true, "percentage": true, "feeSide": "get", "tiers": {"maker": [[0, 0.002], [1.5, 0.00185], [3, 0.00175], [6, 0.00165], [12.5, 0.00155], [25, 0.00145], [75, 0.00135], [200, 0.00125], [500, 0.00115], [1250, 0.00105], [2500, 0.00095], [3000, 0.00085], [6000, 0.00075], [11000, 0.00065], [20000, 0.00055], [40000, 0.00055], [75000, 0.00055]], "taker": [[0, 0.002], [1.5, 0.00195], [3, 0.00185], [6, 0.00175], [12.5, 0.00165], [25, 0.00155], [75, 0.00145], [200, 0.00135], [500, 0.00125], [1250, 0.00115], [2500, 0.00105], [3000, 0.00095], [6000, 0.00085], [11000, 0.00075], [20000, 0.00065], [40000, 0.00065], [75000, 0.00065]]}}, "XRP/USDT:USDT": {"id": "XRP_USDT", "lowercaseId": null, "symbol": "XRP/USDT:USDT", "base": "XRP", "quote": "USDT", "settle": "USDT", "baseId": "XRP", "quoteId": "USDT", "settleId": "usdt", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": 7.5e-06, "maker": -1e-06, "contractSize": 10, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1, "price": 0.0001}, "limits": {"leverage": {"min": 1, "max": 75}, "amount": {"min": 1, "max": 1000000}, "price": {"min": 1.95714, "max": 2.39206}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": 1577404800000, "info": {"funding_rate_indicative": "0.000052", "mark_price_round": "0.0001", "funding_offset": "0", "in_delisting": false, "risk_limit_base": "100000", "interest_rate": "0.0003", "index_price": "2.1757", "order_price_round": "0.0001", "order_size_min": "1", "ref_rebate_rate": "0.2", "name": "XRP_USDT", "ref_discount_rate": "0", "order_price_deviate": "0.1", "maintenance_rate": "0.0066", "mark_type": "index", "funding_interval": "28800", "type": "direct", "risk_limit_step": "99900000", "enable_bonus": true, "enable_credit": true, "leverage_min": "1", "funding_rate": "0.000052", "last_price": "2.1742", "mark_price": "2.1746", "order_size_max": "1000000", "funding_next_apply": "1749254400", "short_users": "654", "config_change_time": "1746773287", "create_time": "1577404800", "trade_size": "4826606213", "position_size": "9317144", "long_users": "1715", "quanto_multiplier": "10", "funding_impact_value": "10000", "leverage_max": "75", "cross_leverage_default": "10", "risk_limit_max": "100000000", "maker_fee_rate": "-0.0001", "taker_fee_rate": "0.00075", "orders_limit": "100", "trade_id": "71369449", "orderbook_id": "17352382075", "funding_cap_ratio": "0.75", "voucher_leverage": "2", "is_pre_market": false}, "tierBased": true, "percentage": true, "feeSide": "get", "tiers": {"maker": [[0, 0.002], [1.5, 0.00185], [3, 0.00175], [6, 0.00165], [12.5, 0.00155], [25, 0.00145], [75, 0.00135], [200, 0.00125], [500, 0.00115], [1250, 0.00105], [2500, 0.00095], [3000, 0.00085], [6000, 0.00075], [11000, 0.00065], [20000, 0.00055], [40000, 0.00055], [75000, 0.00055]], "taker": [[0, 0.002], [1.5, 0.00195], [3, 0.00185], [6, 0.00175], [12.5, 0.00165], [25, 0.00155], [75, 0.00145], [200, 0.00135], [500, 0.00125], [1250, 0.00115], [2500, 0.00105], [3000, 0.00095], [6000, 0.00085], [11000, 0.00075], [20000, 0.00065], [40000, 0.00065], [75000, 0.00065]]}}, "DOGE/USDT:USDT": {"id": "DOGE_USDT", "lowercaseId": null, "symbol": "DOGE/USDT:USDT", "base": "DOGE", "quote": "USDT", "settle": "USDT", "baseId": "DOGE", "quoteId": "USDT", "settleId": "usdt", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": 7.5e-06, "maker": -1e-06, "contractSize": 10, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1, "price": 1e-05}, "limits": {"leverage": {"min": 1, "max": 100}, "amount": {"min": 1, "max": 1000000}, "price": {"min": 0.161343, "max": 0.197197}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": 1594166400000, "info": {"funding_rate_indicative": "0.000017", "mark_price_round": "0.00001", "funding_offset": "0", "in_delisting": false, "risk_limit_base": "10000", "interest_rate": "0.0003", "index_price": "0.179363", "order_price_round": "0.00001", "order_size_min": "1", "ref_rebate_rate": "0.2", "name": "DOGE_USDT", "ref_discount_rate": "0", "order_price_deviate": "0.1", "maintenance_rate": "0.005", "mark_type": "index", "funding_interval": "28800", "type": "direct", "risk_limit_step": "99990000", "enable_bonus": true, "enable_credit": true, "leverage_min": "1", "funding_rate": "0.000017", "last_price": "0.17924", "mark_price": "0.17927", "order_size_max": "1000000", "funding_next_apply": "1749254400", "short_users": "986", "config_change_time": "1746773287", "create_time": "1594166400", "trade_size": "42906823297", "position_size": "119824368", "long_users": "3320", "quanto_multiplier": "10", "funding_impact_value": "10000", "leverage_max": "100", "cross_leverage_default": "10", "risk_limit_max": "100000000", "maker_fee_rate": "-0.0001", "taker_fee_rate": "0.00075", "orders_limit": "100", "trade_id": "151009098", "orderbook_id": "30494650804", "funding_cap_ratio": "0.75", "voucher_leverage": "2", "is_pre_market": false}, "tierBased": true, "percentage": true, "feeSide": "get", "tiers": {"maker": [[0, 0.002], [1.5, 0.00185], [3, 0.00175], [6, 0.00165], [12.5, 0.00155], [25, 0.00145], [75, 0.00135], [200, 0.00125], [500, 0.00115], [1250, 0.00105], [2500, 0.00095], [3000, 0.00085], [6000, 0.00075], [11000, 0.00065], [20000, 0.00055], [40000, 0.00055], [75000, 0.00055]], "taker": [[0, 0.002], [1.5, 0.00195], [3, 0.00185], [6, 0.00175], [12.5, 0.00165], [25, 0.00155], [75, 0.00145], [200, 0.00135], [500, 0.00125], [1250, 0.00115], [2500, 0.00105], [3000, 0.00095], [6000, 0.00085], [11000, 0.00075], [20000, 0.00065], [40000, 0.00065], [75000, 0.00065]]}}, "TRX/USDT:USDT": {"id": "TRX_USDT", "lowercaseId": null, "symbol": "TRX/USDT:USDT", "base": "TRX", "quote": "USDT", "settle": "USDT", "baseId": "TRX", "quoteId": "USDT", "settleId": "usdt", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": 7.5e-06, "maker": -1e-06, "contractSize": 100, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1, "price": 1e-05}, "limits": {"leverage": {"min": 1, "max": 75}, "amount": {"min": 1, "max": 1000000}, "price": {"min": 0.248967, "max": 0.304293}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": 1577404800000, "info": {"funding_rate_indicative": "-0.000139", "mark_price_round": "0.00001", "funding_offset": "0", "in_delisting": false, "risk_limit_base": "50000", "interest_rate": "0.0003", "index_price": "0.276852", "order_price_round": "0.00001", "order_size_min": "1", "ref_rebate_rate": "0.2", "name": "TRX_USDT", "ref_discount_rate": "0", "order_price_deviate": "0.1", "maintenance_rate": "0.0066", "mark_type": "index", "funding_interval": "28800", "type": "direct", "risk_limit_step": "19950000", "enable_bonus": true, "enable_credit": true, "leverage_min": "1", "funding_rate": "-0.000139", "last_price": "0.27663", "mark_price": "0.27663", "order_size_max": "1000000", "funding_next_apply": "1749254400", "short_users": "261", "config_change_time": "1749199170", "create_time": "1577404800", "trade_size": "326277810", "position_size": "203794", "long_users": "330", "quanto_multiplier": "100", "funding_impact_value": "10000", "leverage_max": "75", "cross_leverage_default": "10", "risk_limit_max": "20000000", "maker_fee_rate": "-0.0001", "taker_fee_rate": "0.00075", "orders_limit": "100", "trade_id": "7133511", "orderbook_id": "4994250543", "funding_cap_ratio": "0.75", "voucher_leverage": "0", "is_pre_market": false}, "tierBased": true, "percentage": true, "feeSide": "get", "tiers": {"maker": [[0, 0.002], [1.5, 0.00185], [3, 0.00175], [6, 0.00165], [12.5, 0.00155], [25, 0.00145], [75, 0.00135], [200, 0.00125], [500, 0.00115], [1250, 0.00105], [2500, 0.00095], [3000, 0.00085], [6000, 0.00075], [11000, 0.00065], [20000, 0.00055], [40000, 0.00055], [75000, 0.00055]], "taker": [[0, 0.002], [1.5, 0.00195], [3, 0.00185], [6, 0.00175], [12.5, 0.00165], [25, 0.00155], [75, 0.00145], [200, 0.00135], [500, 0.00125], [1250, 0.00115], [2500, 0.00105], [3000, 0.00095], [6000, 0.00085], [11000, 0.00075], [20000, 0.00065], [40000, 0.00065], [75000, 0.00065]]}}, "BTC/USDT:USDT-240329": {"id": "BTC_USDT_20240329", "symbol": "BTC/USDT:USDT-240329", "base": "BTC", "quote": "USDT", "settle": "USDT", "baseId": "BTC", "quoteId": "USDT", "settleId": "usdt", "type": "future", "spot": false, "margin": false, "swap": false, "future": true, "option": false, "active": true, "contract": true, "linear": true, "inverse": false, "taker": 2.5e-06, "maker": -1.5e-06, "contractSize": 0.0001, "expiry": 1711699200000, "expiryDatetime": "2024-03-29T08:00:00.000Z", "strike": null, "optionType": null, "precision": {"amount": 1, "price": 0.1}, "limits": {"leverage": {"min": 1, "max": 100}, "amount": {"min": 1, "max": 1000000}, "price": {"min": 19042.94, "max": 57128.82}, "cost": {"min": null, "max": null}}, "created": null}}