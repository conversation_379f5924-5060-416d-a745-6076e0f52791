{"BTC/USDT": {"id": "btc_usdt", "lowercaseId": null, "symbol": "BTC/USDT", "base": "BTC", "quote": "USDT", "settle": null, "baseId": "btc", "quoteId": "usdt", "settleId": null, "type": "spot", "spot": true, "margin": null, "swap": false, "future": false, "option": false, "index": false, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": null, "maker": null, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"price": 0.01, "amount": 1e-05, "base": null, "quote": null}, "limits": {"leverage": {"min": 1, "max": null}, "amount": {"min": null, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": 5, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"id": "614", "symbol": "btc_usdt", "displayName": "BTC/USDT", "type": "normal", "state": "ONLINE", "stateTime": "1554048000000", "tradingEnabled": true, "openapiEnabled": true, "nextStateTime": null, "nextState": null, "depthMergePrecision": "5", "baseCurrency": "btc", "baseCurrencyPrecision": "10", "baseCurrencyId": "2", "baseCurrencyLogo": "https://a.static-global.com/1/currency/2766ded1-6427-4258-85ef-3179d183c404-1732260170319.png", "quoteCurrency": "usdt", "quoteCurrencyPrecision": "8", "quoteCurrencyId": "11", "pricePrecision": "2", "quantityPrecision": "5", "orderTypes": ["LIMIT", "MARKET"], "timeInForces": ["GTC", "IOC"], "displayWeight": "10001", "displayLevel": "FULL", "plates": [282], "filters": [{"filter": "PROTECTION_LIMIT", "sellPriceLimitCoefficient": "0.8", "buyMaxDeviation": "0.8", "buyPriceLimitCoefficient": "4", "sellMaxDeviation": "4"}, {"filter": "PROTECTION_MARKET", "maxDeviation": "0.02"}, {"filter": "QUOTE_QTY", "min": "5"}], "takerFeeRate": "0.002", "makerFeeRate": "0.002"}, "tierBased": null, "percentage": null}, "LTC/USDT": {"id": "ltc_usdt", "lowercaseId": null, "symbol": "LTC/USDT", "base": "LTC", "quote": "USDT", "settle": null, "baseId": "ltc", "quoteId": "usdt", "settleId": null, "type": "spot", "spot": true, "margin": null, "swap": false, "future": false, "option": false, "index": false, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": null, "maker": null, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"price": 0.01, "amount": 0.001, "base": null, "quote": null}, "limits": {"leverage": {"min": 1, "max": null}, "amount": {"min": null, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": 5, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"id": "618", "symbol": "ltc_usdt", "displayName": "LTC/USDT", "type": "normal", "state": "ONLINE", "stateTime": "1554048000000", "tradingEnabled": true, "openapiEnabled": true, "nextStateTime": null, "nextState": null, "depthMergePrecision": "5", "baseCurrency": "ltc", "baseCurrencyPrecision": "8", "baseCurrencyId": "21", "baseCurrencyLogo": "https://a.static-global.com/1/currency/ltc.png", "quoteCurrency": "usdt", "quoteCurrencyPrecision": "8", "quoteCurrencyId": "11", "pricePrecision": "2", "quantityPrecision": "3", "orderTypes": ["LIMIT", "MARKET"], "timeInForces": ["GTC", "IOC"], "displayWeight": "9983", "displayLevel": "FULL", "plates": [], "filters": [{"filter": "PROTECTION_LIMIT", "sellPriceLimitCoefficient": "0.8", "buyMaxDeviation": "0.8", "buyPriceLimitCoefficient": "4", "sellMaxDeviation": "4"}, {"filter": "PROTECTION_MARKET", "maxDeviation": "0.02"}, {"filter": "QUOTE_QTY", "min": "5"}], "takerFeeRate": "0.002", "makerFeeRate": "0.002"}, "tierBased": null, "percentage": null}, "ADA/USDT": {"id": "ada_usdt", "lowercaseId": null, "symbol": "ADA/USDT", "base": "ADA", "quote": "USDT", "settle": null, "baseId": "ada", "quoteId": "usdt", "settleId": null, "type": "spot", "spot": true, "margin": null, "swap": false, "future": false, "option": false, "index": false, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": null, "maker": null, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"price": 0.0001, "amount": 0.1, "base": null, "quote": null}, "limits": {"leverage": {"min": 1, "max": null}, "amount": {"min": null, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": 5, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"id": "619", "symbol": "ada_usdt", "displayName": "ADA/USDT", "type": "normal", "state": "ONLINE", "stateTime": "1554048000000", "tradingEnabled": true, "openapiEnabled": true, "nextStateTime": null, "nextState": null, "depthMergePrecision": "5", "baseCurrency": "ada", "baseCurrencyPrecision": "8", "baseCurrencyId": "101", "baseCurrencyLogo": "https://a.static-global.com/1/currency/ada.png", "quoteCurrency": "usdt", "quoteCurrencyPrecision": "8", "quoteCurrencyId": "11", "pricePrecision": "4", "quantityPrecision": "1", "orderTypes": ["LIMIT", "MARKET"], "timeInForces": ["GTC", "IOC"], "displayWeight": "9995", "displayLevel": "FULL", "plates": [282], "filters": [{"filter": "PROTECTION_LIMIT", "sellPriceLimitCoefficient": "0.8", "buyMaxDeviation": "0.8", "buyPriceLimitCoefficient": "4", "sellMaxDeviation": "4"}, {"filter": "PROTECTION_MARKET", "maxDeviation": "0.02"}, {"filter": "QUOTE_QTY", "min": "5"}], "takerFeeRate": "0.002", "makerFeeRate": "0.002"}, "tierBased": null, "percentage": null}, "XRP/USDT": {"id": "xrp_usdt", "lowercaseId": null, "symbol": "XRP/USDT", "base": "XRP", "quote": "USDT", "settle": null, "baseId": "xrp", "quoteId": "usdt", "settleId": null, "type": "spot", "spot": true, "margin": null, "swap": false, "future": false, "option": false, "index": false, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": null, "maker": null, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"price": 0.0001, "amount": 1, "base": null, "quote": null}, "limits": {"leverage": {"min": 1, "max": null}, "amount": {"min": null, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": 5, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"id": "621", "symbol": "xrp_usdt", "displayName": "XRP/USDT", "type": "normal", "state": "ONLINE", "stateTime": "1554048000000", "tradingEnabled": true, "openapiEnabled": true, "nextStateTime": null, "nextState": null, "depthMergePrecision": "5", "baseCurrency": "xrp", "baseCurrencyPrecision": "8", "baseCurrencyId": "90", "baseCurrencyLogo": "https://a.static-global.com/1/currency/xrp.png", "quoteCurrency": "usdt", "quoteCurrencyPrecision": "8", "quoteCurrencyId": "11", "pricePrecision": "4", "quantityPrecision": "0", "orderTypes": ["LIMIT", "MARKET"], "timeInForces": ["GTC", "IOC"], "displayWeight": "9998", "displayLevel": "FULL", "plates": [], "filters": [{"filter": "PROTECTION_LIMIT", "sellPriceLimitCoefficient": "0.8", "buyMaxDeviation": "0.8", "buyPriceLimitCoefficient": "4", "sellMaxDeviation": "4"}, {"filter": "PROTECTION_MARKET", "maxDeviation": "0.02"}, {"filter": "QUOTE_QTY", "min": "5"}], "takerFeeRate": "0.002", "makerFeeRate": "0.002"}, "tierBased": null, "percentage": null}, "SOL/USDT": {"id": "sol_usdt", "lowercaseId": null, "symbol": "SOL/USDT", "base": "SOL", "quote": "USDT", "settle": null, "baseId": "sol", "quoteId": "usdt", "settleId": null, "type": "spot", "spot": true, "margin": null, "swap": false, "future": false, "option": false, "index": false, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": null, "maker": null, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"price": 0.01, "amount": 0.001, "base": null, "quote": null}, "limits": {"leverage": {"min": 1, "max": null}, "amount": {"min": null, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": 5, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"id": "7487", "symbol": "sol_usdt", "displayName": "SOL/USDT", "type": "normal", "state": "ONLINE", "stateTime": "1629190800000", "tradingEnabled": true, "openapiEnabled": true, "nextStateTime": null, "nextState": null, "depthMergePrecision": "5", "baseCurrency": "sol", "baseCurrencyPrecision": "8", "baseCurrencyId": "941", "baseCurrencyLogo": "https://a.static-global.com/1/currency/sol.png", "quoteCurrency": "usdt", "quoteCurrencyPrecision": "8", "quoteCurrencyId": "11", "pricePrecision": "2", "quantityPrecision": "3", "orderTypes": ["LIMIT", "MARKET"], "timeInForces": ["GTC", "IOC"], "displayWeight": "9996", "displayLevel": "FULL", "plates": [251, 282], "filters": [{"filter": "PROTECTION_LIMIT", "sellPriceLimitCoefficient": "0.8", "buyMaxDeviation": "0.8", "buyPriceLimitCoefficient": "4", "sellMaxDeviation": "4"}, {"filter": "PROTECTION_MARKET", "maxDeviation": "0.02"}, {"filter": "QUOTE_QTY", "min": "5"}], "takerFeeRate": "0.002", "makerFeeRate": "0.002"}, "tierBased": null, "percentage": null}, "TRX/USDT": {"id": "trx_usdt", "lowercaseId": null, "symbol": "TRX/USDT", "base": "TRX", "quote": "USDT", "settle": null, "baseId": "trx", "quoteId": "usdt", "settleId": null, "type": "spot", "spot": true, "margin": null, "swap": false, "future": false, "option": false, "index": false, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": null, "maker": null, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"price": 0.0001, "amount": 0.1, "base": null, "quote": null}, "limits": {"leverage": {"min": 1, "max": null}, "amount": {"min": null, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": 5, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"id": "970", "symbol": "trx_usdt", "displayName": "TRX/USDT", "type": "normal", "state": "ONLINE", "stateTime": "1557072000000", "tradingEnabled": true, "openapiEnabled": true, "nextStateTime": null, "nextState": null, "depthMergePrecision": "5", "baseCurrency": "trx", "baseCurrencyPrecision": "8", "baseCurrencyId": "73", "baseCurrencyLogo": "https://a.static-global.com/1/currency/trx.png", "quoteCurrency": "usdt", "quoteCurrencyPrecision": "8", "quoteCurrencyId": "11", "pricePrecision": "4", "quantityPrecision": "1", "orderTypes": ["LIMIT", "MARKET"], "timeInForces": ["GTC", "IOC"], "displayWeight": "9991", "displayLevel": "FULL", "plates": [], "filters": [{"filter": "PROTECTION_LIMIT", "sellPriceLimitCoefficient": "0.8", "buyMaxDeviation": "0.8", "buyPriceLimitCoefficient": "4", "sellMaxDeviation": "4"}, {"filter": "PROTECTION_MARKET", "maxDeviation": "0.02"}, {"filter": "QUOTE_QTY", "min": "5"}], "takerFeeRate": "0.002", "makerFeeRate": "0.002"}, "tierBased": null, "percentage": null}, "LTC/USDT:USDT": {"id": "ltc_usdt", "lowercaseId": null, "symbol": "LTC/USDT:USDT", "base": "LTC", "quote": "USDT", "settle": "USDT", "baseId": "ltc", "quoteId": "usdt", "settleId": "ltc", "type": "swap", "spot": false, "margin": null, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": 0.0006, "maker": 0.0004, "contractSize": 0.1, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"price": 0.01, "amount": 1, "base": 1e-08, "quote": 1e-08}, "limits": {"leverage": {"min": 1, "max": null}, "amount": {"min": 1, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": 5, "max": 15000000}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"id": "18", "symbol": "ltc_usdt", "symbolGroupId": "3", "pair": "ltc_usdt", "contractType": "PERPETUAL", "productType": "perpetual", "predictEventType": null, "predictEventParam": null, "predictEventSort": null, "underlyingType": "U_BASED", "contractSize": "0.1", "tradeSwitch": true, "openSwitch": true, "isDisplay": true, "isOpenApi": true, "state": "0", "initLeverage": "10", "initPositionType": "CROSSED", "baseCoin": "ltc", "spotCoin": "ltc", "quoteCoin": "usdt", "baseCoinPrecision": "8", "baseCoinDisplayPrecision": "2", "quoteCoinPrecision": "8", "quoteCoinDisplayPrecision": "4", "quantityPrecision": "0", "pricePrecision": "2", "supportOrderType": "LIMIT,MARKET", "supportTimeInForce": "GTC,FOK,IOC,GTX", "supportEntrustType": "TAKE_PROFIT,STOP,TAKE_PROFIT_MARKET,STOP_MARKET,TRAILING_STOP_MARKET", "supportPositionType": "CROSSED,ISOLATED", "minQty": "1", "minNotional": "5", "maxNotional": "15000000", "multiplierDown": "0.05", "multiplierUp": "0.05", "maxOpenOrders": "200", "maxEntrusts": "120", "makerFee": "0.0004", "takerFee": "0.0006", "liquidationFee": "0.0125", "marketTakeBound": "0.03", "depthPrecisionMerge": "5", "labels": [], "onboardDate": "1656057600000", "enName": "LTCUSDT ", "cnName": "LTCUSDT", "minStepPrice": "0.01", "minPrice": null, "maxPrice": null, "deliveryDate": null, "deliveryPrice": null, "deliveryCompletion": false, "cnDesc": null, "enDesc": null, "cnRemark": null, "enRemark": null, "plates": [1, 13], "fastTrackCallbackRate1": "0.01", "fastTrackCallbackRate2": "0.02", "minTrackCallbackRate": "0.001", "maxTrackCallbackRate": "0.1", "latestPriceDeviation": "0.0100", "marketOpenTakeBound": "0.030000000000000000", "marketCloseTakeBound": "0.050000000000000000", "offTime": null}, "tierBased": null, "percentage": null}, "BTC/USDT:USDT": {"id": "btc_usdt", "lowercaseId": null, "symbol": "BTC/USDT:USDT", "base": "BTC", "quote": "USDT", "settle": "USDT", "baseId": "btc", "quoteId": "usdt", "settleId": "btc", "type": "swap", "spot": false, "margin": null, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": 0.0006, "maker": 0.0004, "contractSize": 0.0001, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"price": 0.1, "amount": 1, "base": 1e-08, "quote": 1e-08}, "limits": {"leverage": {"min": 1, "max": null}, "amount": {"min": 1, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": 10, "max": 25000000}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"id": "8", "symbol": "btc_usdt", "symbolGroupId": "1", "pair": "btc_usdt", "contractType": "PERPETUAL", "productType": "perpetual", "predictEventType": null, "predictEventParam": null, "predictEventSort": null, "underlyingType": "U_BASED", "contractSize": "0.0001", "tradeSwitch": true, "openSwitch": true, "isDisplay": true, "isOpenApi": true, "state": "0", "initLeverage": "10", "initPositionType": "CROSSED", "baseCoin": "btc", "spotCoin": "btc", "quoteCoin": "usdt", "baseCoinPrecision": "8", "baseCoinDisplayPrecision": "4", "quoteCoinPrecision": "8", "quoteCoinDisplayPrecision": "4", "quantityPrecision": "0", "pricePrecision": "1", "supportOrderType": "LIMIT,MARKET", "supportTimeInForce": "GTC,FOK,IOC,GTX", "supportEntrustType": "TAKE_PROFIT,STOP,TAKE_PROFIT_MARKET,STOP_MARKET,TRAILING_STOP_MARKET", "supportPositionType": "CROSSED,ISOLATED", "minQty": "1", "minNotional": "10", "maxNotional": "25000000", "multiplierDown": "0.05", "multiplierUp": "0.05", "maxOpenOrders": "200", "maxEntrusts": "120", "makerFee": "0.0004", "takerFee": "0.0006", "liquidationFee": "0.0125", "marketTakeBound": "0.03", "depthPrecisionMerge": "5", "labels": ["HOT"], "onboardDate": "1654857001000", "enName": "BTCUSDT ", "cnName": "BTCUSDT ", "minStepPrice": "0.1", "minPrice": null, "maxPrice": null, "deliveryDate": null, "deliveryPrice": null, "deliveryCompletion": false, "cnDesc": null, "enDesc": null, "cnRemark": null, "enRemark": null, "plates": [1, 13], "fastTrackCallbackRate1": "0.01", "fastTrackCallbackRate2": "0.02", "minTrackCallbackRate": "0.001", "maxTrackCallbackRate": "0.1", "latestPriceDeviation": "0.0100", "marketOpenTakeBound": "0.030000000000000000", "marketCloseTakeBound": "0.050000000000000000", "offTime": null}, "tierBased": null, "percentage": null}, "ADA/USDT:USDT": {"id": "ada_usdt", "lowercaseId": null, "symbol": "ADA/USDT:USDT", "base": "ADA", "quote": "USDT", "settle": "USDT", "baseId": "ada", "quoteId": "usdt", "settleId": "ada", "type": "swap", "spot": false, "margin": null, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": 0.0006, "maker": 0.0004, "contractSize": 10, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"price": 0.0001, "amount": 1, "base": 1e-08, "quote": 1e-08}, "limits": {"leverage": {"min": 1, "max": null}, "amount": {"min": 1, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": 5, "max": 10000000}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"id": "27", "symbol": "ada_usdt", "symbolGroupId": "8", "pair": "ada_usdt", "contractType": "PERPETUAL", "productType": "perpetual", "predictEventType": null, "predictEventParam": null, "predictEventSort": null, "underlyingType": "U_BASED", "contractSize": "10", "tradeSwitch": true, "openSwitch": true, "isDisplay": true, "isOpenApi": true, "state": "0", "initLeverage": "10", "initPositionType": "CROSSED", "baseCoin": "ada", "spotCoin": "ada", "quoteCoin": "usdt", "baseCoinPrecision": "8", "baseCoinDisplayPrecision": "4", "quoteCoinPrecision": "8", "quoteCoinDisplayPrecision": "4", "quantityPrecision": "0", "pricePrecision": "4", "supportOrderType": "LIMIT,MARKET", "supportTimeInForce": "GTC,FOK,IOC,GTX", "supportEntrustType": "TAKE_PROFIT,STOP,TAKE_PROFIT_MARKET,STOP_MARKET,TRAILING_STOP_MARKET", "supportPositionType": "CROSSED,ISOLATED", "minQty": "1", "minNotional": "5", "maxNotional": "10000000", "multiplierDown": "0.05", "multiplierUp": "0.05", "maxOpenOrders": "200", "maxEntrusts": "200", "makerFee": "0.0004", "takerFee": "0.0006", "liquidationFee": "0.0125", "marketTakeBound": "0.08", "depthPrecisionMerge": "5", "labels": ["HOT"], "onboardDate": "1656489600000", "enName": "ADAUSDT ", "cnName": "ADAUSDT", "minStepPrice": "0.0001", "minPrice": null, "maxPrice": null, "deliveryDate": null, "deliveryPrice": null, "deliveryCompletion": false, "cnDesc": null, "enDesc": null, "cnRemark": null, "enRemark": null, "plates": [1, 9], "fastTrackCallbackRate1": "0.01", "fastTrackCallbackRate2": "0.02", "minTrackCallbackRate": "0.001", "maxTrackCallbackRate": "0.1", "latestPriceDeviation": "0.0100", "marketOpenTakeBound": "0.050000000000000000", "marketCloseTakeBound": "0.080000000000000000", "offTime": null}, "tierBased": null, "percentage": null}}