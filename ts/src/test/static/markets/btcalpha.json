{"BTC/EUR": {"id": "BTC_EUR", "symbol": "BTC/EUR", "base": "BTC", "quote": "EUR", "baseId": "BTC", "quoteId": "EUR", "active": true, "type": "spot", "spot": true, "swap": false, "future": false, "option": false, "margin": false, "contract": false, "precision": {"amount": 1e-05, "price": 0.01}, "limits": {"amount": {"min": 0.0005, "max": 1000}, "price": {"min": 0.01}, "cost": {"min": 5e-06}, "leverage": {}}, "info": {"name": "BTC_EUR", "currency1": "BTC", "currency2": "EUR", "price_precision": "2", "amount_precision": "5", "minimum_order_size": "0.00050000", "maximum_order_size": "1000.00000000", "minimum_order_value": "5.00000000", "liquidity_type": "10"}, "taker": 0.002, "maker": 0.002}, "BTC/USDT": {"id": "BTC_USDT", "symbol": "BTC/USDT", "base": "BTC", "quote": "USDT", "baseId": "BTC", "quoteId": "USDT", "active": true, "type": "spot", "spot": true, "swap": false, "future": false, "option": false, "margin": false, "contract": false, "precision": {"amount": 1e-06, "price": 0.01}, "limits": {"amount": {"min": 1e-05, "max": 10000}, "price": {"min": 0.01}, "cost": {"min": 1e-07}, "leverage": {}}, "info": {"name": "BTC_USDT", "currency1": "BTC", "currency2": "USDT", "price_precision": "2", "amount_precision": "6", "minimum_order_size": "0.00001000", "maximum_order_size": "10000.00000000", "minimum_order_value": "3.00000000", "liquidity_type": "10"}, "taker": 0.002, "maker": 0.002}, "LTC/USDT": {"id": "LTC_USDT", "symbol": "LTC/USDT", "base": "LTC", "quote": "USDT", "baseId": "LTC", "quoteId": "USDT", "active": true, "type": "spot", "spot": true, "swap": false, "future": false, "option": false, "margin": false, "contract": false, "precision": {"amount": 0.0001, "price": 0.01}, "limits": {"amount": {"min": 0.0001, "max": 100000}, "price": {"min": 0.01}, "cost": {"min": 1e-06}, "leverage": {}}, "info": {"name": "LTC_USDT", "currency1": "LTC", "currency2": "USDT", "price_precision": "2", "amount_precision": "4", "minimum_order_size": "0.00010000", "maximum_order_size": "100000.00000000", "minimum_order_value": "3.00000000", "liquidity_type": "10"}, "taker": 0.002, "maker": 0.002}, "ADA/USDT": {"id": "ADA_USDT", "symbol": "ADA/USDT", "base": "ADA", "quote": "USDT", "baseId": "ADA", "quoteId": "USDT", "active": true, "type": "spot", "spot": true, "swap": false, "future": false, "option": false, "margin": false, "contract": false, "precision": {"amount": 0.0001, "price": 1e-06}, "limits": {"amount": {"min": 1e-05, "max": 900000}, "price": {"min": 1e-06}, "cost": {"min": 1e-11}, "leverage": {}}, "info": {"name": "ADA_USDT", "currency1": "ADA", "currency2": "USDT", "price_precision": "6", "amount_precision": "4", "minimum_order_size": "0.00001000", "maximum_order_size": "900000.00000000", "minimum_order_value": "10.00000000", "liquidity_type": "10"}, "taker": 0.002, "maker": 0.002}, "XRP/USDT": {"id": "XRP_USDT", "symbol": "XRP/USDT", "base": "XRP", "quote": "USDT", "baseId": "XRP", "quoteId": "USDT", "active": true, "type": "spot", "spot": true, "swap": false, "future": false, "option": false, "margin": false, "contract": false, "precision": {"amount": 0.01, "price": 1e-05}, "limits": {"amount": {"min": 0.01, "max": 1000000}, "price": {"min": 1e-05}, "cost": {"min": 1e-07}, "leverage": {}}, "info": {"name": "XRP_USDT", "currency1": "XRP", "currency2": "USDT", "price_precision": "5", "amount_precision": "2", "minimum_order_size": "0.01000000", "maximum_order_size": "1000000.00000000", "minimum_order_value": "3.00000000", "liquidity_type": "10"}, "taker": 0.002, "maker": 0.002}, "SOL/USDT": {"id": "SOL_USDT", "symbol": "SOL/USDT", "base": "SOL", "quote": "USDT", "baseId": "SOL", "quoteId": "USDT", "active": true, "type": "spot", "spot": true, "swap": false, "future": false, "option": false, "margin": false, "contract": false, "precision": {"amount": 0.01, "price": 0.0001}, "limits": {"amount": {"min": 1e-05, "max": 900000}, "price": {"min": 0.0001}, "cost": {"min": 1e-09}, "leverage": {}}, "info": {"name": "SOL_USDT", "currency1": "SOL", "currency2": "USDT", "price_precision": "4", "amount_precision": "2", "minimum_order_size": "0.00001000", "maximum_order_size": "900000.00000000", "minimum_order_value": "3.00000000", "liquidity_type": "10"}, "taker": 0.002, "maker": 0.002}, "TRX/USDT": {"id": "TRX_USDT", "symbol": "TRX/USDT", "base": "TRX", "quote": "USDT", "baseId": "TRX", "quoteId": "USDT", "active": true, "type": "spot", "spot": true, "swap": false, "future": false, "option": false, "margin": false, "contract": false, "precision": {"amount": 0.01, "price": 1e-05}, "limits": {"amount": {"min": 1, "max": 10000000}, "price": {"min": 1e-05}, "cost": {"min": 1e-05}, "leverage": {}}, "info": {"name": "TRX_USDT", "currency1": "TRX", "currency2": "USDT", "price_precision": "5", "amount_precision": "2", "minimum_order_size": "1.00000000", "maximum_order_size": "10000000.00000000", "minimum_order_value": "3.00000000", "liquidity_type": "10"}, "taker": 0.002, "maker": 0.002}, "ETH/USDT": {"id": "ETH_USDT", "symbol": "ETH/USDT", "base": "ETH", "quote": "USDT", "baseId": "ETH", "quoteId": "USDT", "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "active": true, "contract": false, "taker": 0.002, "maker": 0.002, "precision": {"amount": 0.0001, "price": 0.01}, "limits": {"leverage": {}, "amount": {"min": 0.0001, "max": 100000}, "price": {"min": 0.01}, "cost": {"min": 1e-06}}, "info": {"name": "ETH_USDT", "currency1": "ETH", "currency2": "USDT", "price_precision": "2", "amount_precision": "4", "minimum_order_size": "0.00010000", "maximum_order_size": "100000.00000000", "minimum_order_value": "3.00000000", "liquidity_type": "10"}}}