{"BTC/USDT": {"id": "btc_usdt", "lowercaseId": false, "symbol": "BTC/USDT", "base": "BTC", "quote": "USDT", "settle": false, "baseId": "btc", "quoteId": "usdt", "settleId": false, "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "index": false, "active": true, "contract": false, "linear": false, "inverse": false, "subType": false, "taker": 0.06, "maker": 0, "contractSize": false, "expiry": false, "expiryDatetime": false, "strike": false, "optionType": false, "precision": {"amount": 1e-08, "price": 1e-06, "cost": 1}, "limits": {"leverage": {"min": false, "max": false}, "amount": {"min": 1.9e-05, "max": false}, "price": {"min": 1, "max": false}, "cost": {"min": false, "max": false}}, "created": false, "info": {"id": "btcusdt", "symbol": "BTCUSDT", "base_currency": "usdt", "traded_currency": "btc", "traded_currency_unit": "BTC", "description": "BTC/USDT", "ticker_id": "btc_usdt", "volume_precision": "0", "price_precision": "1", "price_round": "6", "pricescale": "1", "trade_min_base_currency": "1", "trade_min_traded_currency": "1.9e-5", "has_memo": false, "memo_name": false, "trade_fee_percent": "0.06", "trade_fee_percent_taker": "0.06", "trade_fee_percent_maker": "0.03", "url_logo": "https://indodax.com/v2/logo/svg/color/btc.svg", "url_logo_png": "https://indodax.com/v2/logo/png/color/btc.png", "is_maintenance": "0", "is_market_suspended": "0", "coingecko_id": "bitcoin", "cmc_id": "1"}, "tierBased": false, "percentage": true}, "BTC/IDR": {"id": "btc_idr", "lowercaseId": false, "symbol": "BTC/IDR", "base": "BTC", "quote": "IDR", "settle": false, "baseId": "btc", "quoteId": "idr", "settleId": false, "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "index": false, "active": true, "contract": false, "linear": false, "inverse": false, "subType": false, "taker": 0.2, "maker": 0, "contractSize": false, "expiry": false, "expiryDatetime": false, "strike": false, "optionType": false, "precision": {"amount": 1e-08, "price": 1e-08, "cost": 1}, "limits": {"leverage": {"min": false, "max": false}, "amount": {"min": 1.222e-05, "max": false}, "price": {"min": 10000, "max": false}, "cost": {"min": false, "max": false}}, "created": false, "info": {"id": "btcidr", "symbol": "BTCIDR", "base_currency": "idr", "traded_currency": "btc", "traded_currency_unit": "BTC", "description": "BTC/IDR", "ticker_id": "btc_idr", "volume_precision": "0", "price_precision": "1000", "price_round": "8", "pricescale": "1000", "trade_min_base_currency": "10000", "trade_min_traded_currency": "1.222e-5", "has_memo": false, "memo_name": false, "trade_fee_percent": "0.2", "trade_fee_percent_taker": "0.2", "trade_fee_percent_maker": "0.1", "url_logo": "https://indodax.com/v2/logo/svg/color/btc.svg", "url_logo_png": "https://indodax.com/v2/logo/png/color/btc.png", "is_maintenance": "0", "is_market_suspended": "0", "coingecko_id": "bitcoin", "cmc_id": "1"}, "tierBased": false, "percentage": true}, "ETH/USDT": {"id": "eth_usdt", "symbol": "ETH/USDT", "base": "ETH", "quote": "USDT", "baseId": "eth", "quoteId": "usdt", "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "active": true, "contract": false, "taker": 0.06, "maker": 0, "precision": {"amount": 1e-08, "price": 1e-06, "cost": 1}, "limits": {"leverage": {}, "amount": {"min": 0.000254}, "price": {"min": 1}, "cost": {}}, "info": {"id": "<PERSON><PERSON><PERSON>", "symbol": "ETHUSDT", "base_currency": "usdt", "traded_currency": "eth", "traded_currency_unit": "ETH", "description": "ETH/USDT", "ticker_id": "eth_usdt", "volume_precision": "0", "price_precision": "1", "price_round": "6", "pricescale": "1", "trade_min_base_currency": "1", "trade_min_traded_currency": "0.000254", "has_memo": false, "memo_name": false, "trade_fee_percent": "0.06", "trade_fee_percent_taker": "0.06", "trade_fee_percent_maker": "0.03", "url_logo": "https://indodax.com/v2/logo/svg/color/eth.svg", "url_logo_png": "https://indodax.com/v2/logo/png/color/eth.png", "is_maintenance": "0", "is_market_suspended": "0", "coingecko_id": "ethereum", "cmc_id": "1027"}, "tierBased": false, "percentage": true}}