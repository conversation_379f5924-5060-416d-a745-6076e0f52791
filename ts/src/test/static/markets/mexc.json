{"BTC/USDT": {"id": "BTCUSDT", "symbol": "BTC/USDT", "base": "BTC", "quote": "USDT", "baseId": "BTC", "quoteId": "USDT", "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "active": false, "contract": false, "taker": 0.0002, "maker": 0, "precision": {"amount": 1e-06, "price": 0.01}, "limits": {"leverage": {}, "amount": {"min": 1e-06}, "price": {}, "cost": {"min": 1, "max": 2000000}}, "marginModes": {}, "info": {"symbol": "BTCUSDT", "status": "1", "baseAsset": "BTC", "baseAssetPrecision": "6", "quoteAsset": "USDT", "quotePrecision": "2", "quoteAssetPrecision": "2", "baseCommissionPrecision": "6", "quoteCommissionPrecision": "2", "orderTypes": ["LIMIT", "MARKET", "LIMIT_MAKER"], "isSpotTradingAllowed": false, "isMarginTradingAllowed": false, "quoteAmountPrecision": "1.000000000000000000000000000000", "baseSizePrecision": "0.000001", "permissions": ["SPOT"], "filters": [], "maxQuoteAmount": "2000000.000000000000000000000000000000", "makerCommission": "0", "takerCommission": "0.0002", "quoteAmountPrecisionMarket": "1.000000000000000000000000000000", "maxQuoteAmountMarket": "200000.000000000000000000000000000000", "fullName": "Bitcoin", "tradeSideType": "1"}, "tierBased": false, "percentage": true}, "LTC/USDT": {"id": "LTCUSDT", "symbol": "LTC/USDT", "base": "LTC", "quote": "USDT", "baseId": "LTC", "quoteId": "USDT", "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "active": false, "contract": false, "taker": 0.0002, "maker": 0, "precision": {"amount": 0.0001, "price": 0.01}, "limits": {"leverage": {}, "amount": {"min": 0.001}, "price": {}, "cost": {"min": 1, "max": 2000000}}, "marginModes": {}, "info": {"symbol": "LTCUSDT", "status": "1", "baseAsset": "LTC", "baseAssetPrecision": "4", "quoteAsset": "USDT", "quotePrecision": "2", "quoteAssetPrecision": "2", "baseCommissionPrecision": "4", "quoteCommissionPrecision": "2", "orderTypes": ["LIMIT", "MARKET", "LIMIT_MAKER"], "isSpotTradingAllowed": false, "isMarginTradingAllowed": false, "quoteAmountPrecision": "1.000000000000000000000000000000", "baseSizePrecision": "0.001", "permissions": ["SPOT"], "filters": [], "maxQuoteAmount": "2000000.000000000000000000000000000000", "makerCommission": "0", "takerCommission": "0.0002", "quoteAmountPrecisionMarket": "1.000000000000000000000000000000", "maxQuoteAmountMarket": "100000.000000000000000000000000000000", "fullName": "Litecoin", "tradeSideType": "1"}, "tierBased": false, "percentage": true}, "ADA/USDT": {"id": "ADAUSDT", "lowercaseId": null, "symbol": "ADA/USDT", "base": "ADA", "quote": "USDT", "settle": null, "baseId": "ADA", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.0005, "maker": 0, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.01, "price": 0.0001}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 0.1, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": 1, "max": 2000000}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"symbol": "ADAUSDT", "status": "1", "baseAsset": "ADA", "baseAssetPrecision": "2", "quoteAsset": "USDT", "quotePrecision": "4", "quoteAssetPrecision": "4", "baseCommissionPrecision": "2", "quoteCommissionPrecision": "4", "orderTypes": ["LIMIT", "MARKET", "LIMIT_MAKER"], "isSpotTradingAllowed": true, "isMarginTradingAllowed": false, "quoteAmountPrecision": "1", "baseSizePrecision": "0.1", "permissions": ["SPOT"], "filters": [], "maxQuoteAmount": "2000000", "makerCommission": "0", "takerCommission": "0.0005", "quoteAmountPrecisionMarket": "1", "maxQuoteAmountMarket": "2000000", "fullName": "Cardano", "tradeSideType": "1", "st": false}, "tierBased": false, "percentage": true}, "XRP/USDT": {"id": "XRPUSDT", "symbol": "XRP/USDT", "base": "XRP", "quote": "USDT", "baseId": "XRP", "quoteId": "USDT", "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "active": false, "contract": false, "taker": 0, "maker": 0, "precision": {"amount": 0.01, "price": 0.0001}, "limits": {"leverage": {}, "amount": {"min": 0.1}, "price": {}, "cost": {"min": 1, "max": 2000000}}, "marginModes": {}, "info": {"symbol": "XRPUSDT", "status": "1", "baseAsset": "XRP", "baseAssetPrecision": "2", "quoteAsset": "USDT", "quotePrecision": "4", "quoteAssetPrecision": "4", "baseCommissionPrecision": "2", "quoteCommissionPrecision": "4", "orderTypes": ["LIMIT", "MARKET", "LIMIT_MAKER"], "isSpotTradingAllowed": false, "isMarginTradingAllowed": false, "quoteAmountPrecision": "1.000000000000000000000000000000", "baseSizePrecision": "0.1", "permissions": ["SPOT"], "filters": [], "maxQuoteAmount": "2000000.000000000000000000000000000000", "makerCommission": "0", "takerCommission": "0", "quoteAmountPrecisionMarket": "1.000000000000000000000000000000", "maxQuoteAmountMarket": "200000.000000000000000000000000000000", "fullName": "XRP", "tradeSideType": "1"}, "tierBased": false, "percentage": true}, "SOL/USDT": {"id": "SOLUSDT", "symbol": "SOL/USDT", "base": "SOL", "quote": "USDT", "baseId": "SOL", "quoteId": "USDT", "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "active": false, "contract": false, "taker": 0.0002, "maker": 0, "precision": {"amount": 0.01, "price": 0.01}, "limits": {"leverage": {}, "amount": {"min": 0.01}, "price": {}, "cost": {"min": 1, "max": 2000000}}, "marginModes": {}, "info": {"symbol": "SOLUSDT", "status": "1", "baseAsset": "SOL", "baseAssetPrecision": "2", "quoteAsset": "USDT", "quotePrecision": "2", "quoteAssetPrecision": "2", "baseCommissionPrecision": "2", "quoteCommissionPrecision": "2", "orderTypes": ["LIMIT", "MARKET", "LIMIT_MAKER"], "isSpotTradingAllowed": false, "isMarginTradingAllowed": false, "quoteAmountPrecision": "1.000000000000000000000000000000", "baseSizePrecision": "0.01", "permissions": ["SPOT"], "filters": [], "maxQuoteAmount": "2000000.000000000000000000000000000000", "makerCommission": "0", "takerCommission": "0.0002", "quoteAmountPrecisionMarket": "1.000000000000000000000000000000", "maxQuoteAmountMarket": "100000.000000000000000000000000000000", "fullName": "Solana", "tradeSideType": "1"}, "tierBased": false, "percentage": true}, "TRX/USDT": {"id": "TRXUSDT", "symbol": "TRX/USDT", "base": "TRX", "quote": "USDT", "baseId": "TRX", "quoteId": "USDT", "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "active": false, "contract": false, "taker": 0.0002, "maker": 0, "precision": {"amount": 0.01, "price": 0.0001}, "limits": {"leverage": {}, "amount": {"min": 1}, "price": {}, "cost": {"min": 1, "max": 2000000}}, "marginModes": {}, "info": {"symbol": "TRXUSDT", "status": "1", "baseAsset": "TRX", "baseAssetPrecision": "2", "quoteAsset": "USDT", "quotePrecision": "4", "quoteAssetPrecision": "4", "baseCommissionPrecision": "2", "quoteCommissionPrecision": "4", "orderTypes": ["LIMIT", "MARKET", "LIMIT_MAKER"], "isSpotTradingAllowed": false, "isMarginTradingAllowed": false, "quoteAmountPrecision": "1.000000000000000000000000000000", "baseSizePrecision": "1", "permissions": ["SPOT"], "filters": [], "maxQuoteAmount": "2000000.000000000000000000000000000000", "makerCommission": "0", "takerCommission": "0.0002", "quoteAmountPrecisionMarket": "1.000000000000000000000000000000", "maxQuoteAmountMarket": "100000.000000000000000000000000000000", "fullName": "Tron", "tradeSideType": "1"}, "tierBased": false, "percentage": true}, "LTC/USDT:USDT": {"id": "LTC_USDT", "symbol": "LTC/USDT:USDT", "base": "LTC", "quote": "USDT", "settle": "USDT", "baseId": "LTC", "quoteId": "USDT", "settleId": "USDT", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": 0.0002, "maker": 0, "contractSize": 0.01, "precision": {"amount": 1, "price": 0.01}, "limits": {"leverage": {"min": 1, "max": 200}, "amount": {"min": 1, "max": 1600000}, "price": {}, "cost": {}}, "marginModes": {}, "info": {"symbol": "LTC_USDT", "displayName": "LTC_USDT永续", "displayNameEn": "LTC_USDT PERPETUAL", "positionOpenType": "3", "baseCoin": "LTC", "quoteCoin": "USDT", "baseCoinName": "LTC", "quoteCoinName": "USDT", "futureType": "1", "settleCoin": "USDT", "contractSize": "0.01", "minLeverage": "1", "maxLeverage": "200", "priceScale": "2", "volScale": "0", "amountScale": "4", "priceUnit": "0.01", "volUnit": "1", "minVol": "1", "maxVol": "1600000", "bidLimitPriceRate": "0.2", "askLimitPriceRate": "0.2", "takerFeeRate": "0.0002", "makerFeeRate": "0", "maintenanceMarginRate": "0.004", "initialMarginRate": "0.005", "riskBaseVol": "1600000", "riskIncrVol": "1600000", "riskLongShortSwitch": "0", "riskIncrMmr": "0.004", "riskIncrImr": "0.004", "riskLevelLimit": "1", "priceCoefficientVariation": "0.2", "indexOrigin": ["BYBIT", "BINANCE", "OKX", "MEXC"], "state": "0", "isNew": false, "isHot": false, "isHidden": false, "conceptPlate": ["mc-trade-zone-pow"], "riskLimitType": "BY_VOLUME", "maxNumOrders": [200, 50], "marketOrderMaxLevel": "20", "marketOrderPriceLimitRate1": "0.2", "marketOrderPriceLimitRate2": "0.005", "triggerProtect": "0.1", "appraisal": "0", "showAppraisalCountdown": "0", "automaticDelivery": "0", "apiAllowed": false, "depthStepList": ["0.01", "0.1", "1", "10"], "limitMaxVol": "1600000", "threshold": "0", "baseCoinIconUrl": "", "id": "14", "vid": "128f589271cb4951b03e71e6323eb7be", "baseCoinId": "e9b47a809bd1456fa1d378f0d7db14ec", "createTime": "1592213599000", "openingTime": "0", "openingCountdownOption": "1"}, "tierBased": false, "percentage": true}, "BTC/USDT:USDT": {"id": "BTC_USDT", "symbol": "BTC/USDT:USDT", "base": "BTC", "quote": "USDT", "settle": "USDT", "baseId": "BTC", "quoteId": "USDT", "settleId": "USDT", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": 0.0002, "maker": 0, "contractSize": 0.0001, "precision": {"amount": 1, "price": 0.1}, "limits": {"leverage": {"min": 1, "max": 200}, "amount": {"min": 1, "max": 530000}, "price": {}, "cost": {}}, "marginModes": {}, "info": {"symbol": "BTC_USDT", "displayName": "BTC_USDT永续", "displayNameEn": "BTC_USDT PERPETUAL", "positionOpenType": "3", "baseCoin": "BTC", "quoteCoin": "USDT", "baseCoinName": "BTC", "quoteCoinName": "USDT", "futureType": "1", "settleCoin": "USDT", "contractSize": "0.0001", "minLeverage": "1", "maxLeverage": "200", "priceScale": "1", "volScale": "0", "amountScale": "4", "priceUnit": "0.1", "volUnit": "1", "minVol": "1", "maxVol": "530000", "bidLimitPriceRate": "0.1", "askLimitPriceRate": "0.1", "takerFeeRate": "0.0002", "makerFeeRate": "0", "maintenanceMarginRate": "0.004", "initialMarginRate": "0.005", "riskBaseVol": "530000", "riskIncrVol": "530000", "riskLongShortSwitch": "0", "riskIncrMmr": "0.004", "riskIncrImr": "0.004", "riskLevelLimit": "1", "priceCoefficientVariation": "0.004", "indexOrigin": ["BITGET", "BYBIT", "BINANCE", "HTX", "OKX", "MEXC", "KUCOIN"], "state": "0", "isNew": false, "isHot": false, "isHidden": false, "conceptPlate": ["mc-trade-zone-pow"], "riskLimitType": "BY_VOLUME", "maxNumOrders": [200, 50], "marketOrderMaxLevel": "20", "marketOrderPriceLimitRate1": "0.2", "marketOrderPriceLimitRate2": "0.005", "triggerProtect": "0.1", "appraisal": "0", "showAppraisalCountdown": "0", "automaticDelivery": "0", "apiAllowed": false, "depthStepList": ["0.1", "1", "10", "100"], "limitMaxVol": "530000", "threshold": "0", "baseCoinIconUrl": "https://public.mocortech.com/coin/F20231101111813257Xc6zrYWjKjAiNW.png", "id": "10", "vid": "128f589271cb4951b03e71e6323eb7be", "baseCoinId": "febc9973be4d4d53bb374476239eb219", "createTime": "1591242684000", "openingTime": "0", "openingCountdownOption": "1"}, "tierBased": false, "percentage": true}, "ADA/USDT:USDT": {"id": "ADA_USDT", "symbol": "ADA/USDT:USDT", "base": "ADA", "quote": "USDT", "settle": "USDT", "baseId": "ADA", "quoteId": "USDT", "settleId": "USDT", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": 0.0002, "maker": 0, "contractSize": 1, "precision": {"amount": 1, "price": 0.0001}, "limits": {"leverage": {"min": 1, "max": 200}, "amount": {"min": 1, "max": 2600000}, "price": {}, "cost": {}}, "marginModes": {}, "info": {"symbol": "ADA_USDT", "displayName": "ADA_USDT永续", "displayNameEn": "ADA_USDT PERPETUAL", "positionOpenType": "3", "baseCoin": "ADA", "quoteCoin": "USDT", "baseCoinName": "ADA", "quoteCoinName": "USDT", "futureType": "1", "settleCoin": "USDT", "contractSize": "1", "minLeverage": "1", "maxLeverage": "200", "priceScale": "4", "volScale": "0", "amountScale": "4", "priceUnit": "0.0001", "volUnit": "1", "minVol": "1", "maxVol": "2600000", "bidLimitPriceRate": "0.2", "askLimitPriceRate": "0.2", "takerFeeRate": "0.0002", "makerFeeRate": "0", "maintenanceMarginRate": "0.004", "initialMarginRate": "0.005", "riskBaseVol": "2600000", "riskIncrVol": "2600000", "riskLongShortSwitch": "0", "riskIncrMmr": "0.004", "riskIncrImr": "0.004", "riskLevelLimit": "1", "priceCoefficientVariation": "0.2", "indexOrigin": ["BITGET", "BYBIT", "BINANCE", "OKX", "MEXC", "KUCOIN"], "state": "0", "isNew": false, "isHot": false, "isHidden": false, "conceptPlate": [], "riskLimitType": "BY_VOLUME", "maxNumOrders": [200, 50], "marketOrderMaxLevel": "20", "marketOrderPriceLimitRate1": "0.2", "marketOrderPriceLimitRate2": "0.005", "triggerProtect": "0.1", "appraisal": "0", "showAppraisalCountdown": "0", "automaticDelivery": "0", "apiAllowed": false, "depthStepList": ["0.0001", "0.001", "0.01"], "limitMaxVol": "2600000", "threshold": "0", "baseCoinIconUrl": "", "id": "27", "vid": "128f589271cb4951b03e71e6323eb7be", "baseCoinId": "2e42b66d3b774954b26146f7f2055b5c", "createTime": "1594213380000", "openingTime": "0", "openingCountdownOption": "1"}, "tierBased": false, "percentage": true}, "XRP/USDT:USDT": {"id": "XRP_USDT", "symbol": "XRP/USDT:USDT", "base": "XRP", "quote": "USDT", "settle": "USDT", "baseId": "XRP", "quoteId": "USDT", "settleId": "USDT", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": 0.0002, "maker": 0, "contractSize": 1, "precision": {"amount": 1, "price": 0.0001}, "limits": {"leverage": {"min": 1, "max": 200}, "amount": {"min": 1, "max": 1050000}, "price": {}, "cost": {}}, "marginModes": {}, "info": {"symbol": "XRP_USDT", "displayName": "XRP_USDT永续", "displayNameEn": "XRP_USDT PERPETUAL", "positionOpenType": "3", "baseCoin": "XRP", "quoteCoin": "USDT", "baseCoinName": "XRP", "quoteCoinName": "USDT", "futureType": "1", "settleCoin": "USDT", "contractSize": "1", "minLeverage": "1", "maxLeverage": "200", "priceScale": "4", "volScale": "0", "amountScale": "4", "priceUnit": "0.0001", "volUnit": "1", "minVol": "1", "maxVol": "1050000", "bidLimitPriceRate": "0.2", "askLimitPriceRate": "0.2", "takerFeeRate": "0.0002", "makerFeeRate": "0", "maintenanceMarginRate": "0.004", "initialMarginRate": "0.005", "riskBaseVol": "1050000", "riskIncrVol": "1050000", "riskLongShortSwitch": "0", "riskIncrMmr": "0.004", "riskIncrImr": "0.004", "riskLevelLimit": "1", "priceCoefficientVariation": "0.2", "indexOrigin": ["BITGET", "BYBIT", "BINANCE", "HTX", "OKX", "MEXC", "KUCOIN"], "state": "0", "isNew": false, "isHot": true, "isHidden": false, "conceptPlate": [], "riskLimitType": "BY_VOLUME", "maxNumOrders": [200, 50], "marketOrderMaxLevel": "20", "marketOrderPriceLimitRate1": "0.2", "marketOrderPriceLimitRate2": "0.005", "triggerProtect": "0.1", "appraisal": "0", "showAppraisalCountdown": "0", "automaticDelivery": "0", "apiAllowed": false, "depthStepList": ["0.0001", "0.001", "0.01", "0.1"], "limitMaxVol": "1050000", "threshold": "0", "baseCoinIconUrl": "", "id": "16", "vid": "128f589271cb4951b03e71e6323eb7be", "baseCoinId": "a7ceba5c75644a82ad2ccc1069f7a177", "createTime": "1592559058000", "openingTime": "0", "openingCountdownOption": "1"}, "tierBased": false, "percentage": true}, "TRX/USDT:USDT": {"id": "TRX_USDT", "symbol": "TRX/USDT:USDT", "base": "TRX", "quote": "USDT", "settle": "USDT", "baseId": "TRX", "quoteId": "USDT", "settleId": "USDT", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": 0.0002, "maker": 0, "contractSize": 10, "precision": {"amount": 1, "price": 1e-05}, "limits": {"leverage": {"min": 1, "max": 200}, "amount": {"min": 1, "max": 200000}, "price": {}, "cost": {}}, "marginModes": {}, "info": {"symbol": "TRX_USDT", "displayName": "TRX_USDT永续", "displayNameEn": "TRX_USDT PERPETUAL", "positionOpenType": "3", "baseCoin": "TRX", "quoteCoin": "USDT", "baseCoinName": "TRX", "quoteCoinName": "USDT", "futureType": "1", "settleCoin": "USDT", "contractSize": "10", "minLeverage": "1", "maxLeverage": "200", "priceScale": "5", "volScale": "0", "amountScale": "4", "priceUnit": "0.00001", "volUnit": "1", "minVol": "1", "maxVol": "200000", "bidLimitPriceRate": "0.2", "askLimitPriceRate": "0.2", "takerFeeRate": "0.0002", "makerFeeRate": "0", "maintenanceMarginRate": "0.004", "initialMarginRate": "0.005", "riskBaseVol": "200000", "riskIncrVol": "200000", "riskLongShortSwitch": "0", "riskIncrMmr": "0.004", "riskIncrImr": "0.004", "riskLevelLimit": "1", "priceCoefficientVariation": "0.2", "indexOrigin": ["BITGET", "BYBIT", "BINANCE", "HTX", "OKX", "MEXC", "KUCOIN"], "state": "0", "isNew": false, "isHot": false, "isHidden": false, "conceptPlate": [], "riskLimitType": "BY_VOLUME", "maxNumOrders": [200, 50], "marketOrderMaxLevel": "20", "marketOrderPriceLimitRate1": "0.2", "marketOrderPriceLimitRate2": "0.005", "triggerProtect": "0.1", "appraisal": "0", "showAppraisalCountdown": "0", "automaticDelivery": "0", "apiAllowed": false, "depthStepList": ["0.00001", "0.0001", "0.001"], "limitMaxVol": "200000", "threshold": "0", "baseCoinIconUrl": "", "id": "55", "vid": "128f589271cb4951b03e71e6323eb7be", "baseCoinId": "fc3c087384814b5d93a6b71f64591194", "createTime": "1601812282000", "openingTime": "0", "openingCountdownOption": "1"}, "tierBased": false, "percentage": true}, "ATLAS/USDT": {"id": "ATLASUSDT", "symbol": "ATLAS/USDT", "base": "ATLAS", "quote": "USDT", "baseId": "ATLAS", "quoteId": "USDT", "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "active": true, "contract": false, "taker": 0.0002, "maker": 0, "precision": {"amount": 0.01, "price": 1e-06}, "limits": {"leverage": {}, "amount": {"min": 0.0001}, "price": {}, "cost": {"min": 1, "max": 2000000}}, "marginModes": {}, "info": {"symbol": "ATLASUSDT", "status": "1", "baseAsset": "ATLAS", "baseAssetPrecision": "2", "quoteAsset": "USDT", "quotePrecision": "6", "quoteAssetPrecision": "6", "baseCommissionPrecision": "2", "quoteCommissionPrecision": "6", "orderTypes": ["LIMIT", "MARKET", "LIMIT_MAKER"], "isSpotTradingAllowed": true, "isMarginTradingAllowed": false, "quoteAmountPrecision": "1.000000000000000000000000000000", "baseSizePrecision": "0.0001", "permissions": ["SPOT"], "filters": [], "maxQuoteAmount": "2000000.000000000000000000000000000000", "makerCommission": "0", "takerCommission": "0.0002", "quoteAmountPrecisionMarket": "1.000000000000000000000000000000", "maxQuoteAmountMarket": "100000.000000000000000000000000000000", "fullName": "Star Atlas", "tradeSideType": "1"}, "tierBased": false, "percentage": true}, "ETH/USDT": {"id": "ETHUSDT", "symbol": "ETH/USDT", "base": "ETH", "quote": "USDT", "baseId": "ETH", "quoteId": "USDT", "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "active": false, "contract": false, "taker": 0.0002, "maker": 0, "precision": {"amount": 1e-05, "price": 0.01}, "limits": {"leverage": {}, "amount": {"min": 0.0001}, "price": {}, "cost": {"min": 1, "max": 2000000}}, "marginModes": {}, "info": {"symbol": "ETHUSDT", "status": "1", "baseAsset": "ETH", "baseAssetPrecision": "5", "quoteAsset": "USDT", "quotePrecision": "2", "quoteAssetPrecision": "2", "baseCommissionPrecision": "5", "quoteCommissionPrecision": "2", "orderTypes": ["LIMIT", "MARKET", "LIMIT_MAKER"], "isSpotTradingAllowed": false, "isMarginTradingAllowed": false, "quoteAmountPrecision": "1.000000000000000000000000000000", "baseSizePrecision": "0.0001", "permissions": ["SPOT"], "filters": [], "maxQuoteAmount": "2000000.000000000000000000000000000000", "makerCommission": "0", "takerCommission": "0.0002", "quoteAmountPrecisionMarket": "1.000000000000000000000000000000", "maxQuoteAmountMarket": "200000.000000000000000000000000000000", "fullName": "Ethereum", "tradeSideType": "1"}, "tierBased": false, "percentage": true}, "ETH/USDT:USDT": {"id": "ETH_USDT", "symbol": "ETH/USDT:USDT", "base": "ETH", "quote": "USDT", "settle": "USDT", "baseId": "ETH", "quoteId": "USDT", "settleId": "USDT", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": 0.0002, "maker": 0, "contractSize": 0.01, "precision": {"amount": 1, "price": 0.01}, "limits": {"leverage": {"min": 1, "max": 200}, "amount": {"min": 1, "max": 82000}, "price": {}, "cost": {}}, "marginModes": {}, "info": {"symbol": "ETH_USDT", "displayName": "ETH_USDT永续", "displayNameEn": "ETH_USDT PERPETUAL", "positionOpenType": "3", "baseCoin": "ETH", "quoteCoin": "USDT", "baseCoinName": "ETH", "quoteCoinName": "USDT", "futureType": "1", "settleCoin": "USDT", "contractSize": "0.01", "minLeverage": "1", "maxLeverage": "200", "priceScale": "2", "volScale": "0", "amountScale": "4", "priceUnit": "0.01", "volUnit": "1", "minVol": "1", "maxVol": "82000", "bidLimitPriceRate": "0.1", "askLimitPriceRate": "0.1", "takerFeeRate": "0.0002", "makerFeeRate": "0", "maintenanceMarginRate": "0.004", "initialMarginRate": "0.005", "riskBaseVol": "82000", "riskIncrVol": "82000", "riskLongShortSwitch": "0", "riskIncrMmr": "0.004", "riskIncrImr": "0.004", "riskLevelLimit": "1", "priceCoefficientVariation": "0.004", "indexOrigin": ["BITGET", "BYBIT", "BINANCE", "HTX", "OKX", "MEXC", "KUCOIN"], "state": "0", "isNew": false, "isHot": false, "isHidden": false, "conceptPlate": [], "riskLimitType": "BY_VOLUME", "maxNumOrders": [200, 50], "marketOrderMaxLevel": "20", "marketOrderPriceLimitRate1": "0.2", "marketOrderPriceLimitRate2": "0.005", "triggerProtect": "0.1", "appraisal": "0", "showAppraisalCountdown": "0", "automaticDelivery": "0", "apiAllowed": false, "depthStepList": ["0.01", "0.1", "1", "10"], "limitMaxVol": "82000", "threshold": "0", "baseCoinIconUrl": "", "id": "11", "vid": "128f589271cb4951b03e71e6323eb7be", "baseCoinId": "93c38b0169214f8689763ce9a63a73ff", "createTime": "1591243107000", "openingTime": "0", "openingCountdownOption": "1"}, "tierBased": false, "percentage": true}, "SOL/USDT:USDT": {"id": "SOL_USDT", "symbol": "SOL/USDT:USDT", "base": "SOL", "quote": "USDT", "settle": "USDT", "baseId": "SOL", "quoteId": "USDT", "settleId": "USDT", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": 0.0002, "maker": 0, "contractSize": 0.1, "precision": {"amount": 1, "price": 0.001}, "limits": {"leverage": {"min": 1, "max": 200}, "amount": {"min": 1, "max": 200000}, "price": {}, "cost": {}}, "marginModes": {}, "info": {"symbol": "SOL_USDT", "displayName": "SOL_USDT永续", "displayNameEn": "SOL_USDT PERPETUAL", "positionOpenType": "3", "baseCoin": "SOL", "quoteCoin": "USDT", "baseCoinName": "SOL", "quoteCoinName": "USDT", "futureType": "1", "settleCoin": "USDT", "contractSize": "0.1", "minLeverage": "1", "maxLeverage": "200", "priceScale": "3", "volScale": "0", "amountScale": "4", "priceUnit": "0.001", "volUnit": "1", "minVol": "1", "maxVol": "200000", "bidLimitPriceRate": "0.2", "askLimitPriceRate": "0.2", "takerFeeRate": "0.0002", "makerFeeRate": "0", "maintenanceMarginRate": "0.004", "initialMarginRate": "0.005", "riskBaseVol": "200000", "riskIncrVol": "200000", "riskLongShortSwitch": "0", "riskIncrMmr": "0.004", "riskIncrImr": "0.004", "riskLevelLimit": "1", "priceCoefficientVariation": "0.2", "indexOrigin": ["BITGET", "BYBIT", "BINANCE", "HTX", "OKX", "MEXC", "KUCOIN"], "state": "0", "isNew": false, "isHot": true, "isHidden": false, "conceptPlate": ["mc-trade-zone-SOL"], "riskLimitType": "BY_VOLUME", "maxNumOrders": [200, 50], "marketOrderMaxLevel": "20", "marketOrderPriceLimitRate1": "0.2", "marketOrderPriceLimitRate2": "0.005", "triggerProtect": "0.1", "appraisal": "0", "showAppraisalCountdown": "0", "automaticDelivery": "0", "apiAllowed": false, "depthStepList": ["0.001", "0.01", "0.1", "1"], "limitMaxVol": "200000", "threshold": "0", "baseCoinIconUrl": "", "id": "51", "vid": "128f589271cb4951b03e71e6323eb7be", "baseCoinId": "8dedc95886d14867ad9a1668a38666db", "createTime": "1601014066000", "openingTime": "0", "openingCountdownOption": "1"}, "tierBased": false, "percentage": true}, "PYTH/USDT": {"id": "PYTHUSDT", "symbol": "PYTH/USDT", "base": "PYTH", "quote": "USDT", "baseId": "PYTH", "quoteId": "USDT", "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "active": true, "contract": false, "taker": 0.0002, "maker": 0, "precision": {"amount": 0.01, "price": 0.0001}, "limits": {"leverage": {}, "amount": {"min": 0}, "price": {}, "cost": {"min": 1, "max": 2000000}}, "marginModes": {}, "info": {"symbol": "PYTHUSDT", "status": "1", "baseAsset": "PYTH", "baseAssetPrecision": "2", "quoteAsset": "USDT", "quotePrecision": "4", "quoteAssetPrecision": "4", "baseCommissionPrecision": "2", "quoteCommissionPrecision": "4", "orderTypes": ["LIMIT", "MARKET", "LIMIT_MAKER"], "isSpotTradingAllowed": true, "isMarginTradingAllowed": false, "quoteAmountPrecision": "1.000000000000000000000000000000", "baseSizePrecision": "0", "permissions": ["SPOT"], "filters": [], "maxQuoteAmount": "2000000.000000000000000000000000000000", "makerCommission": "0", "takerCommission": "0.0002", "quoteAmountPrecisionMarket": "1.000000000000000000000000000000", "maxQuoteAmountMarket": "100000.000000000000000000000000000000", "fullName": "Pyth Network", "tradeSideType": "1"}, "tierBased": false, "percentage": true}}