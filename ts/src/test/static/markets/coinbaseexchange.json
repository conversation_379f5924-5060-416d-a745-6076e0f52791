{"BTC/USDT": {"id": "BTC-USDT", "lowercaseId": null, "symbol": "BTC/USDT", "base": "BTC", "quote": "USDT", "settle": null, "baseId": "BTC", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.006, "maker": 0.004, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1e-08, "price": 0.01}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": null, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": 1, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"id": "BTC-USDT", "base_currency": "BTC", "quote_currency": "USDT", "quote_increment": "0.01", "base_increment": "0.00000001", "display_name": "BTC-USDT", "min_market_funds": "1", "margin_enabled": false, "post_only": false, "limit_only": false, "cancel_only": false, "status": "online", "status_message": "", "trading_disabled": false, "fx_stablecoin": false, "max_slippage_percentage": "0.03000000", "auction_mode": false, "high_bid_limit_percentage": ""}, "tierBased": true, "percentage": true}, "BTC/USD": {"id": "BTC-USD", "lowercaseId": null, "symbol": "BTC/USD", "base": "BTC", "quote": "USD", "settle": null, "baseId": "BTC", "quoteId": "USD", "settleId": null, "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.006, "maker": 0.004, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1e-08, "price": 0.01}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": null, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": 1, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"id": "BTC-USD", "base_currency": "BTC", "quote_currency": "USD", "quote_increment": "0.01", "base_increment": "0.00000001", "display_name": "BTC-USD", "min_market_funds": "1", "margin_enabled": false, "post_only": false, "limit_only": false, "cancel_only": false, "status": "online", "status_message": "", "trading_disabled": false, "fx_stablecoin": false, "max_slippage_percentage": "0.02000000", "auction_mode": false, "high_bid_limit_percentage": ""}, "tierBased": true, "percentage": true}, "ETH/USDT": {"id": "ETH-USDT", "lowercaseId": null, "symbol": "ETH/USDT", "base": "ETH", "quote": "USDT", "settle": null, "baseId": "ETH", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.006, "maker": 0.004, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1e-08, "price": 0.01}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": null, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": 1, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"id": "ETH-USDT", "base_currency": "ETH", "quote_currency": "USDT", "quote_increment": "0.01", "base_increment": "0.00000001", "display_name": "ETH-USDT", "min_market_funds": "1", "margin_enabled": false, "post_only": false, "limit_only": false, "cancel_only": false, "status": "online", "status_message": "", "trading_disabled": false, "fx_stablecoin": false, "max_slippage_percentage": "0.03000000", "auction_mode": false, "high_bid_limit_percentage": ""}, "tierBased": true, "percentage": true}, "ADA/USDT": {"id": "ADA-USDT", "lowercaseId": null, "symbol": "ADA/USDT", "base": "ADA", "quote": "USDT", "settle": null, "baseId": "ADA", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.006, "maker": 0.004, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.01, "price": 0.0001}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": null, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": 1, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"id": "ADA-USDT", "base_currency": "ADA", "quote_currency": "USDT", "quote_increment": "0.0001", "base_increment": "0.01", "display_name": "ADA-USDT", "min_market_funds": "1", "margin_enabled": false, "post_only": false, "limit_only": false, "cancel_only": false, "status": "online", "status_message": "", "trading_disabled": false, "fx_stablecoin": false, "max_slippage_percentage": "0.03000000", "auction_mode": false, "high_bid_limit_percentage": ""}, "tierBased": true, "percentage": true}, "XRP/USDT": {"id": "XRP-USDT", "lowercaseId": null, "symbol": "XRP/USDT", "base": "XRP", "quote": "USDT", "settle": null, "baseId": "XRP", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.006, "maker": 0.004, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1e-06, "price": 0.0001}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": null, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": 1, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"id": "XRP-USDT", "base_currency": "XRP", "quote_currency": "USDT", "quote_increment": "0.0001", "base_increment": "0.000001", "display_name": "XRP-USDT", "min_market_funds": "1", "margin_enabled": false, "post_only": false, "limit_only": false, "cancel_only": false, "status": "online", "status_message": "", "trading_disabled": false, "fx_stablecoin": false, "max_slippage_percentage": "0.05000000", "auction_mode": false, "high_bid_limit_percentage": ""}, "tierBased": true, "percentage": true}, "DOGE/USDT": {"id": "DOGE-USDT", "lowercaseId": null, "symbol": "DOGE/USDT", "base": "DOGE", "quote": "USDT", "settle": null, "baseId": "DOGE", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.006, "maker": 0.004, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.1, "price": 0.0001}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": null, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": 1, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"id": "DOGE-USDT", "base_currency": "DOGE", "quote_currency": "USDT", "quote_increment": "0.0001", "base_increment": "0.1", "display_name": "DOGE-USDT", "min_market_funds": "1", "margin_enabled": false, "post_only": false, "limit_only": false, "cancel_only": false, "status": "online", "status_message": "", "trading_disabled": false, "fx_stablecoin": false, "max_slippage_percentage": "0.03000000", "auction_mode": false, "high_bid_limit_percentage": ""}, "tierBased": true, "percentage": true}}