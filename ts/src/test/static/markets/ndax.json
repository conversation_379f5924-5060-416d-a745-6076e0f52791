{"BTC/USD": {"id": "74", "lowercaseId": null, "symbol": "BTC/USD", "base": "BTC", "quote": "USD", "settle": null, "baseId": "1", "quoteId": "6", "settleId": null, "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.0025, "maker": 0.002, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1e-08, "price": 1e-08}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 1e-08, "max": null}, "price": {"min": 1e-08, "max": null}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"OMSId": "1", "InstrumentId": "74", "Symbol": "BTCUSD", "Product1": "1", "Product1Symbol": "BTC", "Product2": "6", "Product2Symbol": "USD", "InstrumentType": "Standard", "VenueInstrumentId": "12", "VenueId": "1", "SortIndex": "0", "SessionStatus": "Running", "PreviousSessionStatus": "Stopped", "SessionStatusDateTime": "2025-03-09T06:33:26.668Z", "SelfTradePrevention": true, "QuantityIncrement": "0.0000000100000000000000000000", "PriceIncrement": "0.0000000100000000000000000000", "MinimumQuantity": "0.0000000100000000000000000000", "MinimumPrice": "0.0000000100000000000000000000", "VenueSymbol": "BTCUSD", "IsDisable": false, "MasterDataId": "0", "PriceCollarThreshold": "1.0000000000000000000000000000", "PriceCollarPercent": "0.0000000000000000000000000000", "PriceCollarEnabled": true, "PriceFloorLimit": "10000.000000000000000000000000", "PriceFloorLimitEnabled": true, "PriceCeilingLimit": "0.0000000000000000000000000000", "PriceCeilingLimitEnabled": false, "CreateWithMarketRunning": true, "AllowOnlyMarketMakerCounterParty": false, "PriceCollarIndexDifference": "0.0000000000000000000000000000", "PriceCollarConvertToOtcEnabled": false, "PriceCollarConvertToOtcClientUserId": "0", "PriceCollarConvertToOtcAccountId": "0", "PriceCollarConvertToOtcThreshold": "0.0000000000000000000000000000", "OtcConvertSizeThreshold": "0.0000000000000000000000000000", "OtcConvertSizeEnabled": false, "OtcTradesPublic": false, "PriceTier": "0"}, "tierBased": false, "percentage": true}, "LTC/USD": {"id": "79", "lowercaseId": null, "symbol": "LTC/USD", "base": "LTC", "quote": "USD", "settle": null, "baseId": "2", "quoteId": "6", "settleId": null, "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "index": null, "active": false, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.0025, "maker": 0.002, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.001, "price": 0.001}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 1, "max": null}, "price": {"min": 1, "max": null}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"OMSId": "1", "InstrumentId": "79", "Symbol": "LTCUSD", "Product1": "2", "Product1Symbol": "LTC", "Product2": "6", "Product2Symbol": "USD", "InstrumentType": "Standard", "VenueInstrumentId": "17", "VenueId": "1", "SortIndex": "0", "SessionStatus": "Stopped", "PreviousSessionStatus": "Running", "SessionStatusDateTime": "2024-10-10T20:32:31.601Z", "SelfTradePrevention": true, "QuantityIncrement": "0.0010000000000000000000000000", "PriceIncrement": "0.0010000000000000000000000000", "MinimumQuantity": "1.0000000000000000000000000000", "MinimumPrice": "1.0000000000000000000000000000", "VenueSymbol": "LTCUSD", "IsDisable": false, "MasterDataId": "0", "PriceCollarThreshold": "0.0000000000000000000000000000", "PriceCollarPercent": "0.0000000000000000000000000000", "PriceCollarEnabled": false, "PriceFloorLimit": "0.0000000000000000000000000000", "PriceFloorLimitEnabled": false, "PriceCeilingLimit": "0.0000000000000000000000000000", "PriceCeilingLimitEnabled": false, "CreateWithMarketRunning": false, "AllowOnlyMarketMakerCounterParty": false, "PriceCollarIndexDifference": "0.0000000000000000000000000000", "PriceCollarConvertToOtcEnabled": false, "PriceCollarConvertToOtcClientUserId": "0", "PriceCollarConvertToOtcAccountId": "0", "PriceCollarConvertToOtcThreshold": "0.0000000000000000000000000000", "OtcConvertSizeThreshold": "0.0000000000000000000000000000", "OtcConvertSizeEnabled": false, "OtcTradesPublic": false, "PriceTier": "0"}, "tierBased": false, "percentage": true}, "ETH/USD": {"id": "113", "lowercaseId": null, "symbol": "ETH/USD", "base": "ETH", "quote": "USD", "settle": null, "baseId": "3", "quoteId": "6", "settleId": null, "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "index": null, "active": false, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.0025, "maker": 0.002, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1e-08, "price": 1e-08}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 1e-08, "max": null}, "price": {"min": 1e-08, "max": null}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"OMSId": "1", "InstrumentId": "113", "Symbol": "ETHUSD", "Product1": "3", "Product1Symbol": "ETH", "Product2": "6", "Product2Symbol": "USD", "InstrumentType": "Standard", "VenueInstrumentId": "51", "VenueId": "1", "SortIndex": "0", "SessionStatus": "Stopped", "PreviousSessionStatus": "Running", "SessionStatusDateTime": "2024-10-10T20:32:31.755Z", "SelfTradePrevention": true, "QuantityIncrement": "0.0000000100000000000000000000", "PriceIncrement": "0.0000000100000000000000000000", "MinimumQuantity": "0.0000000100000000000000000000", "MinimumPrice": "0.0000000100000000000000000000", "VenueSymbol": "ETHUSD", "IsDisable": false, "MasterDataId": "0", "PriceCollarThreshold": "0.0000000000000000000000000000", "PriceCollarPercent": "0.0000000000000000000000000000", "PriceCollarEnabled": true, "PriceFloorLimit": "0.0000000000000000000000000000", "PriceFloorLimitEnabled": false, "PriceCeilingLimit": "0.0000000000000000000000000000", "PriceCeilingLimitEnabled": false, "CreateWithMarketRunning": false, "AllowOnlyMarketMakerCounterParty": false, "PriceCollarIndexDifference": "0.0000000000000000000000000000", "PriceCollarConvertToOtcEnabled": false, "PriceCollarConvertToOtcClientUserId": "0", "PriceCollarConvertToOtcAccountId": "0", "PriceCollarConvertToOtcThreshold": "0.0000000000000000000000000000", "OtcConvertSizeThreshold": "0.0000000000000000000000000000", "OtcConvertSizeEnabled": false, "OtcTradesPublic": false, "PriceTier": "0"}, "tierBased": false, "percentage": true}, "BTC/USDT": {"id": "82", "lowercaseId": null, "symbol": "BTC/USDT", "base": "BTC", "quote": "USDT", "settle": null, "baseId": "1", "quoteId": "16", "settleId": null, "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "index": null, "active": false, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.0025, "maker": 0.002, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1e-05, "price": 0.01}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 0.0001, "max": null}, "price": {"min": 1, "max": null}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"OMSId": "1", "InstrumentId": "82", "Symbol": "BTCUSDT", "Product1": "1", "Product1Symbol": "BTC", "Product2": "16", "Product2Symbol": "USDT", "InstrumentType": "Standard", "VenueInstrumentId": "20", "VenueId": "1", "SortIndex": "0", "SessionStatus": "Stopped", "PreviousSessionStatus": "Running", "SessionStatusDateTime": "2024-10-10T20:32:31.617Z", "SelfTradePrevention": true, "QuantityIncrement": "0.0000100000000000000000000000", "PriceIncrement": "0.0100000000000000000000000000", "MinimumQuantity": "0.0001000000000000000000000000", "MinimumPrice": "1.0000000000000000000000000000", "VenueSymbol": "BTCUSDT", "IsDisable": false, "MasterDataId": "0", "PriceCollarThreshold": "0.0000000000000000000000000000", "PriceCollarPercent": "5.0000000000000000000000000000", "PriceCollarEnabled": true, "PriceFloorLimit": "15000.000000000000000000000000", "PriceFloorLimitEnabled": false, "PriceCeilingLimit": "35000.000000000000000000000000", "PriceCeilingLimitEnabled": false, "CreateWithMarketRunning": false, "AllowOnlyMarketMakerCounterParty": false, "PriceCollarIndexDifference": "0.0000000000000000000000000000", "PriceCollarConvertToOtcEnabled": false, "PriceCollarConvertToOtcClientUserId": "0", "PriceCollarConvertToOtcAccountId": "0", "PriceCollarConvertToOtcThreshold": "0.0000000000000000000000000000", "OtcConvertSizeThreshold": "0.0000000000000000000000000000", "OtcConvertSizeEnabled": false, "OtcTradesPublic": false, "PriceTier": "0"}, "tierBased": false, "percentage": true}, "ADA/CAD": {"id": "78", "lowercaseId": null, "symbol": "ADA/CAD", "base": "ADA", "quote": "CAD", "settle": null, "baseId": "14", "quoteId": "7", "settleId": null, "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.0025, "maker": 0.002, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.1, "price": 1e-05}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 0.1, "max": null}, "price": {"min": 0.01, "max": null}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"OMSId": "1", "InstrumentId": "78", "Symbol": "ADACAD", "Product1": "14", "Product1Symbol": "ADA", "Product2": "7", "Product2Symbol": "CAD", "InstrumentType": "Standard", "VenueInstrumentId": "16", "VenueId": "0", "SortIndex": "0", "SessionStatus": "Running", "PreviousSessionStatus": "Stopped", "SessionStatusDateTime": "2025-03-09T06:33:26.868Z", "SelfTradePrevention": true, "QuantityIncrement": "0.1000000000000000000000000000", "PriceIncrement": "0.0000100000000000000000000000", "MinimumQuantity": "0.1000000000000000000000000000", "MinimumPrice": "0.0100000000000000000000000000", "VenueSymbol": "ADACAD", "IsDisable": false, "MasterDataId": "0", "PriceCollarThreshold": "0.0000000000000000000000000000", "PriceCollarPercent": "5.0000000000000000000000000000", "PriceCollarEnabled": true, "PriceFloorLimit": "1.5000000000000000000000000000", "PriceFloorLimitEnabled": false, "PriceCeilingLimit": "0.0000000000000000000000000000", "PriceCeilingLimitEnabled": false, "CreateWithMarketRunning": true, "AllowOnlyMarketMakerCounterParty": false, "PriceCollarIndexDifference": "5.0000000000000000000000000000", "PriceCollarConvertToOtcEnabled": false, "PriceCollarConvertToOtcClientUserId": "0", "PriceCollarConvertToOtcAccountId": "0", "PriceCollarConvertToOtcThreshold": "0.0000000000000000000000000000", "OtcConvertSizeThreshold": "0.0000000000000000000000000000", "OtcConvertSizeEnabled": false, "OtcTradesPublic": false, "PriceTier": "0"}, "tierBased": false, "percentage": true}}