{"BTC/USDT": {"id": "BTC-USDT", "lowercaseId": null, "symbol": "BTC/USDT", "base": "BTC", "quote": "USDT", "settle": null, "baseId": "BTC", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": true, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.001, "maker": 0.001, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1e-08, "price": 0.1}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 1e-05, "max": 10000000000}, "price": {"min": null, "max": null}, "cost": {"min": 0.1, "max": 99999999}}, "marginModes": {"cross": false, "isolated": false}, "created": null, "info": {"symbol": "BTC-USDT", "name": "BTC-USDT", "baseCurrency": "BTC", "quoteCurrency": "USDT", "feeCurrency": "USDT", "market": "USDS", "baseMinSize": "0.00001", "quoteMinSize": "0.1", "baseMaxSize": "10000000000", "quoteMaxSize": "99999999", "baseIncrement": "0.00000001", "quoteIncrement": "0.000001", "priceIncrement": "0.1", "priceLimitRate": "0.1", "minFunds": "0.1", "isMarginEnabled": true, "enableTrading": true, "feeCategory": 1, "makerFeeCoefficient": "1.00", "takerFeeCoefficient": "1.00", "st": false, "callauctionIsEnabled": false, "callauctionPriceFloor": null, "callauctionPriceCeiling": null, "callauctionFirstStageStartTime": null, "callauctionSecondStageStartTime": null, "callauctionThirdStageStartTime": null, "tradingStartTime": null}, "tierBased": true, "percentage": true, "tiers": {"taker": [[0, 0.001], [50, 0.001], [200, 0.0009], [500, 0.0008], [1000, 0.0007], [2000, 0.0007], [4000, 0.0006], [8000, 0.0005], [15000, 0.00045], [25000, 0.0004], [40000, 0.00035], [60000, 0.0003], [80000, 0.00025]], "maker": [[0, 0.001], [50, 0.0009], [200, 0.0007], [500, 0.0005], [1000, 0.0003], [2000, 0], [4000, 0], [8000, 0], [15000, -5e-05], [25000, -5e-05], [40000, -5e-05], [60000, -5e-05], [80000, -5e-05]]}}, "ETH/USDT": {"id": "ETH-USDT", "lowercaseId": null, "symbol": "ETH/USDT", "base": "ETH", "quote": "USDT", "settle": null, "baseId": "ETH", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": true, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.001, "maker": 0.001, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1e-07, "price": 0.01}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 0.0001, "max": 10000000000}, "price": {"min": null, "max": null}, "cost": {"min": 0.01, "max": 999999999}}, "marginModes": {"cross": false, "isolated": false}, "created": null, "info": {"symbol": "ETH-USDT", "name": "ETH-USDT", "baseCurrency": "ETH", "quoteCurrency": "USDT", "feeCurrency": "USDT", "market": "USDS", "baseMinSize": "0.0001", "quoteMinSize": "0.01", "baseMaxSize": "10000000000", "quoteMaxSize": "999999999", "baseIncrement": "0.0000001", "quoteIncrement": "0.000001", "priceIncrement": "0.01", "priceLimitRate": "0.1", "minFunds": "0.1", "isMarginEnabled": true, "enableTrading": true, "feeCategory": 1, "makerFeeCoefficient": "1.00", "takerFeeCoefficient": "1.00", "st": false, "callauctionIsEnabled": false, "callauctionPriceFloor": null, "callauctionPriceCeiling": null, "callauctionFirstStageStartTime": null, "callauctionSecondStageStartTime": null, "callauctionThirdStageStartTime": null, "tradingStartTime": null}, "tierBased": true, "percentage": true, "tiers": {"taker": [[0, 0.001], [50, 0.001], [200, 0.0009], [500, 0.0008], [1000, 0.0007], [2000, 0.0007], [4000, 0.0006], [8000, 0.0005], [15000, 0.00045], [25000, 0.0004], [40000, 0.00035], [60000, 0.0003], [80000, 0.00025]], "maker": [[0, 0.001], [50, 0.0009], [200, 0.0007], [500, 0.0005], [1000, 0.0003], [2000, 0], [4000, 0], [8000, 0], [15000, -5e-05], [25000, -5e-05], [40000, -5e-05], [60000, -5e-05], [80000, -5e-05]]}}, "ADA/USDT": {"id": "ADA-USDT", "lowercaseId": null, "symbol": "ADA/USDT", "base": "ADA", "quote": "USDT", "settle": null, "baseId": "ADA", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": true, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.001, "maker": 0.001, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.01, "price": 0.0001}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 1, "max": 10000000000}, "price": {"min": null, "max": null}, "cost": {"min": 0.1, "max": 99999999}}, "marginModes": {"cross": false, "isolated": false}, "created": null, "info": {"symbol": "ADA-USDT", "name": "ADA-USDT", "baseCurrency": "ADA", "quoteCurrency": "USDT", "feeCurrency": "USDT", "market": "USDS", "baseMinSize": "1", "quoteMinSize": "0.1", "baseMaxSize": "10000000000", "quoteMaxSize": "99999999", "baseIncrement": "0.01", "quoteIncrement": "0.0001", "priceIncrement": "0.0001", "priceLimitRate": "0.1", "minFunds": "0.1", "isMarginEnabled": true, "enableTrading": true, "feeCategory": 1, "makerFeeCoefficient": "1.00", "takerFeeCoefficient": "1.00", "st": false, "callauctionIsEnabled": false, "callauctionPriceFloor": null, "callauctionPriceCeiling": null, "callauctionFirstStageStartTime": null, "callauctionSecondStageStartTime": null, "callauctionThirdStageStartTime": null, "tradingStartTime": null}, "tierBased": true, "percentage": true, "tiers": {"taker": [[0, 0.001], [50, 0.001], [200, 0.0009], [500, 0.0008], [1000, 0.0007], [2000, 0.0007], [4000, 0.0006], [8000, 0.0005], [15000, 0.00045], [25000, 0.0004], [40000, 0.00035], [60000, 0.0003], [80000, 0.00025]], "maker": [[0, 0.001], [50, 0.0009], [200, 0.0007], [500, 0.0005], [1000, 0.0003], [2000, 0], [4000, 0], [8000, 0], [15000, -5e-05], [25000, -5e-05], [40000, -5e-05], [60000, -5e-05], [80000, -5e-05]]}}, "LTC/USDT": {"id": "LTC-USDT", "lowercaseId": null, "symbol": "LTC/USDT", "base": "LTC", "quote": "USDT", "settle": null, "baseId": "LTC", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": true, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.001, "maker": 0.001, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.01, "price": 0.001}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 0.01, "max": 10000000000}, "price": {"min": null, "max": null}, "cost": {"min": 0.1, "max": 99999999}}, "marginModes": {"cross": false, "isolated": false}, "created": null, "info": {"symbol": "LTC-USDT", "name": "LTC-USDT", "baseCurrency": "LTC", "quoteCurrency": "USDT", "feeCurrency": "USDT", "market": "USDS", "baseMinSize": "0.01", "quoteMinSize": "0.1", "baseMaxSize": "10000000000", "quoteMaxSize": "99999999", "baseIncrement": "0.01", "quoteIncrement": "0.000001", "priceIncrement": "0.001", "priceLimitRate": "0.1", "minFunds": "0.1", "isMarginEnabled": true, "enableTrading": true, "feeCategory": 1, "makerFeeCoefficient": "1.00", "takerFeeCoefficient": "1.00", "st": false, "callauctionIsEnabled": false, "callauctionPriceFloor": null, "callauctionPriceCeiling": null, "callauctionFirstStageStartTime": null, "callauctionSecondStageStartTime": null, "callauctionThirdStageStartTime": null, "tradingStartTime": null}, "tierBased": true, "percentage": true, "tiers": {"taker": [[0, 0.001], [50, 0.001], [200, 0.0009], [500, 0.0008], [1000, 0.0007], [2000, 0.0007], [4000, 0.0006], [8000, 0.0005], [15000, 0.00045], [25000, 0.0004], [40000, 0.00035], [60000, 0.0003], [80000, 0.00025]], "maker": [[0, 0.001], [50, 0.0009], [200, 0.0007], [500, 0.0005], [1000, 0.0003], [2000, 0], [4000, 0], [8000, 0], [15000, -5e-05], [25000, -5e-05], [40000, -5e-05], [60000, -5e-05], [80000, -5e-05]]}}, "XRP/USDT": {"id": "XRP-USDT", "lowercaseId": null, "symbol": "XRP/USDT", "base": "XRP", "quote": "USDT", "settle": null, "baseId": "XRP", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": true, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.001, "maker": 0.001, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.0001, "price": 1e-05}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 0.1, "max": 10000000000}, "price": {"min": null, "max": null}, "cost": {"min": 0.1, "max": 99999999}}, "marginModes": {"cross": false, "isolated": false}, "created": null, "info": {"symbol": "XRP-USDT", "name": "XRP-USDT", "baseCurrency": "XRP", "quoteCurrency": "USDT", "feeCurrency": "USDT", "market": "USDS", "baseMinSize": "0.1", "quoteMinSize": "0.1", "baseMaxSize": "10000000000", "quoteMaxSize": "99999999", "baseIncrement": "0.0001", "quoteIncrement": "0.000001", "priceIncrement": "0.00001", "priceLimitRate": "0.1", "minFunds": "0.1", "isMarginEnabled": true, "enableTrading": true, "feeCategory": 1, "makerFeeCoefficient": "1.00", "takerFeeCoefficient": "1.00", "st": false, "callauctionIsEnabled": false, "callauctionPriceFloor": null, "callauctionPriceCeiling": null, "callauctionFirstStageStartTime": null, "callauctionSecondStageStartTime": null, "callauctionThirdStageStartTime": null, "tradingStartTime": null}, "tierBased": true, "percentage": true, "tiers": {"taker": [[0, 0.001], [50, 0.001], [200, 0.0009], [500, 0.0008], [1000, 0.0007], [2000, 0.0007], [4000, 0.0006], [8000, 0.0005], [15000, 0.00045], [25000, 0.0004], [40000, 0.00035], [60000, 0.0003], [80000, 0.00025]], "maker": [[0, 0.001], [50, 0.0009], [200, 0.0007], [500, 0.0005], [1000, 0.0003], [2000, 0], [4000, 0], [8000, 0], [15000, -5e-05], [25000, -5e-05], [40000, -5e-05], [60000, -5e-05], [80000, -5e-05]]}}, "DOGE/USDT": {"id": "DOGE-USDT", "lowercaseId": null, "symbol": "DOGE/USDT", "base": "DOGE", "quote": "USDT", "settle": null, "baseId": "DOGE", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": true, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.001, "maker": 0.001, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.0001, "price": 1e-05}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 10, "max": 10000000000}, "price": {"min": null, "max": null}, "cost": {"min": 0.1, "max": 99999999}}, "marginModes": {"cross": false, "isolated": false}, "created": null, "info": {"symbol": "DOGE-USDT", "name": "DOGE-USDT", "baseCurrency": "DOGE", "quoteCurrency": "USDT", "feeCurrency": "USDT", "market": "USDS", "baseMinSize": "10", "quoteMinSize": "0.1", "baseMaxSize": "10000000000", "quoteMaxSize": "99999999", "baseIncrement": "0.0001", "quoteIncrement": "0.00001", "priceIncrement": "0.00001", "priceLimitRate": "0.1", "minFunds": "0.1", "isMarginEnabled": true, "enableTrading": true, "feeCategory": 1, "makerFeeCoefficient": "1.00", "takerFeeCoefficient": "1.00", "st": false, "callauctionIsEnabled": false, "callauctionPriceFloor": null, "callauctionPriceCeiling": null, "callauctionFirstStageStartTime": null, "callauctionSecondStageStartTime": null, "callauctionThirdStageStartTime": null, "tradingStartTime": null}, "tierBased": true, "percentage": true, "tiers": {"taker": [[0, 0.001], [50, 0.001], [200, 0.0009], [500, 0.0008], [1000, 0.0007], [2000, 0.0007], [4000, 0.0006], [8000, 0.0005], [15000, 0.00045], [25000, 0.0004], [40000, 0.00035], [60000, 0.0003], [80000, 0.00025]], "maker": [[0, 0.001], [50, 0.0009], [200, 0.0007], [500, 0.0005], [1000, 0.0003], [2000, 0], [4000, 0], [8000, 0], [15000, -5e-05], [25000, -5e-05], [40000, -5e-05], [60000, -5e-05], [80000, -5e-05]]}}, "TRX/USDT": {"id": "TRX-USDT", "lowercaseId": null, "symbol": "TRX/USDT", "base": "TRX", "quote": "USDT", "settle": null, "baseId": "TRX", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": true, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.001, "maker": 0.001, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.01, "price": 0.0001}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 10, "max": 10000000000}, "price": {"min": null, "max": null}, "cost": {"min": 0.1, "max": 99999999}}, "marginModes": {"cross": false, "isolated": false}, "created": null, "info": {"symbol": "TRX-USDT", "name": "TRX-USDT", "baseCurrency": "TRX", "quoteCurrency": "USDT", "feeCurrency": "USDT", "market": "USDS", "baseMinSize": "10", "quoteMinSize": "0.1", "baseMaxSize": "10000000000", "quoteMaxSize": "99999999", "baseIncrement": "0.01", "quoteIncrement": "0.0001", "priceIncrement": "0.0001", "priceLimitRate": "0.1", "minFunds": "0.1", "isMarginEnabled": true, "enableTrading": true, "feeCategory": 1, "makerFeeCoefficient": "1.00", "takerFeeCoefficient": "1.00", "st": false, "callauctionIsEnabled": false, "callauctionPriceFloor": null, "callauctionPriceCeiling": null, "callauctionFirstStageStartTime": null, "callauctionSecondStageStartTime": null, "callauctionThirdStageStartTime": null, "tradingStartTime": null}, "tierBased": true, "percentage": true, "tiers": {"taker": [[0, 0.001], [50, 0.001], [200, 0.0009], [500, 0.0008], [1000, 0.0007], [2000, 0.0007], [4000, 0.0006], [8000, 0.0005], [15000, 0.00045], [25000, 0.0004], [40000, 0.00035], [60000, 0.0003], [80000, 0.00025]], "maker": [[0, 0.001], [50, 0.0009], [200, 0.0007], [500, 0.0005], [1000, 0.0003], [2000, 0], [4000, 0], [8000, 0], [15000, -5e-05], [25000, -5e-05], [40000, -5e-05], [60000, -5e-05], [80000, -5e-05]]}}}