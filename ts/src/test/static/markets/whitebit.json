{"BTC/USDT": {"id": "BTC_USDT", "lowercaseId": null, "symbol": "BTC/USDT", "base": "BTC", "quote": "USDT", "settle": null, "baseId": "BTC", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": true, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.001, "maker": 0.001, "contractSize": 1e-06, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1e-06, "price": 0.01}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 1e-05, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": 5, "max": 10000000}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"name": "BTC_USDT", "stock": "BTC", "money": "USDT", "stockPrec": "6", "moneyPrec": "2", "feePrec": "6", "makerFee": "0.1", "takerFee": "0.1", "minAmount": "0.00001", "minTotal": "5", "tradesEnabled": true, "type": "spot", "isCollateral": true, "maxTotal": "10000000"}, "tierBased": false, "percentage": true}, "BTC/USD": {"id": "BTC_USD", "lowercaseId": null, "symbol": "BTC/USD", "base": "BTC", "quote": "USD", "settle": null, "baseId": "BTC", "quoteId": "USD", "settleId": null, "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.001, "maker": 0.001, "contractSize": 1e-06, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1e-06, "price": 0.01}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 1e-05, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": 5, "max": 10000000}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"name": "BTC_USD", "stock": "BTC", "money": "USD", "stockPrec": "6", "moneyPrec": "2", "feePrec": "6", "makerFee": "0.1", "takerFee": "0.1", "minAmount": "0.00001", "minTotal": "5", "tradesEnabled": true, "type": "spot", "isCollateral": false, "maxTotal": "10000000"}, "tierBased": false, "percentage": true}, "ETH/USDT": {"id": "ETH_USDT", "lowercaseId": null, "symbol": "ETH/USDT", "base": "ETH", "quote": "USDT", "settle": null, "baseId": "ETH", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": true, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.001, "maker": 0.001, "contractSize": 1e-05, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1e-05, "price": 0.01}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 0.001, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": 5, "max": 100000000}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"name": "ETH_USDT", "stock": "ETH", "money": "USDT", "stockPrec": "5", "moneyPrec": "2", "feePrec": "6", "makerFee": "0.1", "takerFee": "0.1", "minAmount": "0.001", "minTotal": "5", "tradesEnabled": true, "type": "spot", "isCollateral": true, "maxTotal": "100000000"}, "tierBased": false, "percentage": true}, "ADA/USDT": {"id": "ADA_USDT", "lowercaseId": null, "symbol": "ADA/USDT", "base": "ADA", "quote": "USDT", "settle": null, "baseId": "ADA", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": true, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.001, "maker": 0.001, "contractSize": 0.01, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.01, "price": 1e-06}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 1, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": 5, "max": 10000000}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"name": "ADA_USDT", "stock": "ADA", "money": "USDT", "stockPrec": "2", "moneyPrec": "6", "feePrec": "6", "makerFee": "0.1", "takerFee": "0.1", "minAmount": "1", "minTotal": "5", "tradesEnabled": true, "type": "spot", "isCollateral": true, "maxTotal": "10000000"}, "tierBased": false, "percentage": true}, "LTC/USDT": {"id": "LTC_USDT", "lowercaseId": null, "symbol": "LTC/USDT", "base": "LTC", "quote": "USDT", "settle": null, "baseId": "LTC", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": true, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.001, "maker": 0.001, "contractSize": 1e-05, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1e-05, "price": 0.01}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 0.01, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": 5, "max": 100000000}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"name": "LTC_USDT", "stock": "LTC", "money": "USDT", "stockPrec": "5", "moneyPrec": "2", "feePrec": "6", "makerFee": "0.1", "takerFee": "0.1", "minAmount": "0.01", "minTotal": "5", "tradesEnabled": true, "type": "spot", "isCollateral": true, "maxTotal": "100000000"}, "tierBased": false, "percentage": true}, "XRP/USDT": {"id": "XRP_USDT", "lowercaseId": null, "symbol": "XRP/USDT", "base": "XRP", "quote": "USDT", "settle": null, "baseId": "XRP", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": true, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.001, "maker": 0.001, "contractSize": 0.01, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.01, "price": 1e-05}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 1, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": 5, "max": 100000000}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"name": "XRP_USDT", "stock": "XRP", "money": "USDT", "stockPrec": "2", "moneyPrec": "5", "feePrec": "6", "makerFee": "0.1", "takerFee": "0.1", "minAmount": "1", "minTotal": "5", "tradesEnabled": true, "type": "spot", "isCollateral": true, "maxTotal": "100000000"}, "tierBased": false, "percentage": true}, "DOGE/USDT": {"id": "DOGE_USDT", "lowercaseId": null, "symbol": "DOGE/USDT", "base": "DOGE", "quote": "USDT", "settle": null, "baseId": "DOGE", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": true, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.001, "maker": 0.001, "contractSize": 0.1, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.1, "price": 1e-07}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 10, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": 5, "max": 10000000}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"name": "DOGE_USDT", "stock": "DOGE", "money": "USDT", "stockPrec": "1", "moneyPrec": "7", "feePrec": "6", "makerFee": "0.1", "takerFee": "0.1", "minAmount": "10", "minTotal": "5", "tradesEnabled": true, "type": "spot", "isCollateral": true, "maxTotal": "10000000"}, "tierBased": false, "percentage": true}, "TRX/USDT": {"id": "TRX_USDT", "lowercaseId": null, "symbol": "TRX/USDT", "base": "TRX", "quote": "USDT", "settle": null, "baseId": "TRX", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": true, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.001, "maker": 0.001, "contractSize": 0.01, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.01, "price": 1e-06}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 10, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": 5, "max": 10000000}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"name": "TRX_USDT", "stock": "TRX", "money": "USDT", "stockPrec": "2", "moneyPrec": "6", "feePrec": "6", "makerFee": "0.1", "takerFee": "0.1", "minAmount": "10", "minTotal": "5", "tradesEnabled": true, "type": "spot", "isCollateral": true, "maxTotal": "10000000"}, "tierBased": false, "percentage": true}, "BTC/USDT:USDT": {"id": "BTC_PERP", "lowercaseId": null, "symbol": "BTC/USDT:USDT", "base": "BTC", "quote": "USDT", "settle": "USDT", "baseId": "BTC", "quoteId": "USDT", "settleId": "USDT", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": 0.00035, "maker": 0.0001, "contractSize": 0.001, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.001, "price": 0.1}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 0.001, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": 5, "max": 100000000000}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"name": "BTC_PERP", "stock": "BTC", "money": "USDT", "stockPrec": "3", "moneyPrec": "1", "feePrec": "6", "makerFee": "0.01", "takerFee": "0.035", "minAmount": "0.001", "minTotal": "5", "tradesEnabled": true, "type": "futures", "isCollateral": true, "maxTotal": "100000000000"}, "tierBased": false, "percentage": true}, "ETH/USDT:USDT": {"id": "ETH_PERP", "lowercaseId": null, "symbol": "ETH/USDT:USDT", "base": "ETH", "quote": "USDT", "settle": "USDT", "baseId": "ETH", "quoteId": "USDT", "settleId": "USDT", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": 0.00035, "maker": 0.0001, "contractSize": 0.01, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.01, "price": 0.01}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 0.01, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": 5, "max": 100000000000}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"name": "ETH_PERP", "stock": "ETH", "money": "USDT", "stockPrec": "2", "moneyPrec": "2", "feePrec": "6", "makerFee": "0.01", "takerFee": "0.035", "minAmount": "0.01", "minTotal": "5", "tradesEnabled": true, "type": "futures", "isCollateral": true, "maxTotal": "100000000000"}, "tierBased": false, "percentage": true}, "LTC/USDT:USDT": {"id": "LTC_PERP", "lowercaseId": null, "symbol": "LTC/USDT:USDT", "base": "LTC", "quote": "USDT", "settle": "USDT", "baseId": "LTC", "quoteId": "USDT", "settleId": "USDT", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": 0.00035, "maker": 0.0001, "contractSize": 0.1, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.1, "price": 0.01}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 0.1, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": 5, "max": 1000000000000}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"name": "LTC_PERP", "stock": "LTC", "money": "USDT", "stockPrec": "1", "moneyPrec": "2", "feePrec": "6", "makerFee": "0.01", "takerFee": "0.035", "minAmount": "0.1", "minTotal": "5", "tradesEnabled": true, "type": "futures", "isCollateral": true, "maxTotal": "1000000000000"}, "tierBased": false, "percentage": true}, "ADA/USDT:USDT": {"id": "ADA_PERP", "lowercaseId": null, "symbol": "ADA/USDT:USDT", "base": "ADA", "quote": "USDT", "settle": "USDT", "baseId": "ADA", "quoteId": "USDT", "settleId": "USDT", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": 0.00035, "maker": 0.0001, "contractSize": 0.1, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.1, "price": 1e-06}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 10, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": 5, "max": 10000000000}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"name": "ADA_PERP", "stock": "ADA", "money": "USDT", "stockPrec": "-1", "moneyPrec": "6", "feePrec": "6", "makerFee": "0.01", "takerFee": "0.035", "minAmount": "10", "minTotal": "5", "tradesEnabled": true, "type": "futures", "isCollateral": true, "maxTotal": "10000000000"}, "tierBased": false, "percentage": true}, "XRP/USDT:USDT": {"id": "XRP_PERP", "lowercaseId": null, "symbol": "XRP/USDT:USDT", "base": "XRP", "quote": "USDT", "settle": "USDT", "baseId": "XRP", "quoteId": "USDT", "settleId": "USDT", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": 0.00035, "maker": 0.0001, "contractSize": 0.1, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.1, "price": 1e-05}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 10, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": 5, "max": 100000000000}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"name": "XRP_PERP", "stock": "XRP", "money": "USDT", "stockPrec": "-1", "moneyPrec": "5", "feePrec": "6", "makerFee": "0.01", "takerFee": "0.035", "minAmount": "10", "minTotal": "5", "tradesEnabled": true, "type": "futures", "isCollateral": true, "maxTotal": "100000000000"}, "tierBased": false, "percentage": true}, "DOGE/USDT:USDT": {"id": "DOGE_PERP", "lowercaseId": null, "symbol": "DOGE/USDT:USDT", "base": "DOGE", "quote": "USDT", "settle": "USDT", "baseId": "DOGE", "quoteId": "USDT", "settleId": "USDT", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": 0.00035, "maker": 0.0001, "contractSize": 0.1, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.1, "price": 1e-06}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 100, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": 5, "max": 100000000000}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"name": "DOGE_PERP", "stock": "DOGE", "money": "USDT", "stockPrec": "-2", "moneyPrec": "6", "feePrec": "6", "makerFee": "0.01", "takerFee": "0.035", "minAmount": "100", "minTotal": "5", "tradesEnabled": true, "type": "futures", "isCollateral": true, "maxTotal": "100000000000"}, "tierBased": false, "percentage": true}, "TRX/USDT:USDT": {"id": "TRX_PERP", "lowercaseId": null, "symbol": "TRX/USDT:USDT", "base": "TRX", "quote": "USDT", "settle": "USDT", "baseId": "TRX", "quoteId": "USDT", "settleId": "USDT", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": 0.00035, "maker": 0.0001, "contractSize": 0.1, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.1, "price": 1e-06}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 100, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": 5, "max": 100000000000}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"name": "TRX_PERP", "stock": "TRX", "money": "USDT", "stockPrec": "-2", "moneyPrec": "6", "feePrec": "6", "makerFee": "0.01", "takerFee": "0.035", "minAmount": "100", "minTotal": "5", "tradesEnabled": true, "type": "futures", "isCollateral": true, "maxTotal": "100000000000"}, "tierBased": false, "percentage": true}}