{"BTC/USDT": {"id": "BTCUSDT", "lowercaseId": null, "symbol": "BTC/USDT", "base": "BTC", "quote": "USDT", "settle": null, "baseId": "BTC", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": true, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.002, "maker": 0.002, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1e-06, "price": 0.01}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 0, "max": 900000000000000000000}, "price": {"min": null, "max": null}, "cost": {"min": 1, "max": null}}, "marginModes": {"cross": true, "isolated": true}, "created": 1532454360000, "info": {"symbol": "BTCUSDT", "baseCoin": "BTC", "quoteCoin": "USDT", "minTradeAmount": "0", "maxTradeAmount": "900000000000000000000", "takerFeeRate": "0.002", "makerFeeRate": "0.002", "pricePrecision": "2", "quantityPrecision": "6", "quotePrecision": "8", "status": "online", "minTradeUSDT": "1", "buyLimitPriceRatio": "0.05", "sellLimitPriceRatio": "0.05", "areaSymbol": "no", "orderQuantity": "200", "openTime": "1532454360000", "offTime": ""}, "tierBased": null, "percentage": null}, "ETH/USDT": {"id": "ETHUSDT", "lowercaseId": null, "symbol": "ETH/USDT", "base": "ETH", "quote": "USDT", "settle": null, "baseId": "ETH", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": true, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.002, "maker": 0.002, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.0001, "price": 0.01}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 0, "max": 900000000000000000000}, "price": {"min": null, "max": null}, "cost": {"min": 1, "max": null}}, "marginModes": {"cross": true, "isolated": true}, "created": 1532450400000, "info": {"symbol": "ETHUSDT", "baseCoin": "ETH", "quoteCoin": "USDT", "minTradeAmount": "0", "maxTradeAmount": "900000000000000000000", "takerFeeRate": "0.002", "makerFeeRate": "0.002", "pricePrecision": "2", "quantityPrecision": "4", "quotePrecision": "6", "status": "online", "minTradeUSDT": "1", "buyLimitPriceRatio": "0.05", "sellLimitPriceRatio": "0.05", "areaSymbol": "no", "orderQuantity": "200", "openTime": "1532450400000", "offTime": ""}, "tierBased": null, "percentage": null}, "ADA/USDT": {"id": "ADAUSDT", "lowercaseId": null, "symbol": "ADA/USDT", "base": "ADA", "quote": "USDT", "settle": null, "baseId": "ADA", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": true, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.001, "maker": 0.001, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.001, "price": 0.0001}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 0, "max": 900000000000000000000}, "price": {"min": null, "max": null}, "cost": {"min": 1, "max": null}}, "marginModes": {"cross": true, "isolated": true}, "created": 1648021920000, "info": {"symbol": "ADAUSDT", "baseCoin": "ADA", "quoteCoin": "USDT", "minTradeAmount": "0", "maxTradeAmount": "900000000000000000000", "takerFeeRate": "0.001", "makerFeeRate": "0.001", "pricePrecision": "4", "quantityPrecision": "3", "quotePrecision": "7", "status": "online", "minTradeUSDT": "1", "buyLimitPriceRatio": "0.05", "sellLimitPriceRatio": "0.05", "areaSymbol": "no", "orderQuantity": "200", "openTime": "1648021920000", "offTime": ""}, "tierBased": null, "percentage": null}, "LTC/USDT": {"id": "LTCUSDT", "lowercaseId": null, "symbol": "LTC/USDT", "base": "LTC", "quote": "USDT", "settle": null, "baseId": "LTC", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": true, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.002, "maker": 0.002, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.0001, "price": 0.01}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 0, "max": 900000000000000000000}, "price": {"min": null, "max": null}, "cost": {"min": 1, "max": null}}, "marginModes": {"cross": true, "isolated": true}, "created": 1533518760000, "info": {"symbol": "LTCUSDT", "baseCoin": "LTC", "quoteCoin": "USDT", "minTradeAmount": "0", "maxTradeAmount": "900000000000000000000", "takerFeeRate": "0.002", "makerFeeRate": "0.002", "pricePrecision": "2", "quantityPrecision": "4", "quotePrecision": "6", "status": "online", "minTradeUSDT": "1", "buyLimitPriceRatio": "0.05", "sellLimitPriceRatio": "0.05", "areaSymbol": "no", "orderQuantity": "200", "openTime": "1533518760000", "offTime": ""}, "tierBased": null, "percentage": null}, "XRP/USDT": {"id": "XRPUSDT", "lowercaseId": null, "symbol": "XRP/USDT", "base": "XRP", "quote": "USDT", "settle": null, "baseId": "XRP", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": true, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.002, "maker": 0.002, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.0001, "price": 0.0001}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 0, "max": 900000000000000000000}, "price": {"min": null, "max": null}, "cost": {"min": 1, "max": null}}, "marginModes": {"cross": true, "isolated": true}, "created": 1557133320000, "info": {"symbol": "XRPUSDT", "baseCoin": "XRP", "quoteCoin": "USDT", "minTradeAmount": "0", "maxTradeAmount": "900000000000000000000", "takerFeeRate": "0.002", "makerFeeRate": "0.002", "pricePrecision": "4", "quantityPrecision": "4", "quotePrecision": "8", "status": "online", "minTradeUSDT": "1", "buyLimitPriceRatio": "0.05", "sellLimitPriceRatio": "0.05", "areaSymbol": "no", "orderQuantity": "200", "openTime": "1557133320000", "offTime": ""}, "tierBased": null, "percentage": null}, "DOGE/USDT": {"id": "DOGEUSDT", "lowercaseId": null, "symbol": "DOGE/USDT", "base": "DOGE", "quote": "USDT", "settle": null, "baseId": "DOGE", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": true, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.002, "maker": 0.002, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.0001, "price": 1e-05}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 0, "max": 900000000000000000000}, "price": {"min": null, "max": null}, "cost": {"min": 1, "max": null}}, "marginModes": {"cross": true, "isolated": true}, "created": 1619322660000, "info": {"symbol": "DOGEUSDT", "baseCoin": "DOGE", "quoteCoin": "USDT", "minTradeAmount": "0", "maxTradeAmount": "900000000000000000000", "takerFeeRate": "0.002", "makerFeeRate": "0.002", "pricePrecision": "5", "quantityPrecision": "4", "quotePrecision": "9", "status": "online", "minTradeUSDT": "1", "buyLimitPriceRatio": "0.05", "sellLimitPriceRatio": "0.05", "areaSymbol": "no", "orderQuantity": "200", "openTime": "1619322660000", "offTime": ""}, "tierBased": null, "percentage": null}, "TRX/USDT": {"id": "TRXUSDT", "lowercaseId": null, "symbol": "TRX/USDT", "base": "TRX", "quote": "USDT", "settle": null, "baseId": "TRX", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": true, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.002, "maker": 0.002, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.0001, "price": 0.0001}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 0, "max": 900000000000000000000}, "price": {"min": null, "max": null}, "cost": {"min": 1, "max": null}}, "marginModes": {"cross": true, "isolated": true}, "created": 1550736060000, "info": {"symbol": "TRXUSDT", "baseCoin": "TRX", "quoteCoin": "USDT", "minTradeAmount": "0", "maxTradeAmount": "900000000000000000000", "takerFeeRate": "0.002", "makerFeeRate": "0.002", "pricePrecision": "4", "quantityPrecision": "4", "quotePrecision": "8", "status": "online", "minTradeUSDT": "1", "buyLimitPriceRatio": "0.05", "sellLimitPriceRatio": "0.05", "areaSymbol": "no", "orderQuantity": "200", "openTime": "1550736060000", "offTime": ""}, "tierBased": null, "percentage": null}, "BTC/USDT:USDT": {"id": "BTCUSDT", "lowercaseId": null, "symbol": "BTC/USDT:USDT", "base": "BTC", "quote": "USDT", "settle": "USDT", "baseId": "BTC", "quoteId": "USDT", "settleId": "USDT", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": 0.0006, "maker": 0.0002, "contractSize": 1, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.0001, "price": 0.1}, "limits": {"leverage": {"min": 1, "max": 125}, "amount": {"min": 0.0001, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": 5, "max": null}}, "marginModes": {"cross": true, "isolated": true}, "created": null, "info": {"symbol": "BTCUSDT", "baseCoin": "BTC", "quoteCoin": "USDT", "buyLimitPriceRatio": "0.05", "sellLimitPriceRatio": "0.05", "feeRateUpRatio": "0.005", "makerFeeRate": "0.0002", "takerFeeRate": "0.0006", "openCostUpRatio": "0.01", "supportMarginCoins": ["USDT"], "minTradeNum": "0.0001", "priceEndStep": "1", "volumePlace": "4", "pricePlace": "1", "sizeMultiplier": "0.0001", "symbolType": "perpetual", "minTradeUSDT": "5", "maxSymbolOrderNum": "200", "maxProductOrderNum": "1000", "maxPositionNum": "150", "symbolStatus": "normal", "offTime": "-1", "limitOpenTime": "-1", "deliveryTime": "", "deliveryStartTime": "", "deliveryPeriod": "", "launchTime": "", "fundInterval": "8", "minLever": "1", "maxLever": "125", "posLimit": "0.1", "maintainTime": "", "openTime": "", "maxMarketOrderQty": "220", "maxOrderQty": "1200"}, "tierBased": null, "percentage": null}, "BTC/USD:BTC": {"id": "BTCUSD", "lowercaseId": null, "symbol": "BTC/USD:BTC", "base": "BTC", "quote": "USD", "settle": "BTC", "baseId": "BTC", "quoteId": "USD", "settleId": "BTC", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": false, "inverse": true, "subType": "inverse", "taker": 0.0006, "maker": 0.0002, "contractSize": 1, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.0001, "price": 0.1}, "limits": {"leverage": {"min": 1, "max": 125}, "amount": {"min": 0.0001, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": true, "isolated": true}, "created": null, "info": {"symbol": "BTCUSD", "baseCoin": "BTC", "quoteCoin": "USD", "buyLimitPriceRatio": "0.01", "sellLimitPriceRatio": "0.01", "feeRateUpRatio": "0.005", "makerFeeRate": "0.0002", "takerFeeRate": "0.0006", "openCostUpRatio": "0.01", "supportMarginCoins": ["BTC", "STETH", "XRP", "ETH", "USDE", "USDC", "BGB"], "minTradeNum": "0.0001", "priceEndStep": "1", "volumePlace": "4", "pricePlace": "1", "sizeMultiplier": "0.0001", "symbolType": "perpetual", "minTradeUSDT": "5", "maxSymbolOrderNum": "200", "maxProductOrderNum": "1000", "maxPositionNum": "150", "symbolStatus": "normal", "offTime": "-1", "limitOpenTime": "-1", "deliveryTime": "", "deliveryStartTime": "", "deliveryPeriod": "", "launchTime": "", "fundInterval": "8", "minLever": "1", "maxLever": "125", "posLimit": "0.1", "maintainTime": "", "openTime": "", "maxMarketOrderQty": "", "maxOrderQty": ""}, "tierBased": null, "percentage": null}, "ETH/USDT:USDT": {"id": "ETHUSDT", "lowercaseId": null, "symbol": "ETH/USDT:USDT", "base": "ETH", "quote": "USDT", "settle": "USDT", "baseId": "ETH", "quoteId": "USDT", "settleId": "USDT", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": 0.0006, "maker": 0.0002, "contractSize": 1, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.01, "price": 0.01}, "limits": {"leverage": {"min": 1, "max": 100}, "amount": {"min": 0.01, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": 5, "max": null}}, "marginModes": {"cross": true, "isolated": true}, "created": null, "info": {"symbol": "ETHUSDT", "baseCoin": "ETH", "quoteCoin": "USDT", "buyLimitPriceRatio": "0.05", "sellLimitPriceRatio": "0.05", "feeRateUpRatio": "0.005", "makerFeeRate": "0.0002", "takerFeeRate": "0.0006", "openCostUpRatio": "0.01", "supportMarginCoins": ["USDT"], "minTradeNum": "0.01", "priceEndStep": "1", "volumePlace": "2", "pricePlace": "2", "sizeMultiplier": "0.01", "symbolType": "perpetual", "minTradeUSDT": "5", "maxSymbolOrderNum": "200", "maxProductOrderNum": "1000", "maxPositionNum": "150", "symbolStatus": "normal", "offTime": "-1", "limitOpenTime": "-1", "deliveryTime": "", "deliveryStartTime": "", "deliveryPeriod": "", "launchTime": "", "fundInterval": "8", "minLever": "1", "maxLever": "100", "posLimit": "0.1", "maintainTime": "", "openTime": "", "maxMarketOrderQty": "1900", "maxOrderQty": "9900"}, "tierBased": null, "percentage": null}, "LTC/USDT:USDT": {"id": "LTCUSDT", "lowercaseId": null, "symbol": "LTC/USDT:USDT", "base": "LTC", "quote": "USDT", "settle": "USDT", "baseId": "LTC", "quoteId": "USDT", "settleId": "USDT", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": 0.0006, "maker": 0.0002, "contractSize": 1, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.1, "price": 0.01}, "limits": {"leverage": {"min": 1, "max": 75}, "amount": {"min": 0.1, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": 5, "max": null}}, "marginModes": {"cross": true, "isolated": true}, "created": null, "info": {"symbol": "LTCUSDT", "baseCoin": "LTC", "quoteCoin": "USDT", "buyLimitPriceRatio": "0.05", "sellLimitPriceRatio": "0.05", "feeRateUpRatio": "0.005", "makerFeeRate": "0.0002", "takerFeeRate": "0.0006", "openCostUpRatio": "0.01", "supportMarginCoins": ["USDT"], "minTradeNum": "0.1", "priceEndStep": "1", "volumePlace": "1", "pricePlace": "2", "sizeMultiplier": "0.1", "symbolType": "perpetual", "minTradeUSDT": "5", "maxSymbolOrderNum": "200", "maxProductOrderNum": "1000", "maxPositionNum": "150", "symbolStatus": "normal", "offTime": "-1", "limitOpenTime": "-1", "deliveryTime": "", "deliveryStartTime": "", "deliveryPeriod": "", "launchTime": "", "fundInterval": "8", "minLever": "1", "maxLever": "75", "posLimit": "0.1", "maintainTime": "", "openTime": "", "maxMarketOrderQty": "3700", "maxOrderQty": "18500"}, "tierBased": null, "percentage": null}, "ADA/USDT:USDT": {"id": "ADAUSDT", "lowercaseId": null, "symbol": "ADA/USDT:USDT", "base": "ADA", "quote": "USDT", "settle": "USDT", "baseId": "ADA", "quoteId": "USDT", "settleId": "USDT", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": 0.0006, "maker": 0.0002, "contractSize": 1, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1, "price": 0.0001}, "limits": {"leverage": {"min": 1, "max": 75}, "amount": {"min": 1, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": 5, "max": null}}, "marginModes": {"cross": true, "isolated": true}, "created": null, "info": {"symbol": "ADAUSDT", "baseCoin": "ADA", "quoteCoin": "USDT", "buyLimitPriceRatio": "0.05", "sellLimitPriceRatio": "0.05", "feeRateUpRatio": "0.005", "makerFeeRate": "0.0002", "takerFeeRate": "0.0006", "openCostUpRatio": "0.01", "supportMarginCoins": ["USDT"], "minTradeNum": "1", "priceEndStep": "1", "volumePlace": "0", "pricePlace": "4", "sizeMultiplier": "1", "symbolType": "perpetual", "minTradeUSDT": "5", "maxSymbolOrderNum": "200", "maxProductOrderNum": "1000", "maxPositionNum": "150", "symbolStatus": "normal", "offTime": "-1", "limitOpenTime": "-1", "deliveryTime": "", "deliveryStartTime": "", "deliveryPeriod": "", "launchTime": "", "fundInterval": "8", "minLever": "1", "maxLever": "75", "posLimit": "0.1", "maintainTime": "", "openTime": "", "maxMarketOrderQty": "460000", "maxOrderQty": "4200000"}, "tierBased": null, "percentage": null}, "XRP/USDT:USDT": {"id": "XRPUSDT", "lowercaseId": null, "symbol": "XRP/USDT:USDT", "base": "XRP", "quote": "USDT", "settle": "USDT", "baseId": "XRP", "quoteId": "USDT", "settleId": "USDT", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": 0.0006, "maker": 0.0002, "contractSize": 1, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1, "price": 0.0001}, "limits": {"leverage": {"min": 1, "max": 125}, "amount": {"min": 1, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": 5, "max": null}}, "marginModes": {"cross": true, "isolated": true}, "created": null, "info": {"symbol": "XRPUSDT", "baseCoin": "XRP", "quoteCoin": "USDT", "buyLimitPriceRatio": "0.05", "sellLimitPriceRatio": "0.05", "feeRateUpRatio": "0.005", "makerFeeRate": "0.0002", "takerFeeRate": "0.0006", "openCostUpRatio": "0.01", "supportMarginCoins": ["USDT"], "minTradeNum": "1", "priceEndStep": "1", "volumePlace": "0", "pricePlace": "4", "sizeMultiplier": "1", "symbolType": "perpetual", "minTradeUSDT": "5", "maxSymbolOrderNum": "200", "maxProductOrderNum": "1000", "maxPositionNum": "150", "symbolStatus": "normal", "offTime": "-1", "limitOpenTime": "-1", "deliveryTime": "", "deliveryStartTime": "", "deliveryPeriod": "", "launchTime": "", "fundInterval": "8", "minLever": "1", "maxLever": "125", "posLimit": "0.1", "maintainTime": "", "openTime": "", "maxMarketOrderQty": "1200000", "maxOrderQty": "10000000"}, "tierBased": null, "percentage": null}, "DOGE/USDT:USDT": {"id": "DOGEUSDT", "lowercaseId": null, "symbol": "DOGE/USDT:USDT", "base": "DOGE", "quote": "USDT", "settle": "USDT", "baseId": "DOGE", "quoteId": "USDT", "settleId": "USDT", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": 0.0006, "maker": 0.0002, "contractSize": 1, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1, "price": 1e-05}, "limits": {"leverage": {"min": 1, "max": 75}, "amount": {"min": 1, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": 5, "max": null}}, "marginModes": {"cross": true, "isolated": true}, "created": null, "info": {"symbol": "DOGEUSDT", "baseCoin": "DOGE", "quoteCoin": "USDT", "buyLimitPriceRatio": "0.05", "sellLimitPriceRatio": "0.05", "feeRateUpRatio": "0.005", "makerFeeRate": "0.0002", "takerFeeRate": "0.0006", "openCostUpRatio": "0.01", "supportMarginCoins": ["USDT"], "minTradeNum": "1", "priceEndStep": "1", "volumePlace": "0", "pricePlace": "5", "sizeMultiplier": "1", "symbolType": "perpetual", "minTradeUSDT": "5", "maxSymbolOrderNum": "200", "maxProductOrderNum": "1000", "maxPositionNum": "150", "symbolStatus": "normal", "offTime": "-1", "limitOpenTime": "-1", "deliveryTime": "", "deliveryStartTime": "", "deliveryPeriod": "", "launchTime": "", "fundInterval": "8", "minLever": "1", "maxLever": "75", "posLimit": "0.1", "maintainTime": "", "openTime": "", "maxMarketOrderQty": "13000000", "maxOrderQty": "65000000"}, "tierBased": null, "percentage": null}, "TRX/USDT:USDT": {"id": "TRXUSDT", "lowercaseId": null, "symbol": "TRX/USDT:USDT", "base": "TRX", "quote": "USDT", "settle": "USDT", "baseId": "TRX", "quoteId": "USDT", "settleId": "USDT", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": 0.0006, "maker": 0.0002, "contractSize": 1, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1, "price": 1e-05}, "limits": {"leverage": {"min": 1, "max": 75}, "amount": {"min": 1, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": 5, "max": null}}, "marginModes": {"cross": true, "isolated": true}, "created": null, "info": {"symbol": "TRXUSDT", "baseCoin": "TRX", "quoteCoin": "USDT", "buyLimitPriceRatio": "0.05", "sellLimitPriceRatio": "0.05", "feeRateUpRatio": "0.005", "makerFeeRate": "0.0002", "takerFeeRate": "0.0006", "openCostUpRatio": "0.01", "supportMarginCoins": ["USDT"], "minTradeNum": "1", "priceEndStep": "1", "volumePlace": "0", "pricePlace": "5", "sizeMultiplier": "1", "symbolType": "perpetual", "minTradeUSDT": "5", "maxSymbolOrderNum": "200", "maxProductOrderNum": "1000", "maxPositionNum": "150", "symbolStatus": "normal", "offTime": "-1", "limitOpenTime": "-1", "deliveryTime": "", "deliveryStartTime": "", "deliveryPeriod": "", "launchTime": "", "fundInterval": "8", "minLever": "1", "maxLever": "75", "posLimit": "0.1", "maintainTime": "", "openTime": "", "maxMarketOrderQty": "1900000", "maxOrderQty": "9500000"}, "tierBased": null, "percentage": null}}