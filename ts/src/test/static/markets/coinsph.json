{"BTC/USDT": {"id": "BTCUSDT", "lowercaseId": null, "symbol": "BTC/USDT", "base": "BTC", "quote": "USDT", "settle": null, "baseId": "BTC", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": null, "maker": null, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1e-07, "price": 0.01}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 1e-07, "max": 99999999999}, "price": {"min": 0.01, "max": 99999999999}, "cost": {"min": 1, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"symbol": "BTCUSDT", "status": "trading", "baseAsset": "BTC", "baseAssetPrecision": "7", "quoteAsset": "USDT", "quoteAssetPrecision": "9", "orderTypes": ["LIMIT", "MARKET", "LIMIT_MAKER", "STOP_LOSS_LIMIT", "STOP_LOSS", "TAKE_PROFIT_LIMIT", "TAKE_PROFIT"], "filters": [{"filterType": "PRICE_FILTER", "minPrice": "0.01", "maxPrice": "99999999999.00000000", "tickSize": "0.01"}, {"filterType": "LOT_SIZE", "minQty": "0.0000001", "maxQty": "99999999999.00000000", "stepSize": "0.0000001"}, {"filterType": "NOTIONAL", "minNotional": "1", "maxNotional": "50000"}, {"filterType": "MIN_NOTIONAL", "minNotional": "1"}, {"filterType": "STATIC_PRICE_RANGE", "priceUp": "99999999.000000000000000000", "priceDown": "0.010000000000000000"}, {"filterType": "PERCENT_PRICE_BY_SIDE", "bidMultiplierUp": "5.00000000", "bidMultiplierDown": "0.20000000", "askMultiplierUp": "5.00000000", "askMultiplierDown": "0.20000000"}, {"filterType": "PERCENT_PRICE_ORDER_SIZE", "multiplierUp": "1.10000000", "multiplierDown": "0.90000000"}, {"filterType": "MAX_NUM_ORDERS", "maxNumOrders": "200"}, {"filterType": "MAX_NUM_ALGO_ORDERS", "maxNumAlgoOrders": "5"}]}, "tierBased": true, "percentage": true, "feeSide": "get", "tiers": {"taker": [[0, 0.003], [500000, 0.0027], [1000000, 0.0024], [2500000, 0.002], [5000000, 0.0018], [10000000, 0.0015], [100000000, 0.0012], [500000000, 0.0009], [1000000000, 0.0007], [2500000000, 0.0005]], "maker": [[0, 0.0025], [500000, 0.0022], [1000000, 0.0018], [2500000, 0.0015], [5000000, 0.0012], [10000000, 0.001], [100000000, 0.0008], [500000000, 0.0007], [1000000000, 0.0006], [2500000000, 0.0005]]}}, "ETH/USDT": {"id": "ETHUSDT", "lowercaseId": null, "symbol": "ETH/USDT", "base": "ETH", "quote": "USDT", "settle": null, "baseId": "ETH", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": null, "maker": null, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1e-06, "price": 0.01}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 1e-06, "max": 99999999999}, "price": {"min": 0.01, "max": 99999999999}, "cost": {"min": 1, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"symbol": "ETHUSDT", "status": "trading", "baseAsset": "ETH", "baseAssetPrecision": "6", "quoteAsset": "USDT", "quoteAssetPrecision": "8", "orderTypes": ["LIMIT", "MARKET", "LIMIT_MAKER", "STOP_LOSS_LIMIT", "STOP_LOSS", "TAKE_PROFIT_LIMIT", "TAKE_PROFIT"], "filters": [{"filterType": "PRICE_FILTER", "minPrice": "0.01", "maxPrice": "99999999999.00000000", "tickSize": "0.01"}, {"filterType": "LOT_SIZE", "minQty": "0.000001", "maxQty": "99999999999.00000000", "stepSize": "0.000001"}, {"filterType": "NOTIONAL", "minNotional": "1", "maxNotional": "50000"}, {"filterType": "MIN_NOTIONAL", "minNotional": "1"}, {"filterType": "STATIC_PRICE_RANGE", "priceUp": "99999999.000000000000000000", "priceDown": "0.010000000000000000"}, {"filterType": "PERCENT_PRICE_BY_SIDE", "bidMultiplierUp": "5.00000000", "bidMultiplierDown": "0.20000000", "askMultiplierUp": "5.00000000", "askMultiplierDown": "0.20000000"}, {"filterType": "PERCENT_PRICE_ORDER_SIZE", "multiplierUp": "1.10000000", "multiplierDown": "0.90000000"}, {"filterType": "MAX_NUM_ORDERS", "maxNumOrders": "200"}, {"filterType": "MAX_NUM_ALGO_ORDERS", "maxNumAlgoOrders": "5"}]}, "tierBased": true, "percentage": true, "feeSide": "get", "tiers": {"taker": [[0, 0.003], [500000, 0.0027], [1000000, 0.0024], [2500000, 0.002], [5000000, 0.0018], [10000000, 0.0015], [100000000, 0.0012], [500000000, 0.0009], [1000000000, 0.0007], [2500000000, 0.0005]], "maker": [[0, 0.0025], [500000, 0.0022], [1000000, 0.0018], [2500000, 0.0015], [5000000, 0.0012], [10000000, 0.001], [100000000, 0.0008], [500000000, 0.0007], [1000000000, 0.0006], [2500000000, 0.0005]]}}}