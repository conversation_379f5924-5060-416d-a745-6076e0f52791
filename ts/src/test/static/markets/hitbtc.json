{"BTC/USDT": {"id": "BTCUSDT", "lowercaseId": null, "symbol": "BTC/USDT", "base": "BTC", "quote": "USDT", "settle": null, "baseId": "BTC", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": true, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.0025, "maker": 0.0012, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1e-05, "price": 0.01}, "limits": {"leverage": {"min": 1, "max": 25}, "amount": {"min": 1e-05, "max": null}, "price": {"min": 0.01, "max": null}, "cost": {"min": 1e-07, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"type": "spot", "base_currency": "BTC", "quote_currency": "USDT", "status": "working", "quantity_increment": "0.00001", "tick_size": "0.01", "take_rate": "0.0025", "make_rate": "0.0012", "fee_currency": "USDT", "margin_trading": true, "max_initial_leverage": "25.00"}, "tierBased": true, "percentage": true, "tiers": {"maker": [[0, 0.0009], [10, 0.0007], [100, 0.0006], [500, 0.0005], [1000, 0.0003], [5000, 0.0002], [10000, 0.0001], [20000, 0], [50000, -0.0001], [100000, -0.0001]], "taker": [[0, 0.0009], [10, 0.0008], [100, 0.0007], [500, 0.0007], [1000, 0.0006], [5000, 0.0006], [10000, 0.0005], [20000, 0.0004], [50000, 0.0003], [100000, 0.0002]]}, "feeCurrency": "USDT"}, "LTC/USDT": {"id": "LTCUSDT", "lowercaseId": null, "symbol": "LTC/USDT", "base": "LTC", "quote": "USDT", "settle": null, "baseId": "LTC", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": true, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.0025, "maker": 0.0012, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.001, "price": 0.0001}, "limits": {"leverage": {"min": 1, "max": 20}, "amount": {"min": 0.001, "max": null}, "price": {"min": 0.0001, "max": null}, "cost": {"min": 1e-07, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"type": "spot", "base_currency": "LTC", "quote_currency": "USDT", "status": "working", "quantity_increment": "0.001", "tick_size": "0.0001", "take_rate": "0.0025", "make_rate": "0.0012", "fee_currency": "USDT", "margin_trading": true, "max_initial_leverage": "20.00"}, "tierBased": true, "percentage": true, "tiers": {"maker": [[0, 0.0009], [10, 0.0007], [100, 0.0006], [500, 0.0005], [1000, 0.0003], [5000, 0.0002], [10000, 0.0001], [20000, 0], [50000, -0.0001], [100000, -0.0001]], "taker": [[0, 0.0009], [10, 0.0008], [100, 0.0007], [500, 0.0007], [1000, 0.0006], [5000, 0.0006], [10000, 0.0005], [20000, 0.0004], [50000, 0.0003], [100000, 0.0002]]}, "feeCurrency": "USDT"}, "ADA/USDT": {"id": "ADAUSDT", "lowercaseId": null, "symbol": "ADA/USDT", "base": "ADA", "quote": "USDT", "settle": null, "baseId": "ADA", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": true, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.0025, "maker": 0.0012, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1, "price": 1e-07}, "limits": {"leverage": {"min": 1, "max": 10}, "amount": {"min": 1, "max": null}, "price": {"min": 1e-07, "max": null}, "cost": {"min": 1e-07, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"type": "spot", "base_currency": "ADA", "quote_currency": "USDT", "status": "working", "quantity_increment": "1", "tick_size": "0.0000001", "take_rate": "0.0025", "make_rate": "0.0012", "fee_currency": "USDT", "margin_trading": true, "max_initial_leverage": "10.00"}, "tierBased": true, "percentage": true, "tiers": {"maker": [[0, 0.0009], [10, 0.0007], [100, 0.0006], [500, 0.0005], [1000, 0.0003], [5000, 0.0002], [10000, 0.0001], [20000, 0], [50000, -0.0001], [100000, -0.0001]], "taker": [[0, 0.0009], [10, 0.0008], [100, 0.0007], [500, 0.0007], [1000, 0.0006], [5000, 0.0006], [10000, 0.0005], [20000, 0.0004], [50000, 0.0003], [100000, 0.0002]]}, "feeCurrency": "USDT"}, "XRP/USDT": {"id": "XRPUSDT", "lowercaseId": null, "symbol": "XRP/USDT", "base": "XRP", "quote": "USDT", "settle": null, "baseId": "XRP", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": true, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.0025, "maker": 0.0012, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.1, "price": 1e-06}, "limits": {"leverage": {"min": 1, "max": 20}, "amount": {"min": 0.1, "max": null}, "price": {"min": 1e-06, "max": null}, "cost": {"min": 1e-07, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"type": "spot", "base_currency": "XRP", "quote_currency": "USDT", "status": "working", "quantity_increment": "0.1", "tick_size": "0.000001", "take_rate": "0.0025", "make_rate": "0.0012", "fee_currency": "USDT", "margin_trading": true, "max_initial_leverage": "20.00"}, "tierBased": true, "percentage": true, "tiers": {"maker": [[0, 0.0009], [10, 0.0007], [100, 0.0006], [500, 0.0005], [1000, 0.0003], [5000, 0.0002], [10000, 0.0001], [20000, 0], [50000, -0.0001], [100000, -0.0001]], "taker": [[0, 0.0009], [10, 0.0008], [100, 0.0007], [500, 0.0007], [1000, 0.0006], [5000, 0.0006], [10000, 0.0005], [20000, 0.0004], [50000, 0.0003], [100000, 0.0002]]}, "feeCurrency": "USDT"}, "SOL/USDT": {"id": "SOLUSDT", "lowercaseId": null, "symbol": "SOL/USDT", "base": "SOL", "quote": "USDT", "settle": null, "baseId": "SOL", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": true, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.0025, "maker": 0.0012, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.001, "price": 0.0001}, "limits": {"leverage": {"min": 1, "max": 5}, "amount": {"min": 0.001, "max": null}, "price": {"min": 0.0001, "max": null}, "cost": {"min": 1e-07, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"type": "spot", "base_currency": "SOL", "quote_currency": "USDT", "status": "working", "quantity_increment": "0.001", "tick_size": "0.0001", "take_rate": "0.0025", "make_rate": "0.0012", "fee_currency": "USDT", "margin_trading": true, "max_initial_leverage": "5.00"}, "tierBased": true, "percentage": true, "tiers": {"maker": [[0, 0.0009], [10, 0.0007], [100, 0.0006], [500, 0.0005], [1000, 0.0003], [5000, 0.0002], [10000, 0.0001], [20000, 0], [50000, -0.0001], [100000, -0.0001]], "taker": [[0, 0.0009], [10, 0.0008], [100, 0.0007], [500, 0.0007], [1000, 0.0006], [5000, 0.0006], [10000, 0.0005], [20000, 0.0004], [50000, 0.0003], [100000, 0.0002]]}, "feeCurrency": "USDT"}, "TRX/USDT": {"id": "TRXUSDT", "lowercaseId": null, "symbol": "TRX/USDT", "base": "TRX", "quote": "USDT", "settle": null, "baseId": "TRX", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": true, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.0025, "maker": 0.0012, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1, "price": 1e-07}, "limits": {"leverage": {"min": 1, "max": 10}, "amount": {"min": 1, "max": null}, "price": {"min": 1e-07, "max": null}, "cost": {"min": 1e-07, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"type": "spot", "base_currency": "TRX", "quote_currency": "USDT", "status": "working", "quantity_increment": "1", "tick_size": "0.0000001", "take_rate": "0.0025", "make_rate": "0.0012", "fee_currency": "USDT", "margin_trading": true, "max_initial_leverage": "10.00"}, "tierBased": true, "percentage": true, "tiers": {"maker": [[0, 0.0009], [10, 0.0007], [100, 0.0006], [500, 0.0005], [1000, 0.0003], [5000, 0.0002], [10000, 0.0001], [20000, 0], [50000, -0.0001], [100000, -0.0001]], "taker": [[0, 0.0009], [10, 0.0008], [100, 0.0007], [500, 0.0007], [1000, 0.0006], [5000, 0.0006], [10000, 0.0005], [20000, 0.0004], [50000, 0.0003], [100000, 0.0002]]}, "feeCurrency": "USDT"}, "LTC/USDT:USDT": {"id": "LTCUSDT_PERP", "lowercaseId": null, "symbol": "LTC/USDT:USDT", "base": "LTC", "quote": "USDT", "settle": "USDT", "baseId": "LTC", "quoteId": "USDT", "settleId": "USDT", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": 0.0007, "maker": 0.0002, "contractSize": 1, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.01, "price": 0.001}, "limits": {"leverage": {"min": 1, "max": 50}, "amount": {"min": 0.01, "max": null}, "price": {"min": 0.001, "max": null}, "cost": {"min": 1e-05, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"type": "futures", "contract_type": "perpetual", "expiry": null, "underlying": "LTC", "base_currency": null, "quote_currency": "USDT", "status": "working", "quantity_increment": "0.01", "tick_size": "0.001", "take_rate": "0.0007", "make_rate": "0.0002", "fee_currency": "USDT", "margin_trading": true, "max_initial_leverage": "50.00"}, "tierBased": true, "percentage": true, "tiers": {"maker": [[0, 0.0009], [10, 0.0007], [100, 0.0006], [500, 0.0005], [1000, 0.0003], [5000, 0.0002], [10000, 0.0001], [20000, 0], [50000, -0.0001], [100000, -0.0001]], "taker": [[0, 0.0009], [10, 0.0008], [100, 0.0007], [500, 0.0007], [1000, 0.0006], [5000, 0.0006], [10000, 0.0005], [20000, 0.0004], [50000, 0.0003], [100000, 0.0002]]}, "feeCurrency": "USDT"}, "BTC/USDT:USDT": {"id": "BTCUSDT_PERP", "lowercaseId": null, "symbol": "BTC/USDT:USDT", "base": "BTC", "quote": "USDT", "settle": "USDT", "baseId": "BTC", "quoteId": "USDT", "settleId": "USDT", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": 0.0007, "maker": 0.0002, "contractSize": 1, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.0001, "price": 0.01}, "limits": {"leverage": {"min": 1, "max": 100}, "amount": {"min": 0.0001, "max": null}, "price": {"min": 0.01, "max": null}, "cost": {"min": 1e-06, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"type": "futures", "contract_type": "perpetual", "expiry": null, "underlying": "BTC", "base_currency": null, "quote_currency": "USDT", "status": "working", "quantity_increment": "0.0001", "tick_size": "0.01", "take_rate": "0.0007", "make_rate": "0.0002", "fee_currency": "USDT", "margin_trading": true, "max_initial_leverage": "100.00"}, "tierBased": true, "percentage": true, "tiers": {"maker": [[0, 0.0009], [10, 0.0007], [100, 0.0006], [500, 0.0005], [1000, 0.0003], [5000, 0.0002], [10000, 0.0001], [20000, 0], [50000, -0.0001], [100000, -0.0001]], "taker": [[0, 0.0009], [10, 0.0008], [100, 0.0007], [500, 0.0007], [1000, 0.0006], [5000, 0.0006], [10000, 0.0005], [20000, 0.0004], [50000, 0.0003], [100000, 0.0002]]}, "feeCurrency": "USDT"}, "ADA/USDT:USDT": {"id": "ADAUSDT_PERP", "lowercaseId": null, "symbol": "ADA/USDT:USDT", "base": "ADA", "quote": "USDT", "settle": "USDT", "baseId": "ADA", "quoteId": "USDT", "settleId": "USDT", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": 0.0007, "maker": 0.0002, "contractSize": 1, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1, "price": 1e-05}, "limits": {"leverage": {"min": 1, "max": 75}, "amount": {"min": 1, "max": null}, "price": {"min": 1e-05, "max": null}, "cost": {"min": 1e-05, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"type": "futures", "contract_type": "perpetual", "expiry": null, "underlying": "ADA", "base_currency": null, "quote_currency": "USDT", "status": "working", "quantity_increment": "1", "tick_size": "0.00001", "take_rate": "0.0007", "make_rate": "0.0002", "fee_currency": "USDT", "margin_trading": true, "max_initial_leverage": "75.00"}, "tierBased": true, "percentage": true, "tiers": {"maker": [[0, 0.0009], [10, 0.0007], [100, 0.0006], [500, 0.0005], [1000, 0.0003], [5000, 0.0002], [10000, 0.0001], [20000, 0], [50000, -0.0001], [100000, -0.0001]], "taker": [[0, 0.0009], [10, 0.0008], [100, 0.0007], [500, 0.0007], [1000, 0.0006], [5000, 0.0006], [10000, 0.0005], [20000, 0.0004], [50000, 0.0003], [100000, 0.0002]]}, "feeCurrency": "USDT"}, "XRP/USDT:USDT": {"id": "XRPUSDT_PERP", "lowercaseId": null, "symbol": "XRP/USDT:USDT", "base": "XRP", "quote": "USDT", "settle": "USDT", "baseId": "XRP", "quoteId": "USDT", "settleId": "USDT", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": 0.0007, "maker": 0.0002, "contractSize": 1, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1, "price": 0.0001}, "limits": {"leverage": {"min": 1, "max": 75}, "amount": {"min": 1, "max": null}, "price": {"min": 0.0001, "max": null}, "cost": {"min": 0.0001, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"type": "futures", "contract_type": "perpetual", "expiry": null, "underlying": "XRP", "base_currency": null, "quote_currency": "USDT", "status": "working", "quantity_increment": "1", "tick_size": "0.0001", "take_rate": "0.0007", "make_rate": "0.0002", "fee_currency": "USDT", "margin_trading": true, "max_initial_leverage": "75.00"}, "tierBased": true, "percentage": true, "tiers": {"maker": [[0, 0.0009], [10, 0.0007], [100, 0.0006], [500, 0.0005], [1000, 0.0003], [5000, 0.0002], [10000, 0.0001], [20000, 0], [50000, -0.0001], [100000, -0.0001]], "taker": [[0, 0.0009], [10, 0.0008], [100, 0.0007], [500, 0.0007], [1000, 0.0006], [5000, 0.0006], [10000, 0.0005], [20000, 0.0004], [50000, 0.0003], [100000, 0.0002]]}, "feeCurrency": "USDT"}, "TRX/USDT:USDT": {"id": "TRXUSDT_PERP", "lowercaseId": null, "symbol": "TRX/USDT:USDT", "base": "TRX", "quote": "USDT", "settle": "USDT", "baseId": "TRX", "quoteId": "USDT", "settleId": "USDT", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": 0.0007, "maker": 0.0002, "contractSize": 1, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1, "price": 1e-07}, "limits": {"leverage": {"min": 1, "max": 50}, "amount": {"min": 1, "max": null}, "price": {"min": 1e-07, "max": null}, "cost": {"min": 1e-07, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"type": "futures", "contract_type": "perpetual", "expiry": null, "underlying": "TRX", "base_currency": null, "quote_currency": "USDT", "status": "working", "quantity_increment": "1", "tick_size": "0.0000001", "take_rate": "0.0007", "make_rate": "0.0002", "fee_currency": "USDT", "margin_trading": true, "max_initial_leverage": "50.00"}, "tierBased": true, "percentage": true, "tiers": {"maker": [[0, 0.0009], [10, 0.0007], [100, 0.0006], [500, 0.0005], [1000, 0.0003], [5000, 0.0002], [10000, 0.0001], [20000, 0], [50000, -0.0001], [100000, -0.0001]], "taker": [[0, 0.0009], [10, 0.0008], [100, 0.0007], [500, 0.0007], [1000, 0.0006], [5000, 0.0006], [10000, 0.0005], [20000, 0.0004], [50000, 0.0003], [100000, 0.0002]]}, "feeCurrency": "USDT"}, "ETH/USDT": {"id": "ETHUSDT", "lowercaseId": null, "symbol": "ETH/USDT", "base": "ETH", "quote": "USDT", "settle": null, "baseId": "ETH", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": true, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.0025, "maker": 0.0012, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.0001, "price": 0.001}, "limits": {"leverage": {"min": 1, "max": 20}, "amount": {"min": 0.0001, "max": null}, "price": {"min": 0.001, "max": null}, "cost": {"min": 1e-07, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"type": "spot", "base_currency": "ETH", "quote_currency": "USDT", "status": "working", "quantity_increment": "0.0001", "tick_size": "0.001", "take_rate": "0.0025", "make_rate": "0.0012", "fee_currency": "USDT", "margin_trading": true, "max_initial_leverage": "20.00"}, "tierBased": true, "percentage": true, "tiers": {"maker": [[0, 0.0009], [10, 0.0007], [100, 0.0006], [500, 0.0005], [1000, 0.0003], [5000, 0.0002], [10000, 0.0001], [20000, 0], [50000, -0.0001], [100000, -0.0001]], "taker": [[0, 0.0009], [10, 0.0008], [100, 0.0007], [500, 0.0007], [1000, 0.0006], [5000, 0.0006], [10000, 0.0005], [20000, 0.0004], [50000, 0.0003], [100000, 0.0002]]}, "feeCurrency": "USDT"}, "ETH/USDT:USDT": {"id": "ETHUSDT_PERP", "lowercaseId": null, "symbol": "ETH/USDT:USDT", "base": "ETH", "quote": "USDT", "settle": "USDT", "baseId": "ETH", "quoteId": "USDT", "settleId": "USDT", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": 0.0007, "maker": 0.0002, "contractSize": 1, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.001, "price": 0.001}, "limits": {"leverage": {"min": 1, "max": 75}, "amount": {"min": 0.001, "max": null}, "price": {"min": 0.001, "max": null}, "cost": {"min": 1e-06, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"type": "futures", "contract_type": "perpetual", "expiry": null, "underlying": "ETH", "base_currency": null, "quote_currency": "USDT", "status": "working", "quantity_increment": "0.001", "tick_size": "0.001", "take_rate": "0.0007", "make_rate": "0.0002", "fee_currency": "USDT", "margin_trading": true, "max_initial_leverage": "75.00"}, "tierBased": true, "percentage": true, "tiers": {"maker": [[0, 0.0009], [10, 0.0007], [100, 0.0006], [500, 0.0005], [1000, 0.0003], [5000, 0.0002], [10000, 0.0001], [20000, 0], [50000, -0.0001], [100000, -0.0001]], "taker": [[0, 0.0009], [10, 0.0008], [100, 0.0007], [500, 0.0007], [1000, 0.0006], [5000, 0.0006], [10000, 0.0005], [20000, 0.0004], [50000, 0.0003], [100000, 0.0002]]}, "feeCurrency": "USDT"}}