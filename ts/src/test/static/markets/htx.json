{"BTC/USDT": {"id": "btcusdt", "lowercaseId": "btcusdt", "symbol": "BTC/USDT", "base": "BTC", "quote": "USDT", "settle": null, "baseId": "btc", "quoteId": "usdt", "settleId": null, "type": "spot", "spot": true, "margin": true, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.002, "maker": 0.002, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1e-06, "price": 0.01, "cost": 1e-08}, "limits": {"leverage": {"min": 1, "max": 5, "superMax": 3}, "amount": {"min": 1e-05, "max": 1000}, "price": {"min": null, "max": null}, "cost": {"min": 5, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"base-currency": "btc", "quote-currency": "usdt", "price-precision": "2", "amount-precision": "6", "symbol-partition": "main", "symbol": "btcusdt", "state": "online", "value-precision": "8", "min-order-amt": "0.00001", "max-order-amt": "1000", "min-order-value": "5", "limit-order-min-order-amt": "0.00001", "limit-order-max-order-amt": "1000", "limit-order-max-buy-amt": "1000", "limit-order-max-sell-amt": "1000", "buy-limit-must-less-than": "1.1", "sell-limit-must-greater-than": "0.9", "sell-market-min-order-amt": "0.00001", "sell-market-max-order-amt": "14", "buy-market-max-order-value": "1500000", "market-sell-order-rate-must-less-than": "0.01", "market-buy-order-rate-must-less-than": "0.01", "leverage-ratio": "5", "super-margin-leverage-ratio": "3", "funding-leverage-ratio": "3", "api-trading": "enabled", "tags": "activities"}, "tierBased": false, "percentage": true, "feeSide": "get"}, "ETH/USDT": {"id": "<PERSON><PERSON><PERSON>", "lowercaseId": "<PERSON><PERSON><PERSON>", "symbol": "ETH/USDT", "base": "ETH", "quote": "USDT", "settle": null, "baseId": "eth", "quoteId": "usdt", "settleId": null, "type": "spot", "spot": true, "margin": true, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.002, "maker": 0.002, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.0001, "price": 0.01, "cost": 1e-08}, "limits": {"leverage": {"min": 1, "max": 5, "superMax": 3}, "amount": {"min": 0.0001, "max": 10000}, "price": {"min": null, "max": null}, "cost": {"min": 5, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"base-currency": "eth", "quote-currency": "usdt", "price-precision": "2", "amount-precision": "4", "symbol-partition": "main", "symbol": "<PERSON><PERSON><PERSON>", "state": "online", "value-precision": "8", "min-order-amt": "0.0001", "max-order-amt": "10000", "min-order-value": "5", "limit-order-min-order-amt": "0.0001", "limit-order-max-order-amt": "10000", "limit-order-max-buy-amt": "10000", "limit-order-max-sell-amt": "10000", "buy-limit-must-less-than": "1.1", "sell-limit-must-greater-than": "0.9", "sell-market-min-order-amt": "0.0001", "sell-market-max-order-amt": "592", "buy-market-max-order-value": "1500000", "market-sell-order-rate-must-less-than": "0.01", "market-buy-order-rate-must-less-than": "0.01", "leverage-ratio": "5", "super-margin-leverage-ratio": "3", "api-trading": "enabled", "tags": ""}, "tierBased": false, "percentage": true, "feeSide": "get"}, "ADA/USDT": {"id": "<PERSON><PERSON><PERSON>", "lowercaseId": "<PERSON><PERSON><PERSON>", "symbol": "ADA/USDT", "base": "ADA", "quote": "USDT", "settle": null, "baseId": "ada", "quoteId": "usdt", "settleId": null, "type": "spot", "spot": true, "margin": true, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.002, "maker": 0.002, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.0001, "price": 1e-06, "cost": 1e-08}, "limits": {"leverage": {"min": 1, "max": 4, "superMax": 3}, "amount": {"min": 0.1, "max": 5000000}, "price": {"min": null, "max": null}, "cost": {"min": 5, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"base-currency": "ada", "quote-currency": "usdt", "price-precision": "6", "amount-precision": "4", "symbol-partition": "main", "symbol": "<PERSON><PERSON><PERSON>", "state": "online", "value-precision": "8", "min-order-amt": "0.1", "max-order-amt": "5000000", "min-order-value": "5", "limit-order-min-order-amt": "0.1", "limit-order-max-order-amt": "5000000", "limit-order-max-buy-amt": "5000000", "limit-order-max-sell-amt": "5000000", "buy-limit-must-less-than": "1.1", "sell-limit-must-greater-than": "0.9", "sell-market-min-order-amt": "0.1", "sell-market-max-order-amt": "66825", "buy-market-max-order-value": "50000", "market-sell-order-rate-must-less-than": "0.03", "market-buy-order-rate-must-less-than": "0.03", "leverage-ratio": "4", "super-margin-leverage-ratio": "3", "api-trading": "enabled", "tags": ""}, "tierBased": false, "percentage": true, "feeSide": "get"}, "LTC/USDT": {"id": "ltcusdt", "lowercaseId": "ltcusdt", "symbol": "LTC/USDT", "base": "LTC", "quote": "USDT", "settle": null, "baseId": "ltc", "quoteId": "usdt", "settleId": null, "type": "spot", "spot": true, "margin": true, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.002, "maker": 0.002, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.0001, "price": 0.01, "cost": 1e-08}, "limits": {"leverage": {"min": 1, "max": 5, "superMax": 3}, "amount": {"min": 0.001, "max": 100000}, "price": {"min": null, "max": null}, "cost": {"min": 5, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"base-currency": "ltc", "quote-currency": "usdt", "price-precision": "2", "amount-precision": "4", "symbol-partition": "main", "symbol": "ltcusdt", "state": "online", "value-precision": "8", "min-order-amt": "0.001", "max-order-amt": "100000", "min-order-value": "5", "limit-order-min-order-amt": "0.001", "limit-order-max-order-amt": "100000", "limit-order-max-buy-amt": "100000", "limit-order-max-sell-amt": "100000", "buy-limit-must-less-than": "1.1", "sell-limit-must-greater-than": "0.9", "sell-market-min-order-amt": "0.001", "sell-market-max-order-amt": "1055", "buy-market-max-order-value": "100000", "market-sell-order-rate-must-less-than": "0.1", "market-buy-order-rate-must-less-than": "0.1", "leverage-ratio": "5", "super-margin-leverage-ratio": "3", "api-trading": "enabled", "tags": ""}, "tierBased": false, "percentage": true, "feeSide": "get"}, "XRP/USDT": {"id": "xrpusdt", "lowercaseId": "xrpusdt", "symbol": "XRP/USDT", "base": "XRP", "quote": "USDT", "settle": null, "baseId": "xrp", "quoteId": "usdt", "settleId": null, "type": "spot", "spot": true, "margin": true, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.002, "maker": 0.002, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.01, "price": 1e-05, "cost": 1e-08}, "limits": {"leverage": {"min": 1, "max": 5, "superMax": 3}, "amount": {"min": 1, "max": 5000000}, "price": {"min": null, "max": null}, "cost": {"min": 5, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"base-currency": "xrp", "quote-currency": "usdt", "price-precision": "5", "amount-precision": "2", "symbol-partition": "main", "symbol": "xrpusdt", "state": "online", "value-precision": "8", "min-order-amt": "1", "max-order-amt": "5000000", "min-order-value": "5", "limit-order-min-order-amt": "1", "limit-order-max-order-amt": "5000000", "limit-order-max-buy-amt": "5000000", "limit-order-max-sell-amt": "5000000", "buy-limit-must-less-than": "1.1", "sell-limit-must-greater-than": "0.9", "sell-market-min-order-amt": "1", "sell-market-max-order-amt": "63680", "buy-market-max-order-value": "150000", "market-sell-order-rate-must-less-than": "0.03", "market-buy-order-rate-must-less-than": "0.03", "leverage-ratio": "5", "super-margin-leverage-ratio": "3", "api-trading": "enabled", "tags": ""}, "tierBased": false, "percentage": true, "feeSide": "get"}, "DOGE/USDT": {"id": "<PERSON><PERSON><PERSON>", "lowercaseId": "<PERSON><PERSON><PERSON>", "symbol": "DOGE/USDT", "base": "DOGE", "quote": "USDT", "settle": null, "baseId": "doge", "quoteId": "usdt", "settleId": null, "type": "spot", "spot": true, "margin": true, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.002, "maker": 0.002, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.01, "price": 1e-06, "cost": 1e-08}, "limits": {"leverage": {"min": 1, "max": 3, "superMax": 3}, "amount": {"min": 1, "max": 200000000}, "price": {"min": null, "max": null}, "cost": {"min": 5, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"base-currency": "doge", "quote-currency": "usdt", "price-precision": "6", "amount-precision": "2", "symbol-partition": "main", "symbol": "<PERSON><PERSON><PERSON>", "state": "online", "value-precision": "8", "min-order-amt": "1", "max-order-amt": "200000000", "min-order-value": "5", "limit-order-min-order-amt": "1", "limit-order-max-order-amt": "200000000", "limit-order-max-buy-amt": "200000000", "limit-order-max-sell-amt": "200000000", "buy-limit-must-less-than": "1.1", "sell-limit-must-greater-than": "0.9", "sell-market-min-order-amt": "1", "sell-market-max-order-amt": "441426", "buy-market-max-order-value": "100000", "market-sell-order-rate-must-less-than": "0.02", "market-buy-order-rate-must-less-than": "0.02", "leverage-ratio": "3", "super-margin-leverage-ratio": "3", "api-trading": "enabled", "tags": ""}, "tierBased": false, "percentage": true, "feeSide": "get"}, "TRX/USDT": {"id": "trxusdt", "lowercaseId": "trxusdt", "symbol": "TRX/USDT", "base": "TRX", "quote": "USDT", "settle": null, "baseId": "trx", "quoteId": "usdt", "settleId": null, "type": "spot", "spot": true, "margin": true, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.002, "maker": 0.002, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.01, "price": 1e-06, "cost": 1e-08}, "limits": {"leverage": {"min": 1, "max": 5, "superMax": 3}, "amount": {"min": 1, "max": 38000000}, "price": {"min": null, "max": null}, "cost": {"min": 5, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"base-currency": "trx", "quote-currency": "usdt", "price-precision": "6", "amount-precision": "2", "symbol-partition": "main", "symbol": "trxusdt", "state": "online", "value-precision": "8", "min-order-amt": "1", "max-order-amt": "38000000", "min-order-value": "5", "limit-order-min-order-amt": "1", "limit-order-max-order-amt": "38000000", "limit-order-max-buy-amt": "38000000", "limit-order-max-sell-amt": "38000000", "buy-limit-must-less-than": "1.1", "sell-limit-must-greater-than": "0.9", "sell-market-min-order-amt": "1", "sell-market-max-order-amt": "553942", "buy-market-max-order-value": "150000", "market-sell-order-rate-must-less-than": "0.03", "market-buy-order-rate-must-less-than": "0.03", "leverage-ratio": "5", "super-margin-leverage-ratio": "3", "api-trading": "enabled", "tags": "activities"}, "tierBased": false, "percentage": true, "feeSide": "get"}, "BTC/USDT:USDT": {"id": "BTC-USDT", "lowercaseId": "btc-usdt", "symbol": "BTC/USDT:USDT", "base": "BTC", "quote": "USDT", "settle": "USDT", "baseId": "btc", "quoteId": "usdt", "settleId": "usdt", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": 0.0005, "maker": 0.0002, "contractSize": 0.001, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1, "price": 0.1, "cost": null}, "limits": {"leverage": {"min": 1, "max": 1, "superMax": 1}, "amount": {"min": 0.001, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": 1603238400000, "info": {"symbol": "BTC", "contract_code": "BTC-USDT", "contract_size": "0.001000000000000000", "price_tick": "0.100000000000000000", "delivery_date": "", "delivery_time": "", "create_date": "20201021", "contract_status": "1", "adjust": [], "price_estimated": [], "settlement_date": "1747929600000", "support_margin_mode": "all", "business_type": "swap", "pair": "BTC-USDT", "contract_type": "swap", "trade_partition": "USDT"}, "tierBased": false, "percentage": true, "feeSide": "get"}, "BTC/USD:BTC": {"id": "BTC-USD", "lowercaseId": "btc-usd", "symbol": "BTC/USD:BTC", "base": "BTC", "quote": "USD", "settle": "BTC", "baseId": "btc", "quoteId": "usd", "settleId": "btc", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": false, "inverse": true, "subType": "inverse", "taker": 0.0005, "maker": 0.0002, "contractSize": 100, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1, "price": 0.1, "cost": null}, "limits": {"leverage": {"min": 1, "max": 1, "superMax": 1}, "amount": {"min": null, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": 100, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": 1585094400000, "info": {"symbol": "BTC", "contract_code": "BTC-USD", "contract_size": "100.000000000000000000", "price_tick": "0.100000000000000000", "delivery_time": "", "create_date": "20200325", "contract_status": "1", "settlement_date": "1747929600000"}, "tierBased": false, "percentage": true, "feeSide": "get"}, "ETH/USDT:USDT": {"id": "ETH-USDT", "lowercaseId": "eth-usdt", "symbol": "ETH/USDT:USDT", "base": "ETH", "quote": "USDT", "settle": "USDT", "baseId": "eth", "quoteId": "usdt", "settleId": "usdt", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": 0.0005, "maker": 0.0002, "contractSize": 0.01, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1, "price": 0.01, "cost": null}, "limits": {"leverage": {"min": 1, "max": 1, "superMax": 1}, "amount": {"min": 0.01, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": 1603238400000, "info": {"symbol": "ETH", "contract_code": "ETH-USDT", "contract_size": "0.010000000000000000", "price_tick": "0.010000000000000000", "delivery_date": "", "delivery_time": "", "create_date": "20201021", "contract_status": "1", "adjust": [], "price_estimated": [], "settlement_date": "1747929600000", "support_margin_mode": "all", "business_type": "swap", "pair": "ETH-USDT", "contract_type": "swap", "trade_partition": "USDT"}, "tierBased": false, "percentage": true, "feeSide": "get"}, "LTC/USDT:USDT": {"id": "LTC-USDT", "lowercaseId": "ltc-usdt", "symbol": "LTC/USDT:USDT", "base": "LTC", "quote": "USDT", "settle": "USDT", "baseId": "ltc", "quoteId": "usdt", "settleId": "usdt", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": 0.0005, "maker": 0.0002, "contractSize": 0.1, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1, "price": 0.01, "cost": null}, "limits": {"leverage": {"min": 1, "max": 1, "superMax": 1}, "amount": {"min": 0.1, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": 1605052800000, "info": {"symbol": "LTC", "contract_code": "LTC-USDT", "contract_size": "0.100000000000000000", "price_tick": "0.010000000000000000", "delivery_date": "", "delivery_time": "", "create_date": "20201111", "contract_status": "1", "adjust": [], "price_estimated": [], "settlement_date": "1747929600000", "support_margin_mode": "all", "business_type": "swap", "pair": "LTC-USDT", "contract_type": "swap", "trade_partition": "USDT"}, "tierBased": false, "percentage": true, "feeSide": "get"}, "ADA/USDT:USDT": {"id": "ADA-USDT", "lowercaseId": "ada-usdt", "symbol": "ADA/USDT:USDT", "base": "ADA", "quote": "USDT", "settle": "USDT", "baseId": "ada", "quoteId": "usdt", "settleId": "usdt", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": 0.0005, "maker": 0.0002, "contractSize": 10, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1, "price": 1e-06, "cost": null}, "limits": {"leverage": {"min": 1, "max": 1, "superMax": 1}, "amount": {"min": 10, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": 1605830400000, "info": {"symbol": "ADA", "contract_code": "ADA-USDT", "contract_size": "10.000000000000000000", "price_tick": "0.000001000000000000", "delivery_date": "", "delivery_time": "", "create_date": "20201120", "contract_status": "1", "adjust": [], "price_estimated": [], "settlement_date": "1747929600000", "support_margin_mode": "all", "business_type": "swap", "pair": "ADA-USDT", "contract_type": "swap", "trade_partition": "USDT"}, "tierBased": false, "percentage": true, "feeSide": "get"}, "XRP/USDT:USDT": {"id": "XRP-USDT", "lowercaseId": "xrp-usdt", "symbol": "XRP/USDT:USDT", "base": "XRP", "quote": "USDT", "settle": "USDT", "baseId": "xrp", "quoteId": "usdt", "settleId": "usdt", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": 0.0005, "maker": 0.0002, "contractSize": 10, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1, "price": 1e-05, "cost": null}, "limits": {"leverage": {"min": 1, "max": 1, "superMax": 1}, "amount": {"min": 10, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": 1605052800000, "info": {"symbol": "XRP", "contract_code": "XRP-USDT", "contract_size": "10.000000000000000000", "price_tick": "0.000010000000000000", "delivery_date": "", "delivery_time": "", "create_date": "20201111", "contract_status": "1", "adjust": [], "price_estimated": [], "settlement_date": "1747929600000", "support_margin_mode": "all", "business_type": "swap", "pair": "XRP-USDT", "contract_type": "swap", "trade_partition": "USDT"}, "tierBased": false, "percentage": true, "feeSide": "get"}, "DOGE/USDT:USDT": {"id": "DOGE-USDT", "lowercaseId": "doge-usdt", "symbol": "DOGE/USDT:USDT", "base": "DOGE", "quote": "USDT", "settle": "USDT", "baseId": "doge", "quoteId": "usdt", "settleId": "usdt", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": 0.0005, "maker": 0.0002, "contractSize": 100, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1, "price": 1e-06, "cost": null}, "limits": {"leverage": {"min": 1, "max": 1, "superMax": 1}, "amount": {"min": 100, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": 16***********, "info": {"symbol": "DOGE", "contract_code": "DOGE-USDT", "contract_size": "100.000000000000000000", "price_tick": "0.000001000000000000", "delivery_date": "", "delivery_time": "", "create_date": "20210113", "contract_status": "1", "adjust": [], "price_estimated": [], "settlement_date": "1747929600000", "support_margin_mode": "all", "business_type": "swap", "pair": "DOGE-USDT", "contract_type": "swap", "trade_partition": "USDT"}, "tierBased": false, "percentage": true, "feeSide": "get"}, "TRX/USDT:USDT": {"id": "TRX-USDT", "lowercaseId": "trx-usdt", "symbol": "TRX/USDT:USDT", "base": "TRX", "quote": "USDT", "settle": "USDT", "baseId": "trx", "quoteId": "usdt", "settleId": "usdt", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": 0.0005, "maker": 0.0002, "contractSize": 100, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1, "price": 1e-06, "cost": null}, "limits": {"leverage": {"min": 1, "max": 1, "superMax": 1}, "amount": {"min": 100, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": 1605052800000, "info": {"symbol": "TRX", "contract_code": "TRX-USDT", "contract_size": "100.000000000000000000", "price_tick": "0.000001000000000000", "delivery_date": "", "delivery_time": "", "create_date": "20201111", "contract_status": "1", "adjust": [], "price_estimated": [], "settlement_date": "1747929600000", "support_margin_mode": "all", "business_type": "swap", "pair": "TRX-USDT", "contract_type": "swap", "trade_partition": "USDT"}, "tierBased": false, "percentage": true, "feeSide": "get"}, "BTC/USD:BTC-231124": {"id": "BTC231124", "symbol": "BTC/USD:BTC-231124", "base": "BTC", "quote": "USD", "baseId": "btc", "quoteId": "USD", "active": true, "type": "future", "linear": false, "inverse": true, "spot": false, "swap": false, "future": true, "option": false, "margin": false, "contract": true, "contractSize": 100, "expiry": 1700812800000, "expiryDatetime": "2023-11-24T08:00:00.000Z", "optionType": null, "strike": null, "settle": "BTC", "settleId": "btc", "precision": {"amount": 1, "price": 0.01, "cost": null}, "limits": {"amount": {"min": null, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": 100, "max": null}, "leverage": {"min": 1, "max": 1, "superMax": 1}}, "info": {"symbol": "BTC", "contract_code": "BTC231124", "contract_type": "next_week", "contract_size": "100.000000000000000000", "price_tick": "0.010000000000000000", "delivery_date": "20231124", "delivery_time": "1700812800000", "create_date": "20231110", "contract_status": "1", "settlement_time": "1699776000000"}, "tierBased": false, "percentage": true, "taker": 0.0005, "maker": 0.0002, "feeSide": "get", "lowercaseId": "btc231124", "created": 1699574400000}, "ETH/USD:ETH": {"id": "ETH-USD", "lowercaseId": "eth-usd", "symbol": "ETH/USD:ETH", "base": "ETH", "quote": "USD", "settle": "ETH", "baseId": "eth", "quoteId": "usd", "settleId": "eth", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": false, "inverse": true, "subType": "inverse", "taker": 0.0005, "maker": 0.0002, "contractSize": 10, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1, "price": 0.01, "cost": null}, "limits": {"leverage": {"min": 1, "max": 1, "superMax": 1}, "amount": {"min": null, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": 10, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": 1585872000000, "info": {"symbol": "ETH", "contract_code": "ETH-USD", "contract_size": "10.000000000000000000", "price_tick": "0.010000000000000000", "delivery_time": "", "create_date": "20200403", "contract_status": "1", "settlement_date": "1747929600000"}, "tierBased": false, "percentage": true, "feeSide": "get"}, "BTC/USDT:USDT-240329": {"id": "BTC-USDT-240329", "lowercaseId": "btc-usdt-240329", "symbol": "BTC/USDT:USDT-240329", "base": "BTC", "quote": "USDT", "settle": "USDT", "baseId": "btc", "quoteId": "usdt", "settleId": "usdt", "type": "future", "spot": false, "margin": false, "swap": false, "future": true, "option": false, "active": true, "contract": true, "linear": true, "inverse": false, "taker": 0.0005, "maker": 0.0002, "contractSize": 0.001, "expiry": 1711699200000, "expiryDatetime": "2024-03-29T08:00:00.000Z", "precision": {"amount": 1, "price": 0.1}, "limits": {"leverage": {"min": 1, "max": 1, "superMax": 1}, "amount": {"min": 0.001}, "price": {}, "cost": {}}, "created": 1702598400000, "info": {"symbol": "BTC", "contract_code": "BTC-USDT-240329", "contract_size": "0.001000000000000000", "price_tick": "0.100000000000000000", "delivery_date": "20240329", "delivery_time": "1711699200000", "create_date": "20231215", "contract_status": "1", "settlement_date": "1711699200000", "support_margin_mode": "cross", "business_type": "futures", "pair": "BTC-USDT", "contract_type": "next_week", "trade_partition": "USDT"}}, "BTC/USD:BTC-240628": {"id": "BTC240628", "lowercaseId": "btc240628", "symbol": "BTC/USD:BTC-240628", "base": "BTC", "quote": "USD", "settle": "BTC", "baseId": "btc", "quoteId": "USD", "settleId": "btc", "type": "future", "spot": false, "margin": false, "swap": false, "future": true, "option": false, "active": true, "contract": true, "linear": false, "inverse": true, "taker": 0.0005, "maker": 0.0002, "contractSize": 100, "expiry": 1719561600000, "expiryDatetime": "2024-06-28T08:00:00.000Z", "precision": {"amount": 1, "price": 0.01}, "limits": {"leverage": {"min": 1, "max": 1, "superMax": 1}, "amount": {}, "price": {}, "cost": {"min": 100}}, "created": 1710460800000, "info": {"symbol": "BTC", "contract_code": "BTC240628", "contract_type": "quarter", "contract_size": "100.000000000000000000", "price_tick": "0.010000000000000000", "delivery_date": "20240628", "delivery_time": "1719561600000", "create_date": "20240315", "contract_status": "1", "settlement_time": "1710921600000"}}, "ETH-USDT-240607": {"id": "ETH-USDT-240607", "lowercaseId": "eth-usdt-240607", "symbol": "ETH/USDT:USDT-240607", "base": "ETH", "quote": "USDT", "settle": "USDT", "baseId": "eth", "quoteId": "usdt", "settleId": "usdt", "type": "future", "spot": false, "margin": false, "swap": false, "future": true, "option": false, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": 0.0005, "maker": 0.0002, "contractSize": 0.01, "expiry": 1717747200000, "expiryDatetime": "2024-06-07T08:00:00.000Z", "precision": {"amount": 1, "price": 0.01}, "limits": {"leverage": {"min": 1, "max": 1, "superMax": 1}, "amount": {"min": 0.01}, "price": {}, "cost": {}}, "created": 1716508800000, "info": {"symbol": "ETH", "contract_code": "ETH-USDT-240607", "contract_size": "0.010000000000000000", "price_tick": "0.010000000000000000", "delivery_date": "20240607", "delivery_time": "1717747200000", "create_date": "20240524", "contract_status": "1", "settlement_date": "1717747200000", "support_margin_mode": "cross", "business_type": "futures", "pair": "ETH-USDT", "contract_type": "next_week", "trade_partition": "USDT"}, "tierBased": false, "percentage": true, "feeSide": "get"}, "XRP/USD:XRP": {"id": "XRP-USD", "lowercaseId": "xrp-usd", "symbol": "XRP/USD:XRP", "base": "XRP", "quote": "USD", "settle": "XRP", "baseId": "xrp", "quoteId": "usd", "settleId": "xrp", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": false, "inverse": true, "subType": "inverse", "taker": 0.0005, "maker": 0.0002, "contractSize": 10, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1, "price": 1e-05, "cost": null}, "limits": {"leverage": {"min": 1, "max": 1, "superMax": 1}, "amount": {"min": null, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": 10, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": 1586476800000, "info": {"symbol": "XRP", "contract_code": "XRP-USD", "contract_size": "10.000000000000000000", "price_tick": "0.000010000000000000", "delivery_time": "", "create_date": "20200410", "contract_status": "1", "settlement_date": "1747929600000"}, "tierBased": false, "percentage": true, "feeSide": "get"}}