{"BTC/USDT": {"id": "btcusdt", "lowercaseId": null, "symbol": "BTC/USDT", "base": "BTC", "quote": "USDT", "settle": null, "baseId": "btc", "quoteId": "usdt", "settleId": null, "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "index": null, "active": null, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.001, "maker": 0.001, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1e-06, "price": 0.01}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": null, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": 1, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"id": "btcusdt", "name": "BTC/USDT", "ask_precision": "8", "bid_precision": "8", "enabled": true, "price_precision": "2", "amount_precision": "6", "usd_precision": "2", "minimum_trading_amount": "1.0"}, "tierBased": false, "percentage": true}, "ETH/USDT": {"id": "<PERSON><PERSON><PERSON>", "lowercaseId": null, "symbol": "ETH/USDT", "base": "ETH", "quote": "USDT", "settle": null, "baseId": "eth", "quoteId": "usdt", "settleId": null, "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "index": null, "active": null, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.001, "maker": 0.001, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1e-05, "price": 0.01}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": null, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": 1, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"id": "<PERSON><PERSON><PERSON>", "name": "ETH/USDT", "ask_precision": "8", "bid_precision": "8", "enabled": true, "price_precision": "2", "amount_precision": "5", "usd_precision": "2", "minimum_trading_amount": "1.0"}, "tierBased": false, "percentage": true}, "SHA/BTC": {"id": "shabtc", "lowercaseId": null, "symbol": "SHA/BTC", "base": "SHA", "quote": "BTC", "settle": null, "baseId": "sha", "quoteId": "btc", "settleId": null, "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "index": null, "active": null, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.001, "maker": 0.001, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1, "price": 1e-09}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": null, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": 0.0001, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"id": "shabtc", "name": "SHA/BTC", "ask_precision": "9", "bid_precision": "9", "enabled": true, "price_precision": "9", "amount_precision": "0", "usd_precision": "6", "minimum_trading_amount": "0.0001"}, "tierBased": false, "percentage": true}, "OCE/VET": {"id": "oce<PERSON>", "lowercaseId": null, "symbol": "OCE/VET", "base": "OCE", "quote": "VET", "settle": null, "baseId": "oce", "quoteId": "vet", "settleId": null, "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "index": null, "active": null, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.001, "maker": 0.001, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1, "price": 0.0001}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": null, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": 100, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"id": "oce<PERSON>", "name": "OCE/VET", "ask_precision": "8", "bid_precision": "8", "enabled": true, "price_precision": "4", "amount_precision": "0", "usd_precision": "7", "minimum_trading_amount": "100.0"}, "tierBased": false}}