{"BTC/USD": {"id": "btcusd", "symbol": "BTC/USD", "base": "BTC", "quote": "USD", "baseId": "btc", "quoteId": "usd", "active": true, "type": "spot", "spot": true, "swap": false, "future": false, "option": false, "margin": false, "contract": false, "precision": {"amount": 1e-08, "price": 1}, "limits": {"amount": {}, "price": {}, "cost": {"min": 10}, "leverage": {}}, "info": {"name": "BTC/USD", "url_symbol": "btcusd", "base_decimals": 8, "counter_decimals": 0, "instant_order_counter_decimals": 2, "minimum_order": "10 USD", "trading": "Enabled", "instant_and_market_orders": "Enabled", "description": "Bitcoin / U.S. dollar"}, "tierBased": true, "percentage": true, "taker": 0.005, "maker": 0.005, "tiers": {"taker": [[0, 0.005], [20000, 0.0025], [100000, 0.0024], [200000, 0.0022], [400000, 0.002], [600000, 0.0015], [1000000, 0.0014], [2000000, 0.0013], [4000000, 0.0012], [20000000, 0.0011], [50000000, 0.001], [100000000, 0.0007], [500000000, 0.0005], [2000000000, 0.0003], [6000000000, 0.0001], [20000000000, 5e-05], [20000000001, 0]], "maker": [[0, 0.005], [20000, 0.0025], [100000, 0.0024], [200000, 0.0022], [400000, 0.002], [600000, 0.0015], [1000000, 0.0014], [2000000, 0.0013], [4000000, 0.0012], [20000000, 0.0011], [50000000, 0.001], [100000000, 0.0007], [500000000, 0.0005], [2000000000, 0.0003], [6000000000, 0.0001], [20000000000, 5e-05], [20000000001, 0]]}, "marketId": "btc_usd"}, "BTC/EUR": {"id": "btceur", "symbol": "BTC/EUR", "base": "BTC", "quote": "EUR", "baseId": "btc", "quoteId": "eur", "active": true, "type": "spot", "spot": true, "swap": false, "future": false, "option": false, "margin": false, "contract": false, "precision": {"amount": 1e-08, "price": 1}, "limits": {"amount": {}, "price": {}, "cost": {"min": 10}, "leverage": {}}, "info": {"name": "BTC/EUR", "url_symbol": "btceur", "base_decimals": 8, "counter_decimals": 0, "instant_order_counter_decimals": 2, "minimum_order": "10 EUR", "trading": "Enabled", "instant_and_market_orders": "Enabled", "description": "Bitcoin / Euro"}, "tierBased": true, "percentage": true, "taker": 0.005, "maker": 0.005, "tiers": {"taker": [[0, 0.005], [20000, 0.0025], [100000, 0.0024], [200000, 0.0022], [400000, 0.002], [600000, 0.0015], [1000000, 0.0014], [2000000, 0.0013], [4000000, 0.0012], [20000000, 0.0011], [50000000, 0.001], [100000000, 0.0007], [500000000, 0.0005], [2000000000, 0.0003], [6000000000, 0.0001], [20000000000, 5e-05], [20000000001, 0]], "maker": [[0, 0.005], [20000, 0.0025], [100000, 0.0024], [200000, 0.0022], [400000, 0.002], [600000, 0.0015], [1000000, 0.0014], [2000000, 0.0013], [4000000, 0.0012], [20000000, 0.0011], [50000000, 0.001], [100000000, 0.0007], [500000000, 0.0005], [2000000000, 0.0003], [6000000000, 0.0001], [20000000000, 5e-05], [20000000001, 0]]}, "marketId": "btc_eur"}, "LTC/USD": {"id": "ltcusd", "symbol": "LTC/USD", "base": "LTC", "quote": "USD", "baseId": "ltc", "quoteId": "usd", "active": true, "type": "spot", "spot": true, "swap": false, "future": false, "option": false, "margin": false, "contract": false, "precision": {"amount": 1e-08, "price": 0.01}, "limits": {"amount": {}, "price": {}, "cost": {"min": 10}, "leverage": {}}, "info": {"name": "LTC/USD", "url_symbol": "ltcusd", "base_decimals": 8, "counter_decimals": 2, "instant_order_counter_decimals": 2, "minimum_order": "10.00 USD", "trading": "Enabled", "instant_and_market_orders": "Enabled", "description": "Litecoin / U.S. dollar"}, "tierBased": true, "percentage": true, "taker": 0.005, "maker": 0.005, "tiers": {"taker": [[0, 0.005], [20000, 0.0025], [100000, 0.0024], [200000, 0.0022], [400000, 0.002], [600000, 0.0015], [1000000, 0.0014], [2000000, 0.0013], [4000000, 0.0012], [20000000, 0.0011], [50000000, 0.001], [100000000, 0.0007], [500000000, 0.0005], [2000000000, 0.0003], [6000000000, 0.0001], [20000000000, 5e-05], [20000000001, 0]], "maker": [[0, 0.005], [20000, 0.0025], [100000, 0.0024], [200000, 0.0022], [400000, 0.002], [600000, 0.0015], [1000000, 0.0014], [2000000, 0.0013], [4000000, 0.0012], [20000000, 0.0011], [50000000, 0.001], [100000000, 0.0007], [500000000, 0.0005], [2000000000, 0.0003], [6000000000, 0.0001], [20000000000, 5e-05], [20000000001, 0]]}, "marketId": "ltc_usd"}, "ETH/USD": {"id": "et<PERSON>d", "symbol": "ETH/USD", "base": "ETH", "quote": "USD", "baseId": "eth", "quoteId": "usd", "active": true, "type": "spot", "spot": true, "swap": false, "future": false, "option": false, "margin": false, "contract": false, "precision": {"amount": 1e-08, "price": 0.1}, "limits": {"amount": {}, "price": {}, "cost": {"min": 10}, "leverage": {}}, "info": {"name": "ETH/USD", "url_symbol": "et<PERSON>d", "base_decimals": 8, "counter_decimals": 1, "instant_order_counter_decimals": 2, "minimum_order": "10.0 USD", "trading": "Enabled", "instant_and_market_orders": "Enabled", "description": "Ether / U.S. dollar"}, "tierBased": true, "percentage": true, "taker": 0.005, "maker": 0.005, "tiers": {"taker": [[0, 0.005], [20000, 0.0025], [100000, 0.0024], [200000, 0.0022], [400000, 0.002], [600000, 0.0015], [1000000, 0.0014], [2000000, 0.0013], [4000000, 0.0012], [20000000, 0.0011], [50000000, 0.001], [100000000, 0.0007], [500000000, 0.0005], [2000000000, 0.0003], [6000000000, 0.0001], [20000000000, 5e-05], [20000000001, 0]], "maker": [[0, 0.005], [20000, 0.0025], [100000, 0.0024], [200000, 0.0022], [400000, 0.002], [600000, 0.0015], [1000000, 0.0014], [2000000, 0.0013], [4000000, 0.0012], [20000000, 0.0011], [50000000, 0.001], [100000000, 0.0007], [500000000, 0.0005], [2000000000, 0.0003], [6000000000, 0.0001], [20000000000, 5e-05], [20000000001, 0]]}, "marketId": "eth_usd"}, "BTC/USDT": {"id": "btcusdt", "symbol": "BTC/USDT", "base": "BTC", "quote": "USDT", "baseId": "btc", "quoteId": "usdt", "active": true, "type": "spot", "spot": true, "swap": false, "future": false, "option": false, "margin": false, "contract": false, "precision": {"amount": 1e-08, "price": 1}, "limits": {"amount": {}, "price": {}, "cost": {"min": 10}, "leverage": {}}, "info": {"name": "BTC/USDT", "url_symbol": "btcusdt", "base_decimals": 8, "counter_decimals": 0, "instant_order_counter_decimals": 2, "minimum_order": "10 USDT", "trading": "Enabled", "instant_and_market_orders": "Enabled", "description": "Bitcoin / Tether"}, "tierBased": true, "percentage": true, "taker": 0.005, "maker": 0.005, "tiers": {"taker": [[0, 0.005], [20000, 0.0025], [100000, 0.0024], [200000, 0.0022], [400000, 0.002], [600000, 0.0015], [1000000, 0.0014], [2000000, 0.0013], [4000000, 0.0012], [20000000, 0.0011], [50000000, 0.001], [100000000, 0.0007], [500000000, 0.0005], [2000000000, 0.0003], [6000000000, 0.0001], [20000000000, 5e-05], [20000000001, 0]], "maker": [[0, 0.005], [20000, 0.0025], [100000, 0.0024], [200000, 0.0022], [400000, 0.002], [600000, 0.0015], [1000000, 0.0014], [2000000, 0.0013], [4000000, 0.0012], [20000000, 0.0011], [50000000, 0.001], [100000000, 0.0007], [500000000, 0.0005], [2000000000, 0.0003], [6000000000, 0.0001], [20000000000, 5e-05], [20000000001, 0]]}, "marketId": "btc_usdt"}, "XRP/USDT": {"id": "xrpusdt", "symbol": "XRP/USDT", "base": "XRP", "quote": "USDT", "baseId": "xrp", "quoteId": "usdt", "active": true, "type": "spot", "spot": true, "swap": false, "future": false, "option": false, "margin": false, "contract": false, "precision": {"amount": 1e-08, "price": 1e-05}, "limits": {"amount": {}, "price": {}, "cost": {"min": 10}, "leverage": {}}, "info": {"name": "XRP/USDT", "url_symbol": "xrpusdt", "base_decimals": 8, "counter_decimals": 5, "instant_order_counter_decimals": 5, "minimum_order": "10.00000 USDT", "trading": "Enabled", "instant_and_market_orders": "Enabled", "description": "XRP / Tether"}, "tierBased": true, "percentage": true, "taker": 0.005, "maker": 0.005, "tiers": {"taker": [[0, 0.005], [20000, 0.0025], [100000, 0.0024], [200000, 0.0022], [400000, 0.002], [600000, 0.0015], [1000000, 0.0014], [2000000, 0.0013], [4000000, 0.0012], [20000000, 0.0011], [50000000, 0.001], [100000000, 0.0007], [500000000, 0.0005], [2000000000, 0.0003], [6000000000, 0.0001], [20000000000, 5e-05], [20000000001, 0]], "maker": [[0, 0.005], [20000, 0.0025], [100000, 0.0024], [200000, 0.0022], [400000, 0.002], [600000, 0.0015], [1000000, 0.0014], [2000000, 0.0013], [4000000, 0.0012], [20000000, 0.0011], [50000000, 0.001], [100000000, 0.0007], [500000000, 0.0005], [2000000000, 0.0003], [6000000000, 0.0001], [20000000000, 5e-05], [20000000001, 0]]}, "marketId": "xrp_usdt"}, "ETH/USDT": {"id": "<PERSON><PERSON><PERSON>", "symbol": "ETH/USDT", "base": "ETH", "quote": "USDT", "baseId": "eth", "quoteId": "usdt", "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "active": true, "contract": false, "taker": 0.005, "maker": 0.005, "precision": {"amount": 1e-08, "price": 0.1}, "limits": {"leverage": {}, "amount": {}, "price": {}, "cost": {"min": 10}}, "info": {"name": "ETH/USDT", "url_symbol": "<PERSON><PERSON><PERSON>", "base_decimals": 8, "counter_decimals": 1, "instant_order_counter_decimals": 2, "minimum_order": "10.0 USDT", "trading": "Enabled", "instant_and_market_orders": "Enabled", "description": "<PERSON><PERSON> / Tether"}, "tierBased": true, "percentage": true, "tiers": {"taker": [[0, 0.005], [20000, 0.0025], [100000, 0.0024], [200000, 0.0022], [400000, 0.002], [600000, 0.0015], [1000000, 0.0014], [2000000, 0.0013], [4000000, 0.0012], [20000000, 0.0011], [50000000, 0.001], [100000000, 0.0007], [500000000, 0.0005], [2000000000, 0.0003], [6000000000, 0.0001], [20000000000, 5e-05], [20000000001, 0]], "maker": [[0, 0.005], [20000, 0.0025], [100000, 0.0024], [200000, 0.0022], [400000, 0.002], [600000, 0.0015], [1000000, 0.0014], [2000000, 0.0013], [4000000, 0.0012], [20000000, 0.0011], [50000000, 0.001], [100000000, 0.0007], [500000000, 0.0005], [2000000000, 0.0003], [6000000000, 0.0001], [20000000000, 5e-05], [20000000001, 0]]}, "marketId": "eth_usdt"}}