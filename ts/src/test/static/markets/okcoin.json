{"BTC/USD": {"id": "BTC-USD", "symbol": "BTC/USD", "base": "BTC", "quote": "USD", "baseId": "BTC", "quoteId": "USD", "active": true, "type": "spot", "spot": true, "swap": false, "future": false, "option": false, "margin": false, "contract": false, "precision": {"amount": 0.0001, "price": 0.01}, "limits": {"amount": {"min": 0.0001}, "price": {}, "cost": {"max": 1000000}, "leverage": {"min": 1, "max": 1}}, "info": {"alias": "", "baseCcy": "BTC", "category": "1", "ctMult": "", "ctType": "", "ctVal": "", "ctValCcy": "", "expTime": "", "instFamily": "", "instId": "BTC-USD", "instType": "SPOT", "lever": "", "listTime": "1671521075000", "lotSz": "0.0001", "maxIcebergSz": "99999999999999", "maxLmtSz": "99999999999999", "maxMktSz": "1000000", "maxStopSz": "1000000", "maxTriggerSz": "99999999999999", "maxTwapSz": "99999999999999", "minSz": "0.0001", "optType": "", "quoteCcy": "USD", "settleCcy": "", "state": "live", "stk": "", "tickSz": "0.01", "uly": ""}, "taker": 0.0015, "maker": 0.001, "created": 1671521075000}, "ETH/USD": {"id": "ETH-USD", "symbol": "ETH/USD", "base": "ETH", "quote": "USD", "baseId": "ETH", "quoteId": "USD", "active": true, "type": "spot", "spot": true, "swap": false, "future": false, "option": false, "margin": false, "contract": false, "precision": {"amount": 0.0001, "price": 0.01}, "limits": {"amount": {"min": 0.001}, "price": {}, "cost": {"max": 1000000}, "leverage": {"min": 1, "max": 1}}, "info": {"alias": "", "baseCcy": "ETH", "category": "1", "ctMult": "", "ctType": "", "ctVal": "", "ctValCcy": "", "expTime": "", "instFamily": "", "instId": "ETH-USD", "instType": "SPOT", "lever": "", "listTime": "1671521075000", "lotSz": "0.0001", "maxIcebergSz": "99999999999999", "maxLmtSz": "99999999999999", "maxMktSz": "1000000", "maxStopSz": "1000000", "maxTriggerSz": "99999999999999", "maxTwapSz": "99999999999999", "minSz": "0.001", "optType": "", "quoteCcy": "USD", "settleCcy": "", "state": "live", "stk": "", "tickSz": "0.01", "uly": ""}, "taker": 0.0015, "maker": 0.001, "created": 1671521075000}, "USDT/USD": {"id": "USDT-USD", "symbol": "USDT/USD", "base": "USDT", "quote": "USD", "baseId": "USDT", "quoteId": "USD", "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "active": true, "contract": false, "taker": 0.0015, "maker": 0.001, "precision": {"amount": 0.0001, "price": 0.0001}, "limits": {"leverage": {"min": 1, "max": 1}, "amount": {"min": 1}, "price": {}, "cost": {"max": 1000000}}, "created": 1671521075000, "info": {"alias": "", "baseCcy": "USDT", "category": "1", "ctMult": "", "ctType": "", "ctVal": "", "ctValCcy": "", "expTime": "", "instFamily": "", "instId": "USDT-USD", "instType": "SPOT", "lever": "", "listTime": "1671521075000", "lotSz": "0.0001", "maxIcebergSz": "99999999999999", "maxLmtSz": "99999999999999", "maxMktSz": "1000000", "maxStopSz": "1000000", "maxTriggerSz": "99999999999999", "maxTwapSz": "99999999999999", "minSz": "1", "optType": "", "quoteCcy": "USD", "settleCcy": "", "state": "live", "stk": "", "tickSz": "0.0001", "uly": ""}}}