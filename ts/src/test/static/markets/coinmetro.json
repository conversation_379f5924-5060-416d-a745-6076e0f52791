{"BTC/USDT": {"id": "BTCUSDT", "lowercaseId": null, "symbol": "BTC/USDT", "base": "BTC", "quote": "USDT", "settle": null, "baseId": "BTC", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": true, "swap": false, "future": false, "option": false, "index": false, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.001, "maker": 0, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1e-06, "price": 0.01}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 5e-05, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": 5, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"pair": "BTCUSDT", "precision": "2", "margin": true}, "tierBased": false, "percentage": true, "feeSide": "get"}, "ADA/USDT": {"id": "ADAUSDT", "lowercaseId": null, "symbol": "ADA/USDT", "base": "ADA", "quote": "USDT", "settle": null, "baseId": "ADA", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": true, "swap": false, "future": false, "option": false, "index": false, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.001, "maker": 0, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.01, "price": 1e-06}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 7, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": 5, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"pair": "ADAUSDT", "precision": "6", "margin": true}, "tierBased": false, "percentage": true, "feeSide": "get"}, "XRP/USDT": {"id": "XRPUSDT", "lowercaseId": null, "symbol": "XRP/USDT", "base": "XRP", "quote": "USDT", "settle": null, "baseId": "XRP", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": true, "swap": false, "future": false, "option": false, "index": false, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.001, "maker": 0, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.1, "price": 1e-06}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 2, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": 5, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"pair": "XRPUSDT", "precision": "6", "margin": true}, "tierBased": false, "percentage": true, "feeSide": "get"}, "SOL/USDT": {"id": "SOLUSDT", "lowercaseId": null, "symbol": "SOL/USDT", "base": "SOL", "quote": "USDT", "settle": null, "baseId": "SOL", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": true, "swap": false, "future": false, "option": false, "index": false, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.001, "maker": 0, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.0001, "price": 1e-05}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 0.03, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": 5, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"pair": "SOLUSDT", "precision": "5", "margin": true}, "tierBased": false, "percentage": true, "feeSide": "get"}, "DOGE/USDT": {"id": "DOGEUSDT", "lowercaseId": null, "symbol": "DOGE/USDT", "base": "DOGE", "quote": "USDT", "settle": null, "baseId": "DOGE", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": true, "swap": false, "future": false, "option": false, "index": false, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.001, "maker": 0, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.1, "price": 1e-05}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 20, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": 5, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"pair": "DOGEUSDT", "precision": "5", "margin": true}, "tierBased": false, "percentage": true, "feeSide": "get"}, "ETH/USDT": {"id": "ETHUSDT", "lowercaseId": null, "symbol": "ETH/USDT", "base": "ETH", "quote": "USDT", "settle": null, "baseId": "ETH", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": true, "swap": false, "future": false, "option": false, "index": false, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.001, "maker": 0, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.0001, "price": 0.0001}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 0.002, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": 5, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"pair": "ETHUSDT", "precision": "4", "margin": true}, "tierBased": false, "percentage": true, "feeSide": "get"}}