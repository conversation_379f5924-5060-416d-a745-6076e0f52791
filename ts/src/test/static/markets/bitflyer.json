{"BTC/JPY": {"id": "BTC_JPY", "lowercaseId": null, "symbol": "BTC/JPY", "base": "BTC", "quote": "JPY", "settle": null, "baseId": "BTC", "quoteId": "JPY", "settleId": null, "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.002, "maker": 0.002, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": null, "price": null}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": null, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"product_code": "BTC_JPY", "market_type": "Spot"}, "tierBased": null, "percentage": null}, "ETH/BTC": {"id": "ETH_BTC", "lowercaseId": null, "symbol": "ETH/BTC", "base": "ETH", "quote": "BTC", "settle": null, "baseId": "ETH", "quoteId": "BTC", "settleId": null, "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.002, "maker": 0.002, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": null, "price": null}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": null, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"product_code": "ETH_BTC", "market_type": "Spot"}, "tierBased": null, "percentage": null}, "BTC/EUR": {"id": "BTC_EUR", "lowercaseId": null, "symbol": "BTC/EUR", "base": "BTC", "quote": "EUR", "settle": null, "baseId": "BTC", "quoteId": "EUR", "settleId": null, "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.002, "maker": 0.002, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": null, "price": null}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": null, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"product_code": "BTC_EUR", "market_type": "Spot"}, "tierBased": null, "percentage": null}, "BTC/JPY:JPY": {"id": "FX_BTC_JPY", "lowercaseId": null, "symbol": "BTC/JPY:JPY", "base": "BTC", "quote": "JPY", "settle": "JPY", "baseId": "BTC", "quoteId": "JPY", "settleId": null, "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": 0, "maker": 0, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": null, "price": null}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": null, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"product_code": "FX_BTC_JPY", "market_type": "FX"}, "tierBased": null, "percentage": null}}