{"BTC/USDT": {"id": "BTC-USDT", "lowercaseId": null, "symbol": "BTC/USDT", "base": "BTC", "quote": "USDT", "settle": null, "baseId": "BTC", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "index": false, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.001, "maker": 0.001, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1e-06, "price": 0.01}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": null, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": 1, "max": 7588825.16352942}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"symbol": "BTC-USDT", "minQty": "0.0001826", "maxQty": "18.2663756", "minNotional": "1", "maxNotional": "7588825.16352942", "status": "1", "tickSize": "0.01", "stepSize": "0.000001", "apiStateSell": true, "apiStateBuy": true, "timeOnline": "0", "offTime": "0", "maintainTime": "0", "displayName": "BTC-USDT"}, "tierBased": null, "percentage": null, "feeSide": "get"}, "LTC/USDT": {"id": "LTC-USDT", "lowercaseId": null, "symbol": "LTC/USDT", "base": "LTC", "quote": "USDT", "settle": null, "baseId": "LTC", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "index": false, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.001, "maker": 0.001, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.0001, "price": 0.01}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": null, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": 1, "max": 915066.93248965}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"symbol": "LTC-USDT", "minQty": "0.0537", "maxQty": "5376.3441", "minNotional": "1", "maxNotional": "915066.93248965", "status": "1", "tickSize": "0.01", "stepSize": "0.0001", "apiStateSell": true, "apiStateBuy": true, "timeOnline": "0", "offTime": "0", "maintainTime": "0", "displayName": "LTC-USDT"}, "tierBased": null, "percentage": null, "feeSide": "get"}, "ADA/USDT": {"id": "ADA-USDT", "lowercaseId": null, "symbol": "ADA/USDT", "base": "ADA", "quote": "USDT", "settle": null, "baseId": "ADA", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "index": false, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.001, "maker": 0.001, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.01, "price": 0.0001}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": null, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": 1, "max": 934422.87842698}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"symbol": "ADA-USDT", "minQty": "13.27", "maxQty": "1327316.17", "minNotional": "1", "maxNotional": "934422.87842698", "status": "1", "tickSize": "0.0001", "stepSize": "0.01", "apiStateSell": true, "apiStateBuy": true, "timeOnline": "0", "offTime": "0", "maintainTime": "0", "displayName": "ADA-USDT"}, "tierBased": null, "percentage": null, "feeSide": "get"}, "XRP/USDT": {"id": "XRP-USDT", "lowercaseId": null, "symbol": "XRP/USDT", "base": "XRP", "quote": "USDT", "settle": null, "baseId": "XRP", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "index": false, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.001, "maker": 0.001, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.1, "price": 0.0001}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": null, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": 1, "max": 3294898.91661436}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"symbol": "XRP-USDT", "minQty": "11.21", "maxQty": "1121352.81", "minNotional": "1", "maxNotional": "3294898.91661436", "status": "1", "tickSize": "0.0001", "stepSize": "0.1", "apiStateSell": true, "apiStateBuy": true, "timeOnline": "0", "offTime": "0", "maintainTime": "0", "displayName": "XRP-USDT"}, "tierBased": null, "percentage": null, "feeSide": "get"}, "SOL/USDT": {"id": "SOL-USDT", "lowercaseId": null, "symbol": "SOL/USDT", "base": "SOL", "quote": "USDT", "settle": null, "baseId": "SOL", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "index": false, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.001, "maker": 0.001, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.001, "price": 0.01}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": null, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": 1, "max": 8380157.04160199}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"symbol": "SOL-USDT", "minQty": "0.2395", "maxQty": "23957.8343", "minNotional": "1", "maxNotional": "8380157.04160199", "status": "1", "tickSize": "0.01", "stepSize": "0.001", "apiStateSell": true, "apiStateBuy": true, "timeOnline": "0", "offTime": "0", "maintainTime": "0", "displayName": "SOL-USDT"}, "tierBased": null, "percentage": null, "feeSide": "get"}, "TRX/USDT": {"id": "TRX-USDT", "lowercaseId": null, "symbol": "TRX/USDT", "base": "TRX", "quote": "USDT", "settle": null, "baseId": "TRX", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "index": false, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.001, "maker": 0.001, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1, "price": 0.0001}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": null, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": 1, "max": 1092859.64355875}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"symbol": "TRX-USDT", "minQty": "70.9", "maxQty": "7096728.5", "minNotional": "1", "maxNotional": "1092859.64355875", "status": "1", "tickSize": "0.0001", "stepSize": "1", "apiStateSell": true, "apiStateBuy": true, "timeOnline": "0", "offTime": "0", "maintainTime": "0", "displayName": "TRX-USDT"}, "tierBased": null, "percentage": null, "feeSide": "get"}, "LTC/USDT:USDT": {"id": "LTC-USDT", "lowercaseId": null, "symbol": "LTC/USDT:USDT", "base": "LTC", "quote": "USDT", "settle": "USDT", "baseId": "LTC", "quoteId": "USDT", "settleId": "USDT", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": 0.0005, "maker": 0.0002, "contractSize": 1, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.1, "price": 0.01}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 0.1, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": 2, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"contractId": "100006", "symbol": "LTC-USDT", "size": "0.1", "quantityPrecision": "1", "pricePrecision": "2", "feeRate": "0.0005", "makerFeeRate": "0.0002", "takerFeeRate": "0.0005", "tradeMinLimit": "0", "tradeMinQuantity": "0.1", "tradeMinUSDT": "2", "currency": "USDT", "asset": "LTC", "status": "1", "apiStateOpen": "true", "apiStateClose": "true", "ensureTrigger": true, "triggerFeeRate": "0.00040000", "brokerState": false, "launchTime": "1622476800000", "maintainTime": "0", "offTime": "0", "displayName": "LTC-USDT"}, "tierBased": null, "percentage": null, "feeSide": "quote"}, "BTC/USDT:USDT": {"id": "BTC-USDT", "lowercaseId": null, "symbol": "BTC/USDT:USDT", "base": "BTC", "quote": "USDT", "settle": "USDT", "baseId": "BTC", "quoteId": "USDT", "settleId": "USDT", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": 0.0005, "maker": 0.0002, "contractSize": 1, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.0001, "price": 0.1}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 0.0001, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": 2, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"contractId": "100", "symbol": "BTC-USDT", "size": "0.0001", "quantityPrecision": "4", "pricePrecision": "1", "feeRate": "0.0005", "makerFeeRate": "0.0002", "takerFeeRate": "0.0005", "tradeMinLimit": "0", "tradeMinQuantity": "0.0001", "tradeMinUSDT": "2", "currency": "USDT", "asset": "BTC", "status": "1", "apiStateOpen": "true", "apiStateClose": "true", "ensureTrigger": true, "triggerFeeRate": "0.00025000", "brokerState": false, "launchTime": "1586275200000", "maintainTime": "0", "offTime": "0", "displayName": "BTC-USDT"}, "tierBased": null, "percentage": null, "feeSide": "quote"}, "ADA/USDT:USDT": {"id": "ADA-USDT", "lowercaseId": null, "symbol": "ADA/USDT:USDT", "base": "ADA", "quote": "USDT", "settle": "USDT", "baseId": "ADA", "quoteId": "USDT", "settleId": "USDT", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": 0.0005, "maker": 0.0002, "contractSize": 1, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1, "price": 1e-05}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 1, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": 2, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"contractId": "100004", "symbol": "ADA-USDT", "size": "1", "quantityPrecision": "0", "pricePrecision": "5", "feeRate": "0.0005", "makerFeeRate": "0.0002", "takerFeeRate": "0.0005", "tradeMinLimit": "0", "tradeMinQuantity": "1", "tradeMinUSDT": "2", "currency": "USDT", "asset": "ADA", "status": "1", "apiStateOpen": "true", "apiStateClose": "true", "ensureTrigger": true, "triggerFeeRate": "0.00500000", "brokerState": false, "launchTime": "1622476800000", "maintainTime": "0", "offTime": "0", "displayName": "ADA-USDT"}, "tierBased": null, "percentage": null, "feeSide": "quote"}, "ETH/USDT:USDT": {"id": "ETH-USDT", "lowercaseId": null, "symbol": "ETH/USDT:USDT", "base": "ETH", "quote": "USDT", "settle": "USDT", "baseId": "ETH", "quoteId": "USDT", "settleId": "USDT", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": 0.0005, "maker": 0.0002, "contractSize": 1, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.01, "price": 0.01}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 0.01, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": 2, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"contractId": "101", "symbol": "ETH-USDT", "size": "0.01", "quantityPrecision": "2", "pricePrecision": "2", "feeRate": "0.0005", "makerFeeRate": "0.0002", "takerFeeRate": "0.0005", "tradeMinLimit": "0", "tradeMinQuantity": "0.01", "tradeMinUSDT": "2", "currency": "USDT", "asset": "ETH", "status": "1", "apiStateOpen": "true", "apiStateClose": "true", "ensureTrigger": true, "triggerFeeRate": "0.00050000", "brokerState": false, "launchTime": "1586275200000", "maintainTime": "0", "offTime": "0", "displayName": "ETH-USDT"}, "tierBased": null, "percentage": null, "feeSide": "quote"}, "ETH/USDT": {"id": "ETH-USDT", "lowercaseId": null, "symbol": "ETH/USDT", "base": "ETH", "quote": "USDT", "settle": null, "baseId": "ETH", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "index": false, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.001, "maker": 0.001, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.0001, "price": 0.01}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": null, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": 1, "max": 8667861.18076081}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"symbol": "ETH-USDT", "minQty": "0.002736", "maxQty": "273.684672", "minNotional": "1", "maxNotional": "8667861.18076081", "status": "1", "tickSize": "0.01", "stepSize": "0.0001", "apiStateSell": true, "apiStateBuy": true, "timeOnline": "0", "offTime": "0", "maintainTime": "0", "displayName": "ETH-USDT"}, "tierBased": null, "percentage": null, "feeSide": "get"}, "SOL/USDT:USDT": {"id": "SOL-USDT", "lowercaseId": null, "symbol": "SOL/USDT:USDT", "base": "SOL", "quote": "USDT", "settle": "USDT", "baseId": "SOL", "quoteId": "USDT", "settleId": "USDT", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": 0.0005, "maker": 0.0002, "contractSize": 1, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1, "price": 0.001}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 1, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": 2, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"contractId": "100028", "symbol": "SOL-USDT", "size": "1", "quantityPrecision": "0", "pricePrecision": "3", "feeRate": "0.0005", "makerFeeRate": "0.0002", "takerFeeRate": "0.0005", "tradeMinLimit": "0", "tradeMinQuantity": "1", "tradeMinUSDT": "2", "currency": "USDT", "asset": "SOL", "status": "1", "apiStateOpen": "true", "apiStateClose": "true", "ensureTrigger": true, "triggerFeeRate": "0.00050000", "brokerState": false, "launchTime": "1635955200000", "maintainTime": "0", "offTime": "0", "displayName": "SOL-USDT"}, "tierBased": null, "percentage": null, "feeSide": "quote"}, "DOGE/USDT:USDT": {"id": "DOGE-USDT", "lowercaseId": null, "symbol": "DOGE/USDT:USDT", "base": "DOGE", "quote": "USDT", "settle": "USDT", "baseId": "DOGE", "quoteId": "USDT", "settleId": "USDT", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": 0.0005, "maker": 0.0002, "contractSize": 1, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1, "price": 1e-05}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 1, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": 2, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"contractId": "100035", "symbol": "DOGE-USDT", "size": "1", "quantityPrecision": "0", "pricePrecision": "5", "feeRate": "0.0005", "makerFeeRate": "0.0002", "takerFeeRate": "0.0005", "tradeMinLimit": "0", "tradeMinQuantity": "1", "tradeMinUSDT": "2", "currency": "USDT", "asset": "DOGE", "status": "1", "apiStateOpen": "true", "apiStateClose": "true", "ensureTrigger": false, "triggerFeeRate": "0.00030000", "brokerState": false, "launchTime": "1636387200000", "maintainTime": "0", "offTime": "0", "displayName": "DOGE-USDT"}, "tierBased": null, "percentage": null, "feeSide": "quote"}, "BTC/USD:BTC": {"id": "BTC-USD", "lowercaseId": null, "symbol": "BTC/USD:BTC", "base": "BTC", "quote": "USD", "settle": "BTC", "baseId": "BTC", "quoteId": "USD", "settleId": "BTC", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "index": null, "active": false, "contract": true, "linear": false, "inverse": true, "subType": "inverse", "taker": 0.0005, "maker": 0.0002, "contractSize": 1, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": null, "price": 0.1}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 1, "max": null}, "price": {"min": 100, "max": null}, "cost": {"min": 100, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": 1710738000000, "info": {"symbol": "BTC-USD", "pricePrecision": "1", "minTickSize": "100", "minTradeValue": "100", "minQty": "1.00000000", "status": "1", "timeOnline": "1710738000000", "displayName": "BTC-USD"}, "tierBased": null, "percentage": null, "feeSide": "quote"}, "SOL/USD:SOL": {"id": "SOL-USD", "lowercaseId": null, "symbol": "SOL/USD:SOL", "base": "SOL", "quote": "USD", "settle": "SOL", "baseId": "SOL", "quoteId": "USD", "settleId": "SOL", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "index": null, "active": false, "contract": true, "linear": false, "inverse": true, "subType": "inverse", "taker": 0.0005, "maker": 0.0002, "contractSize": 1, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": null, "price": 0.001}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 1, "max": null}, "price": {"min": 10, "max": null}, "cost": {"min": 10, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": 1713250800000, "info": {"symbol": "SOL-USD", "pricePrecision": "3", "minTickSize": "10", "minTradeValue": "10", "minQty": "1.00000000", "status": "1", "timeOnline": "1713250800000", "displayName": "SOL-USD"}, "tierBased": null, "percentage": null, "feeSide": "quote"}, "TRUMP/USDT": {"id": "TRUMPSOL-USDT", "lowercaseId": null, "symbol": "TRUMP/USDT", "base": "TRUMP", "quote": "USDT", "settle": null, "baseId": "TRUMPSOL", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "index": false, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.001, "maker": 0.001, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.01, "price": 0.01}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": null, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": 1, "max": 20000}}, "marginModes": {"cross": null, "isolated": null}, "created": 1737175800000, "info": {"symbol": "TRUMPSOL-USDT", "minQty": "0.241", "maxQty": "241", "minNotional": "1", "maxNotional": "20000", "status": "1", "tickSize": "0.01", "stepSize": "0.01", "apiStateSell": true, "apiStateBuy": true, "timeOnline": "1737175800000", "offTime": "0", "maintainTime": "0", "displayName": "TRUMPSOL-USDT"}, "tierBased": null, "percentage": null, "feeSide": "get"}}