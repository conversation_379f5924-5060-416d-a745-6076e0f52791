{"BTC/USDT": {"id": "BTC_USDT", "lowercaseId": null, "symbol": "BTC/USDT", "base": "BTC", "quote": "USDT", "settle": null, "baseId": "BTC", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.0009, "maker": 0.0009, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1e-06, "price": 0.01}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 1e-06, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": 1, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": 1659018819512, "info": {"symbol": "BTC_USDT", "baseCurrencyName": "BTC", "quoteCurrencyName": "USDT", "displayName": "BTC/USDT", "state": "NORMAL", "visibleStartTime": 1659018819512, "tradableStartTime": 1659018819512, "symbolTradeLimit": {"symbol": "BTC_USDT", "priceScale": 2, "quantityScale": 6, "amountScale": 2, "minQuantity": "0.000001", "minAmount": "1", "maxQuantity": "20", "maxAmount": "2000000", "highestBid": "0", "lowestAsk": "0"}, "crossMargin": {"supportCrossMargin": true, "maxLeverage": 3}}, "tierBased": null, "percentage": null, "feeSide": "get"}, "LTC/USDT": {"id": "LTC_USDT", "lowercaseId": null, "symbol": "LTC/USDT", "base": "LTC", "quote": "USDT", "settle": null, "baseId": "LTC", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.0009, "maker": 0.0009, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1e-06, "price": 0.001}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 1e-06, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": 1, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": 1659018819655, "info": {"symbol": "LTC_USDT", "baseCurrencyName": "LTC", "quoteCurrencyName": "USDT", "displayName": "LTC/USDT", "state": "NORMAL", "visibleStartTime": 1659018819655, "tradableStartTime": 1659018819655, "symbolTradeLimit": {"symbol": "LTC_USDT", "priceScale": 3, "quantityScale": 6, "amountScale": 2, "minQuantity": "0.000001", "minAmount": "1", "maxQuantity": "700", "maxAmount": "100000", "highestBid": "0", "lowestAsk": "0"}, "crossMargin": {"supportCrossMargin": true, "maxLeverage": 3}}, "tierBased": null, "percentage": null, "feeSide": "get"}, "ADA/USDT": {"id": "ADA_USDT", "lowercaseId": null, "symbol": "ADA/USDT", "base": "ADA", "quote": "USDT", "settle": null, "baseId": "ADA", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.0009, "maker": 0.0009, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.0001, "price": 1e-05}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 0.0001, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": 1, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": 1659018850462, "info": {"symbol": "ADA_USDT", "baseCurrencyName": "ADA", "quoteCurrencyName": "USDT", "displayName": "ADA/USDT", "state": "NORMAL", "visibleStartTime": 1659018850462, "tradableStartTime": 1659018850462, "symbolTradeLimit": {"symbol": "ADA_USDT", "priceScale": 5, "quantityScale": 4, "amountScale": 4, "minQuantity": "0.0001", "minAmount": "1", "maxQuantity": "165000", "maxAmount": "200000", "highestBid": "0", "lowestAsk": "0"}, "crossMargin": {"supportCrossMargin": false, "maxLeverage": 1}}, "tierBased": null, "percentage": null, "feeSide": "get"}, "XRP/USDT": {"id": "XRP_USDT", "lowercaseId": null, "symbol": "XRP/USDT", "base": "XRP", "quote": "USDT", "settle": null, "baseId": "XRP", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.0009, "maker": 0.0009, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.0001, "price": 0.0001}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 0.0001, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": 1, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": 1659018819871, "info": {"symbol": "XRP_USDT", "baseCurrencyName": "XRP", "quoteCurrencyName": "USDT", "displayName": "XRP/USDT", "state": "NORMAL", "visibleStartTime": 1659018819871, "tradableStartTime": 1659018819871, "symbolTradeLimit": {"symbol": "XRP_USDT", "priceScale": 4, "quantityScale": 4, "amountScale": 4, "minQuantity": "0.0001", "minAmount": "1", "maxQuantity": "126000", "maxAmount": "300000", "highestBid": "0", "lowestAsk": "0"}, "crossMargin": {"supportCrossMargin": true, "maxLeverage": 3}}, "tierBased": null, "percentage": null, "feeSide": "get"}, "SOL/USDT": {"id": "SOL_USDT", "lowercaseId": null, "symbol": "SOL/USDT", "base": "SOL", "quote": "USDT", "settle": null, "baseId": "SOL", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.0009, "maker": 0.0009, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1e-06, "price": 0.001}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 1e-06, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": 1, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": 1659018848969, "info": {"symbol": "SOL_USDT", "baseCurrencyName": "SOL", "quoteCurrencyName": "USDT", "displayName": "SOL/USDT", "state": "NORMAL", "visibleStartTime": 1659018848969, "tradableStartTime": 1659018848969, "symbolTradeLimit": {"symbol": "SOL_USDT", "priceScale": 3, "quantityScale": 6, "amountScale": 2, "minQuantity": "0.000001", "minAmount": "1", "maxQuantity": "1200", "maxAmount": "300000", "highestBid": "0", "lowestAsk": "0"}, "crossMargin": {"supportCrossMargin": true, "maxLeverage": 3}}, "tierBased": null, "percentage": null, "feeSide": "get"}, "TRX/USDT": {"id": "TRX_USDT", "lowercaseId": null, "symbol": "TRX/USDT", "base": "TRX", "quote": "USDT", "settle": null, "baseId": "TRX", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.0009, "maker": 0.0009, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.001, "price": 1e-05}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 0.001, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": 1, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": 1659018823985, "info": {"symbol": "TRX_USDT", "baseCurrencyName": "TRX", "quoteCurrencyName": "USDT", "displayName": "TRX/USDT", "state": "NORMAL", "visibleStartTime": 1659018823984, "tradableStartTime": 1659018823985, "symbolTradeLimit": {"symbol": "TRX_USDT", "priceScale": 5, "quantityScale": 3, "amountScale": 5, "minQuantity": "0.001", "minAmount": "1", "maxQuantity": "300000", "maxAmount": "100000", "highestBid": "0", "lowestAsk": "0"}, "crossMargin": {"supportCrossMargin": true, "maxLeverage": 3}}, "tierBased": null, "percentage": null, "feeSide": "get"}, "ETH/USDT": {"id": "ETH_USDT", "lowercaseId": null, "symbol": "ETH/USDT", "base": "ETH", "quote": "USDT", "settle": null, "baseId": "ETH", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.0009, "maker": 0.0009, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1e-06, "price": 0.01}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 1e-06, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": 1, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": 1659018820007, "info": {"symbol": "ETH_USDT", "baseCurrencyName": "ETH", "quoteCurrencyName": "USDT", "displayName": "ETH/USDT", "state": "NORMAL", "visibleStartTime": 1659018820007, "tradableStartTime": 1659018820007, "symbolTradeLimit": {"symbol": "ETH_USDT", "priceScale": 2, "quantityScale": 6, "amountScale": 2, "minQuantity": "0.000001", "minAmount": "1", "maxQuantity": "510", "maxAmount": "2000000", "highestBid": "0", "lowestAsk": "0"}, "crossMargin": {"supportCrossMargin": true, "maxLeverage": 3}}, "tierBased": null, "percentage": null, "feeSide": "get"}, "BTC/USDT:USDT": {"id": "BTC_USDT_PERP", "lowercaseId": null, "symbol": "BTC/USDT:USDT", "base": "BTC", "quote": "USDT", "settle": "USDT", "baseId": "BTC", "quoteId": "USDT", "settleId": "USDT", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": null, "maker": null, "contractSize": 0.001, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1, "price": 0.01}, "limits": {"leverage": {"min": null, "max": 75}, "amount": {"min": 1, "max": 1000000}, "price": {"min": 0.01, "max": 1000000}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": 1547435912000, "info": {"symbol": "BTC_USDT_PERP", "bAsset": ".PXBTUSDT", "bCcy": "BTC", "qCcy": "USDT", "visibleStartTime": "1584721775000", "tradableStartTime": "1584721775000", "sCcy": "USDT", "tSz": "0.01", "pxScale": "0.01,0.1,1,10,100", "lotSz": "1", "minSz": "1", "ctVal": "0.001", "status": "OPEN", "oDate": "1547435912000", "maxPx": "1000000", "minPx": "0.01", "maxQty": "1000000", "minQty": "1", "maxLever": "75", "lever": "20", "ctType": "LINEAR", "alias": "", "iM": "0.0133", "mM": "0.006", "mR": "5000", "ordPxRange": "0.05", "marketMaxQty": "10000", "limitMaxQty": "1000000"}, "tierBased": null, "percentage": null, "feeSide": "get"}, "ETH/USDT:USDT": {"id": "ETH_USDT_PERP", "lowercaseId": null, "symbol": "ETH/USDT:USDT", "base": "ETH", "quote": "USDT", "settle": "USDT", "baseId": "ETH", "quoteId": "USDT", "settleId": "USDT", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": null, "maker": null, "contractSize": 0.01, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1, "price": 0.01}, "limits": {"leverage": {"min": null, "max": 75}, "amount": {"min": 1, "max": 1000000}, "price": {"min": 0.01, "max": 1000000}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": 1598599725000, "info": {"symbol": "ETH_USDT_PERP", "bAsset": ".PETHUSDT", "bCcy": "ETH", "qCcy": "USDT", "visibleStartTime": "1599040800000", "tradableStartTime": "1599040800000", "sCcy": "USDT", "tSz": "0.01", "pxScale": "0.01,0.1,1,10,100", "lotSz": "1", "minSz": "1", "ctVal": "0.01", "status": "OPEN", "oDate": "1598599725000", "maxPx": "1000000", "minPx": "0.01", "maxQty": "1000000", "minQty": "1", "maxLever": "75", "lever": "20", "ctType": "LINEAR", "alias": "", "iM": "0.0133", "mM": "0.006", "mR": "5000", "ordPxRange": "0.05", "marketMaxQty": "24000", "limitMaxQty": "1000000"}, "tierBased": null, "percentage": null, "feeSide": "get"}}