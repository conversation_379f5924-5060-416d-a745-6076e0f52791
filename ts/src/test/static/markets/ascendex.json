{"BTC/USDT": {"id": "BTC/USDT", "lowercaseId": null, "symbol": "BTC/USDT", "base": "BTC", "quote": "USDT", "settle": null, "baseId": "BTC", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": true, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.001, "maker": 0.001, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1e-05, "price": 0.01}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 1e-09, "max": 1000000000}, "price": {"min": 0.01, "max": null}, "cost": {"min": 5, "max": 1000000}}, "marginModes": {"cross": null, "isolated": null}, "created": 1546329600000, "info": {"symbol": "BTC/USDT", "displayName": "BTC/USDT", "baseAsset": "BTC", "quoteAsset": "USDT", "status": "Normal", "minNotional": "5", "maxNotional": "1000000", "marginTradable": true, "commissionType": "Quote", "commissionReserveRate": "0.001", "tickSize": "0.01", "lotSize": "0.00001", "domain": "USDS", "tradingStartTime": "1546329600000", "collapseDecimals": "1,0.1,0.01", "minQty": "0.000000001", "maxQty": "1000000000", "statusCode": "Normal", "statusMessage": "", "useTick": false, "useLot": false, "qtyScale": "5", "priceScale": "2", "notionalScale": "4"}, "tierBased": true, "percentage": true, "feeSide": "get"}, "BTC/USDT:USDT": {"id": "BTC-PERP", "lowercaseId": null, "symbol": "BTC/USDT:USDT", "base": "BTC", "quote": "USDT", "settle": "USDT", "baseId": "BTC", "quoteId": "USDT", "settleId": "USDT", "type": "swap", "spot": false, "margin": null, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": 0.001, "maker": 0.001, "contractSize": 1, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.0001, "price": 0.1}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 0.0001, "max": 100000000000}, "price": {"min": 0.1, "max": 1000000}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": 1688432400000, "info": {"symbol": "BTC-PERP", "status": "Normal", "displayName": "BTCUSDT", "settlementAsset": "USDT", "underlying": "BTC/USDT", "tradingStartTime": "1688432400000", "priceFilter": {"minPrice": "0.1", "maxPrice": "1000000", "tickSize": "0.1"}, "lotSizeFilter": {"minQty": "0.0001", "maxQty": "100000000000", "lotSize": "0.0001"}, "commissionType": "Quote", "commissionReserveRate": "0.001", "marketOrderPriceMarkup": "0.002", "marginRequirements": [{"positionNotionalLowerBound": "0", "positionNotionalUpperBound": "300000", "initialMarginRate": "0.005", "maintenanceMarginRate": "0.004"}, {"positionNotionalLowerBound": "300000", "positionNotionalUpperBound": "1000000", "initialMarginRate": "0.008", "maintenanceMarginRate": "0.0055"}, {"positionNotionalLowerBound": "1000000", "positionNotionalUpperBound": "2000000", "initialMarginRate": "0.01", "maintenanceMarginRate": "0.0065"}, {"positionNotionalLowerBound": "2000000", "positionNotionalUpperBound": "2500000", "initialMarginRate": "0.013333", "maintenanceMarginRate": "0.008"}, {"positionNotionalLowerBound": "2500000", "positionNotionalUpperBound": "3000000", "initialMarginRate": "0.02", "maintenanceMarginRate": "0.014"}, {"positionNotionalLowerBound": "3000000", "positionNotionalUpperBound": "3500000", "initialMarginRate": "0.028571", "maintenanceMarginRate": "0.0195"}, {"positionNotionalLowerBound": "3500000", "positionNotionalUpperBound": "4000000", "initialMarginRate": "0.033333", "maintenanceMarginRate": "0.023"}, {"positionNotionalLowerBound": "4000000", "positionNotionalUpperBound": "4500000", "initialMarginRate": "0.05", "maintenanceMarginRate": "0.035"}, {"positionNotionalLowerBound": "4500000", "positionNotionalUpperBound": "5000000", "initialMarginRate": "0.1", "maintenanceMarginRate": "0.07"}, {"positionNotionalLowerBound": "5000000", "positionNotionalUpperBound": "10000000", "initialMarginRate": "0.2", "maintenanceMarginRate": "0.14"}, {"positionNotionalLowerBound": "10000000", "positionNotionalUpperBound": "1000000000", "initialMarginRate": "0.333333", "maintenanceMarginRate": "0.2"}]}, "tierBased": true, "percentage": true, "feeSide": "get"}, "LTC/USDT": {"id": "LTC/USDT", "lowercaseId": null, "symbol": "LTC/USDT", "base": "LTC", "quote": "USDT", "settle": null, "baseId": "LTC", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": true, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.001, "maker": 0.001, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.001, "price": 0.01}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 1e-09, "max": 1000000000}, "price": {"min": 0.01, "max": null}, "cost": {"min": 5, "max": 1000000}}, "marginModes": {"cross": null, "isolated": null}, "created": 1546329600000, "info": {"symbol": "LTC/USDT", "displayName": "LTC/USDT", "baseAsset": "LTC", "quoteAsset": "USDT", "status": "Normal", "minNotional": "5", "maxNotional": "1000000", "marginTradable": true, "commissionType": "Quote", "commissionReserveRate": "0.001", "tickSize": "0.01", "lotSize": "0.001", "domain": "USDS", "tradingStartTime": "1546329600000", "collapseDecimals": "1,0.1,0.01", "minQty": "0.000000001", "maxQty": "1000000000", "statusCode": "Normal", "statusMessage": "", "useTick": false, "useLot": false, "qtyScale": "3", "priceScale": "2", "notionalScale": "4"}, "tierBased": true, "percentage": true, "feeSide": "get"}, "ADA/USDT": {"id": "ADA/USDT", "lowercaseId": null, "symbol": "ADA/USDT", "base": "ADA", "quote": "USDT", "settle": null, "baseId": "ADA", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": true, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.001, "maker": 0.001, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1, "price": 1e-05}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 1e-09, "max": 1000000000}, "price": {"min": 1e-05, "max": null}, "cost": {"min": 5, "max": 1000000}}, "marginModes": {"cross": null, "isolated": null}, "created": 1559685600000, "info": {"symbol": "ADA/USDT", "displayName": "ADA/USDT", "baseAsset": "ADA", "quoteAsset": "USDT", "status": "Normal", "minNotional": "5", "maxNotional": "1000000", "marginTradable": true, "commissionType": "Quote", "commissionReserveRate": "0.001", "tickSize": "0.00001", "lotSize": "1", "domain": "USDS", "tradingStartTime": "1559685600000", "collapseDecimals": "0.01,0.0001,0.00001", "minQty": "0.000000001", "maxQty": "1000000000", "statusCode": "Normal", "statusMessage": "", "useTick": false, "useLot": false, "qtyScale": "0", "priceScale": "5", "notionalScale": "4"}, "tierBased": true, "percentage": true, "feeSide": "get"}, "XRP/USDT": {"id": "XRP/USDT", "lowercaseId": null, "symbol": "XRP/USDT", "base": "XRP", "quote": "USDT", "settle": null, "baseId": "XRP", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": true, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.001, "maker": 0.001, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1, "price": 1e-05}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 1e-09, "max": 1000000000}, "price": {"min": 1e-05, "max": null}, "cost": {"min": 5, "max": 1000000}}, "marginModes": {"cross": null, "isolated": null}, "created": 1546329600000, "info": {"symbol": "XRP/USDT", "displayName": "XRP/USDT", "baseAsset": "XRP", "quoteAsset": "USDT", "status": "Normal", "minNotional": "5", "maxNotional": "1000000", "marginTradable": true, "commissionType": "Quote", "commissionReserveRate": "0.001", "tickSize": "0.00001", "lotSize": "1", "domain": "USDS", "tradingStartTime": "1546329600000", "collapseDecimals": "0.01,0.0001,0.00001", "minQty": "0.000000001", "maxQty": "1000000000", "statusCode": "Normal", "statusMessage": "", "useTick": false, "useLot": false, "qtyScale": "0", "priceScale": "5", "notionalScale": "4"}, "tierBased": true, "percentage": true, "feeSide": "get"}, "LTC/USDT:USDT": {"id": "LTC-PERP", "lowercaseId": null, "symbol": "LTC/USDT:USDT", "base": "LTC", "quote": "USDT", "settle": "USDT", "baseId": "LTC", "quoteId": "USDT", "settleId": "USDT", "type": "swap", "spot": false, "margin": null, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": 0.001, "maker": 0.001, "contractSize": 1, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.1, "price": 0.01}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 0.1, "max": 100000000000000}, "price": {"min": 0.01, "max": 1000000}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": 1688437800000, "info": {"symbol": "LTC-PERP", "status": "Normal", "displayName": "LTCUSDT", "settlementAsset": "USDT", "underlying": "LTC/USDT", "tradingStartTime": "1688437800000", "priceFilter": {"minPrice": "0.01", "maxPrice": "1000000", "tickSize": "0.01"}, "lotSizeFilter": {"minQty": "0.1", "maxQty": "100000000000000", "lotSize": "0.1"}, "commissionType": "Quote", "commissionReserveRate": "0.001", "marketOrderPriceMarkup": "0.1", "marginRequirements": [{"positionNotionalLowerBound": "0", "positionNotionalUpperBound": "8000", "initialMarginRate": "0.01", "maintenanceMarginRate": "0.0065"}, {"positionNotionalLowerBound": "8000", "positionNotionalUpperBound": "15000", "initialMarginRate": "0.013333", "maintenanceMarginRate": "0.008"}, {"positionNotionalLowerBound": "15000", "positionNotionalUpperBound": "30000", "initialMarginRate": "0.02", "maintenanceMarginRate": "0.014"}, {"positionNotionalLowerBound": "30000", "positionNotionalUpperBound": "80000", "initialMarginRate": "0.033333", "maintenanceMarginRate": "0.023"}, {"positionNotionalLowerBound": "80000", "positionNotionalUpperBound": "150000", "initialMarginRate": "0.05", "maintenanceMarginRate": "0.035"}, {"positionNotionalLowerBound": "150000", "positionNotionalUpperBound": "250000", "initialMarginRate": "0.1", "maintenanceMarginRate": "0.07"}, {"positionNotionalLowerBound": "250000", "positionNotionalUpperBound": "500000", "initialMarginRate": "0.2", "maintenanceMarginRate": "0.11"}, {"positionNotionalLowerBound": "500000", "positionNotionalUpperBound": "1000000000", "initialMarginRate": "1", "maintenanceMarginRate": "0.55"}]}, "tierBased": true, "percentage": true, "feeSide": "get"}, "ADA/USDT:USDT": {"id": "ADA-PERP", "lowercaseId": null, "symbol": "ADA/USDT:USDT", "base": "ADA", "quote": "USDT", "settle": "USDT", "baseId": "ADA", "quoteId": "USDT", "settleId": "USDT", "type": "swap", "spot": false, "margin": null, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": 0.001, "maker": 0.001, "contractSize": 1, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1, "price": 0.0001}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 1, "max": 1000000000000000}, "price": {"min": 0.0001, "max": 1000000}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": 1688437800000, "info": {"symbol": "ADA-PERP", "status": "Normal", "displayName": "ADAUSDT", "settlementAsset": "USDT", "underlying": "ADA/USDT", "tradingStartTime": "1688437800000", "priceFilter": {"minPrice": "0.0001", "maxPrice": "1000000", "tickSize": "0.0001"}, "lotSizeFilter": {"minQty": "1", "maxQty": "1000000000000000", "lotSize": "1"}, "commissionType": "Quote", "commissionReserveRate": "0.001", "marketOrderPriceMarkup": "0.1", "marginRequirements": [{"positionNotionalLowerBound": "0", "positionNotionalUpperBound": "8000", "initialMarginRate": "0.01", "maintenanceMarginRate": "0.0065"}, {"positionNotionalLowerBound": "8000", "positionNotionalUpperBound": "15000", "initialMarginRate": "0.013333", "maintenanceMarginRate": "0.008"}, {"positionNotionalLowerBound": "15000", "positionNotionalUpperBound": "30000", "initialMarginRate": "0.02", "maintenanceMarginRate": "0.014"}, {"positionNotionalLowerBound": "30000", "positionNotionalUpperBound": "80000", "initialMarginRate": "0.033333", "maintenanceMarginRate": "0.023"}, {"positionNotionalLowerBound": "80000", "positionNotionalUpperBound": "150000", "initialMarginRate": "0.05", "maintenanceMarginRate": "0.035"}, {"positionNotionalLowerBound": "150000", "positionNotionalUpperBound": "250000", "initialMarginRate": "0.1", "maintenanceMarginRate": "0.07"}, {"positionNotionalLowerBound": "250000", "positionNotionalUpperBound": "500000", "initialMarginRate": "0.2", "maintenanceMarginRate": "0.11"}, {"positionNotionalLowerBound": "500000", "positionNotionalUpperBound": "1000000000", "initialMarginRate": "1", "maintenanceMarginRate": "0.55"}]}, "tierBased": true, "percentage": true, "feeSide": "get"}, "XRP/USDT:USDT": {"id": "XRP-PERP", "lowercaseId": null, "symbol": "XRP/USDT:USDT", "base": "XRP", "quote": "USDT", "settle": "USDT", "baseId": "XRP", "quoteId": "USDT", "settleId": "USDT", "type": "swap", "spot": false, "margin": null, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": 0.001, "maker": 0.001, "contractSize": 1, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1, "price": 0.0001}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 1, "max": 1000000000000000}, "price": {"min": 0.0001, "max": 1000000}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": 1688437800000, "info": {"symbol": "XRP-PERP", "status": "Normal", "displayName": "XRPUSDT", "settlementAsset": "USDT", "underlying": "XRP/USDT", "tradingStartTime": "1688437800000", "priceFilter": {"minPrice": "0.0001", "maxPrice": "1000000", "tickSize": "0.0001"}, "lotSizeFilter": {"minQty": "1", "maxQty": "1000000000000000", "lotSize": "1"}, "commissionType": "Quote", "commissionReserveRate": "0.001", "marketOrderPriceMarkup": "0.1", "marginRequirements": [{"positionNotionalLowerBound": "0", "positionNotionalUpperBound": "8000", "initialMarginRate": "0.01", "maintenanceMarginRate": "0.0065"}, {"positionNotionalLowerBound": "8000", "positionNotionalUpperBound": "15000", "initialMarginRate": "0.013333", "maintenanceMarginRate": "0.008"}, {"positionNotionalLowerBound": "15000", "positionNotionalUpperBound": "30000", "initialMarginRate": "0.02", "maintenanceMarginRate": "0.014"}, {"positionNotionalLowerBound": "30000", "positionNotionalUpperBound": "80000", "initialMarginRate": "0.033333", "maintenanceMarginRate": "0.023"}, {"positionNotionalLowerBound": "80000", "positionNotionalUpperBound": "150000", "initialMarginRate": "0.05", "maintenanceMarginRate": "0.035"}, {"positionNotionalLowerBound": "150000", "positionNotionalUpperBound": "250000", "initialMarginRate": "0.1", "maintenanceMarginRate": "0.07"}, {"positionNotionalLowerBound": "250000", "positionNotionalUpperBound": "500000", "initialMarginRate": "0.2", "maintenanceMarginRate": "0.11"}, {"positionNotionalLowerBound": "500000", "positionNotionalUpperBound": "1000000000", "initialMarginRate": "1", "maintenanceMarginRate": "0.55"}]}, "tierBased": true, "percentage": true, "feeSide": "get"}, "TRX/USDT": {"id": "TRX/USDT", "lowercaseId": null, "symbol": "TRX/USDT", "base": "TRX", "quote": "USDT", "settle": null, "baseId": "TRX", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": true, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.001, "maker": 0.001, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1, "price": 1e-05}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 1e-09, "max": 1000000000}, "price": {"min": 1e-05, "max": null}, "cost": {"min": 5, "max": 1000000}}, "marginModes": {"cross": null, "isolated": null}, "created": 1546329600000, "info": {"symbol": "TRX/USDT", "displayName": "TRX/USDT", "baseAsset": "TRX", "quoteAsset": "USDT", "status": "Normal", "minNotional": "5", "maxNotional": "1000000", "marginTradable": true, "commissionType": "Quote", "commissionReserveRate": "0.001", "tickSize": "0.00001", "lotSize": "1", "domain": "USDS", "tradingStartTime": "1546329600000", "collapseDecimals": "0.01,0.0001,0.00001", "minQty": "0.000000001", "maxQty": "1000000000", "statusCode": "Normal", "statusMessage": "", "useTick": false, "useLot": false, "qtyScale": "0", "priceScale": "5", "notionalScale": "4"}, "tierBased": true, "percentage": true, "feeSide": "get"}, "TRX/USDT:USDT": {"id": "TRX-PERP", "lowercaseId": null, "symbol": "TRX/USDT:USDT", "base": "TRX", "quote": "USDT", "settle": "USDT", "baseId": "TRX", "quoteId": "USDT", "settleId": "USDT", "type": "swap", "spot": false, "margin": null, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": 0.001, "maker": 0.001, "contractSize": 1, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1, "price": 1e-05}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 1, "max": 1000000000000000}, "price": {"min": 1e-05, "max": 1000000}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": 1688437800000, "info": {"symbol": "TRX-PERP", "status": "Normal", "displayName": "TRXUSDT", "settlementAsset": "USDT", "underlying": "TRX/USDT", "tradingStartTime": "1688437800000", "priceFilter": {"minPrice": "0.00001", "maxPrice": "1000000", "tickSize": "0.00001"}, "lotSizeFilter": {"minQty": "1", "maxQty": "1000000000000000", "lotSize": "1"}, "commissionType": "Quote", "commissionReserveRate": "0.001", "marketOrderPriceMarkup": "0.1", "marginRequirements": [{"positionNotionalLowerBound": "0", "positionNotionalUpperBound": "10000", "initialMarginRate": "0.05", "maintenanceMarginRate": "0.03"}, {"positionNotionalLowerBound": "10000", "positionNotionalUpperBound": "30000", "initialMarginRate": "0.1", "maintenanceMarginRate": "0.06"}, {"positionNotionalLowerBound": "30000", "positionNotionalUpperBound": "50000", "initialMarginRate": "0.2", "maintenanceMarginRate": "0.12"}, {"positionNotionalLowerBound": "50000", "positionNotionalUpperBound": "100000", "initialMarginRate": "0.5", "maintenanceMarginRate": "0.325"}, {"positionNotionalLowerBound": "100000", "positionNotionalUpperBound": "1000000000", "initialMarginRate": "1", "maintenanceMarginRate": "0.55"}]}, "tierBased": true, "percentage": true, "feeSide": "get"}, "ETH/USDT": {"id": "ETH/USDT", "lowercaseId": null, "symbol": "ETH/USDT", "base": "ETH", "quote": "USDT", "settle": null, "baseId": "ETH", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": true, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.001, "maker": 0.001, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.001, "price": 0.01}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 1e-09, "max": 1000000000}, "price": {"min": 0.01, "max": null}, "cost": {"min": 5, "max": 1000000}}, "marginModes": {"cross": null, "isolated": null}, "created": 1546329600000, "info": {"symbol": "ETH/USDT", "displayName": "ETH/USDT", "baseAsset": "ETH", "quoteAsset": "USDT", "status": "Normal", "minNotional": "5", "maxNotional": "1000000", "marginTradable": true, "commissionType": "Quote", "commissionReserveRate": "0.001", "tickSize": "0.01", "lotSize": "0.001", "domain": "USDS", "tradingStartTime": "1546329600000", "collapseDecimals": "1,0.1,0.01", "minQty": "0.000000001", "maxQty": "1000000000", "statusCode": "Normal", "statusMessage": "", "useTick": false, "useLot": false, "qtyScale": "3", "priceScale": "2", "notionalScale": "4"}, "tierBased": true, "percentage": true, "feeSide": "get"}, "ETH/USDT:USDT": {"id": "ETH-PERP", "lowercaseId": null, "symbol": "ETH/USDT:USDT", "base": "ETH", "quote": "USDT", "settle": "USDT", "baseId": "ETH", "quoteId": "USDT", "settleId": "USDT", "type": "swap", "spot": false, "margin": null, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": 0.001, "maker": 0.001, "contractSize": 1, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.01, "price": 0.01}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 0.01, "max": 10000000000000}, "price": {"min": 0.01, "max": 1000000}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": 1688432400000, "info": {"symbol": "ETH-PERP", "status": "Normal", "displayName": "ETHUSDT", "settlementAsset": "USDT", "underlying": "ETH/USDT", "tradingStartTime": "1688432400000", "priceFilter": {"minPrice": "0.01", "maxPrice": "1000000", "tickSize": "0.01"}, "lotSizeFilter": {"minQty": "0.01", "maxQty": "10000000000000", "lotSize": "0.01"}, "commissionType": "Quote", "commissionReserveRate": "0.001", "marketOrderPriceMarkup": "0.002", "marginRequirements": [{"positionNotionalLowerBound": "0", "positionNotionalUpperBound": "300000", "initialMarginRate": "0.005", "maintenanceMarginRate": "0.004"}, {"positionNotionalLowerBound": "300000", "positionNotionalUpperBound": "1000000", "initialMarginRate": "0.008", "maintenanceMarginRate": "0.0055"}, {"positionNotionalLowerBound": "1000000", "positionNotionalUpperBound": "2000000", "initialMarginRate": "0.01", "maintenanceMarginRate": "0.0065"}, {"positionNotionalLowerBound": "2000000", "positionNotionalUpperBound": "2500000", "initialMarginRate": "0.013333", "maintenanceMarginRate": "0.008"}, {"positionNotionalLowerBound": "2500000", "positionNotionalUpperBound": "3000000", "initialMarginRate": "0.02", "maintenanceMarginRate": "0.014"}, {"positionNotionalLowerBound": "3000000", "positionNotionalUpperBound": "3500000", "initialMarginRate": "0.028571", "maintenanceMarginRate": "0.0195"}, {"positionNotionalLowerBound": "3500000", "positionNotionalUpperBound": "4000000", "initialMarginRate": "0.033333", "maintenanceMarginRate": "0.023"}, {"positionNotionalLowerBound": "4000000", "positionNotionalUpperBound": "4500000", "initialMarginRate": "0.05", "maintenanceMarginRate": "0.035"}, {"positionNotionalLowerBound": "4500000", "positionNotionalUpperBound": "5000000", "initialMarginRate": "0.1", "maintenanceMarginRate": "0.07"}, {"positionNotionalLowerBound": "5000000", "positionNotionalUpperBound": "10000000", "initialMarginRate": "0.2", "maintenanceMarginRate": "0.14"}, {"positionNotionalLowerBound": "10000000", "positionNotionalUpperBound": "1000000000", "initialMarginRate": "0.333333", "maintenanceMarginRate": "0.2"}]}, "tierBased": true, "percentage": true, "feeSide": "get"}, "BTC/USD": {"id": "BTC/USD", "lowercaseId": null, "symbol": "BTC/USD", "base": "BTC", "quote": "USD", "settle": null, "baseId": "BTC", "quoteId": "USD", "settleId": null, "type": "spot", "spot": true, "margin": true, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.001, "maker": 0.001, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.0001, "price": 1}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 1e-09, "max": 1000000000}, "price": {"min": 1, "max": null}, "cost": {"min": 5, "max": 1000000}}, "marginModes": {"cross": null, "isolated": null}, "created": 1653663600000, "info": {"symbol": "BTC/USD", "displayName": "BTC/USD", "baseAsset": "BTC", "quoteAsset": "USD", "status": "Normal", "minNotional": "5", "maxNotional": "1000000", "marginTradable": true, "commissionType": "Quote", "commissionReserveRate": "0.001", "tickSize": "1", "lotSize": "0.0001", "domain": "USD", "tradingStartTime": "1653663600000", "collapseDecimals": "1", "minQty": "0.000000001", "maxQty": "1000000000", "statusCode": "Normal", "statusMessage": "", "useTick": false, "useLot": false, "qtyScale": "4", "priceScale": "0", "notionalScale": "2"}, "tierBased": true, "percentage": true, "feeSide": "get"}, "LINK/USDT:USDT": {"id": "LINK-PERP", "lowercaseId": null, "symbol": "LINK/USDT:USDT", "base": "LINK", "quote": "USDT", "settle": "USDT", "baseId": "LINK", "quoteId": "USDT", "settleId": "USDT", "type": "swap", "spot": false, "margin": null, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": 0.001, "maker": 0.001, "contractSize": 1, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.1, "price": 0.001}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 0.1, "max": 100000000000000}, "price": {"min": 0.001, "max": 1000000}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": 1688437800000, "info": {"symbol": "LINK-PERP", "status": "Normal", "displayName": "LINKUSDT", "settlementAsset": "USDT", "underlying": "LINK/USDT", "tradingStartTime": "1688437800000", "priceFilter": {"minPrice": "0.001", "maxPrice": "1000000", "tickSize": "0.001"}, "lotSizeFilter": {"minQty": "0.1", "maxQty": "100000000000000", "lotSize": "0.1"}, "commissionType": "Quote", "commissionReserveRate": "0.001", "marketOrderPriceMarkup": "0.1", "marginRequirements": [{"positionNotionalLowerBound": "0", "positionNotionalUpperBound": "8000", "initialMarginRate": "0.01", "maintenanceMarginRate": "0.0065"}, {"positionNotionalLowerBound": "8000", "positionNotionalUpperBound": "15000", "initialMarginRate": "0.013333", "maintenanceMarginRate": "0.008"}, {"positionNotionalLowerBound": "15000", "positionNotionalUpperBound": "30000", "initialMarginRate": "0.02", "maintenanceMarginRate": "0.014"}, {"positionNotionalLowerBound": "30000", "positionNotionalUpperBound": "80000", "initialMarginRate": "0.033333", "maintenanceMarginRate": "0.023"}, {"positionNotionalLowerBound": "80000", "positionNotionalUpperBound": "150000", "initialMarginRate": "0.05", "maintenanceMarginRate": "0.035"}, {"positionNotionalLowerBound": "150000", "positionNotionalUpperBound": "250000", "initialMarginRate": "0.1", "maintenanceMarginRate": "0.07"}, {"positionNotionalLowerBound": "250000", "positionNotionalUpperBound": "500000", "initialMarginRate": "0.2", "maintenanceMarginRate": "0.11"}, {"positionNotionalLowerBound": "500000", "positionNotionalUpperBound": "1000000000", "initialMarginRate": "1", "maintenanceMarginRate": "0.55"}]}, "tierBased": true, "percentage": true, "feeSide": "get"}}