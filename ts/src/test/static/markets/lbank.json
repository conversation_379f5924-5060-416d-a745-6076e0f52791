{"LBK/USDT": {"id": "lbk_usdt", "lowercaseId": null, "symbol": "LBK/USDT", "base": "LBK", "quote": "USDT", "settle": null, "baseId": "lbk", "quoteId": "usdt", "settleId": null, "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.001, "maker": 0.001, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1, "price": 1e-06}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 1, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"symbol": "lbk_usdt", "quantityAccuracy": "0", "minTranQua": "1", "priceAccuracy": "6"}, "tierBased": null, "percentage": null}, "BTC/USDT": {"id": "btc_usdt", "lowercaseId": null, "symbol": "BTC/USDT", "base": "BTC", "quote": "USDT", "settle": null, "baseId": "btc", "quoteId": "usdt", "settleId": null, "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.001, "maker": 0.001, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.0001, "price": 0.01}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 0.0001, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"symbol": "btc_usdt", "quantityAccuracy": "4", "minTranQua": "0.0001", "priceAccuracy": "2"}, "tierBased": null, "percentage": null}, "LTC/USDT": {"id": "ltc_usdt", "lowercaseId": null, "symbol": "LTC/USDT", "base": "LTC", "quote": "USDT", "settle": null, "baseId": "ltc", "quoteId": "usdt", "settleId": null, "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.001, "maker": 0.001, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.0001, "price": 0.01}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 0.0001, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"symbol": "ltc_usdt", "quantityAccuracy": "4", "minTranQua": "0.0001", "priceAccuracy": "2"}, "tierBased": null, "percentage": null}, "ADA/USDT": {"id": "ada_usdt", "lowercaseId": null, "symbol": "ADA/USDT", "base": "ADA", "quote": "USDT", "settle": null, "baseId": "ada", "quoteId": "usdt", "settleId": null, "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.001, "maker": 0.001, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.1, "price": 0.0001}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 0.1, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"symbol": "ada_usdt", "quantityAccuracy": "1", "minTranQua": "0.1", "priceAccuracy": "4"}, "tierBased": null, "percentage": null}, "XRP/USDT": {"id": "xrp_usdt", "lowercaseId": null, "symbol": "XRP/USDT", "base": "XRP", "quote": "USDT", "settle": null, "baseId": "xrp", "quoteId": "usdt", "settleId": null, "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.001, "maker": 0.001, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.01, "price": 0.0001}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 10, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"symbol": "xrp_usdt", "quantityAccuracy": "2", "minTranQua": "10", "priceAccuracy": "4"}, "tierBased": null, "percentage": null}, "SOL/USDT": {"id": "sol_usdt", "lowercaseId": null, "symbol": "SOL/USDT", "base": "SOL", "quote": "USDT", "settle": null, "baseId": "sol", "quoteId": "usdt", "settleId": null, "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.001, "maker": 0.001, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.0001, "price": 0.01}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 0.035, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"symbol": "sol_usdt", "quantityAccuracy": "4", "minTranQua": "0.035", "priceAccuracy": "2"}, "tierBased": null, "percentage": null}, "TRX/USDT": {"id": "trx_usdt", "lowercaseId": null, "symbol": "TRX/USDT", "base": "TRX", "quote": "USDT", "settle": null, "baseId": "trx", "quoteId": "usdt", "settleId": null, "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.001, "maker": 0.001, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1, "price": 0.0001}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 1, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"symbol": "trx_usdt", "quantityAccuracy": "0", "minTranQua": "1", "priceAccuracy": "4"}, "tierBased": null, "percentage": null}, "LTC/USDT:USDT": {"id": "LTCUSDT", "lowercaseId": null, "symbol": "LTC/USDT:USDT", "base": "LTC", "quote": "USDT", "settle": "USDT", "baseId": "LTC", "quoteId": "USDT", "settleId": "USDT", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": null, "subType": "linear", "taker": 0.001, "maker": 0.001, "contractSize": 1, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.001, "price": 0.01}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 0.001, "max": 100000}, "price": {"min": 0.01, "max": 0.01}, "cost": {"min": 5, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"priceLimitUpperValue": "0.01", "symbol": "LTCUSDT", "volumeTick": "0.001", "indexPrice": "101.17540000", "minOrderVolume": "0.001", "priceTick": "0.01", "maxOrderVolume": "100000.0", "baseCurrency": "LTC", "maxLeverage": "75", "volumeMultiple": "1.0", "exchangeID": "Exchange", "priceCurrency": "USDT", "priceLimitLowerValue": "0.01", "clearCurrency": "USDT", "symbolName": "LTCUSDT", "defaultLeverage": "20.0", "minOrderCost": "5.0"}, "tierBased": null, "percentage": null}, "BTC/USDT:USDT": {"id": "BTCUSDT", "lowercaseId": null, "symbol": "BTC/USDT:USDT", "base": "BTC", "quote": "USDT", "settle": "USDT", "baseId": "BTC", "quoteId": "USDT", "settleId": "USDT", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": null, "subType": "linear", "taker": 0.001, "maker": 0.001, "contractSize": 1, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.0001, "price": 0.1}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 0.0001, "max": 600}, "price": {"min": 0.01, "max": 0.01}, "cost": {"min": 1, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"priceLimitUpperValue": "0.01", "symbol": "BTCUSDT", "volumeTick": "0.0001", "indexPrice": "110781.92400000", "minOrderVolume": "0.0001", "priceTick": "0.1", "maxOrderVolume": "600.0", "baseCurrency": "BTC", "maxLeverage": "200", "volumeMultiple": "1.0", "exchangeID": "Exchange", "priceCurrency": "USDT", "priceLimitLowerValue": "0.01", "clearCurrency": "USDT", "symbolName": "BTCUSDT", "defaultLeverage": "20.0", "minOrderCost": "1.0"}, "tierBased": null, "percentage": null}, "ADA/USDT:USDT": {"id": "ADAUSDT", "lowercaseId": null, "symbol": "ADA/USDT:USDT", "base": "ADA", "quote": "USDT", "settle": "USDT", "baseId": "ADA", "quoteId": "USDT", "settleId": "USDT", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": null, "subType": "linear", "taker": 0.001, "maker": 0.001, "contractSize": 1, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1, "price": 0.0001}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 1, "max": 200000000}, "price": {"min": 0.2, "max": 0.2}, "cost": {"min": 5, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"priceLimitUpperValue": "0.2", "symbol": "ADAUSDT", "volumeTick": "1.0", "indexPrice": "0.82570760", "minOrderVolume": "1.0", "priceTick": "0.0001", "maxOrderVolume": "200000000.0", "baseCurrency": "ADA", "maxLeverage": "75", "volumeMultiple": "1.0", "exchangeID": "Exchange", "priceCurrency": "USDT", "priceLimitLowerValue": "0.2", "clearCurrency": "USDT", "symbolName": "ADAUSDT", "defaultLeverage": "20.0", "minOrderCost": "5.0"}, "tierBased": null, "percentage": null}, "XRP/USDT:USDT": {"id": "XRPUSDT", "lowercaseId": null, "symbol": "XRP/USDT:USDT", "base": "XRP", "quote": "USDT", "settle": "USDT", "baseId": "XRP", "quoteId": "USDT", "settleId": "USDT", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": null, "subType": "linear", "taker": 0.001, "maker": 0.001, "contractSize": 1, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.1, "price": 0.0001}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 0.1, "max": 6500000000}, "price": {"min": 0.01, "max": 0.01}, "cost": {"min": 5, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"priceLimitUpperValue": "0.01", "symbol": "XRPUSDT", "volumeTick": "0.1", "indexPrice": "2.45178800", "minOrderVolume": "0.1", "priceTick": "0.0001", "maxOrderVolume": "6500000000.0", "baseCurrency": "XRP", "maxLeverage": "75", "volumeMultiple": "1.0", "exchangeID": "Exchange", "priceCurrency": "USDT", "priceLimitLowerValue": "0.01", "clearCurrency": "USDT", "symbolName": "XRPUSDT", "defaultLeverage": "20.0", "minOrderCost": "5.0"}, "tierBased": null, "percentage": null}, "TRX/USDT:USDT": {"id": "TRXUSDT", "lowercaseId": null, "symbol": "TRX/USDT:USDT", "base": "TRX", "quote": "USDT", "settle": "USDT", "baseId": "TRX", "quoteId": "USDT", "settleId": "USDT", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": null, "subType": "linear", "taker": 0.001, "maker": 0.001, "contractSize": 1, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1, "price": 1e-05}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 1, "max": 50000000}, "price": {"min": 0.2, "max": 0.2}, "cost": {"min": 5, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"priceLimitUpperValue": "0.2", "symbol": "TRXUSDT", "volumeTick": "1.0", "indexPrice": "0.27340180", "minOrderVolume": "1.0", "priceTick": "0.00001", "maxOrderVolume": "50000000.0", "baseCurrency": "TRX", "maxLeverage": "75", "volumeMultiple": "1.0", "exchangeID": "Exchange", "priceCurrency": "USDT", "priceLimitLowerValue": "0.2", "clearCurrency": "USDT", "symbolName": "TRXUSDT", "defaultLeverage": "20.0", "minOrderCost": "5.0"}, "tierBased": null, "percentage": null}, "ETH/USDT": {"id": "eth_usdt", "lowercaseId": null, "symbol": "ETH/USDT", "base": "ETH", "quote": "USDT", "settle": null, "baseId": "eth", "quoteId": "usdt", "settleId": null, "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.001, "maker": 0.001, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.0001, "price": 0.01}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 0.0001, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"symbol": "eth_usdt", "quantityAccuracy": "4", "minTranQua": "0.0001", "priceAccuracy": "2"}, "tierBased": null, "percentage": null}, "ETH/USDT:USDT": {"id": "ETHUSDT", "lowercaseId": null, "symbol": "ETH/USDT:USDT", "base": "ETH", "quote": "USDT", "settle": "USDT", "baseId": "ETH", "quoteId": "USDT", "settleId": "USDT", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": null, "subType": "linear", "taker": 0.001, "maker": 0.001, "contractSize": 1, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.001, "price": 0.01}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 0.001, "max": 6000}, "price": {"min": 0.01, "max": 0.01}, "cost": {"min": 5, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"priceLimitUpperValue": "0.01", "symbol": "ETHUSDT", "volumeTick": "0.001", "indexPrice": "2694.93200000", "minOrderVolume": "0.001", "priceTick": "0.01", "maxOrderVolume": "6000.0", "baseCurrency": "ETH", "maxLeverage": "200", "volumeMultiple": "1.0", "exchangeID": "Exchange", "priceCurrency": "USDT", "priceLimitLowerValue": "0.01", "clearCurrency": "USDT", "symbolName": "ETHUSDT", "defaultLeverage": "20.0", "minOrderCost": "5.0"}, "tierBased": null, "percentage": null}}