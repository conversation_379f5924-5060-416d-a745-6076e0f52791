{"BTC/USD": {"id": "BTCUSD", "symbol": "BTC/USD", "base": "BTC", "quote": "USD", "baseId": "BTC", "quoteId": "USD", "active": true, "type": "spot", "spot": true, "swap": false, "future": false, "option": false, "margin": false, "contract": false, "precision": {"amount": 1e-05, "price": 0.01}, "limits": {"amount": {"min": 1e-05}, "price": {"min": 0.01}, "cost": {"min": 1e-07}, "leverage": {"min": 1, "max": 1}}, "info": {"type": "spot", "base_currency": "BTC", "quote_currency": "USD", "status": "working", "quantity_increment": "0.00001", "tick_size": "0.01", "take_rate": "0.001", "make_rate": "0.0001", "fee_currency": "USD"}, "tierBased": true, "percentage": true, "taker": 0.001, "maker": 0.0001, "tiers": {"maker": [[0, 0.0009], [10, 0.0007], [100, 0.0006], [500, 0.0005], [1000, 0.0003], [5000, 0.0002], [10000, 0.0001], [20000, 0], [50000, -0.0001], [100000, -0.0001]], "taker": [[0, 0.0009], [10, 0.0008], [100, 0.0007], [500, 0.0007], [1000, 0.0006], [5000, 0.0006], [10000, 0.0005], [20000, 0.0004], [50000, 0.0003], [100000, 0.0002]]}, "feeCurrency": "USD"}, "BTC/EUR": {"id": "BTCEUR", "symbol": "BTC/EUR", "base": "BTC", "quote": "EUR", "baseId": "BTC", "quoteId": "EUR", "active": true, "type": "spot", "spot": true, "swap": false, "future": false, "option": false, "margin": false, "contract": false, "precision": {"amount": 1e-05, "price": 0.01}, "limits": {"amount": {"min": 1e-05}, "price": {"min": 0.01}, "cost": {"min": 1e-07}, "leverage": {"min": 1, "max": 1}}, "info": {"type": "spot", "base_currency": "BTC", "quote_currency": "EUR", "status": "working", "quantity_increment": "0.00001", "tick_size": "0.01", "take_rate": "0.001", "make_rate": "0.0001", "fee_currency": "EUR"}, "tierBased": true, "percentage": true, "taker": 0.001, "maker": 0.0001, "tiers": {"maker": [[0, 0.0009], [10, 0.0007], [100, 0.0006], [500, 0.0005], [1000, 0.0003], [5000, 0.0002], [10000, 0.0001], [20000, 0], [50000, -0.0001], [100000, -0.0001]], "taker": [[0, 0.0009], [10, 0.0008], [100, 0.0007], [500, 0.0007], [1000, 0.0006], [5000, 0.0006], [10000, 0.0005], [20000, 0.0004], [50000, 0.0003], [100000, 0.0002]]}, "feeCurrency": "EUR"}, "ETH/USD": {"id": "ETHUSD", "symbol": "ETH/USD", "base": "ETH", "quote": "USD", "baseId": "ETH", "quoteId": "USD", "active": true, "type": "spot", "spot": true, "swap": false, "future": false, "option": false, "margin": false, "contract": false, "precision": {"amount": 0.0001, "price": 0.001}, "limits": {"amount": {"min": 0.0001}, "price": {"min": 0.001}, "cost": {"min": 1e-07}, "leverage": {"min": 1, "max": 1}}, "info": {"type": "spot", "base_currency": "ETH", "quote_currency": "USD", "status": "working", "quantity_increment": "0.0001", "tick_size": "0.001", "take_rate": "0.001", "make_rate": "0.0001", "fee_currency": "USD"}, "tierBased": true, "percentage": true, "taker": 0.001, "maker": 0.0001, "tiers": {"maker": [[0, 0.0009], [10, 0.0007], [100, 0.0006], [500, 0.0005], [1000, 0.0003], [5000, 0.0002], [10000, 0.0001], [20000, 0], [50000, -0.0001], [100000, -0.0001]], "taker": [[0, 0.0009], [10, 0.0008], [100, 0.0007], [500, 0.0007], [1000, 0.0006], [5000, 0.0006], [10000, 0.0005], [20000, 0.0004], [50000, 0.0003], [100000, 0.0002]]}, "feeCurrency": "USD"}, "BTC/USDT": {"id": "BTCUSDT", "symbol": "BTC/USDT", "base": "BTC", "quote": "USDT", "baseId": "BTC", "quoteId": "USDT", "active": true, "type": "spot", "spot": true, "swap": false, "future": false, "option": false, "margin": true, "contract": false, "precision": {"amount": 1e-05, "price": 0.01}, "limits": {"amount": {"min": 1e-05}, "price": {"min": 0.01}, "cost": {"min": 1e-07}, "leverage": {"min": 1, "max": 25}}, "info": {"type": "spot", "base_currency": "BTC", "quote_currency": "USDT", "status": "working", "quantity_increment": "0.00001", "tick_size": "0.01", "take_rate": "0.001", "make_rate": "0.0001", "fee_currency": "USDT", "margin_trading": true, "max_initial_leverage": "25.00"}, "tierBased": true, "percentage": true, "taker": 0.001, "maker": 0.0001, "tiers": {"maker": [[0, 0.0009], [10, 0.0007], [100, 0.0006], [500, 0.0005], [1000, 0.0003], [5000, 0.0002], [10000, 0.0001], [20000, 0], [50000, -0.0001], [100000, -0.0001]], "taker": [[0, 0.0009], [10, 0.0008], [100, 0.0007], [500, 0.0007], [1000, 0.0006], [5000, 0.0006], [10000, 0.0005], [20000, 0.0004], [50000, 0.0003], [100000, 0.0002]]}, "feeCurrency": "USDT"}, "LTC/USDT": {"id": "LTCUSDT", "symbol": "LTC/USDT", "base": "LTC", "quote": "USDT", "baseId": "LTC", "quoteId": "USDT", "active": true, "type": "spot", "spot": true, "swap": false, "future": false, "option": false, "margin": true, "contract": false, "precision": {"amount": 0.001, "price": 0.0001}, "limits": {"amount": {"min": 0.001}, "price": {"min": 0.0001}, "cost": {"min": 1e-07}, "leverage": {"min": 1, "max": 20}}, "info": {"type": "spot", "base_currency": "LTC", "quote_currency": "USDT", "status": "working", "quantity_increment": "0.001", "tick_size": "0.0001", "take_rate": "0.001", "make_rate": "0.0001", "fee_currency": "USDT", "margin_trading": true, "max_initial_leverage": "20.00"}, "tierBased": true, "percentage": true, "taker": 0.001, "maker": 0.0001, "tiers": {"maker": [[0, 0.0009], [10, 0.0007], [100, 0.0006], [500, 0.0005], [1000, 0.0003], [5000, 0.0002], [10000, 0.0001], [20000, 0], [50000, -0.0001], [100000, -0.0001]], "taker": [[0, 0.0009], [10, 0.0008], [100, 0.0007], [500, 0.0007], [1000, 0.0006], [5000, 0.0006], [10000, 0.0005], [20000, 0.0004], [50000, 0.0003], [100000, 0.0002]]}, "feeCurrency": "USDT"}, "ADA/USDT": {"id": "ADAUSDT", "symbol": "ADA/USDT", "base": "ADA", "quote": "USDT", "baseId": "ADA", "quoteId": "USDT", "active": true, "type": "spot", "spot": true, "swap": false, "future": false, "option": false, "margin": true, "contract": false, "precision": {"amount": 1, "price": 1e-07}, "limits": {"amount": {"min": 1}, "price": {"min": 1e-07}, "cost": {"min": 1e-07}, "leverage": {"min": 1, "max": 10}}, "info": {"type": "spot", "base_currency": "ADA", "quote_currency": "USDT", "status": "working", "quantity_increment": "1", "tick_size": "0.0000001", "take_rate": "0.001", "make_rate": "0.0001", "fee_currency": "USDT", "margin_trading": true, "max_initial_leverage": "10.00"}, "tierBased": true, "percentage": true, "taker": 0.001, "maker": 0.0001, "tiers": {"maker": [[0, 0.0009], [10, 0.0007], [100, 0.0006], [500, 0.0005], [1000, 0.0003], [5000, 0.0002], [10000, 0.0001], [20000, 0], [50000, -0.0001], [100000, -0.0001]], "taker": [[0, 0.0009], [10, 0.0008], [100, 0.0007], [500, 0.0007], [1000, 0.0006], [5000, 0.0006], [10000, 0.0005], [20000, 0.0004], [50000, 0.0003], [100000, 0.0002]]}, "feeCurrency": "USDT"}, "XRP/USDT": {"id": "XRPUSDT", "symbol": "XRP/USDT", "base": "XRP", "quote": "USDT", "baseId": "XRP", "quoteId": "USDT", "active": true, "type": "spot", "spot": true, "swap": false, "future": false, "option": false, "margin": false, "contract": false, "precision": {"amount": 0.1, "price": 1e-06}, "limits": {"amount": {"min": 0.1}, "price": {"min": 1e-06}, "cost": {"min": 1e-07}, "leverage": {"min": 1, "max": 1}}, "info": {"type": "spot", "base_currency": "XRP", "quote_currency": "USDT", "status": "working", "quantity_increment": "0.1", "tick_size": "0.000001", "take_rate": "0.001", "make_rate": "0.0001", "fee_currency": "USDT"}, "tierBased": true, "percentage": true, "taker": 0.001, "maker": 0.0001, "tiers": {"maker": [[0, 0.0009], [10, 0.0007], [100, 0.0006], [500, 0.0005], [1000, 0.0003], [5000, 0.0002], [10000, 0.0001], [20000, 0], [50000, -0.0001], [100000, -0.0001]], "taker": [[0, 0.0009], [10, 0.0008], [100, 0.0007], [500, 0.0007], [1000, 0.0006], [5000, 0.0006], [10000, 0.0005], [20000, 0.0004], [50000, 0.0003], [100000, 0.0002]]}, "feeCurrency": "USDT"}, "SOL/USDT": {"id": "SOLUSDT", "symbol": "SOL/USDT", "base": "SOL", "quote": "USDT", "baseId": "SOL", "quoteId": "USDT", "active": true, "type": "spot", "spot": true, "swap": false, "future": false, "option": false, "margin": false, "contract": false, "precision": {"amount": 0.001, "price": 0.0001}, "limits": {"amount": {"min": 0.001}, "price": {"min": 0.0001}, "cost": {"min": 1e-07}, "leverage": {"min": 1, "max": 1}}, "info": {"type": "spot", "base_currency": "SOL", "quote_currency": "USDT", "status": "working", "quantity_increment": "0.001", "tick_size": "0.0001", "take_rate": "0.001", "make_rate": "0.0001", "fee_currency": "USDT"}, "tierBased": true, "percentage": true, "taker": 0.001, "maker": 0.0001, "tiers": {"maker": [[0, 0.0009], [10, 0.0007], [100, 0.0006], [500, 0.0005], [1000, 0.0003], [5000, 0.0002], [10000, 0.0001], [20000, 0], [50000, -0.0001], [100000, -0.0001]], "taker": [[0, 0.0009], [10, 0.0008], [100, 0.0007], [500, 0.0007], [1000, 0.0006], [5000, 0.0006], [10000, 0.0005], [20000, 0.0004], [50000, 0.0003], [100000, 0.0002]]}, "feeCurrency": "USDT"}, "TRX/USDT": {"id": "TRXUSDT", "symbol": "TRX/USDT", "base": "TRX", "quote": "USDT", "baseId": "TRX", "quoteId": "USDT", "active": true, "type": "spot", "spot": true, "swap": false, "future": false, "option": false, "margin": true, "contract": false, "precision": {"amount": 1, "price": 1e-07}, "limits": {"amount": {"min": 1}, "price": {"min": 1e-07}, "cost": {"min": 1e-07}, "leverage": {"min": 1, "max": 10}}, "info": {"type": "spot", "base_currency": "TRX", "quote_currency": "USDT", "status": "working", "quantity_increment": "1", "tick_size": "0.0000001", "take_rate": "0.001", "make_rate": "0.0001", "fee_currency": "USDT", "margin_trading": true, "max_initial_leverage": "10.00"}, "tierBased": true, "percentage": true, "taker": 0.001, "maker": 0.0001, "tiers": {"maker": [[0, 0.0009], [10, 0.0007], [100, 0.0006], [500, 0.0005], [1000, 0.0003], [5000, 0.0002], [10000, 0.0001], [20000, 0], [50000, -0.0001], [100000, -0.0001]], "taker": [[0, 0.0009], [10, 0.0008], [100, 0.0007], [500, 0.0007], [1000, 0.0006], [5000, 0.0006], [10000, 0.0005], [20000, 0.0004], [50000, 0.0003], [100000, 0.0002]]}, "feeCurrency": "USDT"}, "ETH/USDT": {"id": "ETHUSDT", "symbol": "ETH/USDT", "base": "ETH", "quote": "USDT", "baseId": "ETH", "quoteId": "USDT", "type": "spot", "spot": true, "margin": true, "swap": false, "future": false, "option": false, "active": true, "contract": false, "taker": 0.001, "maker": 0.0001, "precision": {"amount": 0.0001, "price": 0.001}, "limits": {"leverage": {"min": 1, "max": 20}, "amount": {"min": 0.0001}, "price": {"min": 0.001}, "cost": {"min": 1e-07}}, "info": {"type": "spot", "base_currency": "ETH", "quote_currency": "USDT", "status": "working", "quantity_increment": "0.0001", "tick_size": "0.001", "take_rate": "0.001", "make_rate": "0.0001", "fee_currency": "USDT", "margin_trading": true, "max_initial_leverage": "20.00"}, "tierBased": true, "percentage": true, "tiers": {"maker": [[0, 0.0009], [10, 0.0007], [100, 0.0006], [500, 0.0005], [1000, 0.0003], [5000, 0.0002], [10000, 0.0001], [20000, 0], [50000, -0.0001], [100000, -0.0001]], "taker": [[0, 0.0009], [10, 0.0008], [100, 0.0007], [500, 0.0007], [1000, 0.0006], [5000, 0.0006], [10000, 0.0005], [20000, 0.0004], [50000, 0.0003], [100000, 0.0002]]}, "feeCurrency": "USDT"}, "ETH/USDT:USDT": {"id": "ETHUSDT_PERP", "symbol": "ETH/USDT:USDT", "base": "ETH", "quote": "USDT", "settle": "USDT", "baseId": "ETH", "quoteId": "USDT", "settleId": "USDT", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": 0.0005, "maker": 0.0001, "contractSize": 1, "precision": {"amount": 0.001, "price": 0.001}, "limits": {"leverage": {"min": 1, "max": 75}, "amount": {"min": 0.001}, "price": {"min": 0.001}, "cost": {"min": 1e-06}}, "info": {"type": "futures", "contract_type": "perpetual", "expiry": null, "underlying": "ETH", "base_currency": null, "quote_currency": "USDT", "status": "working", "quantity_increment": "0.001", "tick_size": "0.001", "take_rate": "0.0005", "make_rate": "0.0001", "fee_currency": "USDT", "margin_trading": true, "max_initial_leverage": "75.00"}, "tierBased": true, "percentage": true, "tiers": {"maker": [[0, 0.0009], [10, 0.0007], [100, 0.0006], [500, 0.0005], [1000, 0.0003], [5000, 0.0002], [10000, 0.0001], [20000, 0], [50000, -0.0001], [100000, -0.0001]], "taker": [[0, 0.0009], [10, 0.0008], [100, 0.0007], [500, 0.0007], [1000, 0.0006], [5000, 0.0006], [10000, 0.0005], [20000, 0.0004], [50000, 0.0003], [100000, 0.0002]]}, "feeCurrency": "USDT"}, "BTC/USDT:USDT": {"id": "BTCUSDT_PERP", "symbol": "BTC/USDT:USDT", "base": "BTC", "quote": "USDT", "settle": "USDT", "baseId": "BTC", "quoteId": "USDT", "settleId": "USDT", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": 0.0005, "maker": 0.0001, "contractSize": 1, "precision": {"amount": 0.0001, "price": 0.01}, "limits": {"leverage": {"min": 1, "max": 100}, "amount": {"min": 0.0001}, "price": {"min": 0.01}, "cost": {"min": 1e-06}}, "info": {"type": "futures", "contract_type": "perpetual", "expiry": null, "underlying": "BTC", "base_currency": null, "quote_currency": "USDT", "status": "working", "quantity_increment": "0.0001", "tick_size": "0.01", "take_rate": "0.0005", "make_rate": "0.0001", "fee_currency": "USDT", "margin_trading": true, "max_initial_leverage": "100.00"}, "tierBased": true, "percentage": true, "tiers": {"maker": [[0, 0.0009], [10, 0.0007], [100, 0.0006], [500, 0.0005], [1000, 0.0003], [5000, 0.0002], [10000, 0.0001], [20000, 0], [50000, -0.0001], [100000, -0.0001]], "taker": [[0, 0.0009], [10, 0.0008], [100, 0.0007], [500, 0.0007], [1000, 0.0006], [5000, 0.0006], [10000, 0.0005], [20000, 0.0004], [50000, 0.0003], [100000, 0.0002]]}, "feeCurrency": "USDT"}}