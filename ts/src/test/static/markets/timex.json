{"BTC/USD": {"id": "BTCUSD", "lowercaseId": null, "symbol": "BTC/USD", "base": "BTC", "quote": "USD", "settle": null, "baseId": "BTC", "quoteId": "USD", "settleId": null, "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.005, "maker": 0.0025, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.0001, "price": 1}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 0.0005, "max": null}, "price": {"min": 1, "max": null}, "cost": {"min": 30, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"symbol": "BTCUSD", "name": "BTC/USD", "baseCurrency": "BTC", "baseTokenAddress": "******************************************", "quoteCurrency": "USD", "quoteTokenAddress": "******************************************", "feeCurrency": "USD", "feeTokenAddress": "******************************************", "alternativeCurrency": null, "alternativeTokenAddress": null, "quantityIncrement": "0.0001", "takerFee": "0.005", "makerFee": "0.0025", "takerAltFee": null, "makerAltFee": null, "tickSize": "1", "baseMinSize": "0.0005", "quoteMinSize": "30", "locked": false, "promotional": false, "showOnLanding": true, "sortOrder": "13", "defaultSlippage": null}, "tierBased": null, "percentage": null}, "ETH/USD": {"id": "ETHUSD", "lowercaseId": null, "symbol": "ETH/USD", "base": "ETH", "quote": "USD", "settle": null, "baseId": "ETH", "quoteId": "USD", "settleId": null, "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.005, "maker": 0.0025, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.001, "price": 0.1}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 0.01, "max": null}, "price": {"min": 0.1, "max": null}, "cost": {"min": 1, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"symbol": "ETHUSD", "name": "ETH/USD", "baseCurrency": "ETH", "baseTokenAddress": "******************************************", "quoteCurrency": "USD", "quoteTokenAddress": "******************************************", "feeCurrency": "USD", "feeTokenAddress": "******************************************", "alternativeCurrency": null, "alternativeTokenAddress": null, "quantityIncrement": "0.001", "takerFee": "0.005", "makerFee": "0.0025", "takerAltFee": null, "makerAltFee": null, "tickSize": "0.1", "baseMinSize": "0.01", "quoteMinSize": "1", "locked": false, "promotional": false, "showOnLanding": false, "sortOrder": "23", "defaultSlippage": null}, "tierBased": null, "percentage": null}, "BTC/USDT": {"id": "BTCUSDT", "lowercaseId": null, "symbol": "BTC/USDT", "base": "BTC", "quote": "USDT", "settle": null, "baseId": "BTC", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.005, "maker": 0.0025, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.0001, "price": 1}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 0.0005, "max": null}, "price": {"min": 1, "max": null}, "cost": {"min": 30, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"symbol": "BTCUSDT", "name": "BTC/USDT", "baseCurrency": "BTC", "baseTokenAddress": "******************************************", "quoteCurrency": "USDT", "quoteTokenAddress": "******************************************", "feeCurrency": "USDT", "feeTokenAddress": "******************************************", "alternativeCurrency": null, "alternativeTokenAddress": null, "quantityIncrement": "0.0001", "takerFee": "0.005", "makerFee": "0.0025", "takerAltFee": null, "makerAltFee": null, "tickSize": "1", "baseMinSize": "0.0005", "quoteMinSize": "30", "locked": false, "promotional": false, "showOnLanding": true, "sortOrder": "12", "defaultSlippage": null}, "tierBased": null, "percentage": null}, "LTC/USDT": {"id": "LTCUSDT", "lowercaseId": null, "symbol": "LTC/USDT", "base": "LTC", "quote": "USDT", "settle": null, "baseId": "LTC", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.005, "maker": 0.0025, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.0001, "price": 0.01}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 0.1, "max": null}, "price": {"min": 0.01, "max": null}, "cost": {"min": 30, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"symbol": "LTCUSDT", "name": "LTC/USDT", "baseCurrency": "LTC", "baseTokenAddress": "0x2a5cf1cf897c14513d880491c39d402fb9385fc7", "quoteCurrency": "USDT", "quoteTokenAddress": "******************************************", "feeCurrency": "USDT", "feeTokenAddress": "******************************************", "alternativeCurrency": null, "alternativeTokenAddress": null, "quantityIncrement": "0.0001", "takerFee": "0.005", "makerFee": "0.0025", "takerAltFee": null, "makerAltFee": null, "tickSize": "0.01", "baseMinSize": "0.1", "quoteMinSize": "30", "locked": false, "promotional": false, "showOnLanding": false, "sortOrder": "41", "defaultSlippage": null}, "tierBased": null, "percentage": null}, "XRP/USDT": {"id": "XRPUSDT", "lowercaseId": null, "symbol": "XRP/USDT", "base": "XRP", "quote": "USDT", "settle": null, "baseId": "XRP", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.01, "maker": 0.0025, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1, "price": 1e-05}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 20, "max": null}, "price": {"min": 1e-05, "max": null}, "cost": {"min": 20, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"symbol": "XRPUSDT", "name": "XRP/USDT", "baseCurrency": "XRP", "baseTokenAddress": "0x0dc8882914f3ddeebf4cec6dc20edb99df3def6c", "quoteCurrency": "USDT", "quoteTokenAddress": "******************************************", "feeCurrency": "USDT", "feeTokenAddress": "******************************************", "alternativeCurrency": null, "alternativeTokenAddress": null, "quantityIncrement": "1", "takerFee": "0.01", "makerFee": "0.0025", "takerAltFee": null, "makerAltFee": null, "tickSize": "0.00001", "baseMinSize": "20", "quoteMinSize": "20", "locked": false, "promotional": false, "showOnLanding": false, "sortOrder": "43", "defaultSlippage": null}, "tierBased": null, "percentage": null}, "ETH/USDT": {"id": "ETHUSDT", "lowercaseId": null, "symbol": "ETH/USDT", "base": "ETH", "quote": "USDT", "settle": null, "baseId": "ETH", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.005, "maker": 0.0025, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.001, "price": 0.1}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 0.01, "max": null}, "price": {"min": 0.1, "max": null}, "cost": {"min": 25, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"symbol": "ETHUSDT", "name": "ETH/USDT", "baseCurrency": "ETH", "baseTokenAddress": "******************************************", "quoteCurrency": "USDT", "quoteTokenAddress": "******************************************", "feeCurrency": "USDT", "feeTokenAddress": "******************************************", "alternativeCurrency": null, "alternativeTokenAddress": null, "quantityIncrement": "0.001", "takerFee": "0.005", "makerFee": "0.0025", "takerAltFee": null, "makerAltFee": null, "tickSize": "0.1", "baseMinSize": "0.01", "quoteMinSize": "25", "locked": false, "promotional": true, "showOnLanding": true, "sortOrder": "22", "defaultSlippage": null}, "tierBased": null, "percentage": null}}