{"BTC/USDT": {"id": "BTC_USDT", "lowercaseId": null, "symbol": "BTC/USDT", "base": "BTC", "quote": "USDT", "settle": null, "baseId": "BTC", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "index": false, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": null, "maker": null, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1e-05, "price": 0.01}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 1e-05, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": 5, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"symbol": "BTC_USDT", "symbol_id": "53", "base_currency": "BTC", "quote_currency": "USDT", "quote_increment": "0.00001", "base_min_size": "0.00001", "price_min_precision": "-1", "price_max_precision": "2", "expiration": "NA", "min_buy_amount": "5.000000000000000000000000000000", "min_sell_amount": "5.000000000000000000000000000000", "trade_status": "trading"}, "tierBased": true, "percentage": true, "tiers": {"taker": [[0, 0.002], [10, 0.18], [50, 0.0016], [250, 0.0014], [1000, 0.0012], [5000, 0.001], [25000, 0.0008], [50000, 0.0006]], "maker": [[0, 0.001], [10, 0.0009], [50, 0.0008], [250, 0.0007], [1000, 0.0006], [5000, 0.0005], [25000, 0.0004], [50000, 0.0003]]}, "numericId": 53}, "ETH/USDT": {"id": "ETH_USDT", "lowercaseId": null, "symbol": "ETH/USDT", "base": "ETH", "quote": "USDT", "settle": null, "baseId": "ETH", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "index": false, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": null, "maker": null, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1e-05, "price": 0.01}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 1e-05, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": 5, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"symbol": "ETH_USDT", "symbol_id": "55", "base_currency": "ETH", "quote_currency": "USDT", "quote_increment": "0.00001", "base_min_size": "0.00001", "price_min_precision": "-1", "price_max_precision": "2", "expiration": "NA", "min_buy_amount": "5.000000000000000000000000000000", "min_sell_amount": "5.000000000000000000000000000000", "trade_status": "trading"}, "tierBased": true, "percentage": true, "tiers": {"taker": [[0, 0.002], [10, 0.18], [50, 0.0016], [250, 0.0014], [1000, 0.0012], [5000, 0.001], [25000, 0.0008], [50000, 0.0006]], "maker": [[0, 0.001], [10, 0.0009], [50, 0.0008], [250, 0.0007], [1000, 0.0006], [5000, 0.0005], [25000, 0.0004], [50000, 0.0003]]}, "numericId": 55}, "ADA/USDT": {"id": "ADA_USDT", "lowercaseId": null, "symbol": "ADA/USDT", "base": "ADA", "quote": "USDT", "settle": null, "baseId": "ADA", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "index": false, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": null, "maker": null, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.01, "price": 1e-06}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 0.01, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": 5, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"symbol": "ADA_USDT", "symbol_id": "1386", "base_currency": "ADA", "quote_currency": "USDT", "quote_increment": "0.01", "base_min_size": "0.01", "price_min_precision": "3", "price_max_precision": "6", "expiration": "NA", "min_buy_amount": "5.000000000000000000000000000000", "min_sell_amount": "5.000000000000000000000000000000", "trade_status": "trading"}, "tierBased": true, "percentage": true, "tiers": {"taker": [[0, 0.002], [10, 0.18], [50, 0.0016], [250, 0.0014], [1000, 0.0012], [5000, 0.001], [25000, 0.0008], [50000, 0.0006]], "maker": [[0, 0.001], [10, 0.0009], [50, 0.0008], [250, 0.0007], [1000, 0.0006], [5000, 0.0005], [25000, 0.0004], [50000, 0.0003]]}, "numericId": 1386}, "LTC/USDT": {"id": "LTC_USDT", "lowercaseId": null, "symbol": "LTC/USDT", "base": "LTC", "quote": "USDT", "settle": null, "baseId": "LTC", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "index": false, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": null, "maker": null, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.001, "price": 0.0001}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 0.001, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": 5, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"symbol": "LTC_USDT", "symbol_id": "1091", "base_currency": "LTC", "quote_currency": "USDT", "quote_increment": "0.001", "base_min_size": "0.001", "price_min_precision": "1", "price_max_precision": "4", "expiration": "NA", "min_buy_amount": "5.000000000000000000000000000000", "min_sell_amount": "5.000000000000000000000000000000", "trade_status": "trading"}, "tierBased": true, "percentage": true, "tiers": {"taker": [[0, 0.002], [10, 0.18], [50, 0.0016], [250, 0.0014], [1000, 0.0012], [5000, 0.001], [25000, 0.0008], [50000, 0.0006]], "maker": [[0, 0.001], [10, 0.0009], [50, 0.0008], [250, 0.0007], [1000, 0.0006], [5000, 0.0005], [25000, 0.0004], [50000, 0.0003]]}, "numericId": 1091}, "XRP/USDT": {"id": "XRP_USDT", "lowercaseId": null, "symbol": "XRP/USDT", "base": "XRP", "quote": "USDT", "settle": null, "baseId": "XRP", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "index": false, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": null, "maker": null, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.1, "price": 1e-05}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 0.1, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": 5, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"symbol": "XRP_USDT", "symbol_id": "2995", "base_currency": "XRP", "quote_currency": "USDT", "quote_increment": "0.1", "base_min_size": "0.1", "price_min_precision": "2", "price_max_precision": "5", "expiration": "NA", "min_buy_amount": "5.000000000000000000000000000000", "min_sell_amount": "5.000000000000000000000000000000", "trade_status": "trading"}, "tierBased": true, "percentage": true, "tiers": {"taker": [[0, 0.002], [10, 0.18], [50, 0.0016], [250, 0.0014], [1000, 0.0012], [5000, 0.001], [25000, 0.0008], [50000, 0.0006]], "maker": [[0, 0.001], [10, 0.0009], [50, 0.0008], [250, 0.0007], [1000, 0.0006], [5000, 0.0005], [25000, 0.0004], [50000, 0.0003]]}, "numericId": 2995}, "DOGE/USDT": {"id": "DOGE_USDT", "lowercaseId": null, "symbol": "DOGE/USDT", "base": "DOGE", "quote": "USDT", "settle": null, "baseId": "DOGE", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "index": false, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": null, "maker": null, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1, "price": 1e-06}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 1, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": 5, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"symbol": "DOGE_USDT", "symbol_id": "1376", "base_currency": "DOGE", "quote_currency": "USDT", "quote_increment": "1", "base_min_size": "1", "price_min_precision": "3", "price_max_precision": "6", "expiration": "NA", "min_buy_amount": "5.000000000000000000000000000000", "min_sell_amount": "5.000000000000000000000000000000", "trade_status": "trading"}, "tierBased": true, "percentage": true, "tiers": {"taker": [[0, 0.002], [10, 0.18], [50, 0.0016], [250, 0.0014], [1000, 0.0012], [5000, 0.001], [25000, 0.0008], [50000, 0.0006]], "maker": [[0, 0.001], [10, 0.0009], [50, 0.0008], [250, 0.0007], [1000, 0.0006], [5000, 0.0005], [25000, 0.0004], [50000, 0.0003]]}, "numericId": 1376}, "TRX/USDT": {"id": "TRX_USDT", "lowercaseId": null, "symbol": "TRX/USDT", "base": "TRX", "quote": "USDT", "settle": null, "baseId": "TRX", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "index": false, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": null, "maker": null, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1, "price": 1e-06}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 1, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": 5, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"symbol": "TRX_USDT", "symbol_id": "1055", "base_currency": "TRX", "quote_currency": "USDT", "quote_increment": "1", "base_min_size": "1", "price_min_precision": "3", "price_max_precision": "6", "expiration": "NA", "min_buy_amount": "5.000000000000000000000000000000", "min_sell_amount": "5.000000000000000000000000000000", "trade_status": "trading"}, "tierBased": true, "percentage": true, "tiers": {"taker": [[0, 0.002], [10, 0.18], [50, 0.0016], [250, 0.0014], [1000, 0.0012], [5000, 0.001], [25000, 0.0008], [50000, 0.0006]], "maker": [[0, 0.001], [10, 0.0009], [50, 0.0008], [250, 0.0007], [1000, 0.0006], [5000, 0.0005], [25000, 0.0004], [50000, 0.0003]]}, "numericId": 1055}, "BTC/USDT:USDT": {"id": "BTCUSDT", "lowercaseId": null, "symbol": "BTC/USDT:USDT", "base": "BTC", "quote": "USDT", "settle": "USDT", "baseId": "BTC", "quoteId": "USDT", "settleId": "USDT", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": null, "maker": null, "contractSize": 0.001, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1, "price": 0.1}, "limits": {"leverage": {"min": 1, "max": 100}, "amount": {"min": 1, "max": 80000}, "price": {"min": null, "max": null}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": 1645977600000, "info": {"symbol": "BTCUSDT", "product_type": "1", "open_timestamp": "1645977600000", "expire_timestamp": "0", "settle_timestamp": "0", "base_currency": "BTC", "quote_currency": "USDT", "last_price": "95961.2", "volume_24h": "154395988", "turnover_24h": "14855216957.8232", "index_price": "96003.72933333", "index_name": "BTCUSDT", "contract_size": "0.001", "min_leverage": "1", "max_leverage": "100", "price_precision": "0.1", "vol_precision": "1", "max_volume": "80000", "min_volume": "1", "funding_rate": "0.0000941", "expected_funding_rate": "0.0000579", "open_interest": "389800", "open_interest_value": "37559981.1735778", "high_24h": "98090.9", "low_24h": "94030.8", "change_24h": "-0.000403125", "funding_time": "1739433600000", "market_max_volume": "80000", "funding_interval_hours": "8"}, "tierBased": true, "percentage": true, "tiers": {"taker": [[0, 0.002], [10, 0.18], [50, 0.0016], [250, 0.0014], [1000, 0.0012], [5000, 0.001], [25000, 0.0008], [50000, 0.0006]], "maker": [[0, 0.001], [10, 0.0009], [50, 0.0008], [250, 0.0007], [1000, 0.0006], [5000, 0.0005], [25000, 0.0004], [50000, 0.0003]]}, "numericId": null}, "ETH/USDT:USDT": {"id": "ETHUSDT", "lowercaseId": null, "symbol": "ETH/USDT:USDT", "base": "ETH", "quote": "USDT", "settle": "USDT", "baseId": "ETH", "quoteId": "USDT", "settleId": "USDT", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": null, "maker": null, "contractSize": 0.001, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1, "price": 0.01}, "limits": {"leverage": {"min": 1, "max": 100}, "amount": {"min": 1, "max": 1600000}, "price": {"min": null, "max": null}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": 1645948800000, "info": {"symbol": "ETHUSDT", "product_type": "1", "open_timestamp": "1645948800000", "expire_timestamp": "0", "settle_timestamp": "0", "base_currency": "ETH", "quote_currency": "USDT", "last_price": "2677.36", "volume_24h": "3875504288", "turnover_24h": "10295699419.88574", "index_price": "2678.66595238", "index_name": "ETHUSDT", "contract_size": "0.001", "min_leverage": "1", "max_leverage": "100", "price_precision": "0.01", "vol_precision": "1", "max_volume": "1600000", "min_volume": "1", "funding_rate": "0.0000955", "expected_funding_rate": "0.0001", "open_interest": "10126366", "open_interest_value": "26523275.26439296", "high_24h": "2794.32", "low_24h": "2546.95", "change_24h": "0.0242348890589135", "funding_time": "1739433600000", "market_max_volume": "1600000", "funding_interval_hours": "8"}, "tierBased": true, "percentage": true, "tiers": {"taker": [[0, 0.002], [10, 0.18], [50, 0.0016], [250, 0.0014], [1000, 0.0012], [5000, 0.001], [25000, 0.0008], [50000, 0.0006]], "maker": [[0, 0.001], [10, 0.0009], [50, 0.0008], [250, 0.0007], [1000, 0.0006], [5000, 0.0005], [25000, 0.0004], [50000, 0.0003]]}, "numericId": null}, "LTC/USDT:USDT": {"id": "LTCUSDT", "lowercaseId": null, "symbol": "LTC/USDT:USDT", "base": "LTC", "quote": "USDT", "settle": "USDT", "baseId": "LTC", "quoteId": "USDT", "settleId": "USDT", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": null, "maker": null, "contractSize": 0.001, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1, "price": 0.01}, "limits": {"leverage": {"min": 1, "max": 20}, "amount": {"min": 1, "max": 10000000}, "price": {"min": null, "max": null}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": 1646006400000, "info": {"symbol": "LTCUSDT", "product_type": "1", "open_timestamp": "1646006400000", "expire_timestamp": "0", "settle_timestamp": "0", "base_currency": "LTC", "quote_currency": "USDT", "last_price": "121.76", "volume_24h": "1457451924", "turnover_24h": "174763789.03074", "index_price": "121.78448485", "index_name": "LTCUSDT", "contract_size": "0.001", "min_leverage": "1", "max_leverage": "20", "price_precision": "0.01", "vol_precision": "1", "max_volume": "10000000", "min_volume": "1", "funding_rate": "0.0000448", "expected_funding_rate": "0.0001", "open_interest": "28884026", "open_interest_value": "3095536.65765148", "high_24h": "129.18", "low_24h": "111.35", "change_24h": "0.0402392140111064", "funding_time": "1739433600000", "market_max_volume": "1000000", "funding_interval_hours": "8"}, "tierBased": true, "percentage": true, "tiers": {"taker": [[0, 0.002], [10, 0.18], [50, 0.0016], [250, 0.0014], [1000, 0.0012], [5000, 0.001], [25000, 0.0008], [50000, 0.0006]], "maker": [[0, 0.001], [10, 0.0009], [50, 0.0008], [250, 0.0007], [1000, 0.0006], [5000, 0.0005], [25000, 0.0004], [50000, 0.0003]]}, "numericId": null}, "ADA/USDT:USDT": {"id": "ADAUSDT", "lowercaseId": null, "symbol": "ADA/USDT:USDT", "base": "ADA", "quote": "USDT", "settle": "USDT", "baseId": "ADA", "quoteId": "USDT", "settleId": "USDT", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": null, "maker": null, "contractSize": 1, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1, "price": 0.0001}, "limits": {"leverage": {"min": 1, "max": 50}, "amount": {"min": 1, "max": 10000000}, "price": {"min": null, "max": null}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": 1646006400000, "info": {"symbol": "ADAUSDT", "product_type": "1", "open_timestamp": "1646006400000", "expire_timestamp": "0", "settle_timestamp": "0", "base_currency": "ADA", "quote_currency": "USDT", "last_price": "0.7796", "volume_24h": "172661098", "turnover_24h": "134235696.13", "index_price": "0.78034458", "index_name": "ADAUSDT", "contract_size": "1", "min_leverage": "1", "max_leverage": "50", "price_precision": "0.0001", "vol_precision": "1", "max_volume": "10000000", "min_volume": "1", "funding_rate": "-0.000034", "expected_funding_rate": "-0.0000791", "open_interest": "10250676", "open_interest_value": "9283950.13341977", "high_24h": "0.8074", "low_24h": "0.7469", "change_24h": "0.0160302358920891", "funding_time": "1739433600000", "market_max_volume": "200000", "funding_interval_hours": "8"}, "tierBased": true, "percentage": true, "tiers": {"taker": [[0, 0.002], [10, 0.18], [50, 0.0016], [250, 0.0014], [1000, 0.0012], [5000, 0.001], [25000, 0.0008], [50000, 0.0006]], "maker": [[0, 0.001], [10, 0.0009], [50, 0.0008], [250, 0.0007], [1000, 0.0006], [5000, 0.0005], [25000, 0.0004], [50000, 0.0003]]}, "numericId": null}, "XRP/USDT:USDT": {"id": "XRPUSDT", "lowercaseId": null, "symbol": "XRP/USDT:USDT", "base": "XRP", "quote": "USDT", "settle": "USDT", "baseId": "XRP", "quoteId": "USDT", "settleId": "USDT", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": null, "maker": null, "contractSize": 0.1, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1, "price": 0.0001}, "limits": {"leverage": {"min": 1, "max": 50}, "amount": {"min": 1, "max": 200000000}, "price": {"min": null, "max": null}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": 1646006400000, "info": {"symbol": "XRPUSDT", "product_type": "1", "open_timestamp": "1646006400000", "expire_timestamp": "0", "settle_timestamp": "0", "base_currency": "XRP", "quote_currency": "USDT", "last_price": "2.4347", "volume_24h": "470034630", "turnover_24h": "113519256.602", "index_price": "2.43577184", "index_name": "XRPUSDT", "contract_size": "0.1", "min_leverage": "1", "max_leverage": "50", "price_precision": "0.0001", "vol_precision": "1", "max_volume": "200000000", "min_volume": "1", "funding_rate": "0.0001171", "expected_funding_rate": "0.0001141", "open_interest": "45682598", "open_interest_value": "10681857.35777748", "high_24h": "2.4952", "low_24h": "2.3318", "change_24h": "0.0110040694294494", "funding_time": "1739433600000", "market_max_volume": "10000000", "funding_interval_hours": "8"}, "tierBased": true, "percentage": true, "tiers": {"taker": [[0, 0.002], [10, 0.18], [50, 0.0016], [250, 0.0014], [1000, 0.0012], [5000, 0.001], [25000, 0.0008], [50000, 0.0006]], "maker": [[0, 0.001], [10, 0.0009], [50, 0.0008], [250, 0.0007], [1000, 0.0006], [5000, 0.0005], [25000, 0.0004], [50000, 0.0003]]}, "numericId": null}, "DOGE/USDT:USDT": {"id": "DOGEUSDT", "lowercaseId": null, "symbol": "DOGE/USDT:USDT", "base": "DOGE", "quote": "USDT", "settle": "USDT", "baseId": "DOGE", "quoteId": "USDT", "settleId": "USDT", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": null, "maker": null, "contractSize": 1, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1, "price": 1e-05}, "limits": {"leverage": {"min": 1, "max": 50}, "amount": {"min": 1, "max": 10000000}, "price": {"min": null, "max": null}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": 1646006400000, "info": {"symbol": "DOGEUSDT", "product_type": "1", "open_timestamp": "1646006400000", "expire_timestamp": "0", "settle_timestamp": "0", "base_currency": "DOGE", "quote_currency": "USDT", "last_price": "0.25785", "volume_24h": "667511870", "turnover_24h": "171779225.91118", "index_price": "0.25799", "index_name": "DOGEUSDT", "contract_size": "1", "min_leverage": "1", "max_leverage": "50", "price_precision": "0.00001", "vol_precision": "1", "max_volume": "10000000", "min_volume": "1", "funding_rate": "0.0000964", "expected_funding_rate": "0.0000869", "open_interest": "34998124", "open_interest_value": "9869177.92458534", "high_24h": "0.26719", "low_24h": "0.24649", "change_24h": "0.0176414870944826", "funding_time": "1739433600000", "market_max_volume": "1000000", "funding_interval_hours": "8"}, "tierBased": true, "percentage": true, "tiers": {"taker": [[0, 0.002], [10, 0.18], [50, 0.0016], [250, 0.0014], [1000, 0.0012], [5000, 0.001], [25000, 0.0008], [50000, 0.0006]], "maker": [[0, 0.001], [10, 0.0009], [50, 0.0008], [250, 0.0007], [1000, 0.0006], [5000, 0.0005], [25000, 0.0004], [50000, 0.0003]]}, "numericId": null}, "TRX/USDT:USDT": {"id": "TRXUSDT", "lowercaseId": null, "symbol": "TRX/USDT:USDT", "base": "TRX", "quote": "USDT", "settle": "USDT", "baseId": "TRX", "quoteId": "USDT", "settleId": "USDT", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": null, "maker": null, "contractSize": 1, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1, "price": 1e-07}, "limits": {"leverage": {"min": 1, "max": 50}, "amount": {"min": 1, "max": 50000000}, "price": {"min": null, "max": null}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": 1646006400000, "info": {"symbol": "TRXUSDT", "product_type": "1", "open_timestamp": "1646006400000", "expire_timestamp": "0", "settle_timestamp": "0", "base_currency": "TRX", "quote_currency": "USDT", "last_price": "0.239445", "volume_24h": "107606116", "turnover_24h": "26049488.914788", "index_price": "0.23959818", "index_name": "TRXUSDT", "contract_size": "1", "min_leverage": "1", "max_leverage": "50", "price_precision": "0.0000001", "vol_precision": "1", "max_volume": "50000000", "min_volume": "1", "funding_rate": "-0.0000563", "expected_funding_rate": "-0.0001694", "open_interest": "16699266", "open_interest_value": "4094343.15453689", "high_24h": "0.2457439", "low_24h": "0.2381149", "change_24h": "0.0013800313643492", "funding_time": "1739433600000", "market_max_volume": "1000000", "funding_interval_hours": "8"}, "tierBased": true, "percentage": true, "tiers": {"taker": [[0, 0.002], [10, 0.18], [50, 0.0016], [250, 0.0014], [1000, 0.0012], [5000, 0.001], [25000, 0.0008], [50000, 0.0006]], "maker": [[0, 0.001], [10, 0.0009], [50, 0.0008], [250, 0.0007], [1000, 0.0006], [5000, 0.0005], [25000, 0.0004], [50000, 0.0003]]}, "numericId": null}}