{"BTC/USDT": {"id": "BTC-USDT", "lowercaseId": null, "symbol": "BTC/USDT", "base": "BTC", "quote": "USDT", "settle": null, "baseId": "BTC", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.002, "maker": 0.002, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1e-06, "price": 0.01, "cost": 1e-08}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 1e-06, "max": 10000000000000000}, "price": {"min": 0.01, "max": 10000000000000000}, "cost": {"min": 1, "max": 10000000000000000}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"id": "BTC-USDT", "base_currency_id": "BTC", "quote_currency_id": "USDT", "min_price": "0.01", "max_price": "9999999999999999", "price_increment": "0.01", "min_quantity": "0.000001", "max_quantity": "9999999999999999", "quantity_precision": "6", "min_cost": "1", "max_cost": "9999999999999999", "cost_precision": "8", "maker_fee_rate": "0.2", "taker_fee_rate": "0.2", "show_in_ui": true, "closed": false}, "tierBased": false, "percentage": true}, "LTC/USDT": {"id": "LTC-USDT", "lowercaseId": null, "symbol": "LTC/USDT", "base": "LTC", "quote": "USDT", "settle": null, "baseId": "LTC", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.002, "maker": 0.002, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1e-05, "price": 0.01, "cost": 1e-08}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 1e-05, "max": 10000000000000000}, "price": {"min": 0.01, "max": 10000000000000000}, "cost": {"min": 1, "max": 10000000000000000}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"id": "LTC-USDT", "base_currency_id": "LTC", "quote_currency_id": "USDT", "min_price": "0.01", "max_price": "9999999999999999", "price_increment": "0.01", "min_quantity": "0.00001", "max_quantity": "9999999999999999", "quantity_precision": "5", "min_cost": "1", "max_cost": "9999999999999999", "cost_precision": "8", "maker_fee_rate": "0.2", "taker_fee_rate": "0.2", "show_in_ui": true, "closed": false}, "tierBased": false, "percentage": true}, "XRP/USDT": {"id": "XRP-USDT", "lowercaseId": null, "symbol": "XRP/USDT", "base": "XRP", "quote": "USDT", "settle": null, "baseId": "XRP", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.002, "maker": 0.002, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.1, "price": 0.0001, "cost": 1e-08}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 0.1, "max": 10000000000000000}, "price": {"min": 0.0001, "max": 10000000000000000}, "cost": {"min": 1, "max": 10000000000000000}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"id": "XRP-USDT", "base_currency_id": "XRP", "quote_currency_id": "USDT", "min_price": "0.0001", "max_price": "9999999999999999", "price_increment": "0.0001", "min_quantity": "0.1", "max_quantity": "9999999999999999", "quantity_precision": "1", "min_cost": "1", "max_cost": "9999999999999999", "cost_precision": "8", "maker_fee_rate": "0.2", "taker_fee_rate": "0.2", "show_in_ui": true, "closed": false}, "tierBased": false, "percentage": true}, "SOL/USDT": {"id": "SOL-USDT", "lowercaseId": null, "symbol": "SOL/USDT", "base": "SOL", "quote": "USDT", "settle": null, "baseId": "SOL", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.002, "maker": 0.002, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.0001, "price": 0.01, "cost": 1e-08}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 0.0001, "max": 10000000000000000}, "price": {"min": 0.01, "max": 10000000000000000}, "cost": {"min": 1, "max": 10000000000000000}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"id": "SOL-USDT", "base_currency_id": "SOL", "quote_currency_id": "USDT", "min_price": "0.01", "max_price": "9999999999999999", "price_increment": "0.01", "min_quantity": "0.0001", "max_quantity": "9999999999999999", "quantity_precision": "4", "min_cost": "1", "max_cost": "9999999999999999", "cost_precision": "8", "maker_fee_rate": "0.2", "taker_fee_rate": "0.2", "show_in_ui": true, "closed": false}, "tierBased": false, "percentage": true}, "TRX/USDT": {"id": "TRX-USDT", "lowercaseId": null, "symbol": "TRX/USDT", "base": "TRX", "quote": "USDT", "settle": null, "baseId": "TRX", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.002, "maker": 0.002, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.0001, "price": 0.0001, "cost": 1e-08}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 0.0001, "max": 10000000000000000}, "price": {"min": 0.0001, "max": 10000000000000000}, "cost": {"min": 1, "max": 10000000000000000}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"id": "TRX-USDT", "base_currency_id": "TRX", "quote_currency_id": "USDT", "min_price": "0.0001", "max_price": "9999999999999999", "price_increment": "0.0001", "min_quantity": "0.0001", "max_quantity": "9999999999999999", "quantity_precision": "4", "min_cost": "1", "max_cost": "9999999999999999", "cost_precision": "8", "maker_fee_rate": "0.2", "taker_fee_rate": "0.2", "show_in_ui": true, "closed": false}, "tierBased": false, "percentage": true}, "ETH/USDT": {"id": "ETH-USDT", "lowercaseId": null, "symbol": "ETH/USDT", "base": "ETH", "quote": "USDT", "settle": null, "baseId": "ETH", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.002, "maker": 0.002, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1e-05, "price": 0.01, "cost": 1e-08}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 1e-05, "max": 10000000000000000}, "price": {"min": 0.01, "max": 10000000000000000}, "cost": {"min": 1, "max": 10000000000000000}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"id": "ETH-USDT", "base_currency_id": "ETH", "quote_currency_id": "USDT", "min_price": "0.01", "max_price": "9999999999999999", "price_increment": "0.01", "min_quantity": "0.00001", "max_quantity": "9999999999999999", "quantity_precision": "5", "min_cost": "1", "max_cost": "9999999999999999", "cost_precision": "8", "maker_fee_rate": "0.2", "taker_fee_rate": "0.2", "show_in_ui": true, "closed": false}, "tierBased": false, "percentage": true}}