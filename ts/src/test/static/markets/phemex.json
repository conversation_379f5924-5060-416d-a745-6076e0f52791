{"BTC/USDT": {"id": "sBTCUSDT", "lowercaseId": null, "symbol": "BTC/USDT", "base": "BTC", "quote": "USDT", "settle": null, "baseId": "BTC", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "index": false, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.001, "maker": 0.001, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1e-06, "price": 0.01}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 1e-06, "max": 10}, "price": {"min": 0.01, "max": null}, "cost": {"min": 1, "max": 500000}}, "marginModes": {"cross": null, "isolated": null}, "created": 1589338800000, "info": {"symbol": "sBTCUSDT", "code": "1001", "type": "Spot", "displaySymbol": "BTC / USDT", "quoteCurrency": "USDT", "priceScale": "8", "ratioScale": "8", "pricePrecision": "2", "baseCurrency": "BTC", "baseTickSize": "0.000001 BTC", "baseTickSizeEv": "100", "quoteTickSize": "0.01 USDT", "quoteTickSizeEv": "1000000", "baseQtyPrecision": "6", "quoteQtyPrecision": "2", "minOrderValue": "1 USDT", "minOrderValueEv": "100000000", "maxBaseOrderSize": "10 BTC", "maxBaseOrderSizeEv": "1000000000", "maxOrderValue": "500,000 USDT", "maxOrderValueEv": "50000000000000", "defaultTakerFee": "0.001", "defaultTakerFeeEr": "100000", "defaultMakerFee": "0.001", "defaultMakerFeeEr": "100000", "description": "BTCUSDT is a BTC/USDT spot trading pair. Minimum order value is 1 USDT", "status": "Listed", "tipOrderQty": "2", "listTime": "1589338800000", "buyPriceUpperLimitPct": "110", "sellPriceLowerLimitPct": "90", "leverage": "5", "valueScale": "8"}, "tierBased": false, "percentage": true, "priceScale": 8, "valueScale": 8, "ratioScale": 8}, "ETH/USDT": {"id": "sETHUSDT", "lowercaseId": null, "symbol": "ETH/USDT", "base": "ETH", "quote": "USDT", "settle": null, "baseId": "ETH", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "index": false, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.001, "maker": 0.001, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1e-05, "price": 0.01}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 1e-05, "max": 150}, "price": {"min": 0.01, "max": null}, "cost": {"min": 1, "max": 500000}}, "marginModes": {"cross": null, "isolated": null}, "created": 1589338800000, "info": {"symbol": "sETHUSDT", "code": "1101", "type": "Spot", "displaySymbol": "ETH / USDT", "quoteCurrency": "USDT", "priceScale": "8", "ratioScale": "8", "pricePrecision": "2", "baseCurrency": "ETH", "baseTickSize": "0.00001 ETH", "baseTickSizeEv": "1000", "quoteTickSize": "0.01 USDT", "quoteTickSizeEv": "1000000", "baseQtyPrecision": "5", "quoteQtyPrecision": "2", "minOrderValue": "1 USDT", "minOrderValueEv": "100000000", "maxBaseOrderSize": "150 ETH", "maxBaseOrderSizeEv": "15000000000", "maxOrderValue": "500,000 USDT", "maxOrderValueEv": "50000000000000", "defaultTakerFee": "0.001", "defaultTakerFeeEr": "100000", "defaultMakerFee": "0.001", "defaultMakerFeeEr": "100000", "description": "ETHUSDT is a ETH/USDT spot trading pair. Minimum order value is 1 USDT", "status": "Listed", "tipOrderQty": "30", "listTime": "1589338800000", "buyPriceUpperLimitPct": "110", "sellPriceLowerLimitPct": "90", "leverage": "5", "valueScale": "8"}, "tierBased": false, "percentage": true, "priceScale": 8, "valueScale": 8, "ratioScale": 8}, "ADA/USDT": {"id": "sADAUSDT", "lowercaseId": null, "symbol": "ADA/USDT", "base": "ADA", "quote": "USDT", "settle": null, "baseId": "ADA", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "index": false, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.001, "maker": 0.001, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.1, "price": 0.0001}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 0.1, "max": 500000}, "price": {"min": 0.0001, "max": null}, "cost": {"min": 1, "max": 500000}}, "marginModes": {"cross": null, "isolated": null}, "created": 1595210400000, "info": {"symbol": "sADAUSDT", "code": "1601", "type": "Spot", "displaySymbol": "ADA / USDT", "quoteCurrency": "USDT", "priceScale": "8", "ratioScale": "8", "pricePrecision": "4", "baseCurrency": "ADA", "baseTickSize": "0.1 ADA", "baseTickSizeEv": "10000000", "quoteTickSize": "0.0001 USDT", "quoteTickSizeEv": "10000", "baseQtyPrecision": "1", "quoteQtyPrecision": "2", "minOrderValue": "1 USDT", "minOrderValueEv": "100000000", "maxBaseOrderSize": "500,000 ADA", "maxBaseOrderSizeEv": "50000000000000", "maxOrderValue": "500,000 USDT", "maxOrderValueEv": "50000000000000", "defaultTakerFee": "0.001", "defaultTakerFeeEr": "100000", "defaultMakerFee": "0.001", "defaultMakerFeeEr": "100000", "description": "ADAUSDT is a ADA/USDT spot trading pair. Minimum order value is 1 USDT", "status": "Listed", "tipOrderQty": "100000", "listTime": "1595210400000", "buyPriceUpperLimitPct": "110", "sellPriceLowerLimitPct": "90", "leverage": "5", "valueScale": "8"}, "tierBased": false, "percentage": true, "priceScale": 8, "valueScale": 8, "ratioScale": 8}, "LTC/USDT": {"id": "sLTCUSDT", "lowercaseId": null, "symbol": "LTC/USDT", "base": "LTC", "quote": "USDT", "settle": null, "baseId": "LTC", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "index": false, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.001, "maker": 0.001, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1e-05, "price": 0.01}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 1e-05, "max": 4000}, "price": {"min": 0.01, "max": null}, "cost": {"min": 1, "max": 500000}}, "marginModes": {"cross": null, "isolated": null}, "created": 1596164400000, "info": {"symbol": "sLTCUSDT", "code": "1501", "type": "Spot", "displaySymbol": "LTC / USDT", "quoteCurrency": "USDT", "priceScale": "8", "ratioScale": "8", "pricePrecision": "2", "baseCurrency": "LTC", "baseTickSize": "0.00001 LTC", "baseTickSizeEv": "1000", "quoteTickSize": "0.01 USDT", "quoteTickSizeEv": "1000000", "baseQtyPrecision": "5", "quoteQtyPrecision": "2", "minOrderValue": "1 USDT", "minOrderValueEv": "100000000", "maxBaseOrderSize": "4,000 LTC", "maxBaseOrderSizeEv": "400000000000", "maxOrderValue": "500,000 USDT", "maxOrderValueEv": "50000000000000", "defaultTakerFee": "0.001", "defaultTakerFeeEr": "100000", "defaultMakerFee": "0.001", "defaultMakerFeeEr": "100000", "description": "LTCUSDT is a LTC/USDT spot trading pair. Minimum order value is 1 USDT", "status": "Listed", "tipOrderQty": "300", "listTime": "1596164400000", "buyPriceUpperLimitPct": "110", "sellPriceLowerLimitPct": "90", "leverage": "5", "valueScale": "8"}, "tierBased": false, "percentage": true, "priceScale": 8, "valueScale": 8, "ratioScale": 8}, "XRP/USDT": {"id": "sXRPUSDT", "lowercaseId": null, "symbol": "XRP/USDT", "base": "XRP", "quote": "USDT", "settle": null, "baseId": "XRP", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "index": false, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.001, "maker": 0.001, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.1, "price": 0.0001}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 0.1, "max": 500000}, "price": {"min": 0.0001, "max": null}, "cost": {"min": 1, "max": 500000}}, "marginModes": {"cross": null, "isolated": null}, "created": 1589338800000, "info": {"symbol": "sXRPUSDT", "code": "1201", "type": "Spot", "displaySymbol": "XRP / USDT", "quoteCurrency": "USDT", "priceScale": "8", "ratioScale": "8", "pricePrecision": "4", "baseCurrency": "XRP", "baseTickSize": "0.1 XRP", "baseTickSizeEv": "10000000", "quoteTickSize": "0.0001 USDT", "quoteTickSizeEv": "10000", "baseQtyPrecision": "1", "quoteQtyPrecision": "2", "minOrderValue": "1 USDT", "minOrderValueEv": "100000000", "maxBaseOrderSize": "500,000 XRP", "maxBaseOrderSizeEv": "50000000000000", "maxOrderValue": "500,000 USDT", "maxOrderValueEv": "50000000000000", "defaultTakerFee": "0.001", "defaultTakerFeeEr": "100000", "defaultMakerFee": "0.001", "defaultMakerFeeEr": "100000", "description": "XRPUSDT is a XRP/USDT spot trading pair. Minimum order value is 1 USDT", "status": "Listed", "tipOrderQty": "100000", "listTime": "1589338800000", "buyPriceUpperLimitPct": "110", "sellPriceLowerLimitPct": "90", "leverage": "5", "valueScale": "8"}, "tierBased": false, "percentage": true, "priceScale": 8, "valueScale": 8, "ratioScale": 8}, "DOGE/USDT": {"id": "sDOGEUSDT", "lowercaseId": null, "symbol": "DOGE/USDT", "base": "DOGE", "quote": "USDT", "settle": null, "baseId": "DOGE", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "index": false, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.001, "maker": 0.001, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1, "price": 1e-05}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 1, "max": 4000000}, "price": {"min": 1e-05, "max": null}, "cost": {"min": 1, "max": 500000}}, "marginModes": {"cross": null, "isolated": null}, "created": 1615604400000, "info": {"symbol": "sDOGEUSDT", "code": "2901", "type": "Spot", "displaySymbol": "DOGE / USDT", "quoteCurrency": "USDT", "priceScale": "8", "ratioScale": "8", "pricePrecision": "5", "baseCurrency": "DOGE", "baseTickSize": "1 DOGE", "baseTickSizeEv": "100000000", "quoteTickSize": "0.00001 USDT", "quoteTickSizeEv": "1000", "baseQtyPrecision": "0", "quoteQtyPrecision": "2", "minOrderValue": "1 USDT", "minOrderValueEv": "100000000", "maxBaseOrderSize": "4,000,000 DOGE", "maxBaseOrderSizeEv": "400000000000000", "maxOrderValue": "500,000 USDT", "maxOrderValueEv": "50000000000000", "defaultTakerFee": "0.001", "defaultTakerFeeEr": "100000", "defaultMakerFee": "0.001", "defaultMakerFeeEr": "100000", "description": "DOGEUSDT is a DOGE/USDT spot trading pair. Minimum order value is 1 USDT", "status": "Listed", "tipOrderQty": "200000", "listTime": "1615604400000", "buyPriceUpperLimitPct": "110", "sellPriceLowerLimitPct": "90", "leverage": "5", "valueScale": "8"}, "tierBased": false, "percentage": true, "priceScale": 8, "valueScale": 8, "ratioScale": 8}, "TRX/USDT": {"id": "sTRXUSDT", "lowercaseId": null, "symbol": "TRX/USDT", "base": "TRX", "quote": "USDT", "settle": null, "baseId": "TRX", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "index": false, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.001, "maker": 0.001, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.1, "price": 0.0001}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 0.1, "max": 7500000}, "price": {"min": 0.0001, "max": null}, "cost": {"min": 1, "max": 500000}}, "marginModes": {"cross": null, "isolated": null}, "created": 1594000800000, "info": {"symbol": "sTRXUSDT", "code": "1701", "type": "Spot", "displaySymbol": "TRX / USDT", "quoteCurrency": "USDT", "priceScale": "8", "ratioScale": "8", "pricePrecision": "4", "baseCurrency": "TRX", "baseTickSize": "0.1 TRX", "baseTickSizeEv": "10000000", "quoteTickSize": "0.0001 USDT", "quoteTickSizeEv": "10000", "baseQtyPrecision": "1", "quoteQtyPrecision": "2", "minOrderValue": "1 USDT", "minOrderValueEv": "100000000", "maxBaseOrderSize": "7,500,000 TRX", "maxBaseOrderSizeEv": "750000000000000", "maxOrderValue": "500,000 USDT", "maxOrderValueEv": "50000000000000", "defaultTakerFee": "0.001", "defaultTakerFeeEr": "100000", "defaultMakerFee": "0.001", "defaultMakerFeeEr": "100000", "description": "TRXUSDT is a TRX/USDT spot trading pair. Minimum order value is 1 USDT", "status": "Listed", "tipOrderQty": "1875000", "listTime": "1594000800000", "buyPriceUpperLimitPct": "110", "sellPriceLowerLimitPct": "90", "leverage": "5", "valueScale": "8"}, "tierBased": false, "percentage": true, "priceScale": 8, "valueScale": 8, "ratioScale": 8}, "BTC/USDT:USDT": {"id": "BTCUSDT", "lowercaseId": null, "symbol": "BTC/USDT:USDT", "base": "BTC", "quote": "USDT", "settle": "USDT", "baseId": "BTC", "quoteId": "USDT", "settleId": "USDT", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": null, "maker": null, "contractSize": 1, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.001, "price": 0.1}, "limits": {"leverage": {"min": 1, "max": 100}, "amount": {"min": null, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"perpProductSubType": "Normal", "symbol": "BTCUSDT", "code": "41541", "type": "PerpetualV2", "displaySymbol": "BTC / USDT", "indexSymbol": ".BTCUSDT", "markSymbol": ".MBTCUSDT", "fundingRateSymbol": ".BTCUSDTFR", "fundingRate8hSymbol": ".BTCUSDTFR8H", "contractUnderlyingAssets": "BTC", "settleCurrency": "USDT", "quoteCurrency": "USDT", "tickSize": "0.1", "priceScale": "0", "ratioScale": "0", "pricePrecision": "1", "baseCurrency": "BTC", "description": "BTC/USDT perpetual contracts are priced on the .BTCUSDT Index. Each contract is worth 1 BTC. Funding fees are paid and received every 8 hours at UTC time: 00:00, 08:00 and 16:00.", "status": "Listed", "tipOrderQty": "0", "listTime": "1668225600000", "majorSymbol": true, "defaultLeverage": "-10", "fundingInterval": "28800", "maxLeverage": "100", "leverageMargin": "1002", "maxOI": "-1.0", "maxOrderQtyRq": "1000", "maxPriceRp": "500000", "minOrderValueRv": "1", "minPriceRp": "1000.0", "qtyPrecision": "3", "qtyStepSize": "0.001", "tipOrderQtyRq": "200", "maxOpenPosLeverage": "100.0", "steps": "0K", "riskLimits": [{"limit": "2000000", "initialMarginRr": "0.01", "maintenanceMarginRr": "0.005"}]}, "tierBased": false, "percentage": true, "priceScale": 0, "valueScale": null, "ratioScale": 0}, "BTC/USD:USD": {"id": "uBTCUSD", "lowercaseId": null, "symbol": "BTC/USD:USD", "base": "BTC", "quote": "USD", "settle": "USD", "baseId": "BTC", "quoteId": "USD", "settleId": "USD", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "index": null, "active": false, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": null, "maker": null, "contractSize": 0.001, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1, "price": 0.1}, "limits": {"leverage": {"min": 1, "max": 100}, "amount": {"min": null, "max": null}, "price": {"min": 10, "max": 1000000}, "cost": {"min": null, "max": 100000}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"symbol": "uBTCUSD", "code": "1021", "type": "Perpetual", "displaySymbol": "BTC / USD", "indexSymbol": ".uBTC", "markSymbol": ".MuBTC", "fundingRateSymbol": ".uBTCFR", "fundingRate8hSymbol": ".uBTCFR8H", "contractUnderlyingAssets": "BTC", "settleCurrency": "USD", "quoteCurrency": "USD", "contractSize": "0.001", "lotSize": "1", "tickSize": "0.1", "priceScale": "4", "ratioScale": "8", "pricePrecision": "1", "minPriceEp": "100000", "maxPriceEp": "10000000000", "maxOrderQty": "100000", "description": "uBTC/USD perpetual contracts are priced on the .uBTC Index. Each contract is worth 0.001 BTC. Funding fees are paid and received every 8 hours at UTC time: 00:00, 08:00 and 16:00.", "status": "Delisted", "tipOrderQty": "20000", "listTime": "1633748400000", "majorSymbol": false, "defaultLeverage": "-10", "fundingInterval": "28800", "maxLeverage": "100", "leverageMargin": "1001", "maxOI": "-1.0", "steps": "0K", "riskLimits": [{"limit": "500000", "initialMargin": "5.0%", "initialMarginEr": "5000000", "maintenanceMargin": "1.0%", "maintenanceMarginEr": "1000000"}]}, "tierBased": false, "percentage": true, "priceScale": 4, "valueScale": null, "ratioScale": 8}, "BTC/USD:BTC": {"id": "BTCUSD", "lowercaseId": null, "symbol": "BTC/USD:BTC", "base": "BTC", "quote": "USD", "settle": "BTC", "baseId": "BTC", "quoteId": "USD", "settleId": "BTC", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": false, "inverse": true, "subType": "inverse", "taker": 0.0075, "maker": -0.0025, "contractSize": 1, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1, "price": 0.5}, "limits": {"leverage": {"min": 1, "max": 100}, "amount": {"min": null, "max": null}, "price": {"min": 10, "max": 10000000000}, "cost": {"min": null, "max": 1000000}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"symbol": "BTCUSD", "code": "1", "type": "Perpetual", "displaySymbol": "BTC / USD", "indexSymbol": ".BTC", "markSymbol": ".MBTC", "fundingRateSymbol": ".BTCFR", "fundingRate8hSymbol": ".BTCFR8H", "contractUnderlyingAssets": "USD", "settleCurrency": "BTC", "quoteCurrency": "USD", "contractSize": "1 USD", "lotSize": "1", "tickSize": "0.5", "priceScale": "4", "ratioScale": "8", "pricePrecision": "1", "minPriceEp": "100000", "maxPriceEp": "100000000000000", "maxOrderQty": "1000000", "description": "BTCUSD is a BTC/USD perpetual contract priced on the .BTC Index. Each contract is worth 1 USD of Bitcoin. Funding is paid and received every 8 hours. At UTC time: 00:00, 08:00, 16:00.", "status": "Listed", "tipOrderQty": "5000000", "listTime": "1574650800000", "majorSymbol": true, "defaultLeverage": "0", "fundingInterval": "8", "maxLeverage": "100", "leverageMargin": "1034", "maxOI": "-1.0", "steps": "0", "riskLimits": [{"limit": "1500", "initialMargin": "10.0%", "initialMarginEr": "10000000", "maintenanceMargin": "5.0%", "maintenanceMarginEr": "5000000"}], "underlyingSymbol": ".BTC", "baseCurrency": "BTC", "settlementCurrency": "BTC", "valueScale": "8", "initMarginEr": "1000000", "maintMarginEr": "500000", "defaultRiskLimitEv": "10000000000", "deleverage": true, "makerFeeRateEr": "-250000", "takerFeeRateEr": "750000", "marketUrl": "https://phemex.com/trade/BTCUSD"}, "tierBased": false, "percentage": true, "priceScale": 4, "valueScale": 8, "ratioScale": 8}, "ETH/USDT:USDT": {"id": "ETHUSDT", "lowercaseId": null, "symbol": "ETH/USDT:USDT", "base": "ETH", "quote": "USDT", "settle": "USDT", "baseId": "ETH", "quoteId": "USDT", "settleId": "USDT", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": null, "maker": null, "contractSize": 1, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.01, "price": 0.01}, "limits": {"leverage": {"min": 1, "max": 100}, "amount": {"min": null, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"perpProductSubType": "Normal", "symbol": "ETHUSDT", "code": "41641", "type": "PerpetualV2", "displaySymbol": "ETH / USDT", "indexSymbol": ".ETHUSDT", "markSymbol": ".METHUSDT", "fundingRateSymbol": ".ETHUSDTFR", "fundingRate8hSymbol": ".ETHUSDTFR8H", "contractUnderlyingAssets": "ETH", "settleCurrency": "USDT", "quoteCurrency": "USDT", "tickSize": "0.01", "priceScale": "0", "ratioScale": "0", "pricePrecision": "2", "baseCurrency": "ETH", "description": "ETH/USDT perpetual contracts are priced on the .ETHUSDT Index. Each contract is worth 1 ETH. Funding fees are paid and received every 8 hours at UTC time: 00:00, 08:00 and 16:00.", "status": "Listed", "tipOrderQty": "0", "listTime": "1668225600000", "majorSymbol": false, "defaultLeverage": "-10", "fundingInterval": "28800", "maxLeverage": "100", "leverageMargin": "1007", "maxOI": "-1.0", "maxOrderQtyRq": "10000", "maxPriceRp": "500000", "minOrderValueRv": "1", "minPriceRp": "100.0", "qtyPrecision": "2", "qtyStepSize": "0.01", "tipOrderQtyRq": "2000", "maxOpenPosLeverage": "100.0", "steps": "0K", "riskLimits": [{"limit": "10000000", "initialMarginRr": "0.01", "maintenanceMarginRr": "0.005"}]}, "tierBased": false, "percentage": true, "priceScale": 0, "valueScale": null, "ratioScale": 0}, "ETH/USD:USD": {"id": "ETHUSD", "lowercaseId": null, "symbol": "ETH/USD:USD", "base": "ETH", "quote": "USD", "settle": "USD", "baseId": "ETH", "quoteId": "USD", "settleId": "USD", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "index": null, "active": false, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": 0.0075, "maker": -0.0025, "contractSize": 0.005, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1, "price": 0.05}, "limits": {"leverage": {"min": 1, "max": 20}, "amount": {"min": null, "max": null}, "price": {"min": 1, "max": 20000}, "cost": {"min": null, "max": 500000}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"symbol": "ETHUSD", "code": "11", "type": "Perpetual", "displaySymbol": "ETH / USD", "indexSymbol": ".ETH", "markSymbol": ".METH", "fundingRateSymbol": ".ETHFR", "fundingRate8hSymbol": ".ETHFR8H", "contractUnderlyingAssets": "ETH", "settleCurrency": "USD", "quoteCurrency": "USD", "contractSize": "0.005 ETH", "lotSize": "1", "tickSize": "0.05", "priceScale": "4", "ratioScale": "8", "pricePrecision": "2", "minPriceEp": "10000", "maxPriceEp": "200000000", "maxOrderQty": "500000", "description": "ETHUSD is a ETH/USD perpetual contract priced on the .ETH Index. Each contract is worth 0.005 ETH of USD. Funding is paid and received every 8 hours. At UTC time: 00:00, 08:00, 16:00.", "status": "Delisted", "tipOrderQty": "100000", "listTime": "1574650800000", "majorSymbol": true, "defaultLeverage": "0", "fundingInterval": "8", "maxLeverage": "20", "leverageMargin": "1001", "maxOI": "-1.0", "steps": "0K", "riskLimits": [{"limit": "500000", "initialMargin": "5.0%", "initialMarginEr": "5000000", "maintenanceMargin": "1.0%", "maintenanceMarginEr": "1000000"}], "underlyingSymbol": ".ETH", "baseCurrency": "ETH", "settlementCurrency": "USD", "valueScale": "4", "initMarginEr": "5000000", "maintMarginEr": "1000000", "defaultRiskLimitEv": "5000000000", "deleverage": true, "makerFeeRateEr": "-250000", "takerFeeRateEr": "750000", "marketUrl": "https://phemex.com/trade/ETHUSD"}, "tierBased": false, "percentage": true, "priceScale": 4, "valueScale": 4, "ratioScale": 8}, "LTC/USDT:USDT": {"id": "LTCUSDT", "lowercaseId": null, "symbol": "LTC/USDT:USDT", "base": "LTC", "quote": "USDT", "settle": "USDT", "baseId": "LTC", "quoteId": "USDT", "settleId": "USDT", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": null, "maker": null, "contractSize": 1, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.01, "price": 0.01}, "limits": {"leverage": {"min": 1, "max": 50}, "amount": {"min": null, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"perpProductSubType": "Normal", "symbol": "LTCUSDT", "code": "44841", "type": "PerpetualV2", "displaySymbol": "LTC / USDT", "indexSymbol": ".LTCUSDT", "markSymbol": ".MLTCUSDT", "fundingRateSymbol": ".LTCUSDTFR", "fundingRate8hSymbol": ".LTCUSDTFR8H", "contractUnderlyingAssets": "LTC", "settleCurrency": "USDT", "quoteCurrency": "USDT", "tickSize": "0.01", "priceScale": "0", "ratioScale": "0", "pricePrecision": "2", "baseCurrency": "LTC", "description": "LTC/USDT perpetual contracts are priced on the .LTCUSDT Index. Each contract is worth 1 LTC. Funding fees are paid and received every 8 hours at UTC time: 00:00, 08:00 and 16:00.", "status": "Listed", "tipOrderQty": "0", "listTime": "1669341600000", "majorSymbol": false, "defaultLeverage": "-10", "fundingInterval": "28800", "maxLeverage": "50", "leverageMargin": "1005", "maxOI": "-1.0", "maxOrderQtyRq": "7000", "maxPriceRp": "500000", "minOrderValueRv": "1", "minPriceRp": "100.0", "qtyPrecision": "2", "qtyStepSize": "0.01", "tipOrderQtyRq": "1400", "maxOpenPosLeverage": "50.0", "steps": "0K", "riskLimits": [{"limit": "5600000", "initialMarginRr": "0.02", "maintenanceMarginRr": "0.01"}]}, "tierBased": false, "percentage": true, "priceScale": 0, "valueScale": null, "ratioScale": 0}, "ADA/USDT:USDT": {"id": "ADAUSDT", "lowercaseId": null, "symbol": "ADA/USDT:USDT", "base": "ADA", "quote": "USDT", "settle": "USDT", "baseId": "ADA", "quoteId": "USDT", "settleId": "USDT", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": null, "maker": null, "contractSize": 1, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.01, "price": 0.0001}, "limits": {"leverage": {"min": 1, "max": 50}, "amount": {"min": null, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"perpProductSubType": "Normal", "symbol": "ADAUSDT", "code": "41841", "type": "PerpetualV2", "displaySymbol": "ADA / USDT", "indexSymbol": ".ADAUSDT", "markSymbol": ".MADAUSDT", "fundingRateSymbol": ".ADAUSDTFR", "fundingRate8hSymbol": ".ADAUSDTFR8H", "contractUnderlyingAssets": "ADA", "settleCurrency": "USDT", "quoteCurrency": "USDT", "tickSize": "0.0001", "priceScale": "0", "ratioScale": "0", "pricePrecision": "4", "baseCurrency": "ADA", "description": "ADA/USDT perpetual contracts are priced on the .ADAUSDT Index. Each contract is worth 1 ADA. Funding fees are paid and received every 8 hours at UTC time: 00:00, 08:00 and 16:00.", "status": "Listed", "tipOrderQty": "0", "listTime": "1668225600000", "majorSymbol": false, "defaultLeverage": "-10", "fundingInterval": "28800", "maxLeverage": "50", "leverageMargin": "1005", "maxOI": "-1.0", "maxOrderQtyRq": "2000000", "maxPriceRp": "500000", "minOrderValueRv": "1", "minPriceRp": "1.0", "qtyPrecision": "2", "qtyStepSize": "0.01", "tipOrderQtyRq": "400000", "maxOpenPosLeverage": "50.0", "steps": "0K", "riskLimits": [{"limit": "5600000", "initialMarginRr": "0.02", "maintenanceMarginRr": "0.01"}]}, "tierBased": false, "percentage": true, "priceScale": 0, "valueScale": null, "ratioScale": 0}, "XRP/USDT:USDT": {"id": "XRPUSDT", "lowercaseId": null, "symbol": "XRP/USDT:USDT", "base": "XRP", "quote": "USDT", "settle": "USDT", "baseId": "XRP", "quoteId": "USDT", "settleId": "USDT", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": null, "maker": null, "contractSize": 1, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.01, "price": 0.0001}, "limits": {"leverage": {"min": 1, "max": 50}, "amount": {"min": null, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"perpProductSubType": "Normal", "symbol": "XRPUSDT", "code": "41741", "type": "PerpetualV2", "displaySymbol": "XRP / USDT", "indexSymbol": ".XRPUSDT", "markSymbol": ".MXRPUSDT", "fundingRateSymbol": ".XRPUSDTFR", "fundingRate8hSymbol": ".XRPUSDTFR8H", "contractUnderlyingAssets": "XRP", "settleCurrency": "USDT", "quoteCurrency": "USDT", "tickSize": "0.0001", "priceScale": "0", "ratioScale": "0", "pricePrecision": "4", "baseCurrency": "XRP", "description": "XRP/USDT perpetual contracts are priced on the .XRPUSDT Index. Each contract is worth 1 XRP. Funding fees are paid and received every 8 hours at UTC time: 00:00, 08:00 and 16:00.", "status": "Listed", "tipOrderQty": "0", "listTime": "1668225600000", "majorSymbol": false, "defaultLeverage": "-10", "fundingInterval": "28800", "maxLeverage": "50", "leverageMargin": "1005", "maxOI": "-1.0", "maxOrderQtyRq": "1200000", "maxPriceRp": "500000", "minOrderValueRv": "1", "minPriceRp": "1.0", "qtyPrecision": "2", "qtyStepSize": "0.01", "tipOrderQtyRq": "240000", "maxOpenPosLeverage": "50.0", "steps": "0K", "riskLimits": [{"limit": "5600000", "initialMarginRr": "0.02", "maintenanceMarginRr": "0.01"}]}, "tierBased": false, "percentage": true, "priceScale": 0, "valueScale": null, "ratioScale": 0}, "DOGE/USDT:USDT": {"id": "DOGEUSDT", "lowercaseId": null, "symbol": "DOGE/USDT:USDT", "base": "DOGE", "quote": "USDT", "settle": "USDT", "baseId": "DOGE", "quoteId": "USDT", "settleId": "USDT", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": null, "maker": null, "contractSize": 1, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1, "price": 1e-05}, "limits": {"leverage": {"min": 1, "max": 50}, "amount": {"min": null, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"perpProductSubType": "Normal", "symbol": "DOGEUSDT", "code": "44941", "type": "PerpetualV2", "displaySymbol": "DOGE / USDT", "indexSymbol": ".DOGEUSDT", "markSymbol": ".MDOGEUSDT", "fundingRateSymbol": ".DOGEUSDTFR", "fundingRate8hSymbol": ".DOGEUSDTFR8H", "contractUnderlyingAssets": "DOGE", "settleCurrency": "USDT", "quoteCurrency": "USDT", "tickSize": "0.00001", "priceScale": "0", "ratioScale": "0", "pricePrecision": "5", "baseCurrency": "DOGE", "description": "DOGE/USDT perpetual contracts are priced on the .DOGEUSDT Index. Each contract is worth 1 DOGE. Funding fees are paid and received every 8 hours at UTC time: 00:00, 08:00 and 16:00.", "status": "Listed", "tipOrderQty": "0", "listTime": "1669341600000", "majorSymbol": false, "defaultLeverage": "-10", "fundingInterval": "28800", "maxLeverage": "50", "leverageMargin": "1005", "maxOI": "-1.0", "maxOrderQtyRq": "8000000", "maxPriceRp": "200000", "minOrderValueRv": "1", "minPriceRp": "1.0", "qtyPrecision": "0", "qtyStepSize": "1.0", "tipOrderQtyRq": "1600000", "maxOpenPosLeverage": "50.0", "steps": "0K", "riskLimits": [{"limit": "5600000", "initialMarginRr": "0.02", "maintenanceMarginRr": "0.01"}]}, "tierBased": false, "percentage": true, "priceScale": 0, "valueScale": null, "ratioScale": 0}, "TRX/USDT:USDT": {"id": "TRXUSDT", "lowercaseId": null, "symbol": "TRX/USDT:USDT", "base": "TRX", "quote": "USDT", "settle": "USDT", "baseId": "TRX", "quoteId": "USDT", "settleId": "USDT", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": null, "maker": null, "contractSize": 1, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1, "price": 1e-05}, "limits": {"leverage": {"min": 1, "max": 50}, "amount": {"min": null, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"perpProductSubType": "Normal", "symbol": "TRXUSDT", "code": "47841", "type": "PerpetualV2", "displaySymbol": "TRX / USDT", "indexSymbol": ".TRXUSDT", "markSymbol": ".MTRXUSDT", "fundingRateSymbol": ".TRXUSDTFR", "fundingRate8hSymbol": ".TRXUSDTFR8H", "contractUnderlyingAssets": "TRX", "settleCurrency": "USDT", "quoteCurrency": "USDT", "tickSize": "0.00001", "priceScale": "0", "ratioScale": "0", "pricePrecision": "5", "baseCurrency": "TRX", "description": "TRX/USDT perpetual contracts are priced on the .TRXUSDT Index. Each contract is worth 1 TRX. Funding fees are paid and received every 8 hours at UTC time: 00:00, 08:00 and 16:00.", "status": "Listed", "tipOrderQty": "0", "listTime": "1671537600000", "majorSymbol": false, "defaultLeverage": "-10", "fundingInterval": "28800", "maxLeverage": "50", "leverageMargin": "1005", "maxOI": "-1.0", "maxOrderQtyRq": "8000000", "maxPriceRp": "200000", "minOrderValueRv": "1", "minPriceRp": "0.1", "qtyPrecision": "0", "qtyStepSize": "1.0", "tipOrderQtyRq": "1600000", "maxOpenPosLeverage": "50.0", "steps": "0K", "riskLimits": [{"limit": "5600000", "initialMarginRr": "0.02", "maintenanceMarginRr": "0.01"}]}, "tierBased": false, "percentage": true, "priceScale": 0, "valueScale": null, "ratioScale": 0}, "SOL/USDT:USDT": {"id": "SOLUSDT", "lowercaseId": null, "symbol": "SOL/USDT:USDT", "base": "SOL", "quote": "USDT", "settle": "USDT", "baseId": "SOL", "quoteId": "USDT", "settleId": "USDT", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": null, "maker": null, "contractSize": 1, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.01, "price": 0.01}, "limits": {"leverage": {"min": 1, "max": 50}, "amount": {"min": null, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"perpProductSubType": "Normal", "symbol": "SOLUSDT", "code": "41941", "type": "PerpetualV2", "displaySymbol": "SOL / USDT", "indexSymbol": ".SOLUSDT", "markSymbol": ".MSOLUSDT", "fundingRateSymbol": ".SOLUSDTFR", "fundingRate8hSymbol": ".SOLUSDTFR8H", "contractUnderlyingAssets": "SOL", "settleCurrency": "USDT", "quoteCurrency": "USDT", "tickSize": "0.01", "priceScale": "0", "ratioScale": "0", "pricePrecision": "2", "baseCurrency": "SOL", "description": "SOL/USDT perpetual contracts are priced on the .SOLUSDT Index. Each contract is worth 1 SOL. Funding fees are paid and received every 8 hours at UTC time: 00:00, 08:00 and 16:00.", "status": "Listed", "tipOrderQty": "0", "listTime": "1670839200000", "majorSymbol": false, "defaultLeverage": "-10", "fundingInterval": "28800", "maxLeverage": "50", "leverageMargin": "1005", "maxOI": "-1.0", "maxOrderQtyRq": "35000", "maxPriceRp": "500000", "minOrderValueRv": "1", "minPriceRp": "100.0", "qtyPrecision": "2", "qtyStepSize": "0.01", "tipOrderQtyRq": "7000", "maxOpenPosLeverage": "50.0", "steps": "0K", "riskLimits": [{"limit": "5600000", "initialMarginRr": "0.02", "maintenanceMarginRr": "0.01"}]}, "tierBased": false, "percentage": true, "priceScale": 0, "valueScale": null, "ratioScale": 0}, "ETH/USDC:USDC": {"id": "ETHUSDC", "lowercaseId": null, "symbol": "ETH/USDC:USDC", "base": "ETH", "quote": "USDC", "settle": "USDC", "baseId": "ETH", "quoteId": "USDC", "settleId": "USDC", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": null, "maker": null, "contractSize": 0, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.01, "price": 0.01}, "limits": {"leverage": {"min": 1, "max": 100}, "amount": {"min": null, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"perpProductSubType": "Normal", "symbol": "ETHUSDC", "code": "40141", "type": "PerpetualV2", "displaySymbol": "ETH / USDC", "indexSymbol": ".ETHUSDC", "markSymbol": ".METHUSDC", "fundingRateSymbol": ".ETHUSDCFR", "fundingRate8hSymbol": ".ETHUSDCFR8H", "contractUnderlyingAssets": "ETH", "settleCurrency": "USDC", "quoteCurrency": "USDC", "tickSize": "0.01", "priceScale": "0", "ratioScale": "0", "pricePrecision": "2", "baseCurrency": "ETH", "description": "ETH/USDC perpetual contracts are priced on the .ETHUSDC Index. Each contract is worth 1 ETH. Funding fees are paid and received every 8 hours at UTC time: 00:00, 08:00 and 16:00.", "status": "Listed", "tipOrderQty": "0", "listTime": "1747735200000", "majorSymbol": false, "defaultLeverage": "-10", "fundingInterval": "28800", "maxLeverage": "100", "leverageMargin": "1036", "maxOI": "-1.0", "maxOrderQtyRq": "10000", "maxPriceRp": "500000", "minOrderValueRv": "1", "minPriceRp": "100", "qtyPrecision": "2", "qtyStepSize": "0.01", "tipOrderQtyRq": "2000", "maxOpenPosLeverage": "100.0", "steps": "0K", "riskLimits": [{"limit": "46000000000", "initialMarginRr": "0.01", "maintenanceMarginRr": "0.005"}]}, "tierBased": false, "percentage": true, "priceScale": 0, "valueScale": null, "ratioScale": 0}}