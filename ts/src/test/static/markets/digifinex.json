{"BTC/USDT": {"id": "BTC_USDT", "lowercaseId": null, "symbol": "BTC/USDT", "base": "BTC", "quote": "USDT", "settle": null, "baseId": "BTC", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": null, "swap": false, "future": false, "option": false, "index": null, "active": false, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.002, "maker": 0.002, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1e-07, "price": 0.01}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 1e-07, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": 2, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"order_types": ["LIMIT", "MARKET"], "quote_asset": "USDT", "minimum_value": "2", "amount_precision": "7", "status": "TRADING", "minimum_amount": "1e-07", "symbol": "BTC_USDT", "is_allow": "0", "zone": "MAIN", "base_asset": "BTC", "price_precision": "2"}, "tierBased": true, "percentage": true}, "BTC/USDT:USDT": {"id": "BTCUSDTPERP", "lowercaseId": null, "symbol": "BTC/USDT:USDT", "base": "BTC", "quote": "USDT", "settle": "USDT", "baseId": "BTC", "quoteId": "USDT", "settleId": "USDT", "type": "swap", "spot": false, "margin": null, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": 0.002, "maker": 0.002, "contractSize": 0.001, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": null, "price": 0.1}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 1, "max": null}, "price": {"min": 0.1, "max": null}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"instrument_id": "BTCUSDTPERP", "type": "REAL", "contract_type": "PERPETUAL", "base_currency": "BTC", "quote_currency": "USDT", "clear_currency": "USDT", "contract_value": "0.001", "contract_value_currency": "BTC", "is_inverse": false, "is_trading": true, "status": "ONLINE", "price_precision": "1", "tick_size": "0.1", "min_order_amount": "1", "open_max_limits": [{"leverage": "100", "maint_margin_ratio": "0.005", "max_limit": "3000000"}, {"leverage": "50", "maint_margin_ratio": "0.01", "max_limit": "10000000"}, {"leverage": "20", "maint_margin_ratio": "0.025", "max_limit": "15000000"}, {"leverage": "10", "maint_margin_ratio": "0.05", "max_limit": "20000000"}]}, "tierBased": true, "percentage": true}, "LTC/USDT": {"id": "LTC_USDT", "lowercaseId": null, "symbol": "LTC/USDT", "base": "LTC", "quote": "USDT", "settle": null, "baseId": "LTC", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": null, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.002, "maker": 0.002, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.001, "price": 0.01}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 0.01, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": 2, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"order_types": ["LIMIT", "MARKET"], "quote_asset": "USDT", "minimum_value": "2", "amount_precision": "3", "status": "TRADING", "minimum_amount": "0.01", "symbol": "LTC_USDT", "is_allow": "1", "zone": "MAIN", "base_asset": "LTC", "price_precision": "2"}, "tierBased": true, "percentage": true}, "ADA/USDT": {"id": "ADA_USDT", "lowercaseId": null, "symbol": "ADA/USDT", "base": "ADA", "quote": "USDT", "settle": null, "baseId": "ADA", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": null, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.002, "maker": 0.002, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.1, "price": 1e-05}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 1, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": 2, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"order_types": ["LIMIT", "MARKET"], "quote_asset": "USDT", "minimum_value": "2", "amount_precision": "1", "status": "TRADING", "minimum_amount": "1", "symbol": "ADA_USDT", "is_allow": "1", "zone": "MAIN", "base_asset": "ADA", "price_precision": "5"}, "tierBased": true, "percentage": true}, "XRP/USDT": {"id": "XRP_USDT", "lowercaseId": null, "symbol": "XRP/USDT", "base": "XRP", "quote": "USDT", "settle": null, "baseId": "XRP", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": null, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.002, "maker": 0.002, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1, "price": 1e-05}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 2, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": 2, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"order_types": ["LIMIT", "MARKET"], "quote_asset": "USDT", "minimum_value": "2", "amount_precision": "0", "status": "TRADING", "minimum_amount": "2", "symbol": "XRP_USDT", "is_allow": "1", "zone": "MAIN", "base_asset": "XRP", "price_precision": "5"}, "tierBased": true, "percentage": true}, "LTC/USDT:USDT": {"id": "LTCUSDTPERP", "lowercaseId": null, "symbol": "LTC/USDT:USDT", "base": "LTC", "quote": "USDT", "settle": "USDT", "baseId": "LTC", "quoteId": "USDT", "settleId": "USDT", "type": "swap", "spot": false, "margin": null, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": 0.002, "maker": 0.002, "contractSize": 0.1, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": null, "price": 0.01}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 1, "max": null}, "price": {"min": 0.01, "max": null}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"instrument_id": "LTCUSDTPERP", "type": "REAL", "contract_type": "PERPETUAL", "base_currency": "LTC", "quote_currency": "USDT", "clear_currency": "USDT", "contract_value": "0.1", "contract_value_currency": "LTC", "is_inverse": false, "is_trading": true, "status": "ONLINE", "price_precision": "2", "tick_size": "0.01", "min_order_amount": "1", "open_max_limits": [{"leverage": "100", "maint_margin_ratio": "0.005", "max_limit": "500000"}, {"leverage": "50", "maint_margin_ratio": "0.01", "max_limit": "1000000"}, {"leverage": "20", "maint_margin_ratio": "0.025", "max_limit": "2000000"}, {"leverage": "10", "maint_margin_ratio": "0.05", "max_limit": "5000000"}]}, "tierBased": true, "percentage": true}, "ADA/USDT:USDT": {"id": "ADAUSDTPERP", "lowercaseId": null, "symbol": "ADA/USDT:USDT", "base": "ADA", "quote": "USDT", "settle": "USDT", "baseId": "ADA", "quoteId": "USDT", "settleId": "USDT", "type": "swap", "spot": false, "margin": null, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": 0.002, "maker": 0.002, "contractSize": 100, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": null, "price": 0.0001}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 1, "max": null}, "price": {"min": 0.0001, "max": null}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"instrument_id": "ADAUSDTPERP", "type": "REAL", "contract_type": "PERPETUAL", "base_currency": "ADA", "quote_currency": "USDT", "clear_currency": "USDT", "contract_value": "100", "contract_value_currency": "ADA", "is_inverse": false, "is_trading": true, "status": "ONLINE", "price_precision": "4", "tick_size": "0.0001", "min_order_amount": "1", "open_max_limits": [{"leverage": "100", "maint_margin_ratio": "0.005", "max_limit": "500000"}, {"leverage": "50", "maint_margin_ratio": "0.01", "max_limit": "1000000"}, {"leverage": "20", "maint_margin_ratio": "0.025", "max_limit": "2000000"}, {"leverage": "10", "maint_margin_ratio": "0.05", "max_limit": "5000000"}]}, "tierBased": true, "percentage": true}, "XRP/USDT:USDT": {"id": "XRPUSDTPERP", "lowercaseId": null, "symbol": "XRP/USDT:USDT", "base": "XRP", "quote": "USDT", "settle": "USDT", "baseId": "XRP", "quoteId": "USDT", "settleId": "USDT", "type": "swap", "spot": false, "margin": null, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": 0.002, "maker": 0.002, "contractSize": 100, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": null, "price": 0.0001}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 1, "max": null}, "price": {"min": 0.0001, "max": null}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"instrument_id": "XRPUSDTPERP", "type": "REAL", "contract_type": "PERPETUAL", "base_currency": "XRP", "quote_currency": "USDT", "clear_currency": "USDT", "contract_value": "100", "contract_value_currency": "XRP", "is_inverse": false, "is_trading": true, "status": "ONLINE", "price_precision": "4", "tick_size": "0.0001", "min_order_amount": "1", "open_max_limits": [{"leverage": "100", "maint_margin_ratio": "0.005", "max_limit": "500000"}, {"leverage": "50", "maint_margin_ratio": "0.01", "max_limit": "1000000"}, {"leverage": "20", "maint_margin_ratio": "0.025", "max_limit": "2000000"}, {"leverage": "10", "maint_margin_ratio": "0.05", "max_limit": "5000000"}]}, "tierBased": true, "percentage": true}, "TRX/USDT": {"id": "TRX_USDT", "lowercaseId": null, "symbol": "TRX/USDT", "base": "TRX", "quote": "USDT", "settle": null, "baseId": "TRX", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": null, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.002, "maker": 0.002, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.1, "price": 1e-06}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 0.1, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": 0, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"order_types": ["LIMIT", "MARKET"], "quote_asset": "USDT", "minimum_value": "0", "amount_precision": "1", "status": "TRADING", "minimum_amount": "0.1", "symbol": "TRX_USDT", "is_allow": "1", "zone": "MAIN", "base_asset": "TRX", "price_precision": "6"}, "tierBased": true, "percentage": true}, "TRX/USDT:USDT": {"id": "TRXUSDTPERP", "symbol": "TRX/USDT:USDT", "base": "TRX", "quote": "USDT", "baseId": "TRX", "quoteId": "USDT", "active": true, "type": "swap", "linear": true, "inverse": false, "spot": false, "swap": true, "future": false, "option": false, "contract": true, "contractSize": 100, "settle": "USDT", "settleId": "USDT", "precision": {"price": 1e-05}, "limits": {"amount": {"min": 1}, "price": {"min": 1e-05}, "cost": {}, "leverage": {}}, "info": {"instrument_id": "TRXUSDTPERP", "type": "REAL", "contract_type": "PERPETUAL", "base_currency": "TRX", "quote_currency": "USDT", "clear_currency": "USDT", "contract_value": "100", "contract_value_currency": "TRX", "is_inverse": false, "is_trading": true, "status": "ONLINE", "price_precision": "5", "tick_size": "0.00001", "min_order_amount": "1", "open_max_limits": [{"leverage": "3", "max_limit": "500000"}, {"leverage": "10", "max_limit": "200000"}, {"leverage": "50", "max_limit": "100000"}]}, "tierBased": true, "percentage": true, "taker": 0.002, "maker": 0.002}, "ETH/USDT": {"id": "ETH_USDT", "lowercaseId": null, "symbol": "ETH/USDT", "base": "ETH", "quote": "USDT", "settle": null, "baseId": "ETH", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": null, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.002, "maker": 0.002, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.0001, "price": 0.01}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 0.0006, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": 2, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"order_types": ["LIMIT", "MARKET"], "quote_asset": "USDT", "minimum_value": "2", "amount_precision": "4", "status": "TRADING", "minimum_amount": "0.0006", "symbol": "ETH_USDT", "is_allow": "1", "zone": "MAIN", "base_asset": "ETH", "price_precision": "2"}, "tierBased": true, "percentage": true}, "ETH/USDT:USDT": {"id": "ETHUSDTPERP", "lowercaseId": null, "symbol": "ETH/USDT:USDT", "base": "ETH", "quote": "USDT", "settle": "USDT", "baseId": "ETH", "quoteId": "USDT", "settleId": "USDT", "type": "swap", "spot": false, "margin": null, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": 0.002, "maker": 0.002, "contractSize": 0.01, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": null, "price": 0.01}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 1, "max": null}, "price": {"min": 0.01, "max": null}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"instrument_id": "ETHUSDTPERP", "type": "REAL", "contract_type": "PERPETUAL", "base_currency": "ETH", "quote_currency": "USDT", "clear_currency": "USDT", "contract_value": "0.01", "contract_value_currency": "ETH", "is_inverse": false, "is_trading": true, "status": "ONLINE", "price_precision": "2", "tick_size": "0.01", "min_order_amount": "1", "open_max_limits": [{"leverage": "100", "maint_margin_ratio": "0.005", "max_limit": "3000000"}, {"leverage": "50", "maint_margin_ratio": "0.01", "max_limit": "10000000"}, {"leverage": "20", "maint_margin_ratio": "0.025", "max_limit": "15000000"}, {"leverage": "10", "maint_margin_ratio": "0.05", "max_limit": "20000000"}]}, "tierBased": true, "percentage": true}}