{"BTC/USD": {"id": "btcusd", "lowercaseId": null, "symbol": "BTC/USD", "base": "BTC", "quote": "USD", "settle": null, "baseId": "BTC", "quoteId": "USD", "settleId": null, "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "index": null, "active": null, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.004, "maker": 0.002, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"price": 0.01, "amount": 1e-08}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 1e-05, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": ["BTCUSD", 2, 8, "0.00001", 10, true], "tierBased": null, "percentage": null}, "BTC/EUR": {"id": "btceur", "lowercaseId": null, "symbol": "BTC/EUR", "base": "BTC", "quote": "EUR", "settle": null, "baseId": "BTC", "quoteId": "EUR", "settleId": null, "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "index": null, "active": null, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.004, "maker": 0.002, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"price": 0.01, "amount": 1e-08}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 1e-05, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": ["BTCEUR", 2, 8, "0.00001", 10, true], "tierBased": null, "percentage": null}, "LTC/USD": {"id": "ltcusd", "lowercaseId": null, "symbol": "LTC/USD", "base": "LTC", "quote": "USD", "settle": null, "baseId": "LTC", "quoteId": "USD", "settleId": null, "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "index": null, "active": null, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.004, "maker": 0.002, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"price": 0.01, "amount": 1e-05}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 0.01, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": ["LTCUSD", 2, 5, "0.01", 7, true], "tierBased": null, "percentage": null}, "ETH/USD": {"id": "et<PERSON>d", "lowercaseId": null, "symbol": "ETH/USD", "base": "ETH", "quote": "USD", "settle": null, "baseId": "ETH", "quoteId": "USD", "settleId": null, "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "index": null, "active": null, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.004, "maker": 0.002, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"price": 0.01, "amount": 1e-06}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 0.001, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": ["ETHUSD", 2, 6, "0.001", 8, true], "tierBased": null, "percentage": null}, "BTC/USDT": {"id": "btcusdt", "lowercaseId": null, "symbol": "BTC/USDT", "base": "BTC", "quote": "USDT", "settle": null, "baseId": "BTC", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "index": null, "active": null, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.004, "maker": 0.002, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"price": 0.01, "amount": 1e-08}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 1e-05, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": ["BTCUSDT", 2, 8, "0.00001", 10, true], "tierBased": null, "percentage": null}, "ETH/USDT": {"id": "<PERSON><PERSON><PERSON>", "lowercaseId": null, "symbol": "ETH/USDT", "base": "ETH", "quote": "USDT", "settle": null, "baseId": "ETH", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "index": null, "active": null, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.004, "maker": 0.002, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"price": 0.01, "amount": 1e-06}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 0.001, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": ["ETHUSDT", 2, 6, "0.001", 8, true], "tierBased": null, "percentage": null}, "USDC/USD": {"id": "usdcusd", "lowercaseId": null, "symbol": "USDC/USD", "base": "USDC", "quote": "USD", "settle": null, "baseId": "USDC", "quoteId": "USD", "settleId": null, "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "index": null, "active": null, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.004, "maker": 0.002, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"price": 1e-05, "amount": 1e-06}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 0.1, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": ["USDCUSD", 5, 6, "0.1", 8, true], "tierBased": null, "percentage": null}, "BTC/GUSD:GUSD": {"id": "btcgusdperp", "lowercaseId": null, "symbol": "BTC/GUSD:GUSD", "base": "BTC", "quote": "GUSD", "settle": "GUSD", "baseId": "BTC", "quoteId": "GUSD", "settleId": "GUSD", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "index": null, "active": null, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": 0.004, "maker": 0.002, "contractSize": 0.1, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"price": 0.1, "amount": 0.0001}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 0.0001, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": ["BTCGUSDPERP", 1, 4, "0.0001", 4, true], "tierBased": null, "percentage": null}, "SOL/BTC": {"id": "solbtc", "lowercaseId": null, "symbol": "SOL/BTC", "base": "SOL", "quote": "BTC", "settle": null, "baseId": "SOL", "quoteId": "BTC", "settleId": null, "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "index": null, "active": null, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.004, "maker": 0.002, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"price": 1e-07, "amount": 1e-06}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 0.001, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": ["SOLBTC", 7, 6, "0.001", 8, true], "tierBased": null, "percentage": null}, "PAXG/USD": {"id": "pax<PERSON>d", "lowercaseId": null, "symbol": "PAXG/USD", "base": "PAXG", "quote": "USD", "settle": null, "baseId": "PAXG", "quoteId": "USD", "settleId": null, "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "index": null, "active": null, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.004, "maker": 0.002, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"price": 0.01, "amount": 1e-08}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 0.0001, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": ["PAXGUSD", 2, 8, "0.0001", 10, true], "tierBased": null, "percentage": null}}