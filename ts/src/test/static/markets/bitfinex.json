{"BTC/USD": {"id": "tBTCUSD", "lowercaseId": null, "symbol": "BTC/USD", "base": "BTC", "quote": "USD", "settle": null, "baseId": "fBTC", "quoteId": "fUSD", "settleId": null, "type": "spot", "spot": true, "margin": true, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.002, "maker": 0.001, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 8, "price": 5}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 4e-05, "max": 2000}, "price": {"min": 1e-08, "max": null}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": [null, null, null, "0.00004", "2000.0", null, null, null, 0.1, 0.05, null, null], "tierBased": true, "percentage": true, "feeSide": "get", "tiers": {"taker": [[0, 0.002], [500000, 0.002], [1000000, 0.002], [2500000, 0.002], [5000000, 0.002], [7500000, 0.002], [10000000, 0.0018], [15000000, 0.0016], [20000000, 0.0014], [25000000, 0.0012], [30000000, 0.001]], "maker": [[0, 0.001], [500000, 0.0008], [1000000, 0.0006], [2500000, 0.0004], [5000000, 0.0002], [7500000, 0], [10000000, 0], [15000000, 0], [20000000, 0], [25000000, 0], [30000000, 0]]}}, "BTC/EUR": {"id": "tBTCEUR", "lowercaseId": null, "symbol": "BTC/EUR", "base": "BTC", "quote": "EUR", "settle": null, "baseId": "fBTC", "quoteId": "fEUR", "settleId": null, "type": "spot", "spot": true, "margin": true, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.002, "maker": 0.001, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 8, "price": 5}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 4e-05, "max": 2000}, "price": {"min": 1e-08, "max": null}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": [null, null, null, "0.00004", "2000.0", null, null, null, 0.2, 0.1, null, null], "tierBased": true, "percentage": true, "feeSide": "get", "tiers": {"taker": [[0, 0.002], [500000, 0.002], [1000000, 0.002], [2500000, 0.002], [5000000, 0.002], [7500000, 0.002], [10000000, 0.0018], [15000000, 0.0016], [20000000, 0.0014], [25000000, 0.0012], [30000000, 0.001]], "maker": [[0, 0.001], [500000, 0.0008], [1000000, 0.0006], [2500000, 0.0004], [5000000, 0.0002], [7500000, 0], [10000000, 0], [15000000, 0], [20000000, 0], [25000000, 0], [30000000, 0]]}}, "LTC/USD": {"id": "tLTCUSD", "lowercaseId": null, "symbol": "LTC/USD", "base": "LTC", "quote": "USD", "settle": null, "baseId": "fLTC", "quoteId": "fUSD", "settleId": null, "type": "spot", "spot": true, "margin": true, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.002, "maker": 0.001, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 8, "price": 5}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 0.04, "max": 5000}, "price": {"min": 1e-08, "max": null}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": [null, null, null, "0.04", "5000.0", null, null, null, 0.2, 0.1, null, null], "tierBased": true, "percentage": true, "feeSide": "get", "tiers": {"taker": [[0, 0.002], [500000, 0.002], [1000000, 0.002], [2500000, 0.002], [5000000, 0.002], [7500000, 0.002], [10000000, 0.0018], [15000000, 0.0016], [20000000, 0.0014], [25000000, 0.0012], [30000000, 0.001]], "maker": [[0, 0.001], [500000, 0.0008], [1000000, 0.0006], [2500000, 0.0004], [5000000, 0.0002], [7500000, 0], [10000000, 0], [15000000, 0], [20000000, 0], [25000000, 0], [30000000, 0]]}}, "ETH/USD": {"id": "tETHUSD", "lowercaseId": null, "symbol": "ETH/USD", "base": "ETH", "quote": "USD", "settle": null, "baseId": "fETH", "quoteId": "fUSD", "settleId": null, "type": "spot", "spot": true, "margin": true, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.002, "maker": 0.001, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 8, "price": 5}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 0.0008, "max": 5000}, "price": {"min": 1e-08, "max": null}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": [null, null, null, "0.0008", "5000.0", null, null, null, 0.2, 0.1, null, null], "tierBased": true, "percentage": true, "feeSide": "get", "tiers": {"taker": [[0, 0.002], [500000, 0.002], [1000000, 0.002], [2500000, 0.002], [5000000, 0.002], [7500000, 0.002], [10000000, 0.0018], [15000000, 0.0016], [20000000, 0.0014], [25000000, 0.0012], [30000000, 0.001]], "maker": [[0, 0.001], [500000, 0.0008], [1000000, 0.0006], [2500000, 0.0004], [5000000, 0.0002], [7500000, 0], [10000000, 0], [15000000, 0], [20000000, 0], [25000000, 0], [30000000, 0]]}}, "BTC/USDT": {"id": "tBTCUST", "lowercaseId": null, "symbol": "BTC/USDT", "base": "BTC", "quote": "USDT", "settle": null, "baseId": "fBTC", "quoteId": "fUST", "settleId": null, "type": "spot", "spot": true, "margin": true, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.002, "maker": 0.001, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 8, "price": 5}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 4e-05, "max": 2000}, "price": {"min": 1e-08, "max": null}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": [null, null, null, "0.00004", "2000.0", null, null, null, 0.1, 0.05, null, null], "tierBased": true, "percentage": true, "feeSide": "get", "tiers": {"taker": [[0, 0.002], [500000, 0.002], [1000000, 0.002], [2500000, 0.002], [5000000, 0.002], [7500000, 0.002], [10000000, 0.0018], [15000000, 0.0016], [20000000, 0.0014], [25000000, 0.0012], [30000000, 0.001]], "maker": [[0, 0.001], [500000, 0.0008], [1000000, 0.0006], [2500000, 0.0004], [5000000, 0.0002], [7500000, 0], [10000000, 0], [15000000, 0], [20000000, 0], [25000000, 0], [30000000, 0]]}}, "LTC/USDT": {"id": "tLTCUST", "lowercaseId": null, "symbol": "LTC/USDT", "base": "LTC", "quote": "USDT", "settle": null, "baseId": "fLTC", "quoteId": "fUST", "settleId": null, "type": "spot", "spot": true, "margin": true, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.002, "maker": 0.001, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 8, "price": 5}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 0.04, "max": 2000}, "price": {"min": 1e-08, "max": null}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": [null, null, null, "0.04", "2000.0", null, null, null, 0.2, 0.1, null, null], "tierBased": true, "percentage": true, "feeSide": "get", "tiers": {"taker": [[0, 0.002], [500000, 0.002], [1000000, 0.002], [2500000, 0.002], [5000000, 0.002], [7500000, 0.002], [10000000, 0.0018], [15000000, 0.0016], [20000000, 0.0014], [25000000, 0.0012], [30000000, 0.001]], "maker": [[0, 0.001], [500000, 0.0008], [1000000, 0.0006], [2500000, 0.0004], [5000000, 0.0002], [7500000, 0], [10000000, 0], [15000000, 0], [20000000, 0], [25000000, 0], [30000000, 0]]}}, "ADA/USDT": {"id": "tADAUST", "lowercaseId": null, "symbol": "ADA/USDT", "base": "ADA", "quote": "USDT", "settle": null, "baseId": "fADA", "quoteId": "fUST", "settleId": null, "type": "spot", "spot": true, "margin": true, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.002, "maker": 0.001, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 8, "price": 5}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 4, "max": 250000}, "price": {"min": 1e-08, "max": null}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": [null, null, null, "4.0", "250000.0", null, null, null, 0.3, 0.15, null, null], "tierBased": true, "percentage": true, "feeSide": "get", "tiers": {"taker": [[0, 0.002], [500000, 0.002], [1000000, 0.002], [2500000, 0.002], [5000000, 0.002], [7500000, 0.002], [10000000, 0.0018], [15000000, 0.0016], [20000000, 0.0014], [25000000, 0.0012], [30000000, 0.001]], "maker": [[0, 0.001], [500000, 0.0008], [1000000, 0.0006], [2500000, 0.0004], [5000000, 0.0002], [7500000, 0], [10000000, 0], [15000000, 0], [20000000, 0], [25000000, 0], [30000000, 0]]}}, "XRP/USDT": {"id": "tXRPUST", "lowercaseId": null, "symbol": "XRP/USDT", "base": "XRP", "quote": "USDT", "settle": null, "baseId": "fXRP", "quoteId": "fUST", "settleId": null, "type": "spot", "spot": true, "margin": true, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.002, "maker": 0.001, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 8, "price": 5}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 4, "max": 2000000}, "price": {"min": 1e-08, "max": null}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": [null, null, null, "4.0", "2000000.0", null, null, null, 0.3, 0.15, null, null], "tierBased": true, "percentage": true, "feeSide": "get", "tiers": {"taker": [[0, 0.002], [500000, 0.002], [1000000, 0.002], [2500000, 0.002], [5000000, 0.002], [7500000, 0.002], [10000000, 0.0018], [15000000, 0.0016], [20000000, 0.0014], [25000000, 0.0012], [30000000, 0.001]], "maker": [[0, 0.001], [500000, 0.0008], [1000000, 0.0006], [2500000, 0.0004], [5000000, 0.0002], [7500000, 0], [10000000, 0], [15000000, 0], [20000000, 0], [25000000, 0], [30000000, 0]]}}, "SOL/USDT": {"id": "tSOLUST", "lowercaseId": null, "symbol": "SOL/USDT", "base": "SOL", "quote": "USDT", "settle": null, "baseId": "fSOL", "quoteId": "fUST", "settleId": null, "type": "spot", "spot": true, "margin": true, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.002, "maker": 0.001, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 8, "price": 5}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 0.02, "max": 50000}, "price": {"min": 1e-08, "max": null}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": [null, null, null, "0.02", "50000.0", null, null, null, 0.3, 0.15, null, null], "tierBased": true, "percentage": true, "feeSide": "get", "tiers": {"taker": [[0, 0.002], [500000, 0.002], [1000000, 0.002], [2500000, 0.002], [5000000, 0.002], [7500000, 0.002], [10000000, 0.0018], [15000000, 0.0016], [20000000, 0.0014], [25000000, 0.0012], [30000000, 0.001]], "maker": [[0, 0.001], [500000, 0.0008], [1000000, 0.0006], [2500000, 0.0004], [5000000, 0.0002], [7500000, 0], [10000000, 0], [15000000, 0], [20000000, 0], [25000000, 0], [30000000, 0]]}}, "TRX/USDT": {"id": "tTRXUST", "lowercaseId": null, "symbol": "TRX/USDT", "base": "TRX", "quote": "USDT", "settle": null, "baseId": "fTRX", "quoteId": "fUST", "settleId": null, "type": "spot", "spot": true, "margin": true, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.002, "maker": 0.001, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 8, "price": 5}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 22, "max": 1000000}, "price": {"min": 1e-08, "max": null}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": [null, null, null, "22.0", "1000000.0", null, null, null, 0.3, 0.15, null, null], "tierBased": true, "percentage": true, "feeSide": "get", "tiers": {"taker": [[0, 0.002], [500000, 0.002], [1000000, 0.002], [2500000, 0.002], [5000000, 0.002], [7500000, 0.002], [10000000, 0.0018], [15000000, 0.0016], [20000000, 0.0014], [25000000, 0.0012], [30000000, 0.001]], "maker": [[0, 0.001], [500000, 0.0008], [1000000, 0.0006], [2500000, 0.0004], [5000000, 0.0002], [7500000, 0], [10000000, 0], [15000000, 0], [20000000, 0], [25000000, 0], [30000000, 0]]}}, "LTC/USDT:USDT": {"id": "tLTCF0:USTF0", "lowercaseId": null, "symbol": "LTC/USDT:USDT", "base": "LTC", "quote": "USDT", "settle": "USDT", "baseId": "fLTCF0", "quoteId": "fUSTF0", "settleId": "USDT", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": 0.002, "maker": 0.001, "contractSize": 1, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 8, "price": 5}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 0.04, "max": 250000}, "price": {"min": 1e-08, "max": null}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": [null, null, null, "0.04", "250000.0", null, null, null, 0.01, 0.005], "tierBased": true, "percentage": true, "feeSide": "get", "tiers": {"taker": [[0, 0.002], [500000, 0.002], [1000000, 0.002], [2500000, 0.002], [5000000, 0.002], [7500000, 0.002], [10000000, 0.0018], [15000000, 0.0016], [20000000, 0.0014], [25000000, 0.0012], [30000000, 0.001]], "maker": [[0, 0.001], [500000, 0.0008], [1000000, 0.0006], [2500000, 0.0004], [5000000, 0.0002], [7500000, 0], [10000000, 0], [15000000, 0], [20000000, 0], [25000000, 0], [30000000, 0]]}}, "BTC/USDT:USDT": {"id": "tBTCF0:USTF0", "lowercaseId": null, "symbol": "BTC/USDT:USDT", "base": "BTC", "quote": "USDT", "settle": "USDT", "baseId": "fBTCF0", "quoteId": "fUSTF0", "settleId": "USDT", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": 0.002, "maker": 0.001, "contractSize": 1, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 8, "price": 5}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 4e-05, "max": 100}, "price": {"min": 1e-08, "max": null}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": [null, null, null, "0.00004", "100.0", null, null, null, 0.01, 0.005], "tierBased": true, "percentage": true, "feeSide": "get", "tiers": {"taker": [[0, 0.002], [500000, 0.002], [1000000, 0.002], [2500000, 0.002], [5000000, 0.002], [7500000, 0.002], [10000000, 0.0018], [15000000, 0.0016], [20000000, 0.0014], [25000000, 0.0012], [30000000, 0.001]], "maker": [[0, 0.001], [500000, 0.0008], [1000000, 0.0006], [2500000, 0.0004], [5000000, 0.0002], [7500000, 0], [10000000, 0], [15000000, 0], [20000000, 0], [25000000, 0], [30000000, 0]]}}, "ADA/USDT:USDT": {"id": "tADAF0:USTF0", "lowercaseId": null, "symbol": "ADA/USDT:USDT", "base": "ADA", "quote": "USDT", "settle": "USDT", "baseId": "fADAF0", "quoteId": "fUSTF0", "settleId": "USDT", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": 0.002, "maker": 0.001, "contractSize": 1, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 8, "price": 5}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 4, "max": 250000}, "price": {"min": 1e-08, "max": null}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": [null, null, null, "4.0", "250000.0", null, null, null, 0.01, 0.005], "tierBased": true, "percentage": true, "feeSide": "get", "tiers": {"taker": [[0, 0.002], [500000, 0.002], [1000000, 0.002], [2500000, 0.002], [5000000, 0.002], [7500000, 0.002], [10000000, 0.0018], [15000000, 0.0016], [20000000, 0.0014], [25000000, 0.0012], [30000000, 0.001]], "maker": [[0, 0.001], [500000, 0.0008], [1000000, 0.0006], [2500000, 0.0004], [5000000, 0.0002], [7500000, 0], [10000000, 0], [15000000, 0], [20000000, 0], [25000000, 0], [30000000, 0]]}}, "XRP/USDT:USDT": {"id": "tXRPF0:USTF0", "lowercaseId": null, "symbol": "XRP/USDT:USDT", "base": "XRP", "quote": "USDT", "settle": "USDT", "baseId": "fXRPF0", "quoteId": "fUSTF0", "settleId": "USDT", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": 0.002, "maker": 0.001, "contractSize": 1, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 8, "price": 5}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 4, "max": 500000}, "price": {"min": 1e-08, "max": null}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": [null, null, null, "4.0", "500000.0", null, null, null, 0.01, 0.005], "tierBased": true, "percentage": true, "feeSide": "get", "tiers": {"taker": [[0, 0.002], [500000, 0.002], [1000000, 0.002], [2500000, 0.002], [5000000, 0.002], [7500000, 0.002], [10000000, 0.0018], [15000000, 0.0016], [20000000, 0.0014], [25000000, 0.0012], [30000000, 0.001]], "maker": [[0, 0.001], [500000, 0.0008], [1000000, 0.0006], [2500000, 0.0004], [5000000, 0.0002], [7500000, 0], [10000000, 0], [15000000, 0], [20000000, 0], [25000000, 0], [30000000, 0]]}}, "TRX/USDT:USDT": {"id": "tTRXF0:USTF0", "lowercaseId": null, "symbol": "TRX/USDT:USDT", "base": "TRX", "quote": "USDT", "settle": "USDT", "baseId": "fTRXF0", "quoteId": "fUSTF0", "settleId": "USDT", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": 0.002, "maker": 0.001, "contractSize": 1, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 8, "price": 5}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 22, "max": 1000000}, "price": {"min": 1e-08, "max": null}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": [null, null, null, "22.0", "1000000.0", null, null, null, 0.01, 0.005], "tierBased": true, "percentage": true, "feeSide": "get", "tiers": {"taker": [[0, 0.002], [500000, 0.002], [1000000, 0.002], [2500000, 0.002], [5000000, 0.002], [7500000, 0.002], [10000000, 0.0018], [15000000, 0.0016], [20000000, 0.0014], [25000000, 0.0012], [30000000, 0.001]], "maker": [[0, 0.001], [500000, 0.0008], [1000000, 0.0006], [2500000, 0.0004], [5000000, 0.0002], [7500000, 0], [10000000, 0], [15000000, 0], [20000000, 0], [25000000, 0], [30000000, 0]]}}, "ETH/USDT": {"id": "tETHUST", "lowercaseId": null, "symbol": "ETH/USDT", "base": "ETH", "quote": "USDT", "settle": null, "baseId": "fETH", "quoteId": "fUST", "settleId": null, "type": "spot", "spot": true, "margin": true, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.002, "maker": 0.001, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 8, "price": 5}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 0.0008, "max": 2000}, "price": {"min": 1e-08, "max": null}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": [null, null, null, "0.0008", "2000.0", null, null, null, 0.2, 0.1, null, null], "tierBased": true, "percentage": true, "feeSide": "get", "tiers": {"taker": [[0, 0.002], [500000, 0.002], [1000000, 0.002], [2500000, 0.002], [5000000, 0.002], [7500000, 0.002], [10000000, 0.0018], [15000000, 0.0016], [20000000, 0.0014], [25000000, 0.0012], [30000000, 0.001]], "maker": [[0, 0.001], [500000, 0.0008], [1000000, 0.0006], [2500000, 0.0004], [5000000, 0.0002], [7500000, 0], [10000000, 0], [15000000, 0], [20000000, 0], [25000000, 0], [30000000, 0]]}}, "ETH/USDT:USDT": {"id": "tETHF0:USTF0", "lowercaseId": null, "symbol": "ETH/USDT:USDT", "base": "ETH", "quote": "USDT", "settle": "USDT", "baseId": "fETHF0", "quoteId": "fUSTF0", "settleId": "USDT", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": 0.002, "maker": 0.001, "contractSize": 1, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 8, "price": 5}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 0.0008, "max": 1000}, "price": {"min": 1e-08, "max": null}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": [null, null, null, "0.0008", "1000.0", null, null, null, 0.01, 0.005], "tierBased": true, "percentage": true, "feeSide": "get", "tiers": {"taker": [[0, 0.002], [500000, 0.002], [1000000, 0.002], [2500000, 0.002], [5000000, 0.002], [7500000, 0.002], [10000000, 0.0018], [15000000, 0.0016], [20000000, 0.0014], [25000000, 0.0012], [30000000, 0.001]], "maker": [[0, 0.001], [500000, 0.0008], [1000000, 0.0006], [2500000, 0.0004], [5000000, 0.0002], [7500000, 0], [10000000, 0], [15000000, 0], [20000000, 0], [25000000, 0], [30000000, 0]]}}}