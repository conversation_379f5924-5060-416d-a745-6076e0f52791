{"BTC/BRL": {"id": "btcbrl", "symbol": "BTC/BRL", "base": "BTC", "quote": "BRL", "baseId": "btc", "quoteId": "brl", "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "index": false, "active": true, "contract": false, "taker": 0.005, "maker": 0.0025, "precision": {"price": 2, "amount": 8, "cost": 2}, "limits": {"leverage": {}, "amount": {"min": 1.65e-06}, "price": {"min": 0.0001}, "cost": {}}, "marginModes": {}, "info": {"base": {"name": "Bitcoin", "precision": "8", "symbol": "btc", "type": "CRYPTO"}, "quote": {"name": "Real", "precision": "2", "symbol": "brl", "type": "FIAT"}, "symbol": "btcbrl", "quantity_min": "0.00000165", "quantity_increment": "0.00000001", "quantity_precision": "8", "price_min": "0.0001", "price_increment": "1.0", "price_precision": "0", "default_fees": {"maker": "0.0025", "taker": "0.005"}, "order_type": ["MARKET", "LIMIT", "INSTANT", "STOP_MARKET", "STOP_LIMIT"]}, "tierBased": false, "percentage": true, "feeSide": "get"}, "ETH/BRL": {"id": "ethbrl", "symbol": "ETH/BRL", "base": "ETH", "quote": "BRL", "baseId": "eth", "quoteId": "brl", "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "index": false, "active": true, "contract": false, "taker": 0.005, "maker": 0.0025, "precision": {"price": 2, "amount": 8, "cost": 2}, "limits": {"leverage": {}, "amount": {"min": 8.311e-05}, "price": {"min": 0.0001}, "cost": {}}, "marginModes": {}, "info": {"base": {"name": "Ethereum", "precision": "8", "symbol": "eth", "type": "CRYPTO"}, "quote": {"name": "Real", "precision": "2", "symbol": "brl", "type": "FIAT"}, "symbol": "ethbrl", "quantity_min": "0.00008311", "quantity_increment": "0.00000001", "quantity_precision": "8", "price_min": "0.0001", "price_increment": "1.0", "price_precision": "0", "default_fees": {"maker": "0.0025", "taker": "0.005"}, "order_type": ["MARKET", "LIMIT", "INSTANT", "STOP_MARKET", "STOP_LIMIT"]}, "tierBased": false, "percentage": true, "feeSide": "get"}, "USDT/BRL": {"id": "usdtbrl", "symbol": "USDT/BRL", "base": "USDT", "quote": "BRL", "baseId": "usdt", "quoteId": "brl", "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "index": false, "active": true, "contract": false, "taker": 0.005, "maker": 0.0025, "precision": {"price": 2, "amount": 8, "cost": 2}, "limits": {"leverage": {}, "amount": {"min": 0.13547611}, "price": {"min": 0.0001}, "cost": {}}, "marginModes": {}, "info": {"base": {"name": "<PERSON><PERSON>", "precision": "8", "symbol": "usdt", "type": "CRYPTO"}, "quote": {"name": "Real", "precision": "2", "symbol": "brl", "type": "FIAT"}, "symbol": "usdtbrl", "quantity_min": "0.13547611", "quantity_increment": "0.00000001", "quantity_precision": "8", "price_min": "0.0001", "price_increment": "0.0001", "price_precision": "4", "default_fees": {"maker": "0.0025", "taker": "0.005"}, "order_type": ["MARKET", "LIMIT", "INSTANT", "STOP_MARKET", "STOP_LIMIT"]}, "tierBased": false, "percentage": true, "feeSide": "get"}, "BTC/USDT": {"id": "btcusdt", "symbol": "BTC/USDT", "base": "BTC", "quote": "USDT", "baseId": "btc", "quoteId": "usdt", "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "index": false, "active": true, "contract": false, "taker": 0.005, "maker": 0.0025, "precision": {"price": 8, "amount": 8, "cost": 8}, "limits": {"leverage": {}, "amount": {"min": 9.56e-06}, "price": {"min": 0.0001}, "cost": {}}, "marginModes": {}, "info": {"base": {"name": "Bitcoin", "precision": "8", "symbol": "btc", "type": "CRYPTO"}, "quote": {"name": "<PERSON><PERSON>", "precision": "8", "symbol": "usdt", "type": "CRYPTO"}, "symbol": "btcusdt", "quantity_min": "0.00000956", "quantity_increment": "0.00000001", "quantity_precision": "8", "price_min": "0.0001", "price_increment": "1.0", "price_precision": "0", "default_fees": {"maker": "0.0025", "taker": "0.005"}, "order_type": ["MARKET", "LIMIT", "INSTANT", "STOP_MARKET", "STOP_LIMIT"]}, "tierBased": false, "percentage": true, "feeSide": "get"}, "XRP/BRL": {"id": "xrpbrl", "lowercaseId": null, "symbol": "XRP/BRL", "base": "XRP", "quote": "BRL", "settle": null, "baseId": "xrp", "quoteId": "brl", "settleId": null, "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "index": false, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.005, "maker": 0.0025, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"price": 2, "amount": 6, "cost": 2}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 0.02306746, "max": null}, "price": {"min": 0.001, "max": null}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"base": {"name": "XRP", "precision": "6", "symbol": "xrp", "type": "CRYPTO"}, "quote": {"name": "Real", "precision": "2", "symbol": "brl", "type": "FIAT"}, "symbol": "xrpbrl", "quantity_min": "0.02306746", "quantity_increment": "0.000001", "quantity_precision": "6", "price_min": "0.001", "price_increment": "0.001", "price_precision": "3", "default_fees": {"maker": "0.0025", "taker": "0.005"}, "order_type": ["MARKET", "LIMIT", "INSTANT", "STOP_MARKET", "STOP_LIMIT"]}, "tierBased": false, "percentage": true, "feeSide": "get"}}