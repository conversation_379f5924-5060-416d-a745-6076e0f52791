{"BTC/USDT": {"id": "BTC_USDT", "lowercaseId": null, "symbol": "BTC/USDT", "base": "BTC", "quote": "USDT", "settle": null, "baseId": "BTC", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": true, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.004, "maker": 0.004, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"price": 0.01, "amount": 1e-05}, "limits": {"leverage": {"min": 1, "max": 50}, "amount": {"min": null, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"symbol": "BTC_USDT", "inst_type": "CCY_PAIR", "display_name": "BTC/USDT", "base_ccy": "BTC", "quote_ccy": "USDT", "quote_decimals": 2, "quantity_decimals": 5, "price_tick_size": "0.01", "qty_tick_size": "0.00001", "max_leverage": "50", "tradable": true, "expiry_timestamp_ms": 0, "beta_product": false, "margin_buy_enabled": true, "margin_sell_enabled": true}, "tierBased": null, "percentage": null, "tiers": {"maker": [[0, 0.004], [25000, 0.0035], [50000, 0.0015], [100000, 0.001], [250000, 0.0009], [1000000, 0.0008], [20000000, 0.0007], [100000000, 0.0006], [200000000, 0.0004]], "taker": [[0, 0.004], [25000, 0.0035], [50000, 0.0025], [100000, 0.0016], [250000, 0.00015], [1000000, 0.00014], [20000000, 0.00013], [100000000, 0.00012], [200000000, 0.0001]]}}, "BTC/USD": {"id": "BTC_USD", "lowercaseId": null, "symbol": "BTC/USD", "base": "BTC", "quote": "USD", "settle": null, "baseId": "BTC", "quoteId": "USD", "settleId": null, "type": "spot", "spot": true, "margin": true, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.004, "maker": 0.004, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"price": 0.01, "amount": 1e-05}, "limits": {"leverage": {"min": 1, "max": 50}, "amount": {"min": null, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"symbol": "BTC_USD", "inst_type": "CCY_PAIR", "display_name": "BTC/USD", "base_ccy": "BTC", "quote_ccy": "USD", "quote_decimals": 2, "quantity_decimals": 5, "price_tick_size": "0.01", "qty_tick_size": "0.00001", "max_leverage": "50", "tradable": true, "expiry_timestamp_ms": 0, "beta_product": false, "margin_buy_enabled": true, "margin_sell_enabled": true}, "tierBased": null, "percentage": null, "tiers": {"maker": [[0, 0.004], [25000, 0.0035], [50000, 0.0015], [100000, 0.001], [250000, 0.0009], [1000000, 0.0008], [20000000, 0.0007], [100000000, 0.0006], [200000000, 0.0004]], "taker": [[0, 0.004], [25000, 0.0035], [50000, 0.0025], [100000, 0.0016], [250000, 0.00015], [1000000, 0.00014], [20000000, 0.00013], [100000000, 0.00012], [200000000, 0.0001]]}}, "ETH/USDT": {"id": "ETH_USDT", "lowercaseId": null, "symbol": "ETH/USDT", "base": "ETH", "quote": "USDT", "settle": null, "baseId": "ETH", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": true, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.004, "maker": 0.004, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"price": 0.01, "amount": 0.0001}, "limits": {"leverage": {"min": 1, "max": 50}, "amount": {"min": null, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"symbol": "ETH_USDT", "inst_type": "CCY_PAIR", "display_name": "ETH/USDT", "base_ccy": "ETH", "quote_ccy": "USDT", "quote_decimals": 2, "quantity_decimals": 4, "price_tick_size": "0.01", "qty_tick_size": "0.0001", "max_leverage": "50", "tradable": true, "expiry_timestamp_ms": 0, "beta_product": false, "margin_buy_enabled": true, "margin_sell_enabled": true}, "tierBased": null, "percentage": null, "tiers": {"maker": [[0, 0.004], [25000, 0.0035], [50000, 0.0015], [100000, 0.001], [250000, 0.0009], [1000000, 0.0008], [20000000, 0.0007], [100000000, 0.0006], [200000000, 0.0004]], "taker": [[0, 0.004], [25000, 0.0035], [50000, 0.0025], [100000, 0.0016], [250000, 0.00015], [1000000, 0.00014], [20000000, 0.00013], [100000000, 0.00012], [200000000, 0.0001]]}}, "ADA/USDT": {"id": "ADA_USDT", "lowercaseId": null, "symbol": "ADA/USDT", "base": "ADA", "quote": "USDT", "settle": null, "baseId": "ADA", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": true, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.004, "maker": 0.004, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"price": 1e-05, "amount": 1}, "limits": {"leverage": {"min": 1, "max": 50}, "amount": {"min": null, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"symbol": "ADA_USDT", "inst_type": "CCY_PAIR", "display_name": "ADA/USDT", "base_ccy": "ADA", "quote_ccy": "USDT", "quote_decimals": 5, "quantity_decimals": 0, "price_tick_size": "0.00001", "qty_tick_size": "1", "max_leverage": "50", "tradable": true, "expiry_timestamp_ms": 0, "beta_product": false, "margin_buy_enabled": true, "margin_sell_enabled": true}, "tierBased": null, "percentage": null, "tiers": {"maker": [[0, 0.004], [25000, 0.0035], [50000, 0.0015], [100000, 0.001], [250000, 0.0009], [1000000, 0.0008], [20000000, 0.0007], [100000000, 0.0006], [200000000, 0.0004]], "taker": [[0, 0.004], [25000, 0.0035], [50000, 0.0025], [100000, 0.0016], [250000, 0.00015], [1000000, 0.00014], [20000000, 0.00013], [100000000, 0.00012], [200000000, 0.0001]]}}, "LTC/USDT": {"id": "LTC_USDT", "lowercaseId": null, "symbol": "LTC/USDT", "base": "LTC", "quote": "USDT", "settle": null, "baseId": "LTC", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": true, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.004, "maker": 0.004, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"price": 0.001, "amount": 0.001}, "limits": {"leverage": {"min": 1, "max": 50}, "amount": {"min": null, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"symbol": "LTC_USDT", "inst_type": "CCY_PAIR", "display_name": "LTC/USDT", "base_ccy": "LTC", "quote_ccy": "USDT", "quote_decimals": 3, "quantity_decimals": 3, "price_tick_size": "0.001", "qty_tick_size": "0.001", "max_leverage": "50", "tradable": true, "expiry_timestamp_ms": 0, "beta_product": false, "margin_buy_enabled": true, "margin_sell_enabled": true}, "tierBased": null, "percentage": null, "tiers": {"maker": [[0, 0.004], [25000, 0.0035], [50000, 0.0015], [100000, 0.001], [250000, 0.0009], [1000000, 0.0008], [20000000, 0.0007], [100000000, 0.0006], [200000000, 0.0004]], "taker": [[0, 0.004], [25000, 0.0035], [50000, 0.0025], [100000, 0.0016], [250000, 0.00015], [1000000, 0.00014], [20000000, 0.00013], [100000000, 0.00012], [200000000, 0.0001]]}}, "XRP/USDT": {"id": "XRP_USDT", "lowercaseId": null, "symbol": "XRP/USDT", "base": "XRP", "quote": "USDT", "settle": null, "baseId": "XRP", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": true, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.004, "maker": 0.004, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"price": 1e-05, "amount": 1}, "limits": {"leverage": {"min": 1, "max": 50}, "amount": {"min": null, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"symbol": "XRP_USDT", "inst_type": "CCY_PAIR", "display_name": "XRP/USDT", "base_ccy": "XRP", "quote_ccy": "USDT", "quote_decimals": 5, "quantity_decimals": 0, "price_tick_size": "0.00001", "qty_tick_size": "1", "max_leverage": "50", "tradable": true, "expiry_timestamp_ms": 0, "beta_product": false, "margin_buy_enabled": true, "margin_sell_enabled": true}, "tierBased": null, "percentage": null, "tiers": {"maker": [[0, 0.004], [25000, 0.0035], [50000, 0.0015], [100000, 0.001], [250000, 0.0009], [1000000, 0.0008], [20000000, 0.0007], [100000000, 0.0006], [200000000, 0.0004]], "taker": [[0, 0.004], [25000, 0.0035], [50000, 0.0025], [100000, 0.0016], [250000, 0.00015], [1000000, 0.00014], [20000000, 0.00013], [100000000, 0.00012], [200000000, 0.0001]]}}, "DOGE/USDT": {"id": "DOGE_USDT", "lowercaseId": null, "symbol": "DOGE/USDT", "base": "DOGE", "quote": "USDT", "settle": null, "baseId": "DOGE", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": true, "swap": false, "future": false, "option": false, "index": null, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.004, "maker": 0.004, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"price": 1e-06, "amount": 1}, "limits": {"leverage": {"min": 1, "max": 50}, "amount": {"min": null, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"symbol": "DOGE_USDT", "inst_type": "CCY_PAIR", "display_name": "DOGE/USDT", "base_ccy": "DOGE", "quote_ccy": "USDT", "quote_decimals": 6, "quantity_decimals": 0, "price_tick_size": "0.000001", "qty_tick_size": "1", "max_leverage": "50", "tradable": true, "expiry_timestamp_ms": 0, "beta_product": false, "margin_buy_enabled": true, "margin_sell_enabled": true}, "tierBased": null, "percentage": null, "tiers": {"maker": [[0, 0.004], [25000, 0.0035], [50000, 0.0015], [100000, 0.001], [250000, 0.0009], [1000000, 0.0008], [20000000, 0.0007], [100000000, 0.0006], [200000000, 0.0004]], "taker": [[0, 0.004], [25000, 0.0035], [50000, 0.0025], [100000, 0.0016], [250000, 0.00015], [1000000, 0.00014], [20000000, 0.00013], [100000000, 0.00012], [200000000, 0.0001]]}}, "BTC/USD:USD": {"id": "BTCUSD-PERP", "lowercaseId": null, "symbol": "BTC/USD:USD", "base": "BTC", "quote": "USD", "settle": "USD", "baseId": "BTC", "quoteId": "USD", "settleId": "USD", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": 0.004, "maker": 0.004, "contractSize": 1, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"price": 0.1, "amount": 0.0001}, "limits": {"leverage": {"min": 1, "max": 100}, "amount": {"min": null, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"symbol": "BTCUSD-PERP", "inst_type": "PERPETUAL_SWAP", "display_name": "BTCUSD Perpetual", "base_ccy": "BTC", "quote_ccy": "USD", "quote_decimals": 1, "quantity_decimals": 4, "price_tick_size": "0.1", "qty_tick_size": "0.0001", "max_leverage": "100", "tradable": true, "expiry_timestamp_ms": 0, "beta_product": false, "underlying_symbol": "BTCUSD-INDEX", "contract_size": "1", "margin_buy_enabled": false, "margin_sell_enabled": false}, "tierBased": null, "percentage": null, "tiers": {"maker": [[0, 0.004], [25000, 0.0035], [50000, 0.0015], [100000, 0.001], [250000, 0.0009], [1000000, 0.0008], [20000000, 0.0007], [100000000, 0.0006], [200000000, 0.0004]], "taker": [[0, 0.004], [25000, 0.0035], [50000, 0.0025], [100000, 0.0016], [250000, 0.00015], [1000000, 0.00014], [20000000, 0.00013], [100000000, 0.00012], [200000000, 0.0001]]}}, "ETH/USD:USD": {"id": "ETHUSD-PERP", "lowercaseId": null, "symbol": "ETH/USD:USD", "base": "ETH", "quote": "USD", "settle": "USD", "baseId": "ETH", "quoteId": "USD", "settleId": "USD", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": 0.004, "maker": 0.004, "contractSize": 1, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"price": 0.01, "amount": 0.0001}, "limits": {"leverage": {"min": 1, "max": 100}, "amount": {"min": null, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"symbol": "ETHUSD-PERP", "inst_type": "PERPETUAL_SWAP", "display_name": "ETHUSD Perpetual", "base_ccy": "ETH", "quote_ccy": "USD", "quote_decimals": 2, "quantity_decimals": 4, "price_tick_size": "0.01", "qty_tick_size": "0.0001", "max_leverage": "100", "tradable": true, "expiry_timestamp_ms": 0, "beta_product": false, "underlying_symbol": "ETHUSD-INDEX", "contract_size": "1", "margin_buy_enabled": false, "margin_sell_enabled": false}, "tierBased": null, "percentage": null, "tiers": {"maker": [[0, 0.004], [25000, 0.0035], [50000, 0.0015], [100000, 0.001], [250000, 0.0009], [1000000, 0.0008], [20000000, 0.0007], [100000000, 0.0006], [200000000, 0.0004]], "taker": [[0, 0.004], [25000, 0.0035], [50000, 0.0025], [100000, 0.0016], [250000, 0.00015], [1000000, 0.00014], [20000000, 0.00013], [100000000, 0.00012], [200000000, 0.0001]]}}, "LTC/USD:USD": {"id": "LTCUSD-PERP", "lowercaseId": null, "symbol": "LTC/USD:USD", "base": "LTC", "quote": "USD", "settle": "USD", "baseId": "LTC", "quoteId": "USD", "settleId": "USD", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": 0.004, "maker": 0.004, "contractSize": 1, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"price": 0.001, "amount": 0.01}, "limits": {"leverage": {"min": 1, "max": 50}, "amount": {"min": null, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"symbol": "LTCUSD-PERP", "inst_type": "PERPETUAL_SWAP", "display_name": "LTCUSD Perpetual", "base_ccy": "LTC", "quote_ccy": "USD", "quote_decimals": 3, "quantity_decimals": 2, "price_tick_size": "0.001", "qty_tick_size": "0.01", "max_leverage": "50", "tradable": true, "expiry_timestamp_ms": 0, "beta_product": false, "underlying_symbol": "LTCUSD-INDEX", "contract_size": "1", "margin_buy_enabled": false, "margin_sell_enabled": false}, "tierBased": null, "percentage": null, "tiers": {"maker": [[0, 0.004], [25000, 0.0035], [50000, 0.0015], [100000, 0.001], [250000, 0.0009], [1000000, 0.0008], [20000000, 0.0007], [100000000, 0.0006], [200000000, 0.0004]], "taker": [[0, 0.004], [25000, 0.0035], [50000, 0.0025], [100000, 0.0016], [250000, 0.00015], [1000000, 0.00014], [20000000, 0.00013], [100000000, 0.00012], [200000000, 0.0001]]}}, "ADA/USD:USD": {"id": "ADAUSD-PERP", "lowercaseId": null, "symbol": "ADA/USD:USD", "base": "ADA", "quote": "USD", "settle": "USD", "baseId": "ADA", "quoteId": "USD", "settleId": "USD", "type": "swap", "spot": false, "margin": false, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": 0.004, "maker": 0.004, "contractSize": 1, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"price": 1e-05, "amount": 1}, "limits": {"leverage": {"min": 1, "max": 50}, "amount": {"min": null, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"symbol": "ADAUSD-PERP", "inst_type": "PERPETUAL_SWAP", "display_name": "ADAUSD Perpetual", "base_ccy": "ADA", "quote_ccy": "USD", "quote_decimals": 5, "quantity_decimals": 0, "price_tick_size": "0.00001", "qty_tick_size": "1", "max_leverage": "50", "tradable": true, "expiry_timestamp_ms": 0, "beta_product": false, "underlying_symbol": "ADAUSD-INDEX", "contract_size": "1", "margin_buy_enabled": false, "margin_sell_enabled": false}, "tierBased": null, "percentage": null, "tiers": {"maker": [[0, 0.004], [25000, 0.0035], [50000, 0.0015], [100000, 0.001], [250000, 0.0009], [1000000, 0.0008], [20000000, 0.0007], [100000000, 0.0006], [200000000, 0.0004]], "taker": [[0, 0.004], [25000, 0.0035], [50000, 0.0025], [100000, 0.0016], [250000, 0.00015], [1000000, 0.00014], [20000000, 0.00013], [100000000, 0.00012], [200000000, 0.0001]]}}}