{"BTC/USDT": {"id": "BTCUSDT", "lowercaseId": null, "symbol": "BTC/USDT", "base": "BTC", "quote": "USDT", "settle": null, "baseId": "BTC", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": true, "swap": false, "future": false, "option": false, "index": false, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.001, "maker": 0.001, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1e-06, "price": 0.1}, "limits": {"leverage": {"min": 1, "max": null}, "amount": {"min": 1.1e-05, "max": 83}, "price": {"min": null, "max": null}, "cost": {"min": 5, "max": 8000000}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"symbol": "BTCUSDT", "baseCoin": "BTC", "quoteCoin": "USDT", "innovation": "0", "status": "Trading", "marginTrading": "utaOnly", "stTag": "0", "lotSizeFilter": {"basePrecision": "0.000001", "quotePrecision": "0.0000001", "minOrderQty": "0.000011", "maxOrderQty": "83", "minOrderAmt": "5", "maxOrderAmt": "8000000"}, "priceFilter": {"tickSize": "0.1"}, "riskParameters": {"priceLimitRatioX": "0.01", "priceLimitRatioY": "0.02"}}, "tierBased": true, "percentage": true, "feeSide": "get"}, "ETH/USDT": {"id": "ETHUSDT", "lowercaseId": null, "symbol": "ETH/USDT", "base": "ETH", "quote": "USDT", "settle": null, "baseId": "ETH", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": true, "swap": false, "future": false, "option": false, "index": false, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.001, "maker": 0.001, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1e-05, "price": 0.01}, "limits": {"leverage": {"min": 1, "max": null}, "amount": {"min": 0.00029, "max": 4616.1353715}, "price": {"min": null, "max": null}, "cost": {"min": 5, "max": 7000000}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"symbol": "ETHUSDT", "baseCoin": "ETH", "quoteCoin": "USDT", "innovation": "0", "status": "Trading", "marginTrading": "utaOnly", "stTag": "0", "lotSizeFilter": {"basePrecision": "0.00001", "quotePrecision": "0.0000001", "minOrderQty": "0.00029", "maxOrderQty": "4616.1353715", "minOrderAmt": "5", "maxOrderAmt": "7000000"}, "priceFilter": {"tickSize": "0.01"}, "riskParameters": {"priceLimitRatioX": "0.01", "priceLimitRatioY": "0.03"}}, "tierBased": true, "percentage": true, "feeSide": "get"}, "ADA/USDT": {"id": "ADAUSDT", "lowercaseId": null, "symbol": "ADA/USDT", "base": "ADA", "quote": "USDT", "settle": null, "baseId": "ADA", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": true, "swap": false, "future": false, "option": false, "index": false, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.001, "maker": 0.001, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.01, "price": 0.0001}, "limits": {"leverage": {"min": 1, "max": null}, "amount": {"min": 1.07, "max": 7591573.353578}, "price": {"min": null, "max": null}, "cost": {"min": 5, "max": 4000000}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"symbol": "ADAUSDT", "baseCoin": "ADA", "quoteCoin": "USDT", "innovation": "0", "status": "Trading", "marginTrading": "utaOnly", "stTag": "0", "lotSizeFilter": {"basePrecision": "0.01", "quotePrecision": "0.000001", "minOrderQty": "1.07", "maxOrderQty": "7591573.353578", "minOrderAmt": "5", "maxOrderAmt": "4000000"}, "priceFilter": {"tickSize": "0.0001"}, "riskParameters": {"priceLimitRatioX": "0.05", "priceLimitRatioY": "0.1"}}, "tierBased": true, "percentage": true, "feeSide": "get"}, "LTC/USDT": {"id": "LTCUSDT", "lowercaseId": null, "symbol": "LTC/USDT", "base": "LTC", "quote": "USDT", "settle": null, "baseId": "LTC", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": true, "swap": false, "future": false, "option": false, "index": false, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.001, "maker": 0.001, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1e-05, "price": 0.01}, "limits": {"leverage": {"min": 1, "max": null}, "amount": {"min": 0.00923, "max": 22975.3015508}, "price": {"min": null, "max": null}, "cost": {"min": 5, "max": 600000}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"symbol": "LTCUSDT", "baseCoin": "LTC", "quoteCoin": "USDT", "innovation": "0", "status": "Trading", "marginTrading": "utaOnly", "stTag": "0", "lotSizeFilter": {"basePrecision": "0.00001", "quotePrecision": "0.0000001", "minOrderQty": "0.00923", "maxOrderQty": "22975.3015508", "minOrderAmt": "5", "maxOrderAmt": "600000"}, "priceFilter": {"tickSize": "0.01"}, "riskParameters": {"priceLimitRatioX": "0.05", "priceLimitRatioY": "0.1"}}, "tierBased": true, "percentage": true, "feeSide": "get"}, "XRP/USDT": {"id": "XRPUSDT", "lowercaseId": null, "symbol": "XRP/USDT", "base": "XRP", "quote": "USDT", "settle": null, "baseId": "XRP", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": true, "swap": false, "future": false, "option": false, "index": false, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.001, "maker": 0.001, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.01, "price": 0.0001}, "limits": {"leverage": {"min": 1, "max": null}, "amount": {"min": 0.44, "max": 2390343.014223}, "price": {"min": null, "max": null}, "cost": {"min": 5, "max": 4000000}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"symbol": "XRPUSDT", "baseCoin": "XRP", "quoteCoin": "USDT", "innovation": "0", "status": "Trading", "marginTrading": "utaOnly", "stTag": "0", "lotSizeFilter": {"basePrecision": "0.01", "quotePrecision": "0.000001", "minOrderQty": "0.44", "maxOrderQty": "2390343.014223", "minOrderAmt": "5", "maxOrderAmt": "4000000"}, "priceFilter": {"tickSize": "0.0001"}, "riskParameters": {"priceLimitRatioX": "0.02", "priceLimitRatioY": "0.06"}}, "tierBased": true, "percentage": true, "feeSide": "get"}, "DOGE/USDT": {"id": "DOGEUSDT", "lowercaseId": null, "symbol": "DOGE/USDT", "base": "DOGE", "quote": "USDT", "settle": null, "baseId": "DOGE", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": true, "swap": false, "future": false, "option": false, "index": false, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.001, "maker": 0.001, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.1, "price": 1e-05}, "limits": {"leverage": {"min": 1, "max": null}, "amount": {"min": 3, "max": 22340128.455739}, "price": {"min": null, "max": null}, "cost": {"min": 5, "max": 4000000}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"symbol": "DOGEUSDT", "baseCoin": "DOGE", "quoteCoin": "USDT", "innovation": "0", "status": "Trading", "marginTrading": "utaOnly", "stTag": "0", "lotSizeFilter": {"basePrecision": "0.1", "quotePrecision": "0.000001", "minOrderQty": "3", "maxOrderQty": "22340128.455739", "minOrderAmt": "5", "maxOrderAmt": "4000000"}, "priceFilter": {"tickSize": "0.00001"}, "riskParameters": {"priceLimitRatioX": "0.03", "priceLimitRatioY": "0.06"}}, "tierBased": true, "percentage": true, "feeSide": "get"}, "TRX/USDT": {"id": "TRXUSDT", "lowercaseId": null, "symbol": "TRX/USDT", "base": "TRX", "quote": "USDT", "settle": null, "baseId": "TRX", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": true, "swap": false, "future": false, "option": false, "index": false, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.001, "maker": 0.001, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.01, "price": 0.0001}, "limits": {"leverage": {"min": 1, "max": null}, "amount": {"min": 3.91, "max": 11125945.705385}, "price": {"min": null, "max": null}, "cost": {"min": 5, "max": 3000000}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"symbol": "TRXUSDT", "baseCoin": "TRX", "quoteCoin": "USDT", "innovation": "0", "status": "Trading", "marginTrading": "utaOnly", "stTag": "0", "lotSizeFilter": {"basePrecision": "0.01", "quotePrecision": "0.000001", "minOrderQty": "3.91", "maxOrderQty": "11125945.705385", "minOrderAmt": "5", "maxOrderAmt": "3000000"}, "priceFilter": {"tickSize": "0.0001"}, "riskParameters": {"priceLimitRatioX": "0.03", "priceLimitRatioY": "0.06"}}, "tierBased": true, "percentage": true, "feeSide": "get"}, "BTC/USDT:USDT": {"id": "BTCUSDT", "lowercaseId": null, "symbol": "BTC/USDT:USDT", "base": "BTC", "quote": "USDT", "settle": "USDT", "baseId": "BTC", "quoteId": "USDT", "settleId": "USDT", "type": "swap", "spot": false, "margin": null, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": 0.0006, "maker": 0.0001, "contractSize": 1, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.001, "price": 0.1}, "limits": {"leverage": {"min": 1, "max": 100}, "amount": {"min": 0.001, "max": 1190}, "price": {"min": 0.1, "max": 1999999.8}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": 1584230400000, "info": {"symbol": "BTCUSDT", "contractType": "LinearPerpetual", "status": "Trading", "baseCoin": "BTC", "quoteCoin": "USDT", "launchTime": "1584230400000", "deliveryTime": "0", "deliveryFeeRate": "", "priceScale": "2", "leverageFilter": {"minLeverage": "1", "maxLeverage": "100.00", "leverageStep": "0.01"}, "priceFilter": {"minPrice": "0.10", "maxPrice": "1999999.80", "tickSize": "0.10"}, "lotSizeFilter": {"maxOrderQty": "1190.000", "minOrderQty": "0.001", "qtyStep": "0.001", "postOnlyMaxOrderQty": "1190.000", "maxMktOrderQty": "119.000", "minNotionalValue": "5"}, "unifiedMarginTrade": true, "fundingInterval": "480", "settleCoin": "USDT", "copyTrading": "both", "upperFundingRate": "0.005", "lowerFundingRate": "-0.005", "isPreListing": false, "preListingInfo": null, "riskParameters": {"priceLimitRatioX": "0.01", "priceLimitRatioY": "0.02"}, "displayName": ""}, "tierBased": true, "percentage": true, "feeSide": "get"}, "BTC/USD:BTC": {"id": "BTCUSD", "lowercaseId": null, "symbol": "BTC/USD:BTC", "base": "BTC", "quote": "USD", "settle": "BTC", "baseId": "BTC", "quoteId": "USD", "settleId": "BTC", "type": "swap", "spot": false, "margin": null, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": false, "inverse": true, "subType": "inverse", "taker": 0.0006, "maker": 0.0001, "contractSize": 1, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1, "price": 0.1}, "limits": {"leverage": {"min": 1, "max": 100}, "amount": {"min": 1, "max": 25000000}, "price": {"min": 0.1, "max": 1999999.8}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": 1542211200000, "info": {"symbol": "BTCUSD", "contractType": "InversePerpetual", "status": "Trading", "baseCoin": "BTC", "quoteCoin": "USD", "launchTime": "1542211200000", "deliveryTime": "0", "deliveryFeeRate": "", "priceScale": "2", "leverageFilter": {"minLeverage": "1", "maxLeverage": "100.00", "leverageStep": "0.01"}, "priceFilter": {"minPrice": "0.10", "maxPrice": "1999999.80", "tickSize": "0.10"}, "lotSizeFilter": {"maxOrderQty": "25000000", "minOrderQty": "1", "qtyStep": "1", "postOnlyMaxOrderQty": "25000000", "maxMktOrderQty": "5000000", "minNotionalValue": ""}, "unifiedMarginTrade": true, "fundingInterval": "480", "settleCoin": "BTC", "copyTrading": "none", "upperFundingRate": "0.00375", "lowerFundingRate": "-0.00375", "isPreListing": false, "preListingInfo": null, "riskParameters": {"priceLimitRatioX": "0.05", "priceLimitRatioY": "0.1"}, "displayName": ""}, "tierBased": true, "percentage": true, "feeSide": "get"}, "ETH/USDT:USDT": {"id": "ETHUSDT", "lowercaseId": null, "symbol": "ETH/USDT:USDT", "base": "ETH", "quote": "USDT", "settle": "USDT", "baseId": "ETH", "quoteId": "USDT", "settleId": "USDT", "type": "swap", "spot": false, "margin": null, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": 0.0006, "maker": 0.0001, "contractSize": 1, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.01, "price": 0.01}, "limits": {"leverage": {"min": 1, "max": 100}, "amount": {"min": 0.01, "max": 7240}, "price": {"min": 0.01, "max": 199999.98}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": 1615766400000, "info": {"symbol": "ETHUSDT", "contractType": "LinearPerpetual", "status": "Trading", "baseCoin": "ETH", "quoteCoin": "USDT", "launchTime": "1615766400000", "deliveryTime": "0", "deliveryFeeRate": "", "priceScale": "2", "leverageFilter": {"minLeverage": "1", "maxLeverage": "100.00", "leverageStep": "0.01"}, "priceFilter": {"minPrice": "0.01", "maxPrice": "199999.98", "tickSize": "0.01"}, "lotSizeFilter": {"maxOrderQty": "7240.00", "minOrderQty": "0.01", "qtyStep": "0.01", "postOnlyMaxOrderQty": "7240.00", "maxMktOrderQty": "724.00", "minNotionalValue": "5"}, "unifiedMarginTrade": true, "fundingInterval": "480", "settleCoin": "USDT", "copyTrading": "both", "upperFundingRate": "0.005", "lowerFundingRate": "-0.005", "isPreListing": false, "preListingInfo": null, "riskParameters": {"priceLimitRatioX": "0.02", "priceLimitRatioY": "0.04"}, "displayName": ""}, "tierBased": true, "percentage": true, "feeSide": "get"}, "LTC/USDT:USDT": {"id": "LTCUSDT", "lowercaseId": null, "symbol": "LTC/USDT:USDT", "base": "LTC", "quote": "USDT", "settle": "USDT", "baseId": "LTC", "quoteId": "USDT", "settleId": "USDT", "type": "swap", "spot": false, "margin": null, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": 0.0006, "maker": 0.0001, "contractSize": 1, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.1, "price": 0.01}, "limits": {"leverage": {"min": 1, "max": 50}, "amount": {"min": 0.1, "max": 28740}, "price": {"min": 0.01, "max": 199999.98}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": 1514764800000, "info": {"symbol": "LTCUSDT", "contractType": "LinearPerpetual", "status": "Trading", "baseCoin": "LTC", "quoteCoin": "USDT", "launchTime": "1514764800000", "deliveryTime": "0", "deliveryFeeRate": "", "priceScale": "2", "leverageFilter": {"minLeverage": "1", "maxLeverage": "50.00", "leverageStep": "0.01"}, "priceFilter": {"minPrice": "0.01", "maxPrice": "199999.98", "tickSize": "0.01"}, "lotSizeFilter": {"maxOrderQty": "28740.0", "minOrderQty": "0.1", "qtyStep": "0.1", "postOnlyMaxOrderQty": "28740.0", "maxMktOrderQty": "6160.0", "minNotionalValue": "5"}, "unifiedMarginTrade": true, "fundingInterval": "480", "settleCoin": "USDT", "copyTrading": "both", "upperFundingRate": "0.01", "lowerFundingRate": "-0.01", "isPreListing": false, "preListingInfo": null, "riskParameters": {"priceLimitRatioX": "0.05", "priceLimitRatioY": "0.1"}, "displayName": ""}, "tierBased": true, "percentage": true, "feeSide": "get"}, "ADA/USDT:USDT": {"id": "ADAUSDT", "lowercaseId": null, "symbol": "ADA/USDT:USDT", "base": "ADA", "quote": "USDT", "settle": "USDT", "baseId": "ADA", "quoteId": "USDT", "settleId": "USDT", "type": "swap", "spot": false, "margin": null, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": 0.0006, "maker": 0.0001, "contractSize": 1, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1, "price": 0.0001}, "limits": {"leverage": {"min": 1, "max": 75}, "amount": {"min": 1, "max": 6315500}, "price": {"min": 0.0001, "max": 1999.9998}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": 1610668800000, "info": {"symbol": "ADAUSDT", "contractType": "LinearPerpetual", "status": "Trading", "baseCoin": "ADA", "quoteCoin": "USDT", "launchTime": "1610668800000", "deliveryTime": "0", "deliveryFeeRate": "", "priceScale": "4", "leverageFilter": {"minLeverage": "1", "maxLeverage": "75.00", "leverageStep": "0.01"}, "priceFilter": {"minPrice": "0.0001", "maxPrice": "1999.9998", "tickSize": "0.0001"}, "lotSizeFilter": {"maxOrderQty": "6315500", "minOrderQty": "1", "qtyStep": "1", "postOnlyMaxOrderQty": "6315500", "maxMktOrderQty": "1109600", "minNotionalValue": "5"}, "unifiedMarginTrade": true, "fundingInterval": "480", "settleCoin": "USDT", "copyTrading": "both", "upperFundingRate": "0.0058", "lowerFundingRate": "-0.0058", "isPreListing": false, "preListingInfo": null, "riskParameters": {"priceLimitRatioX": "0.05", "priceLimitRatioY": "0.1"}, "displayName": ""}, "tierBased": true, "percentage": true, "feeSide": "get"}, "XRP/USDT:USDT": {"id": "XRPUSDT", "lowercaseId": null, "symbol": "XRP/USDT:USDT", "base": "XRP", "quote": "USDT", "settle": "USDT", "baseId": "XRP", "quoteId": "USDT", "settleId": "USDT", "type": "swap", "spot": false, "margin": null, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": 0.0006, "maker": 0.0001, "contractSize": 1, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1, "price": 0.0001}, "limits": {"leverage": {"min": 1, "max": 75}, "amount": {"min": 1, "max": 10965300}, "price": {"min": 0.0001, "max": 1999.9998}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": 1620898440000, "info": {"symbol": "XRPUSDT", "contractType": "LinearPerpetual", "status": "Trading", "baseCoin": "XRP", "quoteCoin": "USDT", "launchTime": "1620898440000", "deliveryTime": "0", "deliveryFeeRate": "", "priceScale": "4", "leverageFilter": {"minLeverage": "1", "maxLeverage": "75.00", "leverageStep": "0.01"}, "priceFilter": {"minPrice": "0.0001", "maxPrice": "1999.9998", "tickSize": "0.0001"}, "lotSizeFilter": {"maxOrderQty": "10965300", "minOrderQty": "1", "qtyStep": "1", "postOnlyMaxOrderQty": "10965300", "maxMktOrderQty": "2467800", "minNotionalValue": "5"}, "unifiedMarginTrade": true, "fundingInterval": "480", "settleCoin": "USDT", "copyTrading": "both", "upperFundingRate": "0.0058", "lowerFundingRate": "-0.0058", "isPreListing": false, "preListingInfo": null, "riskParameters": {"priceLimitRatioX": "0.05", "priceLimitRatioY": "0.1"}, "displayName": ""}, "tierBased": true, "percentage": true, "feeSide": "get"}, "DOGE/USDT:USDT": {"id": "DOGEUSDT", "lowercaseId": null, "symbol": "DOGE/USDT:USDT", "base": "DOGE", "quote": "USDT", "settle": "USDT", "baseId": "DOGE", "quoteId": "USDT", "settleId": "USDT", "type": "swap", "spot": false, "margin": null, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": 0.0006, "maker": 0.0001, "contractSize": 1, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1, "price": 1e-05}, "limits": {"leverage": {"min": 1, "max": 75}, "amount": {"min": 1, "max": 43678900}, "price": {"min": 1e-05, "max": 199.99998}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": 1622630640000, "info": {"symbol": "DOGEUSDT", "contractType": "LinearPerpetual", "status": "Trading", "baseCoin": "DOGE", "quoteCoin": "USDT", "launchTime": "1622630640000", "deliveryTime": "0", "deliveryFeeRate": "", "priceScale": "5", "leverageFilter": {"minLeverage": "1", "maxLeverage": "75.00", "leverageStep": "0.01"}, "priceFilter": {"minPrice": "0.00001", "maxPrice": "199.99998", "tickSize": "0.00001"}, "lotSizeFilter": {"maxOrderQty": "43678900", "minOrderQty": "1", "qtyStep": "1", "postOnlyMaxOrderQty": "43678900", "maxMktOrderQty": "5991100", "minNotionalValue": "5"}, "unifiedMarginTrade": true, "fundingInterval": "480", "settleCoin": "USDT", "copyTrading": "both", "upperFundingRate": "0.0058", "lowerFundingRate": "-0.0058", "isPreListing": false, "preListingInfo": null, "riskParameters": {"priceLimitRatioX": "0.05", "priceLimitRatioY": "0.1"}, "displayName": ""}, "tierBased": true, "percentage": true, "feeSide": "get"}, "TRX/USDT:USDT": {"id": "TRXUSDT", "lowercaseId": null, "symbol": "TRX/USDT:USDT", "base": "TRX", "quote": "USDT", "settle": "USDT", "baseId": "TRX", "quoteId": "USDT", "settleId": "USDT", "type": "swap", "spot": false, "margin": null, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": 0.0006, "maker": 0.0001, "contractSize": 1, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1, "price": 1e-05}, "limits": {"leverage": {"min": 1, "max": 75}, "amount": {"min": 1, "max": 5417800}, "price": {"min": 1e-05, "max": 199.99998}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": 1628726400000, "info": {"symbol": "TRXUSDT", "contractType": "LinearPerpetual", "status": "Trading", "baseCoin": "TRX", "quoteCoin": "USDT", "launchTime": "1628726400000", "deliveryTime": "0", "deliveryFeeRate": "", "priceScale": "5", "leverageFilter": {"minLeverage": "1", "maxLeverage": "75.00", "leverageStep": "0.01"}, "priceFilter": {"minPrice": "0.00001", "maxPrice": "199.99998", "tickSize": "0.00001"}, "lotSizeFilter": {"maxOrderQty": "5417800", "minOrderQty": "1", "qtyStep": "1", "postOnlyMaxOrderQty": "5417800", "maxMktOrderQty": "1356000", "minNotionalValue": "5"}, "unifiedMarginTrade": true, "fundingInterval": "480", "settleCoin": "USDT", "copyTrading": "both", "upperFundingRate": "0.0058", "lowerFundingRate": "-0.0058", "isPreListing": false, "preListingInfo": null, "riskParameters": {"priceLimitRatioX": "0.05", "priceLimitRatioY": "0.1"}, "displayName": ""}, "tierBased": true, "percentage": true, "feeSide": "get"}, "LTC/USD:LTC": {"id": "LTCUSD", "lowercaseId": null, "symbol": "LTC/USD:LTC", "base": "LTC", "quote": "USD", "settle": "LTC", "baseId": "LTC", "quoteId": "USD", "settleId": "LTC", "type": "swap", "spot": false, "margin": null, "swap": true, "future": false, "option": false, "index": null, "active": true, "contract": true, "linear": false, "inverse": true, "subType": "inverse", "taker": 0.0006, "maker": 0.0001, "contractSize": 1, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1, "price": 0.01}, "limits": {"leverage": {"min": 1, "max": 50}, "amount": {"min": 1, "max": 400000}, "price": {"min": 0.01, "max": 199999.98}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": 1648684800000, "info": {"symbol": "LTCUSD", "contractType": "InversePerpetual", "status": "Trading", "baseCoin": "LTC", "quoteCoin": "USD", "launchTime": "1648684800000", "deliveryTime": "0", "deliveryFeeRate": "", "priceScale": "2", "leverageFilter": {"minLeverage": "1", "maxLeverage": "50.00", "leverageStep": "0.01"}, "priceFilter": {"minPrice": "0.01", "maxPrice": "199999.98", "tickSize": "0.01"}, "lotSizeFilter": {"maxOrderQty": "400000", "minOrderQty": "1", "qtyStep": "1", "postOnlyMaxOrderQty": "400000", "maxMktOrderQty": "80000", "minNotionalValue": ""}, "unifiedMarginTrade": true, "fundingInterval": "480", "settleCoin": "LTC", "copyTrading": "none", "upperFundingRate": "0.0075", "lowerFundingRate": "-0.0075", "isPreListing": false, "preListingInfo": null, "riskParameters": {"priceLimitRatioX": "0.05", "priceLimitRatioY": "0.1"}, "displayName": ""}, "tierBased": true, "percentage": true, "feeSide": "get"}, "BTC/USDT:USDT-250530-70000-C": {"id": "BTC-30MAY25-70000-C-USDT", "symbol": "BTC/USDT:USDT-250530-70000-C", "base": "BTC", "quote": "USDT", "settle": "USDT", "baseId": "BTC", "quoteId": "USDT", "settleId": "USDT", "type": "option", "spot": false, "margin": false, "swap": false, "future": false, "option": true, "active": false, "contract": true, "linear": null, "inverse": null, "contractSize": 1, "expiry": 1748592000000, "expiryDatetime": "2025-05-30T08:00:00.000Z", "strike": 70000, "optionType": "call", "precision": {"amount": 0.01, "price": 5}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": null, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": null, "max": null}}, "info": null}, "ETH/USDT:USDT-250530-1700-P": {"id": "ETH-30MAY25-1700-P-USDT", "symbol": "ETH/USDT:USDT-250530-1700-P", "base": "ETH", "quote": "USDT", "settle": "USDT", "baseId": "ETH", "quoteId": "USDT", "settleId": "USDT", "type": "option", "spot": false, "margin": false, "swap": false, "future": false, "option": true, "active": false, "contract": true, "linear": null, "inverse": null, "contractSize": 1, "expiry": 1748592000000, "expiryDatetime": "2025-05-30T08:00:00.000Z", "strike": 1700, "optionType": "put", "precision": {"amount": 0.1, "price": 0.1}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": null, "max": null}, "price": {"min": null, "max": null}, "cost": {"min": null, "max": null}}, "info": null}, "SOL/USDT:USDT-250530-90-P": {"id": "SOL-30MAY25-90-P-USDT", "lowercaseId": null, "symbol": "SOL/USDT:USDT-250530-90-P", "base": "SOL", "quote": "USDT", "settle": "USDT", "baseId": "SOL", "quoteId": "USDT", "settleId": "USDT", "type": "option", "spot": false, "margin": false, "swap": false, "future": false, "option": true, "index": null, "active": true, "contract": true, "linear": true, "inverse": false, "subType": "linear", "taker": 0.0006, "maker": 0.0001, "contractSize": 1, "expiry": 1748592000000, "expiryDatetime": "2025-05-30T08:00:00.000Z", "strike": 90, "optionType": "put", "precision": {"amount": 1, "price": 0.01}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 1, "max": 100000}, "price": {"min": 0.01, "max": 3000}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": 1744009921396, "info": {"symbol": "SOL-30MAY25-90-P-USDT", "status": "Trading", "baseCoin": "SOL", "quoteCoin": "USDT", "settleCoin": "USDT", "optionsType": "Put", "launchTime": "1744009921396", "deliveryTime": "1748592000000", "deliveryFeeRate": "0.0002", "priceFilter": {"minPrice": "0.01", "maxPrice": "3000", "tickSize": "0.01"}, "lotSizeFilter": {"maxOrderQty": "100000", "minOrderQty": "1", "qtyStep": "1"}, "displayName": "SOLUSDT-30MAY25-90-P"}, "tierBased": true, "percentage": true, "feeSide": "get"}}