{"BTC/USDT": {"id": "BTC_USDT", "lowercaseId": null, "symbol": "BTC/USDT", "base": "BTC", "quote": "USDT", "settle": null, "baseId": "BTC", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "index": false, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.02, "maker": 0.02, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.01, "price": 1e-06}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 5, "max": 10000000}, "price": {"min": 5e-05, "max": 4}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"id": "01HSBPVS17CT2GN2FV34K3AMP9", "symbol": "BTC_USDT", "baseCurrency": "BTC", "quoteCurrency": "USDT", "baseMinSize": "0.00005000", "quoteMinSize": "5.00000000", "baseMaxSize": "4.00000000", "quoteMaxSize": "10000000.00000000", "basePrec": "6", "quotePrec": "2", "baseCurrencyFullName": "Bitcoin", "quoteCurrencyFullName": "Tether USD"}, "tierBased": null, "percentage": true, "feeSide": "get"}, "LTC/USDT": {"id": "LTC_USDT", "lowercaseId": null, "symbol": "LTC/USDT", "base": "LTC", "quote": "USDT", "settle": null, "baseId": "LTC", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "index": false, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.02, "maker": 0.02, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.01, "price": 1e-05}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 5, "max": 100000000}, "price": {"min": 0.05462, "max": 200}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"id": "01HSBPVS17CT2GN2FV34K3AMPE", "symbol": "LTC_USDT", "baseCurrency": "LTC", "quoteCurrency": "USDT", "baseMinSize": "0.05462000", "quoteMinSize": "5.00000000", "baseMaxSize": "200.00000000", "quoteMaxSize": "100000000.00000000", "basePrec": "5", "quotePrec": "2", "baseCurrencyFullName": "Litecoin", "quoteCurrencyFullName": "Tether USD"}, "tierBased": null, "percentage": true, "feeSide": "get"}, "SOL/USDT": {"id": "SOL_USDT", "lowercaseId": null, "symbol": "SOL/USDT", "base": "SOL", "quote": "USDT", "settle": null, "baseId": "SOL", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "index": false, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.02, "maker": 0.02, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.0001, "price": 0.0001}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 5, "max": 10000000}, "price": {"min": 0.0311, "max": 500}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"id": "01HSBPVS17CT2GN2FV34K3AMPC", "symbol": "SOL_USDT", "baseCurrency": "SOL", "quoteCurrency": "USDT", "baseMinSize": "0.03110000", "quoteMinSize": "5.00000000", "baseMaxSize": "500.00000000", "quoteMaxSize": "10000000.00000000", "basePrec": "4", "quotePrec": "4", "baseCurrencyFullName": "Solana", "quoteCurrencyFullName": "Tether USD"}, "tierBased": null, "percentage": true, "feeSide": "get"}, "TRX/USDT": {"id": "TRX_USDT", "lowercaseId": null, "symbol": "TRX/USDT", "base": "TRX", "quote": "USDT", "settle": null, "baseId": "TRX", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "index": false, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.02, "maker": 0.02, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 1e-06, "price": 0.01}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 5, "max": 10000000}, "price": {"min": 19.12, "max": 100000}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"id": "01HSBPVS17CT2GN2FV34K3AMPY", "symbol": "TRX_USDT", "baseCurrency": "TRX", "quoteCurrency": "USDT", "baseMinSize": "19.12000000", "quoteMinSize": "5.00000000", "baseMaxSize": "100000.00000000", "quoteMaxSize": "10000000.00000000", "basePrec": "2", "quotePrec": "6", "baseCurrencyFullName": "Tron", "quoteCurrencyFullName": "Tether USD"}, "tierBased": null, "percentage": true, "feeSide": "get"}, "ETH/USDT": {"id": "ETH_USDT", "lowercaseId": null, "symbol": "ETH/USDT", "base": "ETH", "quote": "USDT", "settle": null, "baseId": "ETH", "quoteId": "USDT", "settleId": null, "type": "spot", "spot": true, "margin": false, "swap": false, "future": false, "option": false, "index": false, "active": true, "contract": false, "linear": null, "inverse": null, "subType": null, "taker": 0.02, "maker": 0.02, "contractSize": null, "expiry": null, "expiryDatetime": null, "strike": null, "optionType": null, "precision": {"amount": 0.01, "price": 1e-05}, "limits": {"leverage": {"min": null, "max": null}, "amount": {"min": 5, "max": 100000000}, "price": {"min": 0.00209, "max": 100}, "cost": {"min": null, "max": null}}, "marginModes": {"cross": null, "isolated": null}, "created": null, "info": {"id": "01HSBPVS17CT2GN2FV34K3AMPW", "symbol": "ETH_USDT", "baseCurrency": "ETH", "quoteCurrency": "USDT", "baseMinSize": "0.00209000", "quoteMinSize": "5.00000000", "baseMaxSize": "100.00000000", "quoteMaxSize": "100000000.00000000", "basePrec": "5", "quotePrec": "2", "baseCurrencyFullName": "Ethereum", "quoteCurrencyFullName": "Tether USD"}, "tierBased": null, "percentage": true, "feeSide": "get"}}