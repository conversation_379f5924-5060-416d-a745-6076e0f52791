{
  "env": {
      "node": true
  },
  "parserOptions": {
      "ecmaVersion": 2020,
      "sourceType": "script"
  },
  "extends": [ "eslint:recommended" ],
  "rules": {
      "semi": [ "error", "always" ],
      "no-unused-vars": [ "off" ],
      "quotes": [ "off", "single" ],
      "func-call-spacing": [ "error", "always" ],
      "one-var": "off",
      "indent": [ "error", 4 ],
      "comma-style": "off",
      "no-multi-spaces": "off",
      "comma-dangle": "off",
      "spaced-comment": "off",
      "camelcase": "off",
      "padded-blocks": "off",
      "multiline-comment-style": "off",
      "curly": "off",
      "rest-spread-spacing": "off",
      "no-multiple-empty-lines": "off",
      "no-undef-init": "off",
      "no-useless-return": "off",
      "no-console": "off",
      "operator-linebreak": "off",
      "key-spacing": "off",
      "brace-style": "off",
      "no-path-concat": "off",
      "no-use-before-define": "off",
      "new-cap": "off",
      "max-classes-per-file": [ "error", 2 ],
      "lines-between-class-members": "off",
      "padding-line-between-statements": [ "off",
          { "blankLine": "always", "prev": "function", "next": "*" },
          { "blankLine": "always", "prev": "directive", "next": "*" },
          { "blankLine": "always", "prev": "*", "next": "cjs-export" }
      ],
      "import/no-extraneous-dependencies": [ "error", { "devDependencies": true } ],
      "jsdoc/require-jsdoc": "off",
      "no-shadow": ["error", {
        "builtinGlobals": true, // prevent shadowing global vars (i.e. Object, Array, Number, etc...)
        "hoist": "all", // just for cleanest practices
        "allow": [], // no need to allow any
        "ignoreOnInitialization": false // don't ignore lambdas and etc.
      }]
  }
}
