// -------------------------------------------------------------------------------

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

// -------------------------------------------------------------------------------

import { implicitReturnType } from '../base/types.js';
import { Exchange as _Exchange } from '../base/Exchange.js';

interface Exchange {
    sapiGetCopyTradingFuturesUserStatus (params?: {}): Promise<implicitReturnType>;
    sapiGetCopyTradingFuturesLeadSymbol (params?: {}): Promise<implicitReturnType>;
    sapiGetSystemStatus (params?: {}): Promise<implicitReturnType>;
    sapiGetAccountSnapshot (params?: {}): Promise<implicitReturnType>;
    sapiGetAccountInfo (params?: {}): Promise<implicitReturnType>;
    sapiGetMarginAsset (params?: {}): Promise<implicitReturnType>;
    sapiGetMarginPair (params?: {}): Promise<implicitReturnType>;
    sapiGetMarginAllAssets (params?: {}): Promise<implicitReturnType>;
    sapiGetMarginAllPairs (params?: {}): Promise<implicitReturnType>;
    sapiGetMarginPriceIndex (params?: {}): Promise<implicitReturnType>;
    sapiGetSpotDelistSchedule (params?: {}): Promise<implicitReturnType>;
    sapiGetAssetAssetDividend (params?: {}): Promise<implicitReturnType>;
    sapiGetAssetDribblet (params?: {}): Promise<implicitReturnType>;
    sapiGetAssetTransfer (params?: {}): Promise<implicitReturnType>;
    sapiGetAssetAssetDetail (params?: {}): Promise<implicitReturnType>;
    sapiGetAssetTradeFee (params?: {}): Promise<implicitReturnType>;
    sapiGetAssetLedgerTransferCloudMiningQueryByPage (params?: {}): Promise<implicitReturnType>;
    sapiGetAssetConvertTransferQueryByPage (params?: {}): Promise<implicitReturnType>;
    sapiGetAssetWalletBalance (params?: {}): Promise<implicitReturnType>;
    sapiGetAssetCustodyTransferHistory (params?: {}): Promise<implicitReturnType>;
    sapiGetMarginBorrowRepay (params?: {}): Promise<implicitReturnType>;
    sapiGetMarginLoan (params?: {}): Promise<implicitReturnType>;
    sapiGetMarginRepay (params?: {}): Promise<implicitReturnType>;
    sapiGetMarginAccount (params?: {}): Promise<implicitReturnType>;
    sapiGetMarginTransfer (params?: {}): Promise<implicitReturnType>;
    sapiGetMarginInterestHistory (params?: {}): Promise<implicitReturnType>;
    sapiGetMarginForceLiquidationRec (params?: {}): Promise<implicitReturnType>;
    sapiGetMarginOrder (params?: {}): Promise<implicitReturnType>;
    sapiGetMarginOpenOrders (params?: {}): Promise<implicitReturnType>;
    sapiGetMarginAllOrders (params?: {}): Promise<implicitReturnType>;
    sapiGetMarginMyTrades (params?: {}): Promise<implicitReturnType>;
    sapiGetMarginMaxBorrowable (params?: {}): Promise<implicitReturnType>;
    sapiGetMarginMaxTransferable (params?: {}): Promise<implicitReturnType>;
    sapiGetMarginTradeCoeff (params?: {}): Promise<implicitReturnType>;
    sapiGetMarginIsolatedTransfer (params?: {}): Promise<implicitReturnType>;
    sapiGetMarginIsolatedAccount (params?: {}): Promise<implicitReturnType>;
    sapiGetMarginIsolatedPair (params?: {}): Promise<implicitReturnType>;
    sapiGetMarginIsolatedAllPairs (params?: {}): Promise<implicitReturnType>;
    sapiGetMarginIsolatedAccountLimit (params?: {}): Promise<implicitReturnType>;
    sapiGetMarginInterestRateHistory (params?: {}): Promise<implicitReturnType>;
    sapiGetMarginOrderList (params?: {}): Promise<implicitReturnType>;
    sapiGetMarginAllOrderList (params?: {}): Promise<implicitReturnType>;
    sapiGetMarginOpenOrderList (params?: {}): Promise<implicitReturnType>;
    sapiGetMarginCrossMarginData (params?: {}): Promise<implicitReturnType>;
    sapiGetMarginIsolatedMarginData (params?: {}): Promise<implicitReturnType>;
    sapiGetMarginIsolatedMarginTier (params?: {}): Promise<implicitReturnType>;
    sapiGetMarginRateLimitOrder (params?: {}): Promise<implicitReturnType>;
    sapiGetMarginDribblet (params?: {}): Promise<implicitReturnType>;
    sapiGetMarginDust (params?: {}): Promise<implicitReturnType>;
    sapiGetMarginCrossMarginCollateralRatio (params?: {}): Promise<implicitReturnType>;
    sapiGetMarginExchangeSmallLiability (params?: {}): Promise<implicitReturnType>;
    sapiGetMarginExchangeSmallLiabilityHistory (params?: {}): Promise<implicitReturnType>;
    sapiGetMarginNextHourlyInterestRate (params?: {}): Promise<implicitReturnType>;
    sapiGetMarginCapitalFlow (params?: {}): Promise<implicitReturnType>;
    sapiGetMarginDelistSchedule (params?: {}): Promise<implicitReturnType>;
    sapiGetMarginAvailableInventory (params?: {}): Promise<implicitReturnType>;
    sapiGetMarginLeverageBracket (params?: {}): Promise<implicitReturnType>;
    sapiGetLoanVipLoanableData (params?: {}): Promise<implicitReturnType>;
    sapiGetLoanVipCollateralData (params?: {}): Promise<implicitReturnType>;
    sapiGetLoanVipRequestData (params?: {}): Promise<implicitReturnType>;
    sapiGetLoanVipRequestInterestRate (params?: {}): Promise<implicitReturnType>;
    sapiGetLoanIncome (params?: {}): Promise<implicitReturnType>;
    sapiGetLoanOngoingOrders (params?: {}): Promise<implicitReturnType>;
    sapiGetLoanLtvAdjustmentHistory (params?: {}): Promise<implicitReturnType>;
    sapiGetLoanBorrowHistory (params?: {}): Promise<implicitReturnType>;
    sapiGetLoanRepayHistory (params?: {}): Promise<implicitReturnType>;
    sapiGetLoanLoanableData (params?: {}): Promise<implicitReturnType>;
    sapiGetLoanCollateralData (params?: {}): Promise<implicitReturnType>;
    sapiGetLoanRepayCollateralRate (params?: {}): Promise<implicitReturnType>;
    sapiGetLoanFlexibleOngoingOrders (params?: {}): Promise<implicitReturnType>;
    sapiGetLoanFlexibleBorrowHistory (params?: {}): Promise<implicitReturnType>;
    sapiGetLoanFlexibleRepayHistory (params?: {}): Promise<implicitReturnType>;
    sapiGetLoanFlexibleLtvAdjustmentHistory (params?: {}): Promise<implicitReturnType>;
    sapiGetLoanVipOngoingOrders (params?: {}): Promise<implicitReturnType>;
    sapiGetLoanVipRepayHistory (params?: {}): Promise<implicitReturnType>;
    sapiGetLoanVipCollateralAccount (params?: {}): Promise<implicitReturnType>;
    sapiGetFiatOrders (params?: {}): Promise<implicitReturnType>;
    sapiGetFiatPayments (params?: {}): Promise<implicitReturnType>;
    sapiGetFuturesTransfer (params?: {}): Promise<implicitReturnType>;
    sapiGetFuturesHistDataLink (params?: {}): Promise<implicitReturnType>;
    sapiGetRebateTaxQuery (params?: {}): Promise<implicitReturnType>;
    sapiGetCapitalConfigGetall (params?: {}): Promise<implicitReturnType>;
    sapiGetCapitalDepositAddress (params?: {}): Promise<implicitReturnType>;
    sapiGetCapitalDepositAddressList (params?: {}): Promise<implicitReturnType>;
    sapiGetCapitalDepositHisrec (params?: {}): Promise<implicitReturnType>;
    sapiGetCapitalDepositSubAddress (params?: {}): Promise<implicitReturnType>;
    sapiGetCapitalDepositSubHisrec (params?: {}): Promise<implicitReturnType>;
    sapiGetCapitalWithdrawHistory (params?: {}): Promise<implicitReturnType>;
    sapiGetCapitalWithdrawAddressList (params?: {}): Promise<implicitReturnType>;
    sapiGetCapitalContractConvertibleCoins (params?: {}): Promise<implicitReturnType>;
    sapiGetConvertTradeFlow (params?: {}): Promise<implicitReturnType>;
    sapiGetConvertExchangeInfo (params?: {}): Promise<implicitReturnType>;
    sapiGetConvertAssetInfo (params?: {}): Promise<implicitReturnType>;
    sapiGetConvertOrderStatus (params?: {}): Promise<implicitReturnType>;
    sapiGetConvertLimitQueryOpenOrders (params?: {}): Promise<implicitReturnType>;
    sapiGetAccountStatus (params?: {}): Promise<implicitReturnType>;
    sapiGetAccountApiTradingStatus (params?: {}): Promise<implicitReturnType>;
    sapiGetAccountApiRestrictionsIpRestriction (params?: {}): Promise<implicitReturnType>;
    sapiGetBnbBurn (params?: {}): Promise<implicitReturnType>;
    sapiGetSubAccountFuturesAccount (params?: {}): Promise<implicitReturnType>;
    sapiGetSubAccountFuturesAccountSummary (params?: {}): Promise<implicitReturnType>;
    sapiGetSubAccountFuturesPositionRisk (params?: {}): Promise<implicitReturnType>;
    sapiGetSubAccountFuturesInternalTransfer (params?: {}): Promise<implicitReturnType>;
    sapiGetSubAccountList (params?: {}): Promise<implicitReturnType>;
    sapiGetSubAccountMarginAccount (params?: {}): Promise<implicitReturnType>;
    sapiGetSubAccountMarginAccountSummary (params?: {}): Promise<implicitReturnType>;
    sapiGetSubAccountSpotSummary (params?: {}): Promise<implicitReturnType>;
    sapiGetSubAccountStatus (params?: {}): Promise<implicitReturnType>;
    sapiGetSubAccountSubTransferHistory (params?: {}): Promise<implicitReturnType>;
    sapiGetSubAccountTransferSubUserHistory (params?: {}): Promise<implicitReturnType>;
    sapiGetSubAccountUniversalTransfer (params?: {}): Promise<implicitReturnType>;
    sapiGetSubAccountApiRestrictionsIpRestrictionThirdPartyList (params?: {}): Promise<implicitReturnType>;
    sapiGetSubAccountTransactionStatistics (params?: {}): Promise<implicitReturnType>;
    sapiGetSubAccountSubAccountApiIpRestriction (params?: {}): Promise<implicitReturnType>;
    sapiGetManagedSubaccountAsset (params?: {}): Promise<implicitReturnType>;
    sapiGetManagedSubaccountAccountSnapshot (params?: {}): Promise<implicitReturnType>;
    sapiGetManagedSubaccountQueryTransLogForInvestor (params?: {}): Promise<implicitReturnType>;
    sapiGetManagedSubaccountQueryTransLogForTradeParent (params?: {}): Promise<implicitReturnType>;
    sapiGetManagedSubaccountFetchFutureAsset (params?: {}): Promise<implicitReturnType>;
    sapiGetManagedSubaccountMarginAsset (params?: {}): Promise<implicitReturnType>;
    sapiGetManagedSubaccountInfo (params?: {}): Promise<implicitReturnType>;
    sapiGetManagedSubaccountDepositAddress (params?: {}): Promise<implicitReturnType>;
    sapiGetManagedSubaccountQueryTransLog (params?: {}): Promise<implicitReturnType>;
    sapiGetLendingDailyProductList (params?: {}): Promise<implicitReturnType>;
    sapiGetLendingDailyUserLeftQuota (params?: {}): Promise<implicitReturnType>;
    sapiGetLendingDailyUserRedemptionQuota (params?: {}): Promise<implicitReturnType>;
    sapiGetLendingDailyTokenPosition (params?: {}): Promise<implicitReturnType>;
    sapiGetLendingUnionAccount (params?: {}): Promise<implicitReturnType>;
    sapiGetLendingUnionPurchaseRecord (params?: {}): Promise<implicitReturnType>;
    sapiGetLendingUnionRedemptionRecord (params?: {}): Promise<implicitReturnType>;
    sapiGetLendingUnionInterestHistory (params?: {}): Promise<implicitReturnType>;
    sapiGetLendingProjectList (params?: {}): Promise<implicitReturnType>;
    sapiGetLendingProjectPositionList (params?: {}): Promise<implicitReturnType>;
    sapiGetEthStakingEthHistoryStakingHistory (params?: {}): Promise<implicitReturnType>;
    sapiGetEthStakingEthHistoryRedemptionHistory (params?: {}): Promise<implicitReturnType>;
    sapiGetEthStakingEthHistoryRewardsHistory (params?: {}): Promise<implicitReturnType>;
    sapiGetEthStakingEthQuota (params?: {}): Promise<implicitReturnType>;
    sapiGetEthStakingEthHistoryRateHistory (params?: {}): Promise<implicitReturnType>;
    sapiGetEthStakingAccount (params?: {}): Promise<implicitReturnType>;
    sapiGetEthStakingWbethHistoryWrapHistory (params?: {}): Promise<implicitReturnType>;
    sapiGetEthStakingWbethHistoryUnwrapHistory (params?: {}): Promise<implicitReturnType>;
    sapiGetEthStakingEthHistoryWbethRewardsHistory (params?: {}): Promise<implicitReturnType>;
    sapiGetSolStakingSolHistoryStakingHistory (params?: {}): Promise<implicitReturnType>;
    sapiGetSolStakingSolHistoryRedemptionHistory (params?: {}): Promise<implicitReturnType>;
    sapiGetSolStakingSolHistoryBnsolRewardsHistory (params?: {}): Promise<implicitReturnType>;
    sapiGetSolStakingSolHistoryRateHistory (params?: {}): Promise<implicitReturnType>;
    sapiGetSolStakingAccount (params?: {}): Promise<implicitReturnType>;
    sapiGetSolStakingSolQuota (params?: {}): Promise<implicitReturnType>;
    sapiGetMiningPubAlgoList (params?: {}): Promise<implicitReturnType>;
    sapiGetMiningPubCoinList (params?: {}): Promise<implicitReturnType>;
    sapiGetMiningWorkerDetail (params?: {}): Promise<implicitReturnType>;
    sapiGetMiningWorkerList (params?: {}): Promise<implicitReturnType>;
    sapiGetMiningPaymentList (params?: {}): Promise<implicitReturnType>;
    sapiGetMiningStatisticsUserStatus (params?: {}): Promise<implicitReturnType>;
    sapiGetMiningStatisticsUserList (params?: {}): Promise<implicitReturnType>;
    sapiGetMiningPaymentUid (params?: {}): Promise<implicitReturnType>;
    sapiGetBswapPools (params?: {}): Promise<implicitReturnType>;
    sapiGetBswapLiquidity (params?: {}): Promise<implicitReturnType>;
    sapiGetBswapLiquidityOps (params?: {}): Promise<implicitReturnType>;
    sapiGetBswapQuote (params?: {}): Promise<implicitReturnType>;
    sapiGetBswapSwap (params?: {}): Promise<implicitReturnType>;
    sapiGetBswapPoolConfigure (params?: {}): Promise<implicitReturnType>;
    sapiGetBswapAddLiquidityPreview (params?: {}): Promise<implicitReturnType>;
    sapiGetBswapRemoveLiquidityPreview (params?: {}): Promise<implicitReturnType>;
    sapiGetBswapUnclaimedRewards (params?: {}): Promise<implicitReturnType>;
    sapiGetBswapClaimedHistory (params?: {}): Promise<implicitReturnType>;
    sapiGetBlvtTokenInfo (params?: {}): Promise<implicitReturnType>;
    sapiGetBlvtSubscribeRecord (params?: {}): Promise<implicitReturnType>;
    sapiGetBlvtRedeemRecord (params?: {}): Promise<implicitReturnType>;
    sapiGetBlvtUserLimit (params?: {}): Promise<implicitReturnType>;
    sapiGetApiReferralIfNewUser (params?: {}): Promise<implicitReturnType>;
    sapiGetApiReferralCustomization (params?: {}): Promise<implicitReturnType>;
    sapiGetApiReferralUserCustomization (params?: {}): Promise<implicitReturnType>;
    sapiGetApiReferralRebateRecentRecord (params?: {}): Promise<implicitReturnType>;
    sapiGetApiReferralRebateHistoricalRecord (params?: {}): Promise<implicitReturnType>;
    sapiGetApiReferralKickbackRecentRecord (params?: {}): Promise<implicitReturnType>;
    sapiGetApiReferralKickbackHistoricalRecord (params?: {}): Promise<implicitReturnType>;
    sapiGetBrokerSubAccountApi (params?: {}): Promise<implicitReturnType>;
    sapiGetBrokerSubAccount (params?: {}): Promise<implicitReturnType>;
    sapiGetBrokerSubAccountApiCommissionFutures (params?: {}): Promise<implicitReturnType>;
    sapiGetBrokerSubAccountApiCommissionCoinFutures (params?: {}): Promise<implicitReturnType>;
    sapiGetBrokerInfo (params?: {}): Promise<implicitReturnType>;
    sapiGetBrokerTransfer (params?: {}): Promise<implicitReturnType>;
    sapiGetBrokerTransferFutures (params?: {}): Promise<implicitReturnType>;
    sapiGetBrokerRebateRecentRecord (params?: {}): Promise<implicitReturnType>;
    sapiGetBrokerRebateHistoricalRecord (params?: {}): Promise<implicitReturnType>;
    sapiGetBrokerSubAccountBnbBurnStatus (params?: {}): Promise<implicitReturnType>;
    sapiGetBrokerSubAccountDepositHist (params?: {}): Promise<implicitReturnType>;
    sapiGetBrokerSubAccountSpotSummary (params?: {}): Promise<implicitReturnType>;
    sapiGetBrokerSubAccountMarginSummary (params?: {}): Promise<implicitReturnType>;
    sapiGetBrokerSubAccountFuturesSummary (params?: {}): Promise<implicitReturnType>;
    sapiGetBrokerRebateFuturesRecentRecord (params?: {}): Promise<implicitReturnType>;
    sapiGetBrokerSubAccountApiIpRestriction (params?: {}): Promise<implicitReturnType>;
    sapiGetBrokerUniversalTransfer (params?: {}): Promise<implicitReturnType>;
    sapiGetAccountApiRestrictions (params?: {}): Promise<implicitReturnType>;
    sapiGetC2cOrderMatchListUserOrderHistory (params?: {}): Promise<implicitReturnType>;
    sapiGetNftHistoryTransactions (params?: {}): Promise<implicitReturnType>;
    sapiGetNftHistoryDeposit (params?: {}): Promise<implicitReturnType>;
    sapiGetNftHistoryWithdraw (params?: {}): Promise<implicitReturnType>;
    sapiGetNftUserGetAsset (params?: {}): Promise<implicitReturnType>;
    sapiGetPayTransactions (params?: {}): Promise<implicitReturnType>;
    sapiGetGiftcardVerify (params?: {}): Promise<implicitReturnType>;
    sapiGetGiftcardCryptographyRsaPublicKey (params?: {}): Promise<implicitReturnType>;
    sapiGetGiftcardBuyCodeTokenLimit (params?: {}): Promise<implicitReturnType>;
    sapiGetAlgoSpotOpenOrders (params?: {}): Promise<implicitReturnType>;
    sapiGetAlgoSpotHistoricalOrders (params?: {}): Promise<implicitReturnType>;
    sapiGetAlgoSpotSubOrders (params?: {}): Promise<implicitReturnType>;
    sapiGetAlgoFuturesOpenOrders (params?: {}): Promise<implicitReturnType>;
    sapiGetAlgoFuturesHistoricalOrders (params?: {}): Promise<implicitReturnType>;
    sapiGetAlgoFuturesSubOrders (params?: {}): Promise<implicitReturnType>;
    sapiGetPortfolioAccount (params?: {}): Promise<implicitReturnType>;
    sapiGetPortfolioCollateralRate (params?: {}): Promise<implicitReturnType>;
    sapiGetPortfolioPmLoan (params?: {}): Promise<implicitReturnType>;
    sapiGetPortfolioInterestHistory (params?: {}): Promise<implicitReturnType>;
    sapiGetPortfolioAssetIndexPrice (params?: {}): Promise<implicitReturnType>;
    sapiGetPortfolioRepayFuturesSwitch (params?: {}): Promise<implicitReturnType>;
    sapiGetPortfolioMarginAssetLeverage (params?: {}): Promise<implicitReturnType>;
    sapiGetPortfolioBalance (params?: {}): Promise<implicitReturnType>;
    sapiGetPortfolioNegativeBalanceExchangeRecord (params?: {}): Promise<implicitReturnType>;
    sapiGetPortfolioPmloanHistory (params?: {}): Promise<implicitReturnType>;
    sapiGetStakingProductList (params?: {}): Promise<implicitReturnType>;
    sapiGetStakingPosition (params?: {}): Promise<implicitReturnType>;
    sapiGetStakingStakingRecord (params?: {}): Promise<implicitReturnType>;
    sapiGetStakingPersonalLeftQuota (params?: {}): Promise<implicitReturnType>;
    sapiGetLendingAutoInvestTargetAssetList (params?: {}): Promise<implicitReturnType>;
    sapiGetLendingAutoInvestTargetAssetRoiList (params?: {}): Promise<implicitReturnType>;
    sapiGetLendingAutoInvestAllAsset (params?: {}): Promise<implicitReturnType>;
    sapiGetLendingAutoInvestSourceAssetList (params?: {}): Promise<implicitReturnType>;
    sapiGetLendingAutoInvestPlanList (params?: {}): Promise<implicitReturnType>;
    sapiGetLendingAutoInvestPlanId (params?: {}): Promise<implicitReturnType>;
    sapiGetLendingAutoInvestHistoryList (params?: {}): Promise<implicitReturnType>;
    sapiGetLendingAutoInvestIndexInfo (params?: {}): Promise<implicitReturnType>;
    sapiGetLendingAutoInvestIndexUserSummary (params?: {}): Promise<implicitReturnType>;
    sapiGetLendingAutoInvestOneOffStatus (params?: {}): Promise<implicitReturnType>;
    sapiGetLendingAutoInvestRedeemHistory (params?: {}): Promise<implicitReturnType>;
    sapiGetLendingAutoInvestRebalanceHistory (params?: {}): Promise<implicitReturnType>;
    sapiGetSimpleEarnFlexibleList (params?: {}): Promise<implicitReturnType>;
    sapiGetSimpleEarnLockedList (params?: {}): Promise<implicitReturnType>;
    sapiGetSimpleEarnFlexiblePersonalLeftQuota (params?: {}): Promise<implicitReturnType>;
    sapiGetSimpleEarnLockedPersonalLeftQuota (params?: {}): Promise<implicitReturnType>;
    sapiGetSimpleEarnFlexibleSubscriptionPreview (params?: {}): Promise<implicitReturnType>;
    sapiGetSimpleEarnLockedSubscriptionPreview (params?: {}): Promise<implicitReturnType>;
    sapiGetSimpleEarnFlexibleHistoryRateHistory (params?: {}): Promise<implicitReturnType>;
    sapiGetSimpleEarnFlexiblePosition (params?: {}): Promise<implicitReturnType>;
    sapiGetSimpleEarnLockedPosition (params?: {}): Promise<implicitReturnType>;
    sapiGetSimpleEarnAccount (params?: {}): Promise<implicitReturnType>;
    sapiGetSimpleEarnFlexibleHistorySubscriptionRecord (params?: {}): Promise<implicitReturnType>;
    sapiGetSimpleEarnLockedHistorySubscriptionRecord (params?: {}): Promise<implicitReturnType>;
    sapiGetSimpleEarnFlexibleHistoryRedemptionRecord (params?: {}): Promise<implicitReturnType>;
    sapiGetSimpleEarnLockedHistoryRedemptionRecord (params?: {}): Promise<implicitReturnType>;
    sapiGetSimpleEarnFlexibleHistoryRewardsRecord (params?: {}): Promise<implicitReturnType>;
    sapiGetSimpleEarnLockedHistoryRewardsRecord (params?: {}): Promise<implicitReturnType>;
    sapiGetSimpleEarnFlexibleHistoryCollateralRecord (params?: {}): Promise<implicitReturnType>;
    sapiGetDciProductList (params?: {}): Promise<implicitReturnType>;
    sapiGetDciProductPositions (params?: {}): Promise<implicitReturnType>;
    sapiGetDciProductAccounts (params?: {}): Promise<implicitReturnType>;
    sapiPostAssetDust (params?: {}): Promise<implicitReturnType>;
    sapiPostAssetDustBtc (params?: {}): Promise<implicitReturnType>;
    sapiPostAssetTransfer (params?: {}): Promise<implicitReturnType>;
    sapiPostAssetGetFundingAsset (params?: {}): Promise<implicitReturnType>;
    sapiPostAssetConvertTransfer (params?: {}): Promise<implicitReturnType>;
    sapiPostAccountDisableFastWithdrawSwitch (params?: {}): Promise<implicitReturnType>;
    sapiPostAccountEnableFastWithdrawSwitch (params?: {}): Promise<implicitReturnType>;
    sapiPostCapitalWithdrawApply (params?: {}): Promise<implicitReturnType>;
    sapiPostCapitalContractConvertibleCoins (params?: {}): Promise<implicitReturnType>;
    sapiPostCapitalDepositCreditApply (params?: {}): Promise<implicitReturnType>;
    sapiPostMarginBorrowRepay (params?: {}): Promise<implicitReturnType>;
    sapiPostMarginTransfer (params?: {}): Promise<implicitReturnType>;
    sapiPostMarginLoan (params?: {}): Promise<implicitReturnType>;
    sapiPostMarginRepay (params?: {}): Promise<implicitReturnType>;
    sapiPostMarginOrder (params?: {}): Promise<implicitReturnType>;
    sapiPostMarginOrderOco (params?: {}): Promise<implicitReturnType>;
    sapiPostMarginDust (params?: {}): Promise<implicitReturnType>;
    sapiPostMarginExchangeSmallLiability (params?: {}): Promise<implicitReturnType>;
    sapiPostMarginIsolatedTransfer (params?: {}): Promise<implicitReturnType>;
    sapiPostMarginIsolatedAccount (params?: {}): Promise<implicitReturnType>;
    sapiPostMarginMaxLeverage (params?: {}): Promise<implicitReturnType>;
    sapiPostBnbBurn (params?: {}): Promise<implicitReturnType>;
    sapiPostSubAccountVirtualSubAccount (params?: {}): Promise<implicitReturnType>;
    sapiPostSubAccountMarginTransfer (params?: {}): Promise<implicitReturnType>;
    sapiPostSubAccountMarginEnable (params?: {}): Promise<implicitReturnType>;
    sapiPostSubAccountFuturesEnable (params?: {}): Promise<implicitReturnType>;
    sapiPostSubAccountFuturesTransfer (params?: {}): Promise<implicitReturnType>;
    sapiPostSubAccountFuturesInternalTransfer (params?: {}): Promise<implicitReturnType>;
    sapiPostSubAccountTransferSubToSub (params?: {}): Promise<implicitReturnType>;
    sapiPostSubAccountTransferSubToMaster (params?: {}): Promise<implicitReturnType>;
    sapiPostSubAccountUniversalTransfer (params?: {}): Promise<implicitReturnType>;
    sapiPostSubAccountOptionsEnable (params?: {}): Promise<implicitReturnType>;
    sapiPostManagedSubaccountDeposit (params?: {}): Promise<implicitReturnType>;
    sapiPostManagedSubaccountWithdraw (params?: {}): Promise<implicitReturnType>;
    sapiPostUserDataStream (params?: {}): Promise<implicitReturnType>;
    sapiPostUserDataStreamIsolated (params?: {}): Promise<implicitReturnType>;
    sapiPostFuturesTransfer (params?: {}): Promise<implicitReturnType>;
    sapiPostLendingCustomizedFixedPurchase (params?: {}): Promise<implicitReturnType>;
    sapiPostLendingDailyPurchase (params?: {}): Promise<implicitReturnType>;
    sapiPostLendingDailyRedeem (params?: {}): Promise<implicitReturnType>;
    sapiPostBswapLiquidityAdd (params?: {}): Promise<implicitReturnType>;
    sapiPostBswapLiquidityRemove (params?: {}): Promise<implicitReturnType>;
    sapiPostBswapSwap (params?: {}): Promise<implicitReturnType>;
    sapiPostBswapClaimRewards (params?: {}): Promise<implicitReturnType>;
    sapiPostBlvtSubscribe (params?: {}): Promise<implicitReturnType>;
    sapiPostBlvtRedeem (params?: {}): Promise<implicitReturnType>;
    sapiPostApiReferralCustomization (params?: {}): Promise<implicitReturnType>;
    sapiPostApiReferralUserCustomization (params?: {}): Promise<implicitReturnType>;
    sapiPostApiReferralRebateHistoricalRecord (params?: {}): Promise<implicitReturnType>;
    sapiPostApiReferralKickbackHistoricalRecord (params?: {}): Promise<implicitReturnType>;
    sapiPostBrokerSubAccount (params?: {}): Promise<implicitReturnType>;
    sapiPostBrokerSubAccountMargin (params?: {}): Promise<implicitReturnType>;
    sapiPostBrokerSubAccountFutures (params?: {}): Promise<implicitReturnType>;
    sapiPostBrokerSubAccountApi (params?: {}): Promise<implicitReturnType>;
    sapiPostBrokerSubAccountApiPermission (params?: {}): Promise<implicitReturnType>;
    sapiPostBrokerSubAccountApiCommission (params?: {}): Promise<implicitReturnType>;
    sapiPostBrokerSubAccountApiCommissionFutures (params?: {}): Promise<implicitReturnType>;
    sapiPostBrokerSubAccountApiCommissionCoinFutures (params?: {}): Promise<implicitReturnType>;
    sapiPostBrokerTransfer (params?: {}): Promise<implicitReturnType>;
    sapiPostBrokerTransferFutures (params?: {}): Promise<implicitReturnType>;
    sapiPostBrokerRebateHistoricalRecord (params?: {}): Promise<implicitReturnType>;
    sapiPostBrokerSubAccountBnbBurnSpot (params?: {}): Promise<implicitReturnType>;
    sapiPostBrokerSubAccountBnbBurnMarginInterest (params?: {}): Promise<implicitReturnType>;
    sapiPostBrokerSubAccountBlvt (params?: {}): Promise<implicitReturnType>;
    sapiPostBrokerSubAccountApiIpRestriction (params?: {}): Promise<implicitReturnType>;
    sapiPostBrokerSubAccountApiIpRestrictionIpList (params?: {}): Promise<implicitReturnType>;
    sapiPostBrokerUniversalTransfer (params?: {}): Promise<implicitReturnType>;
    sapiPostBrokerSubAccountApiPermissionUniversalTransfer (params?: {}): Promise<implicitReturnType>;
    sapiPostBrokerSubAccountApiPermissionVanillaOptions (params?: {}): Promise<implicitReturnType>;
    sapiPostGiftcardCreateCode (params?: {}): Promise<implicitReturnType>;
    sapiPostGiftcardRedeemCode (params?: {}): Promise<implicitReturnType>;
    sapiPostGiftcardBuyCode (params?: {}): Promise<implicitReturnType>;
    sapiPostAlgoSpotNewOrderTwap (params?: {}): Promise<implicitReturnType>;
    sapiPostAlgoFuturesNewOrderVp (params?: {}): Promise<implicitReturnType>;
    sapiPostAlgoFuturesNewOrderTwap (params?: {}): Promise<implicitReturnType>;
    sapiPostStakingPurchase (params?: {}): Promise<implicitReturnType>;
    sapiPostStakingRedeem (params?: {}): Promise<implicitReturnType>;
    sapiPostStakingSetAutoStaking (params?: {}): Promise<implicitReturnType>;
    sapiPostEthStakingEthStake (params?: {}): Promise<implicitReturnType>;
    sapiPostEthStakingEthRedeem (params?: {}): Promise<implicitReturnType>;
    sapiPostEthStakingWbethWrap (params?: {}): Promise<implicitReturnType>;
    sapiPostSolStakingSolStake (params?: {}): Promise<implicitReturnType>;
    sapiPostSolStakingSolRedeem (params?: {}): Promise<implicitReturnType>;
    sapiPostMiningHashTransferConfig (params?: {}): Promise<implicitReturnType>;
    sapiPostMiningHashTransferConfigCancel (params?: {}): Promise<implicitReturnType>;
    sapiPostPortfolioRepay (params?: {}): Promise<implicitReturnType>;
    sapiPostLoanVipRenew (params?: {}): Promise<implicitReturnType>;
    sapiPostLoanVipBorrow (params?: {}): Promise<implicitReturnType>;
    sapiPostLoanBorrow (params?: {}): Promise<implicitReturnType>;
    sapiPostLoanRepay (params?: {}): Promise<implicitReturnType>;
    sapiPostLoanAdjustLtv (params?: {}): Promise<implicitReturnType>;
    sapiPostLoanCustomizeMarginCall (params?: {}): Promise<implicitReturnType>;
    sapiPostLoanFlexibleRepay (params?: {}): Promise<implicitReturnType>;
    sapiPostLoanFlexibleAdjustLtv (params?: {}): Promise<implicitReturnType>;
    sapiPostLoanVipRepay (params?: {}): Promise<implicitReturnType>;
    sapiPostConvertGetQuote (params?: {}): Promise<implicitReturnType>;
    sapiPostConvertAcceptQuote (params?: {}): Promise<implicitReturnType>;
    sapiPostConvertLimitPlaceOrder (params?: {}): Promise<implicitReturnType>;
    sapiPostConvertLimitCancelOrder (params?: {}): Promise<implicitReturnType>;
    sapiPostPortfolioAutoCollection (params?: {}): Promise<implicitReturnType>;
    sapiPostPortfolioAssetCollection (params?: {}): Promise<implicitReturnType>;
    sapiPostPortfolioBnbTransfer (params?: {}): Promise<implicitReturnType>;
    sapiPostPortfolioRepayFuturesSwitch (params?: {}): Promise<implicitReturnType>;
    sapiPostPortfolioRepayFuturesNegativeBalance (params?: {}): Promise<implicitReturnType>;
    sapiPostPortfolioMint (params?: {}): Promise<implicitReturnType>;
    sapiPostPortfolioRedeem (params?: {}): Promise<implicitReturnType>;
    sapiPostLendingAutoInvestPlanAdd (params?: {}): Promise<implicitReturnType>;
    sapiPostLendingAutoInvestPlanEdit (params?: {}): Promise<implicitReturnType>;
    sapiPostLendingAutoInvestPlanEditStatus (params?: {}): Promise<implicitReturnType>;
    sapiPostLendingAutoInvestOneOff (params?: {}): Promise<implicitReturnType>;
    sapiPostLendingAutoInvestRedeem (params?: {}): Promise<implicitReturnType>;
    sapiPostSimpleEarnFlexibleSubscribe (params?: {}): Promise<implicitReturnType>;
    sapiPostSimpleEarnLockedSubscribe (params?: {}): Promise<implicitReturnType>;
    sapiPostSimpleEarnFlexibleRedeem (params?: {}): Promise<implicitReturnType>;
    sapiPostSimpleEarnLockedRedeem (params?: {}): Promise<implicitReturnType>;
    sapiPostSimpleEarnFlexibleSetAutoSubscribe (params?: {}): Promise<implicitReturnType>;
    sapiPostSimpleEarnLockedSetAutoSubscribe (params?: {}): Promise<implicitReturnType>;
    sapiPostSimpleEarnLockedSetRedeemOption (params?: {}): Promise<implicitReturnType>;
    sapiPostDciProductSubscribe (params?: {}): Promise<implicitReturnType>;
    sapiPostDciProductAutoCompoundEdit (params?: {}): Promise<implicitReturnType>;
    sapiPutUserDataStream (params?: {}): Promise<implicitReturnType>;
    sapiPutUserDataStreamIsolated (params?: {}): Promise<implicitReturnType>;
    sapiDeleteMarginOpenOrders (params?: {}): Promise<implicitReturnType>;
    sapiDeleteMarginOrder (params?: {}): Promise<implicitReturnType>;
    sapiDeleteMarginOrderList (params?: {}): Promise<implicitReturnType>;
    sapiDeleteMarginIsolatedAccount (params?: {}): Promise<implicitReturnType>;
    sapiDeleteUserDataStream (params?: {}): Promise<implicitReturnType>;
    sapiDeleteUserDataStreamIsolated (params?: {}): Promise<implicitReturnType>;
    sapiDeleteBrokerSubAccountApi (params?: {}): Promise<implicitReturnType>;
    sapiDeleteBrokerSubAccountApiIpRestrictionIpList (params?: {}): Promise<implicitReturnType>;
    sapiDeleteAlgoSpotOrder (params?: {}): Promise<implicitReturnType>;
    sapiDeleteAlgoFuturesOrder (params?: {}): Promise<implicitReturnType>;
    sapiDeleteSubAccountSubAccountApiIpRestrictionIpList (params?: {}): Promise<implicitReturnType>;
    sapiV2GetEthStakingAccount (params?: {}): Promise<implicitReturnType>;
    sapiV2GetSubAccountFuturesAccount (params?: {}): Promise<implicitReturnType>;
    sapiV2GetSubAccountFuturesAccountSummary (params?: {}): Promise<implicitReturnType>;
    sapiV2GetSubAccountFuturesPositionRisk (params?: {}): Promise<implicitReturnType>;
    sapiV2GetLoanFlexibleOngoingOrders (params?: {}): Promise<implicitReturnType>;
    sapiV2GetLoanFlexibleBorrowHistory (params?: {}): Promise<implicitReturnType>;
    sapiV2GetLoanFlexibleRepayHistory (params?: {}): Promise<implicitReturnType>;
    sapiV2GetLoanFlexibleLtvAdjustmentHistory (params?: {}): Promise<implicitReturnType>;
    sapiV2GetLoanFlexibleLoanableData (params?: {}): Promise<implicitReturnType>;
    sapiV2GetLoanFlexibleCollateralData (params?: {}): Promise<implicitReturnType>;
    sapiV2GetPortfolioAccount (params?: {}): Promise<implicitReturnType>;
    sapiV2PostEthStakingEthStake (params?: {}): Promise<implicitReturnType>;
    sapiV2PostSubAccountSubAccountApiIpRestriction (params?: {}): Promise<implicitReturnType>;
    sapiV2PostLoanFlexibleBorrow (params?: {}): Promise<implicitReturnType>;
    sapiV2PostLoanFlexibleRepay (params?: {}): Promise<implicitReturnType>;
    sapiV2PostLoanFlexibleAdjustLtv (params?: {}): Promise<implicitReturnType>;
    sapiV3GetSubAccountAssets (params?: {}): Promise<implicitReturnType>;
    sapiV3PostAssetGetUserAsset (params?: {}): Promise<implicitReturnType>;
    sapiV4GetSubAccountAssets (params?: {}): Promise<implicitReturnType>;
    dapiPublicGetPing (params?: {}): Promise<implicitReturnType>;
    dapiPublicGetTime (params?: {}): Promise<implicitReturnType>;
    dapiPublicGetExchangeInfo (params?: {}): Promise<implicitReturnType>;
    dapiPublicGetDepth (params?: {}): Promise<implicitReturnType>;
    dapiPublicGetTrades (params?: {}): Promise<implicitReturnType>;
    dapiPublicGetHistoricalTrades (params?: {}): Promise<implicitReturnType>;
    dapiPublicGetAggTrades (params?: {}): Promise<implicitReturnType>;
    dapiPublicGetPremiumIndex (params?: {}): Promise<implicitReturnType>;
    dapiPublicGetFundingRate (params?: {}): Promise<implicitReturnType>;
    dapiPublicGetKlines (params?: {}): Promise<implicitReturnType>;
    dapiPublicGetContinuousKlines (params?: {}): Promise<implicitReturnType>;
    dapiPublicGetIndexPriceKlines (params?: {}): Promise<implicitReturnType>;
    dapiPublicGetMarkPriceKlines (params?: {}): Promise<implicitReturnType>;
    dapiPublicGetPremiumIndexKlines (params?: {}): Promise<implicitReturnType>;
    dapiPublicGetTicker24hr (params?: {}): Promise<implicitReturnType>;
    dapiPublicGetTickerPrice (params?: {}): Promise<implicitReturnType>;
    dapiPublicGetTickerBookTicker (params?: {}): Promise<implicitReturnType>;
    dapiPublicGetConstituents (params?: {}): Promise<implicitReturnType>;
    dapiPublicGetOpenInterest (params?: {}): Promise<implicitReturnType>;
    dapiPublicGetFundingInfo (params?: {}): Promise<implicitReturnType>;
    dapiDataGetDeliveryPrice (params?: {}): Promise<implicitReturnType>;
    dapiDataGetOpenInterestHist (params?: {}): Promise<implicitReturnType>;
    dapiDataGetTopLongShortAccountRatio (params?: {}): Promise<implicitReturnType>;
    dapiDataGetTopLongShortPositionRatio (params?: {}): Promise<implicitReturnType>;
    dapiDataGetGlobalLongShortAccountRatio (params?: {}): Promise<implicitReturnType>;
    dapiDataGetTakerBuySellVol (params?: {}): Promise<implicitReturnType>;
    dapiDataGetBasis (params?: {}): Promise<implicitReturnType>;
    dapiPrivateGetPositionSideDual (params?: {}): Promise<implicitReturnType>;
    dapiPrivateGetOrderAmendment (params?: {}): Promise<implicitReturnType>;
    dapiPrivateGetOrder (params?: {}): Promise<implicitReturnType>;
    dapiPrivateGetOpenOrder (params?: {}): Promise<implicitReturnType>;
    dapiPrivateGetOpenOrders (params?: {}): Promise<implicitReturnType>;
    dapiPrivateGetAllOrders (params?: {}): Promise<implicitReturnType>;
    dapiPrivateGetBalance (params?: {}): Promise<implicitReturnType>;
    dapiPrivateGetAccount (params?: {}): Promise<implicitReturnType>;
    dapiPrivateGetPositionMarginHistory (params?: {}): Promise<implicitReturnType>;
    dapiPrivateGetPositionRisk (params?: {}): Promise<implicitReturnType>;
    dapiPrivateGetUserTrades (params?: {}): Promise<implicitReturnType>;
    dapiPrivateGetIncome (params?: {}): Promise<implicitReturnType>;
    dapiPrivateGetLeverageBracket (params?: {}): Promise<implicitReturnType>;
    dapiPrivateGetForceOrders (params?: {}): Promise<implicitReturnType>;
    dapiPrivateGetAdlQuantile (params?: {}): Promise<implicitReturnType>;
    dapiPrivateGetCommissionRate (params?: {}): Promise<implicitReturnType>;
    dapiPrivateGetIncomeAsyn (params?: {}): Promise<implicitReturnType>;
    dapiPrivateGetIncomeAsynId (params?: {}): Promise<implicitReturnType>;
    dapiPrivateGetTradeAsyn (params?: {}): Promise<implicitReturnType>;
    dapiPrivateGetTradeAsynId (params?: {}): Promise<implicitReturnType>;
    dapiPrivateGetOrderAsyn (params?: {}): Promise<implicitReturnType>;
    dapiPrivateGetOrderAsynId (params?: {}): Promise<implicitReturnType>;
    dapiPrivateGetPmExchangeInfo (params?: {}): Promise<implicitReturnType>;
    dapiPrivateGetPmAccountInfo (params?: {}): Promise<implicitReturnType>;
    dapiPrivatePostPositionSideDual (params?: {}): Promise<implicitReturnType>;
    dapiPrivatePostOrder (params?: {}): Promise<implicitReturnType>;
    dapiPrivatePostBatchOrders (params?: {}): Promise<implicitReturnType>;
    dapiPrivatePostCountdownCancelAll (params?: {}): Promise<implicitReturnType>;
    dapiPrivatePostLeverage (params?: {}): Promise<implicitReturnType>;
    dapiPrivatePostMarginType (params?: {}): Promise<implicitReturnType>;
    dapiPrivatePostPositionMargin (params?: {}): Promise<implicitReturnType>;
    dapiPrivatePostListenKey (params?: {}): Promise<implicitReturnType>;
    dapiPrivatePutListenKey (params?: {}): Promise<implicitReturnType>;
    dapiPrivatePutOrder (params?: {}): Promise<implicitReturnType>;
    dapiPrivatePutBatchOrders (params?: {}): Promise<implicitReturnType>;
    dapiPrivateDeleteOrder (params?: {}): Promise<implicitReturnType>;
    dapiPrivateDeleteAllOpenOrders (params?: {}): Promise<implicitReturnType>;
    dapiPrivateDeleteBatchOrders (params?: {}): Promise<implicitReturnType>;
    dapiPrivateDeleteListenKey (params?: {}): Promise<implicitReturnType>;
    dapiPrivateV2GetLeverageBracket (params?: {}): Promise<implicitReturnType>;
    fapiPublicGetPing (params?: {}): Promise<implicitReturnType>;
    fapiPublicGetTime (params?: {}): Promise<implicitReturnType>;
    fapiPublicGetExchangeInfo (params?: {}): Promise<implicitReturnType>;
    fapiPublicGetDepth (params?: {}): Promise<implicitReturnType>;
    fapiPublicGetTrades (params?: {}): Promise<implicitReturnType>;
    fapiPublicGetHistoricalTrades (params?: {}): Promise<implicitReturnType>;
    fapiPublicGetAggTrades (params?: {}): Promise<implicitReturnType>;
    fapiPublicGetKlines (params?: {}): Promise<implicitReturnType>;
    fapiPublicGetContinuousKlines (params?: {}): Promise<implicitReturnType>;
    fapiPublicGetMarkPriceKlines (params?: {}): Promise<implicitReturnType>;
    fapiPublicGetIndexPriceKlines (params?: {}): Promise<implicitReturnType>;
    fapiPublicGetPremiumIndexKlines (params?: {}): Promise<implicitReturnType>;
    fapiPublicGetFundingRate (params?: {}): Promise<implicitReturnType>;
    fapiPublicGetFundingInfo (params?: {}): Promise<implicitReturnType>;
    fapiPublicGetPremiumIndex (params?: {}): Promise<implicitReturnType>;
    fapiPublicGetTicker24hr (params?: {}): Promise<implicitReturnType>;
    fapiPublicGetTickerPrice (params?: {}): Promise<implicitReturnType>;
    fapiPublicGetTickerBookTicker (params?: {}): Promise<implicitReturnType>;
    fapiPublicGetOpenInterest (params?: {}): Promise<implicitReturnType>;
    fapiPublicGetIndexInfo (params?: {}): Promise<implicitReturnType>;
    fapiPublicGetAssetIndex (params?: {}): Promise<implicitReturnType>;
    fapiPublicGetConstituents (params?: {}): Promise<implicitReturnType>;
    fapiPublicGetApiTradingStatus (params?: {}): Promise<implicitReturnType>;
    fapiPublicGetLvtKlines (params?: {}): Promise<implicitReturnType>;
    fapiPublicGetConvertExchangeInfo (params?: {}): Promise<implicitReturnType>;
    fapiDataGetDeliveryPrice (params?: {}): Promise<implicitReturnType>;
    fapiDataGetOpenInterestHist (params?: {}): Promise<implicitReturnType>;
    fapiDataGetTopLongShortAccountRatio (params?: {}): Promise<implicitReturnType>;
    fapiDataGetTopLongShortPositionRatio (params?: {}): Promise<implicitReturnType>;
    fapiDataGetGlobalLongShortAccountRatio (params?: {}): Promise<implicitReturnType>;
    fapiDataGetTakerlongshortRatio (params?: {}): Promise<implicitReturnType>;
    fapiDataGetBasis (params?: {}): Promise<implicitReturnType>;
    fapiPrivateGetForceOrders (params?: {}): Promise<implicitReturnType>;
    fapiPrivateGetAllOrders (params?: {}): Promise<implicitReturnType>;
    fapiPrivateGetOpenOrder (params?: {}): Promise<implicitReturnType>;
    fapiPrivateGetOpenOrders (params?: {}): Promise<implicitReturnType>;
    fapiPrivateGetOrder (params?: {}): Promise<implicitReturnType>;
    fapiPrivateGetAccount (params?: {}): Promise<implicitReturnType>;
    fapiPrivateGetBalance (params?: {}): Promise<implicitReturnType>;
    fapiPrivateGetLeverageBracket (params?: {}): Promise<implicitReturnType>;
    fapiPrivateGetPositionMarginHistory (params?: {}): Promise<implicitReturnType>;
    fapiPrivateGetPositionRisk (params?: {}): Promise<implicitReturnType>;
    fapiPrivateGetPositionSideDual (params?: {}): Promise<implicitReturnType>;
    fapiPrivateGetUserTrades (params?: {}): Promise<implicitReturnType>;
    fapiPrivateGetIncome (params?: {}): Promise<implicitReturnType>;
    fapiPrivateGetCommissionRate (params?: {}): Promise<implicitReturnType>;
    fapiPrivateGetRateLimitOrder (params?: {}): Promise<implicitReturnType>;
    fapiPrivateGetApiTradingStatus (params?: {}): Promise<implicitReturnType>;
    fapiPrivateGetMultiAssetsMargin (params?: {}): Promise<implicitReturnType>;
    fapiPrivateGetApiReferralIfNewUser (params?: {}): Promise<implicitReturnType>;
    fapiPrivateGetApiReferralCustomization (params?: {}): Promise<implicitReturnType>;
    fapiPrivateGetApiReferralUserCustomization (params?: {}): Promise<implicitReturnType>;
    fapiPrivateGetApiReferralTraderNum (params?: {}): Promise<implicitReturnType>;
    fapiPrivateGetApiReferralOverview (params?: {}): Promise<implicitReturnType>;
    fapiPrivateGetApiReferralTradeVol (params?: {}): Promise<implicitReturnType>;
    fapiPrivateGetApiReferralRebateVol (params?: {}): Promise<implicitReturnType>;
    fapiPrivateGetApiReferralTraderSummary (params?: {}): Promise<implicitReturnType>;
    fapiPrivateGetAdlQuantile (params?: {}): Promise<implicitReturnType>;
    fapiPrivateGetPmAccountInfo (params?: {}): Promise<implicitReturnType>;
    fapiPrivateGetOrderAmendment (params?: {}): Promise<implicitReturnType>;
    fapiPrivateGetIncomeAsyn (params?: {}): Promise<implicitReturnType>;
    fapiPrivateGetIncomeAsynId (params?: {}): Promise<implicitReturnType>;
    fapiPrivateGetOrderAsyn (params?: {}): Promise<implicitReturnType>;
    fapiPrivateGetOrderAsynId (params?: {}): Promise<implicitReturnType>;
    fapiPrivateGetTradeAsyn (params?: {}): Promise<implicitReturnType>;
    fapiPrivateGetTradeAsynId (params?: {}): Promise<implicitReturnType>;
    fapiPrivateGetFeeBurn (params?: {}): Promise<implicitReturnType>;
    fapiPrivateGetSymbolConfig (params?: {}): Promise<implicitReturnType>;
    fapiPrivateGetAccountConfig (params?: {}): Promise<implicitReturnType>;
    fapiPrivateGetConvertOrderStatus (params?: {}): Promise<implicitReturnType>;
    fapiPrivatePostBatchOrders (params?: {}): Promise<implicitReturnType>;
    fapiPrivatePostPositionSideDual (params?: {}): Promise<implicitReturnType>;
    fapiPrivatePostPositionMargin (params?: {}): Promise<implicitReturnType>;
    fapiPrivatePostMarginType (params?: {}): Promise<implicitReturnType>;
    fapiPrivatePostOrder (params?: {}): Promise<implicitReturnType>;
    fapiPrivatePostLeverage (params?: {}): Promise<implicitReturnType>;
    fapiPrivatePostListenKey (params?: {}): Promise<implicitReturnType>;
    fapiPrivatePostCountdownCancelAll (params?: {}): Promise<implicitReturnType>;
    fapiPrivatePostMultiAssetsMargin (params?: {}): Promise<implicitReturnType>;
    fapiPrivatePostApiReferralCustomization (params?: {}): Promise<implicitReturnType>;
    fapiPrivatePostApiReferralUserCustomization (params?: {}): Promise<implicitReturnType>;
    fapiPrivatePostFeeBurn (params?: {}): Promise<implicitReturnType>;
    fapiPrivatePostConvertGetQuote (params?: {}): Promise<implicitReturnType>;
    fapiPrivatePostConvertAcceptQuote (params?: {}): Promise<implicitReturnType>;
    fapiPrivatePutListenKey (params?: {}): Promise<implicitReturnType>;
    fapiPrivatePutOrder (params?: {}): Promise<implicitReturnType>;
    fapiPrivatePutBatchOrders (params?: {}): Promise<implicitReturnType>;
    fapiPrivateDeleteBatchOrders (params?: {}): Promise<implicitReturnType>;
    fapiPrivateDeleteOrder (params?: {}): Promise<implicitReturnType>;
    fapiPrivateDeleteAllOpenOrders (params?: {}): Promise<implicitReturnType>;
    fapiPrivateDeleteListenKey (params?: {}): Promise<implicitReturnType>;
    fapiPublicV2GetTickerPrice (params?: {}): Promise<implicitReturnType>;
    fapiPrivateV2GetAccount (params?: {}): Promise<implicitReturnType>;
    fapiPrivateV2GetBalance (params?: {}): Promise<implicitReturnType>;
    fapiPrivateV2GetPositionRisk (params?: {}): Promise<implicitReturnType>;
    fapiPrivateV3GetAccount (params?: {}): Promise<implicitReturnType>;
    fapiPrivateV3GetBalance (params?: {}): Promise<implicitReturnType>;
    fapiPrivateV3GetPositionRisk (params?: {}): Promise<implicitReturnType>;
    eapiPublicGetPing (params?: {}): Promise<implicitReturnType>;
    eapiPublicGetTime (params?: {}): Promise<implicitReturnType>;
    eapiPublicGetExchangeInfo (params?: {}): Promise<implicitReturnType>;
    eapiPublicGetIndex (params?: {}): Promise<implicitReturnType>;
    eapiPublicGetTicker (params?: {}): Promise<implicitReturnType>;
    eapiPublicGetMark (params?: {}): Promise<implicitReturnType>;
    eapiPublicGetDepth (params?: {}): Promise<implicitReturnType>;
    eapiPublicGetKlines (params?: {}): Promise<implicitReturnType>;
    eapiPublicGetTrades (params?: {}): Promise<implicitReturnType>;
    eapiPublicGetHistoricalTrades (params?: {}): Promise<implicitReturnType>;
    eapiPublicGetExerciseHistory (params?: {}): Promise<implicitReturnType>;
    eapiPublicGetOpenInterest (params?: {}): Promise<implicitReturnType>;
    eapiPrivateGetAccount (params?: {}): Promise<implicitReturnType>;
    eapiPrivateGetPosition (params?: {}): Promise<implicitReturnType>;
    eapiPrivateGetOpenOrders (params?: {}): Promise<implicitReturnType>;
    eapiPrivateGetHistoryOrders (params?: {}): Promise<implicitReturnType>;
    eapiPrivateGetUserTrades (params?: {}): Promise<implicitReturnType>;
    eapiPrivateGetExerciseRecord (params?: {}): Promise<implicitReturnType>;
    eapiPrivateGetBill (params?: {}): Promise<implicitReturnType>;
    eapiPrivateGetIncomeAsyn (params?: {}): Promise<implicitReturnType>;
    eapiPrivateGetIncomeAsynId (params?: {}): Promise<implicitReturnType>;
    eapiPrivateGetMarginAccount (params?: {}): Promise<implicitReturnType>;
    eapiPrivateGetMmp (params?: {}): Promise<implicitReturnType>;
    eapiPrivateGetCountdownCancelAll (params?: {}): Promise<implicitReturnType>;
    eapiPrivateGetOrder (params?: {}): Promise<implicitReturnType>;
    eapiPrivateGetBlockOrderOrders (params?: {}): Promise<implicitReturnType>;
    eapiPrivateGetBlockOrderExecute (params?: {}): Promise<implicitReturnType>;
    eapiPrivateGetBlockUserTrades (params?: {}): Promise<implicitReturnType>;
    eapiPrivateGetBlockTrades (params?: {}): Promise<implicitReturnType>;
    eapiPrivatePostOrder (params?: {}): Promise<implicitReturnType>;
    eapiPrivatePostBatchOrders (params?: {}): Promise<implicitReturnType>;
    eapiPrivatePostListenKey (params?: {}): Promise<implicitReturnType>;
    eapiPrivatePostMmpSet (params?: {}): Promise<implicitReturnType>;
    eapiPrivatePostMmpReset (params?: {}): Promise<implicitReturnType>;
    eapiPrivatePostCountdownCancelAll (params?: {}): Promise<implicitReturnType>;
    eapiPrivatePostCountdownCancelAllHeartBeat (params?: {}): Promise<implicitReturnType>;
    eapiPrivatePostBlockOrderCreate (params?: {}): Promise<implicitReturnType>;
    eapiPrivatePostBlockOrderExecute (params?: {}): Promise<implicitReturnType>;
    eapiPrivatePutListenKey (params?: {}): Promise<implicitReturnType>;
    eapiPrivatePutBlockOrderCreate (params?: {}): Promise<implicitReturnType>;
    eapiPrivateDeleteOrder (params?: {}): Promise<implicitReturnType>;
    eapiPrivateDeleteBatchOrders (params?: {}): Promise<implicitReturnType>;
    eapiPrivateDeleteAllOpenOrders (params?: {}): Promise<implicitReturnType>;
    eapiPrivateDeleteAllOpenOrdersByUnderlying (params?: {}): Promise<implicitReturnType>;
    eapiPrivateDeleteListenKey (params?: {}): Promise<implicitReturnType>;
    eapiPrivateDeleteBlockOrderCreate (params?: {}): Promise<implicitReturnType>;
    publicGetPing (params?: {}): Promise<implicitReturnType>;
    publicGetTime (params?: {}): Promise<implicitReturnType>;
    publicGetDepth (params?: {}): Promise<implicitReturnType>;
    publicGetTrades (params?: {}): Promise<implicitReturnType>;
    publicGetAggTrades (params?: {}): Promise<implicitReturnType>;
    publicGetHistoricalTrades (params?: {}): Promise<implicitReturnType>;
    publicGetKlines (params?: {}): Promise<implicitReturnType>;
    publicGetUiKlines (params?: {}): Promise<implicitReturnType>;
    publicGetTicker24hr (params?: {}): Promise<implicitReturnType>;
    publicGetTicker (params?: {}): Promise<implicitReturnType>;
    publicGetTickerTradingDay (params?: {}): Promise<implicitReturnType>;
    publicGetTickerPrice (params?: {}): Promise<implicitReturnType>;
    publicGetTickerBookTicker (params?: {}): Promise<implicitReturnType>;
    publicGetExchangeInfo (params?: {}): Promise<implicitReturnType>;
    publicGetAvgPrice (params?: {}): Promise<implicitReturnType>;
    publicPutUserDataStream (params?: {}): Promise<implicitReturnType>;
    publicPostUserDataStream (params?: {}): Promise<implicitReturnType>;
    publicDeleteUserDataStream (params?: {}): Promise<implicitReturnType>;
    privateGetAllOrderList (params?: {}): Promise<implicitReturnType>;
    privateGetOpenOrderList (params?: {}): Promise<implicitReturnType>;
    privateGetOrderList (params?: {}): Promise<implicitReturnType>;
    privateGetOrder (params?: {}): Promise<implicitReturnType>;
    privateGetOpenOrders (params?: {}): Promise<implicitReturnType>;
    privateGetAllOrders (params?: {}): Promise<implicitReturnType>;
    privateGetAccount (params?: {}): Promise<implicitReturnType>;
    privateGetMyTrades (params?: {}): Promise<implicitReturnType>;
    privateGetRateLimitOrder (params?: {}): Promise<implicitReturnType>;
    privateGetMyPreventedMatches (params?: {}): Promise<implicitReturnType>;
    privateGetMyAllocations (params?: {}): Promise<implicitReturnType>;
    privateGetAccountCommission (params?: {}): Promise<implicitReturnType>;
    privatePostOrderOco (params?: {}): Promise<implicitReturnType>;
    privatePostOrderListOco (params?: {}): Promise<implicitReturnType>;
    privatePostOrderListOto (params?: {}): Promise<implicitReturnType>;
    privatePostOrderListOtoco (params?: {}): Promise<implicitReturnType>;
    privatePostSorOrder (params?: {}): Promise<implicitReturnType>;
    privatePostSorOrderTest (params?: {}): Promise<implicitReturnType>;
    privatePostOrder (params?: {}): Promise<implicitReturnType>;
    privatePostOrderCancelReplace (params?: {}): Promise<implicitReturnType>;
    privatePostOrderTest (params?: {}): Promise<implicitReturnType>;
    privateDeleteOpenOrders (params?: {}): Promise<implicitReturnType>;
    privateDeleteOrderList (params?: {}): Promise<implicitReturnType>;
    privateDeleteOrder (params?: {}): Promise<implicitReturnType>;
    papiGetPing (params?: {}): Promise<implicitReturnType>;
    papiGetUmOrder (params?: {}): Promise<implicitReturnType>;
    papiGetUmOpenOrder (params?: {}): Promise<implicitReturnType>;
    papiGetUmOpenOrders (params?: {}): Promise<implicitReturnType>;
    papiGetUmAllOrders (params?: {}): Promise<implicitReturnType>;
    papiGetCmOrder (params?: {}): Promise<implicitReturnType>;
    papiGetCmOpenOrder (params?: {}): Promise<implicitReturnType>;
    papiGetCmOpenOrders (params?: {}): Promise<implicitReturnType>;
    papiGetCmAllOrders (params?: {}): Promise<implicitReturnType>;
    papiGetUmConditionalOpenOrder (params?: {}): Promise<implicitReturnType>;
    papiGetUmConditionalOpenOrders (params?: {}): Promise<implicitReturnType>;
    papiGetUmConditionalOrderHistory (params?: {}): Promise<implicitReturnType>;
    papiGetUmConditionalAllOrders (params?: {}): Promise<implicitReturnType>;
    papiGetCmConditionalOpenOrder (params?: {}): Promise<implicitReturnType>;
    papiGetCmConditionalOpenOrders (params?: {}): Promise<implicitReturnType>;
    papiGetCmConditionalOrderHistory (params?: {}): Promise<implicitReturnType>;
    papiGetCmConditionalAllOrders (params?: {}): Promise<implicitReturnType>;
    papiGetMarginOrder (params?: {}): Promise<implicitReturnType>;
    papiGetMarginOpenOrders (params?: {}): Promise<implicitReturnType>;
    papiGetMarginAllOrders (params?: {}): Promise<implicitReturnType>;
    papiGetMarginOrderList (params?: {}): Promise<implicitReturnType>;
    papiGetMarginAllOrderList (params?: {}): Promise<implicitReturnType>;
    papiGetMarginOpenOrderList (params?: {}): Promise<implicitReturnType>;
    papiGetMarginMyTrades (params?: {}): Promise<implicitReturnType>;
    papiGetBalance (params?: {}): Promise<implicitReturnType>;
    papiGetAccount (params?: {}): Promise<implicitReturnType>;
    papiGetMarginMaxBorrowable (params?: {}): Promise<implicitReturnType>;
    papiGetMarginMaxWithdraw (params?: {}): Promise<implicitReturnType>;
    papiGetUmPositionRisk (params?: {}): Promise<implicitReturnType>;
    papiGetCmPositionRisk (params?: {}): Promise<implicitReturnType>;
    papiGetUmPositionSideDual (params?: {}): Promise<implicitReturnType>;
    papiGetCmPositionSideDual (params?: {}): Promise<implicitReturnType>;
    papiGetUmUserTrades (params?: {}): Promise<implicitReturnType>;
    papiGetCmUserTrades (params?: {}): Promise<implicitReturnType>;
    papiGetUmLeverageBracket (params?: {}): Promise<implicitReturnType>;
    papiGetCmLeverageBracket (params?: {}): Promise<implicitReturnType>;
    papiGetMarginForceOrders (params?: {}): Promise<implicitReturnType>;
    papiGetUmForceOrders (params?: {}): Promise<implicitReturnType>;
    papiGetCmForceOrders (params?: {}): Promise<implicitReturnType>;
    papiGetUmApiTradingStatus (params?: {}): Promise<implicitReturnType>;
    papiGetUmCommissionRate (params?: {}): Promise<implicitReturnType>;
    papiGetCmCommissionRate (params?: {}): Promise<implicitReturnType>;
    papiGetMarginMarginLoan (params?: {}): Promise<implicitReturnType>;
    papiGetMarginRepayLoan (params?: {}): Promise<implicitReturnType>;
    papiGetMarginMarginInterestHistory (params?: {}): Promise<implicitReturnType>;
    papiGetPortfolioInterestHistory (params?: {}): Promise<implicitReturnType>;
    papiGetUmIncome (params?: {}): Promise<implicitReturnType>;
    papiGetCmIncome (params?: {}): Promise<implicitReturnType>;
    papiGetUmAccount (params?: {}): Promise<implicitReturnType>;
    papiGetCmAccount (params?: {}): Promise<implicitReturnType>;
    papiGetRepayFuturesSwitch (params?: {}): Promise<implicitReturnType>;
    papiGetUmAdlQuantile (params?: {}): Promise<implicitReturnType>;
    papiGetCmAdlQuantile (params?: {}): Promise<implicitReturnType>;
    papiGetUmTradeAsyn (params?: {}): Promise<implicitReturnType>;
    papiGetUmTradeAsynId (params?: {}): Promise<implicitReturnType>;
    papiGetUmOrderAsyn (params?: {}): Promise<implicitReturnType>;
    papiGetUmOrderAsynId (params?: {}): Promise<implicitReturnType>;
    papiGetUmIncomeAsyn (params?: {}): Promise<implicitReturnType>;
    papiGetUmIncomeAsynId (params?: {}): Promise<implicitReturnType>;
    papiGetUmOrderAmendment (params?: {}): Promise<implicitReturnType>;
    papiGetCmOrderAmendment (params?: {}): Promise<implicitReturnType>;
    papiGetUmFeeBurn (params?: {}): Promise<implicitReturnType>;
    papiGetUmAccountConfig (params?: {}): Promise<implicitReturnType>;
    papiGetUmSymbolConfig (params?: {}): Promise<implicitReturnType>;
    papiGetCmAccountConfig (params?: {}): Promise<implicitReturnType>;
    papiGetCmSymbolConfig (params?: {}): Promise<implicitReturnType>;
    papiGetRateLimitOrder (params?: {}): Promise<implicitReturnType>;
    papiPostUmOrder (params?: {}): Promise<implicitReturnType>;
    papiPostUmConditionalOrder (params?: {}): Promise<implicitReturnType>;
    papiPostCmOrder (params?: {}): Promise<implicitReturnType>;
    papiPostCmConditionalOrder (params?: {}): Promise<implicitReturnType>;
    papiPostMarginOrder (params?: {}): Promise<implicitReturnType>;
    papiPostMarginLoan (params?: {}): Promise<implicitReturnType>;
    papiPostRepayLoan (params?: {}): Promise<implicitReturnType>;
    papiPostMarginOrderOco (params?: {}): Promise<implicitReturnType>;
    papiPostUmLeverage (params?: {}): Promise<implicitReturnType>;
    papiPostCmLeverage (params?: {}): Promise<implicitReturnType>;
    papiPostUmPositionSideDual (params?: {}): Promise<implicitReturnType>;
    papiPostCmPositionSideDual (params?: {}): Promise<implicitReturnType>;
    papiPostAutoCollection (params?: {}): Promise<implicitReturnType>;
    papiPostBnbTransfer (params?: {}): Promise<implicitReturnType>;
    papiPostRepayFuturesSwitch (params?: {}): Promise<implicitReturnType>;
    papiPostRepayFuturesNegativeBalance (params?: {}): Promise<implicitReturnType>;
    papiPostListenKey (params?: {}): Promise<implicitReturnType>;
    papiPostAssetCollection (params?: {}): Promise<implicitReturnType>;
    papiPostMarginRepayDebt (params?: {}): Promise<implicitReturnType>;
    papiPostUmFeeBurn (params?: {}): Promise<implicitReturnType>;
    papiPutListenKey (params?: {}): Promise<implicitReturnType>;
    papiPutUmOrder (params?: {}): Promise<implicitReturnType>;
    papiPutCmOrder (params?: {}): Promise<implicitReturnType>;
    papiDeleteUmOrder (params?: {}): Promise<implicitReturnType>;
    papiDeleteUmConditionalOrder (params?: {}): Promise<implicitReturnType>;
    papiDeleteUmAllOpenOrders (params?: {}): Promise<implicitReturnType>;
    papiDeleteUmConditionalAllOpenOrders (params?: {}): Promise<implicitReturnType>;
    papiDeleteCmOrder (params?: {}): Promise<implicitReturnType>;
    papiDeleteCmConditionalOrder (params?: {}): Promise<implicitReturnType>;
    papiDeleteCmAllOpenOrders (params?: {}): Promise<implicitReturnType>;
    papiDeleteCmConditionalAllOpenOrders (params?: {}): Promise<implicitReturnType>;
    papiDeleteMarginOrder (params?: {}): Promise<implicitReturnType>;
    papiDeleteMarginAllOpenOrders (params?: {}): Promise<implicitReturnType>;
    papiDeleteMarginOrderList (params?: {}): Promise<implicitReturnType>;
    papiDeleteListenKey (params?: {}): Promise<implicitReturnType>;
}
abstract class Exchange extends _Exchange {}

export default Exchange
