// -------------------------------------------------------------------------------

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

// -------------------------------------------------------------------------------

import { implicitReturnType } from '../base/types.js';
import { Exchange as _Exchange } from '../base/Exchange.js';

interface Exchange {
    v2PublicGetReferenceCurrencies (params?: {}): Promise<implicitReturnType>;
    v2PublicGetMarketStatus (params?: {}): Promise<implicitReturnType>;
    v2PrivateGetAccountLedger (params?: {}): Promise<implicitReturnType>;
    v2PrivateGetAccountWithdrawQuota (params?: {}): Promise<implicitReturnType>;
    v2PrivateGetAccountWithdrawAddress (params?: {}): Promise<implicitReturnType>;
    v2PrivateGetAccountDepositAddress (params?: {}): Promise<implicitReturnType>;
    v2PrivateGetAccountRepayment (params?: {}): Promise<implicitReturnType>;
    v2PrivateGetReferenceTransactFeeRate (params?: {}): Promise<implicitReturnType>;
    v2PrivateGetAccountAssetValuation (params?: {}): Promise<implicitReturnType>;
    v2PrivateGetPointAccount (params?: {}): Promise<implicitReturnType>;
    v2PrivateGetSubUserUserList (params?: {}): Promise<implicitReturnType>;
    v2PrivateGetSubUserUserState (params?: {}): Promise<implicitReturnType>;
    v2PrivateGetSubUserAccountList (params?: {}): Promise<implicitReturnType>;
    v2PrivateGetSubUserDepositAddress (params?: {}): Promise<implicitReturnType>;
    v2PrivateGetSubUserQueryDeposit (params?: {}): Promise<implicitReturnType>;
    v2PrivateGetUserApiKey (params?: {}): Promise<implicitReturnType>;
    v2PrivateGetUserUid (params?: {}): Promise<implicitReturnType>;
    v2PrivateGetAlgoOrdersOpening (params?: {}): Promise<implicitReturnType>;
    v2PrivateGetAlgoOrdersHistory (params?: {}): Promise<implicitReturnType>;
    v2PrivateGetAlgoOrdersSpecific (params?: {}): Promise<implicitReturnType>;
    v2PrivateGetC2cOffers (params?: {}): Promise<implicitReturnType>;
    v2PrivateGetC2cOffer (params?: {}): Promise<implicitReturnType>;
    v2PrivateGetC2cTransactions (params?: {}): Promise<implicitReturnType>;
    v2PrivateGetC2cRepayment (params?: {}): Promise<implicitReturnType>;
    v2PrivateGetC2cAccount (params?: {}): Promise<implicitReturnType>;
    v2PrivateGetEtpReference (params?: {}): Promise<implicitReturnType>;
    v2PrivateGetEtpTransactions (params?: {}): Promise<implicitReturnType>;
    v2PrivateGetEtpTransaction (params?: {}): Promise<implicitReturnType>;
    v2PrivateGetEtpRebalance (params?: {}): Promise<implicitReturnType>;
    v2PrivateGetEtpLimit (params?: {}): Promise<implicitReturnType>;
    v2PrivatePostAccountTransfer (params?: {}): Promise<implicitReturnType>;
    v2PrivatePostAccountRepayment (params?: {}): Promise<implicitReturnType>;
    v2PrivatePostPointTransfer (params?: {}): Promise<implicitReturnType>;
    v2PrivatePostSubUserManagement (params?: {}): Promise<implicitReturnType>;
    v2PrivatePostSubUserCreation (params?: {}): Promise<implicitReturnType>;
    v2PrivatePostSubUserTradableMarket (params?: {}): Promise<implicitReturnType>;
    v2PrivatePostSubUserTransferability (params?: {}): Promise<implicitReturnType>;
    v2PrivatePostSubUserApiKeyGeneration (params?: {}): Promise<implicitReturnType>;
    v2PrivatePostSubUserApiKeyModification (params?: {}): Promise<implicitReturnType>;
    v2PrivatePostSubUserApiKeyDeletion (params?: {}): Promise<implicitReturnType>;
    v2PrivatePostSubUserDeductMode (params?: {}): Promise<implicitReturnType>;
    v2PrivatePostAlgoOrders (params?: {}): Promise<implicitReturnType>;
    v2PrivatePostAlgoOrdersCancelAllAfter (params?: {}): Promise<implicitReturnType>;
    v2PrivatePostAlgoOrdersCancellation (params?: {}): Promise<implicitReturnType>;
    v2PrivatePostC2cOffer (params?: {}): Promise<implicitReturnType>;
    v2PrivatePostC2cCancellation (params?: {}): Promise<implicitReturnType>;
    v2PrivatePostC2cCancelAll (params?: {}): Promise<implicitReturnType>;
    v2PrivatePostC2cRepayment (params?: {}): Promise<implicitReturnType>;
    v2PrivatePostC2cTransfer (params?: {}): Promise<implicitReturnType>;
    v2PrivatePostEtpCreation (params?: {}): Promise<implicitReturnType>;
    v2PrivatePostEtpRedemption (params?: {}): Promise<implicitReturnType>;
    v2PrivatePostEtpTransactIdCancel (params?: {}): Promise<implicitReturnType>;
    v2PrivatePostEtpBatchCancel (params?: {}): Promise<implicitReturnType>;
    marketGetHistoryKline (params?: {}): Promise<implicitReturnType>;
    marketGetDetailMerged (params?: {}): Promise<implicitReturnType>;
    marketGetDepth (params?: {}): Promise<implicitReturnType>;
    marketGetTrade (params?: {}): Promise<implicitReturnType>;
    marketGetHistoryTrade (params?: {}): Promise<implicitReturnType>;
    marketGetDetail (params?: {}): Promise<implicitReturnType>;
    marketGetTickers (params?: {}): Promise<implicitReturnType>;
    marketGetEtp (params?: {}): Promise<implicitReturnType>;
    publicGetCommonSymbols (params?: {}): Promise<implicitReturnType>;
    publicGetCommonCurrencys (params?: {}): Promise<implicitReturnType>;
    publicGetCommonTimestamp (params?: {}): Promise<implicitReturnType>;
    publicGetCommonExchange (params?: {}): Promise<implicitReturnType>;
    publicGetSettingsCurrencys (params?: {}): Promise<implicitReturnType>;
    privateGetAccountAccounts (params?: {}): Promise<implicitReturnType>;
    privateGetAccountAccountsIdBalance (params?: {}): Promise<implicitReturnType>;
    privateGetAccountAccountsSubUid (params?: {}): Promise<implicitReturnType>;
    privateGetAccountHistory (params?: {}): Promise<implicitReturnType>;
    privateGetCrossMarginLoanInfo (params?: {}): Promise<implicitReturnType>;
    privateGetMarginLoanInfo (params?: {}): Promise<implicitReturnType>;
    privateGetFeeFeeRateGet (params?: {}): Promise<implicitReturnType>;
    privateGetOrderOpenOrders (params?: {}): Promise<implicitReturnType>;
    privateGetOrderOrders (params?: {}): Promise<implicitReturnType>;
    privateGetOrderOrdersId (params?: {}): Promise<implicitReturnType>;
    privateGetOrderOrdersIdMatchresults (params?: {}): Promise<implicitReturnType>;
    privateGetOrderOrdersGetClientOrder (params?: {}): Promise<implicitReturnType>;
    privateGetOrderHistory (params?: {}): Promise<implicitReturnType>;
    privateGetOrderMatchresults (params?: {}): Promise<implicitReturnType>;
    privateGetQueryDepositWithdraw (params?: {}): Promise<implicitReturnType>;
    privateGetMarginLoanOrders (params?: {}): Promise<implicitReturnType>;
    privateGetMarginAccountsBalance (params?: {}): Promise<implicitReturnType>;
    privateGetCrossMarginLoanOrders (params?: {}): Promise<implicitReturnType>;
    privateGetCrossMarginAccountsBalance (params?: {}): Promise<implicitReturnType>;
    privateGetPointsActions (params?: {}): Promise<implicitReturnType>;
    privateGetPointsOrders (params?: {}): Promise<implicitReturnType>;
    privateGetSubuserAggregateBalance (params?: {}): Promise<implicitReturnType>;
    privateGetStableCoinExchangeRate (params?: {}): Promise<implicitReturnType>;
    privateGetStableCoinQuote (params?: {}): Promise<implicitReturnType>;
    privatePostAccountTransfer (params?: {}): Promise<implicitReturnType>;
    privatePostFuturesTransfer (params?: {}): Promise<implicitReturnType>;
    privatePostOrderBatchOrders (params?: {}): Promise<implicitReturnType>;
    privatePostOrderOrdersPlace (params?: {}): Promise<implicitReturnType>;
    privatePostOrderOrdersSubmitCancelClientOrder (params?: {}): Promise<implicitReturnType>;
    privatePostOrderOrdersBatchCancelOpenOrders (params?: {}): Promise<implicitReturnType>;
    privatePostOrderOrdersIdSubmitcancel (params?: {}): Promise<implicitReturnType>;
    privatePostOrderOrdersBatchcancel (params?: {}): Promise<implicitReturnType>;
    privatePostDwWithdrawApiCreate (params?: {}): Promise<implicitReturnType>;
    privatePostDwWithdrawVirtualIdCancel (params?: {}): Promise<implicitReturnType>;
    privatePostDwTransferInMargin (params?: {}): Promise<implicitReturnType>;
    privatePostDwTransferOutMargin (params?: {}): Promise<implicitReturnType>;
    privatePostMarginOrders (params?: {}): Promise<implicitReturnType>;
    privatePostMarginOrdersIdRepay (params?: {}): Promise<implicitReturnType>;
    privatePostCrossMarginTransferIn (params?: {}): Promise<implicitReturnType>;
    privatePostCrossMarginTransferOut (params?: {}): Promise<implicitReturnType>;
    privatePostCrossMarginOrders (params?: {}): Promise<implicitReturnType>;
    privatePostCrossMarginOrdersIdRepay (params?: {}): Promise<implicitReturnType>;
    privatePostStableCoinExchange (params?: {}): Promise<implicitReturnType>;
    privatePostSubuserTransfer (params?: {}): Promise<implicitReturnType>;
}
abstract class Exchange extends _Exchange {}

export default Exchange
