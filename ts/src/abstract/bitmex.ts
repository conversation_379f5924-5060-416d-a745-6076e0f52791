// -------------------------------------------------------------------------------

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

// -------------------------------------------------------------------------------

import { implicitReturnType } from '../base/types.js';
import { Exchange as _Exchange } from '../base/Exchange.js';

interface Exchange {
    publicGetAnnouncement (params?: {}): Promise<implicitReturnType>;
    publicGetAnnouncementUrgent (params?: {}): Promise<implicitReturnType>;
    publicGetChat (params?: {}): Promise<implicitReturnType>;
    publicGetChatChannels (params?: {}): Promise<implicitReturnType>;
    publicGetChatConnected (params?: {}): Promise<implicitReturnType>;
    publicGetChatPinned (params?: {}): Promise<implicitReturnType>;
    publicGetFunding (params?: {}): Promise<implicitReturnType>;
    publicGetGuild (params?: {}): Promise<implicitReturnType>;
    publicGetInstrument (params?: {}): Promise<implicitReturnType>;
    publicGetInstrumentActive (params?: {}): Promise<implicitReturnType>;
    publicGetInstrumentActiveAndIndices (params?: {}): Promise<implicitReturnType>;
    publicGetInstrumentActiveIntervals (params?: {}): Promise<implicitReturnType>;
    publicGetInstrumentCompositeIndex (params?: {}): Promise<implicitReturnType>;
    publicGetInstrumentIndices (params?: {}): Promise<implicitReturnType>;
    publicGetInstrumentUsdVolume (params?: {}): Promise<implicitReturnType>;
    publicGetInsurance (params?: {}): Promise<implicitReturnType>;
    publicGetLeaderboard (params?: {}): Promise<implicitReturnType>;
    publicGetLiquidation (params?: {}): Promise<implicitReturnType>;
    publicGetOrderBookL2 (params?: {}): Promise<implicitReturnType>;
    publicGetPorlNonce (params?: {}): Promise<implicitReturnType>;
    publicGetQuote (params?: {}): Promise<implicitReturnType>;
    publicGetQuoteBucketed (params?: {}): Promise<implicitReturnType>;
    publicGetSchema (params?: {}): Promise<implicitReturnType>;
    publicGetSchemaWebsocketHelp (params?: {}): Promise<implicitReturnType>;
    publicGetSettlement (params?: {}): Promise<implicitReturnType>;
    publicGetStats (params?: {}): Promise<implicitReturnType>;
    publicGetStatsHistory (params?: {}): Promise<implicitReturnType>;
    publicGetStatsHistoryUSD (params?: {}): Promise<implicitReturnType>;
    publicGetTrade (params?: {}): Promise<implicitReturnType>;
    publicGetTradeBucketed (params?: {}): Promise<implicitReturnType>;
    publicGetWalletAssets (params?: {}): Promise<implicitReturnType>;
    publicGetWalletNetworks (params?: {}): Promise<implicitReturnType>;
    privateGetAddress (params?: {}): Promise<implicitReturnType>;
    privateGetApiKey (params?: {}): Promise<implicitReturnType>;
    privateGetExecution (params?: {}): Promise<implicitReturnType>;
    privateGetExecutionTradeHistory (params?: {}): Promise<implicitReturnType>;
    privateGetGlobalNotification (params?: {}): Promise<implicitReturnType>;
    privateGetLeaderboardName (params?: {}): Promise<implicitReturnType>;
    privateGetOrder (params?: {}): Promise<implicitReturnType>;
    privateGetPorlSnapshots (params?: {}): Promise<implicitReturnType>;
    privateGetPosition (params?: {}): Promise<implicitReturnType>;
    privateGetUser (params?: {}): Promise<implicitReturnType>;
    privateGetUserAffiliateStatus (params?: {}): Promise<implicitReturnType>;
    privateGetUserCheckReferralCode (params?: {}): Promise<implicitReturnType>;
    privateGetUserCommission (params?: {}): Promise<implicitReturnType>;
    privateGetUserCsa (params?: {}): Promise<implicitReturnType>;
    privateGetUserDepositAddress (params?: {}): Promise<implicitReturnType>;
    privateGetUserExecutionHistory (params?: {}): Promise<implicitReturnType>;
    privateGetUserGetWalletTransferAccounts (params?: {}): Promise<implicitReturnType>;
    privateGetUserMargin (params?: {}): Promise<implicitReturnType>;
    privateGetUserQuoteFillRatio (params?: {}): Promise<implicitReturnType>;
    privateGetUserQuoteValueRatio (params?: {}): Promise<implicitReturnType>;
    privateGetUserStaking (params?: {}): Promise<implicitReturnType>;
    privateGetUserStakingInstruments (params?: {}): Promise<implicitReturnType>;
    privateGetUserStakingTiers (params?: {}): Promise<implicitReturnType>;
    privateGetUserTradingVolume (params?: {}): Promise<implicitReturnType>;
    privateGetUserUnstakingRequests (params?: {}): Promise<implicitReturnType>;
    privateGetUserWallet (params?: {}): Promise<implicitReturnType>;
    privateGetUserWalletHistory (params?: {}): Promise<implicitReturnType>;
    privateGetUserWalletSummary (params?: {}): Promise<implicitReturnType>;
    privateGetUserAffiliates (params?: {}): Promise<implicitReturnType>;
    privateGetUserEvent (params?: {}): Promise<implicitReturnType>;
    privatePostAddress (params?: {}): Promise<implicitReturnType>;
    privatePostChat (params?: {}): Promise<implicitReturnType>;
    privatePostGuild (params?: {}): Promise<implicitReturnType>;
    privatePostGuildArchive (params?: {}): Promise<implicitReturnType>;
    privatePostGuildJoin (params?: {}): Promise<implicitReturnType>;
    privatePostGuildKick (params?: {}): Promise<implicitReturnType>;
    privatePostGuildLeave (params?: {}): Promise<implicitReturnType>;
    privatePostGuildSharesTrades (params?: {}): Promise<implicitReturnType>;
    privatePostOrder (params?: {}): Promise<implicitReturnType>;
    privatePostOrderCancelAllAfter (params?: {}): Promise<implicitReturnType>;
    privatePostOrderClosePosition (params?: {}): Promise<implicitReturnType>;
    privatePostPositionIsolate (params?: {}): Promise<implicitReturnType>;
    privatePostPositionLeverage (params?: {}): Promise<implicitReturnType>;
    privatePostPositionRiskLimit (params?: {}): Promise<implicitReturnType>;
    privatePostPositionTransferMargin (params?: {}): Promise<implicitReturnType>;
    privatePostUserAddSubaccount (params?: {}): Promise<implicitReturnType>;
    privatePostUserCancelWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostUserCommunicationToken (params?: {}): Promise<implicitReturnType>;
    privatePostUserConfirmEmail (params?: {}): Promise<implicitReturnType>;
    privatePostUserConfirmWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostUserLogout (params?: {}): Promise<implicitReturnType>;
    privatePostUserPreferences (params?: {}): Promise<implicitReturnType>;
    privatePostUserRequestWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostUserUnstakingRequests (params?: {}): Promise<implicitReturnType>;
    privatePostUserUpdateSubaccount (params?: {}): Promise<implicitReturnType>;
    privatePostUserWalletTransfer (params?: {}): Promise<implicitReturnType>;
    privatePutGuild (params?: {}): Promise<implicitReturnType>;
    privatePutOrder (params?: {}): Promise<implicitReturnType>;
    privateDeleteOrder (params?: {}): Promise<implicitReturnType>;
    privateDeleteOrderAll (params?: {}): Promise<implicitReturnType>;
    privateDeleteUserUnstakingRequests (params?: {}): Promise<implicitReturnType>;
}
abstract class Exchange extends _Exchange {}

export default Exchange
