// -------------------------------------------------------------------------------

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

// -------------------------------------------------------------------------------

import { implicitReturnType } from '../base/types.js';
import { Exchange as _Exchange } from '../base/Exchange.js';

interface Exchange {
    publicGetAuth (params?: {}): Promise<implicitReturnType>;
    publicGetExchangeToken (params?: {}): Promise<implicitReturnType>;
    publicGetForkToken (params?: {}): Promise<implicitReturnType>;
    publicGetSetHeartbeat (params?: {}): Promise<implicitReturnType>;
    publicGetDisableHeartbeat (params?: {}): Promise<implicitReturnType>;
    publicGetGetTime (params?: {}): Promise<implicitReturnType>;
    publicGetHello (params?: {}): Promise<implicitReturnType>;
    publicGetStatus (params?: {}): Promise<implicitReturnType>;
    publicGetTest (params?: {}): Promise<implicitReturnType>;
    publicGetSubscribe (params?: {}): Promise<implicitReturnType>;
    publicGetUnsubscribe (params?: {}): Promise<implicitReturnType>;
    publicGetUnsubscribeAll (params?: {}): Promise<implicitReturnType>;
    publicGetGetAnnouncements (params?: {}): Promise<implicitReturnType>;
    publicGetGetBookSummaryByCurrency (params?: {}): Promise<implicitReturnType>;
    publicGetGetBookSummaryByInstrument (params?: {}): Promise<implicitReturnType>;
    publicGetGetContractSize (params?: {}): Promise<implicitReturnType>;
    publicGetGetCurrencies (params?: {}): Promise<implicitReturnType>;
    publicGetGetDeliveryPrices (params?: {}): Promise<implicitReturnType>;
    publicGetGetFundingChartData (params?: {}): Promise<implicitReturnType>;
    publicGetGetFundingRateHistory (params?: {}): Promise<implicitReturnType>;
    publicGetGetFundingRateValue (params?: {}): Promise<implicitReturnType>;
    publicGetGetHistoricalVolatility (params?: {}): Promise<implicitReturnType>;
    publicGetGetIndex (params?: {}): Promise<implicitReturnType>;
    publicGetGetIndexPrice (params?: {}): Promise<implicitReturnType>;
    publicGetGetIndexPriceNames (params?: {}): Promise<implicitReturnType>;
    publicGetGetInstrument (params?: {}): Promise<implicitReturnType>;
    publicGetGetInstruments (params?: {}): Promise<implicitReturnType>;
    publicGetGetLastSettlementsByCurrency (params?: {}): Promise<implicitReturnType>;
    publicGetGetLastSettlementsByInstrument (params?: {}): Promise<implicitReturnType>;
    publicGetGetLastTradesByCurrency (params?: {}): Promise<implicitReturnType>;
    publicGetGetLastTradesByCurrencyAndTime (params?: {}): Promise<implicitReturnType>;
    publicGetGetLastTradesByInstrument (params?: {}): Promise<implicitReturnType>;
    publicGetGetLastTradesByInstrumentAndTime (params?: {}): Promise<implicitReturnType>;
    publicGetGetMarkPriceHistory (params?: {}): Promise<implicitReturnType>;
    publicGetGetOrderBook (params?: {}): Promise<implicitReturnType>;
    publicGetGetTradeVolumes (params?: {}): Promise<implicitReturnType>;
    publicGetGetTradingviewChartData (params?: {}): Promise<implicitReturnType>;
    publicGetGetVolatilityIndexData (params?: {}): Promise<implicitReturnType>;
    publicGetTicker (params?: {}): Promise<implicitReturnType>;
    privateGetLogout (params?: {}): Promise<implicitReturnType>;
    privateGetEnableCancelOnDisconnect (params?: {}): Promise<implicitReturnType>;
    privateGetDisableCancelOnDisconnect (params?: {}): Promise<implicitReturnType>;
    privateGetGetCancelOnDisconnect (params?: {}): Promise<implicitReturnType>;
    privateGetSubscribe (params?: {}): Promise<implicitReturnType>;
    privateGetUnsubscribe (params?: {}): Promise<implicitReturnType>;
    privateGetUnsubscribeAll (params?: {}): Promise<implicitReturnType>;
    privateGetChangeApiKeyName (params?: {}): Promise<implicitReturnType>;
    privateGetChangeScopeInApiKey (params?: {}): Promise<implicitReturnType>;
    privateGetChangeSubaccountName (params?: {}): Promise<implicitReturnType>;
    privateGetCreateApiKey (params?: {}): Promise<implicitReturnType>;
    privateGetCreateSubaccount (params?: {}): Promise<implicitReturnType>;
    privateGetDisableApiKey (params?: {}): Promise<implicitReturnType>;
    privateGetDisableTfaForSubaccount (params?: {}): Promise<implicitReturnType>;
    privateGetEnableAffiliateProgram (params?: {}): Promise<implicitReturnType>;
    privateGetEnableApiKey (params?: {}): Promise<implicitReturnType>;
    privateGetGetAccessLog (params?: {}): Promise<implicitReturnType>;
    privateGetGetAccountSummary (params?: {}): Promise<implicitReturnType>;
    privateGetGetAccountSummaries (params?: {}): Promise<implicitReturnType>;
    privateGetGetAffiliateProgramInfo (params?: {}): Promise<implicitReturnType>;
    privateGetGetEmailLanguage (params?: {}): Promise<implicitReturnType>;
    privateGetGetNewAnnouncements (params?: {}): Promise<implicitReturnType>;
    privateGetGetPortfolioMargins (params?: {}): Promise<implicitReturnType>;
    privateGetGetPosition (params?: {}): Promise<implicitReturnType>;
    privateGetGetPositions (params?: {}): Promise<implicitReturnType>;
    privateGetGetSubaccounts (params?: {}): Promise<implicitReturnType>;
    privateGetGetSubaccountsDetails (params?: {}): Promise<implicitReturnType>;
    privateGetGetTransactionLog (params?: {}): Promise<implicitReturnType>;
    privateGetListApiKeys (params?: {}): Promise<implicitReturnType>;
    privateGetRemoveApiKey (params?: {}): Promise<implicitReturnType>;
    privateGetRemoveSubaccount (params?: {}): Promise<implicitReturnType>;
    privateGetResetApiKey (params?: {}): Promise<implicitReturnType>;
    privateGetSetAnnouncementAsRead (params?: {}): Promise<implicitReturnType>;
    privateGetSetApiKeyAsDefault (params?: {}): Promise<implicitReturnType>;
    privateGetSetEmailForSubaccount (params?: {}): Promise<implicitReturnType>;
    privateGetSetEmailLanguage (params?: {}): Promise<implicitReturnType>;
    privateGetSetPasswordForSubaccount (params?: {}): Promise<implicitReturnType>;
    privateGetToggleNotificationsFromSubaccount (params?: {}): Promise<implicitReturnType>;
    privateGetToggleSubaccountLogin (params?: {}): Promise<implicitReturnType>;
    privateGetExecuteBlockTrade (params?: {}): Promise<implicitReturnType>;
    privateGetGetBlockTrade (params?: {}): Promise<implicitReturnType>;
    privateGetGetLastBlockTradesByCurrency (params?: {}): Promise<implicitReturnType>;
    privateGetInvalidateBlockTradeSignature (params?: {}): Promise<implicitReturnType>;
    privateGetVerifyBlockTrade (params?: {}): Promise<implicitReturnType>;
    privateGetBuy (params?: {}): Promise<implicitReturnType>;
    privateGetSell (params?: {}): Promise<implicitReturnType>;
    privateGetEdit (params?: {}): Promise<implicitReturnType>;
    privateGetEditByLabel (params?: {}): Promise<implicitReturnType>;
    privateGetCancel (params?: {}): Promise<implicitReturnType>;
    privateGetCancelAll (params?: {}): Promise<implicitReturnType>;
    privateGetCancelAllByCurrency (params?: {}): Promise<implicitReturnType>;
    privateGetCancelAllByInstrument (params?: {}): Promise<implicitReturnType>;
    privateGetCancelByLabel (params?: {}): Promise<implicitReturnType>;
    privateGetClosePosition (params?: {}): Promise<implicitReturnType>;
    privateGetGetMargins (params?: {}): Promise<implicitReturnType>;
    privateGetGetMmpConfig (params?: {}): Promise<implicitReturnType>;
    privateGetGetOpenOrdersByCurrency (params?: {}): Promise<implicitReturnType>;
    privateGetGetOpenOrdersByInstrument (params?: {}): Promise<implicitReturnType>;
    privateGetGetOrderHistoryByCurrency (params?: {}): Promise<implicitReturnType>;
    privateGetGetOrderHistoryByInstrument (params?: {}): Promise<implicitReturnType>;
    privateGetGetOrderMarginByIds (params?: {}): Promise<implicitReturnType>;
    privateGetGetOrderState (params?: {}): Promise<implicitReturnType>;
    privateGetGetStopOrderHistory (params?: {}): Promise<implicitReturnType>;
    privateGetGetTriggerOrderHistory (params?: {}): Promise<implicitReturnType>;
    privateGetGetUserTradesByCurrency (params?: {}): Promise<implicitReturnType>;
    privateGetGetUserTradesByCurrencyAndTime (params?: {}): Promise<implicitReturnType>;
    privateGetGetUserTradesByInstrument (params?: {}): Promise<implicitReturnType>;
    privateGetGetUserTradesByInstrumentAndTime (params?: {}): Promise<implicitReturnType>;
    privateGetGetUserTradesByOrder (params?: {}): Promise<implicitReturnType>;
    privateGetResetMmp (params?: {}): Promise<implicitReturnType>;
    privateGetSetMmpConfig (params?: {}): Promise<implicitReturnType>;
    privateGetGetSettlementHistoryByInstrument (params?: {}): Promise<implicitReturnType>;
    privateGetGetSettlementHistoryByCurrency (params?: {}): Promise<implicitReturnType>;
    privateGetCancelTransferById (params?: {}): Promise<implicitReturnType>;
    privateGetCancelWithdrawal (params?: {}): Promise<implicitReturnType>;
    privateGetCreateDepositAddress (params?: {}): Promise<implicitReturnType>;
    privateGetGetCurrentDepositAddress (params?: {}): Promise<implicitReturnType>;
    privateGetGetDeposits (params?: {}): Promise<implicitReturnType>;
    privateGetGetTransfers (params?: {}): Promise<implicitReturnType>;
    privateGetGetWithdrawals (params?: {}): Promise<implicitReturnType>;
    privateGetSubmitTransferToSubaccount (params?: {}): Promise<implicitReturnType>;
    privateGetSubmitTransferToUser (params?: {}): Promise<implicitReturnType>;
    privateGetWithdraw (params?: {}): Promise<implicitReturnType>;
}
abstract class Exchange extends _Exchange {}

export default Exchange
