// -------------------------------------------------------------------------------

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

// -------------------------------------------------------------------------------

import { implicitReturnType } from '../base/types.js';
import { Exchange as _Exchange } from '../base/Exchange.js';

interface Exchange {
    publicGetOhlcPair (params?: {}): Promise<implicitReturnType>;
    publicGetOrderBookPair (params?: {}): Promise<implicitReturnType>;
    publicGetTicker (params?: {}): Promise<implicitReturnType>;
    publicGetTickerHourPair (params?: {}): Promise<implicitReturnType>;
    publicGetTickerPair (params?: {}): Promise<implicitReturnType>;
    publicGetTransactionsPair (params?: {}): Promise<implicitReturnType>;
    publicGetTradingPairsInfo (params?: {}): Promise<implicitReturnType>;
    publicGetCurrencies (params?: {}): Promise<implicitReturnType>;
    publicGetEurUsd (params?: {}): Promise<implicitReturnType>;
    publicGetTravelRuleVasps (params?: {}): Promise<implicitReturnType>;
    privateGetTravelRuleContacts (params?: {}): Promise<implicitReturnType>;
    privateGetContactsContactUuid (params?: {}): Promise<implicitReturnType>;
    privateGetEarnSubscriptions (params?: {}): Promise<implicitReturnType>;
    privateGetEarnTransactions (params?: {}): Promise<implicitReturnType>;
    privatePostAccountBalances (params?: {}): Promise<implicitReturnType>;
    privatePostAccountBalancesCurrency (params?: {}): Promise<implicitReturnType>;
    privatePostBalance (params?: {}): Promise<implicitReturnType>;
    privatePostBalancePair (params?: {}): Promise<implicitReturnType>;
    privatePostBchWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostBchAddress (params?: {}): Promise<implicitReturnType>;
    privatePostUserTransactions (params?: {}): Promise<implicitReturnType>;
    privatePostUserTransactionsPair (params?: {}): Promise<implicitReturnType>;
    privatePostCryptoTransactions (params?: {}): Promise<implicitReturnType>;
    privatePostOpenOrder (params?: {}): Promise<implicitReturnType>;
    privatePostOpenOrdersAll (params?: {}): Promise<implicitReturnType>;
    privatePostOpenOrdersPair (params?: {}): Promise<implicitReturnType>;
    privatePostOrderStatus (params?: {}): Promise<implicitReturnType>;
    privatePostCancelOrder (params?: {}): Promise<implicitReturnType>;
    privatePostCancelAllOrders (params?: {}): Promise<implicitReturnType>;
    privatePostCancelAllOrdersPair (params?: {}): Promise<implicitReturnType>;
    privatePostBuyPair (params?: {}): Promise<implicitReturnType>;
    privatePostBuyMarketPair (params?: {}): Promise<implicitReturnType>;
    privatePostBuyInstantPair (params?: {}): Promise<implicitReturnType>;
    privatePostSellPair (params?: {}): Promise<implicitReturnType>;
    privatePostSellMarketPair (params?: {}): Promise<implicitReturnType>;
    privatePostSellInstantPair (params?: {}): Promise<implicitReturnType>;
    privatePostTransferToMain (params?: {}): Promise<implicitReturnType>;
    privatePostTransferFromMain (params?: {}): Promise<implicitReturnType>;
    privatePostMyTradingPairs (params?: {}): Promise<implicitReturnType>;
    privatePostFeesTrading (params?: {}): Promise<implicitReturnType>;
    privatePostFeesTradingMarketSymbol (params?: {}): Promise<implicitReturnType>;
    privatePostFeesWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostFeesWithdrawalCurrency (params?: {}): Promise<implicitReturnType>;
    privatePostWithdrawalRequests (params?: {}): Promise<implicitReturnType>;
    privatePostWithdrawalOpen (params?: {}): Promise<implicitReturnType>;
    privatePostWithdrawalStatus (params?: {}): Promise<implicitReturnType>;
    privatePostWithdrawalCancel (params?: {}): Promise<implicitReturnType>;
    privatePostLiquidationAddressNew (params?: {}): Promise<implicitReturnType>;
    privatePostLiquidationAddressInfo (params?: {}): Promise<implicitReturnType>;
    privatePostBtcUnconfirmed (params?: {}): Promise<implicitReturnType>;
    privatePostWebsocketsToken (params?: {}): Promise<implicitReturnType>;
    privatePostBtcWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostBtcAddress (params?: {}): Promise<implicitReturnType>;
    privatePostRippleWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostRippleAddress (params?: {}): Promise<implicitReturnType>;
    privatePostLtcWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostLtcAddress (params?: {}): Promise<implicitReturnType>;
    privatePostEthWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostEthAddress (params?: {}): Promise<implicitReturnType>;
    privatePostXrpWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostXrpAddress (params?: {}): Promise<implicitReturnType>;
    privatePostXlmWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostXlmAddress (params?: {}): Promise<implicitReturnType>;
    privatePostPaxWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostPaxAddress (params?: {}): Promise<implicitReturnType>;
    privatePostLinkWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostLinkAddress (params?: {}): Promise<implicitReturnType>;
    privatePostUsdcWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostUsdcAddress (params?: {}): Promise<implicitReturnType>;
    privatePostOmgWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostOmgAddress (params?: {}): Promise<implicitReturnType>;
    privatePostDaiWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostDaiAddress (params?: {}): Promise<implicitReturnType>;
    privatePostKncWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostKncAddress (params?: {}): Promise<implicitReturnType>;
    privatePostMkrWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostMkrAddress (params?: {}): Promise<implicitReturnType>;
    privatePostZrxWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostZrxAddress (params?: {}): Promise<implicitReturnType>;
    privatePostGusdWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostGusdAddress (params?: {}): Promise<implicitReturnType>;
    privatePostAaveWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostAaveAddress (params?: {}): Promise<implicitReturnType>;
    privatePostBatWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostBatAddress (params?: {}): Promise<implicitReturnType>;
    privatePostUmaWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostUmaAddress (params?: {}): Promise<implicitReturnType>;
    privatePostSnxWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostSnxAddress (params?: {}): Promise<implicitReturnType>;
    privatePostUniWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostUniAddress (params?: {}): Promise<implicitReturnType>;
    privatePostYfiWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostYfiAddress (params?: {}): Promise<implicitReturnType>;
    privatePostAudioWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostAudioAddress (params?: {}): Promise<implicitReturnType>;
    privatePostCrvWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostCrvAddress (params?: {}): Promise<implicitReturnType>;
    privatePostAlgoWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostAlgoAddress (params?: {}): Promise<implicitReturnType>;
    privatePostCompWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostCompAddress (params?: {}): Promise<implicitReturnType>;
    privatePostGrtWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostGrtAddress (params?: {}): Promise<implicitReturnType>;
    privatePostUsdtWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostUsdtAddress (params?: {}): Promise<implicitReturnType>;
    privatePostEurtWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostEurtAddress (params?: {}): Promise<implicitReturnType>;
    privatePostMaticWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostMaticAddress (params?: {}): Promise<implicitReturnType>;
    privatePostSushiWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostSushiAddress (params?: {}): Promise<implicitReturnType>;
    privatePostChzWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostChzAddress (params?: {}): Promise<implicitReturnType>;
    privatePostEnjWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostEnjAddress (params?: {}): Promise<implicitReturnType>;
    privatePostAlphaWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostAlphaAddress (params?: {}): Promise<implicitReturnType>;
    privatePostFttWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostFttAddress (params?: {}): Promise<implicitReturnType>;
    privatePostStorjWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostStorjAddress (params?: {}): Promise<implicitReturnType>;
    privatePostAxsWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostAxsAddress (params?: {}): Promise<implicitReturnType>;
    privatePostSandWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostSandAddress (params?: {}): Promise<implicitReturnType>;
    privatePostHbarWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostHbarAddress (params?: {}): Promise<implicitReturnType>;
    privatePostRgtWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostRgtAddress (params?: {}): Promise<implicitReturnType>;
    privatePostFetWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostFetAddress (params?: {}): Promise<implicitReturnType>;
    privatePostSklWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostSklAddress (params?: {}): Promise<implicitReturnType>;
    privatePostCelWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostCelAddress (params?: {}): Promise<implicitReturnType>;
    privatePostSxpWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostSxpAddress (params?: {}): Promise<implicitReturnType>;
    privatePostAdaWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostAdaAddress (params?: {}): Promise<implicitReturnType>;
    privatePostSlpWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostSlpAddress (params?: {}): Promise<implicitReturnType>;
    privatePostFtmWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostFtmAddress (params?: {}): Promise<implicitReturnType>;
    privatePostPerpWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostPerpAddress (params?: {}): Promise<implicitReturnType>;
    privatePostDydxWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostDydxAddress (params?: {}): Promise<implicitReturnType>;
    privatePostGalaWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostGalaAddress (params?: {}): Promise<implicitReturnType>;
    privatePostShibWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostShibAddress (params?: {}): Promise<implicitReturnType>;
    privatePostAmpWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostAmpAddress (params?: {}): Promise<implicitReturnType>;
    privatePostSgbWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostSgbAddress (params?: {}): Promise<implicitReturnType>;
    privatePostAvaxWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostAvaxAddress (params?: {}): Promise<implicitReturnType>;
    privatePostWbtcWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostWbtcAddress (params?: {}): Promise<implicitReturnType>;
    privatePostCtsiWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostCtsiAddress (params?: {}): Promise<implicitReturnType>;
    privatePostCvxWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostCvxAddress (params?: {}): Promise<implicitReturnType>;
    privatePostImxWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostImxAddress (params?: {}): Promise<implicitReturnType>;
    privatePostNexoWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostNexoAddress (params?: {}): Promise<implicitReturnType>;
    privatePostUstWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostUstAddress (params?: {}): Promise<implicitReturnType>;
    privatePostAntWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostAntAddress (params?: {}): Promise<implicitReturnType>;
    privatePostGodsWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostGodsAddress (params?: {}): Promise<implicitReturnType>;
    privatePostRadWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostRadAddress (params?: {}): Promise<implicitReturnType>;
    privatePostBandWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostBandAddress (params?: {}): Promise<implicitReturnType>;
    privatePostInjWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostInjAddress (params?: {}): Promise<implicitReturnType>;
    privatePostRlyWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostRlyAddress (params?: {}): Promise<implicitReturnType>;
    privatePostRndrWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostRndrAddress (params?: {}): Promise<implicitReturnType>;
    privatePostVegaWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostVegaAddress (params?: {}): Promise<implicitReturnType>;
    privatePost1inchWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePost1inchAddress (params?: {}): Promise<implicitReturnType>;
    privatePostEnsWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostEnsAddress (params?: {}): Promise<implicitReturnType>;
    privatePostManaWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostManaAddress (params?: {}): Promise<implicitReturnType>;
    privatePostLrcWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostLrcAddress (params?: {}): Promise<implicitReturnType>;
    privatePostApeWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostApeAddress (params?: {}): Promise<implicitReturnType>;
    privatePostMplWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostMplAddress (params?: {}): Promise<implicitReturnType>;
    privatePostEurocWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostEurocAddress (params?: {}): Promise<implicitReturnType>;
    privatePostSolWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostSolAddress (params?: {}): Promise<implicitReturnType>;
    privatePostDotWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostDotAddress (params?: {}): Promise<implicitReturnType>;
    privatePostNearWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostNearAddress (params?: {}): Promise<implicitReturnType>;
    privatePostDogeWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostDogeAddress (params?: {}): Promise<implicitReturnType>;
    privatePostFlrWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostFlrAddress (params?: {}): Promise<implicitReturnType>;
    privatePostDgldWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostDgldAddress (params?: {}): Promise<implicitReturnType>;
    privatePostLdoWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostLdoAddress (params?: {}): Promise<implicitReturnType>;
    privatePostTravelRuleContacts (params?: {}): Promise<implicitReturnType>;
    privatePostEarnSubscribe (params?: {}): Promise<implicitReturnType>;
    privatePostEarnSubscriptionsSetting (params?: {}): Promise<implicitReturnType>;
    privatePostEarnUnsubscribe (params?: {}): Promise<implicitReturnType>;
    privatePostWecanWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostWecanAddress (params?: {}): Promise<implicitReturnType>;
    privatePostTracWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostTracAddress (params?: {}): Promise<implicitReturnType>;
    privatePostEurcvWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostEurcvAddress (params?: {}): Promise<implicitReturnType>;
    privatePostPyusdWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostPyusdAddress (params?: {}): Promise<implicitReturnType>;
    privatePostLmwrWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostLmwrAddress (params?: {}): Promise<implicitReturnType>;
    privatePostPepeWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostPepeAddress (params?: {}): Promise<implicitReturnType>;
    privatePostBlurWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostBlurAddress (params?: {}): Promise<implicitReturnType>;
    privatePostVextWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostVextAddress (params?: {}): Promise<implicitReturnType>;
    privatePostCsprWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostCsprAddress (params?: {}): Promise<implicitReturnType>;
    privatePostVchfWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostVchfAddress (params?: {}): Promise<implicitReturnType>;
    privatePostVeurWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostVeurAddress (params?: {}): Promise<implicitReturnType>;
    privatePostTrufWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostTrufAddress (params?: {}): Promise<implicitReturnType>;
    privatePostWifWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostWifAddress (params?: {}): Promise<implicitReturnType>;
    privatePostSmtWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostSmtAddress (params?: {}): Promise<implicitReturnType>;
    privatePostSuiWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostSuiAddress (params?: {}): Promise<implicitReturnType>;
    privatePostJupWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostJupAddress (params?: {}): Promise<implicitReturnType>;
    privatePostOndoWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostOndoAddress (params?: {}): Promise<implicitReturnType>;
    privatePostBobaWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostBobaAddress (params?: {}): Promise<implicitReturnType>;
    privatePostPythWithdrawal (params?: {}): Promise<implicitReturnType>;
    privatePostPythAddress (params?: {}): Promise<implicitReturnType>;
}
abstract class Exchange extends _Exchange {}

export default Exchange
