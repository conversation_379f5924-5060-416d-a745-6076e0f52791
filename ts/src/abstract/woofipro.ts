// -------------------------------------------------------------------------------

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

// -------------------------------------------------------------------------------

import { implicitReturnType } from '../base/types.js';
import { Exchange as _Exchange } from '../base/Exchange.js';

interface Exchange {
    v1PublicGetPublicVolumeStats (params?: {}): Promise<implicitReturnType>;
    v1PublicGetPublicBrokerName (params?: {}): Promise<implicitReturnType>;
    v1PublicGetPublicChainInfoBrokerId (params?: {}): Promise<implicitReturnType>;
    v1PublicGetPublicSystemInfo (params?: {}): Promise<implicitReturnType>;
    v1PublicGetPublicVaultBalance (params?: {}): Promise<implicitReturnType>;
    v1PublicGetPublicInsurancefund (params?: {}): Promise<implicitReturnType>;
    v1PublicGetPublicChainInfo (params?: {}): Promise<implicitReturnType>;
    v1PublicGetFaucetUsdc (params?: {}): Promise<implicitReturnType>;
    v1PublicGetPublicAccount (params?: {}): Promise<implicitReturnType>;
    v1PublicGetGetAccount (params?: {}): Promise<implicitReturnType>;
    v1PublicGetRegistrationNonce (params?: {}): Promise<implicitReturnType>;
    v1PublicGetGetOrderlyKey (params?: {}): Promise<implicitReturnType>;
    v1PublicGetPublicLiquidation (params?: {}): Promise<implicitReturnType>;
    v1PublicGetPublicLiquidatedPositions (params?: {}): Promise<implicitReturnType>;
    v1PublicGetPublicConfig (params?: {}): Promise<implicitReturnType>;
    v1PublicGetPublicCampaignRanking (params?: {}): Promise<implicitReturnType>;
    v1PublicGetPublicCampaignStats (params?: {}): Promise<implicitReturnType>;
    v1PublicGetPublicCampaignUser (params?: {}): Promise<implicitReturnType>;
    v1PublicGetPublicCampaignStatsDetails (params?: {}): Promise<implicitReturnType>;
    v1PublicGetPublicCampaigns (params?: {}): Promise<implicitReturnType>;
    v1PublicGetPublicPointsLeaderboard (params?: {}): Promise<implicitReturnType>;
    v1PublicGetClientPoints (params?: {}): Promise<implicitReturnType>;
    v1PublicGetPublicPointsEpoch (params?: {}): Promise<implicitReturnType>;
    v1PublicGetPublicPointsEpochDates (params?: {}): Promise<implicitReturnType>;
    v1PublicGetPublicReferralCheckRefCode (params?: {}): Promise<implicitReturnType>;
    v1PublicGetPublicReferralVerifyRefCode (params?: {}): Promise<implicitReturnType>;
    v1PublicGetReferralAdminInfo (params?: {}): Promise<implicitReturnType>;
    v1PublicGetReferralInfo (params?: {}): Promise<implicitReturnType>;
    v1PublicGetReferralRefereeInfo (params?: {}): Promise<implicitReturnType>;
    v1PublicGetReferralRefereeRebateSummary (params?: {}): Promise<implicitReturnType>;
    v1PublicGetReferralRefereeHistory (params?: {}): Promise<implicitReturnType>;
    v1PublicGetReferralReferralHistory (params?: {}): Promise<implicitReturnType>;
    v1PublicGetReferralRebateSummary (params?: {}): Promise<implicitReturnType>;
    v1PublicGetClientDistributionHistory (params?: {}): Promise<implicitReturnType>;
    v1PublicGetTvConfig (params?: {}): Promise<implicitReturnType>;
    v1PublicGetTvHistory (params?: {}): Promise<implicitReturnType>;
    v1PublicGetTvSymbolInfo (params?: {}): Promise<implicitReturnType>;
    v1PublicGetPublicFundingRateHistory (params?: {}): Promise<implicitReturnType>;
    v1PublicGetPublicFundingRateSymbol (params?: {}): Promise<implicitReturnType>;
    v1PublicGetPublicFundingRates (params?: {}): Promise<implicitReturnType>;
    v1PublicGetPublicInfo (params?: {}): Promise<implicitReturnType>;
    v1PublicGetPublicInfoSymbol (params?: {}): Promise<implicitReturnType>;
    v1PublicGetPublicMarketTrades (params?: {}): Promise<implicitReturnType>;
    v1PublicGetPublicToken (params?: {}): Promise<implicitReturnType>;
    v1PublicGetPublicFutures (params?: {}): Promise<implicitReturnType>;
    v1PublicGetPublicFuturesSymbol (params?: {}): Promise<implicitReturnType>;
    v1PublicPostRegisterAccount (params?: {}): Promise<implicitReturnType>;
    v1PrivateGetClientKeyInfo (params?: {}): Promise<implicitReturnType>;
    v1PrivateGetClientOrderlyKeyIpRestriction (params?: {}): Promise<implicitReturnType>;
    v1PrivateGetOrderOid (params?: {}): Promise<implicitReturnType>;
    v1PrivateGetClientOrderClientOrderId (params?: {}): Promise<implicitReturnType>;
    v1PrivateGetAlgoOrderOid (params?: {}): Promise<implicitReturnType>;
    v1PrivateGetAlgoClientOrderClientOrderId (params?: {}): Promise<implicitReturnType>;
    v1PrivateGetOrders (params?: {}): Promise<implicitReturnType>;
    v1PrivateGetAlgoOrders (params?: {}): Promise<implicitReturnType>;
    v1PrivateGetTradeTid (params?: {}): Promise<implicitReturnType>;
    v1PrivateGetTrades (params?: {}): Promise<implicitReturnType>;
    v1PrivateGetOrderOidTrades (params?: {}): Promise<implicitReturnType>;
    v1PrivateGetClientLiquidatorLiquidations (params?: {}): Promise<implicitReturnType>;
    v1PrivateGetLiquidations (params?: {}): Promise<implicitReturnType>;
    v1PrivateGetAssetHistory (params?: {}): Promise<implicitReturnType>;
    v1PrivateGetClientHolding (params?: {}): Promise<implicitReturnType>;
    v1PrivateGetWithdrawNonce (params?: {}): Promise<implicitReturnType>;
    v1PrivateGetSettleNonce (params?: {}): Promise<implicitReturnType>;
    v1PrivateGetPnlSettlementHistory (params?: {}): Promise<implicitReturnType>;
    v1PrivateGetVolumeUserDaily (params?: {}): Promise<implicitReturnType>;
    v1PrivateGetVolumeUserStats (params?: {}): Promise<implicitReturnType>;
    v1PrivateGetClientStatistics (params?: {}): Promise<implicitReturnType>;
    v1PrivateGetClientInfo (params?: {}): Promise<implicitReturnType>;
    v1PrivateGetClientStatisticsDaily (params?: {}): Promise<implicitReturnType>;
    v1PrivateGetPositions (params?: {}): Promise<implicitReturnType>;
    v1PrivateGetPositionSymbol (params?: {}): Promise<implicitReturnType>;
    v1PrivateGetFundingFeeHistory (params?: {}): Promise<implicitReturnType>;
    v1PrivateGetNotificationInboxNotifications (params?: {}): Promise<implicitReturnType>;
    v1PrivateGetNotificationInboxUnread (params?: {}): Promise<implicitReturnType>;
    v1PrivateGetVolumeBrokerDaily (params?: {}): Promise<implicitReturnType>;
    v1PrivateGetBrokerFeeRateDefault (params?: {}): Promise<implicitReturnType>;
    v1PrivateGetBrokerUserInfo (params?: {}): Promise<implicitReturnType>;
    v1PrivateGetOrderbookSymbol (params?: {}): Promise<implicitReturnType>;
    v1PrivateGetKline (params?: {}): Promise<implicitReturnType>;
    v1PrivatePostOrderlyKey (params?: {}): Promise<implicitReturnType>;
    v1PrivatePostClientSetOrderlyKeyIpRestriction (params?: {}): Promise<implicitReturnType>;
    v1PrivatePostClientResetOrderlyKeyIpRestriction (params?: {}): Promise<implicitReturnType>;
    v1PrivatePostOrder (params?: {}): Promise<implicitReturnType>;
    v1PrivatePostBatchOrder (params?: {}): Promise<implicitReturnType>;
    v1PrivatePostAlgoOrder (params?: {}): Promise<implicitReturnType>;
    v1PrivatePostLiquidation (params?: {}): Promise<implicitReturnType>;
    v1PrivatePostClaimInsuranceFund (params?: {}): Promise<implicitReturnType>;
    v1PrivatePostWithdrawRequest (params?: {}): Promise<implicitReturnType>;
    v1PrivatePostSettlePnl (params?: {}): Promise<implicitReturnType>;
    v1PrivatePostNotificationInboxMarkRead (params?: {}): Promise<implicitReturnType>;
    v1PrivatePostNotificationInboxMarkReadAll (params?: {}): Promise<implicitReturnType>;
    v1PrivatePostClientLeverage (params?: {}): Promise<implicitReturnType>;
    v1PrivatePostClientMaintenanceConfig (params?: {}): Promise<implicitReturnType>;
    v1PrivatePostDelegateSigner (params?: {}): Promise<implicitReturnType>;
    v1PrivatePostDelegateOrderlyKey (params?: {}): Promise<implicitReturnType>;
    v1PrivatePostDelegateSettlePnl (params?: {}): Promise<implicitReturnType>;
    v1PrivatePostDelegateWithdrawRequest (params?: {}): Promise<implicitReturnType>;
    v1PrivatePostBrokerFeeRateSet (params?: {}): Promise<implicitReturnType>;
    v1PrivatePostBrokerFeeRateSetDefault (params?: {}): Promise<implicitReturnType>;
    v1PrivatePostBrokerFeeRateDefault (params?: {}): Promise<implicitReturnType>;
    v1PrivatePostReferralCreate (params?: {}): Promise<implicitReturnType>;
    v1PrivatePostReferralUpdate (params?: {}): Promise<implicitReturnType>;
    v1PrivatePostReferralBind (params?: {}): Promise<implicitReturnType>;
    v1PrivatePostReferralEditSplit (params?: {}): Promise<implicitReturnType>;
    v1PrivatePutOrder (params?: {}): Promise<implicitReturnType>;
    v1PrivatePutAlgoOrder (params?: {}): Promise<implicitReturnType>;
    v1PrivateDeleteOrder (params?: {}): Promise<implicitReturnType>;
    v1PrivateDeleteAlgoOrder (params?: {}): Promise<implicitReturnType>;
    v1PrivateDeleteClientOrder (params?: {}): Promise<implicitReturnType>;
    v1PrivateDeleteAlgoClientOrder (params?: {}): Promise<implicitReturnType>;
    v1PrivateDeleteAlgoOrders (params?: {}): Promise<implicitReturnType>;
    v1PrivateDeleteOrders (params?: {}): Promise<implicitReturnType>;
    v1PrivateDeleteBatchOrder (params?: {}): Promise<implicitReturnType>;
    v1PrivateDeleteClientBatchOrder (params?: {}): Promise<implicitReturnType>;
}
abstract class Exchange extends _Exchange {}

export default Exchange
