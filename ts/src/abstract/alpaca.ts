// -------------------------------------------------------------------------------

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

// -------------------------------------------------------------------------------

import { implicitReturnType } from '../base/types.js';
import { Exchange as _Exchange } from '../base/Exchange.js';

interface Exchange {
    traderPrivateGetV2Account (params?: {}): Promise<implicitReturnType>;
    traderPrivateGetV2Orders (params?: {}): Promise<implicitReturnType>;
    traderPrivateGetV2OrdersOrderId (params?: {}): Promise<implicitReturnType>;
    traderPrivateGetV2Positions (params?: {}): Promise<implicitReturnType>;
    traderPrivateGetV2PositionsSymbolOrAssetId (params?: {}): Promise<implicitReturnType>;
    traderPrivateGetV2AccountPortfolioHistory (params?: {}): Promise<implicitReturnType>;
    traderPrivateGetV2Watchlists (params?: {}): Promise<implicitReturnType>;
    traderPrivateGetV2WatchlistsWatchlistId (params?: {}): Promise<implicitReturnType>;
    traderPrivateGetV2WatchlistsByName (params?: {}): Promise<implicitReturnType>;
    traderPrivateGetV2AccountConfigurations (params?: {}): Promise<implicitReturnType>;
    traderPrivateGetV2AccountActivities (params?: {}): Promise<implicitReturnType>;
    traderPrivateGetV2AccountActivitiesActivityType (params?: {}): Promise<implicitReturnType>;
    traderPrivateGetV2Calendar (params?: {}): Promise<implicitReturnType>;
    traderPrivateGetV2Clock (params?: {}): Promise<implicitReturnType>;
    traderPrivateGetV2Assets (params?: {}): Promise<implicitReturnType>;
    traderPrivateGetV2AssetsSymbolOrAssetId (params?: {}): Promise<implicitReturnType>;
    traderPrivateGetV2CorporateActionsAnnouncementsId (params?: {}): Promise<implicitReturnType>;
    traderPrivateGetV2CorporateActionsAnnouncements (params?: {}): Promise<implicitReturnType>;
    traderPrivateGetV2Wallets (params?: {}): Promise<implicitReturnType>;
    traderPrivateGetV2WalletsTransfers (params?: {}): Promise<implicitReturnType>;
    traderPrivatePostV2Orders (params?: {}): Promise<implicitReturnType>;
    traderPrivatePostV2Watchlists (params?: {}): Promise<implicitReturnType>;
    traderPrivatePostV2WatchlistsWatchlistId (params?: {}): Promise<implicitReturnType>;
    traderPrivatePostV2WatchlistsByName (params?: {}): Promise<implicitReturnType>;
    traderPrivatePostV2WalletsTransfers (params?: {}): Promise<implicitReturnType>;
    traderPrivatePutV2OrdersOrderId (params?: {}): Promise<implicitReturnType>;
    traderPrivatePutV2WatchlistsWatchlistId (params?: {}): Promise<implicitReturnType>;
    traderPrivatePutV2WatchlistsByName (params?: {}): Promise<implicitReturnType>;
    traderPrivatePatchV2OrdersOrderId (params?: {}): Promise<implicitReturnType>;
    traderPrivatePatchV2AccountConfigurations (params?: {}): Promise<implicitReturnType>;
    traderPrivateDeleteV2Orders (params?: {}): Promise<implicitReturnType>;
    traderPrivateDeleteV2OrdersOrderId (params?: {}): Promise<implicitReturnType>;
    traderPrivateDeleteV2Positions (params?: {}): Promise<implicitReturnType>;
    traderPrivateDeleteV2PositionsSymbolOrAssetId (params?: {}): Promise<implicitReturnType>;
    traderPrivateDeleteV2WatchlistsWatchlistId (params?: {}): Promise<implicitReturnType>;
    traderPrivateDeleteV2WatchlistsByName (params?: {}): Promise<implicitReturnType>;
    traderPrivateDeleteV2WatchlistsWatchlistIdSymbol (params?: {}): Promise<implicitReturnType>;
    marketPublicGetV1beta3CryptoLocBars (params?: {}): Promise<implicitReturnType>;
    marketPublicGetV1beta3CryptoLocLatestBars (params?: {}): Promise<implicitReturnType>;
    marketPublicGetV1beta3CryptoLocLatestOrderbooks (params?: {}): Promise<implicitReturnType>;
    marketPublicGetV1beta3CryptoLocLatestQuotes (params?: {}): Promise<implicitReturnType>;
    marketPublicGetV1beta3CryptoLocLatestTrades (params?: {}): Promise<implicitReturnType>;
    marketPublicGetV1beta3CryptoLocQuotes (params?: {}): Promise<implicitReturnType>;
    marketPublicGetV1beta3CryptoLocSnapshots (params?: {}): Promise<implicitReturnType>;
    marketPublicGetV1beta3CryptoLocTrades (params?: {}): Promise<implicitReturnType>;
    marketPrivateGetV1beta1CorporateActions (params?: {}): Promise<implicitReturnType>;
    marketPrivateGetV1beta1ForexLatestRates (params?: {}): Promise<implicitReturnType>;
    marketPrivateGetV1beta1ForexRates (params?: {}): Promise<implicitReturnType>;
    marketPrivateGetV1beta1LogosSymbol (params?: {}): Promise<implicitReturnType>;
    marketPrivateGetV1beta1News (params?: {}): Promise<implicitReturnType>;
    marketPrivateGetV1beta1ScreenerStocksMostActives (params?: {}): Promise<implicitReturnType>;
    marketPrivateGetV1beta1ScreenerMarketTypeMovers (params?: {}): Promise<implicitReturnType>;
    marketPrivateGetV2StocksAuctions (params?: {}): Promise<implicitReturnType>;
    marketPrivateGetV2StocksBars (params?: {}): Promise<implicitReturnType>;
    marketPrivateGetV2StocksBarsLatest (params?: {}): Promise<implicitReturnType>;
    marketPrivateGetV2StocksMetaConditionsTicktype (params?: {}): Promise<implicitReturnType>;
    marketPrivateGetV2StocksMetaExchanges (params?: {}): Promise<implicitReturnType>;
    marketPrivateGetV2StocksQuotes (params?: {}): Promise<implicitReturnType>;
    marketPrivateGetV2StocksQuotesLatest (params?: {}): Promise<implicitReturnType>;
    marketPrivateGetV2StocksSnapshots (params?: {}): Promise<implicitReturnType>;
    marketPrivateGetV2StocksTrades (params?: {}): Promise<implicitReturnType>;
    marketPrivateGetV2StocksTradesLatest (params?: {}): Promise<implicitReturnType>;
    marketPrivateGetV2StocksSymbolAuctions (params?: {}): Promise<implicitReturnType>;
    marketPrivateGetV2StocksSymbolBars (params?: {}): Promise<implicitReturnType>;
    marketPrivateGetV2StocksSymbolBarsLatest (params?: {}): Promise<implicitReturnType>;
    marketPrivateGetV2StocksSymbolQuotes (params?: {}): Promise<implicitReturnType>;
    marketPrivateGetV2StocksSymbolQuotesLatest (params?: {}): Promise<implicitReturnType>;
    marketPrivateGetV2StocksSymbolSnapshot (params?: {}): Promise<implicitReturnType>;
    marketPrivateGetV2StocksSymbolTrades (params?: {}): Promise<implicitReturnType>;
    marketPrivateGetV2StocksSymbolTradesLatest (params?: {}): Promise<implicitReturnType>;
}
abstract class Exchange extends _Exchange {}

export default Exchange
