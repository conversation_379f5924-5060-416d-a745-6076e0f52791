// -------------------------------------------------------------------------------

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

// -------------------------------------------------------------------------------

import { implicitReturnType } from '../base/types.js';
import { Exchange as _Exchange } from '../base/Exchange.js';

interface Exchange {
    v1PubGetHistKline (params?: {}): Promise<implicitReturnType>;
    v1PubGetHistTrades (params?: {}): Promise<implicitReturnType>;
    v1PublicGetInfo (params?: {}): Promise<implicitReturnType>;
    v1PublicGetInfoSymbol (params?: {}): Promise<implicitReturnType>;
    v1PublicGetSystemInfo (params?: {}): Promise<implicitReturnType>;
    v1PublicGetMarketTrades (params?: {}): Promise<implicitReturnType>;
    v1PublicGetToken (params?: {}): Promise<implicitReturnType>;
    v1PublicGetTokenNetwork (params?: {}): Promise<implicitReturnType>;
    v1PublicGetFundingRates (params?: {}): Promise<implicitReturnType>;
    v1PublicGetFundingRateSymbol (params?: {}): Promise<implicitReturnType>;
    v1PublicGetFundingRateHistory (params?: {}): Promise<implicitReturnType>;
    v1PublicGetFutures (params?: {}): Promise<implicitReturnType>;
    v1PublicGetFuturesSymbol (params?: {}): Promise<implicitReturnType>;
    v1PublicGetOrderbookSymbol (params?: {}): Promise<implicitReturnType>;
    v1PublicGetKline (params?: {}): Promise<implicitReturnType>;
    v1PrivateGetClientToken (params?: {}): Promise<implicitReturnType>;
    v1PrivateGetOrderOid (params?: {}): Promise<implicitReturnType>;
    v1PrivateGetClientOrderClientOrderId (params?: {}): Promise<implicitReturnType>;
    v1PrivateGetOrders (params?: {}): Promise<implicitReturnType>;
    v1PrivateGetClientTradeTid (params?: {}): Promise<implicitReturnType>;
    v1PrivateGetOrderOidTrades (params?: {}): Promise<implicitReturnType>;
    v1PrivateGetClientTrades (params?: {}): Promise<implicitReturnType>;
    v1PrivateGetClientHistTrades (params?: {}): Promise<implicitReturnType>;
    v1PrivateGetStakingYieldHistory (params?: {}): Promise<implicitReturnType>;
    v1PrivateGetClientHolding (params?: {}): Promise<implicitReturnType>;
    v1PrivateGetAssetDeposit (params?: {}): Promise<implicitReturnType>;
    v1PrivateGetAssetHistory (params?: {}): Promise<implicitReturnType>;
    v1PrivateGetSubAccountAll (params?: {}): Promise<implicitReturnType>;
    v1PrivateGetSubAccountAssets (params?: {}): Promise<implicitReturnType>;
    v1PrivateGetSubAccountAssetDetail (params?: {}): Promise<implicitReturnType>;
    v1PrivateGetSubAccountIpRestriction (params?: {}): Promise<implicitReturnType>;
    v1PrivateGetAssetMainSubTransferHistory (params?: {}): Promise<implicitReturnType>;
    v1PrivateGetTokenInterest (params?: {}): Promise<implicitReturnType>;
    v1PrivateGetTokenInterestToken (params?: {}): Promise<implicitReturnType>;
    v1PrivateGetInterestHistory (params?: {}): Promise<implicitReturnType>;
    v1PrivateGetInterestRepay (params?: {}): Promise<implicitReturnType>;
    v1PrivateGetFundingFeeHistory (params?: {}): Promise<implicitReturnType>;
    v1PrivateGetPositions (params?: {}): Promise<implicitReturnType>;
    v1PrivateGetPositionSymbol (params?: {}): Promise<implicitReturnType>;
    v1PrivateGetClientTransactionHistory (params?: {}): Promise<implicitReturnType>;
    v1PrivateGetClientFuturesLeverage (params?: {}): Promise<implicitReturnType>;
    v1PrivatePostOrder (params?: {}): Promise<implicitReturnType>;
    v1PrivatePostOrderCancelAllAfter (params?: {}): Promise<implicitReturnType>;
    v1PrivatePostAssetMainSubTransfer (params?: {}): Promise<implicitReturnType>;
    v1PrivatePostAssetLtv (params?: {}): Promise<implicitReturnType>;
    v1PrivatePostAssetWithdraw (params?: {}): Promise<implicitReturnType>;
    v1PrivatePostAssetInternalWithdraw (params?: {}): Promise<implicitReturnType>;
    v1PrivatePostInterestRepay (params?: {}): Promise<implicitReturnType>;
    v1PrivatePostClientAccountMode (params?: {}): Promise<implicitReturnType>;
    v1PrivatePostClientPositionMode (params?: {}): Promise<implicitReturnType>;
    v1PrivatePostClientLeverage (params?: {}): Promise<implicitReturnType>;
    v1PrivatePostClientFuturesLeverage (params?: {}): Promise<implicitReturnType>;
    v1PrivatePostClientIsolatedMargin (params?: {}): Promise<implicitReturnType>;
    v1PrivateDeleteOrder (params?: {}): Promise<implicitReturnType>;
    v1PrivateDeleteClientOrder (params?: {}): Promise<implicitReturnType>;
    v1PrivateDeleteOrders (params?: {}): Promise<implicitReturnType>;
    v1PrivateDeleteAssetWithdraw (params?: {}): Promise<implicitReturnType>;
    v2PrivateGetClientHolding (params?: {}): Promise<implicitReturnType>;
    v3PublicGetSystemInfo (params?: {}): Promise<implicitReturnType>;
    v3PublicGetInstruments (params?: {}): Promise<implicitReturnType>;
    v3PublicGetToken (params?: {}): Promise<implicitReturnType>;
    v3PublicGetTokenNetwork (params?: {}): Promise<implicitReturnType>;
    v3PublicGetTokenInfo (params?: {}): Promise<implicitReturnType>;
    v3PublicGetMarketTrades (params?: {}): Promise<implicitReturnType>;
    v3PublicGetMarketTradesHistory (params?: {}): Promise<implicitReturnType>;
    v3PublicGetOrderbook (params?: {}): Promise<implicitReturnType>;
    v3PublicGetKline (params?: {}): Promise<implicitReturnType>;
    v3PublicGetKlineHistory (params?: {}): Promise<implicitReturnType>;
    v3PublicGetFutures (params?: {}): Promise<implicitReturnType>;
    v3PublicGetFundingRate (params?: {}): Promise<implicitReturnType>;
    v3PublicGetFundingRateHistory (params?: {}): Promise<implicitReturnType>;
    v3PublicGetInsuranceFund (params?: {}): Promise<implicitReturnType>;
    v3PrivateGetTradeOrder (params?: {}): Promise<implicitReturnType>;
    v3PrivateGetTradeOrders (params?: {}): Promise<implicitReturnType>;
    v3PrivateGetTradeAlgoOrder (params?: {}): Promise<implicitReturnType>;
    v3PrivateGetTradeAlgoOrders (params?: {}): Promise<implicitReturnType>;
    v3PrivateGetTradeTransaction (params?: {}): Promise<implicitReturnType>;
    v3PrivateGetTradeTransactionHistory (params?: {}): Promise<implicitReturnType>;
    v3PrivateGetTradeTradingFee (params?: {}): Promise<implicitReturnType>;
    v3PrivateGetAccountInfo (params?: {}): Promise<implicitReturnType>;
    v3PrivateGetAccountTokenConfig (params?: {}): Promise<implicitReturnType>;
    v3PrivateGetAccountSymbolConfig (params?: {}): Promise<implicitReturnType>;
    v3PrivateGetAccountSubAccountsAll (params?: {}): Promise<implicitReturnType>;
    v3PrivateGetAccountReferralSummary (params?: {}): Promise<implicitReturnType>;
    v3PrivateGetAccountReferralRewardHistory (params?: {}): Promise<implicitReturnType>;
    v3PrivateGetAccountCredentials (params?: {}): Promise<implicitReturnType>;
    v3PrivateGetAssetBalances (params?: {}): Promise<implicitReturnType>;
    v3PrivateGetAssetTokenHistory (params?: {}): Promise<implicitReturnType>;
    v3PrivateGetAssetTransferHistory (params?: {}): Promise<implicitReturnType>;
    v3PrivateGetAssetWalletHistory (params?: {}): Promise<implicitReturnType>;
    v3PrivateGetAssetWalletDeposit (params?: {}): Promise<implicitReturnType>;
    v3PrivateGetAssetStakingYieldHistory (params?: {}): Promise<implicitReturnType>;
    v3PrivateGetFuturesPositions (params?: {}): Promise<implicitReturnType>;
    v3PrivateGetFuturesLeverage (params?: {}): Promise<implicitReturnType>;
    v3PrivateGetFuturesDefaultMarginMode (params?: {}): Promise<implicitReturnType>;
    v3PrivateGetFuturesFundingFeeHistory (params?: {}): Promise<implicitReturnType>;
    v3PrivateGetSpotMarginInterestRate (params?: {}): Promise<implicitReturnType>;
    v3PrivateGetSpotMarginInterestHistory (params?: {}): Promise<implicitReturnType>;
    v3PrivateGetSpotMarginMaxMargin (params?: {}): Promise<implicitReturnType>;
    v3PrivateGetAlgoOrderOid (params?: {}): Promise<implicitReturnType>;
    v3PrivateGetAlgoOrders (params?: {}): Promise<implicitReturnType>;
    v3PrivateGetBalances (params?: {}): Promise<implicitReturnType>;
    v3PrivateGetPositions (params?: {}): Promise<implicitReturnType>;
    v3PrivateGetBuypower (params?: {}): Promise<implicitReturnType>;
    v3PrivateGetConvertExchangeInfo (params?: {}): Promise<implicitReturnType>;
    v3PrivateGetConvertAssetInfo (params?: {}): Promise<implicitReturnType>;
    v3PrivateGetConvertRfq (params?: {}): Promise<implicitReturnType>;
    v3PrivateGetConvertTrade (params?: {}): Promise<implicitReturnType>;
    v3PrivateGetConvertTrades (params?: {}): Promise<implicitReturnType>;
    v3PrivatePostTradeOrder (params?: {}): Promise<implicitReturnType>;
    v3PrivatePostTradeAlgoOrder (params?: {}): Promise<implicitReturnType>;
    v3PrivatePostTradeCancelAllAfter (params?: {}): Promise<implicitReturnType>;
    v3PrivatePostAccountTradingMode (params?: {}): Promise<implicitReturnType>;
    v3PrivatePostAccountListenKey (params?: {}): Promise<implicitReturnType>;
    v3PrivatePostAssetTransfer (params?: {}): Promise<implicitReturnType>;
    v3PrivatePostAssetWalletWithdraw (params?: {}): Promise<implicitReturnType>;
    v3PrivatePostSpotMarginLeverage (params?: {}): Promise<implicitReturnType>;
    v3PrivatePostSpotMarginInterestRepay (params?: {}): Promise<implicitReturnType>;
    v3PrivatePostAlgoOrder (params?: {}): Promise<implicitReturnType>;
    v3PrivatePostConvertRft (params?: {}): Promise<implicitReturnType>;
    v3PrivatePutTradeOrder (params?: {}): Promise<implicitReturnType>;
    v3PrivatePutTradeAlgoOrder (params?: {}): Promise<implicitReturnType>;
    v3PrivatePutFuturesLeverage (params?: {}): Promise<implicitReturnType>;
    v3PrivatePutFuturesPositionMode (params?: {}): Promise<implicitReturnType>;
    v3PrivatePutOrderOid (params?: {}): Promise<implicitReturnType>;
    v3PrivatePutOrderClientClientOrderId (params?: {}): Promise<implicitReturnType>;
    v3PrivatePutAlgoOrderOid (params?: {}): Promise<implicitReturnType>;
    v3PrivatePutAlgoOrderClientClientOrderId (params?: {}): Promise<implicitReturnType>;
    v3PrivateDeleteTradeOrder (params?: {}): Promise<implicitReturnType>;
    v3PrivateDeleteTradeOrders (params?: {}): Promise<implicitReturnType>;
    v3PrivateDeleteTradeAlgoOrder (params?: {}): Promise<implicitReturnType>;
    v3PrivateDeleteTradeAlgoOrders (params?: {}): Promise<implicitReturnType>;
    v3PrivateDeleteTradeAllOrders (params?: {}): Promise<implicitReturnType>;
    v3PrivateDeleteAlgoOrderOrderId (params?: {}): Promise<implicitReturnType>;
    v3PrivateDeleteAlgoOrdersPending (params?: {}): Promise<implicitReturnType>;
    v3PrivateDeleteAlgoOrdersPendingSymbol (params?: {}): Promise<implicitReturnType>;
    v3PrivateDeleteOrdersPending (params?: {}): Promise<implicitReturnType>;
}
abstract class Exchange extends _Exchange {}

export default Exchange
