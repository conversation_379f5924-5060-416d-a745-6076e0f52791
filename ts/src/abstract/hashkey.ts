// -------------------------------------------------------------------------------

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

// -------------------------------------------------------------------------------

import { implicitReturnType } from '../base/types.js';
import { Exchange as _Exchange } from '../base/Exchange.js';

interface Exchange {
    publicGetApiV1ExchangeInfo (params?: {}): Promise<implicitReturnType>;
    publicGetQuoteV1Depth (params?: {}): Promise<implicitReturnType>;
    publicGetQuoteV1Trades (params?: {}): Promise<implicitReturnType>;
    publicGetQuoteV1Klines (params?: {}): Promise<implicitReturnType>;
    publicGetQuoteV1Ticker24hr (params?: {}): Promise<implicitReturnType>;
    publicGetQuoteV1TickerPrice (params?: {}): Promise<implicitReturnType>;
    publicGetQuoteV1TickerBookTicker (params?: {}): Promise<implicitReturnType>;
    publicGetQuoteV1DepthMerged (params?: {}): Promise<implicitReturnType>;
    publicGetQuoteV1MarkPrice (params?: {}): Promise<implicitReturnType>;
    publicGetQuoteV1Index (params?: {}): Promise<implicitReturnType>;
    publicGetApiV1FuturesFundingRate (params?: {}): Promise<implicitReturnType>;
    publicGetApiV1FuturesHistoryFundingRate (params?: {}): Promise<implicitReturnType>;
    publicGetApiV1Ping (params?: {}): Promise<implicitReturnType>;
    publicGetApiV1Time (params?: {}): Promise<implicitReturnType>;
    privateGetApiV1SpotOrder (params?: {}): Promise<implicitReturnType>;
    privateGetApiV1SpotOpenOrders (params?: {}): Promise<implicitReturnType>;
    privateGetApiV1SpotTradeOrders (params?: {}): Promise<implicitReturnType>;
    privateGetApiV1FuturesLeverage (params?: {}): Promise<implicitReturnType>;
    privateGetApiV1FuturesOrder (params?: {}): Promise<implicitReturnType>;
    privateGetApiV1FuturesOpenOrders (params?: {}): Promise<implicitReturnType>;
    privateGetApiV1FuturesUserTrades (params?: {}): Promise<implicitReturnType>;
    privateGetApiV1FuturesPositions (params?: {}): Promise<implicitReturnType>;
    privateGetApiV1FuturesHistoryOrders (params?: {}): Promise<implicitReturnType>;
    privateGetApiV1FuturesBalance (params?: {}): Promise<implicitReturnType>;
    privateGetApiV1FuturesLiquidationAssignStatus (params?: {}): Promise<implicitReturnType>;
    privateGetApiV1FuturesRiskLimit (params?: {}): Promise<implicitReturnType>;
    privateGetApiV1FuturesCommissionRate (params?: {}): Promise<implicitReturnType>;
    privateGetApiV1FuturesGetBestOrder (params?: {}): Promise<implicitReturnType>;
    privateGetApiV1AccountVipInfo (params?: {}): Promise<implicitReturnType>;
    privateGetApiV1Account (params?: {}): Promise<implicitReturnType>;
    privateGetApiV1AccountTrades (params?: {}): Promise<implicitReturnType>;
    privateGetApiV1AccountType (params?: {}): Promise<implicitReturnType>;
    privateGetApiV1AccountCheckApiKey (params?: {}): Promise<implicitReturnType>;
    privateGetApiV1AccountBalanceFlow (params?: {}): Promise<implicitReturnType>;
    privateGetApiV1SpotSubAccountOpenOrders (params?: {}): Promise<implicitReturnType>;
    privateGetApiV1SpotSubAccountTradeOrders (params?: {}): Promise<implicitReturnType>;
    privateGetApiV1SubAccountTrades (params?: {}): Promise<implicitReturnType>;
    privateGetApiV1FuturesSubAccountOpenOrders (params?: {}): Promise<implicitReturnType>;
    privateGetApiV1FuturesSubAccountHistoryOrders (params?: {}): Promise<implicitReturnType>;
    privateGetApiV1FuturesSubAccountUserTrades (params?: {}): Promise<implicitReturnType>;
    privateGetApiV1AccountDepositAddress (params?: {}): Promise<implicitReturnType>;
    privateGetApiV1AccountDepositOrders (params?: {}): Promise<implicitReturnType>;
    privateGetApiV1AccountWithdrawOrders (params?: {}): Promise<implicitReturnType>;
    privatePostApiV1UserDataStream (params?: {}): Promise<implicitReturnType>;
    privatePostApiV1SpotOrderTest (params?: {}): Promise<implicitReturnType>;
    privatePostApiV1SpotOrder (params?: {}): Promise<implicitReturnType>;
    privatePostApiV11SpotOrder (params?: {}): Promise<implicitReturnType>;
    privatePostApiV1SpotBatchOrders (params?: {}): Promise<implicitReturnType>;
    privatePostApiV1FuturesLeverage (params?: {}): Promise<implicitReturnType>;
    privatePostApiV1FuturesOrder (params?: {}): Promise<implicitReturnType>;
    privatePostApiV1FuturesPositionTradingStop (params?: {}): Promise<implicitReturnType>;
    privatePostApiV1FuturesBatchOrders (params?: {}): Promise<implicitReturnType>;
    privatePostApiV1AccountAssetTransfer (params?: {}): Promise<implicitReturnType>;
    privatePostApiV1AccountAuthAddress (params?: {}): Promise<implicitReturnType>;
    privatePostApiV1AccountWithdraw (params?: {}): Promise<implicitReturnType>;
    privatePutApiV1UserDataStream (params?: {}): Promise<implicitReturnType>;
    privateDeleteApiV1SpotOrder (params?: {}): Promise<implicitReturnType>;
    privateDeleteApiV1SpotOpenOrders (params?: {}): Promise<implicitReturnType>;
    privateDeleteApiV1SpotCancelOrderByIds (params?: {}): Promise<implicitReturnType>;
    privateDeleteApiV1FuturesOrder (params?: {}): Promise<implicitReturnType>;
    privateDeleteApiV1FuturesBatchOrders (params?: {}): Promise<implicitReturnType>;
    privateDeleteApiV1FuturesCancelOrderByIds (params?: {}): Promise<implicitReturnType>;
    privateDeleteApiV1UserDataStream (params?: {}): Promise<implicitReturnType>;
}
abstract class Exchange extends _Exchange {}

export default Exchange
