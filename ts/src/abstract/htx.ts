// -------------------------------------------------------------------------------

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

// -------------------------------------------------------------------------------

import { implicitReturnType } from '../base/types.js';
import { Exchange as _Exchange } from '../base/Exchange.js';

interface Exchange {
    v2PublicGetReferenceCurrencies (params?: {}): Promise<implicitReturnType>;
    v2PublicGetMarketStatus (params?: {}): Promise<implicitReturnType>;
    v2PrivateGetAccountLedger (params?: {}): Promise<implicitReturnType>;
    v2PrivateGetAccountWithdrawQuota (params?: {}): Promise<implicitReturnType>;
    v2PrivateGetAccountWithdrawAddress (params?: {}): Promise<implicitReturnType>;
    v2PrivateGetAccountDepositAddress (params?: {}): Promise<implicitReturnType>;
    v2PrivateGetAccountRepayment (params?: {}): Promise<implicitReturnType>;
    v2PrivateGetReferenceTransactFeeRate (params?: {}): Promise<implicitReturnType>;
    v2PrivateGetAccountAssetValuation (params?: {}): Promise<implicitReturnType>;
    v2PrivateGetPointAccount (params?: {}): Promise<implicitReturnType>;
    v2PrivateGetSubUserUserList (params?: {}): Promise<implicitReturnType>;
    v2PrivateGetSubUserUserState (params?: {}): Promise<implicitReturnType>;
    v2PrivateGetSubUserAccountList (params?: {}): Promise<implicitReturnType>;
    v2PrivateGetSubUserDepositAddress (params?: {}): Promise<implicitReturnType>;
    v2PrivateGetSubUserQueryDeposit (params?: {}): Promise<implicitReturnType>;
    v2PrivateGetUserApiKey (params?: {}): Promise<implicitReturnType>;
    v2PrivateGetUserUid (params?: {}): Promise<implicitReturnType>;
    v2PrivateGetAlgoOrdersOpening (params?: {}): Promise<implicitReturnType>;
    v2PrivateGetAlgoOrdersHistory (params?: {}): Promise<implicitReturnType>;
    v2PrivateGetAlgoOrdersSpecific (params?: {}): Promise<implicitReturnType>;
    v2PrivateGetC2cOffers (params?: {}): Promise<implicitReturnType>;
    v2PrivateGetC2cOffer (params?: {}): Promise<implicitReturnType>;
    v2PrivateGetC2cTransactions (params?: {}): Promise<implicitReturnType>;
    v2PrivateGetC2cRepayment (params?: {}): Promise<implicitReturnType>;
    v2PrivateGetC2cAccount (params?: {}): Promise<implicitReturnType>;
    v2PrivateGetEtpReference (params?: {}): Promise<implicitReturnType>;
    v2PrivateGetEtpTransactions (params?: {}): Promise<implicitReturnType>;
    v2PrivateGetEtpTransaction (params?: {}): Promise<implicitReturnType>;
    v2PrivateGetEtpRebalance (params?: {}): Promise<implicitReturnType>;
    v2PrivateGetEtpLimit (params?: {}): Promise<implicitReturnType>;
    v2PrivatePostAccountTransfer (params?: {}): Promise<implicitReturnType>;
    v2PrivatePostAccountRepayment (params?: {}): Promise<implicitReturnType>;
    v2PrivatePostPointTransfer (params?: {}): Promise<implicitReturnType>;
    v2PrivatePostSubUserManagement (params?: {}): Promise<implicitReturnType>;
    v2PrivatePostSubUserCreation (params?: {}): Promise<implicitReturnType>;
    v2PrivatePostSubUserTradableMarket (params?: {}): Promise<implicitReturnType>;
    v2PrivatePostSubUserTransferability (params?: {}): Promise<implicitReturnType>;
    v2PrivatePostSubUserApiKeyGeneration (params?: {}): Promise<implicitReturnType>;
    v2PrivatePostSubUserApiKeyModification (params?: {}): Promise<implicitReturnType>;
    v2PrivatePostSubUserApiKeyDeletion (params?: {}): Promise<implicitReturnType>;
    v2PrivatePostSubUserDeductMode (params?: {}): Promise<implicitReturnType>;
    v2PrivatePostAlgoOrders (params?: {}): Promise<implicitReturnType>;
    v2PrivatePostAlgoOrdersCancelAllAfter (params?: {}): Promise<implicitReturnType>;
    v2PrivatePostAlgoOrdersCancellation (params?: {}): Promise<implicitReturnType>;
    v2PrivatePostC2cOffer (params?: {}): Promise<implicitReturnType>;
    v2PrivatePostC2cCancellation (params?: {}): Promise<implicitReturnType>;
    v2PrivatePostC2cCancelAll (params?: {}): Promise<implicitReturnType>;
    v2PrivatePostC2cRepayment (params?: {}): Promise<implicitReturnType>;
    v2PrivatePostC2cTransfer (params?: {}): Promise<implicitReturnType>;
    v2PrivatePostEtpCreation (params?: {}): Promise<implicitReturnType>;
    v2PrivatePostEtpRedemption (params?: {}): Promise<implicitReturnType>;
    v2PrivatePostEtpTransactIdCancel (params?: {}): Promise<implicitReturnType>;
    v2PrivatePostEtpBatchCancel (params?: {}): Promise<implicitReturnType>;
    publicGetCommonSymbols (params?: {}): Promise<implicitReturnType>;
    publicGetCommonCurrencys (params?: {}): Promise<implicitReturnType>;
    publicGetCommonTimestamp (params?: {}): Promise<implicitReturnType>;
    publicGetCommonExchange (params?: {}): Promise<implicitReturnType>;
    publicGetSettingsCurrencys (params?: {}): Promise<implicitReturnType>;
    privateGetAccountAccounts (params?: {}): Promise<implicitReturnType>;
    privateGetAccountAccountsIdBalance (params?: {}): Promise<implicitReturnType>;
    privateGetAccountAccountsSubUid (params?: {}): Promise<implicitReturnType>;
    privateGetAccountHistory (params?: {}): Promise<implicitReturnType>;
    privateGetCrossMarginLoanInfo (params?: {}): Promise<implicitReturnType>;
    privateGetMarginLoanInfo (params?: {}): Promise<implicitReturnType>;
    privateGetFeeFeeRateGet (params?: {}): Promise<implicitReturnType>;
    privateGetOrderOpenOrders (params?: {}): Promise<implicitReturnType>;
    privateGetOrderOrders (params?: {}): Promise<implicitReturnType>;
    privateGetOrderOrdersId (params?: {}): Promise<implicitReturnType>;
    privateGetOrderOrdersIdMatchresults (params?: {}): Promise<implicitReturnType>;
    privateGetOrderOrdersGetClientOrder (params?: {}): Promise<implicitReturnType>;
    privateGetOrderHistory (params?: {}): Promise<implicitReturnType>;
    privateGetOrderMatchresults (params?: {}): Promise<implicitReturnType>;
    privateGetQueryDepositWithdraw (params?: {}): Promise<implicitReturnType>;
    privateGetMarginLoanOrders (params?: {}): Promise<implicitReturnType>;
    privateGetMarginAccountsBalance (params?: {}): Promise<implicitReturnType>;
    privateGetCrossMarginLoanOrders (params?: {}): Promise<implicitReturnType>;
    privateGetCrossMarginAccountsBalance (params?: {}): Promise<implicitReturnType>;
    privateGetPointsActions (params?: {}): Promise<implicitReturnType>;
    privateGetPointsOrders (params?: {}): Promise<implicitReturnType>;
    privateGetSubuserAggregateBalance (params?: {}): Promise<implicitReturnType>;
    privateGetStableCoinExchangeRate (params?: {}): Promise<implicitReturnType>;
    privateGetStableCoinQuote (params?: {}): Promise<implicitReturnType>;
    privatePostAccountTransfer (params?: {}): Promise<implicitReturnType>;
    privatePostFuturesTransfer (params?: {}): Promise<implicitReturnType>;
    privatePostOrderBatchOrders (params?: {}): Promise<implicitReturnType>;
    privatePostOrderOrdersPlace (params?: {}): Promise<implicitReturnType>;
    privatePostOrderOrdersSubmitCancelClientOrder (params?: {}): Promise<implicitReturnType>;
    privatePostOrderOrdersBatchCancelOpenOrders (params?: {}): Promise<implicitReturnType>;
    privatePostOrderOrdersIdSubmitcancel (params?: {}): Promise<implicitReturnType>;
    privatePostOrderOrdersBatchcancel (params?: {}): Promise<implicitReturnType>;
    privatePostDwWithdrawApiCreate (params?: {}): Promise<implicitReturnType>;
    privatePostDwWithdrawVirtualIdCancel (params?: {}): Promise<implicitReturnType>;
    privatePostDwTransferInMargin (params?: {}): Promise<implicitReturnType>;
    privatePostDwTransferOutMargin (params?: {}): Promise<implicitReturnType>;
    privatePostMarginOrders (params?: {}): Promise<implicitReturnType>;
    privatePostMarginOrdersIdRepay (params?: {}): Promise<implicitReturnType>;
    privatePostCrossMarginTransferIn (params?: {}): Promise<implicitReturnType>;
    privatePostCrossMarginTransferOut (params?: {}): Promise<implicitReturnType>;
    privatePostCrossMarginOrders (params?: {}): Promise<implicitReturnType>;
    privatePostCrossMarginOrdersIdRepay (params?: {}): Promise<implicitReturnType>;
    privatePostStableCoinExchange (params?: {}): Promise<implicitReturnType>;
    privatePostSubuserTransfer (params?: {}): Promise<implicitReturnType>;
    statusPublicSpotGetApiV2SummaryJson (params?: {}): Promise<implicitReturnType>;
    statusPublicFutureInverseGetApiV2SummaryJson (params?: {}): Promise<implicitReturnType>;
    statusPublicFutureLinearGetApiV2SummaryJson (params?: {}): Promise<implicitReturnType>;
    statusPublicSwapInverseGetApiV2SummaryJson (params?: {}): Promise<implicitReturnType>;
    statusPublicSwapLinearGetApiV2SummaryJson (params?: {}): Promise<implicitReturnType>;
    spotPublicGetV2MarketStatus (params?: {}): Promise<implicitReturnType>;
    spotPublicGetV1CommonSymbols (params?: {}): Promise<implicitReturnType>;
    spotPublicGetV1CommonCurrencys (params?: {}): Promise<implicitReturnType>;
    spotPublicGetV2SettingsCommonCurrencies (params?: {}): Promise<implicitReturnType>;
    spotPublicGetV2ReferenceCurrencies (params?: {}): Promise<implicitReturnType>;
    spotPublicGetV1CommonTimestamp (params?: {}): Promise<implicitReturnType>;
    spotPublicGetV1CommonExchange (params?: {}): Promise<implicitReturnType>;
    spotPublicGetV1SettingsCommonChains (params?: {}): Promise<implicitReturnType>;
    spotPublicGetV1SettingsCommonCurrencys (params?: {}): Promise<implicitReturnType>;
    spotPublicGetV1SettingsCommonSymbols (params?: {}): Promise<implicitReturnType>;
    spotPublicGetV2SettingsCommonSymbols (params?: {}): Promise<implicitReturnType>;
    spotPublicGetV1SettingsCommonMarketSymbols (params?: {}): Promise<implicitReturnType>;
    spotPublicGetMarketHistoryCandles (params?: {}): Promise<implicitReturnType>;
    spotPublicGetMarketHistoryKline (params?: {}): Promise<implicitReturnType>;
    spotPublicGetMarketDetailMerged (params?: {}): Promise<implicitReturnType>;
    spotPublicGetMarketTickers (params?: {}): Promise<implicitReturnType>;
    spotPublicGetMarketDetail (params?: {}): Promise<implicitReturnType>;
    spotPublicGetMarketDepth (params?: {}): Promise<implicitReturnType>;
    spotPublicGetMarketTrade (params?: {}): Promise<implicitReturnType>;
    spotPublicGetMarketHistoryTrade (params?: {}): Promise<implicitReturnType>;
    spotPublicGetMarketEtp (params?: {}): Promise<implicitReturnType>;
    spotPublicGetV2EtpReference (params?: {}): Promise<implicitReturnType>;
    spotPublicGetV2EtpRebalance (params?: {}): Promise<implicitReturnType>;
    spotPrivateGetV1AccountAccounts (params?: {}): Promise<implicitReturnType>;
    spotPrivateGetV1AccountAccountsAccountIdBalance (params?: {}): Promise<implicitReturnType>;
    spotPrivateGetV2AccountValuation (params?: {}): Promise<implicitReturnType>;
    spotPrivateGetV2AccountAssetValuation (params?: {}): Promise<implicitReturnType>;
    spotPrivateGetV1AccountHistory (params?: {}): Promise<implicitReturnType>;
    spotPrivateGetV2AccountLedger (params?: {}): Promise<implicitReturnType>;
    spotPrivateGetV2PointAccount (params?: {}): Promise<implicitReturnType>;
    spotPrivateGetV2AccountDepositAddress (params?: {}): Promise<implicitReturnType>;
    spotPrivateGetV2AccountWithdrawQuota (params?: {}): Promise<implicitReturnType>;
    spotPrivateGetV2AccountWithdrawAddress (params?: {}): Promise<implicitReturnType>;
    spotPrivateGetV2ReferenceCurrencies (params?: {}): Promise<implicitReturnType>;
    spotPrivateGetV1QueryDepositWithdraw (params?: {}): Promise<implicitReturnType>;
    spotPrivateGetV1QueryWithdrawClientOrderId (params?: {}): Promise<implicitReturnType>;
    spotPrivateGetV2UserApiKey (params?: {}): Promise<implicitReturnType>;
    spotPrivateGetV2UserUid (params?: {}): Promise<implicitReturnType>;
    spotPrivateGetV2SubUserUserList (params?: {}): Promise<implicitReturnType>;
    spotPrivateGetV2SubUserUserState (params?: {}): Promise<implicitReturnType>;
    spotPrivateGetV2SubUserAccountList (params?: {}): Promise<implicitReturnType>;
    spotPrivateGetV2SubUserDepositAddress (params?: {}): Promise<implicitReturnType>;
    spotPrivateGetV2SubUserQueryDeposit (params?: {}): Promise<implicitReturnType>;
    spotPrivateGetV1SubuserAggregateBalance (params?: {}): Promise<implicitReturnType>;
    spotPrivateGetV1AccountAccountsSubUid (params?: {}): Promise<implicitReturnType>;
    spotPrivateGetV1OrderOpenOrders (params?: {}): Promise<implicitReturnType>;
    spotPrivateGetV1OrderOrdersOrderId (params?: {}): Promise<implicitReturnType>;
    spotPrivateGetV1OrderOrdersGetClientOrder (params?: {}): Promise<implicitReturnType>;
    spotPrivateGetV1OrderOrdersOrderIdMatchresult (params?: {}): Promise<implicitReturnType>;
    spotPrivateGetV1OrderOrdersOrderIdMatchresults (params?: {}): Promise<implicitReturnType>;
    spotPrivateGetV1OrderOrders (params?: {}): Promise<implicitReturnType>;
    spotPrivateGetV1OrderHistory (params?: {}): Promise<implicitReturnType>;
    spotPrivateGetV1OrderMatchresults (params?: {}): Promise<implicitReturnType>;
    spotPrivateGetV2ReferenceTransactFeeRate (params?: {}): Promise<implicitReturnType>;
    spotPrivateGetV2AlgoOrdersOpening (params?: {}): Promise<implicitReturnType>;
    spotPrivateGetV2AlgoOrdersHistory (params?: {}): Promise<implicitReturnType>;
    spotPrivateGetV2AlgoOrdersSpecific (params?: {}): Promise<implicitReturnType>;
    spotPrivateGetV1MarginLoanInfo (params?: {}): Promise<implicitReturnType>;
    spotPrivateGetV1MarginLoanOrders (params?: {}): Promise<implicitReturnType>;
    spotPrivateGetV1MarginAccountsBalance (params?: {}): Promise<implicitReturnType>;
    spotPrivateGetV1CrossMarginLoanInfo (params?: {}): Promise<implicitReturnType>;
    spotPrivateGetV1CrossMarginLoanOrders (params?: {}): Promise<implicitReturnType>;
    spotPrivateGetV1CrossMarginAccountsBalance (params?: {}): Promise<implicitReturnType>;
    spotPrivateGetV2AccountRepayment (params?: {}): Promise<implicitReturnType>;
    spotPrivateGetV1StableCoinQuote (params?: {}): Promise<implicitReturnType>;
    spotPrivateGetV1StableCoinExchangeRate (params?: {}): Promise<implicitReturnType>;
    spotPrivateGetV2EtpTransactions (params?: {}): Promise<implicitReturnType>;
    spotPrivateGetV2EtpTransaction (params?: {}): Promise<implicitReturnType>;
    spotPrivateGetV2EtpLimit (params?: {}): Promise<implicitReturnType>;
    spotPrivatePostV1AccountTransfer (params?: {}): Promise<implicitReturnType>;
    spotPrivatePostV1FuturesTransfer (params?: {}): Promise<implicitReturnType>;
    spotPrivatePostV2PointTransfer (params?: {}): Promise<implicitReturnType>;
    spotPrivatePostV2AccountTransfer (params?: {}): Promise<implicitReturnType>;
    spotPrivatePostV1DwWithdrawApiCreate (params?: {}): Promise<implicitReturnType>;
    spotPrivatePostV1DwWithdrawVirtualWithdrawIdCancel (params?: {}): Promise<implicitReturnType>;
    spotPrivatePostV2SubUserDeductMode (params?: {}): Promise<implicitReturnType>;
    spotPrivatePostV2SubUserCreation (params?: {}): Promise<implicitReturnType>;
    spotPrivatePostV2SubUserManagement (params?: {}): Promise<implicitReturnType>;
    spotPrivatePostV2SubUserTradableMarket (params?: {}): Promise<implicitReturnType>;
    spotPrivatePostV2SubUserTransferability (params?: {}): Promise<implicitReturnType>;
    spotPrivatePostV2SubUserApiKeyGeneration (params?: {}): Promise<implicitReturnType>;
    spotPrivatePostV2SubUserApiKeyModification (params?: {}): Promise<implicitReturnType>;
    spotPrivatePostV2SubUserApiKeyDeletion (params?: {}): Promise<implicitReturnType>;
    spotPrivatePostV1SubuserTransfer (params?: {}): Promise<implicitReturnType>;
    spotPrivatePostV1TrustUserActiveCredit (params?: {}): Promise<implicitReturnType>;
    spotPrivatePostV1OrderOrdersPlace (params?: {}): Promise<implicitReturnType>;
    spotPrivatePostV1OrderBatchOrders (params?: {}): Promise<implicitReturnType>;
    spotPrivatePostV1OrderAutoPlace (params?: {}): Promise<implicitReturnType>;
    spotPrivatePostV1OrderOrdersOrderIdSubmitcancel (params?: {}): Promise<implicitReturnType>;
    spotPrivatePostV1OrderOrdersSubmitCancelClientOrder (params?: {}): Promise<implicitReturnType>;
    spotPrivatePostV1OrderOrdersBatchCancelOpenOrders (params?: {}): Promise<implicitReturnType>;
    spotPrivatePostV1OrderOrdersBatchcancel (params?: {}): Promise<implicitReturnType>;
    spotPrivatePostV2AlgoOrdersCancelAllAfter (params?: {}): Promise<implicitReturnType>;
    spotPrivatePostV2AlgoOrders (params?: {}): Promise<implicitReturnType>;
    spotPrivatePostV2AlgoOrdersCancellation (params?: {}): Promise<implicitReturnType>;
    spotPrivatePostV2AccountRepayment (params?: {}): Promise<implicitReturnType>;
    spotPrivatePostV1DwTransferInMargin (params?: {}): Promise<implicitReturnType>;
    spotPrivatePostV1DwTransferOutMargin (params?: {}): Promise<implicitReturnType>;
    spotPrivatePostV1MarginOrders (params?: {}): Promise<implicitReturnType>;
    spotPrivatePostV1MarginOrdersOrderIdRepay (params?: {}): Promise<implicitReturnType>;
    spotPrivatePostV1CrossMarginTransferIn (params?: {}): Promise<implicitReturnType>;
    spotPrivatePostV1CrossMarginTransferOut (params?: {}): Promise<implicitReturnType>;
    spotPrivatePostV1CrossMarginOrders (params?: {}): Promise<implicitReturnType>;
    spotPrivatePostV1CrossMarginOrdersOrderIdRepay (params?: {}): Promise<implicitReturnType>;
    spotPrivatePostV1StableCoinExchange (params?: {}): Promise<implicitReturnType>;
    spotPrivatePostV2EtpCreation (params?: {}): Promise<implicitReturnType>;
    spotPrivatePostV2EtpRedemption (params?: {}): Promise<implicitReturnType>;
    spotPrivatePostV2EtpTransactIdCancel (params?: {}): Promise<implicitReturnType>;
    spotPrivatePostV2EtpBatchCancel (params?: {}): Promise<implicitReturnType>;
    contractPublicGetApiV1Timestamp (params?: {}): Promise<implicitReturnType>;
    contractPublicGetHeartbeat (params?: {}): Promise<implicitReturnType>;
    contractPublicGetApiV1ContractContractInfo (params?: {}): Promise<implicitReturnType>;
    contractPublicGetApiV1ContractIndex (params?: {}): Promise<implicitReturnType>;
    contractPublicGetApiV1ContractQueryElements (params?: {}): Promise<implicitReturnType>;
    contractPublicGetApiV1ContractPriceLimit (params?: {}): Promise<implicitReturnType>;
    contractPublicGetApiV1ContractOpenInterest (params?: {}): Promise<implicitReturnType>;
    contractPublicGetApiV1ContractDeliveryPrice (params?: {}): Promise<implicitReturnType>;
    contractPublicGetMarketDepth (params?: {}): Promise<implicitReturnType>;
    contractPublicGetMarketBbo (params?: {}): Promise<implicitReturnType>;
    contractPublicGetMarketHistoryKline (params?: {}): Promise<implicitReturnType>;
    contractPublicGetIndexMarketHistoryMarkPriceKline (params?: {}): Promise<implicitReturnType>;
    contractPublicGetMarketDetailMerged (params?: {}): Promise<implicitReturnType>;
    contractPublicGetMarketDetailBatchMerged (params?: {}): Promise<implicitReturnType>;
    contractPublicGetV2MarketDetailBatchMerged (params?: {}): Promise<implicitReturnType>;
    contractPublicGetMarketTrade (params?: {}): Promise<implicitReturnType>;
    contractPublicGetMarketHistoryTrade (params?: {}): Promise<implicitReturnType>;
    contractPublicGetApiV1ContractRiskInfo (params?: {}): Promise<implicitReturnType>;
    contractPublicGetApiV1ContractInsuranceFund (params?: {}): Promise<implicitReturnType>;
    contractPublicGetApiV1ContractAdjustfactor (params?: {}): Promise<implicitReturnType>;
    contractPublicGetApiV1ContractHisOpenInterest (params?: {}): Promise<implicitReturnType>;
    contractPublicGetApiV1ContractLadderMargin (params?: {}): Promise<implicitReturnType>;
    contractPublicGetApiV1ContractApiState (params?: {}): Promise<implicitReturnType>;
    contractPublicGetApiV1ContractEliteAccountRatio (params?: {}): Promise<implicitReturnType>;
    contractPublicGetApiV1ContractElitePositionRatio (params?: {}): Promise<implicitReturnType>;
    contractPublicGetApiV1ContractLiquidationOrders (params?: {}): Promise<implicitReturnType>;
    contractPublicGetApiV1ContractSettlementRecords (params?: {}): Promise<implicitReturnType>;
    contractPublicGetIndexMarketHistoryIndex (params?: {}): Promise<implicitReturnType>;
    contractPublicGetIndexMarketHistoryBasis (params?: {}): Promise<implicitReturnType>;
    contractPublicGetApiV1ContractEstimatedSettlementPrice (params?: {}): Promise<implicitReturnType>;
    contractPublicGetApiV3ContractLiquidationOrders (params?: {}): Promise<implicitReturnType>;
    contractPublicGetSwapApiV1SwapContractInfo (params?: {}): Promise<implicitReturnType>;
    contractPublicGetSwapApiV1SwapIndex (params?: {}): Promise<implicitReturnType>;
    contractPublicGetSwapApiV1SwapQueryElements (params?: {}): Promise<implicitReturnType>;
    contractPublicGetSwapApiV1SwapPriceLimit (params?: {}): Promise<implicitReturnType>;
    contractPublicGetSwapApiV1SwapOpenInterest (params?: {}): Promise<implicitReturnType>;
    contractPublicGetSwapExMarketDepth (params?: {}): Promise<implicitReturnType>;
    contractPublicGetSwapExMarketBbo (params?: {}): Promise<implicitReturnType>;
    contractPublicGetSwapExMarketHistoryKline (params?: {}): Promise<implicitReturnType>;
    contractPublicGetIndexMarketHistorySwapMarkPriceKline (params?: {}): Promise<implicitReturnType>;
    contractPublicGetSwapExMarketDetailMerged (params?: {}): Promise<implicitReturnType>;
    contractPublicGetV2SwapExMarketDetailBatchMerged (params?: {}): Promise<implicitReturnType>;
    contractPublicGetIndexMarketHistorySwapPremiumIndexKline (params?: {}): Promise<implicitReturnType>;
    contractPublicGetSwapExMarketDetailBatchMerged (params?: {}): Promise<implicitReturnType>;
    contractPublicGetSwapExMarketTrade (params?: {}): Promise<implicitReturnType>;
    contractPublicGetSwapExMarketHistoryTrade (params?: {}): Promise<implicitReturnType>;
    contractPublicGetSwapApiV1SwapRiskInfo (params?: {}): Promise<implicitReturnType>;
    contractPublicGetSwapApiV1SwapInsuranceFund (params?: {}): Promise<implicitReturnType>;
    contractPublicGetSwapApiV1SwapAdjustfactor (params?: {}): Promise<implicitReturnType>;
    contractPublicGetSwapApiV1SwapHisOpenInterest (params?: {}): Promise<implicitReturnType>;
    contractPublicGetSwapApiV1SwapLadderMargin (params?: {}): Promise<implicitReturnType>;
    contractPublicGetSwapApiV1SwapApiState (params?: {}): Promise<implicitReturnType>;
    contractPublicGetSwapApiV1SwapEliteAccountRatio (params?: {}): Promise<implicitReturnType>;
    contractPublicGetSwapApiV1SwapElitePositionRatio (params?: {}): Promise<implicitReturnType>;
    contractPublicGetSwapApiV1SwapEstimatedSettlementPrice (params?: {}): Promise<implicitReturnType>;
    contractPublicGetSwapApiV1SwapLiquidationOrders (params?: {}): Promise<implicitReturnType>;
    contractPublicGetSwapApiV1SwapSettlementRecords (params?: {}): Promise<implicitReturnType>;
    contractPublicGetSwapApiV1SwapFundingRate (params?: {}): Promise<implicitReturnType>;
    contractPublicGetSwapApiV1SwapBatchFundingRate (params?: {}): Promise<implicitReturnType>;
    contractPublicGetSwapApiV1SwapHistoricalFundingRate (params?: {}): Promise<implicitReturnType>;
    contractPublicGetSwapApiV3SwapLiquidationOrders (params?: {}): Promise<implicitReturnType>;
    contractPublicGetIndexMarketHistorySwapEstimatedRateKline (params?: {}): Promise<implicitReturnType>;
    contractPublicGetIndexMarketHistorySwapBasis (params?: {}): Promise<implicitReturnType>;
    contractPublicGetLinearSwapApiV1SwapContractInfo (params?: {}): Promise<implicitReturnType>;
    contractPublicGetLinearSwapApiV1SwapIndex (params?: {}): Promise<implicitReturnType>;
    contractPublicGetLinearSwapApiV1SwapQueryElements (params?: {}): Promise<implicitReturnType>;
    contractPublicGetLinearSwapApiV1SwapPriceLimit (params?: {}): Promise<implicitReturnType>;
    contractPublicGetLinearSwapApiV1SwapOpenInterest (params?: {}): Promise<implicitReturnType>;
    contractPublicGetLinearSwapExMarketDepth (params?: {}): Promise<implicitReturnType>;
    contractPublicGetLinearSwapExMarketBbo (params?: {}): Promise<implicitReturnType>;
    contractPublicGetLinearSwapExMarketHistoryKline (params?: {}): Promise<implicitReturnType>;
    contractPublicGetIndexMarketHistoryLinearSwapMarkPriceKline (params?: {}): Promise<implicitReturnType>;
    contractPublicGetLinearSwapExMarketDetailMerged (params?: {}): Promise<implicitReturnType>;
    contractPublicGetLinearSwapExMarketDetailBatchMerged (params?: {}): Promise<implicitReturnType>;
    contractPublicGetV2LinearSwapExMarketDetailBatchMerged (params?: {}): Promise<implicitReturnType>;
    contractPublicGetLinearSwapExMarketTrade (params?: {}): Promise<implicitReturnType>;
    contractPublicGetLinearSwapExMarketHistoryTrade (params?: {}): Promise<implicitReturnType>;
    contractPublicGetLinearSwapApiV1SwapRiskInfo (params?: {}): Promise<implicitReturnType>;
    contractPublicGetSwapApiV1LinearSwapApiV1SwapInsuranceFund (params?: {}): Promise<implicitReturnType>;
    contractPublicGetLinearSwapApiV1SwapAdjustfactor (params?: {}): Promise<implicitReturnType>;
    contractPublicGetLinearSwapApiV1SwapCrossAdjustfactor (params?: {}): Promise<implicitReturnType>;
    contractPublicGetLinearSwapApiV1SwapHisOpenInterest (params?: {}): Promise<implicitReturnType>;
    contractPublicGetLinearSwapApiV1SwapLadderMargin (params?: {}): Promise<implicitReturnType>;
    contractPublicGetLinearSwapApiV1SwapCrossLadderMargin (params?: {}): Promise<implicitReturnType>;
    contractPublicGetLinearSwapApiV1SwapApiState (params?: {}): Promise<implicitReturnType>;
    contractPublicGetLinearSwapApiV1SwapCrossTransferState (params?: {}): Promise<implicitReturnType>;
    contractPublicGetLinearSwapApiV1SwapCrossTradeState (params?: {}): Promise<implicitReturnType>;
    contractPublicGetLinearSwapApiV1SwapEliteAccountRatio (params?: {}): Promise<implicitReturnType>;
    contractPublicGetLinearSwapApiV1SwapElitePositionRatio (params?: {}): Promise<implicitReturnType>;
    contractPublicGetLinearSwapApiV1SwapLiquidationOrders (params?: {}): Promise<implicitReturnType>;
    contractPublicGetLinearSwapApiV1SwapSettlementRecords (params?: {}): Promise<implicitReturnType>;
    contractPublicGetLinearSwapApiV1SwapFundingRate (params?: {}): Promise<implicitReturnType>;
    contractPublicGetLinearSwapApiV1SwapBatchFundingRate (params?: {}): Promise<implicitReturnType>;
    contractPublicGetLinearSwapApiV1SwapHistoricalFundingRate (params?: {}): Promise<implicitReturnType>;
    contractPublicGetLinearSwapApiV3SwapLiquidationOrders (params?: {}): Promise<implicitReturnType>;
    contractPublicGetIndexMarketHistoryLinearSwapPremiumIndexKline (params?: {}): Promise<implicitReturnType>;
    contractPublicGetIndexMarketHistoryLinearSwapEstimatedRateKline (params?: {}): Promise<implicitReturnType>;
    contractPublicGetIndexMarketHistoryLinearSwapBasis (params?: {}): Promise<implicitReturnType>;
    contractPublicGetLinearSwapApiV1SwapEstimatedSettlementPrice (params?: {}): Promise<implicitReturnType>;
    contractPrivateGetApiV1ContractSubAuthList (params?: {}): Promise<implicitReturnType>;
    contractPrivateGetApiV1ContractApiTradingStatus (params?: {}): Promise<implicitReturnType>;
    contractPrivateGetSwapApiV1SwapSubAuthList (params?: {}): Promise<implicitReturnType>;
    contractPrivateGetSwapApiV1SwapApiTradingStatus (params?: {}): Promise<implicitReturnType>;
    contractPrivateGetLinearSwapApiV1SwapSubAuthList (params?: {}): Promise<implicitReturnType>;
    contractPrivateGetLinearSwapApiV1SwapApiTradingStatus (params?: {}): Promise<implicitReturnType>;
    contractPrivateGetLinearSwapApiV1SwapCrossPositionSide (params?: {}): Promise<implicitReturnType>;
    contractPrivateGetLinearSwapApiV1SwapPositionSide (params?: {}): Promise<implicitReturnType>;
    contractPrivateGetLinearSwapApiV3UnifiedAccountInfo (params?: {}): Promise<implicitReturnType>;
    contractPrivateGetLinearSwapApiV3FixPositionMarginChangeRecord (params?: {}): Promise<implicitReturnType>;
    contractPrivateGetLinearSwapApiV3SwapUnifiedAccountType (params?: {}): Promise<implicitReturnType>;
    contractPrivateGetLinearSwapApiV3LinearSwapOverviewAccountInfo (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostApiV1ContractBalanceValuation (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostApiV1ContractAccountInfo (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostApiV1ContractPositionInfo (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostApiV1ContractSubAuth (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostApiV1ContractSubAccountList (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostApiV1ContractSubAccountInfoList (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostApiV1ContractSubAccountInfo (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostApiV1ContractSubPositionInfo (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostApiV1ContractFinancialRecord (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostApiV1ContractFinancialRecordExact (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostApiV1ContractUserSettlementRecords (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostApiV1ContractOrderLimit (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostApiV1ContractFee (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostApiV1ContractTransferLimit (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostApiV1ContractPositionLimit (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostApiV1ContractAccountPositionInfo (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostApiV1ContractMasterSubTransfer (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostApiV1ContractMasterSubTransferRecord (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostApiV1ContractAvailableLevelRate (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostApiV3ContractFinancialRecord (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostApiV3ContractFinancialRecordExact (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostApiV1ContractCancelAfter (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostApiV1ContractOrder (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostApiV1ContractBatchorder (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostApiV1ContractCancel (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostApiV1ContractCancelall (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostApiV1ContractSwitchLeverRate (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostApiV1LightningClosePosition (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostApiV1ContractOrderInfo (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostApiV1ContractOrderDetail (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostApiV1ContractOpenorders (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostApiV1ContractHisorders (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostApiV1ContractHisordersExact (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostApiV1ContractMatchresults (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostApiV1ContractMatchresultsExact (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostApiV3ContractHisorders (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostApiV3ContractHisordersExact (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostApiV3ContractMatchresults (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostApiV3ContractMatchresultsExact (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostApiV1ContractTriggerOrder (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostApiV1ContractTriggerCancel (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostApiV1ContractTriggerCancelall (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostApiV1ContractTriggerOpenorders (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostApiV1ContractTriggerHisorders (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostApiV1ContractTpslOrder (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostApiV1ContractTpslCancel (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostApiV1ContractTpslCancelall (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostApiV1ContractTpslOpenorders (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostApiV1ContractTpslHisorders (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostApiV1ContractRelationTpslOrder (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostApiV1ContractTrackOrder (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostApiV1ContractTrackCancel (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostApiV1ContractTrackCancelall (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostApiV1ContractTrackOpenorders (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostApiV1ContractTrackHisorders (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostSwapApiV1SwapBalanceValuation (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostSwapApiV1SwapAccountInfo (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostSwapApiV1SwapPositionInfo (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostSwapApiV1SwapAccountPositionInfo (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostSwapApiV1SwapSubAuth (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostSwapApiV1SwapSubAccountList (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostSwapApiV1SwapSubAccountInfoList (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostSwapApiV1SwapSubAccountInfo (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostSwapApiV1SwapSubPositionInfo (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostSwapApiV1SwapFinancialRecord (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostSwapApiV1SwapFinancialRecordExact (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostSwapApiV1SwapUserSettlementRecords (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostSwapApiV1SwapAvailableLevelRate (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostSwapApiV1SwapOrderLimit (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostSwapApiV1SwapFee (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostSwapApiV1SwapTransferLimit (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostSwapApiV1SwapPositionLimit (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostSwapApiV1SwapMasterSubTransfer (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostSwapApiV1SwapMasterSubTransferRecord (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostSwapApiV3SwapFinancialRecord (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostSwapApiV3SwapFinancialRecordExact (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostSwapApiV1SwapCancelAfter (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostSwapApiV1SwapOrder (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostSwapApiV1SwapBatchorder (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostSwapApiV1SwapCancel (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostSwapApiV1SwapCancelall (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostSwapApiV1SwapLightningClosePosition (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostSwapApiV1SwapSwitchLeverRate (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostSwapApiV1SwapOrderInfo (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostSwapApiV1SwapOrderDetail (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostSwapApiV1SwapOpenorders (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostSwapApiV1SwapHisorders (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostSwapApiV1SwapHisordersExact (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostSwapApiV1SwapMatchresults (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostSwapApiV1SwapMatchresultsExact (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostSwapApiV3SwapMatchresults (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostSwapApiV3SwapMatchresultsExact (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostSwapApiV3SwapHisorders (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostSwapApiV3SwapHisordersExact (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostSwapApiV1SwapTriggerOrder (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostSwapApiV1SwapTriggerCancel (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostSwapApiV1SwapTriggerCancelall (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostSwapApiV1SwapTriggerOpenorders (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostSwapApiV1SwapTriggerHisorders (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostSwapApiV1SwapTpslOrder (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostSwapApiV1SwapTpslCancel (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostSwapApiV1SwapTpslCancelall (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostSwapApiV1SwapTpslOpenorders (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostSwapApiV1SwapTpslHisorders (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostSwapApiV1SwapRelationTpslOrder (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostSwapApiV1SwapTrackOrder (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostSwapApiV1SwapTrackCancel (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostSwapApiV1SwapTrackCancelall (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostSwapApiV1SwapTrackOpenorders (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostSwapApiV1SwapTrackHisorders (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV1SwapLeverPositionLimit (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV1SwapCrossLeverPositionLimit (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV1SwapBalanceValuation (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV1SwapAccountInfo (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV1SwapCrossAccountInfo (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV1SwapPositionInfo (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV1SwapCrossPositionInfo (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV1SwapAccountPositionInfo (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV1SwapCrossAccountPositionInfo (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV1SwapSubAuth (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV1SwapSubAccountList (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV1SwapCrossSubAccountList (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV1SwapSubAccountInfoList (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV1SwapCrossSubAccountInfoList (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV1SwapSubAccountInfo (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV1SwapCrossSubAccountInfo (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV1SwapSubPositionInfo (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV1SwapCrossSubPositionInfo (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV1SwapFinancialRecord (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV1SwapFinancialRecordExact (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV1SwapUserSettlementRecords (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV1SwapCrossUserSettlementRecords (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV1SwapAvailableLevelRate (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV1SwapCrossAvailableLevelRate (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV1SwapOrderLimit (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV1SwapFee (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV1SwapTransferLimit (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV1SwapCrossTransferLimit (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV1SwapPositionLimit (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV1SwapCrossPositionLimit (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV1SwapMasterSubTransfer (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV1SwapMasterSubTransferRecord (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV1SwapTransferInner (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV3SwapFinancialRecord (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV3SwapFinancialRecordExact (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV1SwapOrder (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV1SwapCrossOrder (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV1SwapBatchorder (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV1SwapCrossBatchorder (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV1SwapCancel (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV1SwapCrossCancel (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV1SwapCancelall (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV1SwapCrossCancelall (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV1SwapSwitchLeverRate (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV1SwapCrossSwitchLeverRate (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV1SwapLightningClosePosition (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV1SwapCrossLightningClosePosition (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV1SwapOrderInfo (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV1SwapCrossOrderInfo (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV1SwapOrderDetail (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV1SwapCrossOrderDetail (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV1SwapOpenorders (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV1SwapCrossOpenorders (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV1SwapHisorders (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV1SwapCrossHisorders (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV1SwapHisordersExact (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV1SwapCrossHisordersExact (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV1SwapMatchresults (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV1SwapCrossMatchresults (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV1SwapMatchresultsExact (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV1SwapCrossMatchresultsExact (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV1LinearCancelAfter (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV1SwapSwitchPositionMode (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV1SwapCrossSwitchPositionMode (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV3SwapMatchresults (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV3SwapCrossMatchresults (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV3SwapMatchresultsExact (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV3SwapCrossMatchresultsExact (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV3SwapHisorders (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV3SwapCrossHisorders (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV3SwapHisordersExact (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV3SwapCrossHisordersExact (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV3FixPositionMarginChange (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV3SwapSwitchAccountType (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV3LinearSwapFeeSwitch (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV1SwapTriggerOrder (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV1SwapCrossTriggerOrder (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV1SwapTriggerCancel (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV1SwapCrossTriggerCancel (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV1SwapTriggerCancelall (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV1SwapCrossTriggerCancelall (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV1SwapTriggerOpenorders (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV1SwapCrossTriggerOpenorders (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV1SwapTriggerHisorders (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV1SwapCrossTriggerHisorders (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV1SwapTpslOrder (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV1SwapCrossTpslOrder (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV1SwapTpslCancel (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV1SwapCrossTpslCancel (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV1SwapTpslCancelall (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV1SwapCrossTpslCancelall (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV1SwapTpslOpenorders (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV1SwapCrossTpslOpenorders (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV1SwapTpslHisorders (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV1SwapCrossTpslHisorders (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV1SwapRelationTpslOrder (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV1SwapCrossRelationTpslOrder (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV1SwapTrackOrder (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV1SwapCrossTrackOrder (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV1SwapTrackCancel (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV1SwapCrossTrackCancel (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV1SwapTrackCancelall (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV1SwapCrossTrackCancelall (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV1SwapTrackOpenorders (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV1SwapCrossTrackOpenorders (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV1SwapTrackHisorders (params?: {}): Promise<implicitReturnType>;
    contractPrivatePostLinearSwapApiV1SwapCrossTrackHisorders (params?: {}): Promise<implicitReturnType>;
}
abstract class Exchange extends _Exchange {}

export default Exchange
